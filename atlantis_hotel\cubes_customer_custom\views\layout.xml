<odoo>
    <data>

    <template id="web.external_layout">
        <t t-if="not o" t-set="o" t-value="doc"/>

        <t t-if="not company">
            <!-- Multicompany -->
            <t t-if="company_id">
                <t t-set="company" t-value="company_id"/>
            </t>
            <t t-elif="o and 'company_id' in o and o.company_id.sudo()">
                <t t-set="company" t-value="o.company_id.sudo()"/>
            </t>
            <t t-else="else">
                <t t-set="company" t-value="res_company"/>
            </t>
        </t>

<!--        <t t-if="company.external_report_layout_id" t-call="{{company.external_report_layout_id.sudo().key}}"><t t-out="0"/></t>-->
        <t  t-call="web.basic_layout"><t t-out="0"/></t>

    </template>


    </data>
</odoo>