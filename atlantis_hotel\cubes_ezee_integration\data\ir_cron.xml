<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="ir_cron_ezee_config_pull_bills" model="ir.cron">
        <field name="name">Ezee Config API Request</field>
        <field name="model_id" ref="model_ezee_config"/>
        <field name="state">code</field>
        <field name="code">model.cron_pull_bills()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
        <field name="active" eval="True"/>
    </record>
</odoo>