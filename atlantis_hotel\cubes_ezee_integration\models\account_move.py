from odoo import models, fields
import requests
import csv
import json
from io import StringIO


class EzeeUnique(models.Model):
    _name = 'ezee.unique.records'
    _description = 'Storing unique'

    name = fields.Char(string="Name")
    move_id = fields.Many2one('account.move', string="Move")


class AccountMove(models.Model):
    _inherit = 'account.move'

    unique_ids = fields.One2many('ezee.unique.records', 'move_id', string="Unique Ids")
    ezee_unique_id = fields.Char(string='Unique ID')
    ezee_hotel_name = fields.Char(string='Hotel Name')
    ezee_hotel_code = fields.Char(string='Hotel Code')
    ezee_folio_no = fields.Char(string='Folio No')
    ezee_room_no = fields.Char(string='Room No')
    ezee_date = fields.Char(string='Date')
    ezee_voucher_no = fields.Char(string='Voucher No/Receipt No')
    ezee_invoice_number = fields.Char(string='Invoice Number')
    ezee_guest_name = fields.Char(string='Guest Name')
    ezee_bill_to_name = fields.Char(string='Bill To Name')
    ezee_guest_gst_number = fields.Char(string='Guest GST Number')
    ezee_state = fields.Char(string='State')
    ezee_phone_number = fields.Char(string='Phone Number')
    ezee_mobile_number = fields.Char(string='Mobile Number')
    ezee_entry_type = fields.Char(string='Type')
    ezee_particular = fields.Char(string='Particular')
    ezee_currency = fields.Char(string='Currency')
    ezee_gst_rate = fields.Float(string='GST Rate')
    ezee_cgst_tax_amount = fields.Float(string='CGST Tax Amount')
    ezee_sgst_tax_amount = fields.Float(string='SGST Tax Amount')
    ezee_igst_tax_amount = fields.Float(string='IGST Tax Amount')
    ezee_service_tax = fields.Float(string='Service Tax')
    ezee_luxury_tax = fields.Float(string='Luxury Tax')
    ezee_adjustment = fields.Float(string='Adjustment')
    ezee_is_advance_deposit = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Is Advance Deposit')
    ezee_is_inclusion = fields.Selection([('Yes', 'Yes'), ('No', 'No')], string='Is Inclusion')
    ezee_posted_by = fields.Char(string='Posted By')

    ezee_reservation_no = fields.Char(string='Ezee Reservation Number')
    # ezee_guest_name = fields.Char(string='Ezee Guest Name')
    ezee_arrival_date = fields.Char(string='Ezee Arrival Date')
    ezee_departure_date = fields.Char(string='Ezee Departure Date')
    ezee_reservation_date = fields.Char(string='Ezee Reservation Date')
    ezee_room = fields.Char(string='Ezee Room')
    ezee_room_short_code = fields.Char(string='Ezee Room Short Code')
    ezee_reservation_guarantee = fields.Char(string='Ezee Reservation Guarantee')
    ezee_source = fields.Char(string='Ezee Source')
    # ezee_voucher_no = fields.Char(string='Ezee Voucher Number')
    ezee_mobile = fields.Char(string='Ezee Mobile')
    ezee_address = fields.Char(string='Ezee Address')
    ezee_email = fields.Char(string='Ezee Email')
    ezee_country = fields.Char(string='Ezee Country')
    ezee_adult = fields.Integer(string='Ezee Adult')
    ezee_child = fields.Integer(string='Ezee Child')
    ezee_phone = fields.Char(string='Ezee Phone')
    ezee_no_of_guest = fields.Integer(string='Ezee Number of Guests')
    ezee_no_of_nights = fields.Integer(string='Ezee Number of Nights')
    ezee_salutation = fields.Char(string='Ezee Salutation')
    ezee_first_name = fields.Char(string='Ezee First Name')
    ezee_last_name = fields.Char(string='Ezee Last Name')
    ezee_due_amount = fields.Monetary(string='Ezee Due Amount')
    ezee_deposit = fields.Monetary(string='Ezee Deposit')
    ezee_status = fields.Char(string='Ezee Status')
    ezee_booking_status = fields.Char(string='Ezee Booking Status')
    ezee_transaction_status = fields.Char(string='Ezee Transaction Status')
    ezee_total_tax = fields.Monetary(string='Ezee Total Tax')
    ezee_total_inclusive_tax = fields.Monetary(string='Ezee Total Inclusive Tax')
    ezee_total_exclusive_tax = fields.Monetary(string='Ezee Total Exclusive Tax')
    ezee_other_revenue_exclusive_tax = fields.Monetary(string='Ezee Other Revenue Exclusive Tax')
    ezee_other_revenue_inclusive_tax = fields.Monetary(string='Ezee Other Revenue Inclusive Tax')
    # ezee_folio_no = fields.Char(string='Ezee Folio Number')
    ezee_base_rate_exclusive_tax = fields.Monetary(string='Ezee Base Rate Exclusive Tax')
    ezee_base_rate_inclusive_tax = fields.Monetary(string='Ezee Base Rate Inclusive Tax')
    ezee_payment_type = fields.Char(string='Ezee Payment Type')
    ezee_rate_plan = fields.Char(string='Ezee Rate Plan')
    ezee_arrival_time = fields.Char(string='Ezee Arrival Time')
    ezee_departure_time = fields.Char(string='Ezee Departure Time')
    # ezee_room_no = fields.Char(string='Ezee Room Number')
    ezee_bed_type = fields.Char(string='Ezee Bed Type')

