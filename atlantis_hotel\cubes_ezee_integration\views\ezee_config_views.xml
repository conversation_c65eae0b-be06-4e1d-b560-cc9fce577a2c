<?xml version="1.0" encoding="Big5" ?>
<odoo>
    <data>
        <menuitem id="menu_ezee_config_root" name="Ezee Config" sequence="1"/>

        <!-- Action for opening the list view -->
        <record id="action_ezee_config_list" model="ir.actions.act_window">
            <field name="name">Ezee Config</field>
            <field name="res_model">ezee.config</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- Menuitem for opening the list view -->
        <menuitem id="menu_ezee_config_list" name="Ezee Config" parent="menu_ezee_config_root"
                  action="action_ezee_config_list" sequence="1"/>

        <!-- Form view for Ezee Config -->
        <record id="view_ezee_config_form" model="ir.ui.view">
            <field name="name">ezee.config.form</field>
            <field name="model">ezee.config</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="url"/>
                                <field name="authcode"/>
                                <field name="hotel_code"/>
                                <field name="days_before"/>
                                <field name="days_after"/>
                                <field name="company_id" required="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Tree view for Ezee Config -->
        <record id="view_ezee_config_tree" model="ir.ui.view">
            <field name="name">ezee.config.tree</field>
            <field name="model">ezee.config</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="url"/>
                    <field name="authcode"/>
                    <field name="hotel_code"/>
                </tree>
            </field>
        </record>

    </data>
</odoo>
