# -*- coding: utf-8 -*-
# from odoo import http


# class CubesRestrictionProductCategoryPerCaompany(http.Controller):
#     @http.route('/cubes_restriction_product_category_per_caompany/cubes_restriction_product_category_per_caompany', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/cubes_restriction_product_category_per_caompany/cubes_restriction_product_category_per_caompany/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('cubes_restriction_product_category_per_caompany.listing', {
#             'root': '/cubes_restriction_product_category_per_caompany/cubes_restriction_product_category_per_caompany',
#             'objects': http.request.env['cubes_restriction_product_category_per_caompany.cubes_restriction_product_category_per_caompany'].search([]),
#         })

#     @http.route('/cubes_restriction_product_category_per_caompany/cubes_restriction_product_category_per_caompany/objects/<model("cubes_restriction_product_category_per_caompany.cubes_restriction_product_category_per_caompany"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('cubes_restriction_product_category_per_caompany.object', {
#             'object': obj
#         })
