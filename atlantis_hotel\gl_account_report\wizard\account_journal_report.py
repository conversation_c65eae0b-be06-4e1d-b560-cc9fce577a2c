from odoo import fields, models, api


class AccountReport(models.TransientModel):
    _name = "account.account.report"

    journal_ids = fields.Many2many(
        comodel_name='account.journal',
        string='Journal')

    def account_report(self):
        domain = []
        if self.journal_ids:
            domain.append(('id', 'in', self.journal_ids.mapped('default_account_id').ids))
        elif self.env.user.journal_ids:
            domain.append(('id', 'in', self.env.user.journal_ids.mapped('default_account_id').ids))

        action = self.env.ref('gl_account_report.account_account_balance_action')
        result = action.read()[0]
        result['domain'] = domain
        # return result
        return {
            'res_model': 'account.account',
            'view_mode': 'tree',
            'view_id': self.env.ref('gl_account_report.account_account_view_tree').id,
            'type': 'ir.actions.act_window',
            'domain': domain,
            'context': {'create': False, 'edit': False}
        }
