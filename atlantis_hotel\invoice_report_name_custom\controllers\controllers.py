# -*- coding: utf-8 -*-
# from odoo import http


# class CustomReport(http.Controller):
#     @http.route('/invoice_report_name_custom/invoice_report_name_custom', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/invoice_report_name_custom/invoice_report_name_custom/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('invoice_report_name_custom.listing', {
#             'root': '/invoice_report_name_custom/invoice_report_name_custom',
#             'objects': http.request.env['invoice_report_name_custom.invoice_report_name_custom'].search([]),
#         })

#     @http.route('/invoice_report_name_custom/invoice_report_name_custom/objects/<model("invoice_report_name_custom.invoice_report_name_custom"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('invoice_report_name_custom.object', {
#             'object': obj
#         })
