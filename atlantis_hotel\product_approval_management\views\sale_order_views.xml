<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--    Product domain in the sale order line-->
    <record id="view_order_form" model="ir.ui.view">
        <field name="name">sale.order.view.form.inherit.product.approval.management</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//tree/field[@name='product_id']"
                   position="attributes">
                <attribute name="domain">[('approve_state', '=', 'confirmed'),
                    ('sale_ok', '=', True), '|',
                    ('company_id', '=', False), ('company_id', '=', company_id)]
                </attribute>
            </xpath>
        </field>
    </record>
</odoo>
