# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_no_negative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-06-29 10:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.17\n"

#. module: stock_no_negative
#. odoo-python
#: code:addons/stock_no_negative/models/stock_quant.py:0
#, python-format
msgid " lot {}"
msgstr " lot {}"

#. module: stock_no_negative
#: model:ir.model.fields,field_description:stock_no_negative.field_product_category__allow_negative_stock
#: model:ir.model.fields,field_description:stock_no_negative.field_product_product__allow_negative_stock
#: model:ir.model.fields,field_description:stock_no_negative.field_product_template__allow_negative_stock
#: model:ir.model.fields,field_description:stock_no_negative.field_stock_location__allow_negative_stock
msgid "Allow Negative Stock"
msgstr "Dozvoli negativnu zalihu"

#. module: stock_no_negative
#: model:ir.model.fields,help:stock_no_negative.field_product_category__allow_negative_stock
msgid ""
"Allow negative stock levels for the stockable products attached to this "
"category. The options doesn't apply to products attached to sub-categories "
"of this category."
msgstr ""
"Dozvoli negativna stanja uskladištivih proizvoda pridruženih ovoj "
"kategoriji. Opcije ne se odnose na proizvode iz pordeđenih kategorija ovoj."

#. module: stock_no_negative
#: model:ir.model.fields,help:stock_no_negative.field_stock_location__allow_negative_stock
msgid ""
"Allow negative stock levels for the stockable products attached to this "
"location."
msgstr "Dozvoli negativnu zalihu za uskladištive proizvode na ovoj lokaciji."

#. module: stock_no_negative
#: model:ir.model.fields,help:stock_no_negative.field_product_product__allow_negative_stock
#: model:ir.model.fields,help:stock_no_negative.field_product_template__allow_negative_stock
msgid ""
"If this option is not active on this product nor on its product category and "
"that this product is a stockable product, then the validation of the related "
"stock moves will be blocked if the stock level becomes negative with the "
"stock move."
msgstr ""
"Ako ova opcija nije aktivna na ovom proizvodu niti na kategoriji, i ovo je "
"uskladištivi proizvod, tada će potvrđivanje skladišnih kretanja biti "
"blokirano ako tim kretanjem zaliha ulazi u minus."

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_stock_location
msgid "Inventory Locations"
msgstr "Skladišne lokacije"

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_product_template
msgid "Product"
msgstr "Proizvod"

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_product_category
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_stock_quant
msgid "Quants"
msgstr "Količine"

#. module: stock_no_negative
#. odoo-python
#: code:addons/stock_no_negative/models/stock_quant.py:0
#, python-format
msgid ""
"You cannot validate this stock operation because the stock level of the "
"product '{name}'{name_lot} would become negative ({q_quantity}) on the stock "
"location '{complete_name}' and negative stock is not allowed for this "
"product and/or location."
msgstr ""
"Nije moguće potvrditi ovu skladišnu operaciju jer bi količine proizvoda "
"'{name}'{name_lot} na stanju postale negativne ({q_quantity}) na skladišnoj "
"lokaciji '{complete_name}' a negativna zaliha nije dozvoljena za taj "
"proizvod i/ili lokaciju."
