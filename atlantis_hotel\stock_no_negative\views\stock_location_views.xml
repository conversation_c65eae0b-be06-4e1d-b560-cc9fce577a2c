<?xml version="1.0" encoding="utf-8" ?>
<!--
  Copyright 2018 Akretion (http://www.akretion.com/)
  <AUTHOR> <<EMAIL>>
  License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
-->
<odoo>
    <record id="view_location_form" model="ir.ui.view">
        <field name="name">stock.location.form.allow_negative_stock</field>
        <field name="model">stock.location</field>
        <field name="inherit_id" ref="stock.view_location_form" />
        <field name="arch" type="xml">
            <field name="usage" position="after">
                <field
                    name="allow_negative_stock"
                    attrs="{'invisible': [('usage', 'not in', ['internal', 'transit'])]}"
                />
            </field>
        </field>
    </record>
</odoo>
