<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="advanced_web_domain_widget.DomainSelectorBranchOperatorBits" owl="1">
        <t t-if="!props.readonly">
            <div class="d-inline-flex o_domain_tree_operator_selector" aria-atomic="true">
                <Dropdown togglerClass="'btn btn-link btn-sm btn-primary py-0 px-1 o_domain_tree_operator_caret'" showCaret="props.showCaret">
                    <t t-set-slot="toggler">
                            <t t-if="props.node.operator === '&amp;'">all</t>
                            <t t-elif="props.node.operator === '|'">any</t>
                            <t t-elif="props.node.operator === '!'">none</t>
                    </t>
                    <DropdownItem onSelected="() => this.onOperatorSelected('&amp;')">all</DropdownItem>
                    <DropdownItem onSelected="() => this.onOperatorSelected('|')">any</DropdownItem>
                </Dropdown>
            </div>
        </t>
        <t t-else="">
            <strong>
                <t t-if="props.node.operator === '&amp;'">all</t>
                <t t-elif="props.node.operator === '|'">any</t>
                <t t-elif="props.node.operator === '!'">none</t>
            </strong>
        </t>
    </t>

</templates>
