<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="advanced_web_domain_widget.DomainSelectorTextFieldBits" owl="1">
        <t t-if="props.operator.category === 'in'">
            <DomainSelectorFieldInputWithTagsBits t-props="{ ...props, node: subNode }"/>
        </t>
        <t t-else="">
            <div class="o_ds_value_cell">
                <DomainSelectorFieldInputBits t-props="{ ...props, node: subNode }"/>
            </div>
        </t>
    </t>

</templates>
