
.o_record_selector_value {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: row wrap;
    flex-flow: row wrap;
    align-items: center;
    height: 100%;
    min-height: 20px;
    width: 100%;
    border-bottom: 1px solid #7e7a7aee;
    &:active, &:focus, &:active:focus {
        outline: none;
    }

    > .o_record_selector_chain_part {
        padding: 0px 1px;
        border: 1px solid #dae0e5;
        background: #f8f9fa;
        margin-bottom: 1px;
    }
    > i {
        align-self: center;
        margin: 0 2px;
        font-size: 10px;
    }
}
.o_record_selector_controls {
    // position: absolute;
    top: 0;
    left: auto;
    bottom: 1px;
    right: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.o_record_selector_controls::after {
    content: "";
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    border-right: 0.3em solid transparent;
    border-top: 0.3em solid;
    -moz-transform: scale(0.9999);
}

.o_record_selector_popover {
    position: absolute;
    top: 100%;
    // left: 0;
    bottom: auto;
    right: auto;
    z-index: 1051;
    width: 265px;
    margin-top: 7px;
    background: white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);

    &:focus {          
        outline: none;
    }                   

    .o_record_selector_popover_header {
        color: white;
        // background: theme-color('primary');
        font-weight: bold;
        padding: 5px 0 5px 0.4em;

        .o_record_selector_title {
            width: 100%;
            display: inline-block;
            max-width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: top;
            padding: 0px 35px;
            text-align: center;
        }
        .o_record_selector_search {
            padding-right: 0.4rem;
            > .o_input {
                padding: 5px 0.4rem;
            }
        }
        .o_record_selector_popover_option {
            position: absolute;
            top: 0;
            left: auto;
            bottom: auto;
            right: auto;
            padding: 8px;
            &.o_record_selector_prev_page {
                left: 0;
            }
            &.o_record_selector_close {
                right: 0;
            }                           
            &:hover {
                // background: darken(theme-color('primary'), 10%);
            }
        }
        &:before {
            position: absolute;
            top: -7px;
            left: 7px;
            bottom: auto;
            right: auto;
            content: "";
            border-left: 7px solid rgba(0, 0, 0, 0);
            border-right: 7px solid rgba(0, 0, 0, 0);
            border-bottom: 7px solid #00A09D;
        }
    }
    .o_record_selector_popover_body {
        .o_record_selector_page {
            position: relative;
            max-height: 320px;
            overflow: auto;
            margin: 0;
            padding: 0;

            > .o_record_selector_item {
                list-style: none;
                position: relative;
                padding: 5px 0 5px 0.4em;
                cursor: pointer;
                font-family: Arial;
                font-size: 13px;
                color: #444;
                border-bottom: 1px solid #eee;
                &.active {
                    background: #f5f5f5;
                }
                .o_record_selector_item_title {
                    font-size: 12px;
                }
                .o_record_selector_relation_icon {
                    position: absolute;
                    top: 0;
                    left: auto;
                    bottom: 0;
                    right: 0;
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: flex;
                    align-items: center;
                    padding: 10px;
                }
            }
        }
    }
    .o_record_selector_popover_footer {
        // background: theme-color('primary');
        padding: 5px 0.4em;

        > input {
            width: 100%;
        }
    }
}
    
// @include media-breakpoint-up(sm) {
//     :not(.s_popup) > .modal {
//         .modal-body {         
//             overflow: inherit !important;
//             min-height: 0;
//         }                   
//     }
// }    
