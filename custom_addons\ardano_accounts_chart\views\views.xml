<odoo>
    <data>
        <record model="ir.ui.view" id="chart_of_accounts_tree_view_inherit">
            <field name="name">chart_of_accounts_tree_view_inherit</field>
            <field name="model">account.account</field>
            <field name="inherit_id" ref="account.view_account_list"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='account_type']" position="after">
                    <field name="type_of_account"/>
                    <field name="main_account" attrs="{'invisible': [('type_of_account', 'in', ('main_account',False))]}"/>
                    <field name="sub_account" attrs="{'invisible': [('type_of_account', 'in', ('subaccount','main_account',False))]}"/>
                    <field name="detailed_account" attrs="{'invisible': [('type_of_account', 'in', ('subaccount','main_account','detailed_account',False))]}"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>