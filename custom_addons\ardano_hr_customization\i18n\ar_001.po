# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ardano_hr_customization
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-15 11:16+0000\n"
"PO-Revision-Date: 2024-08-15 14:17+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"Language: ar\n"
"X-Generator: Poedit 2.3\n"

#. module: ardano_hr_customization
#: model:mail.template,body_html:ardano_hr_customization.email_template_date_remainder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\"/>\n"
"\n"
"                    Dear ${object.partner_id.name},\n"
"                    <br/>\n"
"                    <br/>\n"
"                    We wanted to let you know that your contract <strong>${object.name}</strong> contract end date is\n"
"                    ,<strong>${object.date_end}</strong>.\n"
"                    <br/>\n"
"                    <br/>\n"
"                    Feel free to contact us if you have any question.\n"
"                    <br/>\n"
"                    <br/>\n"
"\n"
"                    % if object.company_id:\n"
"                    Best regards,\n"
"                    <br/>\n"
"                    from\n"
"                    <strong>${object.company_id.name | safe}\n"
"                    </strong>\n"
"                    % endif\n"
"                </div>\n"
"            "
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_account_setup_bank_manual_config__partner_id
#: model:ir.model.fields,field_description:ardano_hr_customization.field_res_partner_bank__partner_id
msgid "Account Holder"
msgstr "مالك الحساب"

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_payslip_input_type__active
msgid "Active"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model,name:ardano_hr_customization.model_hr_attendance
msgid "Attendance"
msgstr "الحاضرين "

#. module: ardano_hr_customization
#: model:ir.model,name:ardano_hr_customization.model_res_partner_bank
msgid "Bank Accounts"
msgstr "الحسابات البنكية"

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee__bank_ids
msgid "Banks"
msgstr ""

#. module: ardano_hr_customization
#: model_terms:ir.ui.view,arch_db:ardano_hr_customization.hr_contract_form_view_inherit
msgid "Bonus"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__name
msgid "Certificate"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee__certificate_id
msgid "Certificate Level"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee__date_of_certification
msgid "Certification Date"
msgstr "تاريخ الحصول على المؤهل"

#. module: ardano_hr_customization
#: model:ir.actions.server,name:ardano_hr_customization.contract_date_cron_data_ir_actions_server
#: model:ir.cron,cron_name:ardano_hr_customization.contract_date_cron_data
#: model:ir.cron,name:ardano_hr_customization.contract_date_cron_data
msgid "Contract Date End Reminder"
msgstr ""

#. module: ardano_hr_customization
#: model:mail.template,name:ardano_hr_customization.email_template_date_remainder
msgid "Contract Date Reminder"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__create_uid
msgid "Created by"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__create_date
msgid "Created on"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__display_name
msgid "Display Name"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model,name:ardano_hr_customization.model_hr_employee
#: model:ir.model.fields,field_description:ardano_hr_customization.field_account_setup_bank_manual_config__employee_id
#: model:ir.model.fields,field_description:ardano_hr_customization.field_res_partner_bank__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: ardano_hr_customization
#: model:ir.model,name:ardano_hr_customization.model_hr_contract
msgid "Employee Contract"
msgstr "عقد الموظف"

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__general_manager_bonus
msgid "General Manager Bonus"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__general_manager_bonus_percentage
msgid "General Manager Percentage(%)"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__id
msgid "ID"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee__id_number
msgid "ID Number"
msgstr "رقم المُعرف"

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__job_bonus
msgid "Job Bonus"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__job_bonus_percentage
msgid "Job Percentage(%)"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate____last_update
msgid "Last Modified on"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__write_uid
msgid "Last Updated by"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_employee_certificate__write_date
msgid "Last Updated on"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__location_bonus
msgid "Location Bonus"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__location_bonus_percentage
msgid "Location Percentage(%)"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model,name:ardano_hr_customization.model_hr_payslip_input_type
msgid "Payslip Input Type"
msgstr "نوع مدخل إيصال الدفع "

#. module: ardano_hr_customization
#: model_terms:ir.ui.view,arch_db:ardano_hr_customization.hr_attendance_search_view_inherit
msgid "Registration Number"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_attendance__registration_number
msgid "Registration Number of the Employee"
msgstr ""

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__date_start
msgid "Start Date"
msgstr "تاريخ مباشرة العمل"

#. module: ardano_hr_customization
#: model:ir.model.fields,field_description:ardano_hr_customization.field_hr_contract__work_start_date
msgid "Work Start Date"
msgstr "تاريخ بدء العقد"

#. module: ardano_hr_customization
#: model:ir.model,name:ardano_hr_customization.model_hr_employee_certificate
msgid "hr.employee.certificate"
msgstr ""


#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Start Date"
msgstr "تاريخ مباشرة العمل"
