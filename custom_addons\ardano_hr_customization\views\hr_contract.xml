<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="email_template_date_remainder" model="mail.template">
            <field name="name">Contract Date Reminder</field>
            <field name="model_id" ref="hr_contract.model_hr_contract"/>
            <field name="email_from">${user.partner_id.email |safe}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;"/>

                    Dear ${object.partner_id.name},
                    <br/>
                    <br/>
                    We wanted to let you know that your contract <strong>${object.name}</strong> contract end date is
                    ,<strong>${object.date_end}</strong>.
                    <br/>
                    <br/>
                    Feel free to contact us if you have any question.
                    <br/>
                    <br/>

                    % if object.company_id:
                    Best regards,
                    <br/>
                    from
                    <strong>${object.company_id.name | safe}
                    </strong>
                    % endif
                </div>
            </field>
        </record>
        <record id="contract_date_cron_data" model="ir.cron">
            <field name="name">Contract Date End Reminder</field>
            <field name="model_id" ref="hr_contract.model_hr_contract"/>
            <field name="state">code</field>
            <field name="code">model._check_contract_date_end()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="priority">10</field>
            <field name="doall" eval="False"/>
        </record>
        <record id="hr_contract_form_view_inherit" model="ir.ui.view">
            <field name="name">hr.contract.form.view.inherit</field>
            <field name="model">hr.contract</field>
            <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='information']" position="after">
                    <page string="Bonus" name="bonus" class="o_hr_contract_salary_information">
                        <group name="bonus_info">
                            <group name="location_bonus">
                                <field name="location_bonus"/>
                                <field name="location_bonus_percentage"
                                       attrs="{'invisible':[('location_bonus','=',False)]}"/>
                               <!--  <field name="location_bonus_amount"
                                       attrs="{'invisible':[('location_bonus','=',False)]}"/> -->
                            </group>
                            <group name="job_bonus">
                                <field name="job_bonus" attrs="{'readonly': [('job_bonus_percentage', '!=',0 )]}"/>
                                <field name="job_bonus_percentage"
                                       attrs="{'invisible':[('job_bonus','=',False)]}"/>
                               <!--  <field name="job_bonus_amount"
                                       attrs="{'invisible':[('job_bonus','=',False)]}"/> -->

                            </group>
                            <group name="gm_bonus">
                                <field name="general_manager_bonus"/>
                                <field name="general_manager_bonus_percentage"
                                       attrs="{'invisible':[('general_manager_bonus','=',False)]}"/>
                                <!-- <field name="general_manager_bonus_amount"
                                       attrs="{'invisible':[('general_manager_bonus','=',False)]}"/> -->
                            </group>
                        </group>
                    </page>
                </xpath>
                <xpath expr="//field[@name='date_start']" position="after">
                    <field name="work_start_date" class="no_translation"/>
                </xpath>
                <xpath expr="//field[@name='date_start']" position="attributes">
                    <attribute name="class">no_translation</attribute>
                </xpath>
                <xpath expr="//field[@name='date_end']" position="attributes">
                    <attribute name="class">no_translation</attribute>
                </xpath>
                <xpath expr="//field[@name='hr_responsible_id']" position="after">
                    <field name="registration_number"/>
                </xpath>
            </field>
        </record>
         <record id="hr_contract_view_tree_inherit" model="ir.ui.view">
            <field name="name">hr.contract.tree.view.inherit</field>
            <field name="model">hr.contract</field>
            <field name="inherit_id" ref="hr_contract.hr_contract_view_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='date_start']" position="attributes">
                    <attribute name="class">no_translation</attribute>
                </xpath>
                <xpath expr="//field[@name='date_end']" position="attributes">
                    <attribute name="class">no_translation</attribute>
                </xpath>
            </field>
        </record> 
    </data>
</odoo>
