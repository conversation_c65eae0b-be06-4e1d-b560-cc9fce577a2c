<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_partner_form_view_inherit" model="ir.ui.view">
            <field name="name">res.partner.form.view.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.res_partner_view_form_private"/>
            <field name="arch" type="xml">
                <xpath expr="//group[last()]" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="hr_employee_form_view_inherit" model="ir.ui.view">
            <field name="name">hr.employee.form.view.inherit</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='identification_id']" position="after">
                    <field name="id_number"/>
                </xpath>
                <xpath expr="//field[@name='lang']" position="before">
                    <field name="bank_ids"
                           context="{'default_partner_id':address_home_id}">
                        <tree editable="bottom">
                            <field name="bank_id"/>
                            <field name="acc_number"/>
                            <field name="acc_holder_name" invisible="1"/>
                            <field name="partner_id" required="0"/>
                        </tree>
                    </field>
                </xpath> 
                <xpath expr="//field[@name='certificate']" position="after">
                    <field name="certificate_id"/>
                </xpath>
                <xpath expr="//field[@name='certificate']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='study_school']" position="after">
                    <field name="date_of_certification" class="no_translation"/>
                </xpath>
                <xpath expr="//field[@name='visa_expire']" position="attributes">
                    <attribute name="class">no_translation</attribute>
                </xpath>
                <xpath expr="//field[@name='birthday']" position="attributes">
                    <attribute name="class">no_translation</attribute>
                </xpath>
                <xpath expr="//sheet//group//group[2]//field[@name='job_id']" position="after">
                    <field name="bonus_id" options="{'no_create':True, 'no_create_edit': True}"/>
                    <field name="ardano_department_id" options="{'no_create':True, 'no_create_edit': True}"/>
                    <field name="ardano_team_id" options="{'no_create':True, 'no_create_edit': True}"/>
                    <field name="ardano_unit_id" options="{'no_create':True, 'no_create_edit': True}"/>
              </xpath>
                <xpath expr="//sheet//group//group[2]//field[@name='job_id']" position="attributes">
                    <attribute name="string">Job Grade</attribute>
              </xpath>
                <xpath expr="//field[@name='department_id']" position="attributes">
                    <attribute name="string">Division</attribute>
              </xpath>
            </field>
        </record>

        <record id="hr_job_form_view_inherit" model="ir.ui.view">
            <field name="name">hr.job.inherit.form.view</field>
            <field name="model">hr.job</field>
            <field name="inherit_id" ref="hr.view_hr_job_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='recruitment_target']" position="after">
                    <field name="hourly_wage"/>
                  </xpath>
            </field>
        </record>
    </data>
</odoo>
