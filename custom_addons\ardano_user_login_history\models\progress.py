# -*- coding: utf-8 -*-
from odoo import models, fields, api


class UserProgress(models.Model):
    _inherit = 'res.users'

    project_ids = fields.One2many('project.project', 'create_uid', string='مراكز التكلفه')
    len_project_ids = fields.Integer(compute='compute_len_project_ids')

    @api.depends('project_ids')
    def compute_len_project_ids(self):
        for rec in self:
            rec.len_project_ids = len(rec.project_ids)

    work_order_ids = fields.One2many('project.task', 'create_uid', string='أوامر العمل',
                                     domain=[('request_type', '=', 'work_order')])
    work_order_len = fields.Integer(compute='compute_work_order_len')

    @api.depends('work_order_ids')
    def compute_work_order_len(self):
        for rec in self:
            rec.work_order_len = len(rec.work_order_ids)

    delivery_order_ids = fields.One2many('project.task', 'create_uid', string='أوامر التوريد',
                                         domain=[('request_type', '=', 'delivery_order')])

    delivery_order_len = fields.Integer(compute='compute_delivery_order_len')

    @api.depends('delivery_order_ids')
    def compute_delivery_order_len(self):
        for rec in self:
            rec.delivery_order_len = len(rec.delivery_order_ids)


    approval_ids = fields.One2many('approval.request', 'create_uid', string='أوامر سداد')
    approval_len = fields.Integer(compute='compute_approval_len')

    @api.depends('approval_ids')
    def compute_approval_len(self):
        for rec in self:
            rec.approval_len = len(rec.approval_ids)
    bill_ids = fields.One2many('account.move', 'create_uid', string='أوامر صرف',
                               domain=[('move_type', '=', 'in_invoice')])

    bill_len = fields.Integer(compute='compute_bill_len')

    @api.depends('bill_ids')
    def compute_bill_len(self):
        for rec in self:
            rec.bill_len = len(rec.bill_ids)

    def get_projects(self):
        action = {
            'name': 'مراكز التكلفه',
            'type': 'ir.actions.act_window',
            'res_model': 'project.project',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', self.project_ids.ids)]
        }
        return action

    def get_work_orders(self):
            action = {
                'name': 'أوامر العمل',
                'type': 'ir.actions.act_window',
                'res_model': 'project.task',
                'view_mode': 'tree,form',
                'target': 'current',
                'domain': [('id', 'in', self.work_order_ids.ids)]
            }
            return action

    def get_delivery_orders(self):
            action = {
                'name': 'أوامر التوريد',
                'type': 'ir.actions.act_window',
                'res_model': 'project.task',
                'view_mode': 'tree,form',
                'target': 'current',
                'domain': [('id', 'in', self.delivery_order_ids.ids)]
            }
            return action
