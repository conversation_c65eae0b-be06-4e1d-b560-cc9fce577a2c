# -*- coding:utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID
import xlsxwriter
from io import BytesIO
import base64
import logging
import os
_logger = logging.getLogger(__name__)

import time
from datetime import datetime
from dateutil import relativedelta
from odoo.exceptions import ValidationError

class AnalyticREport(models.TransientModel):
    _name = 'analytic.wizard.report'

    partner_id = fields.Many2one('res.partner', string="Customer")
    date_from = fields.Date(string="Date From", required=True, default=time.strftime('%Y-%m-01'))
    date_to = fields.Date(string="Date To", required=True,
                          default=str(datetime.now() + relativedelta.relativedelta(months=+1, day=1, days=-1))[:10])
    #equity_saving = fields.Float(default='11587.**********')
    #warranty = fields.Float(default='246.************')
    #rental = fields.Float(deafult='9077.***********')
    #salary = fields.Float(deafult='3265.***********')

    service_margin = fields.Float(default = '.15')

    gentextfile = fields.Binary('Click On Save As Button To Download File', readonly=True)
    analytics_ids = fields.Many2many('account.analytic.account', string='Analytic Accounts', required=True)




    def print_analytic_report_excel(self):
        if self.date_from > self.date_to:
            raise ValidationError('Start Date must be less than End Date')

        xls_filename = 'analytic_report.xlsx'
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        from_date = self.date_from
        to_date = self.date_to
        if self.partner_id:
            unique_partners = set(self.partner_id)
        else:
            All_analytic_accounts = self.env['account.analytic.account'].search([])
            unique_partners = set(All_analytic_accounts.mapped('partner_id'))
        for partner in unique_partners:
            sheet = workbook.add_worksheet(partner.name)
            format1 = workbook.add_format(
                {'font_size': 10, 'bottom': True, 'right': True, 'left': True, 'top': True, 'align': 'center',
                 'bold': True})
            format2 = workbook.add_format(
                {'font_size': 10, 'bottom': True, 'right': True, 'left': True, 'top': True, 'align': 'center',
                 'bold': True})
            date_style = workbook.add_format({'text_wrap': True, 'num_format': 'dd-mm-yyyy', 'bottom': True, 'right': True, 'left': True, 'top': True})
            format2.set_align('center')
            format2.set_align('vcenter')
            format2.set_color('white')
            format2.set_bg_color('blue')
            header_row_style = workbook.add_format(
                { 'align': 'center', 'border': True})

            #'bg_color': '#919191', 'color': '#000000', 'bold': True,

            header2_row_style = workbook.add_format(
                {'bg_color': '#ffff00', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header2_row_style.set_align('center')
            header2_row_style.set_align('vcenter')
            header3_row_style =  workbook.add_format(
                {'bg_color': '#92d050', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header3_row_style.set_align('center')
            header3_row_style.set_align('vcenter')
            header4_row_style = workbook.add_format(
                {'bg_color': '#fac090', 'color': '#000000', 'bold': True, 'align': 'center', 'border': True})
            header4_row_style.set_align('center')
            header4_row_style.set_align('vcenter')
            header5_row_style = workbook.add_format(
                { 'bold': True, 'align': 'center', 'border': True,'text_wrap': True})
            header5_row_style.set_align('center')
            header5_row_style.set_align('vcenter')

            header6_row_style = workbook.add_format(
                { 'font_size': 12,'align': 'center', 'border': True, 'text_wrap': True})
            header6_row_style.set_align('center')
            header6_row_style.set_align('vcenter')




            sheet.set_column(0, 1, 20)
            sheet.set_column(1, 2, 15)
            sheet.set_column(2, 3, 15)
            sheet.set_column(3, 4, 15)
            sheet.set_column(4, 5, 15)
            sheet.set_column(5, 6, 15)
            sheet.set_column(6, 7, 15)
            sheet.set_column(7, 8, 15)
            sheet.set_column(8, 9, 15)
            sheet.set_column(9, 10, 15)
            sheet.set_column(11, 12, 15)
            sheet.set_column(13, 14, 15)
            sheet.set_column(15, 16, 15)

            sheet.insert_image('B2', '../static/images/logo.png')
            sheet.merge_range('B7:I7', 'المطالبات الشهرية لشركة الوصيد', header6_row_style)
            sheet.merge_range('B8:B8', 'Date ', format2)
            sheet.merge_range('B8:I8', str(' From  ' + str(from_date) + ' To  ' + str(to_date)), header6_row_style)
            sheet.merge_range('B11:E11','التكلفة المباشرة', header2_row_style)
            sheet.merge_range('A12:A13', 'اسم المركز', header5_row_style)
            sheet.merge_range('B12:B13', 'المواد السلعية', header2_row_style)
            sheet.merge_range('C12:C13', 'مواد وادوات التنظيف', header2_row_style)
            sheet.merge_range('D12:D13', 'المصروفات  التشغيلية', header2_row_style)
            sheet.merge_range('E12:E13', 'الإجمالي ', header3_row_style)
            sheet.merge_range('F11:J11', 'التكلفة غير المباشرة', header2_row_style)
            sheet.merge_range('F12:F13', 'مصروفات توفير نقدية', header4_row_style)
            sheet.merge_range('G12:G13', 'م.التأمين', header4_row_style)
            sheet.merge_range('H12:H13', 'م.ايجارات', header4_row_style)
            sheet.merge_range('I12:I13', 'م.مرتبات', header4_row_style)
            sheet.merge_range('J12:J13', 'اجمالي', header3_row_style)
            sheet.merge_range('K11:K13', 'اجمالي التكلفة  المباشرة + غير المباشرة للمركز', header5_row_style)
            sheet.merge_range('L11:L13','هامش الخدمة', header5_row_style)
            sheet.merge_range('M11:M13', 'خدمات نقل قمامة ', header5_row_style)
            sheet.merge_range('N11:N13', 'إجمالي تكلفة المركز', header5_row_style)


            col = 0
            line_row = 12

            data = []
            analytic_ids = self.env['account.analytic.account'].search([('partner_id','=',partner.id)])
            total_balance = 0
            total_goods_balance = 0
            total_cleaning_balance = 0
            total_other_balance = 0
            total_cleaning_garbage_amount = 0
            total_equity_saving_amount = 0
            total_warranty_amount = 0
            total_rental_amount = 0
            total_salary_amount = 0

            for analytic in analytic_ids:
                lines = self.env['account.analytic.line'].search(
                    [('account_id', '=', analytic.id),
                     ('date', '>=', self.date_from), ('date', '<=', self.date_to)], order="id desc")
                for line in lines:
                    total_balance += line.amount
                    total_goods_balance += line.goods_balance
                    total_cleaning_balance += line.cleaning_balance
                    total_other_balance += line.other_balance


            for analytic in analytic_ids:
                account_row = line_row
                account_col = col
                line_row += 1
                balance = 0
                goods_balance = 0
                cleaning_balance =0
                other_balance = 0
                cleaning_garbage_amount = 0
                equity_saving_amount = 0
                warranty_amount = 0
                rental_amount = 0
                salary_amount = 0


                # calculate garbage amount
                account_moves_garbage = self.env['account.move'].search([
                    ('indirect_cost','=','garbage_account'),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to),
                    ('analytic_account_custom' , '=' , analytic.id)
                ] , limit=1 )
                if account_moves_garbage :
                    cleaning_garbage_amount_line = self.env['account.move.line'].search([
                        ('move_id','in', account_moves_garbage.ids),
                    ],limit=1)
                    if cleaning_garbage_amount_line:
                        cleaning_garbage_amount = cleaning_garbage_amount_line.debit
                total_cleaning_garbage_amount = total_cleaning_garbage_amount + cleaning_garbage_amount

                # calculate equity saving
                account_moves_equity_saving = self.env['account.move'].search([
                    ('indirect_cost', '=', 'equity_saving_account'),
                    ('partner_id_custom','=',partner.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to)
                ], limit=1)
                if account_moves_equity_saving:
                    equity_saving_amount_line = self.env['account.move.line'].search([
                        ('debit', '>', 0),
                        ('move_id', 'in', account_moves_equity_saving.ids),
                    ], limit=1)
                    if equity_saving_amount_line:
                        equity_saving_amount = equity_saving_amount_line.debit
                total_equity_saving_amount = total_equity_saving_amount + equity_saving_amount

                # calculate warranty
                account_moves_warranty = self.env['account.move'].search([
                    ('indirect_cost', '=', 'warranty_account'),
                    ('partner_id_custom', '=', partner.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to)
                ], limit=1)
                if account_moves_warranty:
                    warranty_amount_line = self.env['account.move.line'].search([
                        ('debit', '>', 0),
                        ('move_id', 'in', account_moves_warranty.ids),
                    ], limit=1)
                    if warranty_amount_line:
                        warranty_amount = warranty_amount_line.debit
                total_warranty_amount = total_warranty_amount + warranty_amount

                # calculate rental
                account_moves_rental = self.env['account.move'].search([
                    ('indirect_cost', '=', 'rental_account'),
                    ('partner_id_custom', '=', partner.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to)
                ], limit=1)
                if account_moves_rental:
                    rental_amount_line = self.env['account.move.line'].search([
                        ('debit', '>', 0),
                        ('move_id', 'in', account_moves_rental.ids),
                    ], limit=1)
                    if rental_amount_line:
                        rental_amount = rental_amount_line.debit
                total_rental_amount = total_rental_amount + rental_amount

                # calculate salary
                account_moves_salary = self.env['account.move'].search([
                    ('indirect_cost', '=', 'salary_account'),
                    ('partner_id_custom', '=', partner.id),
                    ('date', '>=', self.date_from),
                    ('date', '<=', self.date_to)
                ], limit=1)
                if account_moves_salary:
                    salary_amount_line = self.env['account.move.line'].search([
                        ('debit', '>', 0),
                        ('move_id', 'in', account_moves_salary.ids),
                    ], limit=1)
                    if salary_amount_line:
                        salary_amount = salary_amount_line.debit
                total_salary_amount = total_salary_amount + salary_amount


                lines = self.env['account.analytic.line'].search(
                    [('account_id', '=', analytic.id),
                     ('date', '>=', self.date_from), ('date', '<=', self.date_to)], order="id desc")
                for reco in lines :
                    balance += reco.amount
                    goods_balance += reco.goods_balance
                    cleaning_balance += reco.cleaning_balance
                    other_balance += reco.other_balance


                sheet.write(line_row, col , analytic.name, header_row_style)
                sheet.write(line_row, col + 1, goods_balance, header_row_style)
                sheet.write(line_row, col + 2, cleaning_balance, header_row_style)
                #sheet.merge_range(line_row, col+3, line_row, col+5, 'Open Balance', header_row_style)
                sheet.write(line_row, col + 3, other_balance, header_row_style)
                sheet.write(line_row, col + 4, balance, header_row_style)
                indirect_equity_saving_line =  balance/ total_balance * equity_saving_amount  if total_balance > 0 else 0
                sheet.write(line_row, col + 5,indirect_equity_saving_line, header_row_style)
                indirect_warranty_line = balance / total_balance * warranty_amount if total_balance > 0 else 0
                sheet.write(line_row, col + 6, indirect_warranty_line, header_row_style)
                indirect_rental_line = balance / total_balance * rental_amount if total_balance > 0 else 0
                sheet.write(line_row, col + 7,balance / total_balance * rental_amount if total_balance > 0 else 0, header_row_style)
                indirect_salary_line = balance / total_balance * salary_amount if total_balance > 0 else 0
                sheet.write(line_row, col + 8, indirect_salary_line, header_row_style)
                total_indirect_line = indirect_equity_saving_line + indirect_warranty_line + indirect_rental_line + indirect_salary_line
                sheet.write(line_row, col + 9, total_indirect_line, header_row_style)
                sheet.write(line_row,col + 10 , balance + total_indirect_line)
                sheet.write(line_row,col + 11 , (balance + total_indirect_line) * self.service_margin)
                sheet.write(line_row, col + 12, cleaning_garbage_amount)
                sheet.write(line_row, col + 13,  balance + total_indirect_line + (balance + total_indirect_line) * self.service_margin + cleaning_garbage_amount)

            line_row+=1
            sheet.write(line_row, col,'اجمالي المطالبات الداخلية', header_row_style)
            sheet.write(line_row, col + 1,total_goods_balance , header_row_style)
            sheet.write(line_row, col + 2, total_cleaning_balance, header_row_style)
            # sheet.merge_range(line_row, col+3, line_row, col+5, 'Open Balance', header_row_style)
            sheet.write(line_row, col + 3, total_other_balance, header_row_style)
            sheet.write(line_row, col + 4, total_balance, header_row_style)
            indirect_equity_saving_line = total_balance / total_balance * equity_saving_amount if total_balance > 0 else 0
            sheet.write(line_row, col + 5, indirect_equity_saving_line, header_row_style)
            indirect_warranty_line = total_balance / total_balance * warranty_amount if total_balance > 0 else 0
            sheet.write(line_row, col + 6, indirect_warranty_line, header_row_style)
            indirect_rental_line = total_balance / total_balance * rental_amount if total_balance > 0 else 0
            sheet.write(line_row, col + 7, total_balance / total_balance * rental_amount if total_balance > 0 else 0, header_row_style)
            indirect_salary_line = total_balance / total_balance * salary_amount if total_balance > 0 else 0
            sheet.write(line_row, col + 8, indirect_salary_line, header_row_style)
            total_indirect_line = indirect_equity_saving_line + indirect_warranty_line + indirect_rental_line + indirect_salary_line
            sheet.write(line_row, col + 9, total_indirect_line, header_row_style)
            sheet.write(line_row, col + 10, total_balance + total_indirect_line)
            sheet.write(line_row, col + 11, (total_balance + total_indirect_line) * self.service_margin)
            sheet.write(line_row, col + 12, total_cleaning_garbage_amount)
            sheet.write(line_row, col + 13, total_balance + total_indirect_line + (
                        total_balance + total_indirect_line) * self.service_margin +total_cleaning_garbage_amount)

        workbook.close()
        output.seek(0)
        self.write({'gentextfile': base64.encodebytes(output.getvalue())})
        return {
            'type': 'ir.actions.act_url',
            'name': 'Test Report',
            'url': '/web/content/analytic.wizard.report/{}/gentextfile/المطالبات الشهرية_{}_{}.xlsx?download=true'.format(
                self.id,
                self.date_from,
                self.date_to
            ),
            'target': 'new'
        }

