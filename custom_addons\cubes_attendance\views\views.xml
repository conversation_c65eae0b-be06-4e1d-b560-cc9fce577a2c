<odoo>
  <data>
    <record id="hr_attendance_server_action" model="ir.actions.server">
			<field name="name">Update Attendance</field>
			<field name="type">ir.actions.server</field>
			<field name="model_id" ref="model_hr_attendance"/>
			<field name="state">code</field>
			<field name="code">
				if records:
				action = records.update_check_in_out()
			</field>
			<field name="binding_model_id" ref="model_hr_attendance"/>
		</record>
  </data>
</odoo>
