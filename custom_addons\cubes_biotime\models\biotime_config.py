# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import requests, json
from datetime import datetime, timedelta
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

_logger = logging.getLogger(__name__)

class BioTime(models.Model):
    _name = 'biotime.config'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'BioTime Configuration'

    name = fields.Char(string="Name", tracking=True)
    server_url = fields.Char(string="Server URL", default="http://**************:8081", tracking=True)
    username = fields.Char(string="Username", default="admin", tracking=True)
    password = fields.Char(string="Password", default="saddam@1990")
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company.id, tracking=True)

    pull_from_date = fields.Datetime('Pull From Date')
    pull_to_date = fields.Datetime('Pull To Date')

    def action_get_all_terminals(self):
        """Get all terminals from Biotime"""
        for rec in self:
            terminal_env = self.env['biotime.terminal'].sudo()
            url = "%s/iclock/api/terminals/" % rec.server_url

            payload = {}
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'JWT %s' % rec.generate_access_token().get('token')
            }

            page = 1
            while True:
                response = requests.request("GET", f"{url}?page={page}", headers=headers, data=payload)
                data = response.json()

                if data.get('data'):
                    for terminal in data['data']:
                        check = terminal_env.search([
                            ('biotime_id', '=', rec.id),
                            ('terminal_sn', '=', terminal.get('sn')),
                        ])
                        if not check:
                            terminal_env.create({
                                'name': terminal.get('terminal_name') if terminal.get(
                                    'terminal_name') else 'New Device',
                                'terminal_id': terminal.get('id'),
                                'terminal_sn': terminal.get('sn'),
                                'ip_address': terminal.get('ip_address'),
                                'alias': terminal.get('alias'),
                                'terminal_tz': terminal.get('terminal_tz'),
                                'biotime_id': rec.id
                            })

                    if 'next' in data and data['next']:
                        page += 1
                    else:
                        break
                else:
                    break

    def action_get_all_employees(self, page=1):
        """Get all employees from Biotime"""
        for rec in self:
            employee_env = self.env['biotime.employee'].sudo()
            url = "%s/personnel/api/employees/?page=%s" % (rec.server_url, page)

            payload = {}
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'JWT %s' % rec.generate_access_token().get('token')
            }

            response = requests.request("GET", url, headers=headers, data=payload)
            data = response.json()
            if data.get('data'):
                for employee in data['data']:
                    check = employee_env.search([
                        ('biotime_id', '=', rec.id),
                        ('employee_id', '=', employee.get('id')),
                    ])
                    if not check:
                        employee_env.create({
                            'name': employee.get('first_name'),
                            'employee_id': employee.get('id'),
                            'emp_code': employee.get('emp_code'),
                            'biotime_id': rec.id
                        })
                    else:
                        check.emp_code = employee.get('emp_code')
                        check.employee_id = employee.get('id')
            if data.get('next'):
                self.action_get_all_employees(page=data.get('next').split('page=')[1])

    def action_pull_specific_dates(self):
        """Pull attendance for specific dates"""
        self.ensure_one()
        if not self.pull_from_date or not self.pull_to_date:
            raise ValidationError(_("Please set both From and To dates"))

        # Process one day at a time to avoid timeouts
        current_date = self.pull_from_date
        while current_date <= self.pull_to_date:
            try:
                _logger.info(f"Processing date: {current_date.date()}")
                self.action_get_today_attendance(
                    from_date=current_date,
                    to_date=current_date + timedelta(days=1)
                )
                self.env.cr.commit()  # Commit after each successful day
                current_date += timedelta(days=1)
            except Exception as e:
                _logger.error(f"Error processing date {current_date.date()}: {str(e)}")
                self.env.cr.rollback()
                raise ValidationError(_(
                    "Error processing date %s: %s\nPlease try again.",
                    current_date.date(),
                    str(e)
                ))

    def generate_access_token(self):
        for rec in self:
            try:
                url = "%s/jwt-api-token-auth/" % rec.server_url
                
                session = requests.Session()
                retries = Retry(
                    total=5,
                    backoff_factor=1,
                    status_forcelist=[408, 429, 500, 502, 503, 504]
                )
                adapter = HTTPAdapter(max_retries=retries)
                session.mount("http://", adapter)
                session.mount("https://", adapter)

                response = session.post(
                    url,
                    headers={'Content-Type': 'application/json'},
                    json={
                        "username": rec.username,
                        "password": rec.password
                    },
                    timeout=(10, 30)  # (connect timeout, read timeout)
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    _logger.error(f"Token generation failed: Status {response.status_code}, Response: {response.text}")
                    raise ValidationError(_("Failed to generate access token."))

            except requests.exceptions.Timeout:
                _logger.error("Timeout while generating access token")
                raise ValidationError(_("Connection to Biotime server timed out. Please try again later."))
            except requests.exceptions.ConnectionError:
                _logger.error("Connection error while generating access token")
                raise ValidationError(_("Could not connect to Biotime server. Please check your network connection."))
            except Exception as e:
                _logger.error(f"Error generating access token: {str(e)}")
                raise ValidationError(_("Error generating access token: %s") % str(e))

    def action_get_today_attendance(self, from_date=False, to_date=False):
        for rec in self:
            # Process one terminal at a time to avoid overlapping
            for tz in self.env['biotime.terminal'].sudo().search([('biotime_id', '=', rec.id)]):
                _logger.info(f"Processing terminal: {tz.terminal_sn}")
                page = 1
                all_transactions = []
                
                while True:
                    response = tz.action_get_transactions(
                        page=page,
                        from_date=from_date,
                        to_date=to_date
                    )
                    
                    if not response.get('data'):
                        break

                    current_records = response.get('data', [])
                    
                    # Check for duplicate records
                    new_records = [r for r in current_records if r not in all_transactions]
                    all_transactions.extend(new_records)
                    
                    _logger.info(f"Added {len(new_records)} new records. Total records so far: {len(all_transactions)}")
                    
                    if not response.get('next'):
                        break
                        
                    page += 1

                if all_transactions:
                    # Group transactions by employee and date
                    employee_punches = {}
                    
                    for record in sorted(all_transactions, key=lambda x: x['punch_time']):
                        emp_code = record.get('emp_code')
                        punch_time = record.get('punch_time') or '00:00:00'
                        punch_datetime = datetime.strptime(punch_time, '%Y-%m-%d %H:%M:%S')
                        adjusted_time = punch_datetime - timedelta(hours=2)
                        
                        if emp_code not in employee_punches:
                            employee_punches[emp_code] = []
                        employee_punches[emp_code].append(adjusted_time)

                    # Process punches for each employee
                    for emp_code, punches in employee_punches.items():
                        check_employee = self.env['biotime.employee'].sudo().search([
                            ('emp_code', '=', emp_code),
                            ('biotime_id', '=', rec.id)
                        ], limit=1)

                        if check_employee and check_employee.odoo_employee_id:
                            sorted_punches = sorted(punches)
                            
                            for i in range(0, len(sorted_punches) - 1, 2):
                                check_in = sorted_punches[i]
                                check_out = sorted_punches[i + 1] if i + 1 < len(sorted_punches) else check_in

                                existing_attendance = self.env['hr.attendance'].sudo().search([
                                    ('employee_id', '=', check_employee.odoo_employee_id.id),
                                    ('check_in', '=', check_in),
                                    ('check_out', '=', check_out)
                                ], limit=1)

                                if not existing_attendance:
                                    self.env['hr.attendance'].sudo().create({
                                        'employee_id': check_employee.odoo_employee_id.id,
                                        'check_in': check_in,
                                        'check_out': check_out
                                    })
                    
                    # Commit after each terminal to save progress
                    self.env.cr.commit()


