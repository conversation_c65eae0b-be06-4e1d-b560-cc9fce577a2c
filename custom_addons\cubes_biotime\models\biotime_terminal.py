# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import json
from datetime import datetime

_logger = logging.getLogger(__name__)

class BioTimeTerminal(models.Model):
    _name = 'biotime.terminal'

    name = fields.Char(string="Name")
    terminal_id = fields.Char(string="Terminal ID")
    terminal_sn = fields.Char(string="Terminal SN")
    ip_address = fields.Char(string="IP Address")
    alias = fields.Char(string="Alias")
    terminal_tz = fields.Char(string="Terminal TZ")
    biotime_id = fields.Many2one('biotime.config', string="Biotime")
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company.id)

    def _get_session(self):
        """Create a requests session with retry logic"""
        session = requests.Session()
        
        # Define retry strategy
        retries = Retry(
            total=5,  # number of retries
            backoff_factor=1,  # wait 1, 2, 4, 8, 16 seconds between retries
            status_forcelist=[408, 429, 500, 502, 503, 504],  # retry on these HTTP status codes
        )
        
        # Mount the adapter with retry strategy for both HTTP and HTTPS
        adapter = HTTPAdapter(max_retries=retries)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session

    def action_get_transactions(self, page=1, from_date=False, to_date=False):
        for rec in self:
            try:
                if not from_date:
                    from_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                if not to_date:
                    to_date = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)

                start_time = from_date.strftime('%Y-%m-%d %H:%M:%S')
                end_time = to_date.strftime('%Y-%m-%d %H:%M:%S')

                url = "%s/iclock/api/transactions/?page=%s&limit=100&terminal_sn=%s&start_time=%s&end_time=%s" % (
                    rec.biotime_id.server_url,
                    page,
                    rec.terminal_sn,
                    start_time,
                    end_time
                )

                _logger.info(f"Fetching page {page} for terminal {rec.terminal_sn}")
                _logger.info(f"Date range: {start_time} to {end_time}")

                session = self._get_session()
                
                # Add timeout parameters
                response = session.get(
                    url,
                    headers={
                        'Content-Type': 'application/json',
                        'Authorization': 'JWT %s' % rec.biotime_id.generate_access_token().get('token')
                    },
                    timeout=(10, 30)  # (connect timeout, read timeout)
                )

                if response.status_code == 200:
                    data = response.json()
                    records_count = len(data.get('data', []))
                    _logger.info(f"Received {records_count} records for terminal {rec.terminal_sn} on page {page}")
                    return data
                else:
                    _logger.error(f"API Error: Status {response.status_code}, Response: {response.text}")
                    raise ValidationError(_("Failed to fetch transactions from the terminal."))

            except requests.exceptions.Timeout as e:
                _logger.error(f"Timeout error for terminal {rec.terminal_sn}: {str(e)}")
                raise ValidationError(_("Connection to Biotime server timed out. Please try again later."))
            except requests.exceptions.ConnectionError as e:
                _logger.error(f"Connection error for terminal {rec.terminal_sn}: {str(e)}")
                raise ValidationError(_("Could not connect to Biotime server. Please check your network connection."))
            except Exception as e:
                _logger.error(f"Error fetching transactions for terminal {rec.terminal_sn}: {str(e)}")
                raise ValidationError(_("Error fetching transactions: %s") % str(e))

