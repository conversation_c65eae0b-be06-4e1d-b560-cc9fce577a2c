<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_biotime_config_form" model="ir.ui.view">
        <field name="name">biotime.config.form</field>
        <field name="model">biotime.config</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_get_all_terminals" type="object" string="GET TERMINALS" class="oe_highlight"/>
                    <button name="action_get_all_employees" type="object" string="PULL EMPLOYEES" class="oe_highlight"/>
                    <button name="action_get_today_attendance" type="object" string="PULL TODAY ATTENDANCE" class="oe_highlight"/>
                    <button name="action_pull_specific_dates" 
                            attrs="{'invisible': ['|',('pull_from_date','=',False),('pull_to_date','=',False)]}" 
                            type="object" 
                            string="PULL ATTENDANCE FROM/TO" 
                            class="oe_highlight"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Server Information" name="server_information">
                            <field name="server_url" />
                            <field name="username" />
                            <field name="password" password="1" />
                            <field name="company_id" readonly="1" force_save="1" />
                        </group>
                        <group string="Pull Specific Date" name="pull_date">
                            <field name="pull_from_date" />
                            <field name="pull_to_date" />
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record id="view_biotime_config_tree" model="ir.ui.view">
        <field name="name">view.biotime_config.tree</field>
        <field name="model">biotime.config</field>
        <field name="arch" type="xml">
            <tree string="Biotime Config">
                <field name="name"/>
                <field name="server_url" />
                <field name="username" />
                <field name="password" password="1"/>
                <field name="company_id" />
            </tree>
        </field>
    </record>
    <record id="action_biotime_config_view" model="ir.actions.act_window">
        <field name="name">Biotime Config</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">biotime.config</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>
