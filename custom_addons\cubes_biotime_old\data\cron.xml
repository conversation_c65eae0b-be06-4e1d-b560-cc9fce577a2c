<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data>
        <record id="cron_pull_today_attendance" model="ir.cron">
            <field name="name">Biotime: Pull Today Attendance</field>
            <field name="model_id" ref="model_biotime_config" />
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active" eval="True" />
            <field name="doall" eval="False" />
            <field name="state">code</field>
            <field name="code">model.cron_get_today_attendance()</field>
        </record>
    </data>
</odoo>