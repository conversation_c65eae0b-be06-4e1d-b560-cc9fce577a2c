# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import requests, json
from datetime import datetime, timedelta


class BioTime(models.Model):
    _name = 'biotime.config'

    name = fields.Char(string="Name")
    server_url = fields.Char(string="Server URL", default="http://102.222.252.74:8081")
    username = fields.Char(string="Username", default="admin")
    password = fields.Char(string="Password", default="saddam@1990")
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company.id)

    pull_from_date = fields.Datetime('Pull From Date')
    pull_to_date = fields.Datetime('Pull To Date')

    def action_pull_specific_dates(self):
        for rec in self.env['biotime.config'].sudo().search([]):
            rec.action_get_today_attendance(from_date=rec.pull_from_date, to_date=rec.pull_to_date)

    def generate_access_token(self):
        for rec in self:
            url = "%s/jwt-api-token-auth/" % rec.server_url
            payload = json.dumps({
                "username": rec.username,
                "password": rec.password
            })
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=payload)
            print(response.json())
            return response.json()

    def action_get_all_terminals(self):
        for rec in self:
            terminal_env = self.env['biotime.terminal'].sudo()
            url = "%s/iclock/api/terminals/" % rec.server_url

            payload = {}
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'JWT %s' % rec.generate_access_token().get('token')
            }

            page = 1
            while True:
                response = requests.request("GET", f"{url}?page={page}", headers=headers, data=payload)
                data = response.json()

                if data.get('data'):
                    for terminal in data['data']:
                        check = terminal_env.search([
                            ('biotime_id', '=', rec.id),
                            ('terminal_sn', '=', terminal.get('sn')),
                        ])
                        if not check:
                            terminal_env.create({
                                'name': terminal.get('terminal_name') if terminal.get(
                                    'terminal_name') else 'New Device',
                                'terminal_id': terminal.get('id'),
                                'terminal_sn': terminal.get('sn'),
                                'ip_address': terminal.get('ip_address'),
                                'alias': terminal.get('alias'),
                                'terminal_tz': terminal.get('terminal_tz'),
                                'biotime_id': rec.id
                            })

                    # تحقق مما إذا كانت هناك صفحة تالية
                    if 'next' in data and data['next']:
                        page += 1
                    else:
                        break  # لا توجد صفحات إضافية
                else:
                    break  # لا توجد بيانات

    def action_get_all_employees(self, page=1):
        for rec in self:
            employee_env = self.env['biotime.employee'].sudo()
            url = "%s/personnel/api/employees/?page=%s" % (rec.server_url, page)

            payload = {}
            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'JWT %s' % rec.generate_access_token().get('token')
            }

            response = requests.request("GET", url, headers=headers, data=payload)
            print(response.text)
            data = response.json()
            if data.get('data'):
                for employee in data['data']:
                    check = employee_env.search([
                        ('biotime_id', '=', rec.id),
                        ('employee_id', '=', employee.get('id')),
                    ])
                    if not check:
                        employee_env.create({
                            'name': employee.get('first_name'),
                            'employee_id': employee.get('id'),
                            'emp_code': employee.get('emp_code'),
                            'biotime_id': rec.id
                        })
                    else:
                        check.emp_code = employee.get('emp_code')
                        check.employee_id = employee.get('id')
            if data.get('next'):
                self.action_get_all_employees(page=data.get('next').split('page=')[1])

    def cron_get_today_attendance(self):
        for rec in self.env['biotime.config'].sudo().search([]):
            rec.action_get_today_attendance()

    def action_get_today_attendance(self, from_date=False, to_date=False):
        for rec in self:
            for tz in self.env['biotime.terminal'].sudo().search([('biotime_id', '=', rec.id)]):
                if from_date and to_date:
                    transactions = tz.action_get_transactions(from_date=rec.pull_from_date,
                                                              to_date=rec.pull_to_date).get('data')
                else:
                    transactions = tz.action_get_transactions().get('data')
                if transactions:
                    records = sorted(transactions, key=lambda x: x['punch_time'])
                    for record in records:
                        check_employee = self.env['biotime.employee'].sudo().search([
                            ('emp_code', '=', record.get('emp_code')),
                            ('biotime_id', '=', rec.id)
                        ], limit=1)

                        if check_employee and check_employee.odoo_employee_id:
                            check_last_attendance = self.env['hr.attendance'].sudo().search([
                                ('employee_id', '=', check_employee.odoo_employee_id.id),
                                ('check_in', '>=', fields.Date.today())  # تأكد أن check_in هو لليوم الحالي
                            ], limit=1, order='id desc')

                            punch_time = record.get(
                                'punch_time') or '00:00:00'  # إذا كان punch_time مفقود، افتراضيًا لـ 00:00:00

                            # تحويل وقت البصمة إلى datetime
                            punch_datetime = datetime.strptime(punch_time, '%Y-%m-%d %H:%M:%S')

                            # طرح ساعتين من وقت البصمة
                            adjusted_punch_time = punch_datetime - timedelta(hours=2)

                            if check_last_attendance and not check_last_attendance.check_out:
                                # إذا كان هناك check_in اليوم ولم يتم تسجيل check_out بعد، تحديث check_out
                                check_last_attendance.write({
                                    'check_out': adjusted_punch_time
                                })
                            else:
                                # إذا لم يكن هناك check_in لليوم الحالي، إنشاء سجل حضور جديد
                                attendance = self.env['hr.attendance'].sudo().create({
                                    'employee_id': check_employee.odoo_employee_id.id,
                                    'check_in': adjusted_punch_time
                                })

                                # تعيين check_out مبدئيًا ليكون نفس check_in إذا لم يتم تسجيله
                                attendance.write({
                                    'check_out': adjusted_punch_time
                                })


