# -*- coding: utf-8 -*-
import logging
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
import requests
import json
from datetime import datetime  # Added import for datetime


class BioTimeTerminal(models.Model):
    _name = 'biotime.terminal'

    name = fields.Char(string="Name")
    terminal_id = fields.Char(string="Terminal ID")
    terminal_sn = fields.Char(string="Terminal SN")
    ip_address = fields.Char(string="IP Address")
    alias = fields.Char(string="Alias")
    terminal_tz = fields.Char(string="Terminal TZ")
    biotime_id = fields.Many2one('biotime.config', string="Biotime")
    company_id = fields.Many2one('res.company', string="Company", default=lambda self: self.env.company.id)

    def action_get_transactions(self, page=1, from_date=False, to_date=False):
        for rec in self:
            try:
                # Use current datetime for today if no dates are provided
                if not from_date:
                    from_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)  # Start of today
                if not to_date:
                    to_date = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)  # End of today

                # Format the dates into the proper string format
                today_start = from_date.strftime('%Y-%m-%d %H:%M:%S')
                today_end = to_date.strftime('%Y-%m-%d %H:%M:%S')

                # Build the URL with formatted dates
                url = "%s/iclock/api/transactions/?page=%s&limit=10000&terminal_sn=%s&start_time=%s&end_time=%s" % (
                    rec.biotime_id.server_url,
                    page,
                    rec.terminal_sn,
                    today_start,
                    today_end
                )

                logging.info(
                    f"Requesting transactions from URL: {url}")  # Changed print to logging for better traceability

                payload = {}
                headers = {
                    'Content-Type': 'application/json',
                    'Authorization': 'JWT %s' % rec.biotime_id.generate_access_token().get('token')
                }

                # Sending the request to the BioTime server
                response = requests.request("GET", url, headers=headers, data=payload)

                # Check for successful response (status code 200)
                if response.status_code == 200:
                    return response.json()  # Return the JSON response if successful
                else:
                    logging.error(f"Failed to fetch transactions: {response.status_code} - {response.text}")
                    raise ValidationError(_("Failed to fetch transactions from the terminal."))

            except requests.exceptions.RequestException as e:
                logging.error(f"Error during request: {str(e)}")
                raise ValidationError(
                    _("There was an issue connecting to the BioTime server. Please check your configuration and network."))

