# -*- coding: utf-8 -*-
# from odoo import http


# class CubesProductCustom(http.Controller):
#     @http.route('/cubes_product_custom/cubes_product_custom', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/cubes_product_custom/cubes_product_custom/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('cubes_product_custom.listing', {
#             'root': '/cubes_product_custom/cubes_product_custom',
#             'objects': http.request.env['cubes_product_custom.cubes_product_custom'].search([]),
#         })

#     @http.route('/cubes_product_custom/cubes_product_custom/objects/<model("cubes_product_custom.cubes_product_custom"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('cubes_product_custom.object', {
#             'object': obj
#         })
