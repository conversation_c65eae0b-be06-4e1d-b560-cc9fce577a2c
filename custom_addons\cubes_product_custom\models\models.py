# -*- coding: utf-8 -*-

from odoo import models, fields, api

class product_category(models.Model):
    _inherit = 'product.category'

    cubes_type = fields.Selection(string='Type',
                                  selection=[('goods', 'سلعي'), ('cleaning', 'تنظيف'),
                                             ('service', 'خدمي'), ('Other', 'أخرى'),
                                             ('hypothetical', 'افتراضي')],
                                  default='hypothetical')

class product_template(models.Model):
    _inherit = 'product.template'

    cubes_type = fields.Selection(string='Type',
                                         selection=[('goods', 'سلعي'), ('cleaning', 'تنظيف'),
                                                    ('service', 'خدمي'), ('Other', 'أخرى'),
                                                    ('hypothetical', 'افتراضي')],
                                         default='hypothetical', related='categ_id.cubes_type', store=True)


