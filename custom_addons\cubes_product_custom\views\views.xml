<odoo>
  <data>

      <!-- Product Category View -->
      <record id="cubes_product_category_inherit" model="ir.ui.view">
      <field name="name">Cubes_products category inherit</field>
      <field name="model">product.category</field>
      <field name="inherit_id" ref="product.product_category_form_view" />
      <field name="arch" type="xml">
        <xpath expr="//field[@name='parent_id']" position="after">
          <field name="cubes_type"/>
        </xpath>
      </field>
      </record>

     <record id="cubes_product_template_inherit" model="ir.ui.view">
      <field name="name">Cubes_products template inherit</field>
      <field name="model">product.template</field>
      <field name="inherit_id" ref="product.product_template_only_form_view" />
      <field name="arch" type="xml">
        <xpath expr="//field[@name='detailed_type']" position="after">
          <field name="cubes_type"/>
        </xpath>
      </field>
    </record>

   <record id="cubes_product_template_search_view" model="ir.ui.view">
        <field name="name">cubes.product.template.search</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">
                <filter name="type" position="after">
                    <filter string="Type" name="cubes_type" context="{'group_by':'cubes_type'}"/>
                </filter>
        </field>
    </record>


  </data>
</odoo>