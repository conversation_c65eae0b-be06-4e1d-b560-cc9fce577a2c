/** @odoo-module **/

import { DatePicker } from "@web/core/datepicker/datepicker";
import { patch } from "@web/core/utils/patch";

patch(DatePicker.prototype, "date_field_no_translation.DatePicker", {
	getOptions() {
		if ($(this.inputRef.el.parentElement.parentElement).hasClass("no_translation")){
			return {
				format: "MM/dd/yyyy",
				locale: this.props.locale || (this.date && this.date.locale),
				numberingSystem: "latn",
			};
		} else {
			return this._super(...arguments);
		}
	}
});