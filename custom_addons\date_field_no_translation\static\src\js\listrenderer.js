/** @odoo-module */

import { ListRenderer } from "@web/views/list/list_renderer";
import { localization } from "@web/core/l10n/localization";
const { DateTime, Settings } = luxon;
import { patch } from "@web/core/utils/patch";
import { getFormattedValue } from "@web/views/utils";

const NUMBERING_SYSTEMS = [
	[/^ar-(sa|sy|001)$/i, "arab"],
	[/^bn/i, "beng"],
	[/^bo/i, "tibt"],
	// [/^fa/i, "Farsi (Persian)"], // No numberingSystem found in Intl
	// [/^(hi|mr|ne)/i, "Hindi"], // No numberingSystem found in Intl
	// [/^my/i, "Burmese"], // No numberingSystem found in Intl
	[/^pa-in/i, "guru"],
	[/^ta/i, "tamldec"],
	[/.*/i, "latn"],
];

patch(ListRenderer.prototype, "date_field_no_translation.ListRenderer", {
	getFormattedValue(column, record) {
		if (column.rawAttrs.class == "no_translation") {
			record.localization_dateFormat = localization.dateFormat
			Settings.defaultNumberingSystem = "latn"
			localization.dateFormat = "MM/dd/yyyy"
		} else {
			var locale = Settings.defaultLocale
			for (const [re, numberingSystem] of NUMBERING_SYSTEMS) {
				if (re.test(locale)) {
					Settings.defaultNumberingSystem = numberingSystem;
					break;
				}
			}

			if (record.localization_dateFormat) {
				localization.dateFormat = record.localization_dateFormat
			}
		}
		const fieldName = column.name;
		return getFormattedValue(record, fieldName, column.rawAttrs);
	}
})