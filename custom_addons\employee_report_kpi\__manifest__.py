# -*- coding: utf-8 -*-
{
    'name': "Employee Report KPI",

    'summary': """
        This model displays an evaluation of the employee's job performance during the month """,


'category': 'Human Resources',
'version': '16.0.0.0.0',

# any module necessary for this one to work correctly
'depends': ['hr','base','ardano_hr_customization'],

# always loaded
'data': [
    'security/ir.model.access.csv',
    'view/views.xml',
    'view/menu_item.xml',
    'wizard/employee_report_annual.xml',
    'report/kpi_report.xml',
],
"assets": {
    'web.assets_backend': [
    'employee_report_kpi/static/src/js/action_manager.js'
    ],
},

# only loaded in demonstration mode
'demo': [
],
}

