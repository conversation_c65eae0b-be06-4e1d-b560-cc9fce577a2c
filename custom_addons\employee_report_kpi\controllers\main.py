import json
from odoo import http
from odoo.http import request, content_disposition
from odoo.tools import html_escape

class XLSXReport(http.Controller):

    @http.route('/xlsx_reports', type='http', auth="user", methods=["POST"])
    def get_report_xlsx(self, model, options, output_format, report_name, **kw):
        uid = request.session.uid
        report_obj = request.env[model].with_user(uid)
        options = json.loads(options)
        token = 'dummy-because-api-expects-one'
        try:
            if output_format == 'xlsx':
                response = request.make_response(
                    None,
                    headers=[
                        ('Content-Type', 'application/vnd.ms-excel'),
                        ('Content-Disposition', content_disposition(report_name + '.xlsx'))
                    ]
                )
                report_obj.get_xlsx_report(options, response)
                response.set_cookie('fileToken', token)
                return response
        except Exception as e:
            se = http.serialize_exception(e)
            error = {
                'code': 200,
                'message': 'Odoo server error',
                'data': se
            }
            return request.make_response(html_escape(json.dumps(error)))
