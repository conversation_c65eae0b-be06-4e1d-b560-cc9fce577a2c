# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* employee_report_kpi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e-20240522\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-30 17:03+0000\n"
"PO-Revision-Date: 2024-08-30 19:56+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5);\n"
"X-Generator: Poedit 3.4.4\n"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__presence__evalu_items__absence_without_permission_during_the_month
#, fuzzy
msgid "Absence without permission during the month"
msgstr "الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__disciplined__evalu_items_dis__adherence_to_occupational_safety_rules
#, fuzzy
msgid "Adherence to occupational safety rules"
msgstr "الالتزام بقواعد السلامة المهنية"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__disciplined__evalu_items_dis__adherence_to_regulations_and_instructions
#, fuzzy
msgid "Adherence to regulations and instructions"
msgstr "الالتزام بالانظمة والتعليمات"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__administrative_behavior__evalu_items_adm__adherence_to_the_company's_regulatory_documents
#, fuzzy
msgid "Adherence to the company's regulatory documents"
msgstr "الالتزام بالمستندات التنظيمية للشركة"

#. module: employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_administrative_behavior
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__administrative_behavior_ids
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#, fuzzy
msgid "Administrative Behavior"
msgstr "السلوك الإداري"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__presence__evalu_items__annual_leave_during_the_month
#, fuzzy
msgid "Annual leave during the month"
msgstr "الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__exceptional_performance__evalu_items_exc__assignments_for_exceptional_operations_during_the_month
#, fuzzy
msgid "Assignments for exceptional operations during the month"
msgstr "التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__exceptional_performance__evalu_items_exc__attention-grabbing_messages_during_the_month
#, fuzzy
msgid "Attention-grabbing messages during the month"
msgstr "رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__class_weight_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__class_weight_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__class_weight_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__class_weight
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__class_weight_tra
#, fuzzy
msgid "Class Weight"
msgstr "وزن التصنيف"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__class_weight_adm
msgid "Class Weight Adm"
msgstr "زن التصنيف"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__executive_performance__evalu_items_per__commitment_to_completing_tasks_on_time
#, fuzzy
msgid "Commitment to completing tasks on time"
msgstr "الالتزام بتنفيذ المهام في الوقت المحدد"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__executive_performance__evalu_items_per__commitment_to_the_reference
#, fuzzy
msgid "Commitment to the reference"
msgstr "الالتزام بمرجعية الهيكل التنظيمي للطلب والاستفهام"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__disciplined__evalu_items_dis__commitment_to_the_workplace
#, fuzzy
msgid "Commitment to the workplace"
msgstr "الالتزام بمقر العمل وحسن الهيئة والمظهر"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__disciplined__evalu_items_dis__preserving_the_companys_resources_and_making_good_use_of_them
#, fuzzy
msgid "Preserving the companys resources and making good use of them"
msgstr "الحفاظ على موارد الشركة وحسن استخدامها"

#. module: employee_report_kpi
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
msgid "Report Employee Xlsx"
msgstr "طباعة تقرير التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__create_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__create_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__create_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__create_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__create_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__create_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__create_uid
msgid "Created by"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__create_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__create_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__create_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__create_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__create_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__create_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__create_date
msgid "Created on"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__date
#, fuzzy
msgid "Date"
msgstr "التاريخ"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__direct_evalu_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__direct_evalu_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__direct_evalu_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__direct_evalu
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__direct_evalu_tra
#, fuzzy
msgid "Direct Evalu"
msgstr "تقييم المستوى المباشرم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__direct_evalu_adm
msgid "Direct Evalu Adm"
msgstr "تقييم المستوى المباشر"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__direct_side_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__direct_side_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__direct_side_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__direct_side
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__direct_side_tra
msgid "Direct Side"
msgstr "جهة التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__direct_side_adm
#, fuzzy
msgid "Direct Side Adm"
msgstr "جهة التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__exceptional_performance__evalu_items_exc__disciplinary_penalties_during_the_month
#, fuzzy
msgid "Disciplinary penalties during the month"
msgstr "العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)"

#. module: employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_disciplined
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__disciplined_ids
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#, fuzzy
msgid "Disciplined"
msgstr "الحرص والإنضباط"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__display_name
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__display_name
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__display_name
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__display_name
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__display_name
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__display_name
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__display_name
msgid "Display Name"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__presence__evalu_items__duration_of_delay_and_early_departure
#, fuzzy
msgid "Duration of delay and early departure"
msgstr "مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__presence__evalu_items__emergency_leaves_during_the_month
#, fuzzy
msgid "Emergency leaves during the month"
msgstr "الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)"

#. module: employee_report_kpi
#: model:ir.actions.act_window,name:employee_report_kpi.action_employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__employee_report_kpi_adm_id
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__employee_report_kpi_dis_id
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__employee_report_kpi_exc_id
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__employee_report_kpi_per_id
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__employee_report_kpi_id
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__employee_report_kpi_tra_id
#: model:ir.ui.menu,name:employee_report_kpi.menu_employee_record
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_tree
#, fuzzy
msgid "Employee Report KPI"
msgstr "تقرير تقييم الموظف"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__evalu_item_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__evalu_item_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__evalu_item_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__evalu_item
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__evalu_item_tra
#, fuzzy
msgid "Evalu Item"
msgstr "تقييم العنصر"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__evalu_item_adm
msgid "Evalu Item Adm"
msgstr "تقييم العنصر"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__evalu_items_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__evalu_items_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__evalu_items
#, fuzzy
msgid "Evalu Items"
msgstr "عناصر التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__evalu_items_adm
msgid "Evalu Items Adm"
msgstr "عناصر التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__evalu_items_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__evalu_items_tra
#, fuzzy
msgid "Evaluation Items"
msgstr "عناصر التقييم"

#. module: employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_exceptional_performance
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__exceptional_performance_ids
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#, fuzzy
msgid "Exceptional Performance"
msgstr "الآداء الإستثنائي"

#. module: employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_executive_performance
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__executive_performance_ids
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#, fuzzy
msgid "Executive Performance"
msgstr "الآداء التنفيذي"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__final_evalu_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__final_evalu_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__final_evalu_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__final_evalu
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__final_evalu_tra
#, fuzzy
msgid "Final Evalu"
msgstr "التقييم النهائي"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__final_evalu_adm
msgid "Final Evalu Adm"
msgstr "القييم النهائي"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__final_evalue
#, fuzzy
msgid "Final Evalue"
msgstr "القييم النهائي"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__higher_evalu_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__higher_evalu_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__higher_evalu_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__higher_evalu
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__higher_evalu_tra
#, fuzzy
msgid "Higher Evalu"
msgstr "تقييم المستوى الأعلى"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__higher_evalu_adm
msgid "Higher Evalu Adm"
msgstr "تقييم المستوى الأعلى"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__higher_side_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__higher_side_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__higher_side_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__higher_side
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__higher_side_tra
#, fuzzy
msgid "Higher Side"
msgstr "تقييم المستوى المباشر"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__higher_side_adm
msgid "Higher Side Adm"
msgstr "تقييم المستوى المباشر"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__id
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__id
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__id
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__id
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__id
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__id
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__id
msgid "ID"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__executive_performance__evalu_items_per__identify_obstacles_and_strive_to_overcome_them
#, fuzzy
msgid "Identify obstacles and strive to overcome them"
msgstr "تحديد العوائق والسعي للتغلب عليها"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__job_number
#, fuzzy
msgid "Job Number"
msgstr "الرقم الوظيفي"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior____last_update
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined____last_update
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi____last_update
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance____last_update
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance____last_update
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence____last_update
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education____last_update
msgid "Last Modified on"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__write_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__write_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__write_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__write_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__write_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__write_uid
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__write_uid
msgid "Last Updated by"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__write_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__write_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__write_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__write_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__write_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__write_date
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__write_date
msgid "Last Updated on"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__administration
#, fuzzy
msgid "Location/Management"
msgstr "القسم"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__disciplined__evalu_items_dis__maintain_the_privacy_of_business_data
#, fuzzy
msgid "Maintain the privacy of business data"
msgstr "الحفاظ على خصوصية بيانات العمل"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__exceptional_performance__evalu_items_exc__messages_of_thanks_and_appreciation_during_the_month
#, fuzzy
msgid "Messages of thanks and appreciation during the month"
msgstr "رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__administrative_behavior__evalu_items_adm__organizing_the_work_and_informing
#, fuzzy
msgid "Organizing the work and informing"
msgstr "تنظيم العمل واطلاع المخولين عليه"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__administrative_behavior__evalu_items_adm__participation_and_working_within_a_team
#, fuzzy
msgid "Participation and working within a team"
msgstr "المشاركة والعمل ضمن فريق"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__training_education__evalu_items_tra__passing_electronic_software_learning_courses
#, fuzzy
msgid "Passing electronic software learning courses"
msgstr "اجتياز دورات تعلم برامج الكترونية"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__training_education__evalu_items_tra__passing_foreign_language_courses
#, fuzzy
msgid "Passing foreign language courses"
msgstr "اجتياز دورات تعلم لغة أجنبية"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__training_education__evalu_items_tra__passing_training_and_development_courses_in_a_supporting_specialty
#, fuzzy
msgid "Passing training and development courses in a supporting specialty"
msgstr "اجتياز دورات تدريب وتطوير في اختصاص مساند"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__training_education__evalu_items_tra__passing_training_and_development_courses_in_the_field_of_specialization
#, fuzzy
msgid "Passing training and development courses in the field of specialization"
msgstr "اجتياز دورات تدريب وتطوير في مجال الاختصاص"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__executive_performance__evalu_items_per__performing_tasks_under_pressure
#, fuzzy
msgid "Performing tasks under pressure"
msgstr "تنفيذ المهام تحت الضغط"

#. module: employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_presence
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__presence_ids
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#, fuzzy
msgid "Presence"
msgstr "التواجد"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__disciplined__evalu_items_dis__preserving_the_company's_resources_and_making_good_use_of_them
msgid "Preserving the company's resources and making good use of them"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__executive_performance__evalu_items_per__quality_of_completion_of_assigned_tasks
#, fuzzy
msgid "Quality of completion of assigned tasks"
msgstr "جودة انجاز المهام المكلف بها"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__administrative_behavior__evalu_items_adm__relationship_with_co-workers
#, fuzzy
msgid "Relationship with co-workers"
msgstr "العلاقة مع زملاء العمل"

#. module: employee_report_kpi
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
msgid "Report Employee Xlsx"
msgstr ""

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__presence__evalu_items__sick_leave_during_the_month
#, fuzzy
msgid "Sick leave during the month"
msgstr "الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__executive_performance__evalu_items_per__striving_for_continuous_development
#, fuzzy
msgid "Striving for continuous development"
msgstr "السعي للتطوير المستمر"

#. module: employee_report_kpi
#: model:ir.model,name:employee_report_kpi.model_training_education
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__training_education_ids
#: model_terms:ir.ui.view,arch_db:employee_report_kpi.view_employee_report_kpi_form
#, fuzzy
msgid "Training and Education"
msgstr "التدريب والتعليم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__unit_measur_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__unit_measur_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__unit_measur_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__unit_measur
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__unit_measur_tra
#, fuzzy
msgid "Unit Measur"
msgstr "وحدة قياس التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__unit_measur_adm
msgid "Unit Measur Adm"
msgstr "حدة قياس التقييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_disciplined__weight_item_dis
#: model:ir.model.fields,field_description:employee_report_kpi.field_exceptional_performance__weight_item_exc
#: model:ir.model.fields,field_description:employee_report_kpi.field_executive_performance__weight_item_per
#: model:ir.model.fields,field_description:employee_report_kpi.field_presence__weight_item
#: model:ir.model.fields,field_description:employee_report_kpi.field_training_education__weight_item_tra
#, fuzzy
msgid "Weight Item"
msgstr "وزن عنصر التفييم"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_administrative_behavior__weight_item_adm
msgid "Weight Item Adm"
msgstr "وزن عنصر التفييم"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__administrative_behavior__evalu_items_adm__written_communication_skills_with_relevant_parties
#, fuzzy
msgid "Written communication skills with relevant parties"
msgstr "مهارات التواصل الكتابية مع الأطراف ذات العلاقة"

#. module: employee_report_kpi
#: model:ir.model.fields.selection,name:employee_report_kpi.selection__training_education__unit_measur_tra__yes(1)/no(0)
#, fuzzy
msgid "Yes(1)/No(0)"
msgstr "نعم (1) / لا (0)"

#. module: employee_report_kpi
#: model:ir.model.fields,field_description:employee_report_kpi.field_employee_report_kpi__employee_id
#, fuzzy
msgid "name"
msgstr "الاسم"
