
import json
from odoo.tools import date_utils
from odoo import models, fields, api
import io
import xlsxwriter

class EmployeeReportKPI(models.Model):
    _name = 'employee.report.kpi'
    _description = 'Employee Report KPI'

    employee_id = fields.Many2one('hr.employee', string="name", required=True)
    date = fields.Date(string="Date", required=True)
    administration = fields.Char(string="Location/Management", required=True)
    job_number = fields.Char(string="Job Number", required=True)

    presence_ids = fields.One2many('presence', 'employee_report_kpi_id', string="Presence")
    executive_performance_ids = fields.One2many('executive.performance', 'employee_report_kpi_per_id', string="Executive Performance")
    administrative_behavior_ids = fields.One2many('administrative.behavior', 'employee_report_kpi_adm_id', string="Administrative Behavior")
    disciplined_ids = fields.One2many('disciplined', 'employee_report_kpi_dis_id', string="Disciplined")
    exceptional_performance_ids = fields.One2many('exceptional.performance', 'employee_report_kpi_exc_id', string='Exceptional Performance')
    training_education_ids = fields.One2many(
        'training.education',
        'employee_report_kpi_tra_id',
        string='Training and Education'
    )
    final_evalue = fields.Float(
        string="Final Evalue",
        compute='_compute_final_evalue',
        store=True
    )

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        if self.employee_id:
            self.administration = self.employee_id.department_id.name or ''
            self.job_number = self.employee_id.registration_number or ''

    @api.depends(
        'presence_ids.evalu_item',
        'executive_performance_ids.evalu_item_per',
        'administrative_behavior_ids.evalu_item_adm',
        'disciplined_ids.evalu_item_dis',
        'exceptional_performance_ids.evalu_item_exc',
        'training_education_ids.evalu_item_tra'
    )
    def _compute_final_evalue(self):
        for record in self:
            total_presence = sum(record.presence_ids.mapped('evalu_item'))
            total_executive = sum(record.executive_performance_ids.mapped('evalu_item_per'))
            total_administrative = sum(record.administrative_behavior_ids.mapped('evalu_item_adm'))
            total_disciplined = sum(record.disciplined_ids.mapped('evalu_item_dis'))
            total_exceptional = sum(record.exceptional_performance_ids.mapped('evalu_item_exc'))
            total_training = sum(record.training_education_ids.mapped('evalu_item_tra'))

            record.final_evalue = (
                total_presence +
                total_executive +
                total_administrative +
                total_disciplined +
                total_exceptional +
                total_training
            )



    @api.onchange('date')
    def _onchange_date(self):
        if self.date:
            # تعيين اليوم إلى اليوم الأول من الشهر
            date_obj = fields.Date.from_string(self.date)
            self.date = date_obj.replace(day=1).strftime('%Y-%m-%d')

    PRESENCE_EVALU_ITEMS = [
        ('الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)', 'الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)'),
        ('مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)',
         'مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)'),
        ('الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)', 'الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)'),
        ('الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)', 'الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)'),
        ('الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)', 'الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)'),
    ]
    EVALU_ITEMS_PER_SELECTION = [
        ('جودة انجاز المهام المكلف بها', 'جودة انجاز المهام المكلف بها'),
        ('الالتزام بتنفيذ المهام في الوقت المحدد', 'الالتزام بتنفيذ المهام في الوقت المحدد'),
        ('الالتزام بمرجعية الهيكل التنظيمي للطلب والاستفهام', 'الالتزام بمرجعية الهيكل التنظيمي للطلب والاستفهام'),
        ('تحديد العوائق والسعي للتغلب عليها', 'تحديد العوائق والسعي للتغلب عليها'),
        ('تنفيذ المهام تحت الضغط', 'تنفيذ المهام تحت الضغط'),
        ('السعي للتطوير المستمر', 'السعي للتطوير المستمر'),
    ]
    EVALU_ITEMS_ADM_SELECTION = [
        ('العلاقة مع زملاء العمل', 'العلاقة مع زملاء العمل'),
        ('المشاركة والعمل ضمن فريق', 'المشاركة والعمل ضمن فريق'),
        ('تنظيم العمل واطلاع المخولين عليه', 'تنظيم العمل واطلاع المخولين عليه'),
        ('مهارات التواصل الكتابية مع الأطراف ذات العلاقة', 'مهارات التواصل الكتابية مع الأطراف ذات العلاقة'),
        ('الالتزام بالمستندات التنظيمية للشركة', 'الالتزام بالمستندات التنظيمية للشركة')
    ]
    EVALU_ITEMS_SELECTION = [
        ('الالتزام بالانظمة والتعليمات', 'الالتزام بالانظمة والتعليمات'),
        ('الالتزام بقواعد السلامة المهنية', 'الالتزام بقواعد السلامة المهنية'),
        ('الالتزام بمقر العمل وحسن الهيئة والمظهر', 'الالتزام بمقر العمل وحسن الهيئة والمظهر'),
        ('الحفاظ على موارد الشركة وحسن استخدامها', 'الحفاظ على موارد الشركة وحسن استخدامها'),
        ('الحفاظ على خصوصية بيانات العمل', 'الحفاظ على خصوصية بيانات العمل'),
    ]
    EVALU_ITEMS_SELECTION_EXC = [
        ('رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)', 'رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)'),
        ('العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)', 'العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)'),
        ('رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)', 'رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)'),
        ('التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)',
         'التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)'),
    ]
    EVALU_ITEMS_TRA_SELECTION = [
        ('اجتياز دورات تدريب وتطوير في مجال الاختصاص', 'اجتياز دورات تدريب وتطوير في مجال الاختصاص'),
        ('اجتياز دورات تدريب وتطوير في اختصاص مساند', 'اجتياز دورات تدريب وتطوير في اختصاص مساند'),
        ('اجتياز دورات تعلم لغة أجنبية', 'اجتياز دورات تعلم لغة أجنبية'),
        ('اجتياز دورات تعلم برامج الكترونية', 'اجتياز دورات تعلم برامج الكترونية'),
    ]

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if 'date' in vals and vals['date']:
                date_obj = fields.Date.from_string(vals['date'])
                vals['date'] = date_obj.replace(day=1).strftime('%Y-%m-%d')
        res = super().create(vals_list)
        for kpi in res:
            for key, value in self.PRESENCE_EVALU_ITEMS:
                kpi.presence_ids = [(0, 0, {'evalu_items': key})]
        for kpi in res:
            for key, value in self.EVALU_ITEMS_PER_SELECTION:
                kpi.executive_performance_ids = [(0, 0, {'evalu_items_per': key})]
        for kpi in res:
            for key, value in self.EVALU_ITEMS_ADM_SELECTION:
                kpi.administrative_behavior_ids = [(0, 0, {'evalu_items_adm': key})]
        for kpi in res:
            for key, value in self.EVALU_ITEMS_SELECTION:
                kpi.disciplined_ids = [(0, 0, {'evalu_items_dis': key})]
        for kpi in res:
            for key, value in self.EVALU_ITEMS_SELECTION_EXC:
                kpi.exceptional_performance_ids = [(0, 0, {'evalu_items_exc': key})]
        for kpi in res:
            for key, value in self.EVALU_ITEMS_TRA_SELECTION:
                kpi.training_education_ids = [(0, 0, {'evalu_items_tra': key})]

        return res

    def write(self, vals):
        if 'date' in vals and vals['date']:
            date_obj = fields.Date.from_string(vals['date'])
            vals['date'] = date_obj.replace(day=1).strftime('%Y-%m-%d')
        return super(EmployeeReportKPI, self).write(vals)

    def report_employee_Xlsx(self):
        data = {
            'employee_id': self.employee_id.name,
            'date': self.date,
            'administration': self.administration,
            'job_number': self.job_number,
            'final_evalue': self.final_evalue,
            'presence': [{
                'evalu_items': presence.evalu_items,
                'unit_measur': presence.unit_measur,
                'higher_evalu': presence.higher_evalu,
                'higher_side': presence.higher_side,
                'direct_evalu': presence.direct_evalu,
                'direct_side': presence.direct_side,
                'final_evalu': presence.final_evalu,
                'class_weight': presence.class_weight,
                'weight_item': presence.weight_item,
                'evalu_item': presence.evalu_item,
                # أضف الحقول الأخرى هنا
            } for presence in self.presence_ids],
            'executive_performance': [{
                'evalu_items_per': executive.evalu_items_per,
                'unit_measur_per': executive.unit_measur_per,
                'higher_evalu_per': executive.higher_evalu_per,
                'higher_side_per': executive.higher_side_per,
                'direct_evalu_per': executive.direct_evalu_per,
                'direct_side_per': executive.direct_side_per,
                'final_evalu_per': executive.final_evalu_per,
                'class_weight_per': executive.class_weight_per,
                'weight_item_per': executive.weight_item_per,
                'evalu_item_per': executive.evalu_item_per,
                # أضف الحقول الأخرى هنا
            } for executive in self.executive_performance_ids],
            'administrative_behavior': [{
                'evalu_items_adm': behavior.evalu_items_adm,
                'unit_measur_adm': behavior.unit_measur_adm,
                'higher_evalu_adm': behavior.higher_evalu_adm,
                'higher_side_adm': behavior.higher_side_adm,
                'direct_evalu_adm': behavior.direct_evalu_adm,
                'direct_side_adm': behavior.direct_side_adm,
                'final_evalu_adm': behavior.final_evalu_adm,
                'class_weight_adm': behavior.class_weight_adm,
                'weight_item_adm': behavior.weight_item_adm,
                'evalu_item_adm': behavior.evalu_item_adm,
                # أضف الحقول الأخرى هنا
            } for behavior in self.administrative_behavior_ids],
            'disciplined': [{
                'evalu_items_dis': disciplined.evalu_items_dis,
                'unit_measur_dis': disciplined.unit_measur_dis,
                'higher_evalu_dis': disciplined.higher_evalu_dis,
                'higher_side_dis': disciplined.higher_side_dis,
                'direct_evalu_dis': disciplined.direct_evalu_dis,
                'direct_side_dis': disciplined.direct_side_dis,
                'final_evalu_dis': disciplined.final_evalu_dis,
                'class_weight_dis': disciplined.class_weight_dis,
                'weight_item_dis': disciplined.weight_item_dis,
                'evalu_item_dis': disciplined.evalu_item_dis,
                # أضف الحقول الأخرى هنا
            } for disciplined in self.disciplined_ids],
            'exceptional_performance': [{
                'evalu_items_exc': performance.evalu_items_exc,
                'unit_measur_exc': performance.unit_measur_exc,
                'higher_evalu_exc': performance.higher_evalu_exc,
                'higher_side_exc': performance.higher_side_exc,
                'direct_evalu_exc': performance.direct_evalu_exc,
                'direct_side_exc': performance.direct_side_exc,
                'final_evalu_exc': performance.final_evalu_exc,
                'class_weight_exc': performance.class_weight_exc,
                'weight_item_exc': performance.weight_item_exc,
                'evalu_item_exc': performance.evalu_item_exc,
                # أضف الحقول الأخرى هنا
            } for performance in self.exceptional_performance_ids],
            'training_education': [{
                'evalu_items_tra': training.evalu_items_tra,
                'unit_measur_tra': training.unit_measur_tra,
                'higher_evalu_tra': training.higher_evalu_tra,
                'higher_side_tra': training.higher_side_tra,
                'direct_evalu_tra': training.direct_evalu_tra,
                'direct_side_tra': training.direct_side_tra,
                'final_evalu_tra': training.final_evalu_tra,
                'class_weight_tra': training.class_weight_tra,
                'weight_item_tra': training.weight_item_tra,
                'evalu_item_tra': training.evalu_item_tra,
                # أضف الحقول الأخرى هنا
            } for training in self.training_education_ids],
        }

        return {
            'type': 'ir.actions.report',
            'data': {
                'model': 'employee.report.kpi',
                'options': json.dumps(data, default=date_utils.json_default),
                'output_format': 'xlsx',
                'report_name': 'تقرير تقييم الموظف'
            },
            'report_type': 'xlsx',
        }


    def get_xlsx_report(self, data, response):

        # Create an in-memory output stream
        output = io.BytesIO()

        # Create a workbook and add a worksheet
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet()
        # تعيين الورقة للعمل من اليمين إلى اليسار
        worksheet.right_to_left()

        # Define formats
        header_format = workbook.add_format({'font_size': 12, 'align': 'center', 'bold': True})
        cell_format = workbook.add_format({'font_size': 10, 'align': 'right'})
        table_header_format = workbook.add_format({'font_size': 10, 'align': 'center', 'bold': True, 'border': 1})
        table_cell_format = workbook.add_format({'font_size': 8, 'align': 'right', 'border': 1,'bold': True})
        # Define a format for table cells with centered text (for columns C to K)
        centered_table_cell_format = workbook.add_format({
            'border': 1,
            'align': 'center',  # Center the text horizontally
            'valign': 'vcenter'  # Center the text vertically
        })
        # Write employee details
        worksheet.write('A1', '/ الاسم', header_format)
        worksheet.write('B1', data.get('employee_id', ''), cell_format)
        worksheet.write('A2', '/ الرقم الوظيفي', header_format)
        worksheet.write('B2', data.get('job_number', ''), cell_format)
        worksheet.write('C1', '/ تاريخ', header_format)
        worksheet.write('D1', data.get('date', ''), cell_format)
        worksheet.write('C2', ' / الادارة ', header_format)
        worksheet.write('D2', data.get('administration', ''), cell_format)

        # Add a blank line between the header and the first table
        start_row = 4  # Start the first table after leaving a blank row

        # Set column widths for better readability in the table
        worksheet.set_column('A:A', 15)  # Rating classification
        worksheet.set_column('B:B', 30)  # Evaluation items
        worksheet.set_column('C:C', 20)  # Evaluation measurement unit
        worksheet.set_column('D:E', 40)  # Higher level evaluation (merged)
        worksheet.set_column('F:G', 40)  # Direct level assessment (merged)
        worksheet.set_column('H:H', 20)  # Final evaluation
        worksheet.set_column('I:I', 20)  # Classification weight
        worksheet.set_column('J:J', 25)  # Weight of the evaluation item
        worksheet.set_column('K:K', 25)  # Evaluate the item

        # Write the merged headers for "Higher level evaluation" and "Direct level assessment"
        worksheet.merge_range(start_row, 3, start_row, 4, 'تقييم المستوى الأعلى', table_header_format)
        worksheet.merge_range(start_row, 5, start_row, 6, 'تقييم المستوى المباشر', table_header_format)

        # Write the sub-headers for the merged columns
        worksheet.write(start_row + 1, 3, 'جهة التقييم', table_header_format)
        worksheet.write(start_row + 1, 4, 'درجة التقييم', table_header_format)
        worksheet.write(start_row + 1, 5, 'جهة التقييم', table_header_format)
        worksheet.write(start_row + 1, 6, 'درجة التقييم', table_header_format)

        # Write the rest of the headers
        worksheet.write(start_row, 0, 'تصنيف التقييم', table_header_format)
        worksheet.write(start_row, 1, 'عناصر التقييم', table_header_format)
        worksheet.write(start_row, 2, 'وحدة قياس '
                                      'التقييم', table_header_format)
        worksheet.write(start_row, 7, 'التقييم النهائي', table_header_format)
        worksheet.write(start_row, 8, 'وزن التصنيف', table_header_format)
        worksheet.write(start_row, 9, 'وزن عنصر'
                                      ' التفييم', table_header_format)
        worksheet.write(start_row, 10, 'تقييم العنصر', table_header_format)


        # Write the data rows for the first table
        row = start_row + 2  # Start writing data after the headers
        for record in data.get('presence', []):
            if isinstance(record, dict):  # Ensure that record is a dictionary
                worksheet.write(row, 0, 'التواجد',
                                table_cell_format)  # Write 'Presence' in the "Rating classification" column
                worksheet.write(row, 1, str(record.get('evalu_items', '')), table_cell_format)
                worksheet.write(row, 2, str(record.get('unit_measur', '')), centered_table_cell_format)
                worksheet.write(row, 3, str(record.get('higher_evalu', '')), centered_table_cell_format)
                worksheet.write(row, 4, 'الموارد البشرية', centered_table_cell_format)
                worksheet.write(row, 5, str(record.get('direct_evalu', '')), centered_table_cell_format)
                worksheet.write(row, 6, 'الشؤون الإدارية', centered_table_cell_format)
                worksheet.write(row, 7, str(record.get('final_evalu', '')), centered_table_cell_format)
                worksheet.write(row, 8, str(record.get('class_weight', '')), centered_table_cell_format)
                worksheet.write(row, 9, str(record.get('weight_item', '')), centered_table_cell_format)
                worksheet.write(row, 10, str(record.get('evalu_item', '')), centered_table_cell_format)
                row += 1

            # Add a blank row between the first table and the second table
        row += 1  # Leave a blank row

        # Write the data rows for the second table
        for record in data.get('executive_performance', []):
            if isinstance(record, dict):  # Ensure that record is a dictionary
                worksheet.write(row, 0, 'الآداء التنفيذي',
                                table_cell_format)  # Write 'Executive Performance' in the "Rating classification" column
                worksheet.write(row, 1, str(record.get('evalu_items_per', '')), table_cell_format)
                worksheet.write(row, 2, 'نسبة مئوية', centered_table_cell_format)
                worksheet.write(row, 3, str(record.get('higher_evalu_per', '')), centered_table_cell_format)
                worksheet.write(row, 4, 'التدقيق '
                                        '/ المراجع الداخلي', centered_table_cell_format)
                worksheet.write(row, 5, str(record.get('direct_evalu_per', '')), centered_table_cell_format)
                worksheet.write(row, 6, 'مدير الإدارة'
                                        ' / ما يليه', centered_table_cell_format)
                worksheet.write(row, 7, str(record.get('final_evalu_per', '')), centered_table_cell_format)
                worksheet.write(row, 8, str(record.get('class_weight_per', '')), centered_table_cell_format)
                worksheet.write(row, 9, str(record.get('weight_item_per', '')), centered_table_cell_format)
                worksheet.write(row, 10, str(record.get('evalu_item_per', '')), centered_table_cell_format)
                row += 1
        row += 1
        for record in data.get('administrative_behavior', []):
            if isinstance(record, dict):  # Ensure that record is a dictionary
                worksheet.write(row, 0, 'السلوك الإداري',
                                table_cell_format)  # Write 'Executive Performance' in the "Rating classification" column
                worksheet.write(row, 1, str(record.get('evalu_items_adm', '')), table_cell_format)
                worksheet.write(row, 2, 'نسبة مئوية', centered_table_cell_format)
                worksheet.write(row, 3, str(record.get('higher_evalu_adm', '')), centered_table_cell_format)
                worksheet.write(row, 4, 'الرئيس المباشر', centered_table_cell_format)
                worksheet.write(row, 5, str(record.get('direct_evalu_adm', '')), centered_table_cell_format)
                worksheet.write(row, 6, 'مدير الإدارة'
                                        ' / ما يليه', centered_table_cell_format)
                worksheet.write(row, 7, str(record.get('final_evalu_adm', '')), centered_table_cell_format)
                worksheet.write(row, 8, str(record.get('class_weight_adm', '')), centered_table_cell_format)
                worksheet.write(row, 9, str(record.get('weight_item_adm', '')), centered_table_cell_format)
                worksheet.write(row, 10, str(record.get('evalu_item_adm', '')), centered_table_cell_format)
                row += 1
        row += 1


        for record in data.get('disciplined', []):
            if isinstance(record, dict):  # Ensure that record is a dictionary
                worksheet.write(row, 0, 'الحرص والإنضباط',
                                table_cell_format)  # Write 'Executive Performance' in the "Rating classification" column
                worksheet.write(row, 1, str(record.get('evalu_items_dis', '')), table_cell_format)
                worksheet.write(row, 2, 'نسبة مئوية', centered_table_cell_format)
                worksheet.write(row, 3, str(record.get('higher_evalu_dis', '')), centered_table_cell_format)
                worksheet.write(row, 4, 'الرئيس المباشر', centered_table_cell_format)
                worksheet.write(row, 5, str(record.get('direct_evalu_dis', '')), centered_table_cell_format)
                worksheet.write(row, 6, 'مدير الإدارة '
                                        '/ ما يليه', centered_table_cell_format)
                worksheet.write(row, 7, str(record.get('final_evalu_dis', '')), centered_table_cell_format)
                worksheet.write(row, 8, str(record.get('class_weight_dis', '')), centered_table_cell_format)
                worksheet.write(row, 9, str(record.get('weight_item_dis', '')), centered_table_cell_format)
                worksheet.write(row, 10, str(record.get('evalu_item_dis', '')), centered_table_cell_format)
                row += 1
        row += 1

        for record in data.get('exceptional_performance', []):
            if isinstance(record, dict):  # Ensure that record is a dictionary
                worksheet.write(row, 0, 'الآداء الإستثنائي',
                                table_cell_format)  # Write 'Executive Performance' in the "Rating classification" column
                worksheet.write(row, 1, str(record.get('evalu_items_exc', '')), table_cell_format)
                worksheet.write(row, 2, str(record.get('unit_measur_exc', '')), centered_table_cell_format)
                worksheet.write(row, 3, str(record.get('higher_evalu_exc', '')), centered_table_cell_format)
                worksheet.write(row, 4, 'شؤون الموظفين', centered_table_cell_format)
                worksheet.write(row, 5, str(record.get('direct_evalu_exc', '')), centered_table_cell_format)
                worksheet.write(row, 6, 'الشؤون الإدارية', centered_table_cell_format)
                worksheet.write(row, 7, str(record.get('final_evalu_exc', '')), centered_table_cell_format)
                worksheet.write(row, 8, str(record.get('class_weight_exc', '')), centered_table_cell_format)
                worksheet.write(row, 9, str(record.get('weight_item_exc', '')), centered_table_cell_format)
                worksheet.write(row, 10, str(record.get('evalu_item_exc', '')), centered_table_cell_format)
                row += 1
        row += 1

        for record in data.get('training_education', []):
            if isinstance(record, dict):  # Ensure that record is a dictionary
                worksheet.write(row, 0, 'التدريب والتعليم',
                                    table_cell_format)  # Write 'Executive Performance' in the "Rating classification" column
                worksheet.write(row, 1, str(record.get('evalu_items_tra', '')), table_cell_format)
                worksheet.write(row, 2, 'نعم(1)/لا(0)', centered_table_cell_format)
                worksheet.write(row, 3, str(record.get('higher_evalu_tra', '')), centered_table_cell_format)
                worksheet.write(row, 4, 'الشؤون الإدارية', centered_table_cell_format)
                worksheet.write(row, 5, str(record.get('direct_evalu_tra', '')), centered_table_cell_format)
                worksheet.write(row, 6,'الشؤون الإدارية', centered_table_cell_format)
                worksheet.write(row, 7, str(record.get('final_evalu_tra', '')), centered_table_cell_format)
                worksheet.write(row, 8, str(record.get('class_weight_tra', '')), centered_table_cell_format)
                worksheet.write(row, 9, str(record.get('weight_item_tra', '')), centered_table_cell_format)
                worksheet.write(row, 10, str(record.get('evalu_item_tra', '')), centered_table_cell_format)
                row += 1
        row += 1

        # Add the fixed row with specific values
        worksheet.write(row, 0, '', table_cell_format)  # Example text
        worksheet.write(row, 1, '', table_cell_format)  # Leave empty
        worksheet.write(row, 2, '', table_cell_format)  # Leave empty
        worksheet.write(row, 3, '', table_cell_format)  # Leave empty
        worksheet.write(row, 4, '', table_cell_format)  # Leave empty
        worksheet.write(row, 5, '', table_cell_format)  # Leave empty
        worksheet.write(row, 6, '', table_cell_format)  # Leave empty
        worksheet.write(row, 7, '', table_cell_format)
        worksheet.write(row, 8, '130%', centered_table_cell_format)  # Classification weight
        worksheet.write(row, 9, '130%', centered_table_cell_format)  # Weight of the evaluation item
        worksheet.write(row, 10, str(data.get('final_evalue', '')),
                            centered_table_cell_format)
            # Leave empty

        worksheet.set_column('C:K', 8)  # Width of 10 for columns C to K
            # Close the workbook
        workbook.close()

        # Set the response headers and send the response
        output.seek(0)
        response.stream.write(output.read())
        output.close()



class Presence(models.Model):
    _name = 'presence'
    _description = 'Presence'

    PRESENCE_EVALU_ITEMS = [
        ('الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)', 'الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)'),
        ('مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)', 'مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)'),
        ('الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)', 'الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)'),
        ('الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)', 'الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)'),
        ('الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)', 'الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)'),
    ]
    evalu_items = fields.Selection(
        PRESENCE_EVALU_ITEMS,
        string="Evalu Items",
        required=True
    )
    unit_measur = fields.Char(
        string="Unit Measur",
        compute='_compute_unit_measur',
        store=True
    )
    higher_evalu = fields.Float(string="Higher Evalu", default=0.0)
    higher_side = fields.Char(string="Higher Side", default="Administrative affairs", required=True)
    direct_evalu = fields.Float(string="Direct Evalu", default=0.0)
    direct_side = fields.Char(string="Direct Side", default="Human resources", required=True)
    final_evalu = fields.Float(
        string="Final Evalu",
        compute='_compute_final_evalu',
        store=True
    )
    class_weight = fields.Char(
        string="Class Weight",
        default='20%',
        readonly=True
    )
    weight_item = fields.Float(
        string="Weight Item",
        compute='_compute_weight_item',
        store=True
    )
    evalu_item = fields.Float(string="Evalu Item", compute='_compute_evalu_item', store=True)
    employee_report_kpi_id = fields.Many2one('employee.report.kpi', string="Employee Report KPI")

    @api.depends('higher_evalu', 'direct_evalu')
    def _compute_final_evalu(self):
        for record in self:
            if record.higher_evalu is not None and record.direct_evalu is not None:
                record.final_evalu = (record.higher_evalu + record.direct_evalu) / 2
            else:
                record.final_evalu = 0.0

    @api.depends('evalu_items', 'weight_item', 'final_evalu')
    def _compute_evalu_item(self):
        for record in self:
            if record.evalu_items == 'الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)':
                record.evalu_item = round((((7 - record.final_evalu) / 7) * record.weight_item),2)
            elif record.evalu_items == 'مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)':
                record.evalu_item = round((((180 - record.final_evalu) / 180) * record.weight_item),2)
            elif record.evalu_items in ['الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)', 'الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)', 'الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)']:
                record.evalu_item = round((((3 - record.final_evalu) / 3) * record.weight_item),2)
            else:
                record.evalu_item = 0.0

    @api.depends('evalu_items')
    def _compute_unit_measur(self):
        for record in self:
            if record.evalu_items in ['الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)', 'الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)', 'الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)', 'الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)']:
                record.unit_measur = 'ايام'
            elif record.evalu_items == 'مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)':
                record.unit_measur = 'دقائق'
            else:
                record.unit_measur = ''

    @api.depends('evalu_items')
    def _compute_weight_item(self):
        for record in self:
            if record.evalu_items in ['الغياب بدون إذن خلال الشهر (حد أقصي7 أيام)', 'مدة التأخير والانصراف المبكر بدون إذن خلال الشهر (حد أقصى 180 دقيقة)']:
                record.weight_item = 7.00
            elif record.evalu_items in ['الاجازات السنوية خلال الشهر (حد أقصى 3 أيام)', 'الاجازات المرضية خلال الشهر  (حد أقصى 3 أيام)', 'الاجازات الطارئة خلال الشهر (حد أقصى 3 أيام)']:
                record.weight_item = 2.00
            else:
                record.weight_item = 0.0

    @api.onchange('evalu_items')
    def _onchange_evalu_items(self):
        self._compute_unit_measur()
        self._compute_weight_item()


class ExecutivePerformance(models.Model):
    _name = 'executive.performance'
    _description = 'Executive Performance'

    EVALU_ITEMS_PER_SELECTION = [
        ('جودة انجاز المهام المكلف بها', 'جودة انجاز المهام المكلف بها'),
        ('الالتزام بتنفيذ المهام في الوقت المحدد', 'الالتزام بتنفيذ المهام في الوقت المحدد'),
        ('الالتزام بمرجعية الهيكل التنظيمي للطلب والاستفهام', 'الالتزام بمرجعية الهيكل التنظيمي للطلب والاستفهام'),
        ('تحديد العوائق والسعي للتغلب عليها', 'تحديد العوائق والسعي للتغلب عليها'),
        ('تنفيذ المهام تحت الضغط', 'تنفيذ المهام تحت الضغط'),
        ('السعي للتطوير المستمر', 'السعي للتطوير المستمر'),
    ]

    employee_report_kpi_per_id = fields.Many2one('employee.report.kpi', string="Employee Report KPI")

    evalu_items_per = fields.Selection(
        EVALU_ITEMS_PER_SELECTION,
        string="Evalu Items",
        required=True
    )
    unit_measur_per = fields.Char(
        string="Unit Measur",
        default="Percentage",
        readonly=True
    )
    higher_evalu_per = fields.Float(string="Higher Evalu", default=0.0)
    higher_side_per = fields.Char(string="Higher Side", default="Auditing", required=True)
    direct_evalu_per = fields.Float(string="Direct Evalu", default=0.0)
    direct_side_per = fields.Char(string="Direct Side", default="Administration Manager", required=True)
    final_evalu_per = fields.Float(
        string="Final Evalu",
        compute='_compute_final_evalu_per',
        store=True
    )
    class_weight_per = fields.Char(
        string="Class Weight",
        default='40%',
        readonly=True
    )
    weight_item_per = fields.Float(
        string="Weight Item",
        compute='_compute_weight_item_per',
        store=True
    )
    evalu_item_per = fields.Float(string="Evalu Item", compute='_compute_evalu_item_per', store=True)

    @api.depends('higher_evalu_per', 'direct_evalu_per')
    def _compute_final_evalu_per(self):
        for record in self:
            if record.higher_evalu_per is not None and record.direct_evalu_per is not None:
                record.final_evalu_per = (record.higher_evalu_per + record.direct_evalu_per) / 2
            else:
                record.final_evalu_per = 0.0

    @api.depends('final_evalu_per', 'weight_item_per')
    def _compute_evalu_item_per(self):
        for record in self:
            record.evalu_item_per = round((record.final_evalu_per * record.weight_item_per ) / 100,2)

    @api.depends('evalu_items_per')
    def _compute_weight_item_per(self):
        for record in self:
            if record.evalu_items_per in ['تنفيذ المهام تحت الضغط', 'الالتزام بتنفيذ المهام في الوقت المحدد', 'الالتزام بمرجعية الهيكل التنظيمي للطلب والاستفهام', 'جودة انجاز المهام المكلف بها']:
                record.weight_item_per = 7.00
            elif record.evalu_items_per in ['السعي للتطوير المستمر', 'تحديد العوائق والسعي للتغلب عليها']:
                record.weight_item_per = 6.00
            else:
                record.weight_item_per = 0.0

    @api.onchange('evalu_items_per')
    def _onchange_evalu_items_per(self):
        self._compute_weight_item_per()


class AdministrativeBehavior(models.Model):
    _name = 'administrative.behavior'
    _description = 'Administrative Behavior'

    EVALU_ITEMS_ADM_SELECTION = [
        ('العلاقة مع زملاء العمل', 'العلاقة مع زملاء العمل'),
        ('المشاركة والعمل ضمن فريق', 'المشاركة والعمل ضمن فريق'),
        ('تنظيم العمل واطلاع المخولين عليه', 'تنظيم العمل واطلاع المخولين عليه'),
        ('مهارات التواصل الكتابية مع الأطراف ذات العلاقة', 'مهارات التواصل الكتابية مع الأطراف ذات العلاقة'),
        ('الالتزام بالمستندات التنظيمية للشركة', 'الالتزام بالمستندات التنظيمية للشركة')
    ]

    WEIGHT_ITEM_SELECTION = [
        ('0.05', '5%'),
    ]

    evalu_items_adm = fields.Selection(
        EVALU_ITEMS_ADM_SELECTION,
        string="Evalu Items Adm",
        required=True
    )
    unit_measur_adm = fields.Char(
        string="Unit Measur Adm",
        default='Percentage',
        readonly=True
    )
    higher_evalu_adm = fields.Float(string="Higher Evalu Adm", default=0.0)
    higher_side_adm = fields.Char(string="Higher Side Adm", default="Direct superior", required=True)
    direct_evalu_adm = fields.Float(string="Direct Evalu Adm", default=0.0)
    direct_side_adm = fields.Char(string="Direct Side Adm", default="Administration Manager", required=True)
    final_evalu_adm = fields.Float(
        string="Final Evalu Adm",
        compute='_compute_final_evalu_adm',
        store=True
    )
    class_weight_adm = fields.Char(
        string="Class Weight Adm",
        default='25%',
        readonly=True
    )
    weight_item_adm = fields.Selection(
        WEIGHT_ITEM_SELECTION,
        string="Weight Item Adm",
        required=True,
        default='0.05'
    )
    evalu_item_adm = fields.Float(
        string="Evalu Item Adm",
        compute='_compute_evalu_item_adm',
        store=True
    )
    employee_report_kpi_adm_id = fields.Many2one('employee.report.kpi', string="Employee Report KPI")

    @api.depends('higher_evalu_adm', 'direct_evalu_adm')
    def _compute_final_evalu_adm(self):
        for record in self:
            if record.higher_evalu_adm is not None and record.direct_evalu_adm is not None:
                record.final_evalu_adm = (record.higher_evalu_adm + record.direct_evalu_adm) / 2
            else:
                record.final_evalu_adm = 0.0

    @api.depends('final_evalu_adm', 'weight_item_adm')
    def _compute_evalu_item_adm(self):
        for record in self:
            record.evalu_item_adm = round((record.final_evalu_adm * float(record.weight_item_adm)*100) / 100,2)

class Disciplined(models.Model):
    _name = 'disciplined'
    _description = 'Disciplined'

    EVALU_ITEMS_SELECTION = [
        ('الالتزام بالانظمة والتعليمات', 'الالتزام بالانظمة والتعليمات'),
        ('الالتزام بقواعد السلامة المهنية', 'الالتزام بقواعد السلامة المهنية'),
        ('الالتزام بمقر العمل وحسن الهيئة والمظهر', 'الالتزام بمقر العمل وحسن الهيئة والمظهر'),
        ('الحفاظ على موارد الشركة وحسن استخدامها', 'الحفاظ على موارد الشركة وحسن استخدامها'),
        ('الحفاظ على خصوصية بيانات العمل', 'الحفاظ على خصوصية بيانات العمل'),
    ]

    WEIGHT_ITEM_SELECTION = [
        ('0.03', '3%'),
    ]

    evalu_items_dis = fields.Selection(
        EVALU_ITEMS_SELECTION,
        string="Evalu Items",
        required=True
    )
    unit_measur_dis = fields.Char(
        string="Unit Measur",
        default="Percentage",
        readonly=True
    )
    higher_evalu_dis = fields.Float(string="Higher Evalu", default=0.0)
    higher_side_dis = fields.Char(string="Higher Side", default="Direct superior", required=True)
    direct_evalu_dis = fields.Float(string="Direct Evalu", default=0.0)
    direct_side_dis = fields.Char(string="Direct Side", default="Administration Manager", required=True)
    final_evalu_dis = fields.Float(
        string="Final Evalu",
        compute='_compute_final_evalu_dis',
        store=True
    )
    class_weight_dis = fields.Char(
        string="Class Weight",
        default='15%',
        readonly=True
    )
    weight_item_dis = fields.Selection(
        WEIGHT_ITEM_SELECTION,
        string="Weight Item",
        required=True,
        default='0.03',
    )
    evalu_item_dis = fields.Float(string="Evalu Item", compute='_compute_evalu_item_dis', store=True)
    employee_report_kpi_dis_id = fields.Many2one('employee.report.kpi', string="Employee Report KPI")

    @api.depends('higher_evalu_dis', 'direct_evalu_dis')
    def _compute_final_evalu_dis(self):
        for record in self:
            if record.higher_evalu_dis is not None and record.direct_evalu_dis is not None:
                record.final_evalu_dis = (record.higher_evalu_dis + record.direct_evalu_dis) / 2
            else:
                record.final_evalu_dis = 0.0

    @api.depends('final_evalu_dis', 'weight_item_dis')
    def _compute_evalu_item_dis(self):
        for record in self:
            record.evalu_item_dis = round((record.final_evalu_dis * float(record.weight_item_dis) * 100) / 100,2)

class ExceptionalPerformance(models.Model):
    _name = 'exceptional.performance'
    _description = 'Exceptional Performance'

    EVALU_ITEMS_SELECTION_EXC = [
        ('رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)', 'رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)'),
        ('العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)', 'العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)'),
        ('رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)', 'رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)'),
        ('التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)',
         'التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)'),
    ]

    evalu_items_exc = fields.Selection(EVALU_ITEMS_SELECTION_EXC
                                       , string="Evaluation Items", required=True)

    unit_measur_exc = fields.Char(string="Unit Measur")  # Changed from Selection to Char

    higher_evalu_exc = fields.Float(string="Higher Evalu", default=0.0)
    higher_side_exc = fields.Char(string="Higher Side", default="Personnel affairs")
    direct_evalu_exc = fields.Float(string="Direct Evalu", default=0.0)
    direct_side_exc = fields.Char(string="Direct Side", default="Administrative affairs")

    final_evalu_exc = fields.Float(
        string="Final Evalu",
        compute='_compute_final_evalu_exc',
        store=True
    )

    WEIGHT_ITEM_SELECTION = [
        ('0.05', '5%'),
    ]

    class_weight_exc = fields.Char(
        string="Class Weight",
        default='20%',
        readonly=True
    )

    weight_item_exc = fields.Selection(
        WEIGHT_ITEM_SELECTION,
        string="Weight Item",
        required=True,
        default='0.05',
    )

    evalu_item_exc = fields.Float(string="Evalu Item", compute='_compute_evalu_item_exc', store=True)
    employee_report_kpi_exc_id = fields.Many2one('employee.report.kpi', string="Employee Report KPI")

    @api.depends('higher_evalu_exc', 'direct_evalu_exc')
    def _compute_final_evalu_exc(self):
        for record in self:
            if record.higher_evalu_exc is not None and record.direct_evalu_exc is not None:
                record.final_evalu_exc = (record.higher_evalu_exc + record.direct_evalu_exc) / 2
            else:
                record.final_evalu_exc = 0.0

    @api.depends('evalu_items_exc')
    def set_unit_measur_exc(self):
        """Set unit_measur_exc based on evalu_items_exc value."""
        for record in self:
            if record.evalu_items_exc in ['رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)', 'رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)']:
                record.unit_measur_exc = 'عدد'
            elif record.evalu_items_exc in ['العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)', 'التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)']:
                record.unit_measur_exc = 'ايام'
            else:
                record.unit_measur_exc = ''

    @api.depends('final_evalu_exc', 'weight_item_exc', 'evalu_items_exc')
    def _compute_evalu_item_exc(self):
        for record in self:
            # Call the separate function to set unit_measur_exc
            record.set_unit_measur_exc()

            # Compute evalu_item_exc
            if record.evalu_items_exc == 'رسائل لفت النظر خلال الشهر (حد أقصى 3 رسائل)' or record.evalu_items_exc == 'رسائل الشكر  والتقدير خلال الشهر (حد أقصى 3 رسائل)':
                record.evalu_item_exc = round(((record.final_evalu_exc / 3) * float(record.weight_item_exc)) * 100,2)
            elif record.evalu_items_exc == 'العقوبات التأديبية خلال الشهر (حد أقصى 14 يوم)':
                record.evalu_item_exc = round(((record.final_evalu_exc / 14) * float(record.weight_item_exc)) * 100,2)
            elif record.evalu_items_exc == 'التكليفات بعميات استثنائية خلال الشهر (حد أقصى 30 يوم)':
                record.evalu_item_exc = round(((record.final_evalu_exc / 30) * float(record.weight_item_exc)) * 100,2)
            else:
                record.evalu_item_exc = 0.0

class TrainingEducation(models.Model):
    _name = 'training.education'
    _description = 'Training and Education'

    EVALU_ITEMS_TRA_SELECTION = [
        ('اجتياز دورات تدريب وتطوير في مجال الاختصاص', 'اجتياز دورات تدريب وتطوير في مجال الاختصاص'),
        ('اجتياز دورات تدريب وتطوير في اختصاص مساند', 'اجتياز دورات تدريب وتطوير في اختصاص مساند'),
        ('اجتياز دورات تعلم لغة أجنبية', 'اجتياز دورات تعلم لغة أجنبية'),
        ('اجتياز دورات تعلم برامج الكترونية', 'اجتياز دورات تعلم برامج الكترونية'),
    ]

    evalu_items_tra = fields.Selection(
        EVALU_ITEMS_TRA_SELECTION,
        string="Evaluation Items",
        required=True
    )

    unit_measur_tra = fields.Selection(
        [('yes(1)/no(0)', 'Yes(1)/No(0)')],
        string="Unit Measur",
        required=True,
        default='yes(1)/no(0)'
    )

    higher_evalu_tra = fields.Float(string="Higher Evalu", default=0.0)
    higher_side_tra = fields.Char(string="Higher Side", default="Personnel affairs", required=True)
    direct_evalu_tra = fields.Float(string="Direct Evalu", default=0.0)
    direct_side_tra = fields.Char(string="Direct Side", default="Training and development", required=True)

    final_evalu_tra = fields.Float(
        string="Final Evalu",
        compute='_compute_final_evalu_tra',
        store=True
    )

    class_weight_tra = fields.Char(
        string="Class Weight",
        default='10%',
        readonly=True
    )

    weight_item_tra = fields.Float(
        string="Weight Item",
        compute='_compute_weight_item_tra',
        store=True
    )

    evalu_item_tra = fields.Float(string="Evalu Item", compute='_compute_evalu_item_tra', store=True)
    employee_report_kpi_tra_id = fields.Many2one('employee.report.kpi', string="Employee Report KPI")

    @api.depends('higher_evalu_tra', 'direct_evalu_tra')
    def _compute_final_evalu_tra(self):
        for record in self:
            if record.higher_evalu_tra is not None and record.direct_evalu_tra is not None:
                record.final_evalu_tra = (record.higher_evalu_tra + record.direct_evalu_tra) / 2
            else:
                record.final_evalu_tra = 0.0

    @api.depends('final_evalu_tra', 'weight_item_tra')
    def _compute_evalu_item_tra(self):
        for record in self:
            record.evalu_item_tra =round((record.final_evalu_tra * record.weight_item_tra ),2)

    @api.depends('evalu_items_tra')
    def _compute_weight_item_tra(self):
        for record in self:
            if record.evalu_items_tra in ['اجتياز دورات تدريب وتطوير في مجال الاختصاص', 'اجتياز دورات تدريب وتطوير في اختصاص مساند']:
                record.weight_item_tra = 3.00
            elif record.evalu_items_tra in ['اجتياز دورات تعلم لغة أجنبية', 'اجتياز دورات تعلم برامج الكترونية']:
                record.weight_item_tra = 2.00
            else:
                record.weight_item_tra = 0.0

    @api.onchange('evalu_items_tra')
    def _onchange_evalu_items_tra(self):
        self._compute_weight_item_tra()
