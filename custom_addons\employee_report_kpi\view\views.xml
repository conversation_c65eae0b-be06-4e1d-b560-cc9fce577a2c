<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Form view for Employee Report KPI -->
    <record id="view_employee_report_kpi_form" model="ir.ui.view">
        <field name="name">employee.report.kpi.form</field>
        <field name="model">employee.report.kpi</field>
        <field name="arch" type="xml">
            <form string="Employee Report KPI">
                   <header>
                    <button name="report_employee_Xlsx" type="object" string="Report Employee Xlsx" class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="employee_id"/>
                            <field name="date"/>
                            <field name="final_evalue" readonly="1"/>
                        </group>
                        <group>
                            <field name="administration"/>
                            <field name="job_number"/>
                        </group>
                    </group>
                    <notebook>
                        <!-- Presence Page -->
                        <page string="Presence">
                            <field name="presence_ids">
                                <tree editable="bottom">
                                    <field name="evalu_items"/>
                                    <field name="unit_measur" optional="hide"  />
                                    <field name="higher_evalu"/>
                                    <field name="higher_side" optional="hide" readonly="1" />
                                    <field name="direct_evalu"/>
                                    <field name="direct_side" optional="hide" readonly="1" />
                                    <field name="final_evalu" />
                                    <field name="weight_item" optional="hide" />
                                    <field name="class_weight" optional="hide" readonly="1" />
                                    <field name="evalu_item"/>
                                </tree>
                            </field>
                        </page>
                        <!-- Executive Performance Page -->
                        <page string="Executive Performance">
                            <field name="executive_performance_ids">
                                <tree editable="bottom">
                                    <field name="evalu_items_per"/>
                                    <field name="unit_measur_per" optional="hide" readonly="1" />
                                    <field name="higher_evalu_per"/>
                                    <field name="higher_side_per" optional="hide" readonly="1" />
                                    <field name="direct_evalu_per"/>
                                    <field name="direct_side_per" optional="hide" readonly="1" />
                                    <field name="final_evalu_per" />
                                    <field name="weight_item_per" optional="hide"/>
                                    <field name="class_weight_per" readonly="1" optional="hide"/>
                                    <field name="evalu_item_per"/>
                                </tree>
                            </field>
                        </page>

                        <!-- Administrative Behavior Page -->
                        <page string="Administrative Behavior">
                            <field name="administrative_behavior_ids">
                                <tree editable="bottom">
                                    <field name="evalu_items_adm"/>
                                    <field name="unit_measur_adm" optional="hide" readonly="1"/>
                                    <field name="higher_evalu_adm"/>
                                    <field name="higher_side_adm"  readonly="1" optional="hide"/>
                                    <field name="direct_evalu_adm"/>
                                    <field name="direct_side_adm"   readonly="1" optional="hide"/>
                                    <field name="final_evalu_adm" />
                                    <field name="weight_item_adm" optional="hide"/>
                                    <field name="class_weight_adm" readonly="1" optional="hide"/>
                                    <field name="evalu_item_adm" />
                                </tree>
                            </field>
                        </page>

                        <!-- Disciplined Page -->
                        <page string="Disciplined">
                            <field name="disciplined_ids">
                                <tree editable="bottom">
                                    <field name="evalu_items_dis"/>
                                    <field name="unit_measur_dis" optional="hide" readonly="1"/>
                                    <field name="higher_evalu_dis"/>
                                    <field name="higher_side_dis"  readonly="1" optional="hide"/>
                                    <field name="direct_evalu_dis"/>
                                    <field name="direct_side_dis"   readonly="1" optional="hide"/>
                                    <field name="final_evalu_dis" />
                                    <field name="weight_item_dis" optional="hide"/>
                                    <field name="class_weight_dis" readonly="1" optional="hide"/>
                                    <field name="evalu_item_dis"  />
                                </tree>
                            </field>
                        </page>

                         <!-- Exceptional Performance Page -->
                        <page string="Exceptional Performance">
                            <field name="exceptional_performance_ids">
                                <tree editable="bottom">
                                    <field name="evalu_items_exc"/>
                                    <field name="unit_measur_exc" optional="hide"/>
                                    <field name="higher_evalu_exc"/>
                                    <field name="higher_side_exc" optional="hide"/>
                                    <field name="direct_evalu_exc"/>
                                    <field name="direct_side_exc" optional="hide"/>
                                    <field name="final_evalu_exc"  />
                                    <field name="class_weight_exc" readonly="1" optional="hide"/>
                                    <field name="weight_item_exc" optional="hide" readonly="1"/>
                                    <field name="evalu_item_exc" />
                                </tree>
                            </field>
                        </page>
                        <!-- Training and Education Page -->
                        <page string="Training and Education">
                            <field name="training_education_ids">
                                <tree editable="bottom">
                                    <field name="evalu_items_tra"/>
                                    <field name="unit_measur_tra" optional="hide" readonly="1"/>
                                    <field name="higher_evalu_tra"/>
                                    <field name="higher_side_tra" optional="hide"/>
                                    <field name="direct_evalu_tra"/>
                                    <field name="direct_side_tra" optional="hide"/>
                                    <field name="final_evalu_tra" />
                                    <field name="class_weight_tra" readonly="1" optional="hide"/>
                                    <field name="weight_item_tra" optional="hide"/>
                                    <field name="evalu_item_tra" />
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree view for Employee Report KPI -->
    <record id="view_employee_report_kpi_tree" model="ir.ui.view">
        <field name="name">employee.report.kpi.tree</field>
        <field name="model">employee.report.kpi</field>
        <field name="arch" type="xml">
            <tree string="Employee Report KPI">
                <field name="employee_id"/>
                <field name="date" widget="date" options="{'mode': 'month'}"/>
                <field name="administration"/>
                <field name="job_number"/>
                <field name="final_evalue" readonly="1"/>
            </tree>
        </field>
    </record>

</odoo>
