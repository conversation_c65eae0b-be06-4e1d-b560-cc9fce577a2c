from odoo import models, fields, api
from datetime import datetime
from odoo.exceptions import ValidationError


class EmployeeReportAnnual(models.Model):
    _name = 'employee.report.annual'
    _description = 'Employee Report Annual'

    date_from = fields.Date(string='Date From', )
    date_to = fields.Date(string='Date To', )

    department_ids = fields.Many2many('hr.ardano_department', string='Departments')
    division_ids = fields.Many2many('hr.department', string='Divisions')
    team_ids = fields.Many2many('hr.team', string='Teams')
    unit_ids = fields.Many2many('hr.unit', string='Units')

    def confirm(self):
        domain_employee_ids = []

        # Collect all subdomains
        if self.department_ids:
            domain_employee_ids.append(('ardano_department_id', 'in', self.department_ids.ids))
        if self.division_ids:
            domain_employee_ids.append(('department_id', 'in', self.division_ids.ids))
        if self.team_ids:
            domain_employee_ids.append(('ardano_team_id', 'in', self.team_ids.ids))
        if self.unit_ids:
            domain_employee_ids.append(('ardano_unit_id', 'in', self.unit_ids.ids))

        if len(domain_employee_ids) > 1:
            num_or_operators = len(domain_employee_ids) - 1
            domain_employee_ids = ['|'] * num_or_operators + domain_employee_ids

        employee_ids = self.env['hr.employee'].search(domain_employee_ids)

        if not employee_ids:
            raise ValidationError('No employee found with the specified criteria')

        if self.date_from and self.date_to:
            kpi_domain = [('date', '>=', self.date_from), ('date', '<=', self.date_to)]
        else:
            kpi_domain = [('date', '>=', datetime.now().strftime('%Y-01-01')),
                          ('date', '<=', datetime.now().strftime('%Y-12-31'))]
        kpi_domain.append(('employee_id', 'in', employee_ids.ids))
        kpi_ids = self.env['employee.report.kpi'].search(kpi_domain, order='employee_id ASC')
        if not kpi_ids:
            raise ValidationError('No KPI found with the specified criteria')
        kpi_dict = {
            'data': [
                {
                    'employee_id': emp.name,
                    '1': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 1).final_evalue),
                    '2': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 2).final_evalue),
                    '3': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 3).final_evalue),
                    '4': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 4).final_evalue),
                    '5': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 5).final_evalue),
                    '6': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 6).final_evalue),
                    '7': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 7).final_evalue),
                    '8': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 8).final_evalue),
                    '9': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 9).final_evalue),
                    '10': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 10).final_evalue),
                    '11': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 11).final_evalue),
                    '12': float(
                        kpi_ids.filtered(lambda kpi: kpi.employee_id == emp and kpi.date.month == 12).final_evalue)
                } for emp in employee_ids.filtered(lambda emp: emp in kpi_ids.mapped('employee_id'))
            ]}
        return self.env.ref('employee_report_kpi.action_report_employee_kpi').report_action(self, kpi_dict)
