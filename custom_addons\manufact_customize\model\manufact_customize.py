from odoo import models, fields, api

class ManufacturingCustomize(models.Model):
    _inherit = 'mrp.production'

    customer_id = fields.Many2one('res.partner', string="Customer", domain="[('customer_rank', '>', 0)]")
    show_delivery_button = fields.Boolean(string="Delivery", default=False)

    def button_mark_done(self):
        # Call the original method first
        res = super(ManufacturingCustomize, self).button_mark_done()

        # Create a sale order if customer_id is present
        for production in self:
            if production.customer_id:
                sale_order_vals = {
                    'partner_id': production.customer_id.id,
                    'origin': production.name,
                    'order_line': [(0, 0, {
                        'product_id': production.product_id.id,
                        'product_uom_qty': production.product_qty,
                        'price_unit': production.product_id.lst_price,
                        'name': production.product_id.name,
                    })],
                }

                # Create and confirm the sale order
                sale_order = self.env['sale.order'].create(sale_order_vals)
                sale_order.action_confirm()

        # After production is marked as done, set the field to show the delivery button
        self.show_delivery_button = True

        return res

    def _get_action_view_picking(self, pickings):
        # Fetch the delivery orders related to the sales order created for this manufacturing order
        related_picking_ids = self.env['stock.picking'].search([('sale_id.origin', '=', self.name)])

        # Check if we have more than one related picking
        if len(related_picking_ids) > 1:
            action = self.env["ir.actions.actions"]._for_xml_id("stock.action_picking_tree_all")
            action['domain'] = [('id', 'in', related_picking_ids.ids)]
        elif related_picking_ids:
            # If there's only one picking, show the form view
            action = self.env["ir.actions.actions"]._for_xml_id("stock.action_picking_tree_all")
            form_view = [(self.env.ref('stock.view_picking_form').id, 'form')]
            if 'views' in action:
                action['views'] = form_view + [(state, view) for state, view in action['views'] if view != 'form']
            else:
                action['views'] = form_view
            action['res_id'] = related_picking_ids.id

        # If no related picking exists, return an empty context
        else:
            action = {}

        # Prepare the context with relevant data
        if related_picking_ids:
            picking_id = related_picking_ids.filtered(lambda l: l.picking_type_id.code == 'outgoing')
            if picking_id:
                picking_id = picking_id[0]
            else:
                picking_id = related_picking_ids[0]

            action['context'] = dict(self._context,
                                     default_partner_id=self.customer_id.id,
                                     default_picking_type_id=picking_id.picking_type_id.id,
                                     default_origin=self.name,
                                     default_group_id=picking_id.group_id.id)
        return action

    def action_create_delivery(self):
        # Call the action when the delivery button is clicked
        return self._get_action_view_picking(self.picking_ids)
