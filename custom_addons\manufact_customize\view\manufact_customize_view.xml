<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_mrp_production_form_inherit" model="ir.ui.view">
        <field name="name">mrp.production.form.inherit</field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
        <field name="arch" type="xml">
            <!-- Add the 'Customer' field -->
            <xpath expr="//group[@name='group_extra_info']/field[@name='user_id']" position="after">
                <field name="customer_id"/>
            </xpath>
            <!-- Add the invisible 'show_delivery_button' field -->
            <xpath expr="//form" position="inside">
                <field name="show_delivery_button" invisible="1"/>
            </xpath>
            <!-- Add the 'Delivery' button in the header, visible based on the condition -->
            <xpath expr="//div[@name='button_box']" position="inside">
                <button type="object"
                        name="action_create_delivery"
                        icon="fa-truck"
                        string="Delivery"
                        attrs="{'invisible': [('show_delivery_button', '=', False)]}"/>
            </xpath>
        </field>
    </record>
</odoo>

