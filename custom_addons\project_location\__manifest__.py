# -*- coding: utf-8 -*-
{
    'name': "Project Location",

    'author': "Cubes",

    'category': 'Project',
    'version': '1.0',

    # any module necessary for this one to work correctly
    'depends': ['base', 'project', 'hr', 'account', 'purchase', 'stock', 'ardano_accounts_chart', 'project_template',
                'sale_project', 'approvals'],

    # always loaded
    'data': [
        'security/groups.xml',
        'security/ir.model.access.csv',
        'data/sequence.xml',
        'views/project.xml',
        'views/task_tree_view.xml',
        'views/location.xml',
        'views/region.xml',
        'views/contracting_types.xml',
        'views/preparation_types.xml',
        'views/project_stage.xml',
        'views/project_task.xml',
        'views/project_kanban.xml',
        'views/project_create.xml',
        'views/project_task_type.xml',
        'wizard/task_mat_purchase.xml',
        'wizard/project_task.xml',
        'wizard/project_import_sample.xml',
        'wizard/project_export.xml',
        'wizard/point_selection_wizard.xml',
        'wizard/closing_stage_wizard.xml',
        'wizard/canceling_stage_wizard.xml',
        'wizard/check_duplication_wizard.xml',
        'wizard/approval_bill.xml',
        'wizard/daily_bill_report.xml',
        'wizard/project_import.xml',
        'wizard/canceling_work_order_wizard.xml',
        'wizard/cancel_approval_request.xml',
        'wizard/project_report.xml',
        'views/material_details.xml',
        'views/stock_picking.xml',
        'data/server_action.xml',
        'security/ir.model.access.csv',
        'views/config.xml',
        'views/approval_category.xml',
        'views/approval_request.xml',
        'views/approval_tree_view.xml',
        'views/project_smart_buttons.xml',
        'views/account_move.xml',
        'views/account_payment.xml',
        'views/vendor_bill.xml',
        'views/task.xml',
        'views/res_partner.xml',
        'views/approval_request_search_view.xml',
        'reports/ardano_paperformat.xml',
        'reports/daily_bill_report.xml',
        'reports/ardano_bill_report.xml',
        'reports/work_cost_price_template.xml',
        'reports/work_sale_price_template.xml',
        'reports/closing_report.xml',
        'reports/report.xml',
        'views/project_task_delivery_order.xml',
        'views/task_search_view.xml',
        'views/hr_department.xml',
    ],
}
