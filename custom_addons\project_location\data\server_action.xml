<odoo>
    <data>
        <record model="ir.actions.server" id="action_create_bill">
            <field name="name">إنشاء أمر صرف</field>
            <field name="model_id" ref="approvals.model_approval_request"/>
            <field name="binding_model_id" ref="approvals.model_approval_request"/>
            <field name="state">code</field>
            <field name="binding_view_types">list,form</field>
            <field name="code">
                action = records.create_bills()
            </field>
        </record>
        <record model="ir.actions.server" id="action_confirm">
            <field name="name">ترحيل</field>
            <field name="model_id" ref="approvals.model_approval_request"/>
            <field name="binding_model_id" ref="approvals.model_approval_request"/>
            <field name="state">code</field>
            <field name="binding_view_types">list,form</field>
            <field name="code">
                action = records.confirm()
            </field>
        </record>

    </data>
</odoo>