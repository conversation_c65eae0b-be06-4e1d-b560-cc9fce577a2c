from odoo import fields, models, api
from datetime import datetime
from ast import literal_eval
from odoo.exceptions import ValidationError
import calendar
from translate import Translator


class AccountMove(models.Model):
    _inherit = 'account.move'

    created_from_payment_request = fields.Boolean(string='Created from Payment Request')

    payment_selection = fields.Selection([
        ('cash', 'نقدي'),
        ('bank', 'مصرفي'),
    ], string='نوع السداد')
    day_of_week = fields.Char(compute='_compute_day_of_week')
    day_of_week_arabic = fields.Char(compute='_compute_day_of_week')

    @api.depends('invoice_date')
    def _compute_day_of_week(self):
        translator = Translator(to_lang="Arabic")
        for rec in self:
            if rec.invoice_date:
                rec.day_of_week = calendar.day_name[rec.invoice_date.weekday()]
                rec.day_of_week_arabic = translator.translate(
                    calendar.day_name[rec.invoice_date.weekday()])
            else:
                rec.day_of_week = None
                rec.day_of_week_arabic = None

    payment_method = fields.Many2one(comodel_name='account.journal', string='طريقه الدفع')
    percentage = fields.Float(string='النسبه')
    percentage_value = fields.Monetary(string='قيمه النسبه', compute='_compute_percentage_value',
                                       currency_field='currency_id')
    # total_after_percentage = fields.Monetary(string='الإحمالي بعد قيمه النسبه',
    #                                          compute='_compute_percentage_value_after_percentage',
    #                                          currency_field='currency_id')
    bank_ids = fields.Many2many(comodel_name='res.partner.bank', compute='_compute_bank_ids')

    @api.depends('partner_id')
    def _compute_bank_ids(self):
        for rec in self:
            rec.bank_ids = rec.partner_id.bank_ids.ids

    @api.depends('percentage', 'amount_total')
    def _compute_percentage_value(self):
        for rec in self:
            rec.percentage_value = rec.percentage * rec.amount_total

    # @api.depends('percentage', 'amount_total')
    # def _compute_percentage_value_after_percentage(self):
    #     for rec in self:
    #         rec.total_after_percentage = rec.amount_total + rec.percentage_value

    signer_ids = fields.One2many(comodel_name='move.signers', inverse_name='move_id', string='التوقيعات', )

    def calculate_percentage(self):
        for rec in self:
            service_product_id = self.env['ir.config_parameter'].sudo().get_param(
                'ardano_approval.service_product_id')
            service_product_id = literal_eval(service_product_id)
            serviced_lines = self.invoice_line_ids.filtered(
                lambda r: r.percentaged_line == True and r.display_type == 'product')
            non_serviced_lines = self.invoice_line_ids.filtered(
                lambda r: r.percentaged_line == False and r.percentage_line == False and r.display_type == 'product')
            percentage = rec.percentage
            for line in serviced_lines:
                total = line.price_subtotal
                percentage_line_id = line.percentage_line_id
                line_vals = {
                    'product_id': service_product_id,
                    'price_unit': total * percentage,
                    'analytic_distribution': line.analytic_distribution,
                    'percentaged_line': True,
                }
                percentage_line_id.write(line_vals)
            for line in non_serviced_lines:
                total = line.price_subtotal
                line_vals = {
                    'product_id': service_product_id,
                    'price_unit': total * percentage,
                    'analytic_distribution': line.analytic_distribution,
                    'account_id': line.account_id.id,
                    'percentage_line': True,
                    'percentaged_line_id': line.id,
                }
                rec.invoice_line_ids = [(0, 0, line_vals)]
                line.percentaged_line = True
                percentage_line_id = self.env['account.move.line'].search([('percentaged_line_id', '=', line.id)])
                line.percentage_line_id = percentage_line_id.id

            # if service_line:
            #     service_line.price_unit = percentage * (total - sum(line.price_subtotal for line in
            #                                                         service_line.move_id.invoice_line_ids.filtered(
            #                                                             lambda r: r.percentage_line == True)))
            # else:
            #     ic(total, percentage)
            #     line_vals = {
            #         'product_id': service_product_id,
            #         'price_unit': total * percentage,
            #         'percentage_line': True,
            #     }
            #     rec.invoice_line_ids = [(0, 0, line_vals)]

    # @api.model
    # def default_get(self, fields):
    #     res = super(VendorBill, self).default_get(fields)
    #     if not self.signer_ids:
    #         default_lines = [
    #             (0, 0, {'type': ''}),
    #             (0, 0, {'type': ''}),
    #             (0, 0, {'type': ''}),
    #             (0, 0, {'type': ''}),
    #             (0, 0, {'type': ''}),
    #         ]
    #         res['signer_ids'] = default_lines
    #     return res

    approval_request_ids = fields.Many2many(comodel_name='approval.request')

    def action_post(self):
        res = super(AccountMove, self).action_post()
        if self.move_type == 'in_invoice' and self.payment_selection and self.payment_method and self.partner_bank_id:
            account_payment_model = self.env['account.payment'].sudo()
            payment_vals = {
                'amount': self.amount_total,
                'partner_id': self.partner_id.id,
                'date': datetime.today(),
                'journal_id': self.payment_method.id,
                'currency_id': self.currency_id.id,
                'partner_bank_id': self.partner_bank_id.id,
                'state': 'draft',
                'creating_bill_id': self.id,
                'partner_type': 'supplier',
            }
            account_payment = account_payment_model.create(payment_vals)
            account_payment.write({'payment_type': 'outbound'})
        return res

    def _get_day_name(self, day_en):
        days = {
            'Saturday': 'السبت',
            'Sunday': 'الأحد',
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة'
        }
        return days[day_en]


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    approval_request_id = fields.Many2one(comodel_name='approval.request')
    project_location_id = fields.Many2one(comodel_name='project.location', string='الموقع')
    work_delivery_order = fields.Many2one(comodel_name='project.task', string='أمر العمل\ التوريد')

    # the relation to percentage line
    percentaged_line = fields.Boolean(default=False)
    percentaged_line_id = fields.Many2one(comodel_name='account.move.line')

    # the line of percentage related
    percentage_line = fields.Boolean(default=False)
    percentage_line_id = fields.Many2one(comodel_name='account.move.line')

    def unlink(self):
        for rec in self:
            if rec.percentage_line and rec.percentaged_line_id:
                rec.percentaged_line_id.percentaged_line = False
        return super(AccountMoveLine, self).unlink()

    def write(self, vals):
        if any(key in vals for key in (
                'name', 'account_id', 'analytic_distribution', 'quantity', 'product_uom_id', 'price_unit')):
            if len(self.move_id) == 1:
                if self.move_id.created_from_payment_request:
                    raise ValidationError('لا يمكن تعديل فاتوره تم إنشائها من امر سداد.')
            else:
                for move in self.move_id:
                    if move.created_from_payment_request:
                        raise ValidationError('لا يمكن تعديل فاتوره تم إنشائها من امر سداد.')
        res = super(AccountMoveLine, self).write(vals)
        return res


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    creating_bill_id = fields.Many2one(comodel_name='account.move')
    creating_bill_amount = fields.Monetary(related='creating_bill_id.amount_total', currency_field='currency_id')
    # def action_post(self):
    #     if self.creating_bill_id:
    #         self.reconciled_bill_ids = [(4, self.creating_bill_id.id)]
    #     res = super(AccountPayment, self).action_post()
    #     return res

    bill_ids = fields.Many2many(comodel_name='account.move', compute='_compute_bill_ids', string='طلبات الصرف')
    request_ids = fields.Many2many(comodel_name='approval.request', compute='_request_ids', string='أوامر السداد')

    @api.depends('bill_ids')
    def _compute_bill_ids(self):
        for rec in self:
            rec.bill_ids = [bill.id for bill in rec.reconciled_bill_ids]

    @api.depends('request_ids')
    def _request_ids(self):
        for rec in self:
            rec.request_ids = [request_id for bill in rec.bill_ids for request_id in bill.approval_request_ids.ids]

    @api.onchange('amount')
    def _onchange_amount(self):
        for rec in self:
            if rec.creating_bill_id:
                if rec.amount > rec.creating_bill_amount:
                    raise ValidationError('كمية الدفع لا يمكن ان تكون اكبر من كمية فاتورة المورد!')
