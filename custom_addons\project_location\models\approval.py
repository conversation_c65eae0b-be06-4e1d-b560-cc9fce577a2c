from odoo import fields, models, api


class Approval(models.Model):
    _inherit = 'approval.category'

    approval_type = fields.Selection(selection_add=[('payment_order', 'امر سداد')],
                                     ondelete={'payment_order': 'cascade'})

    @api.onchange('approval_type')
    def _onchange_approval_type_payment(self):
        if not self.approval_type == 'payment_order':
            pass
        else:
            self.has_product = 'optional'
            self.has_quantity = 'required'
            self.has_amount = 'required'
