from odoo import fields, models, api
from odoo.exceptions import ValidationError,UserError
import re
import inspect

class ApprovalCategory(models.Model):
    _inherit = 'approval.category'

    def create_request(self):
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "res_model": "approval.request",
            "views": [[False, "form"]],
            "context": {
                'form_view_initial_mode': 'edit',
                'default_category_id': self.id,
                'default_request_owner_id': self.env.user.id,
                'default_request_status': 'new'
            },
        }


class Approval(models.Model):
    _inherit = 'approval.request'

    def action_draft(self):
        res = super(Approval, self).action_draft()
        self.write({'date_confirmed': False})
        return res

    test_field = fields.Boolean(string='test')

    currency_type = fields.Many2one(comodel_name='res.currency',string="نوع العمله")
    currency_rate = fields.Float(string="معدل التحويل")

    @api.onchange('request_type','work_order_id','delivery_order_id')
    def _compute_currency_type(self):
        for rec in self:
            rec.currency_type = rec.work_order_id.currency_type.id if rec.request_type == 'work_order' else rec.currency_type

    work_del_order = fields.Char(compute='_compute_work_del_order', store=1, string='رقم أمر العمل \ التوريد')
    cancel_reason = fields.Char(string='سبب الإلغاء')



    @api.depends('request_type')
    def _compute_work_del_order(self):
        for rec in self:
            rec.work_del_order = rec.work_order_id.name if rec.request_type == 'work_order' else rec.delivery_order_id.name if rec.request_type == 'delivery_order' else False

    def confirm(self):
        status_list = [request.request_status == 'new' for request in self]
        if not all(status_list):
            raise ValidationError("برجاء التأكد من ان كل الأوامر في حاله المسوده")
        user_employee = self.sudo().env.user.employee_id
        for rec in self:
            rec.sudo().action_confirm()
            rec.sudo().action_approve()
            # rec.bill_date = fields.Date.today()
            confirm_sign = rec.signer_ids.filtered(lambda x: x.type == 'control')
            if confirm_sign and user_employee:
                confirm_sign.employee_id = user_employee.id
                confirm_sign.date = fields.Date.today()

    request_status_custom = fields.Selection([
        ('new', 'مسوده'),
        ('pending', 'مرحل'),
        ('approved', 'Approved'),
        ('refused', 'Refused'),
        ('cancel', 'Cancel'),
        ('payment_order', 'أمر صرف'),
        ('partial_payment', 'دفع جزئي'),
        ('paid', 'دفع كلي'),
    ], compute="_compute_request_status_custom", tracking=True, search='_search_request_status_custom', string='حاله الأمر')

    @api.model
    def _search_request_status_custom(self, operator, value):
        all_requests = self.search([]).filtered(lambda r: r.request_status_custom == value)
        domain = [('id', 'in', all_requests.ids)]
        return domain

    def _compute_request_status_custom(self):
        for rec in self:
            payment_status = rec.payment_status
            billed = rec.billed
            if billed == 'billed' and payment_status == 'processing':
                rec.request_status_custom = 'payment_order'
            elif payment_status == 'partial':
                rec.write({'request_status_custom': 'paid'})
            elif payment_status == 'paid':
                rec.request_status_custom = 'paid'
            else:
                rec.request_status_custom = rec.request_status

    billed = fields.Selection(string='مرحل', selection=[('billed', 'نعم'), ('not_billed', 'لا')],
                              compute='_compute_billed', search='_search_billed')

    @api.model
    def _search_billed(self, operator, value):
        all_ids = self.search([]).filtered(lambda r: r.billed == value)
        if operator in ['=', '==']:
            return [('id', 'in', all_ids.ids)]
        elif operator in ['!=', '<>']:
            return [('id', 'in', all_ids.ids)]

    @api.depends('billed')
    def _compute_billed(self):
        for record in self:
            record.billed = 'billed' if record.request_status not in ('cancel','new','refused') else 'not_billed'
            # billed = self.env['account.move'].search([('approval_request_ids', 'in', record.id)])
            # record.billed = 'not_billed' if not billed else 'billed'

    payment_status = fields.Selection(selection=[
        ('paid', 'تم الدفع'),
        ('processing', 'تحت الإجراء'),
        ('partial', 'دفع جزئي'),
        ('failed', 'تعذر الدفع'),

    ], string='حاله الدفع', compute='_compute_payment_status', search='_search_payment_status')

    @api.model
    def _search_payment_status(self, operator, value):
        if operator not in ('=', '!='):
            raise UserError("Unsupported search operator")

        records = self.search([])  # Search for all records
        matching_records = self.browse()
        for rec in records:
            if (rec.payment_status == value and operator == '=') or (rec.payment_status != value and operator == '!='):
                matching_records += rec
        return [('id', 'in', matching_records.ids)]

    def _compute_payment_status(self):
        for rec in self:
            payment_ids = self.env['account.move'].search([('approval_request_ids', 'in', rec.id)])
            if not payment_ids:
                rec.payment_status = None
                continue
            rec_paid = [payment.payment_state == 'paid' for payment in payment_ids]
            rec_partial = [payment.payment_state == 'partial' for payment in payment_ids]
            rec_not_paid = [payment.payment_state == 'not_paid' for payment in payment_ids]
            rec_canceled = [payment.state == 'cancel' for payment in payment_ids]
            if all(rec_paid):
                rec.payment_status = 'paid'
            elif all(rec_partial):
                rec.payment_status = 'partial'
            elif all(rec_not_paid):
                rec.payment_status = 'processing'
            elif all(rec_canceled):
                rec.payment_status = 'failed'
            else:
                rec.payment_status = None

    bill_date = fields.Date('تاريخ امر السداد', readonly=False)

    def action_confirm(self):
        res = super(Approval, self).action_confirm()
        if self.category_id.approval_type == 'payment_order':
            self.sudo().action_approve()
        return res

    def action_cancel(self):
        res = super(Approval, self).action_cancel()
        moves = self.env['account.move'].search([('approval_request_ids', 'in', self.ids)])
        if moves:
            raise ValidationError('Cannot Cancel a request with payments')
        if not inspect.stack()[1].function == 'cancel':
            action = {
                "type": "ir.actions.act_window",
                "res_model": "approval_request.cancel",
                'target': 'new',
                'view_mode': 'form',
                'context': {'default_approval_request_id': self.id},
            }
            return action
        return res

    @api.model
    def create(self, vals):
        if 'name' in vals:
            self._check_name(vals['name'])
        default_lines = [
            (0, 0, {'type': 'preparation'}),
            (0, 0, {'type': 'accountant'}),
            (0, 0, {'type': 'Approval'}),
            (0, 0, {'type': 'control'}),
            (0, 0, {'type': 'department'}),
        ]
        if not vals.get('signer_ids'):
            vals['signer_ids'] = default_lines

        return super(Approval, self).create(vals)

    def write(self, vals):
        # from icecream import ic
        # ic(vals)
        # if self.request_status_custom != 'new' and not vals.get('request_status_custom') and not vals.get(
        #         'date_confirmed') and not vals.get('bill_date') and not vals.get('project_currency_id'):
        #     raise ValidationError('Request status must be "new" or "draft"')
        if 'name' in vals:
            self._check_name(vals['name'])
        if not any(key in vals for key in ('hold', 'active')) and self.hold:
            raise ValidationError('لا يمكن التعديل في حالة التوقف!')
        return super(Approval, self).write(vals)
    active = fields.Boolean(default=True)
    hold = fields.Boolean(default=False)

    def hold_order(self):
        for rec in self:
            rec.active = not rec.active
            rec.hold = not rec.hold

    def _check_name(self, name):
        if not re.match('^[0-9]+$', name):
            raise ValidationError("رقم أمر السداد لا يمكن ان يحتوي على أحرف.")
        if self.search([('name', '=', name)]):
            raise ValidationError("لا يمكن تكرار رقم امر السداد.")

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          compute='_compute_project_currency_id', store=1)

    @api.depends('work_order_id', 'delivery_order_id', 'request_type')
    def _compute_project_currency_id(self):
        for rec in self:
            rec.project_currency_id = rec.work_order_id.project_currency_id if rec.request_type == 'work_order' \
                else rec.delivery_order_id.project_currency_id if rec.request_type == 'delivery_order' else None

    project_id = fields.Many2one(comodel_name='project.project', )
    work_order_id = fields.Many2one(comodel_name='project.task', string='أمر العمل',domain=[('stage_status','!=','cancel')])
    delivery_order_id = fields.Many2one(comodel_name='project.task', string='أمر التوريد')

    contractor_id = fields.Many2one(string='أسم المتعهد', store=True,
                                    comodel_name='res.partner', compute="_compute_contractor_id", readonly=False)
    total_paid = fields.Monetary(string='إجمالي القيم المدفوعه', currency_field='project_currency_id',
                                 compute="_compute_total_paid")

    def _compute_total_paid(self):
        for rec in self:
            if rec.payment_status == 'paid':
                rec.total_paid = rec.total_after_discount
            else:
                rec.total_paid = 0

    @api.depends('request_type', 'work_order_id', 'delivery_order_id')
    def _compute_contractor_id(self):
        for rec in self:
            rec.contractor_id = rec.work_order_id.contractor_id.id if rec.request_type == 'work_order' \
                else rec.delivery_order_id.contractor_id.id if rec.request_type == 'delivery_order' else None

    request_type = fields.Selection(string='نوع الطلب', selection=[
        ('work_order', 'أمر عمل'),
        ('delivery_order', 'طلب توريد'),
    ])

    purchase_order_id = fields.Many2many(comodel_name='purchase.order', string='أوامر شراء')

    @api.onchange('purchase_order_id')
    def _onchange_purchase_order_id(self):
        for rec in self:
            rec.product_line_ids = [(5, 0, 0)]
            for purchase in rec.purchase_order_id:
                for line in purchase.order_line:
                    values = {
                        'product_id': line.product_id.id,
                        'description': line.name,
                        'quantity': line.task_line_id.qty,
                        # 'current_quantity': line.qty_received,
                        'current_quantity': 0,
                        'product_uom_id': line.product_uom.id,
                        'unit_price': line.price_unit,
                        'work_order_purchase_line': line.task_line_id.id,
                    }
                    rec.product_line_ids = [(0, 0, values)]

    order_status = fields.Boolean(compute='_compute_order_status')
    project_status = fields.Boolean(compute='_compute_project_status')
    warning_message = fields.Char(compute='_compute_warning_message')

    @api.depends('order_status', 'project_status')
    def _compute_warning_message(self):
        for rec in self:
            project_message = f" حالة المشروع {rec.project_id.stage_id.name}" if not rec.project_status else ""
            task_message = f"حالة الأمر متوقفه" if not rec.order_status else ""
            rec.warning_message = f"{project_message if project_message else ''} {task_message if task_message else ''}"

    @api.onchange('project_id.stage_status')
    def _compute_project_status(self):
        for rec in self:
            if rec.project_id:
                rec.project_status = True if rec.project_id.stage_status == 'progress' else False
            else:
                rec.project_status = True

    @api.onchange('work_order_id', 'delivery_order_id')
    def _compute_order_status(self):
        for rec in self:
            if rec.request_type == 'work_order' and rec.work_order_id:
                rec.order_status = True if rec.work_order_id.operational_status == 'operational' else False
            elif rec.request_type == 'delivery_order' and rec.delivery_order_id:
                rec.order_status = True if rec.delivery_order_id.operational_status == 'operational' else False
            else:
                rec.order_status = True

    @api.onchange('work_order_id', 'delivery_order_id')
    def _onchange_work_order_id(self):
        for rec in self:
            if rec.work_order_id:
                rec.product_line_ids = [(5, 0, 0)]
                if rec.request_type == 'work_order':
                    rec.project_id = rec.work_order_id.project_id.id
                    for point in rec.work_order_id.point_ids:
                        prev_request = self.env['approval.product.line'].search(
                            [('work_order_line_id', '=', point.id),
                             ('approval_request_id', '!=', rec._origin.id)]).filtered(
                            lambda r: r.approval_request_id.request_status_custom not in ('new','cancel','refused'))
                        prev_quantity = sum(request.current_quantity for request in prev_request)
                        values = {
                            'description': point.point_description if point.point_description else "-",
                            'quantity': point.quantity,
                            # 'current_quantity': point.quantity - prev_quantity,
                            'current_quantity': 0,
                            'prev_quantity': prev_quantity,
                            'product_uom_id': point.uom.id,
                            'unit_price': point.unit_price,
                            'work_order_line_id': point.id,
                            'main_account': rec.work_order_id.main_account.id,
                            'sub_account': rec.work_order_id.sub_account.id,
                            'detailed_account': rec.work_order_id.detailed_account.id,
                            'analytic_account': rec.work_order_id.analytic_account.id,
                        }
                        rec.product_line_ids = [(0, 0, values)]
            elif rec.delivery_order_id:
                rec.product_line_ids = [(5, 0, 0)]
                if rec.request_type == 'delivery_order':
                    rec.project_id = rec.delivery_order_id.project_id.id
                    for point in rec.delivery_order_id.material_ids:
                        prev_request = self.env['approval.product.line'].search(
                            [('delivery_order_line_id', '=', point.id), ('approval_request_id', '!=', rec._origin.id),
                             ('approval_request_id.request_status', '=', 'approved')])
                        prev_quantity = sum(request.current_quantity for request in prev_request)
                        values = {
                            'product_id': point.material_description,
                            'description': point.material_description.name,
                            'quantity': point.qty,
                            'current_quantity': point.qty - prev_quantity,
                            # 'current_quantity': 0,
                            'prev_quantity': prev_quantity,
                            'delivery_order_line_id': point.id,
                            # 'product_uom_id': point.uom.id,
                            # 'unit_price': point.unit_price,
                            # 'main_account': rec.work_order_id.main_account.id,
                            # 'sub_account': rec.work_order_id.sub_account.id,
                            # 'detailed_account': rec.work_order_id.detailed_account.id,
                            # 'analytic_account': rec.work_order_id.analytic_account.id,
                        }
                        rec.product_line_ids = [(0, 0, values)]

    financial_attachment_bool = fields.Boolean(string='ملحق فني ومالي', default=False)
    financial_attachment = fields.Binary(string='ملحق فني ومالي')

    drawings_bool = fields.Boolean(string='رسومات', default=False)
    drawings_attachment = fields.Binary(string='رسومات')

    amount_table_bool = fields.Boolean(string='جدول كميات', default=False)
    amount_table_attachment = fields.Binary(string='جدول كميات')

    proposal_bool = fields.Boolean(string='عرض معتمد', default=False)
    proposal_attachment = fields.Binary(string='عرض معتمد')

    other_bool = fields.Boolean(string='اخرى', default=False)
    other_attachment = fields.Binary(string='اخرى')

    def create_bills(self):
        vendor_ids = [approval.contractor_id.id for approval in self]
        vendor = vendor_ids[0] if all(i == vendor_ids[0] for i in vendor_ids) else None
        action_vals = {
            'res_model': 'approval.bill',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_approval_ids': self.ids, 'vendor_id': vendor},
        }
        return action_vals

    vendor_bill_count = fields.Integer(string='فواتير الموردين', compute='_compute_total_bills')

    def _compute_total_bills(self):
        for rec in self:
            bills = self.env['account.move'].search([('approval_request_ids', 'in', rec.id)])
            rec.vendor_bill_count = len(bills)

    def action_view_payments(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Vendor Bill',
            'res_model': 'account.move',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('approval_request_ids', '=', self.id)],
            'context': {'nocreate': True},
        }

    discount_ids = fields.One2many(comodel_name='request.discount', inverse_name='request_id')
    total_discount = fields.Monetary(compute="_compute_total_discount", currency_field='project_currency_id',
                                     string='إجمالي الخصومات')
    total_pre_discount = fields.Monetary(compute="_compute_total_pre_discount", currency_field='project_currency_id',
                                         string='إجمالي')
    total_after_discount = fields.Monetary(compute="_compute_total_after_discount",
                                           currency_field='project_currency_id', string='إجمالي بعد الخصومات')

    @api.depends('total_discount', 'total_pre_discount')
    def _compute_total_after_discount(self):
        for rec in self:
            rec.total_after_discount = rec.total_pre_discount - rec.total_discount

    @api.depends('product_line_ids')
    def _compute_total_pre_discount(self):
        for rec in self:
            rec.total_pre_discount = sum([line.subtotal for line in rec.product_line_ids])

    @api.depends('discount_ids')
    def _compute_total_discount(self):
        for rec in self:
            rec.total_discount = sum([discount.discount_amount for discount in rec.discount_ids]) + sum([line.discount for line in rec.product_line_ids])

    signer_ids = fields.One2many(comodel_name='approval.signers', inverse_name='approval_id', string='التوقيعات',
                                 ondelete='restrict')

    @api.model
    def default_get(self, fields):

        res = super(Approval, self).default_get(fields)
        if not res.get('category_id'):
            payment_order = self.env['approval.category'].search([('approval_type', '=', 'payment_order')],
                                                                      limit=1).id
            res['category_id'] = payment_order if payment_order else False
        return res


class RequestDiscounts(models.Model):
    _name = 'request.discount'

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='request_id.project_id.project_currency_id')

    request_id = fields.Many2one(comodel_name='approval.request')

    discount_name = fields.Char(string='سبب الخصم', )
    discount_account_id = fields.Many2one(comodel_name='account.account', string='اسم حساب الخصم',
                                          related='request_id.project_id.discount_account')
    discount_amount = fields.Monetary(string='قيمة الخصم', currency_field='project_currency_id')


class ApprovalSigners(models.Model):
    _name = 'approval.signers'

    type = fields.Selection(selection=[
        ('preparation', 'إعداد'),
        ('accountant', 'محاسب'),
        ('Approval', 'إعتماد'),
        ('control', 'تحكم'),
        ('department', 'الإدارة المحتصه'),
    ], string='النوع')
    employee_id = fields.Many2one(comodel_name='hr.employee', string='اسم الموظف')
    date = fields.Date(string='التاريخ')
    note = fields.Text(string='البيان')
    approval_id = fields.Many2one(comodel_name='approval.request')


class VendorBill(models.Model):
    _inherit = 'account.move'


class MoveSigners(models.Model):
    _name = 'move.signers'


    type = fields.Selection(selection=[
        ('preparation', 'إعداد'),
        ('Approval', 'إعتماد'),
        ('Authentication', 'مصادقه'),
        ('control', 'تحكم'),
    ], string='النوع')
    employee_id = fields.Many2one(comodel_name='hr.employee', string='اسم الموظف')
    date = fields.Date(string='التاريخ')
    move_id = fields.Many2one(comodel_name='account.move')


class ApprovalLine(models.Model):
    _inherit = 'approval.product.line'

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='approval_request_id.project_currency_id')

    work_order_line_id = fields.Many2one(comodel_name='task.point')
    work_order_id = fields.Many2one(comodel_name='project.task')
    delivery_order_line_id = fields.Many2one(comodel_name='task.point')
    delivery_order_id = fields.Many2one(comodel_name='project.task')
    work_order_purchase_line = fields.Many2one(comodel_name='task.purchases')
    quantity = fields.Float(default=1.0, string='الكمية التقديرية')
    unit_price = fields.Monetary(string='سعر الوحده', currency_field='project_currency_id')
    prev_quantity = fields.Float(default=1.0, string='الكمية السابقة', )
    current_quantity = fields.Float(default=1.0, string='الكميه الحالية')
    addon = fields.Float(string='اضافه')
    discount = fields.Float(string='خصم')

    @api.constrains('current_quantity')
    def _constraint_current_quantity(self):
        for rec in self:
            if rec.current_quantity < 0:
                raise ValidationError('الكميه الحاليه لا يمكن ان تكون بالسالب')

    discount_reason = fields.Char(string='سبب الخصم')
    discount_amount = fields.Float(string='الخصم')

    # Accounts
    main_account = fields.Many2one(comodel_name='account.account', domain=[('type_of_account', '=', 'main_account')],
                                   string='الحساب الرئيسيي', readonly=True)
    sub_account = fields.Many2one(comodel_name='account.account', domain=[('type_of_account', '=', 'subaccount')],
                                  string='الحساب الفرعي', readonly=True)
    detailed_account = fields.Many2one(comodel_name='account.account',
                                       domain=[('type_of_account', '=', 'detailed_account')],
                                       string='الحساب التفصيلي', readonly=True)
    analytic_account = fields.Many2one(comodel_name='account.account',
                                       domain=[('type_of_account', '=', 'analytic_account')],
                                       string='الحساب التحليلي')
    subtotal = fields.Float('إجمالي فرعي', compute='_compute_subtotal', store=1)

    @api.depends('current_quantity','unit_price','discount','addon')
    def _compute_subtotal(self):
        from icecream import ic
        for rec in self:
            line_subtotal = rec.current_quantity * rec.unit_price
            ic(line_subtotal, rec.addon, rec.discount)
            rec.subtotal = (line_subtotal + rec.addon) - rec.discount

    @api.constrains('current_quantity')
    def _check_current_quantity(self):
        for rec in self:
            if (rec.current_quantity + rec.prev_quantity) > rec.quantity:
                raise ValidationError('الكميه الحاليه والسابقه اكير من الكميه الأساسيه')