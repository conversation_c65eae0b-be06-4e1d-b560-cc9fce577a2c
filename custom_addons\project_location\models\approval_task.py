from odoo import models, fields


class ProjectTask(models.Model):
    _inherit = 'project.task'

    approval_requests = fields.One2many(comodel_name='approval.request', inverse_name='work_order_id')
    approval_count = fields.Integer(string='طلبات السداد', compute='_compute_total_requests')

    def _compute_total_requests(self):
        for rec in self:
            rec.approval_count = len(rec.approval_requests)

    def action_view_payment_requests(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'أوامر السداد',
            'res_model': 'approval.request',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('work_order_id', '=', self.id)],
            'context': {'nocreate': True},
        }

    def create_approval_request(self):
        approval_type = int(self.env['ir.config_parameter'].sudo().get_param(
            'ardano_approval.approval_type'))
        type = 'work_order' if self.request_type == 'work_order' else 'delivery_order'
        vals = {'request_owner_id': self.env.uid, 'project_id': self.project_id.id,
                'category_id': approval_type,
                'work_order_id': self.id,
                'currency_type': self.currency_type.id,
                'currency_rate': self.currency_rate,
                'delivery_order_id': self.id,
                'request_type': type}
        request = self.sudo().env['approval.request'].create(vals)
        request._onchange_work_order_id()
        return {
            'type': 'ir.actions.act_window',
            'name': 'أوامر السداد',
            'res_model': 'approval.request',
            'view_mode': 'form',
            'target': 'current',
            'res_id': request.id,
        }

    def task_payments(self):
        task_ids = self.id
        approval_lines = self.env['approval.product.line'].search(
            ['|', ('approval_request_id.work_order_id', '=', task_ids),
             ('approval_request_id.delivery_order_id', '=', task_ids), ])
        approvals = [approval.approval_request_id.id for approval in approval_lines]
        move_ids = self.env['account.move'].search(
            [('approval_request_ids', 'in', approvals), ('move_type', '=', 'in_invoice')])
        payment_ids = self.env['account.payment'].search([])
        filtered_payment_ids = []
        for payment_id in payment_ids:
            if any(elem.id in move_ids.ids for elem in payment_id.reconciled_bill_ids):
                filtered_payment_ids.append(payment_id.id)

        return {
            'name': 'الدفعات',
            'res_model': 'account.payment',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', filtered_payment_ids)],
        }

    def task_bills(self):
        task_ids = self.id
        approval_lines = self.env['approval.product.line'].search(
            ['|', ('approval_request_id.work_order_id', '=', task_ids),
             ('approval_request_id.delivery_order_id', '=', task_ids), ])
        approvals = [approval.approval_request_id.id for approval in approval_lines]
        move_ids = self.env['account.move'].search(
            [('approval_request_ids', 'in', approvals), ('move_type', '=', 'in_invoice')])
        return {
            'name': 'أوامر الدفع',
            'res_model': 'account.move',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', move_ids.ids)],
        }
