from odoo import models, fields, api
from num2words import num2words


class AccountMove(models.Model):
    _inherit = 'account.move'

    total_in_arabic_words = fields.Char(string='الإجمالي بالحروف', compute='_compute_total_in_arabic_words')

    @api.depends('amount_residual')
    def _compute_total_in_arabic_words(self):
        for record in self:
            record.total_in_arabic_words = num2words(record.amount_residual, lang='ar')
