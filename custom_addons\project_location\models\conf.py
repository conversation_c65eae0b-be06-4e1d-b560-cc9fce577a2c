# -*- coding: utf-8 -*-

from odoo import models, fields, api
from ast import literal_eval


class ApprovalsConf(models.TransientModel):
    _inherit = 'res.config.settings'

    approval_type = fields.Many2one('approval.category')
    operation_type = fields.Many2one(comodel_name='stock.picking.type', )
    service_product_id = fields.Many2one(comodel_name='product.product', string='مصاريف تدبير عمله',
                                         domain=[('type', '=', 'service')])

    def set_values(self):
        res = super(ApprovalsConf, self).set_values()
        self.env['ir.config_parameter'].set_param('ardano_approval.approval_type',
                                                  self.approval_type.id)
        self.env['ir.config_parameter'].set_param('ardano_approval.operation_type',
                                                  self.operation_type.id)
        self.env['ir.config_parameter'].set_param('ardano_approval.service_product_id',
                                                  self.service_product_id.id)
        return res

    @api.model
    def get_values(self):
        res = super(ApprovalsConf, self).get_values()
        try:
            approval_type = self.env['ir.config_parameter'].sudo().get_param(
                'ardano_approval.approval_type')
            approval_type = literal_eval(approval_type)
            res.update(
                approval_type=approval_type,
            )
        except Exception:
            pass
        try:
            operation_type = self.env['ir.config_parameter'].sudo().get_param(
                'ardano_approval.operation_type')
            operation_type = literal_eval(operation_type)

            res.update(
                operation_type=operation_type,
            )
        except Exception:
            pass
        try:
            service_product_id = self.env['ir.config_parameter'].sudo().get_param(
                'ardano_approval.service_product_id')
            service_product_id = literal_eval(service_product_id)

            res.update(
                service_product_id=service_product_id,
            )
        except Exception:
            pass
        return res
