from odoo import fields, models, api
from datetime import datetime
from ast import literal_eval
from odoo.exceptions import ValidationError
import calendar
from translate import Translator


class HrDepartment(models.Model):
    _inherit = 'hr.department'

    accounting_department = fields.Boolean(string='Accounting Division')

class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    account_department = fields.Boolean(string='Accounting Department', related='department_id.accounting_department', store=True)