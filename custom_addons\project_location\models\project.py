from odoo import models
from odoo.exceptions import ValidationError

class ProjectProject(models.Model):
    _inherit = 'project.project'


    def unlink(self):
        if self.stage_id.status != 'draft':
            raise ValidationError('لا يمكن مسح مشروع في غير حاله المسوده')
        res = super(ProjectProject, self).unlink()
        return res

    def write(self, vals):
        if self.stage_id.status == 'progress' and not self.env.user.has_group('project_location.group_Project_edit_in_progress') and 'label_tasks' not in vals:
            raise ValidationError('لا يمكن تديل المشروع في حالة الجاري')
        res = super(ProjectProject, self).write(vals)
        return res

    def action_edit_project_custom(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Edit Project',
            'res_model': 'project.project',
            'view_mode': 'form',
            'target': 'current',
            'res_id': self.id,
        }
