from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
from collections import defaultdict
import ast
import re


class ProjectProject(models.Model):
    _inherit = 'project.project'
    _rec_name = 'display_name'

    delivery_order_count = fields.Integer(compute='_compute_delivery_count')
    project_account_fulfilment = fields.Boolean(compute='_compute_project_account_fulfilment',
                                                store=True)
    project_stock_location = fields.Many2one('stock.location', string='Stock Location')

    @api.depends('cost_of_revenue_account.main_account', 'cost_of_revenue_account.sub_account',
                 'cost_of_revenue_account.detailed_account')
    def _compute_project_account_fulfilment(self):
        for rec in self:
            account_id = rec.cost_of_revenue_account
            fulfilment = bool(account_id.main_account and account_id.sub_account and account_id.detailed_account)
            rec.project_account_fulfilment = True if fulfilment else False

    def action_view_delivery_order(self):
        action = self.env['ir.actions.act_window'].with_context({'active_id': self.id})._for_xml_id(
            'project_location.act_project_project_2_project_task_delivery_order')
        action['display_name'] = _("%(name)s", name=self.name)
        context = action['context'].replace('active_id', str(self.id))
        context = ast.literal_eval(context)
        context.update({
            'create': self.active,
            'active_test': self.active
        })
        action['context'] = context
        return action

    def _compute_delivery_count(self):
        for rec in self:
            rec.delivery_order_count = self.env['project.task'].search_count(
                [('project_id', '=', rec.id), ('request_type', '=', 'delivery_order')])

    def _compute_task_count(self):
        domain = [('project_id', 'in', self.ids), ('is_closed', '=', False), ('request_type', '=', 'work_order')]
        fields = ['project_id', 'display_project_id:count']
        groupby = ['project_id', 'active']
        result_wo_subtask = defaultdict(int)
        result_with_subtasks = defaultdict(int)
        task_all_data = self.env['project.task'].with_context(active_test=False)._read_group(domain, fields, groupby,
                                                                                             lazy=False)
        active_project_ids = self.filtered('active').ids
        for data in task_all_data:
            project_id = data['project_id'][0]
            if data['active'] or project_id not in active_project_ids:
                # count active tasks only of all if the project is archived
                result_wo_subtask[project_id] += data['display_project_id']
            if data['active'] or not self.env.context.get('active_test', True):
                # count subtasks only for active tasks
                result_with_subtasks[project_id] += data['__count']

        for project in self:
            project.task_count = result_wo_subtask[project.id]
            project.task_count_with_subtasks = result_with_subtasks[project.id]

    project_code = fields.Char(string='Project Code', required=1, copy=False)

    display_name = fields.Char(compute='_compute_display_name', store=True)

    @api.depends('project_code')
    def _compute_display_name(self):
        for rec in self:
            rec.display_name = f"{rec.project_code}" if rec.project_code else rec.name

    def write(self, vals):
        if 'project_code' in vals:
            self._check_project_code(vals['project_code'])
        return super(ProjectProject, self).write(vals)

    def _check_project_code(self, code):
        if code and not code.isdigit():
            raise ValidationError("Project Code must be a numerical value.")

    cost_of_revenue_account = fields.Many2one(comodel_name='account.account', readonly=1)
    discount_account = fields.Many2one(comodel_name='account.account', readonly=1, string='حساب الخصم المكتسب')
    _sql_constraints = [
        ('field_unique',
         'unique(project_code)',
         'Choose another value - Project Code has to be unique!')
    ]

    @api.model
    def create(self, vals_list):
        self._check_project_code(vals_list.get('project_code'))
        res = super(ProjectProject, self).create(vals_list)
        # Cost Of Revenue Account
        account_vals = {
            'code': res.project_code,
            'name': res.name,
            'account_type': 'expense_direct_cost',
            'type_of_account': 'analytic_account',
        }
        account_account_id = self.env['account.account'].create(account_vals)
        res.cost_of_revenue_account = account_account_id.id
        ########################################################
        # Discount Account
        account_vals = {
            'name': f"{res.name}{res.project_code}",
            'account_type': 'income_other',
            'type_of_account': 'analytic_account',
            'code': self.get_next_other_income_default_code(),
        }
        discount_account_id = self.env['account.account'].create(account_vals)
        res.discount_account = discount_account_id
        ########################################################
        analytic_plan = self.env['account.analytic.plan'].create(
            {'default_applicability': 'optional', 'name': res.project_code})
        analytic_account = self.env['account.analytic.account'].create(
            {'name': res.name, 'plan_id': analytic_plan.id, 'code': res.project_code})
        res.analytic_account_id = analytic_account.id
        ######################## Add Project Stages ################################
        project_stage_ids = self.env['project.task.type'].search([('user_id', '=', False)])
        for stage in project_stage_ids.sudo():
            stage.project_ids = [(4, res.id)]
        ######################## Stock Location ################################
        stock_vals = {
            'name':vals_list.get('name'),
            'usage':'inventory',
            'valuation_in_account_id':res.cost_of_revenue_account.id,
            'valuation_out_account_id':res.cost_of_revenue_account.id,
                      }
        project_stock_location = self.env['stock.location'].create(stock_vals)
        res.project_stock_location = project_stock_location.id

        return res

    @api.model
    def get_next_other_income_default_code(self):
        latest_account = self.env['account.account'].search([('account_type', '=', 'income_other')],
                                                            order='code DESC', limit=1)
        if latest_account:
            match = re.match(r"([a-z]+)([0-9]+)", latest_account.code, re.I)
            if match:
                non_numeric, numeric = match.groups()
            else:
                non_numeric, numeric = '', latest_account.code
            next_numeric = str(int(numeric) + 1)
            new_code = non_numeric + next_numeric
            return new_code
