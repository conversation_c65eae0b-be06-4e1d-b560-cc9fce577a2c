from odoo import fields, models, api, _


class ProjectProject(models.Model):
    _inherit = 'project.project'

    def export_work(self):
        action = {
            "type": "ir.actions.act_window",
            "res_model": "project.measurement.export.wizard",
            'target': 'new',
            'view_mode': 'form',
            'context': {'default_project_id': self.id},
        }
        return action
