# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ProjectLocation(models.Model):
    _name = 'project.location'
    _description = 'project location'

    name = fields.Char(required=True)
    location_manager = fields.Many2one(comodel_name='hr.employee', string='مدير الموقع')
    region_id = fields.Many2one(comodel_name='project.region', string='المنطقه')


class ProjectRegion(models.Model):
    _name = 'project.region'
    _description = 'project region'

    active = fields.Boolean(default=True)
    name = fields.Char(required=True)
    region_manager = fields.Many2one(comodel_name='hr.employee', string='مدير المنطقه')
    location_ids = fields.One2many(comodel_name='project.location', inverse_name='region_id', string='المواقع')


class ProjectManager(models.Model):
    _name = 'project.manager'
    _description = 'project Manager'

    manager_id = fields.Many2one(comodel_name='hr.employee', string='مدير المشروع', required=True)
    job_title = fields.Char(related='manager_id.job_title')
    job_description = fields.Char()
    project_id = fields.Many2one(comodel_name='project.project')


class ProjectProject(models.Model):
    _inherit = 'project.project'

    location_id = fields.Many2one(comodel_name='project.location', string='الموقع')
    region_id = fields.Many2one(comodel_name='project.region', string='المنطقه', readonly=True)
    location_manager = fields.Many2one(comodel_name='hr.employee', string='مدير الموقع', readonly=True)
    region_manager = fields.Many2one(comodel_name='hr.employee', string='مدير المنطقه', readonly=True)

    operational_cost = fields.Float(string='المصاريف الإدارية والتشغيلية')
    preparation_cost = fields.Float(string='مصاريف تجهيز موقع المشروع  Mobilization')
    currency_cost = fields.Float(string='عمولة توفير السيولة')
    tax_cost = fields.Float(string='الضرائب')
    profit_cost = fields.Float(string='هامش الربح')
    cost_percentage = fields.Float(string='نسبه التكاليف', compute='_compute_cost_percentage', store=True)
    weight_calculation = fields.Selection(string='حسبه وزن المقايسه',
                                          selection=[('auto', 'Automatic'), ('manual', 'Manual')])
    total_weights = fields.Float(compute='_compute_total_weights', store=1)

    @api.depends('work_measurement')
    def _compute_total_weights(self):
        for rec in self:
            rec.total_weights = sum(line.work_measurement_weight for line in rec.work_measurement)

    @api.constrains('work_measurement')
    def _compute_constrain_total_weights(self):
        for rec in self:
            total_weights = sum(line.work_measurement_weight for line in rec.work_measurement)
            if total_weights > 1:
                raise ValidationError("برجاء مراجعه اوزان المقايسه!")

    @api.depends('operational_cost', 'preparation_cost', 'currency_cost', 'tax_cost', 'profit_cost')
    def _compute_cost_percentage(self):
        for rec in self:
            rec.cost_percentage = (rec.operational_cost + rec.preparation_cost
                                   + rec.currency_cost + rec.tax_cost + rec.profit_cost)

    @api.onchange('location_id')
    def _onchange_location_id(self):
        for rec in self:
            rec.region_id = rec.location_id.region_id.id
            rec.location_manager = rec.location_id.location_manager.id
            rec.region_manager = rec.location_id.region_id.region_manager.id

    project_manager_ids = fields.One2many(comodel_name='project.manager', inverse_name='project_id')

    work_measurement = fields.One2many(
        comodel_name='work.measurement',
        inverse_name='project_id',
        string='المقايسه التنفيذيه',
        required=False
    )

    cost_price_total = fields.Float(compute="_compute_cost_price_total", store=1)

    @api.depends('work_measurement')
    def _compute_cost_price_total(self):
        for rec in self:
            rec.cost_price_total = sum(work.total_cost_price for work in rec.work_measurement)

    # Project Type

    project_type = fields.Selection(selection=[
        ('contracting', 'مقاولات'),
        ('preparation', 'تجهيزات'),
    ], string='نوع المشروع')

    preparation_type = fields.Many2one(comodel_name='project.preparation_type', string='نوع التجهيزات')
    contracting_type = fields.Many2one(comodel_name='project.contracting_type', string='نوع المقاولات')

    label_tasks = fields.Char(string='Use Tasks as', compute='_get_default_label', translate=True,
                              help="Name used to refer to the tasks of your project e.g. tasks, tickets, sprints, etc...")

    def _get_default_label(self):
        for rec in self:
            rec.write({'label_tasks': 'أوامر العمل'})


class ProjectPreparationType(models.Model):
    _name = 'project.preparation_type'
    _description = 'Project Preparation Type'

    name = fields.Char(required=True)


class ProjectContractingType(models.Model):
    _name = 'project.contracting_type'
    _description = 'Project Contracting Type'

    name = fields.Char(required=True)
