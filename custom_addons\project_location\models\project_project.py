from odoo import fields, models, api
from datetime import date
import io
import xlsxwriter
from odoo.http import content_disposition, request


class ProjectProject(models.Model):
    _inherit = 'project.project'

    is_odoo_admin = fields.Boolean(string='Is Odoo Admin', compute='_compute_is_odoo_admin')
    english_name = fields.Char(string='English Name')

    plan_number = fields.Char(string='رقم المخطط العام')
    map_number = fields.Char(string='رقم الخريطة التفصيلية')
    contract_number = fields.Char(string='رقم العقد')


    def _compute_is_odoo_admin(self):
        for rec in self:
            rec.is_odoo_admin = self.sudo().env.user.has_group('base.group_erp_manager')

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='cost_of_revenue_account.currency_id', store=True)

    total_paid = fields.Monetary(currency_field='currency_id', compute='_compute_total_paid')

    def _compute_total_paid(self):
        for rec in self:
            rec.total_paid = sum(l.paid_values for l in rec.work_measurement)

    total_commitment = fields.Monetary(currency_field='currency_id', compute='_compute_total_commitment')

    def _compute_total_commitment(self):
        for rec in self:
            rec.total_commitment = sum(
                l.commitment_amount if l.commitment_amount > 0 else 0 for l in rec.work_measurement)

    total_cost_price = fields.Monetary(currency_field='currency_id', compute='_compute_total_cost_price')

    def _compute_total_cost_price(self):
        for rec in self:
            rec.total_cost_price = sum(l.total_cost_price for l in rec.work_measurement)

    total_sales_price = fields.Monetary(currency_field='currency_id', compute='_compute_total_sales_price')

    def _compute_total_sales_price(self):
        for rec in self:
            rec.total_sales_price = sum(l.price_subtotal for l in rec.work_measurement)

    total_work_order_price = fields.Monetary(currency_field='currency_id', compute='_compute_work_order_price')

    def _compute_work_order_price(self):
        for rec in self:
            work_orders = rec.task_ids.filtered(lambda r: r.request_type == 'work_order')
            rec.total_work_order_price = sum(l.total_points for l in work_orders)

    total_approval_request_price = fields.Monetary(currency_field='currency_id', compute='_compute_approval_request_price')

    def _compute_approval_request_price(self):
        for rec in self:
            approval_request_total = sum(request.payment_order_total for request in rec.work_measurement)
            rec.total_approval_request_price = approval_request_total

    total_discounts = fields.Monetary(currency_field='currency_id', compute='_compute_total_discounts')

    def _compute_total_discounts(self):
        for rec in self:
            request_ids = self.env['approval.request'].search(
                ['|', ('work_order_id', 'in', rec.task_ids.ids), ('delivery_order_id', 'in', rec.task_ids.ids)])
            rec.total_discounts = sum(l.total_discount for l in request_ids)

    total_technical_progress = fields.Monetary(currency_field='currency_id', compute='_compute_technical_progress')

    def _compute_technical_progress(self):
        for rec in self:
            line_ids = rec.work_measurement
            rec.total_technical_progress = sum(l.technical_progress for l in line_ids) / len(line_ids) if len(line_ids) > 0 else 0

    total_financial_progress = fields.Monetary(currency_field='currency_id', compute='_compute_financial_progress')

    def _compute_financial_progress(self):
        for rec in self:
            line_ids = rec.work_measurement
            rec.total_financial_progress = sum(l.financial_progress if l.financial_progress <= 1 else 1 for l in line_ids.filtered(lambda r: r.financial_progress > 0)) / len(line_ids) if len(line_ids) > 0 else 0

    has_negative_commitment = fields.Boolean(compute='_has_negative_commitment', search='_search_has_negative_commitment', store=True)

    def _search_has_negative_commitment(self, operator, value):
        project_ids = self.env['project.project'].search([]).filtered(lambda r: r.has_negative_commitment == value)
        return [('id', 'in', project_ids.ids)]

    @api.depends('work_measurement')
    def _has_negative_commitment(self):
        for rec in self:
            rec.has_negative_commitment = True if any(line.commitment_amount_negative for line in rec.work_measurement) else False

    def action_generate_excel(self):
        return {
            'context': self.env.context,
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'project.measurement.import.sample.wizard',
            'type': 'ir.actions.act_window',
            'target': 'new',
        }


class ClosingReport(models.AbstractModel):
    _name = 'report.project_location.closing_report_template'

    @api.model
    def _get_report_values(self, docids, data=None):
        today = date.today()
        project_id = self.env['project.project'].browse([docids[0]])
        all_points = [point for task in project_id.task_ids.filtered(lambda r: r.request_type == 'work_order') for
                      point in task.point_ids if point.commitment_amount != 0]

        return {
            'doc_ids': docids,
            'doc_model': 'project.project',
            'docs': project_id,
            'date': today,
            'points': all_points,
        }
