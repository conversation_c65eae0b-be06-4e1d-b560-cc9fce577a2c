from odoo import fields, models, api


class ProjectProject(models.Model):
    _inherit = 'project.project'

    total_work_orders = fields.Float(string='Total Work Orders', compute='_compute_total_work_orders')
    def _compute_total_work_orders(self):
        for rec in self:
            tasks = self.env['project.task'].search([('project_id', '=', rec.id), ('request_type', '=', 'work_order')]).mapped('total_points')
            rec.total_work_orders = sum(tasks)

    def project_work_orders_action(self):
        return {
            'name': 'أوامر العمل',
            'res_model': 'project.task',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'context': {'default_request_type': 'work_order', 'default_project_id': self.id, 'create': False},
            'domain': [('project_id', '=', self.id), ('request_type', '=', 'work_order')],
        }

    def project_delivery_action(self):
        return {
            'name': 'طلب التوريد',
            'res_model': 'project.task',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'context': {'default_request_type': 'delivery_order', 'default_project_id': self.id, 'create': False},
            'domain': [('project_id', '=', self.id), ('request_type', '=', 'delivery_order')],
        }

    total_payment_requests = fields.Float(string='Total Payment Requests', compute='_compute_payment_requests')

    def _compute_payment_requests(self):
        for rec in self:
            task_ids = rec.task_ids.ids
            requests = self.env['approval.request'].search(['|', ('delivery_order_id', 'in', task_ids), ('work_order_id', 'in', task_ids)]).mapped('total_after_discount')
            rec.total_payment_requests = sum(requests)
    def project_payment_requests(self):
        task_ids = self.task_ids.ids
        return {
            'name': 'أوامر السداد',
            'res_model': 'approval.request',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'context': {'create': False},
            'domain': ['|', ('delivery_order_id', 'in', task_ids), ('work_order_id', 'in', task_ids)],
        }

    def project_purchase_orders(self):
        task_ids = self.task_ids.ids
        approval_ids = self.env['approval.request'].search(
            ['|', ('work_order_id', 'in', task_ids), ('delivery_order_id', 'in', task_ids), ]).ids

        move_ids = self.env['account.move'].search(
            [('approval_request_ids', 'in', approval_ids), ('move_type', '=', 'in_invoice')]).ids
        return {
            'name': 'طلبات الصرف',
            'res_model': 'account.move',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', move_ids)],
        }

    def project_payments(self):
        task_ids = self.task_ids.ids
        approval_ids = self.env['approval.request'].search(
            ['|', ('work_order_id', 'in', task_ids), ('delivery_order_id', 'in', task_ids), ]).ids

        move_ids = self.env['account.move'].search(
            [('approval_request_ids', 'in', approval_ids), ('move_type', '=', 'in_invoice')])

        payment_ids = self.env['account.payment'].search([])
        filtered_payment_ids = []
        for payment_id in payment_ids:
            if any(elem.id in move_ids.ids for elem in payment_id.reconciled_bill_ids):
                filtered_payment_ids.append(payment_id.id)

        return {
            'name': 'الدفعات',
            'res_model': 'account.payment',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', filtered_payment_ids)],
            'context': {'create': False},
        }
