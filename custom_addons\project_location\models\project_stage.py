from odoo import fields, models, api


class ProjectStage(models.Model):
    _inherit = 'project.project.stage'

    status = fields.Selection(selection=[
        ('draft', 'مسوده'),
        ('progress', 'جاري'),
        ('paused', 'متوقف'),
        ('finished', 'منتهي'),
        ('canceled', 'ملغي'),
        ('closed', 'مقفل'),
    ], string="الحاله")

    _sql_constraints = [
        ('status_unique', 'unique(status)', 'The status must be unique!')
    ]


class ProjectProject(models.Model):
    _inherit = 'project.project'

    stage_status = fields.Selection(selection=[
        ('draft', 'مسوده'),
        ('progress', 'جاري'),
        ('paused', 'متوقف'),
        ('finished', 'منتهي'),
        ('canceled', 'ملغي'),
        ('closed', 'مقفل'),
    ], string="الحاله", related='stage_id.status', store=1)
    closing_description = fields.Char(string="سبب الإقفال", readonly=True, )
    closing_date = fields.Date(string="تاريخ الإقفال", readonly=True, )
    canceling_description = fields.Char(string="سبب الإلغاء", readonly=True, )

    def stage_draft(self):
        for rec in self:
            rec.stage_id = rec.stage_id.search([('status', '=', 'draft')], limit=1).id

    def stage_progress(self):
        for rec in self:
            rec.stage_id = rec.stage_id.search([('status', '=', 'progress')], limit=1).id

    def stage_paused(self):
        for rec in self:
            rec.stage_id = rec.stage_id.search([('status', '=', 'paused')], limit=1).id

    def stage_finished(self):
        for rec in self:
            rec.stage_id = rec.stage_id.search([('status', '=', 'finished')], limit=1).id

    def stage_canceled(self):
        return {
            'name': 'إقفال',
            'res_model': 'project.canceling_wizard',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_project_ids': self.ids},
        }

    def stage_closed(self):
        return {
            'name': 'إقفال',
            'res_model': 'project.closing_wizard',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_project_ids': self.ids},
        }
