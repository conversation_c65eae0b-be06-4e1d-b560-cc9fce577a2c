from odoo import fields, models, api, _
from odoo import api, Command, fields, models, tools, SUPERUSER_ID, _, _lt
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class ProjectTask(models.Model):
    _inherit = 'project.task'

    confirmed_date = fields.Datetime(string='تاريخ التاكيد')
    currency_type = fields.Many2one(comodel_name='res.currency',string="نوع العمله")
    currency_rate = fields.Float(string="معدل التحويل")
    
    bill_number = fields.Char(string='رقم الفاتورة')

    project_name = fields.Char(related='project_id.name')
    total_payments_points_match = fields.Boolean(compute='_compute_total_payments_points_match')
    task_end_date = fields.Date(string='تاريخ الإنتهاء', store=1, compute='_compute_task_end_date' )

    point_description_ids = fields.Many2many(comodel_name='project_point.description', compute='_compute_point_description_ids', store=1)

    total_payment = fields.Monetary(compute='_compute_total_payment', currency_field='project_currency_id', string='إجمالي القيم المدفوعه')
    total_payment_request = fields.Monetary(compute='_compute_total_payment', currency_field='project_currency_id', string='اجمالي أوامر السداد')
    total_commitment = fields.Monetary(compute='_compute_total_commitment', currency_field='project_currency_id', string='اجمالي باقي الإلتزامات')

    def _compute_total_commitment(self):
        for rec in self:
            total = sum(line.commitment_amount for line in rec.point_ids)
            rec.total_commitment =total

    def _compute_total_payment(self):
        for rec in self:
            payments_request_ids = self.env['approval.request'].search([('work_order_id', '=', rec.id)])
            rec.total_payment_request = sum(payments_request_ids.mapped('total_after_discount'))
            rec.total_payment = sum(payments_request_ids.mapped('total_paid'))

    @api.depends('point_ids')
    def _compute_point_description_ids(self):
        for rec in self:
            points = rec.point_ids.mapped('description_id')
            rec.sudo().point_description_ids = [(6, 0, points.ids)]

    @api.depends('date','work_duration')
    def _compute_task_end_date(self):
        for rec in self:
            rec.task_end_date = rec.date + timedelta(days=rec.work_duration)
    def _compute_total_payments_points_match(self):
        for rec in self:
            rec.total_payments_points_match = True if rec.total_points == rec.total_payments else False

    @api.onchange('project_id')
    def _onchange_project_id_validation(self):
        for rec in self:
            if rec.project_id:
                if rec.project_id.stage_status != 'progress':
                    raise ValidationError("حاله المشروع ليست جاري")
                if not rec.project_id.project_account_fulfilment:
                    raise ValidationError("برجاء مراجعه حسابات المشروع")

    order_status = fields.Selection(selection=[
        ('draft', 'مسوده'),
        ('confirmed', 'مؤكد'),
    ], string='الحاله')

    def confirm_order(self):
        for rec in self:
            rec.order_status = 'confirmed'

    def reset_order(self):
        for rec in self:
            rec.order_status = 'draft'

    @api.model
    def _read_group_stage_ids(self, stages, domain, order):
        search_domain = [('id', 'in', stages.ids)]
        if domain:
            for criteria in domain:
                if criteria[0] == 'request_type':
                    search_domain.append(('stage_type', '=', criteria[2]))
                    break
        if 'default_project_id' in self.env.context:
            search_domain = ['|', ('project_ids', '=', self.env.context['default_project_id'])] + search_domain

        stage_ids = stages._search(search_domain, order=order, access_rights_uid=SUPERUSER_ID)
        return stages.browse(stage_ids)

    def confirm_work_oder(self):
        confirm_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'work_order'), ('task_type_work_order', '=', 'confirm'),
             ('project_ids', 'in', self.project_id.id)], limit=1)
        self.confirmed_date = fields.Datetime.now()
        self.stage_id = confirm_stage.id
        user_employee = self.sudo().env.user.employee_id
        confirm_sign = self.signer_ids.filtered(lambda x: x.type == 'control')
        if confirm_sign and user_employee:
            confirm_sign.employee_id = user_employee.id
            confirm_sign.date = fields.Date.today()

    cancel_reason = fields.Char('سبب الإلغاء')
    def cancel_work_oder(self):
        order_approvals = self.env['approval.request'].search([('work_order_id', '=', self.id)]).mapped('request_status_custom')
        raise_validate = True if any(approval != 'canceled' for approval in order_approvals) else False
        if raise_validate:
            raise ValidationError('لا يمكن الغاء الطلب بسبب وجود اوامر سدادغير ملغيه')
        action = {
            "type": "ir.actions.act_window",
            "res_model": "task.cancel_work_order.wizard",
            'target': 'new',
            'view_mode': 'form',
            'context': {'default_task_id': self.id},
        }
        return action

    def draft_work_oder(self):
        draft_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'work_order'), ('task_type_work_order', '=', 'draft'),
             ('project_ids', 'in', self.project_id.id)], limit=1)
        self.stage_id = draft_stage.id
        self.sudo().cancel_reason = False

    def product_section_delivery_order(self):
        product_section_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'delivery_order'), ('task_type_delivery_order', '=', 'product_section'),
             ('project_ids', 'in', self.project_id.id)], limit=1)
        self.stage_id = product_section_stage.id

    def delivery_order_delivery_order(self):
        delivery_order_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'delivery_order'), ('task_type_delivery_order', '=', 'delivery_order'),
             ('project_ids', 'in', self.project_id.id)], limit=1)
        self.stage_id = delivery_order_stage.id

    def done_delivery_order(self):
        done_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'delivery_order'), ('task_type_delivery_order', '=', 'done'),
             ('project_ids', 'in', self.project_id.id)], limit=1)
        self.stage_id = done_stage.id

    def draft_delivery_order(self):
        draft_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'delivery_order'), ('task_type_delivery_order', '=', 'draft'),
             ('project_ids', 'in', self.project_id.id)], limit=1)
        self.stage_id = draft_stage.id

    stage_status = fields.Char(compute="_compute_stage_status", store=1)

    @api.depends('stage_id')
    def _compute_stage_status(self):
        for rec in self:
            rec.stage_status = rec.stage_id.task_type_work_order if rec.request_type == 'work_order' \
                else rec.stage_id.task_type_delivery_order

    name = fields.Char(string='رقم الأمر')

    project_area = fields.Many2one(
        comodel_name='project.region',
        string='المنطقه',
        compute='_compute_related_fields',
        required=False,
        store=1
    )

    project_location = fields.Many2one(
        comodel_name='project.location',
        compute='_compute_related_fields',
        string='الموقع',
        required=False,
        store=1
    )

    project_type = fields.Selection(selection=[
        ('contracting', 'مقاولات'),
        ('preparation', 'تجهيزات'),
    ], compute='_compute_related_fields', string='نوع المشروع',
        store=1
    )

    preparation_type = fields.Many2one(
        comodel_name='project.preparation_type',
        compute='_compute_related_fields',
        string='نوع التجهيزات',
        store=1
    )
    contracting_type = fields.Many2one(
        comodel_name='project.contracting_type',
        compute='_compute_related_fields',
        string='نوع المقاولات',
        store=1
    )

    @api.depends('project_id')
    def _compute_related_fields(self):
        for compute in self:
            compute.project_area = compute.project_id.region_id.id if compute.project_id.region_id else False
            compute.project_location = compute.project_id.location_id.id if compute.project_id.location_id else False
            compute.project_type = compute.project_id.project_type if compute.project_id.project_type else False
            compute.preparation_type = compute.project_id.preparation_type.id if compute.project_id.preparation_type else False
            compute.contracting_type = compute.project_id.contracting_type.id if compute.project_id.contracting_type else False

    def write(self, vals):
        if self.request_type == 'work_order':
            if self.stage_id.task_type_work_order == 'confirm' and not vals.get('stage_id') and not vals.get('signer_ids') and not vals.get('confirmed_date'):
                raise ValidationError('لا يمكن التعديل في حالة المؤكد!')

        if not any(key in vals for key in ('hold', 'active')) and self.hold:
            raise ValidationError('لا يمكن التعديل في حالة التوقف!')
        res = super(ProjectTask, self).write(vals)
        return res

    hold = fields.Boolean(default=False)

    def hold_order(self):
        for rec in self:
            rec.active = not rec.active
            rec.hold = not rec.hold

    def unlink(self):
        if self.approval_requests:
            raise ValidationError('لا يمكن حذف الأمر بسبب وجود أمر سداد')
        return super(ProjectTask, self).unlink()