# -*- coding: utf-8 -*-

from odoo import models, fields, api
from num2words import num2words
from odoo.exceptions import ValidationError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    supplier_rank_edit = fields.Boolean(string='متعهد', default=False)

    @api.onchange('supplier_rank_edit')
    def _compute_supplier_rank(self):
        for rec in self:
            rec.supplier_rank = 1 if rec.supplier_rank_edit else 0

    contractor_state = fields.Selection(string='الحالة',
                                        selection=[('operational', 'مستمر'), ('non_operational', 'متوقف')],
                                        default='operational')
