# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import ValidationError
from ast import literal_eval


class Task(models.Model):
    _inherit = 'project.task'

    directed_to = fields.Char(string='موجه إلى')
    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          compute="_compute_project_currency_id", store=1, readonly=False)

    @api.depends('project_id.project_currency_id')
    def _compute_project_currency_id(self):
        for task in self:
            task.project_currency_id = task.project_id.project_currency_id.id

    material_ids = fields.One2many(comodel_name='task.material', inverse_name='task_id')
    valid_material_ids = fields.Many2many('product.product', compute='_compute_valid_material_ids')
    buy_return = fields.Selection(string='شراء - ترجيع', selection=[('buy', 'شراء'), ('return', 'ترجيع'), ],
                                  default='buy')
    mat_purchase_ids = fields.One2many(comodel_name='task.purchases', inverse_name='task_id')
    mat_ops_ids = fields.One2many(comodel_name='task.operations', inverse_name='task_id')
    add_give = fields.Selection(string='صرف - إضافه', selection=[('give', 'صرف'), ('add', 'إضافه'), ],
                                default='give')

    purchase_count = fields.Integer(compute='_compute_purchase_count')

    stock_transfers_count = fields.Integer(compute='_stock_transfers_count')

    def _compute_purchase_count(self):
        for rec in self:
            rec.purchase_count = len({po_id
                                      for purchase in rec.mat_purchase_ids
                                      for po_id in purchase.purchase_order_ids})

    def _stock_transfers_count(self):
        for rec in self:
            rec.stock_transfers_count = len({po_id
                                             for transfer in rec.mat_ops_ids
                                             for po_id in transfer.stock_picking_ids})

    def view_task_purchases(self):
        purchase_ids = [po_id
                        for purchase in self.mat_purchase_ids
                        for po_id in purchase.purchase_order_ids.ids]
        return {
            'name': 'Purchase Orders',
            'res_model': 'purchase.order',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', purchase_ids)],
        }

    def view_task_transfers(self):
        purchase_ids = [po_id
                        for transfer in self.mat_ops_ids
                        for po_id in transfer.stock_picking_ids.ids]
        return {
            'name': 'Transfers',
            'res_model': 'stock.picking',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', purchase_ids)],
        }

    @api.depends('material_ids')
    def _compute_valid_material_ids(self):
        for record in self:
            if record.material_ids:
                used_material_ids = record.material_ids.mapped('material_description').ids
                record.valid_material_ids = [(6, 0, used_material_ids)]
            else:
                record.valid_material_ids = [(6, 0, [])]

    def open_mat_purchase_wizard(self):
        if not self.buy_return:
            raise ValidationError(
                "تأكد من أنك أخترت أذ كان أمر ترجيع أو شراء")
        vals = {
            'task_id': self.id,
        }
        wizard_id = self.env['task.mat_purchase_wizard'].create(vals)
        for line in self.mat_purchase_ids:
            wizard_id.write({'line_ids': [(0, 0,
                                           {'task_id': self.id,
                                            'material_description': line.material_description.id,
                                            'category': line.category,
                                            'task_line_id': line.id,
                                            'qty': line.qty})], })
        return {
            'res_model': 'task.mat_purchase_wizard',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': wizard_id.id,
        }

    def create_transfer(self):
        return self.mat_ops_ids.create_transfer()

    def create_transfer_and_purchase_order(self):
        try:
            self.create_transfer_from_materials()
            wizard_id = self.create_purchase_from_materials()
            return {
                'res_model': 'task.mat_purchase_wizard',
                'view_mode': 'form',
                'type': 'ir.actions.act_window',
                'target': 'new',
                'res_id': wizard_id.id,
            }
        except Exception as e:
            print(e)

    def create_purchase_from_materials(self):
        vals = {
            'task_id': self.id,
        }
        wizard_id = self.env['task.mat_purchase_wizard'].create(vals)

        for line in self.material_ids:
            purchase_vals = {
                'material_description': line.material_description.id,
                'qty': line.qty_needed,
                'task_id': self.id,
            }
            purchase_line = self.env['task.purchases'].create(purchase_vals)
            wizard_line_vals = {
                'task_id': self.id,
                'material_description': line.material_description.id,
                'task_line_id': purchase_line.id,
                'qty': line.qty_needed
            }
            wizard_id.write({'line_ids': [(0, 0, wizard_line_vals)]})

        return wizard_id

    def create_transfer_from_materials(self):
        operation_type = self.env['ir.config_parameter'].sudo().get_param(
            'ardano_approval.operation_type')
        operation_type = literal_eval(operation_type)

        # Search for the stock picking type and get its default destination location
        picking_type = self.env['stock.picking.type'].search([('id', '=', operation_type)])
        location_id = picking_type.default_location_dest_id

        # Create a new stock picking
        transfer_id = self.env['stock.picking'].create({
            'location_id': location_id.id,
            'picking_type_id': picking_type.id,
            'cost_center_id': self.project_id.cost_of_revenue_account.id,
        })

        # Prepare lines for stock move and mat_ops
        move_lines = []
        mat_ops_lines = []
        for transfer in self.material_ids:
            transfer_qty = transfer.qty - transfer.qty_needed
            transfer_vals = {
                'product_id': transfer.material_description.id,
                'name': transfer.material_description.name,
                'product_uom_qty': transfer_qty,
                'analytic_distribution': {transfer.project_id.cost_of_revenue_account.id: 100},
                'product_uom': transfer.material_uom_id.id,
                'location_id': location_id.id,
                'location_dest_id': picking_type.default_location_dest_id.id,
                'picking_id': transfer_id.id,
            }
            move_lines.append((0, 0, transfer_vals))

            ops_vals = {
                'material_description': transfer.material_description.id,
                'qty': transfer_qty,
                'stock_picking_ids': [(4, transfer_id.id)]
            }
            mat_ops_lines.append((0, 0, ops_vals))

        # Update the move lines and mat_ops for the stock picking
        transfer_id.write({'move_ids_without_package': move_lines})
        self.write({'mat_ops_ids': mat_ops_lines})


class TaskMaterial(models.Model):
    _name = 'task.material'

    task_id = fields.Many2one(comodel_name='project.task')
    seq_number = fields.Integer(readonly=True, string='رقم البند', compute='_compute_point_number', )
    material_description_char = fields.Char(string='وصف المواد')
    material_description = fields.Many2one(comodel_name='product.product', string='الماده')
    material_barcode = fields.Char(related='material_description.barcode', string='Barcode')
    cost_center_number = fields.Char(related='project_id.project_code', string='رقم مركز التكلفه', store=1)
    material_uom_id = fields.Many2one(comodel_name='uom.uom', related='material_description.uom_id',
                                      string='وحده القياس')
    qty = fields.Float(string='الكمية')
    avail_qty = fields.Float(string='الكمية المتاحه', compute='get_vail_qty')
    qty_needed = fields.Float(string='الكمية المطلوب شرائها', compute='get_qty_needed')

    @api.depends('material_description')
    def get_vail_qty(self):
        for rec in self:
            product = rec.material_description
            avail_qty = product.qty_available - product.outgoing_qty
            rec.avail_qty = avail_qty

    @api.depends('avail_qty', 'qty')
    def get_qty_needed(self):
        for rec in self:
            if rec.qty - rec.avail_qty > 0:
                qty_needed = rec.qty - rec.avail_qty
            else:
                qty_needed = 0.0
            rec.qty_needed = qty_needed

    category = fields.Selection(string='التصنيف', selection=[
        ('primary', 'رئيسي'),
        ('sub', 'فرعي')
    ])
    project_id = fields.Many2one(comodel_name='project.project')
    mat_symbol = fields.Char(string='رمز البند', )
    note = fields.Char(string='ملاحظات')

    @api.onchange('material_description')
    def _onchange_material_description(self):
        for rec in self:
            rec.mat_symbol = rec.material_description.default_code

    @api.depends('seq_number', 'task_id.material_ids')
    def _compute_point_number(self):
        point_number = 1
        for rec in self.task_id.material_ids:
            rec.seq_number = point_number
            point_number += 1


class TaskPurchases(models.Model):
    _name = 'task.purchases'

    task_id = fields.Many2one(comodel_name='project.task')
    seq_number = fields.Integer(readonly=True, string='رقم البند', compute='_compute_point_number', )

    @api.depends('seq_number', 'task_id.mat_purchase_ids')
    def _compute_point_number(self):
        point_number = 1
        for rec in self.task_id.mat_purchase_ids:
            rec.seq_number = point_number
            point_number += 1

    material_description = fields.Many2one(comodel_name='product.product', string='وصف المواد', )
    category = fields.Selection(string='التصنيف', selection=[
        ('primary', 'رئيسي'),
        ('sub', 'فرعي')
    ])
    qty = fields.Float(string='الكمية')
    purchase_order_ids = fields.Many2many(comodel_name='purchase.order')


class TaskOperations(models.Model):
    _name = 'task.operations'

    task_id = fields.Many2one(comodel_name='project.task')
    seq_number = fields.Integer(readonly=True, string='رقم البند', compute='_compute_point_number', )
    location_id = fields.Many2one(comodel_name='stock.location', string='المخزن')

    @api.depends('seq_number', 'task_id.mat_ops_ids')
    def _compute_point_number(self):
        point_number = 1
        for rec in self.task_id.mat_ops_ids:
            rec.seq_number = point_number
            point_number += 1

    material_description = fields.Many2one(comodel_name='product.product', string='وصف المواد', )
    qty = fields.Float(string='الكمية')
    stock_picking_ids = fields.Many2many(comodel_name='stock.picking')

    def create_transfer(self):
        operation_type = self.env['ir.config_parameter'].sudo().get_param(
            'ardano_approval.operation_type')
        operation_type = literal_eval(operation_type)
        # location_id = self.env['stock.picking.type'].search([('id', '=', operation_type)]).default_location_dest_id
        location_id = self.task_id.project_id.project_stock_location
        picking_ids = []
        for transfer in self:
            picking_id = self.sudo().env['stock.picking'].create({
                'location_dest_id': location_id.id if location_id else None,
                'location_id': self.location_id.id if location_id else None,
                'picking_type_id': operation_type,
                'task_id': transfer.task_id.id,
            })
            new_line = {
                'name': transfer.material_description.name,
                'product_id': transfer.material_description.id,
                'product_uom_qty': transfer.qty,
                'location_dest_id': location_id.id if transfer.task_id.add_give == 'add' else transfer.location_id.id,
                'location_id': location_id.id if transfer.task_id.add_give == 'give' else transfer.location_id.id,
            }
            picking_id.sudo().update({'move_ids_without_package': [(0, 0, new_line)]})
            transfer.stock_picking_ids = [(4, picking_id.id)]
            picking_ids.append(picking_id.id)
        return {
            'res_model': 'stock.picking',
            'view_mode': 'tree,form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'domain': [('id', 'in', picking_ids)]
        }
