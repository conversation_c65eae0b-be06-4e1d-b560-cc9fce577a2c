# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from num2words import num2words
from odoo.exceptions import ValidationError
import re


class Task(models.Model):
    _inherit = 'project.task'
    _rec_name = 'display_name'

    def _check_name(self, name):
        if isinstance(name, str):
            if not re.match('^[0-9]+$', name):
                raise ValidationError("رقم الأمر لا يمكن ان يحتوي على أحرف.")

    contractor_status = fields.Selection(string='الحالة',
                                         selection=[('operational', 'مستمر'), ('non_operational', 'متوقف')],
                                         related='contractor_id.contractor_state')

    contractor_id = fields.Many2one(comodel_name='res.partner', domain=[('supplier_rank', '=', 1)],
                                    string='اسم المتعهد')
    contractor_bill = fields.Char(string='رقم فاتورة المتعهد')
    vat = fields.Char(related='contractor_id.vat', string='رقم الهوية')

    point_ids = fields.One2many(comodel_name='task.point', inverse_name='task_id')

    cost_center_number = fields.Char(string='رقم مركز التكلفه', related='analytic_account_id.code')
    cost_center_category = fields.Many2one(comodel_name='account.account',
                                           domain=[('account_type', 'in', ('expense_direct_cost', 'expense'))],
                                           string='تصنيف مركز التكلفه')
    cost_center_group = fields.Many2one(comodel_name='account.account',
                                        domain=[('account_type', 'in', ('expense_direct_cost', 'expense'))],
                                        string='مراكز التكلفه المجمعة')
    cost_center_analytic = fields.Many2one(comodel_name='account.account',
                                           domain=[('account_type', 'in', ('expense_direct_cost', 'expense'))],
                                           string='مركز التكلفه التحليلية',
                                           related='project_id.cost_of_revenue_account')

    total_points = fields.Monetary(string='الإجمالي', compute='_compute_total', currency_field='project_currency_id')


    @api.model
    def _get_view(self, view_id=None, view_type='form', **options):
        arch, view = super()._get_view(view_id, view_type, **options)
        type = self._context.get('default_request_type')
        string = "أجمالي أوامر العمل" if type == 'work_order' else 'إجمالي طلبات التوريد' if type == 'delivery_order' else False
        if view_type == 'tree' and string:
            for node in arch.xpath("//field[@name='total_points']"):
                node.set('string', string)
        return arch, view

    total_in_arabic_words = fields.Char(string='الإجمالي بالحروف', compute='_compute_total_in_arabic_words')

    @api.onchange('cost_center_analytic')
    def _onchange_cost_center_analytic_account(self):
        for rec in self:
            if rec.cost_center_analytic:
                rec.cost_center_group = rec.cost_center_analytic.sub_account.id
                rec.cost_center_category = rec.cost_center_analytic.main_account.id
                for point in rec.point_ids:
                    point.cost_center_analytic = rec.cost_center_analytic.id
                    point.cost_center_group = rec.cost_center_analytic.sub_account.id
                    point.cost_center_category = rec.cost_center_analytic.main_account.id

    @api.depends('point_ids')
    def _compute_total(self):
        for record in self:
            total = 0
            for point in record.point_ids:
                total += point.total
            record.total_points = total

    @api.depends('total_points')
    def _compute_total_in_arabic_words(self):
        for record in self:
            words = num2words(record.total_points, lang='ar')
            record.total_in_arabic_words = (
                    words + f"{record.project_currency_id.symbol}") if record.project_currency_id else words

    def write(self, vals):

        # if self.search_count([('work_order_number', '=', vals.get('work_order_number'))]) and vals.get(
        #         'work_order_number'):
        #     raise ValidationError("The work order number must be unique!")
        if self.search_count([('delivery_order_number', '=', vals.get('delivery_order_number'))]) and vals.get(
                'delivery_order_number'):
            raise ValidationError("The delivery order number must be unique!")
        if 'name' in vals:
            self._check_name(vals['name'])

        res = super(Task, self).write(vals)
        # if ((self.analytic_account and vals.get('contractor_id')) or
        #         (self.contractor_id and vals.get('analytic_account'))):
        #     self.contractor_id.property_account_payable_id = self.analytic_account.id
        return res

    type_of_account = fields.Selection(string='Account Type', selection=[
        ('main_account', 'Main Account'),
        ('subaccount', 'Subaccount'),
        ('detailed_account', 'Detailed Account'),
        ('analytic_account', 'Analytic Account'),
    ])

    main_account = fields.Many2one(comodel_name='account.account', string='الحساب الرئيسيي', readonly=True)
    sub_account = fields.Many2one(comodel_name='account.account', string='الحساب الفرعي', readonly=True)
    detailed_account = fields.Many2one(comodel_name='account.account',
                                       string='الحساب التفصيلي', readonly=True)
    analytic_account = fields.Many2one(comodel_name='account.account',
                                       domain=[('type_of_account', '=', 'analytic_account')],
                                       string='الحساب التحليلي', related='contractor_id.property_account_payable_id')

    request_type = fields.Selection(string='نوع الطلب', selection=[
        ('work_order', 'أمر عمل'),
        ('delivery_order', 'طلب توريد'),
    ])

    @api.onchange('analytic_account')
    def _onchange_analytic_account(self):
        for rec in self:
            if rec.analytic_account:
                rec.detailed_account = rec.analytic_account.detailed_account.id
                rec.sub_account = rec.analytic_account.sub_account.id
                rec.main_account = rec.analytic_account.main_account.id

    operational_status = fields.Selection(string='الحالة',
                                          selection=[('operational', 'مستمر'), ('non_operational', 'متوقف')],
                                          default='operational')
    work_order_number = fields.Char(string='رقم أمر العمل', copy=False)

    # @api.constrains('work_order_number')
    # def _constrain_work_order_number(self):
    #     for rec in self:
    #         if len(str(rec.work_order_number)) != 5:
    #             raise ValidationError("رقم أمر العمل لا يمكن ان يقل او يزيد عن 5 أرقام")

    delivery_order_number = fields.Char(string='كود أمر التوريد', copy=False, default=lambda self: _('New'))

    @api.model
    def create(self, vals):
        if vals.get('work_order_number') and self.search_count(
                [('work_order_number', '=', vals.get('work_order_number'))]):
            raise ValidationError(f"The work order number must be unique!")
        if vals.get('delivery_order_number') and self.search_count(
                [('delivery_order_number', '=', vals.get('delivery_order_number'))]):
            raise ValidationError("The delivery order number must be unique!")
        if 'name' in vals:
            self._check_name(vals['name'])
        if vals['request_type'] == 'delivery_order':
            vals['delivery_order_number'] = self.env['ir.sequence'].next_by_code(
                'delivery.order') or None
            vals['name'] = vals['delivery_order_number']
        else:
            vals['delivery_order_number'] = None
        res = super(Task, self).create(vals)
        return res

    @api.onchange('work_order_number')
    def _onchange_work_order_number(self):
        for rec in self:
            rec.name = rec.work_order_number

    @api.onchange('delivery_order_number')
    def _onchange_delivery_order_number(self):
        for rec in self:
            rec.name = rec.delivery_order_number

    date = fields.Date(string='التاريخ', default=fields.Date.context_today)

    financial_attachment_bool = fields.Boolean(string='ملحق فني ومالي', default=False)
    financial_attachment = fields.Binary(string='ملحق فني ومالي')

    drawings_bool = fields.Boolean(string='رسومات', default=False)
    drawings_attachment = fields.Binary(string='رسومات')

    amount_table_bool = fields.Boolean(string='جدول كميات', default=False)
    amount_table_attachment = fields.Binary(string='جدول كميات')

    proposal_bool = fields.Boolean(string='عرض معتمد', default=False)
    proposal_attachment = fields.Binary(string='عرض معتمد')

    other_bool = fields.Boolean(string='اخرى', default=False)
    other_attachment = fields.Binary(string='اخرى')

    work_duration = fields.Integer(default=0, string='مدة التنفيذ بالأيام', readonly=False)

    # Payments

    payment_ids = fields.One2many(comodel_name='task.payment', inverse_name='task_id')

    display_name = fields.Char(compute='_compute_display_name', store=True)

    @api.depends('name', 'request_type', 'work_order_number', 'delivery_order_number')
    def _compute_display_name(self):
        for rec in self:
            if rec.request_type == 'work_order':
                rec.display_name = f"{rec.work_order_number}"
                print(rec.display_name)
            elif rec.request_type == 'delivery_order':
                rec.display_name = f"{rec.delivery_order_number}"
            else:
                rec.display_name = f"{rec.name}"

    total_payments = fields.Monetary(compute='_compute_total_payments', currency_field='project_currency_id')

    @api.depends('payment_ids')
    def _compute_total_payments(self):
        for rec in self:
            total = sum([p.output_val for p in rec.payment_ids])
            if total > rec.total_points:
                raise ValidationError("إجمالي الشروط لا يمكن ان يكون اكبر من أجمالي البنود!")
            rec.total_payments = total

    signer_ids = fields.One2many(comodel_name='task.signers', inverse_name='task_id', string='التوقيعات', )

    @api.model
    def default_get(self, fields_list):
        res = super(Task, self).default_get(fields_list)
        if not self.signer_ids:
            default_lines = [
                (0, 0, {'type': 'preparation'}),
                (0, 0, {'type': 'Approval'}),
                (0, 0, {'type': 'Authentication'}),
                (0, 0, {'type': 'control'}),
            ]
            self.signer_ids = default_lines
        return res

    approval_status_editable = fields.Boolean(compute='_compute_approval_status_editable')
    approval_status_editable_override_user = fields.Boolean(compute='_compute_approval_status_editable_override_user')

    @api.depends('approval_requests')
    def _compute_approval_status_editable(self):
        for rec in self:
            rec.approval_status_editable = True if any(
                [approval.request_status == 'approved' for approval in rec.approval_requests]) else False

    @api.depends('approval_status_editable_override_user')
    def _compute_approval_status_editable_override_user(self):
        for rec in self:
            rec.approval_status_editable_override_user = True if self.env.user.has_group(
                'project_location.group_Project_edit_approved_tasks') else False


class TaskSigners(models.Model):
    _name = 'task.signers'

    type = fields.Selection(selection=[
        ('preparation', 'إعداد'),
        ('Approval', 'إعتماد'),
        ('Authentication', 'الإداره المختصه'),
        ('control', 'مصادقه تحكم'),
    ], string='النوع')
    employee_id = fields.Many2one(comodel_name='hr.employee', string='اسم الموظف', domain=[('account_department', '=', True)])
    task_id = fields.Many2one(comodel_name='project.task')
    date = fields.Date(string='التاريخ')
    note = fields.Text(string='البيان')


class TaskPayment(models.Model):
    _name = 'task.payment'
    _description = 'Task Payment'

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='task_id.project_currency_id')

    payment_number = fields.Integer(readonly=True, string='رقم الدفعه', compute='_compute_payment_number',
                                    default=1)
    payment = fields.Selection(string='الدفعه', selection=[
        ('amount', 'قيمة'),
        ('percentage', 'نسبه'),
    ])
    input_val = fields.Float(string='النسبة المئوية', )
    output_val = fields.Monetary(string='القيمة', currency_field='project_currency_id')

    @api.onchange('output_val', 'input_val')
    def _onchange_payment_input(self):
        for rec in self:
            if rec.payment == 'percentage':
                rec.output_val = rec.input_val * rec.task_id.total_points
            elif rec.payment == 'amount':
                if rec.task_id.total_points != 0:
                    rec.input_val = rec.output_val / rec.task_id.total_points
                else:
                    rec.input_val = 0

    payment_term = fields.Char(string='شروط الدفعه')
    payment_type = fields.Selection(string='نوع السداد', selection=[
        ('cash', 'كاش'),
        ('bank', 'تحويل بنكي'),
        ('bond', 'صك'),
    ])
    notes = fields.Char(string='ملاخظات')
    task_id = fields.Many2one(comodel_name='project.task')

    @api.depends('payment_number', 'task_id.payment_ids')
    def _compute_payment_number(self):
        payment_number = 1
        for rec in self.task_id.payment_ids:
            rec.payment_number = payment_number
            payment_number += 1


class TaskPoint(models.Model):
    _name = 'task.point'
    _description = 'Task Point'

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='task_id.project_currency_id')

    payment_order_total = fields.Monetary(string='قيم أوامر السداد', compute='_compute_payment_order_total',
                                          currency_field='project_currency_id')

    @api.depends('payment_order_total')
    def _compute_payment_order_total(self):
        for rec in self:
            approval_lines = self.env['approval.product.line'].search(
                [('work_order_line_id', '=', rec.id)]).filtered(
                lambda r: r.approval_request_id.request_status_custom not in ('new', 'cancel', 'refused'))
            rec.payment_order_total = sum(
                approval.subtotal for approval in approval_lines)

    project_work_descriptions = fields.Many2many(compute='project_chosen_work',
                                                 comodel_name='project.work.description')

    project_points_descriptions = fields.Many2many(compute='project_chosen_points',
                                                   comodel_name='project_point.description')

    def check_point_duplication(self):
        shared_points = self.search([
            ('task_id.project_id', '=', self.task_id.project_id.id),
            ('description_id', '=', self.description_id.id),
            ('categorization', '=', self.categorization.id),
            ('id', '!=', self.id)
        ])

        # Create Wizard Lines records and get their IDs
        line_ids = []
        for point in shared_points:
            line_id = self.env['check.duplication_wizard_lines'].create({'task_point_id': point.id}).id
            line_ids.append(line_id)

        # Create Wizard record with the correct format for Many2many fields
        wizard_id = self.env['check.duplication_wizard'].create({'task_point_ids': [(6, 0, line_ids)]})
        return {
            'name': 'Details',
            'res_model': 'check.duplication_wizard',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'res_id': wizard_id.id,
        }

    @api.depends('task_id.project_id.work_measurement.description_id')
    def project_chosen_points(self):
        for rec in self:
            point_ids = [p.description_id.id for p in rec.task_id.project_id.work_measurement if p.description_id]
            rec.project_points_descriptions = [(6, 0, point_ids)]

    @api.depends('task_id.project_id.work_measurement.work_description')
    def project_chosen_work(self):
        for rec in self:
            point_ids = [p.work_description.id for p in rec.task_id.project_id.work_measurement if p.work_description]
            rec.project_work_descriptions = [(6, 0, point_ids)]

    point_number = fields.Integer(readonly=True, string='رقم البند', compute='_compute_point_number', )
    point_description = fields.Char(string='بيان الأعمال')

    categorization = fields.Many2one(comodel_name='project.work.description', string='تصنيف البند')
    description_id = fields.Many2one(string='وصف البند فى في المقايسة', required=False,
                                     comodel_name='project_point.description', )
    description = fields.Char(string='وصف البند فى في المقايسة', required=False, )

    @api.onchange('description_id')
    def _onchange_description_id(self):
        for rec in self:
            project_point = self.env['work.measurement'].search(
                [('description_id', '=', rec.description_id.id), ('project_id', '=', rec.task_id.project_id.id)],
                limit=1)
            project_point.point_id = rec._origin.id
            vals = {
                'quantity': 0,
                'categorization': project_point.work_description.id,
                'uom': project_point.product_uom_id.id,
                'unit_price': project_point.price_unit,
                # 'created_from_project': True,
            }
            rec.write(vals)

    uom = fields.Many2one(comodel_name='uom.uom', string='الوحده')
    quantity = fields.Float(string='الكميه')
    quantity_match = fields.Boolean(compute="compute_quantity_match")

    @api.depends('project_points_descriptions')
    def compute_quantity_match(self):
        for rec in self:
            project_point = self.env['work.measurement'].search(
                [('description_id', '=', rec.description_id.id), ('project_id', '=', rec.task_id.project_id.id)])
            rec.quantity_match = True if rec.quantity == sum(project_point.mapped('qty')) else False

    unit_price = fields.Monetary(string='سعر الوحده', currency_field='project_currency_id')
    notes = fields.Char(string='ملاخظات')
    total = fields.Monetary(string='القيمة', compute='_compute_total', currency_field='project_currency_id')

    cost_center_number = fields.Char(string='رقم مركز التكلفه', related='task_id.analytic_account_id.code')

    cost_center_category = fields.Many2one(comodel_name='account.account',
                                           domain=[('account_type', 'in', ('expense_direct_cost', 'expense'))],
                                           string='تصنيف مركز التكلفه')
    cost_center_group = fields.Many2one(comodel_name='account.account',
                                        domain=[('account_type', 'in', ('expense_direct_cost', 'expense'))],
                                        string='مراكز التكلفه المجمعة')
    cost_center_analytic = fields.Many2one(comodel_name='account.account',
                                           domain=[('account_type', 'in', ('expense_direct_cost', 'expense'))],
                                           string='مركز التكلفه التحليلية')
    work_measurement_id = fields.Many2one(comodel_name='work.measurement')
    created_from_project = fields.Boolean(default=False)

    @api.onchange('cost_center_analytic')
    def _onchange_point_cost_center_analytic_account(self):
        for rec in self:
            if rec.cost_center_analytic:
                rec.cost_center_group = rec.cost_center_analytic.sub_account.id
                rec.cost_center_category = rec.cost_center_analytic.main_account.id

    @api.onchange('work_measurement_id')
    def _onchange_work_measurement(self):
        for record in self:
            record.work_measurement_id.task_id = record.task_id.id

    task_id = fields.Many2one(comodel_name='project.task')

    @api.depends('quantity', 'unit_price')
    def _compute_total(self):
        for rec in self:
            rec.total = rec.quantity * rec.unit_price

    @api.depends('point_number', 'task_id.point_ids')
    def _compute_point_number(self):
        point_number = 1
        for rec in self.task_id.point_ids:
            rec.point_number = point_number
            point_number += 1

    total_in_arabic_words = fields.Char(string='الإجمالي بالحروف', compute='_compute_total_in_arabic_words')

    @api.depends('total')
    def _compute_total_in_arabic_words(self):
        for record in self:
            record.total_in_arabic_words = num2words(record.total, lang='ar')

    paid_values = fields.Monetary(
        string='القيم المدفوعه',
        required=False,
        compute='_compute_paid_values',
        currency_field='project_currency_id',
    )

    commitment_amount = fields.Monetary(
        string='باقي اﻷلتزامات',
        required=False,
        compute='_compute_paid_values',
        currency_field='project_currency_id',
    )

    def _compute_paid_values(self):
        for rec in self:
            approval_lines = self.env['approval.product.line'].search(
                [('work_order_line_id', '=', rec.id)]).filtered(
                lambda r: r.approval_request_id.payment_status == 'paid')
            approvals = [approval_line.current_quantity * approval_line.unit_price for approval_line in
                         approval_lines]
            total_paid = sum(approvals)
            rec.paid_values = total_paid
            rec.commitment_amount = rec.total - rec.paid_values

    def write(self, vals):
        res = super(TaskPoint, self).write(vals)
        # TODO: Check Validation #
        # if vals.get('quantity'):
        #     qty = vals.get('quantity') or 0
        #     older_qty = self.search(
        #         [('description_id', '=', self.description_id.id), ('id', '!=', self._origin.id)]).mapped('quantity')
        #     total_qty = qty + sum(older_qty)
        #     project_qty = sum(self._origin.task_id.project_id.work_measurement.filtered(
        #         lambda l: l.description_id.id == self.description_id.id).mapped('qty'))
        #     if total_qty > project_qty:
        #         raise ValidationError("إجمالي الكميات في البند أكبر من الكميات في المشروع")
        # if vals.get('unit_price'):
        #     project_price = sum(self.task_id.project_id.work_measurement.filtered(
        #         lambda l: l.description_id.id == self.description_id.id).mapped('price_unit'))
        #     price = vals.get('unit_price') or 0
        #     if price > project_price:
        #         raise ValidationError("السعر في البند اكبر من السعر في المشروع")
        return res
