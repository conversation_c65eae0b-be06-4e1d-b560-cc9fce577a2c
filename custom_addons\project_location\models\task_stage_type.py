from odoo import fields, models, api, _


class TaskStageType(models.Model):
    _inherit = 'project.task.type'

    stage_type = fields.Selection(selection=[('work_order', 'أمر عمل'), ('delivery_order', 'أمر توريد')])
    task_type_work_order = fields.Selection(selection=[('draft', 'مسوده'), ('confirm', 'مؤكد'),('cancel','ملغي')])
    task_type_delivery_order = fields.Selection(selection=[
        ('draft', 'مسوده'),
        ('product_section', 'قسم المواد'),
        ('delivery_order', 'أمر توريد'),
        ('done', 'منتهي')])
