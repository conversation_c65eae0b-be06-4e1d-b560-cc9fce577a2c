from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class WorkMeasurement(models.Model):
    _name = 'work.measurement'

    work_measurement_weight = fields.Float(string='وزن البند', compute='_compute_work_measurement_weight',
                                           store=True)
    financial_progress = fields.Float(string='نسبه الإنجاز الماليه', compute='_compute_financial_progress', )
    financial_progress_over_85 = fields.Bo<PERSON>an(compute='_compute_financial_progress_over_85', )

    @api.depends('financial_progress')
    def _compute_financial_progress_over_85(self):
        for rec in self:
            rec.financial_progress_over_85 = True if rec.financial_progress >= 0.85 else False

    technical_progress = fields.Float(string='نسبه الإنجاز الفنيه', compute='_compute_technical_progress', )
    has_entries = fields.<PERSON><PERSON><PERSON>(compute='_compute_has_entries', )

    def unlink(self):
        if self.has_entries:
            raise ValidationError("Lines Cannot Be Deleted.")
        return super(WorkMeasurement, self).unlink()

    def _compute_has_entries(self):
        for rec in self:
            rec.has_entries = True if rec.payment_order_total or rec.paid_values else False

    @api.depends('project_id.work_measurement', 'work_measurement_weight', 'financial_progress')
    def _compute_technical_progress(self):
        for rec in self:
            rec.technical_progress = rec.financial_progress * rec.work_measurement_weight \
                if (rec.financial_progress != 0 and rec.work_measurement_weight) != 0 else 0

    @api.depends('project_id.work_measurement', 'project_id.cost_price_total', 'payment_order_total')
    def _compute_financial_progress(self):
        for rec in self:
            rec.financial_progress = rec.payment_order_total / rec.total_cost_price \
                if (rec.payment_order_total != 0 and rec.total_cost_price) != 0 else 0

    @api.depends('project_id.work_measurement', 'total_cost_price', 'project_id.cost_price_total',
                 'project_id.weight_calculation')
    def _compute_work_measurement_weight(self):
        for rec in self:
            if rec.project_id.weight_calculation == 'auto':
                rec.work_measurement_weight = rec.total_cost_price / rec.project_id.cost_price_total \
                    if rec.total_cost_price != 0 and rec.project_id.cost_price_total != 0 else 0
            else:
                rec.work_measurement_weight = rec.work_measurement_weight

    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='project_id.project_currency_id')
    payment_order_total = fields.Monetary(string='قيم أوامر السداد', compute='_compute_payment_order_total',
                                          currency_field='project_currency_id')

    @api.depends('payment_order_total')
    def _compute_payment_order_total(self):
        for rec in self:
            task_ids = rec.project_id.task_ids
            point_lines = self.env['task.point'].search(
                [('description_id', '=', rec.description_id.id), ('task_id', 'in', task_ids.ids), '|',
                 ('task_id.stage_id.task_type_work_order', '=', 'confirm'),
                 ('task_id.stage_id.task_type_delivery_order', '!=', 'draft')])
            approval_lines = self.env['approval.product.line'].search(
                [('work_order_line_id', 'in', point_lines.ids)]).filtered(
                lambda r: r.approval_request_id.request_status_custom not in ('new', 'cancel', 'refused'))
            rec.payment_order_total = sum(
                approval.subtotal for approval in approval_lines)

    project_id = fields.Many2one(
        comodel_name='project.project',
        string='Project',
        required=False)

    task_id = fields.Many2one(
        comodel_name='project.task',
        string='Task Id',
        required=False)

    point_id = fields.Many2many(
        comodel_name='task.point',
        string='Task Point',
        required=False)

    seq = fields.Integer(
        string='ر.م',
        compute='_compute_sequence',
        required=False)

    @api.depends('seq', 'project_id.work_measurement')
    def _compute_sequence(self):
        number = 1
        for record in self.project_id.work_measurement:
            record.seq = number
            number += 1

    work_description = fields.Many2one(
        comodel_name='project.work.description',
        string='تصنيف البند',
        required=False)

    description_id = fields.Many2one(
        string='وصف البند في المقايسة التنفيذية',
        required=False,
        comodel_name='project_point.description'
    )

    qty = fields.Float(
        string='الكميه',
        required=False)

    product_uom_id = fields.Many2one('uom.uom', string='وحده القياس ')

    price_unit = fields.Monetary(
        string='سعر الوحده (البيع)',
        required=False,
        compute='_compute_sale_price',
        currency_field='project_currency_id')

    cost_price = fields.Monetary(
        string='سعر التكلفه',
        currency_field='project_currency_id'
    )

    total_cost_price = fields.Monetary(
        string='إجمالي سعر التكلفه',
        compute='_compute_total_cost_price',
        currency_field='project_currency_id')

    price_subtotal = fields.Monetary(
        string='الاجمالي سعر البيع',
        compute='_compute_total_price',
        required=False,
        currency_field='project_currency_id'
    )

    paid_values = fields.Monetary(
        string='القيم المدفوعه',
        required=False,
        compute='_compute_paid_values',
        currency_field='project_currency_id'
    )

    commitment_amount = fields.Monetary(
        string='باقي اﻷلتزامات',
        required=False,
        compute='_compute_paid_values',
        currency_field='project_currency_id'
    )
    commitment_amount_negative = fields.Boolean(compute='commitment_amount_negative_negative')

    @api.depends('commitment_amount')
    def commitment_amount_negative_negative(self):
        for rec in self:
            rec.commitment_amount_negative = True if rec.commitment_amount < 0 else False

    notes = fields.Char(
        string='ملاحظات',
        required=False,
    )

    def _compute_paid_values(self):
        for rec in self:
            filtered_lines = self.env['task.point'].search(
                [('task_id.project_id', '=', rec.project_id.id), ('description_id', '=', rec.description_id.id), '|',
                 ('task_id.stage_id.task_type_work_order', '=', 'confirm'),
                 ('task_id.stage_id.task_type_delivery_order', '!=', 'draft')])
            approval_lines = self.env['approval.product.line'].search(
                [('work_order_line_id', 'in', filtered_lines.ids), ('approval_request_id.payment_status', '=', 'paid')])

            approvals = [approval_line.current_quantity * approval_line.unit_price for approval_line in
                         approval_lines]
            total_paid = sum(approvals)
            rec.paid_values = total_paid
            rec.commitment_amount = rec.total_cost_price - rec.paid_values

    @api.depends('qty', 'price_unit')
    def _compute_total_price(self):
        for rec in self:
            rec.price_subtotal = rec.qty * rec.price_unit

    @api.depends('price_unit', 'project_id.cost_percentage')
    def _compute_cost_price(self):
        for rec in self:
            rec.cost_price = rec.price_unit - (rec.project_id.cost_percentage * rec.price_unit)

    @api.depends('cost_price', 'project_id.cost_percentage')
    def _compute_sale_price(self):
        for rec in self:
            rec.price_unit = round(rec.cost_price + (rec.project_id.cost_percentage * rec.cost_price))

    @api.depends('price_unit', 'project_id.cost_percentage')
    def _compute_total_cost_price(self):
        for rec in self:
            rec.total_cost_price = rec.cost_price * rec.qty

    def action_create_task(self):
        task_obj = self.env['project.task'].create({
            'name': self.work_description.name,
            'project_id': self.project_id.id,
            'partner_id': self.project_id.partner_id.id,
            'request_type': 'work_order',
        })
        self.task_id = task_obj.id
        return {
            'res_model': 'project.task',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'res_id': task_obj.id,
        }

    def action_create_task_delivery(self):
        task_obj = self.env['project.task'].create({
            'name': self.work_description.name,
            'project_id': self.project_id.id,
            'partner_id': self.project_id.partner_id.id,
            'request_type': 'delivery_order',
        })
        self.task_id = task_obj.id
        return {
            'res_model': 'project.task',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'res_id': task_obj.id,
        }


class WorkDescription(models.Model):
    _name = 'project.work.description'

    name = fields.Char()


class ProjectWorkDescription(models.Model):
    _name = 'project_point.description'

    name = fields.Char()
