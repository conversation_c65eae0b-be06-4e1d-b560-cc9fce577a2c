<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>

        <record model="ir.module.category" id="project_location.Project_stage_category">
            <field name="name">Project Stages</field>
            <field name="sequence">4</field>
            <field name="parent_id" ref="base.module_category_services"/>
            <field name="description">Stage Buttons Security Access</field>
        </record>

        <record id="group_Project_edit_approved_tasks" model="res.groups">
            <field name="name">تعديل التاسك المغلقه</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_Project_edit_in_progress" model="res.groups">
            <field name="name">تعديل المشاريع الجاريه</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_project_drafting" model="res.groups">
            <field name="name">إرجاع المشروع إلى مسوده من مقفل</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_approval_bill_report" model="res.groups">
            <field name="name">تقرير المرحل اليومي</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_Project_stage_draft" model="res.groups">
            <field name="name">مسوده</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_Project_stage_progress" model="res.groups">
            <field name="name">جاري</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_Project_stage_paused" model="res.groups">
            <field name="name">متوقف</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_Project_stage_finished" model="res.groups">
            <field name="name">منتهي</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_Project_stage_canceled" model="res.groups">
            <field name="name">ملغي</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_Project_stage_closed" model="res.groups">
            <field name="name">مقفل</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_confirm_work_order" model="res.groups">
            <field name="name">تأكيد أمر العمل</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_draft_work_order" model="res.groups">
            <field name="name">مسوده أمر العمل</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_product_section_delivery_order" model="res.groups">
            <field name="name">قسم المواد أمر توريد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_delivery_order_delivery_order" model="res.groups">
            <field name="name">أمر توريد أمر توريد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_done_delivery_order" model="res.groups">
            <field name="name">منتهي أمر توريد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_draft_delivery_order" model="res.groups">
            <field name="name">مسوده أمر توريد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_material_description" model="res.groups">
            <field name="name">تفاصيل المواد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>

        <record id="group_create_bill_from_approval" model="res.groups">
            <field name="name">انشاء أمر صرف من أمر السداد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>


        <record id="group_hold_work_order" model="res.groups">
            <field name="name">إيقاف/تفعيل أمر العمل</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_hold_approval_request" model="res.groups">
            <field name="name">إيقاف/تفعيل أمر السداد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_hold_partner" model="res.groups">
            <field name="name">إيقاف/تفعيل مورد</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>
        <record id="group_hold_location" model="res.groups">
            <field name="name">إيقاف/تفعيل منطقه</field>
            <field name="category_id" ref="project_location.Project_stage_category"/>
        </record>


    </data>
</odoo>