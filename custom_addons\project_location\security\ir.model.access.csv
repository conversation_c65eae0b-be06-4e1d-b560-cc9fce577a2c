id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_project_location_project_location,project_location.project_location,model_project_location,,1,1,1,1
access_project_location_project_region,project_location.project_region,model_project_region,,1,1,1,1
access_project_location_work_measurement,project_location.work_measurement,model_work_measurement,,1,1,1,1
access_project_location_project_work_description,project_location.project_work_description,model_project_work_description,,1,1,1,1
access_project_location_project_manager,project_location.project_manager,model_project_manager,,1,1,1,1
access_project_location_project_preparation_type,project_location.project_preparation_type,model_project_preparation_type,,1,1,1,1
access_project_location_project_contracting_type,project_location.project_contracting_type,model_project_contracting_type,,1,1,1,1

access_project_location_task_point,project_location.task_point,model_task_point,,1,1,1,1
access_project_location_task_material,project_location.task_material,model_task_material,,1,1,1,1
access_project_location_task_purchases,project_location.task_purchases,model_task_purchases,,1,1,1,1
access_project_location_task_mat_purchase_wizard,project_location.task_mat_purchase_wizard,model_task_mat_purchase_wizard,,1,1,1,1
access_project_location_task_purchases_wizard_line,project_location.task_purchases_wizard_line,model_task_purchases_wizard_line,,1,1,1,1

access_project_location_task_operations,project_location.task_operations,model_task_operations,,1,1,1,1
access_project_location_task_point_wizard,project_location.task_point_wizard,model_task_point_wizard,,1,1,1,1
access_project_location_project_point_description,project_location.project_point_description,model_project_point_description,,1,1,1,1

access_project_location_task_signers,project_location.task_signers,model_task_signers,,1,1,1,1
access_project_location_project_closing_wizard,project_location.project_closing_wizard,model_project_closing_wizard,,1,1,1,1
access_project_location_project_canceling_wizard,project_location.project_canceling_wizard,model_project_canceling_wizard,,1,1,1,1
access_project_location_check_duplication_wizard,project_location.check_duplication_wizard,model_check_duplication_wizard,,1,1,1,1
access_project_location_check_duplication_wizard_lines,project_location.check_duplication_wizard_lines,model_check_duplication_wizard_lines,,1,1,1,1
access_project_location_approval_daily_bill_wizard_line,project_location.approval_daily_bill_wizard_line,model_approval_daily_bill_wizard_line,,1,1,1,1

access_project_location_approval_bill,project_location.approval_bill,model_approval_bill,base.group_user,1,1,1,1
access_project_location_approval_daily_bill_wizard,project_location.approval_daily_bill_wizard,model_approval_daily_bill_wizard,base.group_user,1,1,1,1
access_project_location_request_discount,project_location.request_discount,model_request_discount,base.group_user,1,1,1,1
access_project_location_approval_signers,project_location.approval_signers,model_approval_signers,base.group_user,1,1,1,1
access_project_location_move_signers,project_location.move_signers,model_move_signers,base.group_user,1,1,1,1

access_project_location_project_measurement_import_wizard,project_location.project_measurement_import_wizard,model_project_measurement_import_wizard,base.group_user,1,1,1,1
access_project_location_project_measurement_import_wizard_line,project_location.project_measurement_import_wizard_line,model_project_measurement_import_wizard_line,base.group_user,1,1,1,1

access_project_location_project_measurement_import_sample_wizard,project_location.project_measurement_import_sample_wizard,model_project_measurement_import_sample_wizard,base.group_user,1,1,1,1
access_project_location_project_measurement_export_wizard,project_location.project_measurement_export_wizard,model_project_measurement_export_wizard,base.group_user,1,1,1,1

access_project_location_task_payment,access_project_location.task_payment,model_task_payment,base.group_user,1,1,1,1
access_project_location_task_cancel_work_order_wizard,access_project_location.task_cancel_work_order_wizard,model_task_cancel_work_order_wizard,base.group_user,1,1,1,1

access_project_location_approval_request_cancel,access_project_location.approval_request_cancel,model_approval_request_cancel,base.group_user,1,1,1,1

access_project_location_project_report_wizard,access_project_location.project_report_wizard,model_project_report_wizard,base.group_user,1,1,1,1
access_project_location_project_report_wizard_line,access_project_location.project_report_wizard_line,model_project_report_wizard_line,base.group_user,1,1,1,1