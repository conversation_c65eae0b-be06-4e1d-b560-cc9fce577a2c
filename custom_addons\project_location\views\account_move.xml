<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="account_move_inherit" model="ir.ui.view">
            <field name="name">account.move.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('oe_chatter')]" position="replace">
                </xpath>
                <xpath expr="//sheet" position="inside">
                    <separator/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" options="{'post_refresh':True}" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </xpath>
                <xpath expr="//group[@id='header_right_group']" position="inside">
                    <field name="payment_selection"/>
                    <field name="payment_method" domain="[('type','=',payment_selection)]"
                           attrs="{'invisible':[('payment_selection','=',False)]}"/>
                    <label for="percentage"/>
                    <div class="o_row"
                         attrs="{'invisible':[('payment_selection','!=','bank')]}">
                        <field name="percentage" widget="percentage"/>
                        <button name="calculate_percentage" type="object" string="Calculate" class="oe_highlight"/>
                    </div>

                    <field name="bank_ids" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='amount_total']" position="after">
                    <!--                    <field name="percentage_value"/>-->
                    <!--                    <field name="total_after_percentage"/>-->
                </xpath>
            </field>
        </record>
        <record id="account_move_line_inherit" model="ir.ui.view">
            <field name="name">account.move.line.inherit</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='product_id']" position="after">
                    <field name="project_location_id"
                           attrs="{'column_invisible': [('parent.move_type', '!=', 'in_invoice')]}" optional="show" readonly="1"/>
                    <field name="work_delivery_order"
                           attrs="{'column_invisible': [('parent.move_type', '!=', 'in_invoice')]}" optional="show" readonly="1"/>
                </xpath>
                <xpath expr="//field[@name='product_uom_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='tax_ids']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='price_subtotal']" position="attributes">
                    <attribute name="string">الإجمالي</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']//field[@name='name']" position="attributes">
                    <attribute name="string">أمر السداد</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>