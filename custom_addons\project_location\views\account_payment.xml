<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="account_payment_form_inherit" model="ir.ui.view">
            <field name="name">account_payment_form_inherit</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('oe_chatter')]" position="replace">
                </xpath>
                <xpath expr="//sheet" position="inside">
                    <separator/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" options="{'post_refresh':True}" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </xpath>
                <xpath expr="//field[@name='payment_type']" position="after">
                    <field name="creating_bill_id" invisible="True"/>
                </xpath>
                <xpath expr="//field[@name='payment_type']" position="attributes">
                    <attribute name="attrs">{'readonly': [('creating_bill_id', '!=', False)]}</attribute>
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="attributes">
                    <attribute name="attrs">{
                        'readonly': ['|', ('creating_bill_id', '!=', False), ('state', '!=', 'draft')]}</attribute>
                </xpath>
                <xpath expr="//field[@name='payment_method_line_id']" position="attributes">
                    <attribute name="attrs">{
                        'readonly': ['|', ('creating_bill_id', '!=', False), ('state', '!=', 'draft')]}</attribute>
                </xpath>
                <xpath expr="//group/group/field[@name='partner_id'][2]" position="attributes">
                    <attribute name="attrs">{
                        'readonly': ['|', ('creating_bill_id', '!=', False), ('state', '!=', 'draft')]}</attribute>
                </xpath>
                <xpath expr="//group[@name='group2']/field[@name='partner_bank_id'][2]" position="attributes">
                    <attribute name="attrs">{
                        'readonly': ['|', ('creating_bill_id', '!=', False), ('state', '!=', 'draft')],
                        'invisible': ['|', '|', '|', ('show_partner_bank_account', '=', False),
                                      ('partner_type', '!=', 'supplier'), ('is_internal_transfer', '=', True),
                                      ('payment_type', '=', 'inbound')],
                        'required': [('require_partner_bank_account', '=', True),
                                     ('is_internal_transfer', '=', False)]}</attribute>
                </xpath>
            </field>
        </record>

        <!-- Inherit the existing tree view -->
        <record id="view_account_payment_tree_inherit" model="ir.ui.view">
            <field name="name">account.payment.tree.inherit</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_tree"/>
            <field name="arch" type="xml">
                <!-- Locate the position where you want to insert the new field -->
                <xpath expr="//field[@name='name']" position="before">
                    <!-- Add your new field here -->
                    <field name="bill_ids" widget="many2many_tags"/>
                    <field name="request_ids" widget="many2many_tags"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
