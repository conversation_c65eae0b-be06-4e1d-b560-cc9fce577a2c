<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="ardano_approval_approval_categ_form" model="ir.ui.view">
            <field name="name">ardano_approval_approval_categ_form</field>
            <field name="model">approval.category</field>
            <field name="inherit_id" ref="approvals.approval_category_view_form"/>
            <field name="mode">primary</field>
            <field name="priority">0</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='has_product']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': ['|', ('approval_type', '=', 'payment_order'), ('approval_type', '=', 'purchase')]}
                    </attribute>
                </xpath>
                <xpath expr="//field[@name='has_quantity']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': ['|',('approval_type', '=', 'payment_order'), ('approval_type', '=', 'purchase')]}
                    </attribute>
                </xpath>
                <xpath expr="//field[@name='has_amount']" position="attributes">
                    <attribute name="attrs">
                        {'readonly': [('approval_type', '=', 'payment_order')]}
                    </attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>