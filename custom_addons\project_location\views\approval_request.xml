<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="ardano_approval_approval_request_form" model="ir.ui.view">
            <field name="name">ardano_approval_approval_request_form</field>
            <field name="model">approval.request</field>
            <field name="inherit_id" ref="approvals.approval_request_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('oe_chatter')]" position="replace">
                </xpath>
                <xpath expr="//sheet" position="inside">
                    <separator/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" options="{'post_refresh':True}" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </xpath>
                <div name="button_box" position="inside">
                    <button name="action_view_payments"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-cloud-upload">
                        <field name="vendor_bill_count" widget="statinfo" string="Payments"/>
                    </button>
                </div>
                <xpath expr="//field[@name='request_status']" position="replace">
                    <field name="request_status" widget="statusbar" invisible="True"/>
                    <field name="request_status_custom" widget="statusbar"
                           statusbar_visible="new,payment_order,partial_payment,paid" invisible="False"/>
                </xpath>
                <xpath expr="//button[@name='action_cancel']" position="after">
                    <field name="hold" invisible="1"/>
                    <button name="hold_order" type="object" string="إيقاف" class="oe_highlight"
                            attrs="{'invisible':[('hold','=',True)]}" groups="project_location.group_hold_approval_request"/>
                    <button name="hold_order" type="object" string="تفعيل" class="oe_highlight"
                            attrs="{'invisible':[('hold','=',False)]}" groups="project_location.group_hold_approval_request"/>

                    <button name="create_bills" type="object" string="إنشاء أمر صرف" class="oe_highlight"
                            attrs="{'invisible':['|',('vendor_bill_count','!=',0),('hold','=',True)]}" groups="project_location.group_create_bill_from_approval"/>

                    <button name="confirm" type="object" string="ترحيل" class="oe_highlight"
                            attrs="{'invisible':[('request_status_custom','!=','new')]}"/>
                </xpath>
                <xpath expr="//widget[@name='attach_document']" position="replace">
                </xpath>
                <xpath expr="//widget[@name='attach_document']" position="replace">
                </xpath>
                <xpath expr="//button[@name='action_confirm']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//button[@name='action_withdraw']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='category_id']" position="after">
                    <field name="project_currency_id" invisible="1"/>
                    <field name="payment_status"/>
                    <field name="cancel_reason"
                           attrs="{'invisible':['|',('cancel_reason','=',False),('request_status_custom','!=','cancel')]}"
                           readonly="1"/>
                    <field name="billed"/>
                    <field name="bill_date" force_save="True"/>
                </xpath>
                <xpath expr="//field[@name='category_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="string">رقم امر السداد</attribute>
                    <attribute name="placeholder"></attribute>
                    <attribute name="required">1</attribute>
                </xpath>
                <xpath expr="//field[@name='name']" position="before">
                    <h1 style="color: #ff0000; background-color: #ffff00;"
                        attrs="{'invisible':[('order_status','=',True),('project_status','=',True)]}">
                        <div style="text-align: center;">
                            <field name="warning_message"/>
                        </div>
                    </h1>
                </xpath>
                <xpath expr="//group[@name='request_main']" position="after">
                    <group>
                        <!-- Work Order -->
                        <field name="order_status" invisible="True"/>
                        <field name="project_status" invisible="True"/>
                        <field name="date_confirmed" invisible="True"/>
                        <field name="request_type" widget="radio" options="{'horizontal': true}"/>
                        <field name="work_order_id" attrs="{'invisible': [('request_type', '!=', 'work_order')], 'readonly':[('date_confirmed','!=',False)],'required':[('request_type','=','work_order')]}"
                               domain="[('request_type','=','work_order')]"
                               options="{'no_create':True, 'no_create_edit': True}"/>
                        <field name="delivery_order_id"
                               attrs="{'invisible': [('request_type', '!=', 'delivery_order')],'readonly':[('date_confirmed','!=',False)],'required':[('request_type','=','delivery_order')]}" string="أمر التوريد"
                               domain="[('request_type','=','delivery_order'),('project_id.stage_status','=','progress')]"/>
                        <field name="project_id" readonly="True" force_save="1"/>
                        <field name="contractor_id" attrs="{'readonly':[('date_confirmed','!=',False)]}"/>
<!--                        <field name="test_field" widget="boolean_toggle"/>-->
                        <field name="currency_type"/>
                        <field name="currency_rate"/>
                        <field name="purchase_order_id" domain="[('task_id','=',delivery_order_id)]"
                               attrs="{'invisible': [('request_type', '!=', 'delivery_order')]}"
                               widget="many2many_tags"/>
                    </group>
                </xpath>
                <xpath expr="//notebook" position="inside">
                    <page string="المرفقات">
                        <group>
                            <group>
                                <field name="financial_attachment_bool"/>
                                <field name="financial_attachment"
                                       attrs="{'invisible': [('financial_attachment_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="drawings_bool"/>
                                <field name="drawings_attachment"
                                       attrs="{'invisible': [('drawings_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="amount_table_bool"/>
                                <field name="amount_table_attachment"
                                       attrs="{'invisible': [('amount_table_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="proposal_bool"/>
                                <field name="proposal_attachment"
                                       attrs="{'invisible': [('proposal_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="other_bool"/>
                                <field name="other_attachment" attrs="{'invisible': [('other_bool', '=', False)]}"/>
                            </group>
                        </group>
                    </page>
                    <page string="الخصومات">
                        <field name="discount_ids">
                            <tree editable="bottom">
                                <field name="discount_name"/>
                                <field name="discount_amount"/>
                                <field name="discount_account_id" options='{"no_open": True}'/>
                            </tree>
                        </field>
                        <group>
                            <field name="total_discount" string="إجمالي الخصومات"/>
                        </group>
                    </page>
                    <page string="التوقيعات">
                        <field name="signer_ids">
                            <tree editable="bottom" delete="0" create="0">
                                <field name="type" readonly="1"/>
                                <field name="employee_id"/>
                                <field name="date"/>
                                <field name="note"/>
                            </tree>
                        </field>
                    </page>
                </xpath>
                <xpath expr="//field[@name='product_line_ids']" position="after">
                    <group>
                        <field name="total_pre_discount" string="الإجمالي الأساسي"/>
                        <field name="total_discount" string="إجمالي الخصومات"/>
                        <field name="total_after_discount" string="إجمالي بعد الخصم"/>
                    </group>
                </xpath>
                <xpath expr="//field[@name='product_line_ids']" position="attributes">
                    <attribute name="attrs">{'readonly': [('order_status', '!=', True)]}</attribute>
                </xpath>
                <xpath expr="//field[@name='request_owner_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="ardano_approval_purchase_product_line_view_tree_inherit" model="ir.ui.view">
            <field name="name">ardano_approval.purchase.product.line.view.tree.inherit</field>
            <field name="model">approval.product.line</field>
            <field name="inherit_id" ref="approvals.approval_product_line_view_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name="create">0</attribute>
                </xpath>
                <xpath expr="//field[@name='product_id']" position="attributes">
                    <attribute name="attrs">{}</attribute>
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='description']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', ('parent.work_order_id', '!=', False),
                                                          ('parent.delivery_order_id', '!=', False)]}</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//field[@name='product_uom_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//tree" position="attributes">
                    <attribute name="delete">0</attribute>
                </xpath>
                <xpath expr="//field[@name='quantity']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//field[@name='product_uom_id']" position="after">
                    <field name="discount" readonly="1"/>
                    <field name="addon" readonly="1"/>
                    <field name="subtotal" readonly="True"/>
                    <field name="project_currency_id" invisible="1"/>
                    <field name="main_account" optional="hide" force_save="1"/>
                    <field name="sub_account" optional="hide" force_save="1"/>
                    <field name="detailed_account" optional="hide" force_save="1"/>
                    <field name="analytic_account" optional="hide" force_save="1"/>
                </xpath>
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="work_order_line_id" invisible="1"/>
                    <field name="work_order_id" invisible="1"/>
                    <field name="prev_quantity" optional="show" readonly="1" force_save="1"/>
                    <field name="current_quantity" force_save="1"/>
                    <field name="unit_price" readonly="1" force_save="1"/>
                </xpath>

            </field>
        </record>

    </data>
</odoo>