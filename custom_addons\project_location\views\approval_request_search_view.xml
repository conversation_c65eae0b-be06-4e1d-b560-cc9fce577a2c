<odoo>
    <data>
        <!-- Inherit the existing search view -->
        <record id="view_approval_inherited_search" model="ir.ui.view">
            <field name="name">approvals.approval_search_view_search.inherited</field>
            <field name="model">approval.request</field> <!-- Replace with the actual model name -->
            <field name="inherit_id" ref="approvals.approval_search_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <filter string="مرحل" name="billed_yes" domain="[('billed', '=', 'billed')]"/>
                    <filter string="غير مرحل" name="billed_no" domain="[('billed', '=', 'not_billed')]"/>
                    <filter string="تم الدفع" name="paid" domain="[('payment_status', '=', 'paid')]"/>
                    <filter string="تحت الإجراء" name="processing" domain="[('payment_status', '=', 'processing')]"/>
                    <filter string="دفع جزئي" name="partial" domain="[('payment_status', '=', 'partial')]"/>
                    <filter string="تعذر الدفع" name="failed" domain="[('payment_status', '=', 'failed')]"/>
                    <filter string="الحاله دفع جزئي" name="status_partial" domain="[('request_status_custom', '=', 'partial_payment')]"/>
                    <filter string="الحاله دفع كلي" name="failed" domain="[('request_status_custom', '=', 'paid')]"/>
                    <filter string="متوقف" name="archived" domain="[('active', '=', False)]"/>
                </xpath>
                <xpath expr="//filter[@name='filter_approvals_to_review']" position="after">
                    <group string="Group By">
                        <filter string="Contractor Name" name="group_by_contractor_name"
                                context="{'group_by': 'contractor_id'}"/>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
