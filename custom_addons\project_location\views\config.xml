<odoo>
    <data>

        <record id="project_approval_type_settings" model="ir.ui.view">
            <field name="name">Project Approval Type Settings</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="50"/>
            <field name="inherit_id" ref="project.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@id='project_milestone']" position="after">
                    <div>
                        <label for="approval_type"/>
                        <div class="content-group">
                            <div class="mt16">
                                <field name="approval_type"/>
                            </div>
                        </div>
                        <label for="operation_type"/>
                        <div class="content-group">
                            <div class="mt16">
                                <field name="operation_type"/>
                            </div>
                        </div>
                        <label for="service_product_id"/>
                        <div class="content-group">
                            <div class="mt16">
                                <field name="service_product_id"/>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

    </data>
</odoo>