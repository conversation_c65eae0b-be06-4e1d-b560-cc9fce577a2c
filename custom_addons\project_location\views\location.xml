<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_location_form_view" model="ir.ui.view">
            <field name="name">Project Location From</field>
            <field name="model">project.location</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" placeholder="Name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="location_manager"/>
                                <field name="region_id"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="project_location_tree_view" model="ir.ui.view">
            <field name="name">Project Location Tree View</field>
            <field name="model">project.location</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="location_manager"/>
                    <field name="region_id"/>
                </tree>
            </field>
        </record>

        <record id="project_location_search_view" model="ir.ui.view">
            <field name="name">Project Location Search</field>
            <field name="model">project.location</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Name"/>
                    <field name="location_manager" string="Location Manager"/>
                    <group expand="1" string="Group By">
                        <filter string="Region" name="region_id" domain="[]"
                                context="{'group_by':'region_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="project_location_act_window" model="ir.actions.act_window">
            <field name="name">Project Location Action</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">project.location</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    There is no examples click here to add new Locations.
                </p>
            </field>
        </record>

        <menuitem name="المواقع" id="project_location_menu" parent="project.menu_project_config"
                  action="project_location_act_window" sequence="50"/>

    </data>
</odoo>