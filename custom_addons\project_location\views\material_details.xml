<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>


        <record id="mat_description_general_tree_view" model="ir.ui.view">
            <field name="name">mat_description_tree_view</field>
            <field name="model">task.material</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="task_id"/>
                    <field name="material_description_char"/>
                    <field name="material_description"/>
                    <field name="material_barcode"/>
                    <field name="cost_center_number"/>
                    <field name="material_uom_id"/>
                    <field name="qty"/>
                    <field name="avail_qty"/>
                    <field name="qty_needed"/>
                    <field name="category"/>
                    <field name="project_id"/>
                    <field name="mat_symbol"/>
                    <field name="note"/>
                </tree>
            </field>
        </record>

        <record id="project_material_search" model="ir.ui.view">
            <field name="name">project.material.search</field>
            <field name="model">task.material</field>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <search>
                    <field string="Project" name="project_id" filter_domain="[('project_id','ilike',self)]"/>
                    <field string="رقم مركز التكلفه" name="cost_center_number"
                           filter_domain="[('cost_center_number','ilike',self)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Project" name="project_group" context="{'group_by': 'project_id'}"/>
                        <filter string="Product" name="product_group" context="{'group_by': 'material_description'}"/>
                        <filter string="رقم مركز التكلفه" name="cost_center_number_group"
                                context="{'group_by': 'cost_center_number'}"/>
                    </group>

                </search>
            </field>
        </record>

        <record id="action_view_all_task_material_description" model="ir.actions.act_window">
            <field name="name">تفاصيل المواد</field>
            <field name="res_model">task.material</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="project_material_search"/>
            <field name="view_id" ref="mat_description_general_tree_view"/>
        </record>


        <menuitem name="تفاصيل المواد" id="menu_project_management_material_details"
                  parent="menu_project_management_delivery"
                  action="action_view_all_task_material_description" sequence="1"
                  groups="project_location.group_material_description"/>


    </data>
</odoo>