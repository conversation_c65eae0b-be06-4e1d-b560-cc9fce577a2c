<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_preparation_form_view" model="ir.ui.view">
            <field name="name">Project Preparation From</field>
            <field name="model">project.preparation_type</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" placeholder="Name"/>
                            </h1>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="project_preparation_tree_view" model="ir.ui.view">
            <field name="name">Project Preparation Tree View</field>
            <field name="model">project.preparation_type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="project_preparation_search_view" model="ir.ui.view">
            <field name="name">Project Preparation Search</field>
            <field name="model">project.preparation_type</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Name"/>
                </search>
            </field>
        </record>

        <record id="project_preparation_act_window" model="ir.actions.act_window">
            <field name="name">Project Preparation</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">project.preparation_type</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    There is no examples click here to add new Preparation Types.
                </p>
            </field>
        </record>

        <menuitem name="أنواع التجهيزات" id="project_preparation_menu" parent="project.menu_project_config"
                  action="project_preparation_act_window" sequence="52"/>

    </data>
</odoo>