<odoo>
    <data>

        <record id="project.open_view_project_all_group_stage" model="ir.actions.act_window">
            <field name="view_id" ref="project.view_project"/>
        </record>

        <record model="ir.ui.view" id="ardano_project_project_location_form">
            <field name="name">Ardano Project Location Form</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.edit_project"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='settings']" position="attributes">
                    <attribute name="attrs">{'invisible': [('is_odoo_admin', '=', False)]}</attribute>
                </xpath>
                <xpath expr="//div[hasclass('oe_chatter')]" position="replace">
                </xpath>
                <xpath expr="//notebook" position="after">
                    <separator/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" options="{'post_refresh':True}" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </xpath>
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="placeholder">مثال:مشروع تنفيذ مبنى سكني</attribute>
                </xpath>
                <xpath expr="//field[@name='stage_id']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//field[@name='user_id']" position="replace">
                </xpath>
                <xpath expr="//field[@name='is_template']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath>
                <xpath expr="//sheet/group/group/field[@name='company_id']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath>
                <xpath expr="//button[@data-hotkey='r']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath>
                <xpath expr="//button[@data-hotkey='e']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath>
                <xpath expr="//field[@name='label_tasks']" position="replace">
                    <field name="english_name"/>
                    <field name="project_code" string="رقم مركز التكلفه"/>
                </xpath>
                <xpath expr="//field[@name='privacy_visibility']" position="after">
                    <field name="cost_of_revenue_account"/>
                    <field name="project_stock_location" readonly="1"/>
                    <field name="discount_account"/>
                </xpath>
                <xpath expr="//header" position="inside">
                    <button name="stage_draft" string="مسوده" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','=','draft')]}"
                            groups="project_location.group_Project_stage_draft"/>
                    <button name="stage_draft" string="مسوده" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','!=','closed')]}"
                            groups="project_location.group_project_drafting"/>
                    <button name="stage_progress" string="جاري" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','in',('progress','closed'))]}"
                            groups="project_location.group_Project_stage_progress"/>
                    <button name="stage_paused" string="متوقف" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','in',('paused','closed'))]}"
                            groups="project_location.group_Project_stage_paused"/>
                    <button name="stage_finished" string="منتهي" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','in',('finished','closed'))]}"
                            groups="project_location.group_Project_stage_finished"/>
                    <button name="stage_canceled" string="ملغي" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','in',('canceled','closed'))]}"
                            groups="project_location.group_Project_stage_canceled"/>
                    <button name="stage_closed" string="مقفل" type="object" class="oe_highlight"
                            attrs="{'invisible':[('stage_status','=','closed')]}"
                            groups="project_location.group_Project_stage_closed"/>
                </xpath>
                <xpath expr="//group[field[@name='active']]" position="inside">
                    <field name="stage_status" invisible="1"/>
                    <field name="is_odoo_admin" invisible="1"/>
                    <field name="project_account_fulfilment" invisible="1"/>
                    <field name="project_type"/>
                    <field name="preparation_type" attrs="{'invisible': [('project_type', '!=', 'preparation')]}"/>
                    <field name="contracting_type" attrs="{'invisible': [('project_type', '!=', 'contracting')]}"/>
                    <field name="plan_number"/>
                    <field name="map_number"/>
                    <field name="contract_number"/>
                    <field name="project_currency_id"/>
                    <field name="closing_description" attrs="{'invisible':['|' ,('closing_date','=',False),('stage_status','!=','closed')]}"/>
                    <field name="closing_date"
                           attrs="{'invisible':['|' ,('closing_date','=',False),('stage_status','!=','closed')]}"/>
                    <field name="canceling_description" attrs="{'invisible':[('canceling_description','=',False)]}"/>
                </xpath>
                <xpath expr="//group[field[@name='active']]" position="after">
                    <group name="cost" string="التكاليف">
                        <field name="operational_cost" widget="percentage"/>
                        <field name="preparation_cost" widget="percentage"/>
                        <field name="currency_cost" widget="percentage"/>
                        <field name="tax_cost" widget="percentage"/>
                        <field name="profit_cost" widget="percentage"/>
                        <field name="cost_percentage" widget="percentage"/>
                        <separator/>
                        <field name="weight_calculation" widget="radio" options="{'horizontal': true}"/>
                        <field name="total_weights" invisible="1"/>
                    </group>

                </xpath>
                <xpath expr="//group[field[@name='active']]" position="after">
                    <group>
                        <field name="location_id"/>
                        <field name="location_manager" force_save="1"/>
                        <field name="region_id" force_save="1"/>
                        <field name="region_manager" force_save="1"/>
                        <field name="total_paid" string="إجمالي المدفوعات"/>
                        <field name="total_commitment" string="إجمالي الإلتزامات المتبقيه"/>
                        <field name="total_cost_price" string="إجمالي التكاليف"/>
                        <field name="total_sales_price" string="إجمالي سعر البيع"/>
                        <field name="total_work_order_price" string="إجمالي أوامر العمل"/>
                        <field name="total_approval_request_price" string="إجمالي أوامر السداد"/>
                        <field name="total_discounts" string="إجمالي الخصومات المكتسبه"/>
                        <field name="total_technical_progress" string="إجمالي الإنجاز الفني" widget="percentage"/>
                        <field name="total_financial_progress" string="إجمالي الإنجاز المالي" widget="percentage"/>
                        <field name="has_negative_commitment" invisible="1"/>
                    </group>
                </xpath>
                <xpath expr="//notebook/page[@name='description']" position="before">
                    <page string="المقايسـه التنفيذية">
                        <div>
                            <button name="import_measurement" type="object" string="إدخال من ملف Excel"
                                    class="oe_highlight m-2" />
                            <button name="action_generate_excel" type="object" string="تنزيل ملف ادخال"
                                    class="oe_highlight m-2"/>
                            <button name="export_work" type="object" string="استخراج البيانات"
                                    class="oe_highlight m-2"/>
                        </div>
                        <field name="work_measurement">
                            <tree editable="bottom" decoration-success="financial_progress_over_85 == True">
                                <field name="project_id" invisible="1"/>
                                <field name="project_currency_id" invisible="1"/>
                                <field name="seq"/>
                                <field name="commitment_amount_negative" string="الالتزامات تخطت السعر"/>
                                <field name="work_description" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="description_id" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="qty" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="product_uom_id" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="cost_price" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="total_cost_price" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="price_unit" attrs="{'readonly':[('has_entries','=',True)]}"/>
                                <field name="price_subtotal" />
                                <field name="paid_values"/>
                                <field name="commitment_amount" decoration-danger="commitment_amount_negative == True"/>
                                <field name="payment_order_total" />
                                <field name="work_measurement_weight" widget="percentage"
                                       attrs="{'readonly':[('parent.weight_calculation','!=','manual')]}"/>
                                <field name="financial_progress" widget="percentage"/>
                                <field name="financial_progress_over_85" invisible="1"/>
                                <field name="technical_progress" widget="percentage"/>
                                <field name="notes"/>
                                <field name="has_entries" invisible="1"/>
                                <!--                                <button name="action_create_task" type="object" string="انشاء امر عمل"-->
                                <!--                                        class="oe_highlight"/>-->
                                <!--                                <button name="action_create_task_delivery" type="object" string="انشاء طلب توريد"-->
                                <!--                                        class="oe_highlight"/>-->
                                <field name="task_id" invisible="1"/>

                            </tree>
                        </field>
                    </page>
                    <page string="فريق المشروع">
                        <field name="project_manager_ids">
                            <tree editable="bottom">
                                <field name="manager_id"/>
                                <field name="job_title"/>
                                <field name="job_description"/>
                            </tree>
                        </field>
                    </page>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="ardano_project_project_location_tree">
            <field name="name">Ardano Project Location Tree</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.view_project"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='user_id']" position="replace">
                </xpath>
                <xpath expr="//field[@name='display_name']" position="attributes">
                    <attribute name="string">رقم مركز التكلفه</attribute>
                </xpath>
                <xpath expr="//field[@name='last_update_status']" position="replace">
                </xpath>
                <xpath expr="//field[@name='stage_id']" position="attributes">
                    <attribute name="string">الحاله</attribute>
                </xpath>
                <xpath expr="//field[@name='display_name']" position="after">
                    <field name="name"/>
                </xpath>
<!--                <xpath expr="//field[@name='stage_id']" position="after">-->
<!--                    <field name="total_sales_price" string="إجمالي المقايسه"/>-->
<!--                    <field name="total_work_orders" string="إجمالي أوامر العمل"/>-->
<!--                    <field name="total_payment_requests" string="إجمالي أوامر السداد"/>-->
<!--                    <field name="total_paid" string="إجمالي السداد"/>-->
<!--                    <field name="total_commitment" string="باقي الإلتزامات"/>-->
<!--                </xpath>-->
            </field>
        </record>


        <record id="project.action_view_all_task" model="ir.actions.act_window">
            <field name="name">أوامر العمــل</field>
            <field name="res_model">project.task</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_my_tasks': 1, 'search_default_open_tasks': 1, 'all_task': 0,
                                   'default_user_ids': [(4, uid)], 'default_request_type': 'work_order'}</field>
            <field name="domain">[('request_type', '=', 'work_order')]</field>
            <field name="search_view_id" ref="project.view_task_search_form_extended"/>
            <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree'}),
            (0, 0, {'view_mode': 'form'})]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No tasks found. Let's create one!
                </p>
                <p>
                    Organize your tasks by dispatching them across the pipeline.
                    <br/>
                    Collaborate efficiently by chatting in real-time or via email.
                </p>
            </field>
        </record>
        <record id="action_view_all_task_delivery_order" model="ir.actions.act_window">
            <field name="name">طلب توريد</field>
            <field name="res_model">project.task</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_my_tasks': 1, 'search_default_open_tasks': 1, 'all_task': 0,
                                   'default_user_ids': [(4, uid)], 'default_request_type': 'delivery_order'}</field>
            <field name="domain">[('request_type', '=', 'delivery_order')]</field>
            <field name="search_view_id" ref="project.view_task_search_form_extended"/>
            <field name="view_id" ref="project.view_task_tree2"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No tasks found. Let's create one!
                </p>
                <p>
                    Organize your tasks by dispatching them across the pipeline.
                    <br/>
                    Collaborate efficiently by chatting in real-time or via email.
                </p>
            </field>
        </record>

        <record id="view_project_search_form_extended" model="ir.ui.view">
            <field name="name">view_project_search_form_extended</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.view_project_project_filter"></field>
            <field name="arch" type="xml">
                <!-- Add new search field -->
                <xpath expr="//filter[@name='own_projects']" position="before">
                    <filter string="المقايسه التي تحتوي التزامات ساليه" name="has_negative_commitment" domain="[('has_negative_commitment', '=', True)]"/>
                </xpath>
                <xpath expr="//field[@name='name']" position="before">
                    <field string="رقم مركز التكلفه" name="project_code" filter_domain="[('project_code', 'ilike', self)]"/>
                </xpath>
                <xpath expr="//field[@name='analytic_account_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='stage_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_project_inherited_search_form_extended" model="ir.ui.view">
            <field name="name">view_project_search_form_extended_inherited</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="sale_project.project_project_view_inherit_project_filter"></field>
            <field name="arch" type="xml">
                <!-- Add new search field -->
                <xpath expr="//field[@name='sale_order_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="approvals.approval_request_action_all" model="ir.actions.act_window">
            <field name="name">All Approvals</field>
            <field name="res_model">approval.request</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No Approvals
                </p>
                <p>
                    Let's go to the <a type="action" class="text-primary"
                                       name="%(approvals.approval_category_action_new_request)d">new request
                </a> menu
                </p>
            </field>
        </record>


        <menuitem name="أوامر العمــل" id="project.menu_project_management" parent="project.menu_main_pm"
                  action="project.action_view_all_task" sequence="2"/>

        <menuitem name="طلبات توريد" id="menu_project_management_delivery" parent="project.menu_main_pm" sequence="3"/>
        <menuitem name="طلبات توريد" id="menu_project_management_delivery_sub" parent="menu_project_management_delivery"
                  action="action_view_all_task_delivery_order" sequence="0"/>
        <menuitem name="أوامر السداد" id="menu_payment_request_all" parent="project.menu_main_pm" sequence="4" action="approvals.approval_request_action_all"/>


        <record id="approval_request_action_all_accounting" model="ir.actions.act_window">
            <field name="name">All Approvals</field>
            <field name="res_model">approval.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('request_status_custom', '=', 'pending')]</field>
            <field name="context">{'create':False}</field>
        </record>


        <menuitem name="أوامر السداد" id="menu_payment_request_all_account" parent="account.menu_finance_payables" sequence="4" action="approval_request_action_all_accounting"/>


    </data>
</odoo>