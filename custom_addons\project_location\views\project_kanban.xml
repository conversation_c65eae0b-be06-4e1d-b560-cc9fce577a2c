<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="project.act_project_project_2_project_task_all" model="ir.actions.act_window">
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph,activity</field>
            <field name="domain">[('display_project_id', '=', active_id), ('request_type', '=', 'work_order')]</field>
            <field name="context">{
                'default_project_id': active_id,
                'show_project_update': True,
                'default_request_type': 'work_order',
            }</field>
        </record>

        <record id="act_project_project_2_project_task_delivery_order" model="ir.actions.act_window">
            <field name="name">Tasks</field>
            <field name="res_model">project.task</field>
            <field name="view_mode">kanban,tree,form,calendar,pivot,graph,activity</field>
            <field name="domain">[('display_project_id', '=', active_id),
                                  ('request_type', '=', 'delivery_order')]</field>
            <field name="context">{
                'default_project_id': active_id,
                'show_project_update': True,
                'default_request_type': 'delivery_order',
            }</field>
            <field name="search_view_id" ref="project.view_task_search_form"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No tasks found. Let's create one!
                </p>
                <p>
                    Keep track of the progress of your tasks from creation to completion.
                    <br/>
                    Collaborate efficiently by chatting in real-time or via email.
                </p>
            </field>
        </record>


        <!-- Inherit the Kanban view -->
        <record id="project_location.inherited_kanban_view" model="ir.ui.view">
            <field name="name">project.project.custom.kanban</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.view_project_kanban"/>
            <field name="arch" type="xml">
                <kanban position="attributes">
                    <attribute name="records_draggable">False</attribute>
                </kanban>
                <xpath expr="//field[@name='tag_ids']" position="after">
                    <field name="delivery_order_count"/>
                </xpath>
                <xpath expr="//div[hasclass('o_project_kanban_boxes')]" position="inside">
                    <!-- Add your new div here. You can replicate the structure of label_tasks or customize it. -->
                    <a class="o_project_kanban_box" name="action_view_delivery_order" type="object">
                        <div>
                            <span class="o_value">
                                <t t-esc="record.delivery_order_count.value"/>
                            </span>
                            <span class="o_label ms-1">
                                طلب التوريد
                            </span>
                        </div>
                    </a>
                </xpath>
            </field>
        </record>
        <!-- Inherit the Kanban view -->
        <record model="ir.ui.view" id="project_kanban_inherit">
            <field name="name">project.project.kanban.inherit</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.view_project_kanban"/>
            <field name="arch" type="xml">
                <xpath expr="//div" position="inside">
                    <button name="action_edit_project_custom" type="object"
                            class="oe_kanban_action oe_kanban_action_button">
                        <i class="fa fa-pencil"/>
                        Edit
                    </button>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="task_kanban_inherit">
            <field name="name">project.task.kanban.inherit</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_kanban_inherit_my_task"/>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="default_group_by">stage_id</attribute>
                </xpath>

            </field>
        </record>
        <record model="ir.ui.view" id="task_kanban_base_inherit">
            <field name="name">task_kanban_base_inherit</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_kanban"/>
            <field name="arch" type="xml">
                <xpath expr="//kanban" position="attributes">
                    <attribute name="records_draggable">False</attribute>
                </xpath>

            </field>
        </record>

        <record id="project.open_create_project" model="ir.actions.act_window">
            <field name="name">Create a Custom Project</field>
            <field name="view_id" ref="project.edit_project"/>
            <field name="target">current</field>
        </record>


    </data>
</odoo>