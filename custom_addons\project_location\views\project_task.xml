<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_task_form_inherit" model="ir.ui.view">
            <field name="name">inherited.project_task.form</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_form2"/>
            <field name="arch" type="xml">

                <xpath expr="//div[hasclass('oe_chatter')]" position="replace">
                </xpath>
                <xpath expr="//notebook" position="after">
                    <separator/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" options="{'post_refresh':True}" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </xpath>


                <xpath expr="//field[@name='name']" position="replace">
                    <div>
                        <field name="work_order_number" class="o_task_name w-100 w-md-75 pe-2"
                               placeholder="رقم أمر العمل"
                               attrs="{'invisible': [('request_type', '!=', 'work_order')]
                               ,'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                        <field name="delivery_order_number" class="o_task_name w-100 w-md-75 pe-2"
                               placeholder="رقم أمر التوريد"
                               attrs="{'invisible': [('request_type', '!=', 'delivery_order')]
                               ,'required':[('request_type','=','delivery_order')]}" readonly="1"/>
                        <br/>
                        <h1 style="color: #ff0000; background-color: #ffff00;" class="o_row"
                            attrs="{'invisible':['|',('contractor_status','=','operational'),('request_type','!=','work_order')]}">
                            <div style="text-align: center;">
                                حالة المتعهد متوقفه
                            </div>
                        </h1>
                    </div>

                </xpath>
                <xpath expr="//field[@name='stage_id']" position="attributes">
                    <attribute name="domain">[('stage_type', '=', request_type),
                                              ('project_ids', '=', project_id)]</attribute>
                    <attribute name="attrs">{'readonly':True}</attribute>
                </xpath>
                <xpath expr="//field[@name='project_id']" position="attributes">
<!--                    <attribute name="domain">[('stage_status', '=', 'progress'),-->
<!--                                              ('project_account_fulfilment', '=', True)]</attribute>-->
                    <attribute name="attrs">{'readonly': [('approval_status_editable', '=', True),
                                                          ('approval_status_editable_override_user', '!=',
                                                           True)]}</attribute>
                    <attribute name="options">{'no_create': True, 'no_create_edit': True}</attribute>

                </xpath>
                <xpath expr="//field[@name='project_id']" position="after">
                    <field name="name" invisible="1"/>
                    <field name="cancel_reason" attrs="{'invisible': [('cancel_reason', '=', False)]}" readonly="1"/>
                    <field name="project_area"/>
                    <field name="project_currency_id" invisible="1"/>
                    <field name="project_location"/>
                    <field name="project_type"/>
                    <field name="preparation_type" attrs="{'invisible': [('project_type', '!=', 'preparation')]}"/>
                    <field name="contracting_type" attrs="{'invisible': [('project_type', '!=', 'contracting')]}"/>
                    <field name="request_type" invisible="1"/>
                    <field name="bill_number"/>
                    <field name="operational_status" widget="radio" options="{'horizontal': true}"
                           attrs="{'invisible': [('request_type', '!=', 'work_order')],
                           'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                    <field name="approval_count" string="عدد الدفعات"/>
                    <field name="confirmed_date" groups="base.group_no_one" readonly="1"/>
                    <field name="date" attrs="{'invisible': [('request_type', '!=', 'work_order')],
                    'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                </xpath>
                <xpath expr="//field[@name='date_deadline']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath>

                <xpath expr="//field[@name='tag_ids']" position="after">
                    <field name="work_duration" attrs="{'invisible': [('request_type', '!=', 'work_order')],
                    'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                    <field name="task_end_date" attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>
                </xpath>
                <!--                <xpath expr="//group[field[@name='tag_ids']]" position="after">-->
                <!--                    <group>-->
                <!--                        <field name="main_account" force_save="1"-->
                <!--                               attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>-->
                <!--                        <field name="sub_account" force_save="1"-->
                <!--                               attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>-->
                <!--                        <field name="detailed_account" force_save="1"-->
                <!--                               attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>-->
                <!--                        <field name="analytic_account" force_save="1"-->
                <!--                               attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>-->
                <!--                    </group>-->
                <!--                </xpath>-->

                <xpath expr="//field[@name='partner_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='user_ids']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='tag_ids']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//page[@name='extra_info']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="contractor_status" invisible="1"/>
                    <field name="contractor_id"
                           attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)],'invisible':[('request_type','!=','work_order')],'required':[('request_type','=','work_order')]}"
                           context="{'default_supplier_rank':1}"/>
                    <field name="currency_type"/>
                    <field name="currency_rate"/>
                    <field name="vat" attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>
                    <field name="cost_center_number" attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>
                    <field name="cost_center_category" readonly="1" force_save="1"
                           attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>
                    <field name="cost_center_group" readonly="1" force_save="1"
                           attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>
                    <field name="cost_center_analytic" attrs="{'invisible': [('request_type', '!=', 'work_order')]}"/>
                </xpath>
                <xpath expr="//field[@name='project_id']" position="after">
                    <field name="approval_status_editable" invisible="1"/>
                    <field name="approval_status_editable_override_user" invisible="1"/>
                </xpath>
                <xpath expr="//page[@name='description_page']" position="before">
                    <page string="البنود" attrs="{'invisible': [('request_type', '!=', 'work_order')]}">
                        <field name="total_payments_points_match" invisible="1"/>
<!--                        <div>-->
<!--                            <h1 style="color: #ff0000; background-color: #ffff00;" class="o_row"-->
<!--                                attrs="{'invisible':[('total_payments_points_match','=',True)]}">-->
<!--                                <div style="text-align: center;">-->
<!--                                    إجمالي شروط الدفع لا تطابق إجمالي البنود-->
<!--                                </div>-->
<!--                            </h1>-->
<!--                        </div>-->
                        <field name="point_description_ids" invisible="1"/>
                        <field name="point_ids"
                               context="{'default_cost_center_category': cost_center_category,'default_cost_center_group':cost_center_group,'default_cost_center_analytic':cost_center_analytic}"
                               attrs="{'readonly': ['|', '|', ('approval_status_editable','=',True), ('approval_status_editable_override_user','!=', True), ('stage_status','=','confirm')]}">
                            <tree editable="bottom">
                                <field name="point_number" style="width: 90%;"/>
                                <field name="created_from_project" invisible="1"/>
                                <field name="project_currency_id" invisible="1"/>
                                <field name="point_description"/>
                                <field name="project_points_descriptions" invisible="1"/>
                                <field name="project_work_descriptions" invisible="1"/>
                                <field name="description_id" domain="[('id', 'in', project_points_descriptions)]"/>
                                <field name="categorization" domain="[('id', 'in', project_work_descriptions)]" readonly="1" force_save="1"/>
                                <field name="cost_center_number"/>
                                <field name="cost_center_category" readonly="1" force_save="1" optional="hide"/>
                                <field name="cost_center_group" readonly="1" force_save="1" optional="hide"/>
                                <field name="cost_center_analytic" optional="hide"/>
                                <field name="uom"/>
                                <field name="quantity_match" invisible="1"/>
                                <field name="quantity"/>
                                <field name="unit_price"/>
                                <field name="total"/>
                                <field name="paid_values"/>
                                <field name="commitment_amount"/>
                                <field name="total_in_arabic_words" invisible="1"/>
                                <field name="work_measurement_id" invisible="True"/>
                                <field name="notes" invisible="1"/>
                                <field name="payment_order_total"/>
                                <button name="check_point_duplication" type="object" string="Details"
                                        class="oe_highlight"/>
                            </tree>
                        </field>
                        <group>
                            <field name="total_payment_request"/>
                            <field name="total_payment"/>
                            <field name="total_commitment"/>
                            <field name="total_points"/>
                            <field name="total_in_arabic_words"/>
                        </group>
                    </page>
                    <page string="شروط الدفع" attrs="{'invisible': [('request_type', '!=', 'work_order')]}">
                        <field name="payment_ids"
                               attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}">
                            <tree editable="bottom">

                                <field name="project_currency_id" invisible="1"/>
                                <field name="payment_number" width="20%"/>
                                <field name="payment" width="20%"/>
                                <field name="input_val" width="20%" attrs="{'readonly':[('payment','!=','percentage')]}"
                                       widget="percentage" force_save="1"/>
                                <field name="output_val" width="20%" attrs="{'readonly':[('payment','!=','amount')]}"
                                       force_save="1"/>
                                <field name="payment_term"/>
                                <field name="payment_type"/>
                                <field name="notes"/>
                            </tree>
                        </field>
                        <group>
                            <field name="total_payments" string="إحمالي شروط الدفع"/>
                        </group>
                    </page>
                    <page string="المرفقات" attrs="{'invisible': [('request_type', '!=', 'work_order')],}">
                        <group>
                            <group>
                                <field name="financial_attachment_bool"
                                       attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                                <field name="financial_attachment"
                                       attrs="{'invisible': [('financial_attachment_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="drawings_bool"
                                       attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                                <field name="drawings_attachment"
                                       attrs="{'invisible': [('drawings_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="amount_table_bool"
                                       attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                                <field name="amount_table_attachment"
                                       attrs="{'invisible': [('amount_table_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="proposal_bool"
                                       attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                                <field name="proposal_attachment"
                                       attrs="{'invisible': [('proposal_bool', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="other_bool"
                                       attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}"/>
                                <field name="other_attachment" attrs="{'invisible': [('other_bool', '=', False)]}"/>
                            </group>
                        </group>
                    </page>
                    <page string="التوقيعات" attrs="{'invisible': [('request_type', '!=', 'work_order')]}">
                        <field name="signer_ids"
                               attrs="{'readonly':[('approval_status_editable','=',True),('approval_status_editable_override_user','!=', True)]}">
                            <tree editable="bottom" delete="0" create="0">
                                <field name="type" readonly="True"/>
                                <field name="employee_id"/>
                                <field name="date"/>
                                <field name="note"/>
                            </tree>
                        </field>
                    </page>
                    <page string="الملاحظات" attrs="{'invisible': [('request_type', '!=', 'work_order')]}">
                        <group>

                        </group>
                    </page>
                </xpath>
            </field>
        </record>

        <record id="sale_project.view_sale_project_inherit_form" model="ir.ui.view">
            <field name="active">False</field>
        </record>

        <record id="work_measurement_description_form" model="ir.ui.view">
            <field name="name">work.description.measurement.view.form</field>
            <field name="model">project.work.description</field>
            <field name="arch" type="xml">
                <form string="بيان الاعمال">
                    <sheet>
                        <group>
                            <field name="name" string="عنوان بيان الاعمال"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record id="work_measurement_description_tree_view" model="ir.ui.view">
            <field name="name">measurement.description.tree.view</field>
            <field name="model">project.work.description</field>
            <field name="arch" type="xml">
                <tree string="بيان الاعمال">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="project_work_measurement_act_window" model="ir.actions.act_window">
            <field name="name">بيان الاعمال</field>
            <field name="res_model">project.work.description</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    There is no examples click here to add new بيان الاعمال
                </p>
            </field>
        </record>

        <menuitem name="بيان الاعمال" id="project_description_menu" parent="project.menu_project_config"
                  action="project_work_measurement_act_window" sequence="55"/>

    </data>
</odoo>