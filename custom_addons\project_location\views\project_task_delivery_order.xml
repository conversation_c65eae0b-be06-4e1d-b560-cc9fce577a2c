<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_task_form_inherit_delivery_order" model="ir.ui.view">
            <field name="name">inherited.project_task_delivery_order.form</field>
            <field name="model">project.task</field>
            <field name="inherit_id" ref="project.view_task_form2"/>
            <field name="arch" type="xml">
                <div name="button_box" position="inside">
                    <button name="view_task_purchases"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-cloud-upload" attrs="{'invisible':[('request_type','!=','delivery_order')]}">
                        <field name="purchase_count" widget="statinfo" string="Purchases"/>
                    </button>
                    <button name="view_task_transfers"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-cloud-upload" attrs="{'invisible':[('request_type','!=','delivery_order')]}">
                        <field name="stock_transfers_count" widget="statinfo" string="Transfers"/>
                    </button>
                </div>

                <xpath expr="//group[field[@name='project_id']]" position="after">
                    <group attrs="{'invisible': [('request_type', '!=', 'delivery_order')]}">
                        <!--                        <field name="directed_to"/>-->
                        <field name="valid_material_ids" widget="many2many_tags" invisible="1"/>
                    </group>
                </xpath>
                <xpath expr="//page[@name='description_page']" position="before">
                    <page attrs="{'invisible': [('request_type', '!=', 'delivery_order')]}" string="المواد">
                        <field name="material_ids" context="{'default_project_id': project_id}">
                            <tree editable="bottom">
                                <field name="seq_number"/>
                                <field name="material_description_char"/>
                                <field name="material_description" options="{'no_create': True}"/>
                                <field name="material_uom_id"/>
                                <field name="qty"/>
                                <field name="avail_qty"/>
                                <field name="qty_needed"/>
                                <field name="category"/>
<!--                                <field name="project_id"/>-->
<!--                                <field name="mat_symbol"/>-->
                                <field name="note"/>
                            </tree>
                        </field>
<!--                        <button name="create_transfer_and_purchase_order" type="object"-->
<!--                                string="إنشاء أوامر شراء وطلبات صرف" class="oe_highlight"/>-->
                    </page>
                    <page attrs="{'invisible': [('request_type', '!=', 'delivery_order')]}" string="أوامر الشراء">
                        <field name="buy_return" widget="radio" options="{'horizontal': true}"/>
                        <field name="mat_purchase_ids">
                            <tree editable="bottom">
                                <field name="seq_number"/>
                                <field name="material_description" domain="[('id', 'in',parent.valid_material_ids )]"
                                       options="{'no_create': True}"/>
                                <field name="category"/>
                                <field name="qty"/>
                                <field name="purchase_order_ids" widget="many2many_tags" readonly="True"/>
                            </tree>
                        </field>
                        <button type="object" name="open_mat_purchase_wizard" string="إنشاء أمر شراء"
                                class="oe_highlight"/>
                    </page>
                    <page attrs="{'invisible': [('request_type', '!=', 'delivery_order')]}" string="المخازن">
                        <field name="add_give" widget="radio" options="{'horizontal': true}"/>
                        <field name="mat_ops_ids">
                            <tree editable="bottom">
                                <field name="seq_number"/>
                                <field name="material_description" domain="[('id', 'in',parent.valid_material_ids )]"
                                       options="{'no_create': True}"/>
                                <field name="qty"/>
                                <field name="location_id" domain="[('usage','in',('internal','inventory'))]"/>
                                <field name="stock_picking_ids" widget="many2many_tags" readonly="True"/>
<!--                                <button name="create_transfer" type="object" string="إنشاء طلب صرف / إضافه"-->
<!--                                        class="oe_highlight"/>-->
                            </tree>
                        </field>
                        <button name="create_transfer" type="object" string="إنشاء طلب صرف / إضافه"
                                class="oe_highlight"/>

                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>