<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_task_type_tree_inherited_ardano" model="ir.ui.view">
            <field name="name">project_task_tree_ardano_inherit</field>
            <field name="model">project.task.type</field>
            <field name="inherit_id" ref="project.task_type_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="stage_type"/>
                </xpath>
            </field>
        </record>
        <record id="project_task_type_from_inherited_ardano" model="ir.ui.view">
            <field name="name">project_task_tree_ardano_inherit</field>
            <field name="model">project.task.type</field>
            <field name="inherit_id" ref="project.task_type_edit"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="stage_type"/>
                    <field name="task_type_work_order" attrs="{'invisible':[('stage_type','!=','work_order')]}"/>
                    <field name="task_type_delivery_order" attrs="{'invisible':[('stage_type','!=','delivery_order')]}"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>