<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_region_form_view" model="ir.ui.view">
            <field name="name">Project Region From</field>
            <field name="model">project.region</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <field name="active" invisible="1"/>
                        <button name="toggle_active" type="object" class="oe_highlight" string="إيقاف"
                                attrs="{'invisible': [('active', '!=', True)]}"
                                groups="project_location.group_hold_location"/>
                        <button name="toggle_active" type="object" class="oe_highlight" string="تفعيل"
                                attrs="{'invisible': [('active', '!=', False)]}"
                                groups="project_location.group_hold_location"/>

                    </header>
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" placeholder="Name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="region_manager"/>
                                <field name="location_ids" widget="many2many_tags"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="project_region_tree_view" model="ir.ui.view">
            <field name="name">Project region Tree View</field>
            <field name="model">project.region</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="region_manager"/>
                    <field name="location_ids" widget="many2many_tags"/>
                </tree>
            </field>
        </record>

        <record id="project_region_search_view" model="ir.ui.view">
            <field name="name">Project region Search</field>
            <field name="model">project.region</field>
            <field name="arch" type="xml">
                <search>
                    <filter string="ملغي" name="archived" domain="[('active', '=', False)]"/>
                    <field name="name" string="Name"/>
                    <field name="region_manager" string="Region Manager"/>
                </search>
            </field>
        </record>

        <record id="project_region_act_window" model="ir.actions.act_window">
            <field name="name">Project region Action</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">project.region</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    There is no examples click here to add new regions.
                </p>
            </field>
        </record>

        <menuitem name="المناطق" id="project_region_menu" parent="project.menu_project_config"
                  action="project_region_act_window" sequence="51"/>

    </data>
</odoo>