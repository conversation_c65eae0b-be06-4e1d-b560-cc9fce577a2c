<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_partner_inherit" model="ir.ui.view">
            <field name="name">res_partner_inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='vat']" position="after">
                    <field name="supplier_rank_edit" widget="boolean_toggle"/>
                    <field name="supplier_rank" invisible="1"/>
                    <field name="contractor_state"
                           attrs="{'invisible': [('supplier_rank', '=', 0)]}"/>
                </xpath>
                <xpath expr="//div" position="before">
                    <header>
                        <field name="active" invisible="1"/>
                        <button name="toggle_active" type="object" class="oe_highlight" string="إيقاف" attrs="{'invisible': [('active', '!=', True)]}" groups="project_location.group_hold_partner"/>
                        <button name="toggle_active" type="object" class="oe_highlight" string="تفعيل" attrs="{'invisible': [('active', '!=', False)]}" groups="project_location.group_hold_partner"/>
                    </header>
                </xpath>
            </field>
        </record>
    </data>
</odoo>