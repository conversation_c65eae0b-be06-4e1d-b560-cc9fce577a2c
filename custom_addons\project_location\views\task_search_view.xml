<odoo>
    <data>
        <!-- Inherit the existing search view -->
        <record id="project.view_task_search_form" model="ir.ui.view">
            <field name="name">project.task.search.form.inherit</field>
            <field name="model">project.task</field>
            <field name="arch" type="xml">
                <search>
                    <filter name="tasks_due_today" invisible="1"/>
                    <field name="partner_id" invisible="1"/>
                    <field name="point_description_ids" string="وصف البند"/>
                    <field name="work_order_number" string="رقم أمر العمل"/>
                    <field name="delivery_order_number" string="كود أمر التوريد"/>
                    <group expand="0" string="Group By">
                        <filter name="customer" invisible="1"/>
                        <filter name="customer" invisible="1"/>

                        <filter string="Project" name="project_group" context="{'group_by': 'project_id'}"/>
                        <filter string="المتعهد" name="contractor_group" context="{'group_by': 'contractor_id'}"/>
                        <filter string="المرحله" name="stage_type_ids" context="{'group_by': 'stage_id'}"/>

                    </group>
                </search>
            </field>
        </record>
        <!--  <record id="project_enterprise.view_task_search_form_inherit_enterprise" model="ir.ui.view">
             <field name="name">project_enterprise.view_task_search_form_inherit_enterprise</field>
             <field name="model">project.task</field>
            <field name="arch" type="xml">
                <search>
        
                </search>
            </field>
        </record> -->
        <record id="project.view_task_search_form_extended" model="ir.ui.view">
            <field name="name">project.view_task_search_form_extended</field>
            <field name="model">project.task</field>
            <field name="arch" type="xml">
                <search>

                </search>
            </field>
        </record>
        <record id="sale_project.project_task_view_search" model="ir.ui.view">
            <field name="name">sale_project.project_task_view_search</field>
            <field name="model">project.task</field>
            <field name="arch" type="xml">
                <search>

                </search>
            </field>
        </record>
    </data>
</odoo>
