from odoo import fields, models, api
from odoo.exceptions import ValidationError
from datetime import datetime
from icecream import ic

class Approval(models.TransientModel):
    _name = 'approval.bill'

    approval_ids = fields.Many2many(comodel_name='approval.request', )
    currency_match = fields.Boolean(compute='run_validations')
    type_match = fields.Boolean(compute='run_validations')
    vendor_match = fields.Boolean(compute='run_validations')
    missing_match = fields.Char(compute='_compute_missing_match')
    approved_match = fields.<PERSON>olean(compute='validate_approvals')
    has_bill = fields.Boolean(compute='validate_bills')


    @api.depends('approval_ids')
    def validate_bills(self):
        bills = self.env['account.move'].search([('approval_request_ids', 'in', self.approval_ids.ids)])
        ic(bills)
        self.has_bill = True if bills else False

    @api.depends('currency_match', 'type_match', 'vendor_match')
    def _compute_missing_match(self):
        self.missing_match = f"برجاء مراجعه تطابق البيانات بين{'أنواع الأوامر' if not self.type_match else ''} {'تطابق العملات' if not self.currency_match else ''} {'تطابق الموردين' if not self.vendor_match else ''} {'حاله الأوامر' if not self.approved_match else ''}"

    @api.depends('currency_match', 'type_match', 'vendor_match')
    def validate_approvals(self):
        self.approved_match = all(request.request_status_custom not in ('cancel','new','refused') for request in self.approval_ids)

    @api.depends('approval_ids')
    def run_validations(self):
        first_currency_id = self.approval_ids[0].project_currency_id.id
        first_request_type = self.approval_ids[0].request_type
        first_contractor_id = self.approval_ids[0].contractor_id
        for approval in self.approval_ids:
            self.currency_match = False if approval.project_currency_id.id != first_currency_id else True
            self.type_match = False if approval.request_type != first_request_type else True
            self.vendor_match = False if approval.contractor_id != first_contractor_id else True
        return

    def create_bills(self):

        if self.approval_ids:
            currency_id = self.approval_ids[0].project_currency_id.id
            if not currency_id:
                self.approval_ids[0]._compute_project_currency_id()
                currency_id = self.approval_ids[0].project_currency_id.id
            bill_obj = self.env['account.move']
            vals = {
                'partner_id': self.env.context.get('vendor_id') or None,
                'currency_id': currency_id ,
                'move_type': 'in_invoice',
                'created_from_payment_request': True,
            }
            bill = bill_obj.create(vals)
            for request in self.approval_ids:
                bill.update({'approval_request_ids': [(4, request.id)]})
                request_account = request.work_order_id.project_id.cost_of_revenue_account.id if request.request_type == 'work_order' \
                    else request.delivery_order_id.project_id.cost_of_revenue_account.id
                analytic_account = request.work_order_id.project_id.analytic_account_id.id if request.request_type == 'work_order' \
                    else request.delivery_order_id.project_id.analytic_account_id.id
                vals = {
                    'name': request.name,
                    'approval_request_id': request.id,
                    'work_delivery_order': request.work_order_id.id if request.request_type == 'work_order' else request.delivery_order_id.id,
                    'project_location_id': request.work_order_id.project_id.location_id.id if request.request_type == 'work_order'
                    else request.delivery_order_id.project_id.location_id.id,
                    'account_id': request_account or None,
                    'analytic_distribution': {analytic_account: 100} or None,
                    'quantity': 1,
                    'price_unit': request.total_after_discount,
                    # 'product_uom_id': line.product_uom_id.id,
                }

                bill.update({'invoice_line_ids': [(0, 0, vals)]})
                # if not request.bill_date:
                #     request.bill_date = datetime.today()
                # return {
            #     'name': 'Created Bills',
            #     'res_model': 'account.move',
            #     'view_mode': 'form',
            #     'type': 'ir.actions.act_window',
            #     'target': 'current',
            #     'res_id': bill.id,
            # }

# for line in request.product_line_ids:
#     vals = {
#         'name': line.approval_request_id.name,
#         'approval_request_line_id': line.id,
#         'account_id': request_account or None,
#         'analytic_distribution': {analytic_account: 100} or None,
#         # 'quantity': line.current_quantity,
#         'quantity': 1,
#         'price_unit': line.unit_price,
#         'product_uom_id': line.product_uom_id.id,
#     }
#     if line.work_order_line_id.cost_center_analytic and request.work_order_id.analytic_account_id:
#         vals['account_id'] = line.work_order_line_id.cost_center_analytic.id
#         vals['analytic_distribution'] = {request.work_order_id.analytic_account_id.id: 100}
#     order_line_values.append(vals)
# for discount_line in request.discount_ids:
#     vals = {
#         'name': discount_line.discount_name,
#         'quantity': 1,
#         'price_unit': -discount_line.discount_amount,
#         'account_id': discount_line.discount_account_id.id,
#     }
#     order_line_values.append(vals)
# for line in order_line_values:
