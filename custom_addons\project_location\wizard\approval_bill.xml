<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="approval_bill_form_view" model="ir.ui.view">
            <field name="name">approval_bill_form_view</field>
            <field name="model">approval.bill</field>
            <field name="arch" type="xml">
                <form>
                    <div>
                        <h1>
                            <field name="missing_match"
                                   attrs="{'invisible':[('vendor_match','=',True),('type_match','=',True),('currency_match','=',True),('approved_match','=',True)]}"/>
                            <div attrs="{'invisible':[('has_bill','=',False)]}">
                                برجاء التأكد أن أوامر الصرف لا تحتوي بالفعل على فاتوره مورد
                            </div>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="approval_ids" widget="many2many_tags"/>
                            <field name="currency_match" invisible="True"/>
                            <field name="type_match" invisible="True"/>
                            <field name="vendor_match" invisible="True"/>
                            <field name="approved_match" invisible="True"/>
                            <field name="has_bill" invisible="True"/>
                        </group>
                    </group>
                    <footer>
                        <button string="ترحيل" name="create_bills" type="object" default_focus="1"
                                class="btn-primary"
                                attrs="{'invisible':['|','|','|','|',('vendor_match','=',False),('type_match','=',False),('currency_match','=',False),('approved_match','=',False),('has_bill','=',True)]}"/>
                        <button string="Cancel" class="btn-default" special="cancel"/>
                    </footer>

                </form>
            </field>
        </record>


    </data>
</odoo>