from odoo import models, fields, api
from datetime import date


class ApprovalRequestCancel(models.TransientModel):
    _name = 'approval_request.cancel'

    approval_request_id = fields.Many2one('approval.request')
    reason = fields.Text(string='سبب الإلغاء', required=True)

    def cancel(self):
        self.ensure_one()
        self.approval_request_id.cancel_reason = self.reason
        return self.approval_request_id.action_cancel()
