from odoo import models, fields, api
from datetime import date


class ProjectClosingWizard(models.TransientModel):
    _name = 'task.cancel_work_order.wizard'

    task_id = fields.Many2one('project.task')
    reason = fields.Text(string='سبب الإلغاء', required=True)

    def cancel(self):
        self.ensure_one()
        self.task_id.cancel_reason = self.reason
        cancel_stage = self.env['project.task.type'].search(
            [('stage_type', '=', 'work_order'), ('task_type_work_order', '=', 'cancel'),
             ('project_ids', 'in', self.task_id.project_id.id)], limit=1)
        self.task_id.stage_id = cancel_stage.id
