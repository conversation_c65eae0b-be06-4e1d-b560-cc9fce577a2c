from odoo import models, fields, api


class ProjectCancelingWizard(models.TransientModel):
    _name = 'project.canceling_wizard'

    project_ids = fields.Many2many('project.project')
    reason = fields.Text(string='سبب الإلغاء', required=True)

    def close(self):
        for project in self.project_ids:
            project.canceling_description = self.reason
            project.stage_id = project.stage_id.search([('status', '=', 'canceled')], limit=1).id
