from odoo import models, fields, api


class CheckDuplicationWizard(models.TransientModel):
    _name = 'check.duplication_wizard'

    task_point_ids = fields.Many2many(comodel_name='check.duplication_wizard_lines')


class CheckDuplicationWizardLines(models.TransientModel):
    _name = 'check.duplication_wizard_lines'

    task_point_id = fields.Many2one(comodel_name='task.point')
    task_id = fields.Many2one(comodel_name='project.task', string='رقم أمر العمل', related='task_point_id.task_id')
    contractor_id = fields.Many2one(comodel_name='res.partner', string='اسم المتعهد',
                                    related='task_point_id.task_id.contractor_id')
    cost_center_number = fields.Char(string='رقم مركز التكلفه', related='task_point_id.cost_center_number')
    point_description = fields.Char(string='بيان الأعمال', related='task_point_id.point_description')
    uom_id = fields.Many2one(comodel_name='uom.uom', string='وحده القياس', related='task_point_id.uom')
    qty = fields.Float(string='الكميه', related='task_point_id.quantity')
    project_currency_id = fields.Many2one('res.currency', string='عمله المقايسه التنفيذيه',
                                          related='task_point_id.task_id.project_id.project_currency_id')
    price = fields.Monetary(string='السعر', currency_field='project_currency_id', related='task_point_id.unit_price')
