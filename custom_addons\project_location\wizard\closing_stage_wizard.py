from odoo import models, fields, api
from datetime import date


class ProjectClosingWizard(models.TransientModel):
    _name = 'project.closing_wizard'

    project_ids = fields.Many2many('project.project')
    reason = fields.Text(string='سبب الإقفال', required=True)

    def close(self):
        for project in self.project_ids:
            project.closing_description = self.reason
            project.closing_date = date.today()
            project.stage_id = project.stage_id.search([('status', '=', 'closed')], limit=1).id
