from odoo import fields, models, api
from odoo.exceptions import ValidationError
from datetime import datetime
import io
import xlsxwriter
import base64


def prepare_date(approvals):
    if not approvals:
        data = {
            'lines': [],
            'total': 0,
                }
        return data
    data = {
        'lines': [],
        'total': 0,
    }
    for approval in approvals:
        data['total'] = data['total'] + approval.total_after_discount
        data['lines'].append({
            'bill_date': approval.bill_date,
            'payment_order_name': approval.name,
            'order_id': approval.work_order_id.name,
            'order_value': approval.total_after_discount,
            'payment_order_value': approval.total_after_discount,
            'contractor_id': approval.contractor_id.name,
            'cost_center_number': approval.work_order_id.project_id.project_code,
            'cost_center_name': approval.work_order_id.project_id.name,

        })
    data['total'] = round(data['total'], 2)
    return data


class DailyBillReportLines(models.TransientModel):
    _name = 'approval.daily_bill_wizard_line'

    order_id = fields.Char()
    order_value = fields.Float()
    contractor_id = fields.Char()
    cost_center_number = fields.Char()


class DailyBillReport(models.TransientModel):
    _name = 'approval.daily_bill_wizard'

    report_view = fields.Boolean(default=False)
    report_ids = fields.Many2many(comodel_name='approval.daily_bill_wizard_line')
    report_type = fields.Selection(selection=[('PDF', 'PDF'), ('EXCEL', 'EXCEL'), ('VIEW', 'VIEW')],
                                   string='نوع التقرير', required=True, default='PDF')
    approvals = fields.Many2many(comodel_name='approval.request', compute='_compute_approvals', string='الطلبات')

    @api.depends('date_from', 'date_to')
    def _compute_approvals(self):
        approvals = self.env['approval.request'].search(
            [('date_confirmed', '>=', self.date_from), '|', ('date_confirmed', '<=', self.date_to), ('date_confirmed', '!=', False)])
        self.approvals = [(6, 0, approvals.ids)]

    date_from = fields.Date(string='من', required=1)
    date_to = fields.Date(string='إلى', required=1)

    def fill_view(self, data):
        for line in data.get('lines'):
            vals = {
                'order_id': line['order_id'],
                'order_value': line['order_value'],
                'contractor_id': line['contractor_id'],
                'cost_center_number': line['cost_center_number'],
            }
            self.report_ids = [(0, 0, vals)]
        self.report_view = True
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'approval.daily_bill_wizard',
            'res_id': self.id,
            'target': 'new',
        }

    def reset_view(self):
        self.report_view = False
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'approval.daily_bill_wizard',
            'res_id': self.id,
            'target': 'new',
        }

    def print(self):
        approvals = self.approvals
        if not approvals:
            return
        data = prepare_date(approvals)
        data['date_from'] = self.date_from
        data['date_to'] = self.date_to
        data['requester'] = self.env.user.name
        if self.report_type == 'PDF':
            return self.env.ref('project_location.daily_bill_report_action').report_action(self, data=data)
        if self.report_type == 'EXCEL':
            return self.generate_excel_report(data)
        if self.report_type == 'VIEW':
            return self.fill_view(data)

    def generate_excel_report(self, data):
        stream = io.BytesIO()
        workbook = xlsxwriter.Workbook(stream)
        worksheet = workbook.add_worksheet()

        # Write headers
        worksheet.write(0, 0, 'تاريخ ترحيل أمر السداد')
        worksheet.write(0, 1, 'اسم المتعهد')
        worksheet.write(0, 2, 'رقم امر العمل')
        worksheet.write(0, 3, 'قيمة امر لعمل ')
        worksheet.write(0, 4, 'رقم امر السداد ')
        worksheet.write(0, 5, 'قيمة امر السداد ')
        worksheet.write(0, 6, 'رقم مركز التكلفة ')
        worksheet.write(0, 7, 'اسم مركز التكلفة ')

        # Write data rows
        row = 1
        total = 0
        for line in data['lines']:
            total = total + line['payment_order_value']
            worksheet.write(row, 0, str(line['bill_date']))
            worksheet.write(row, 1, line['contractor_id'])
            worksheet.write(row, 2, line['order_id'])
            worksheet.write(row, 3, line['order_value'])
            worksheet.write(row, 4, line['payment_order_name'])
            worksheet.write(row, 5, line['payment_order_value'])
            worksheet.write(row, 6, line['cost_center_number'])
            worksheet.write(row, 7, line['cost_center_name'])
            row += 1
        worksheet.write(row - 1, 8, "الإجمالي")
        worksheet.write(row, 8, data['total'])

        workbook.close()

        stream.seek(0)
        file_data = stream.read()

        attach_id = self.env['ir.attachment'].create({
            'name': f'تقرير المرحل اليومي{self.date_from} - {self.date_to}.xlsx',
            'datas': base64.b64encode(file_data),
            'store_fname': 'daily_bill_report.xlsx',
            'type': 'binary'
        })

        url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url += "/web/content/%s?download=true" % attach_id.id
        return {
            "type": "ir.actions.act_url",
            "url": url,
            "target": "new",
        }
