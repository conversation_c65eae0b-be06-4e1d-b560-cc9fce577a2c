<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_task_point_select_wizard" model="ir.ui.view">
            <field name="name">project_task_point_select_wizard</field>
            <field name="model">task_point.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <group>
                            <field name="project_id"/>
                            <field name="task_id"/>
                        </group>
                        <field name="point_ids" options="{'no_create': True, 'no_quick_create':True,}">
                            <tree editable="bottom" create="0">
                                <field name="work_description"/>
                                <field name="description_id"/>
                                <field name="qty"/>
                                <field name="product_uom_id"/>
                                <field name="price_unit"/>
                                <field name="price_subtotal"/>
                                <field name="paid_values"/>
                                <field name="commitment_amount"/>
                            </tree>
                        </field>
                    </group>
                    <footer>
                        <button name="confirm" string="Confirm" type="object"
                                class="btn-primary"/>
                        <button special="cancel" string="Cancel" class="btn-secondary"/>
                    </footer>

                </form>
            </field>
        </record>


    </data>
</odoo>