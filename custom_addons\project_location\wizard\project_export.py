import base64
import io
import xlsxwriter
from odoo import fields, models, api, _


class ProjectMeasurementExportWizardLine(models.TransientModel):
    _name = 'project.measurement.export.wizard'

    work_export_file = fields.Binary('Excel File')
    excel_file_name = fields.Char('Excel File Name', default='work_measurement.xlsx')
    project_id = fields.Many2one(comodel_name='project.project', readonly=1)

    def export_work(self):
        for record in self:
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            worksheet = workbook.add_worksheet('Work Measurement')
            headers = ['إسم المشروع', 'رقم مركز التكلفه', 'تصنيف البند', 'وصف البند في المقايسه التنفيذيه', 'الكميه',
                       'وحده القياس', 'سعر التكلفه', 'إجمالي سعر التكلفه', 'سعر البيع', 'إجمالي سعر البيع',
                       'القيم المدفوعه', 'باقي الإلتزامات', 'قيم أوامر السداد', 'وزن البند', 'نسبه الإنجاز الماليه',
                       'نسبه الإنجاز الفنيه', 'ملاحظات']
            for col_num, header in enumerate(headers):
                worksheet.write(0, col_num, header)
            row = 1
            for line in record.project_id.work_measurement:
                worksheet.write(row, 0, line.project_id.name if line.project_id.name else '')
                worksheet.write(row, 1, line.project_id.project_code if line.project_id.project_code else '')
                worksheet.write(row, 2, line.work_description.name if line.work_description else '')
                worksheet.write(row, 3, line.description_id.name if line.description_id else '')
                worksheet.write(row, 4, line.qty)
                worksheet.write(row, 5, line.product_uom_id.name if line.product_uom_id else '')
                worksheet.write(row, 6, line.cost_price)
                worksheet.write(row, 7, line.total_cost_price)
                worksheet.write(row, 8, line.price_unit)
                worksheet.write(row, 9, line.price_subtotal)
                worksheet.write(row, 10, line.paid_values)
                worksheet.write(row, 11, line.commitment_amount)
                worksheet.write(row, 12, line.payment_order_total)
                worksheet.write(row, 13, line.work_measurement_weight)
                #TODO: Check Financial Progress and Technical Progress
                worksheet.write(row, 14, line.financial_progress)
                worksheet.write(row, 15, line.technical_progress if line.technical_progress else 0)
                worksheet.write(row, 16, line.notes if line.notes else '')
                row += 1
            workbook.close()
            output.seek(0)
            record.work_export_file = base64.b64encode(output.getvalue())
            record.excel_file_name = 'work_measurement.xlsx'
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'project.measurement.export.wizard',
            'view_mode': 'form',
            'view_type': 'form',
            'res_id': self.id,
            'target': 'new',
        }
