<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_export_wizard" model="ir.ui.view">
            <field name="name">project_export_wizard.form</field>
            <field name="model">project.measurement.export.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <field name="work_export_file" filename="excel_file_name" attrs="{'invisible':[('work_export_file','=',False)]}" readonly="1"/>
                        <field name="excel_file_name" invisible="1"/>
                        <field name="project_id" invisible="1"/>
                        <separator/>
                        <button name="export_work" string="إخراج" class="oe_highlight" type="object" attrs="{'invisible':[('work_export_file','!=',False)]}"/>
                    </group>
                    <footer>
                        <button string="Close" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>


    </data>
</odoo>