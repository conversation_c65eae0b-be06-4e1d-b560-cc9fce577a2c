from odoo import fields, models, api
import base64
import csv
import io
from odoo.exceptions import ValidationError,UserError
try:
    import xlrd
except ImportError:
    xlrd = None
try:
    from openpyxl import load_workbook
except ImportError:
    load_workbook = None
from icecream import ic

class ProjectMeasurementImport(models.Model):
    _inherit = 'project.project'

    def import_measurement(self):
        action = {
            "type": "ir.actions.act_window",
            "res_model": "project.measurement.import.wizard",
            'target': 'new',
            'view_mode': 'form',
            'context': {'default_project_id': self.id},
        }
        return action


class ProjectMeasurementImportWizardLine(models.TransientModel):
    _name = 'project.measurement.import.wizard.line'

    wizard_id = fields.Many2one(comodel_name='project.measurement.import.wizard')
    work_description = fields.Many2one(comodel_name='project.work.description', string='تصنيف البند')
    description_id = fields.Many2one(comodel_name='project_point.description', string='وصف البند في المقايسه التنفيذيه')
    qty = fields.Float(string='الكمية')
    uom_id = fields.Many2one(comodel_name='uom.uom', string='وحده القياس')
    cost_price = fields.Float(string='سعر التكلفه')


class ProjectMeasurementImportWizard(models.TransientModel):
    _name = 'project.measurement.import.wizard'

    project_id = fields.Many2one(comodel_name='project.project')
    file_upload = fields.Binary(string='استيراد')
    file_name = fields.Char(string='File Name')

    measurement_lines = fields.One2many(comodel_name='project.measurement.import.wizard.line', inverse_name='wizard_id',
                                        string='المقايسه')

    def button_upload_file(self):
        if not self.file_upload:
            raise UserError("Please upload a file.")
        file_extension = self.file_name.split('.')[-1].lower()
        file_content = base64.b64decode(self.file_upload)

        if file_extension == 'csv':
            rows = self._parse_csv(file_content)
        elif file_extension == 'xls' and xlrd:
            rows = self._parse_xls(file_content)
        elif file_extension == 'xlsx' and load_workbook:
            rows = self._parse_xlsx(file_content)
        else:
            raise UserError("Unsupported file type. Please upload a CSV, XLS, or XLSX file.")
        self._process_rows(rows)

        return {
            'context': self.env.context,
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'project.measurement.import.wizard',
            'res_id': self.id,
            'type': 'ir.actions.act_window',
            'target': 'new',
        }

    def _process_rows(self, rows):
        for row in rows:
            work_description = self.env['project.work.description'].search([('name', '=', row[0])], limit=1) or None
            if not work_description and row[0]:
                work_description = self.env['project.work.description'].create({'name': row[0]})
            description_id = self.env['project_point.description'].search([('name', '=', row[1])], limit=1) or None
            if not description_id and row[1]:
                description_id = self.env['project_point.description'].create({'name': row[1]})
            qty = row[2] or 0
            uom_id = self.env['uom.uom'].search([('name', '=', row[3])], limit=1) or None
            cost_price = row[4] or 0
            data = {
                'work_description': work_description.id if work_description else None,
                'description_id': description_id.id if description_id else None,
                'qty': qty,
                'uom_id': uom_id.id if uom_id else None,
                'cost_price': cost_price,
                'wizard_id': self.id
            }
            self.env['project.measurement.import.wizard.line'].create(data)

    def _parse_xlsx(self, file_content):
        file_stream = io.BytesIO(file_content)
        workbook = load_workbook(file_stream, read_only=True)
        sheet = workbook.active
        return [[cell.value for cell in row] for row in sheet.iter_rows(min_row=2) if any(cell.value for cell in row)]  # Start from row 2

    def _parse_xls(self, file_content):
        workbook = xlrd.open_workbook(file_contents=file_content)
        sheet = workbook.sheet_by_index(0)
        return [sheet.row_values(row_idx) for row_idx in range(1, sheet.nrows) if any(sheet.row_values(row_idx))]

    def _parse_csv(self, file_content):
        file_stream = io.StringIO(file_content.decode("utf-8"))
        reader = csv.reader(file_stream)
        next(reader)
        return [row for row in reader if any(row)]

    def confirm_import(self):
        if self.measurement_lines:
            for measurement_line in self.measurement_lines:
                project_vals = {
                    'work_description': measurement_line.work_description.id if measurement_line.work_description is not None else None,
                    'description_id': measurement_line.description_id.id if measurement_line.description_id is not None else None,
                    'qty': measurement_line.qty if measurement_line.qty is not None else None,
                    'product_uom_id': measurement_line.uom_id.id if measurement_line.uom_id is not None else None,
                    'cost_price': measurement_line.cost_price if measurement_line.cost_price is not None else None,
                    'project_id': self.project_id.id
                }
                self.env['work.measurement'].create(project_vals)
