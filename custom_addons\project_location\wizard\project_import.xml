<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_import_wizard" model="ir.ui.view">
            <field name="name">project_import_wizard.form</field>
            <field name="model">project.measurement.import.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <group>
                        <field name="file_upload" required="1" filename="file_name"/>
                        <field name="file_name" invisible="1"/>
                        <field name="project_id" invisible="1"/>
                        <separator/>
                        <button name="button_upload_file" string="تحميل" class="oe_highlight" type="object"/>
                    </group>
                    <field name="measurement_lines">
                        <tree create="0" editable="bottom">
                            <field name="work_description"/>
                            <field name="description_id"/>
                            <field name="qty"/>
                            <field name="uom_id"/>
                            <field name="cost_price"/>
                        </tree>
                    </field>
                    <footer>
                        <button name="confirm_import" type="object" string="Confirm" class="oe_highlight"/>
                        or
                        <button string="Cancel" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>


    </data>
</odoo>