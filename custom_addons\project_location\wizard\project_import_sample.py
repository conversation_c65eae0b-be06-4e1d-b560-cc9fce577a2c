from odoo import models, fields, api
import base64
import io
import xlsxwriter

sample_list = [
    'الأعمال التمهيدية',
    'أعمال الهدم والإزالة والنقل',
    'أعمال الحفر والردم والتسوية',
    'أعمال الهيكل الخرساني',
    'أعمال خرسانات القواعد',
    'أعمال خرسانات الميد (السملات)',
    'أعمال خرسانات الكرسنة والأرضيات',
    'أعمال خرسانات الحوائط الخرسانية',
    'أعمال خرسانات الأعمدة',
    'أعمال خرسانات الأسقف و السلالم',
    'أعمال خرسانات الأعتاب والكرانيش والديكورات',
    'أعمال خرسانات دكة الميول ( الباتوتة)',
    'أعمال تنفيذ خزانات الصرف الصحي',
    'أعمال تنفيذ خزان المياه السفلي',
    'أعمال تنفيذ خزان المياه العلوي',
    'أعمال المباني',
    'أعمال اللياسة',
    'أعمال الطلاء',
    'أعمال تكسيات الأرضيات والحوائط',
    'أعمال تكسيات وديكورات الرخام',
    'أعمال النجارة ',
    'أعمال الالومنيوم + PVC',
    'أعمال الجبس بورد والأسقف المعلقة',
    'السباكة والأعمال الصحية (تأسيس + تشطيب)',
    'الأعمال الكهربائية (تأسيس + تشطيب)',
    'الأعمال المعدنية',
    'اعمال العزل والمعالجة',
    'الأعمال الكهروميكانيكية (التكييف المركزي + المصاعد)',
    'أعمال أحواض السباحة (أعمال مدنية + منظومات)',
    'أعمال المنظومات (شبكات + انترنت + ....)',
    'أعمال شبكة الكهرباء والإنارة الخارجية',
    'أعمال شبكة الصرف الصحي',
    'أعمال شبكة مياه الأمطار',
    'أعمال شبكة مياه الشرب',
    'أعمال شبكة الرئ والمسطحات الخضراء',
    'أعمال الطرق والأرصفة',
    'الاعمال الإضافية',
    'العمالة العارضة ',
    'مواد المشروع',
    'أوامر عمل الآليات',
]

class ProjectMeasurementImportSampleWizard(models.TransientModel):
    _name = 'project.measurement.import.sample.wizard'

    excel_file = fields.Binary('Excel File')
    excel_file_name = fields.Char('Excel File Name', default='todo_tasks.xlsx')

    def action_generate_excel(self):
        for record in self:
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'import': True})
            worksheet = workbook.add_worksheet('Import Sample')
            headers = ['تصنيف البند', 'وصف البند في المقايسه التنفيذيه', 'الكميه', 'وحده القياس', 'سعر التكلفه']
            for col_num, header in enumerate(headers):
                worksheet.write(0, col_num, header)
            for i, line in enumerate(sample_list):
                worksheet.write(i+1, 0, line)
            workbook.close()
            output.seek(0)
            # Save the Excel file in the binary field
            record.excel_file = base64.b64encode(output.getvalue())
            record.excel_file_name = 'import_work.xlsx'
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'project.measurement.import.sample.wizard',
            'view_mode': 'form',
            'view_type': 'form',
            'res_id': self.id,
            'target': 'new',
        }
