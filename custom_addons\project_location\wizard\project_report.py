from odoo import fields, models, api
from odoo.exceptions import ValidationError
from datetime import datetime
import io
import xlsxwriter
import base64


def prepare_date(tasks):
    if not tasks:
        data = {'lines': []}
        return data
    data = {'lines': []}
    for task in tasks:
        data['lines'].append({
            'create_date': task.create_date,
            'work_order_id': task.name,
            'order_value': task.total_points,

            # 'payment_order_value': approval.total_after_discount,
            # 'cost_center_number': approval.work_order_id.cost_center_number,
            # 'cost_center_name': approval.work_order_id.project_name,

        })
    return data


class ProjectReportLines(models.TransientModel):
    _name = 'project.report_wizard_line'

    order_id = fields.Char()
    order_value = fields.Float()
    contractor_id = fields.Char()
    cost_center_number = fields.Char()


class ProjectReport(models.TransientModel):
    _name = 'project.report_wizard'

    report_view = fields.Boolean(default=False)
    report_ids = fields.Many2many(comodel_name='approval.daily_bill_wizard_line')
    report_type = fields.Selection(selection=[('PDF', 'PDF'), ('EXCEL', 'EXCEL')],
                                   string='نوع التقرير', required=True, default='PDF')
    tasks = fields.Many2many(comodel_name='project.task', compute='_compute_tasks', string='أوامر العمل')

    @api.depends('date_from', 'date_to')
    def _compute_tasks(self):
        tasks = self.env['project.task'].search(
            [('date', '>=', self.date_from),'|',('date', '<=', self.date_to), ('date', '!=', False),
             ('request_type', '=', 'work_order')])
        self.tasks = [(6, 0, tasks.ids)]

    date_from = fields.Date(string='من', required=1)
    date_to = fields.Date(string='إلى', required=1)


    def reset_view(self):
        self.report_view = False
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'approval.daily_bill_wizard',
            'res_id': self.id,
            'target': 'new',
        }

    def print(self):
        tasks = self.tasks
        if not tasks:
            return
        data = prepare_date(tasks)
        data['date_from'] = self.date_from
        data['date_to'] = self.date_to
        data['requester'] = self.env.user.name
        if self.report_type == 'PDF':
            return self.env.ref('project_location.daily_bill_report_action').report_action(self, data=data)
        if self.report_type == 'EXCEL':
            return self.generate_excel_report(data)

    def generate_excel_report(self, data):
        stream = io.BytesIO()
        workbook = xlsxwriter.Workbook(stream)
        worksheet = workbook.add_worksheet()

        # Write headers
        worksheet.write(0, 0, 'تاريخ ادخال امر العمل')
        worksheet.write(0, 1, 'رقم أمر العمل')
        worksheet.write(0, 2, 'قيمة أمر العمل')
        worksheet.write(0, 3, 'رقم الدفعة')
        worksheet.write(0, 4, 'تاريخ ترحيل امر السداد')
        worksheet.write(0, 5, 'رقم أمر السداد')
        worksheet.write(0, 6, 'القيمة الدفعة بأمر السداد')
        worksheet.write(0, 7, 'نسبه الإنجاز الماليه')

        # Write data rows
        row = 1
        for line in data['lines']:
            worksheet.write(row, 0, line['create_date'])
            worksheet.write(row, 1, line['work_order_id'])
            worksheet.write(row, 2, line['order_value'])
            # worksheet.write(row, 3, line['order_id'])
            # worksheet.write(row, 4, line['order_value'])
            # worksheet.write(row, 5, line['payment_order_value'])
            # worksheet.write(row, 6, line['cost_center_number'])
            # worksheet.write(row, 7, line['cost_center_name'])
            row += 1

        workbook.close()

        stream.seek(0)
        file_data = stream.read()

        attach_id = self.env['ir.attachment'].create({
            'name': f'جدول تقرير مراكز التكلفه{self.date_from} - {self.date_to}.xlsx',
            'datas': base64.b64encode(file_data),
            'store_fname': 'work_orders_report.xlsx',
            'type': 'binary'
        })

        url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url += "/web/content/%s?download=true" % attach_id.id
        return {
            "type": "ir.actions.act_url",
            "url": url,
            "target": "new",
        }
