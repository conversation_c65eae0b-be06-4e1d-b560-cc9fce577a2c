<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="project_report_wizard" model="ir.ui.view">
            <field name="name">project.report_wizard</field>
            <field name="model">project.report_wizard</field>
            <field name="arch" type="xml">
                <form>
                    <field name="report_view" invisible="1"/>
                    <group attrs="{'invisible':[('report_view','!=',False)]}">
                        <group>
                            <field name="report_type"/>
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="tasks" widget="many2many_tags"/>
                        </group>
                    </group>
                    <footer>
                        <button name="print" string="Print" type="object"
                                class="btn-primary" attrs="{'invisible':[('report_view','!=',False)]}"/>
                        <button name="reset_view" string="Rest" type="object"
                                class="btn-primary" attrs="{'invisible':[('report_view','!=',True)]}"/>
                        <button special="cancel" string="Cancel" class="btn-secondary"/>
                    </footer>

                </form>
            </field>
        </record>


        <record id="project_report_action" model="ir.actions.act_window">
            <field name="name">تقرير مراكز التكلفه</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">project.report_wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

        <menuitem name="تقرير مراكز التكلفه" id="project_report_menu_item" parent="project.menu_main_pm"
                  action="project_report_action" sequence="5"/>

    </data>
</odoo>