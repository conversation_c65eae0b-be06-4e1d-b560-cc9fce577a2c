from odoo import models, fields, api


class TaskMatPurchase(models.TransientModel):
    _name = 'task.mat_purchase_wizard'

    vendor_id = fields.Many2one(comodel_name='res.partner', domain=[('supplier_rank', '=', 1)], string='المورد')
    line_ids = fields.Many2many(comodel_name='task.purchases_wizard_line', string='الطلب')
    task_id = fields.Many2one(comodel_name='project.task')

    def confirm_purchase(self):
        vals = {
            'partner_id': self.vendor_id.id,
            'task_id': self.task_id.id,
        }
        purchase_order = self.env['purchase.order'].create(vals)
        for line in self.line_ids:
            purchase_order.write({'order_line': [(0, 0,
                                                  {'product_id': line.material_description.id,
                                                   'product_qty': line.qty if line.task_id.buy_return == 'buy' else -line.qty,
                                                   'analytic_distribution': {
                                                       self.task_id.project_id.analytic_account_id.id: 100},
                                                   'task_line_id': line.task_line_id.id,
                                                   })], })
            line.task_line_id.purchase_order_ids = [(4, purchase_order.id)]
        return {
            'res_model': 'purchase.order',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'current',
            'res_id': purchase_order.id,
        }


class TaskPurchasesWizardLine(models.TransientModel):
    _name = 'task.purchases_wizard_line'

    task_id = fields.Many2one(comodel_name='project.task')
    task_line_id = fields.Many2one(comodel_name='task.purchases')
    material_description = fields.Many2one(comodel_name='product.product', string='وصف المواد', readonly=True)
    category = fields.Selection(string='التصنيف', selection=[
        ('primary', 'رئيسي'),
        ('sub', 'فرعي')
    ], readonly=True)
    qty = fields.Float(string='الكمية')
