from odoo import fields, models, api


class ProjectTask(models.Model):
    _inherit = 'project.task'

    def open_points_wizard(self):
        return {
            'name': 'البنود',
            'res_model': 'task_point.wizard',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {'default_task_id': self.id},
        }


class ModelName(models.TransientModel):
    _name = 'task_point.wizard'
    _description = 'Task Point Wizard'

    task_id = fields.Many2one('project.task', readonly=True)
    project_id = fields.Many2one('project.project', related='task_id.project_id', readonly=True)
    point_ids = fields.Many2many(comodel_name='work.measurement', compute='_compute_points',
                                 string='المقايسة التنفيذية', readonly=False)

    @api.depends('project_id')
    def _compute_points(self):
        project_points = self.env['work.measurement'].search([('project_id', '=', self.project_id.id)])
        self.point_ids = [(6, 0, project_points.ids)]

    def confirm(self):
        point_list = []
        for line in self.point_ids:
            vals = {
                'point_description_id': line.work_description.id,
                'description': line.description,
                'categorization': line.work_description.id,
                'quantity': line.qty,
                'uom': line.product_uom_id.id,
                'unit_price': line.price_unit,
                'created_from_project': True,
            }
            point_list.append(self.env['task.point'].create(vals).id)
        self.task_id.point_ids = [(6, 0, point_list)]
