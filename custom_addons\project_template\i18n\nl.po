# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_template
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-10-09 02:52+0000\n"
"Last-Translator: \"<PERSON> [<PERSON>]\" <<EMAIL>>\n"
"Language-Team: none\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: project_template
#: model_terms:ir.ui.view,arch_db:project_template.project_template_view_inherit_form
msgid "Create Project From Template"
msgstr "Project maken op basis van sjabloon"

#. module: project_template
#: model_terms:ir.ui.view,arch_db:project_template.project_template_view_inherit_kanban
msgid "Create Project from Template"
msgstr "Project maken op basis van sjabloon"

#. module: project_template
#: model:ir.model.fields,field_description:project_template.field_project_project__is_template
msgid "Is Template"
msgstr "Is een sjabloon"

#. module: project_template
#: model_terms:ir.ui.view,arch_db:project_template.project_template_view_inherit_form
msgid "Is Template?"
msgstr "Is een sjabloon?"

#. module: project_template
#: model_terms:ir.ui.view,arch_db:project_template.project_template_view_inherit_search
msgid "Non-Templates"
msgstr "Geen sjablonen"

#. module: project_template
#: model:ir.model,name:project_template.model_project_project
msgid "Project"
msgstr ""

#. module: project_template
#: model_terms:ir.ui.view,arch_db:project_template.project_template_view_inherit_search
msgid "Templates"
msgstr "Sjablonen"
