from odoo import models, fields

class ServiceProvider(models.Model):
    _name = 'service.provider'
    _description = 'Service Provider'

    name = fields.Char(string='Service Provider', required=True)

class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    service_provider_id = fields.Many2one('service.provider', string='Service Provider')

    # تعديل الحقول لجعلها غير مرتبطة
    phone = fields.Char(string='Private Phone', groups="hr.group_hr_user", readonly=False)
    private_email = fields.Char(string='Private Email', groups="hr.group_hr_user", readonly=False)
