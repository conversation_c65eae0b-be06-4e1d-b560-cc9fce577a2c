<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Tree view for service providers -->
    <record id="view_service_provider_tree" model="ir.ui.view">
        <field name="name">service.provider.tree</field>
        <field name="model">service.provider</field>
        <field name="arch" type="xml">
            <tree string="Service Providers">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <!-- Form view for service providers -->
    <record id="view_service_provider_form" model="ir.ui.view">
        <field name="name">service.provider.form</field>
        <field name="model">service.provider</field>
        <field name="arch" type="xml">
            <form string="Service Providers">
                <sheet>
                    <group>
                        <field name="name" style="width: 200px;"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for service provider -->
    <record id="action_service_provider" model="ir.actions.act_window">
        <field name="name">Service Providers</field>
        <field name="res_model">service.provider</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- Menu item under the Configuration menu -->
    <menuitem id="menu_service_provider"
              name="Service Providers"
              parent="hr.menu_human_resources_configuration"
              action="action_service_provider"
              sequence="101"/>

    <!-- Form view modification for hr.employee to add service provider and edit fields -->
    <record id="view_employee_form_service_provider" model="ir.ui.view">
        <field name="name">hr.employee.form.service.provider</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <!-- Add the service provider field -->
            <xpath expr="//field[@name='mobile_phone']" position="before">
                <field name="service_provider_id"/>
            </xpath>
            <!-- Make phone and private_email editable -->
            <xpath expr="//page[@name='personal_information']//field[@name='phone']" position="attributes">
                <attribute name="readonly">0</attribute>
            </xpath>
            <xpath expr="//page[@name='personal_information']//field[@name='private_email']" position="attributes">
                <attribute name="readonly">0</attribute>
            </xpath>
        </field>
    </record>

    <!-- Modify Search View to add Service Provider filter -->
    <record id="view_employee_filter_service_provider" model="ir.ui.view">
        <field name="name">hr.employee.search.service.provider</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <!-- Insert the filter after the Unread Messages filter -->
            <xpath expr="//filter[@name='message_needaction']" position="after">
                <filter string="Service Provider" name="service_provider_filter"
                        domain="[('service_provider_id', '!=', False)]"/>
            </xpath>

     <!-- Add filter for Running Contracts expiring in less than 10 days -->
            <xpath expr="//filter[@name='service_provider_filter']" position="after">
                <filter string="Running Contracts (Next 10 days)" name="contract_running_filter"
                        domain="[('contract_ids', '!=', False),
                                ('contract_ids.state', '=', 'open'),
                                ('contract_ids.date_end', '&gt;', context_today()),
                                ('contract_ids.date_end', '&lt;', (context_today() + relativedelta(days=10)))]"/>
            </xpath>
             <!-- Add search panel for filtering by service provider -->
        <xpath expr="//searchpanel" position="inside">
            <field name="service_provider_id" string="Service Provider" icon="fa-briefcase" enable_counters="1"/>
        </xpath>
        </field>
    </record>

       <!-- Add Service Provider filter to hr.attendance -->
    <record id="view_hr_attendance_filter_service_provider" model="ir.ui.view">
    <field name="name">hr.attendance.search.service.provider</field>
    <field name="model">hr.attendance</field>
    <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter"/> <!-- Inherit from your filter -->
    <field name="arch" type="xml">
        <!-- Add the new filter for Service Provider after "My Attendances" -->
        <xpath expr="//filter[@name='myattendances']" position="after">
            <filter string="Service Provider" name="service_provider_attendance_filter"
                    domain="[('employee_id.service_provider_id', '!=', False)]"/>
        </xpath>

    </field>
</record>

</odoo>
