==============
Stock Analytic
==============

.. !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--analytic-lightgray.png?logo=github
    :target: https://github.com/OCA/account-analytic/tree/16.0/stock_analytic
    :alt: OCA/account-analytic
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-analytic-16-0/account-analytic-16-0-stock_analytic
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runbot-Try%20me-875A7B.png
    :target: https://runbot.odoo-community.org/runbot/87/16.0
    :alt: Try me on Runbot

|badge1| |badge2| |badge3| |badge4| |badge5| 

Adds Analytic Distribution field in stock move to be able to get
analytic information when generating the journal items.

**Table of contents**

.. contents::
   :local:

Configuration
=============

As necessary, go to *Invoicing > Configuration > Analytic Plans*, open the relevant
record and update the applicability for 'Stock Move'.

Usage
=====

To Assign an Analytic Account to a Stock Move
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

You need to:

#. Create manually or open draft picking
#. Add move lines and assign an **analytic account** in Analytic Distribution field

Assigned Journal Items created from Stock Move with Analytic Account
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If stock move automatically create journal entry, the journal entry will
contain journal items with following rule:

#. Journal item with account equal to product's valuation account will not be
   assigned to any analytic account.
#. Journal item with account different to product's valuation account will be
   assigned to an analytic account according to the stock move's analytic
   account.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-analytic/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us smashing it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-analytic/issues/new?body=module:%20stock_analytic%0Aversion:%2016.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Julius Network Solutions
* ClearCorp
* OpenSynergy Indonesia
* Hibou Corp.

Contributors
~~~~~~~~~~~~

* Hanane ELKHAL <<EMAIL>>
* Yvan Patry <<EMAIL>>
* Pierre <<EMAIL>>
* Mathieu VATEL <<EMAIL>>
* Fabio Vílchez <<EMAIL>>
* Andhitia Rama <<EMAIL>>
* Michael Viriyananda <<EMAIL>>
* Aaron Henriquez <<EMAIL>>
* Jared Kipe <<EMAIL>>
* Alan Ramos <<EMAIL>>
* Mantas Šniukas <<EMAIL>>
* `Quartile <https://www.quartile.co>`__:

  * Yoshi Tashiro

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-analytic <https://github.com/OCA/account-analytic/tree/16.0/stock_analytic>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
