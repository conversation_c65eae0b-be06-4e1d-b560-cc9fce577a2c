# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_analytic
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-19 22:13+0000\n"
"PO-Revision-Date: 2017-11-19 22:13+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Croatian (Croatia) (https://www.transifex.com/oca/teams/23907/"
"hr_HR/)\n"
"Language: hr_HR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: stock_analytic
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move__analytic_distribution
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move_line__analytic_distribution
#: model:ir.model.fields,field_description:stock_analytic.field_stock_scrap__analytic_distribution
msgid "Analytic"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: stock_analytic
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move__analytic_precision
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move_line__analytic_precision
#: model:ir.model.fields,field_description:stock_analytic.field_stock_scrap__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: stock_analytic
#: model:ir.model.fields,field_description:stock_analytic.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_scrap
msgid "Scrap"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_move
#: model:ir.model.fields.selection,name:stock_analytic.selection__account_analytic_applicability__business_domain__stock_move
msgid "Stock Move"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_picking
msgid "Transfer"
msgstr ""

#~ msgid "Analytic Account"
#~ msgstr "Analitički konto"
