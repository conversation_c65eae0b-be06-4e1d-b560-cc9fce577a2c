# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_analytic
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 8.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-12-16 03:41+0000\n"
"PO-Revision-Date: 2016-12-16 03:41+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2016\n"
"Language-Team: Romanian (https://www.transifex.com/oca/teams/23907/ro/)\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

#. module: stock_analytic
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move__analytic_distribution
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move_line__analytic_distribution
#: model:ir.model.fields,field_description:stock_analytic.field_stock_scrap__analytic_distribution
msgid "Analytic"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: stock_analytic
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move__analytic_precision
#: model:ir.model.fields,field_description:stock_analytic.field_stock_move_line__analytic_precision
#: model:ir.model.fields,field_description:stock_analytic.field_stock_scrap__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: stock_analytic
#: model:ir.model.fields,field_description:stock_analytic.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_scrap
msgid "Scrap"
msgstr ""

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_move
#: model:ir.model.fields.selection,name:stock_analytic.selection__account_analytic_applicability__business_domain__stock_move
msgid "Stock Move"
msgstr "Mișcare stoc"

#. module: stock_analytic
#: model:ir.model,name:stock_analytic.model_stock_picking
msgid "Transfer"
msgstr ""

#~ msgid "Analytic Account"
#~ msgstr "Cont analitic"
