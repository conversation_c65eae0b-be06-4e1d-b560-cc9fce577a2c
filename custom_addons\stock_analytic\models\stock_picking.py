# Copyright 2023 Quartile Limited
# License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl).

from odoo import fields, models,api

class StockPicking(models.Model):
    _inherit = "stock.picking"

    def button_validate(self):
        self = self.with_context(validate_analytic=True)
        return super().button_validate()


    deliver_name = fields.Char(string="المسلم")
    recipient_name = fields.Char(string="مستلم البضاعة")
    total_quantity = fields.Float(compute='_compute_total_quantity',string="Total Quantity")

    @api.depends('move_ids_without_package','move_ids_without_package.quantity_done')
    def _compute_total_quantity(self):
        self.total_quantity = 0
        for rec in self:
            total=0
            for line in rec.move_ids_without_package:
                total += line.quantity_done
                rec.total_quantity = total


