<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="stock.report_picking">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="row justify-content-end mb16">
                                <div class="col-4" name="right_box">
                                    <div t-field="o.name" t-options="{'widget': 'barcode', 'width': 600, 'height': 100, 'img_style': 'width:300px;height:50px;'}"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6" name="div_outgoing_address">
                                    <div t-if="o.should_print_delivery_address()">
                                        <span><strong>Delivery Address:</strong></span>
                                        <div t-field="o.move_ids[0].partner_id"
                                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                                    </div>
                                    <div t-elif="o.picking_type_id.code != 'internal' and o.picking_type_id.warehouse_id.partner_id">
                                        <span><strong>Warehouse Address:</strong></span>
                                        <div t-field="o.picking_type_id.warehouse_id.partner_id"
                                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                                    </div>
                                </div>
                                <div class="col-5 offset-1" name="div_incoming_address">
                                    <t t-set="show_partner" t-value="False" />
                                    <div t-if="o.picking_type_id.code=='incoming' and o.partner_id">
                                        <span><strong>Vendor Address:</strong></span>
                                        <t t-set="show_partner" t-value="True" />
                                    </div>
                                    <div t-if="o.picking_type_id.code=='internal' and o.partner_id">
                                        <span><strong>Warehouse Address:</strong></span>
                                        <t t-set="show_partner" t-value="True" />
                                    </div>
                                    <div t-if="o.picking_type_id.code=='outgoing' and o.partner_id and o.partner_id != o.partner_id.commercial_partner_id">
                                        <span><strong>Customer Address:</strong></span>
                                        <t t-set="show_partner" t-value="True" />
                                    </div>
                                    <div t-if="show_partner" name="partner_header">
                                        <div t-field="o.partner_id.commercial_partner_id"
                                             t-options='{"widget": "contact", "fields": ["address", "name", "phone", "vat"], "no_marker": True, "phone_icons": True}'/>
                                    </div>
                                </div>
                            </div>
                            <br/>
                            <h1 t-field="o.name" class="mt0"/>
                            <div class="row mt48 mb32">
                                <div t-if="o.origin" class="col-auto" name="div_origin">
                                    <strong>Order:</strong>
                                    <p t-field="o.origin"/>
                                </div>
                                <div class="col-auto" name="div_state">
                                    <strong>Status:</strong>
                                    <p t-field="o.state"/>
                                </div>
                                <div class="col-auto" name="div_sched_date">
                                    <strong>Scheduled Date:</strong>
                                    <p t-field="o.scheduled_date"/>
                                </div>
                            </div>
                            <table class="table table-sm" t-if="o.move_line_ids and o.move_ids_without_package">
                                <t t-set="has_barcode" t-value="any(move_line.product_id and move_line.product_id.sudo().barcode or move_line.package_id for move_line in o.move_line_ids)"/>
                                <t t-set="has_serial_number" t-value="any(move_line.lot_id or move_line.lot_name for move_line in o.move_line_ids)" groups="stock.group_production_lot"/>
                                <thead>
                                    <tr>
                                        <th name="th_product">
                                            <strong>Product</strong>
                                        </th>
                                        <th>
                                            <strong>Quantity</strong>
                                        </th>
                                        <th name="th_from" t-if="o.picking_type_id.code != 'incoming'" align="left" groups="stock.group_stock_multi_locations">
                                            <strong>From</strong>
                                        </th>
                                        <th name="th_to" t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                            <strong>To</strong>
                                        </th>
                                        <th name="th_serial_number" class="text-center" t-if="has_serial_number">
                                           <strong>Lot/Serial Number</strong>
                                        </th>
                                        <th name="th_barcode" class="text-center" t-if="has_barcode">
                                            <strong>Product Barcode</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- In case you come across duplicated lines, ask NIM or LAP -->
                                    <t t-foreach="o.move_line_ids_without_package" t-as="ml">
                                        <tr>
                                            <td>
                                                <span t-field="ml.product_id.display_name"/><br/>
                                                <span t-field="ml.product_id.description_picking"/>
                                            </td>
                                            <td>
                                                <span t-if="o.state != 'done'" t-field="ml.reserved_uom_qty"/>
                                                <span t-if="o.state == 'done'" t-field="ml.qty_done"/>
                                                <span t-field="ml.product_uom_id" groups="uom.group_uom"/>
                                            </td>
                                            <td t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">
                                                <span t-esc="ml.location_id.display_name"/>
                                                    <t t-if="ml.package_id">
                                                        <span t-field="ml.package_id"/>
                                                    </t>
                                            </td>
                                            <td t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                                <div>
                                                    <span t-field="ml.location_dest_id"/>
                                                    <t t-if="ml.result_package_id">
                                                        <span t-field="ml.result_package_id"/>
                                                    </t>
                                                </div>
                                            </td>
                                            <td class=" text-center h6" t-if="has_serial_number">
                                                <div t-if="has_serial_number and (ml.lot_id or ml.lot_name)" t-esc="ml.lot_id.name or ml.lot_name" t-options="{'widget': 'barcode', 'humanreadable': 1, 'width': 400, 'height': 100, 'img_style': 'width:100%;height:35px;'}"/>
                                            </td>
                                            <td class="text-center" t-if="has_barcode">
                                                <t t-if="product_barcode != ml.product_id.barcode">
                                                    <span t-if="ml.product_id and ml.product_id.barcode">
                                                        <div t-field="ml.product_id.barcode" t-options="{'widget': 'barcode', 'symbology': 'auto', 'width': 400, 'height': 100, 'quiet': 0, 'img_style': 'height:35px;'}"/>
                                                    </span>
                                                    <t t-set="product_barcode" t-value="ml.product_id.barcode"/>
                                                </t>
                                            </td>
                                        </tr>
                                        
                                    </t>
                                    <tr class="is-total text-center">
                                        <td  colspan="1"> <strong>Total</strong>
                                        </td>
                                        <td style="text-align: left">
                                            <span t-if="o.state == 'done'" t-field="o.total_quantity"/>
                                            <span t-if="o.state != 'done'">
                                                <t t-esc="'{0:,.2f}'.format(float(sum([p['reserved_uom_qty'] for p in o.move_line_ids_without_package])))"/>
                                            </span>
                                        </td>
                                    </tr>
                                  </tbody>
                            </table>
                        
                            <table class="table table-sm" t-if="o.package_level_ids and o.picking_type_entire_packs and o.state in ['assigned', 'done']">
                                <thead>
                                    <tr>
                                        <th name="th_package">Package</th>
                                        <th name="th_pko_from" t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">From</th>
                                        <th name="th_pki_from" t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">To</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="o.package_level_ids.sorted(key=lambda p: p.package_id.name)" t-as="package">
                                        <t t-set="package" t-value="package.with_context(picking_id=o.id)" />
                                        <td name="td_pk_barcode">
                                            <div t-field="package.package_id.name" t-options="{'widget': 'barcode', 'humanreadable': 1, 'width': 600, 'height': 100, 'img_style': 'width:300px;height:50px;margin-left: -50px;'}"/><br/>
                                        </td>
                                        <td t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">
                                            <span t-field="package.location_id"/>
                                        </td>
                                        <td t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                            <span t-field="package.location_dest_id"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <br></br>
                            <br></br>
                            <table class="table table-borderless" style="border:none">
                                <tbody>
                                    <tr>
                                        <td>صرفت من قبل</td>
                                        <td>..................................................</td>
                                        <td> المستلم</td>
                                        <td><span t-field="o.recipient_name"/></td>
                                    </tr>
                                    <tr>
                                        <td>إعداد: </td>
                                        <td> <t t-esc="env.user.name"/></td>
                                        <td>مكان التسليم</td>
                                        <td>..................................................</td>
                                    </tr>
                                    <tr>
                                        <td>إسم السائق</td>
                                        <td>..................................................</td>
                                        <td>التوقيع</td>
                                        <td>..................................................</td>
                                    </tr>
                                </tbody>
                            </table>
                            <t t-set="no_reserved_product" t-value="o.move_ids.filtered(lambda x: x.product_uom_qty != x.reserved_availability and x.move_line_ids and x.state!='done')"/>
                            <p t-if="o.state in ['draft', 'waiting', 'confirmed'] or no_reserved_product"><i class="fa fa-exclamation-triangle" />
                                All products could not be reserved. Click on the "Check Availability" button to try to reserve products.
                            </p>
                            <p t-field="o.note"/>
                        </div>
                    </t>
                </t>
            </t>
        </template>
<!--         <template id="report_picking_type_label">
            <t t-set="title">Operation Types</t>
            <t t-call="stock.report_generic_barcode"/>
        </template> -->


<template id="stock_delivery_report_template" inherit_id="stock.report_delivery_document">
        <xpath expr="." position="replace">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                <t t-set="o" t-value="o.with_context(lang=o._get_report_lang())" />
                <t t-set="partner" t-value="o.partner_id or (o.move_ids and o.move_ids[0].partner_id) or False"/>

                <t t-set="address">
                    <div name="div_outgoing_address">
                        <div name="outgoing_delivery_address"
                            t-if="o.should_print_delivery_address()">
                            <span><strong>Delivery Address:</strong></span>
                            <div t-field="o.move_ids[0].partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        </div>
                        <div name="outgoing_warehouse_address"
                             t-elif="o.picking_type_id.code != 'internal' and o.picking_type_id.warehouse_id.partner_id">
                            <span><strong>Warehouse Address:</strong></span>
                            <div t-field="o.picking_type_id.warehouse_id.partner_id"
                                t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        </div>
                    </div>
                </t>
                <t t-set="information_block">
                    <div class="row">
                        <div class="col-7" name="div_incoming_address">
                            <t t-set="show_partner" t-value="False" />
                            <div name="vendor_address" t-if="o.picking_type_id.code=='incoming' and partner">
                                <span><strong>Vendor Address:</strong></span>
                                <t t-set="show_partner" t-value="True" />
                            </div>
                            <div name="customer_address" t-if="o.picking_type_id.code=='outgoing' and partner and partner != partner.commercial_partner_id">
                                <span><strong>Customer Address:</strong></span>
                                <t t-set="show_partner" t-value="True" />
                            </div>
                            <div t-if="show_partner" name="partner_header">
                                <div t-field="partner.commercial_partner_id"
                                     t-options='{"widget": "contact", "fields": ["address", "name", "phone", "vat"], "no_marker": True, "phone_icons": True}'/>
                            </div>
                        </div>
                    </div>
                </t>
                <div class="page">
                    <h2>
                        <span t-field="o.name"/>
                    </h2>
                    <div class="row mt32 mb32">
                        <div t-if="o.origin" class="col-auto" name="div_origin">
                            <strong>Order:</strong>
                            <p t-field="o.origin"/>
                        </div>
                        <div t-if="o.state" class="col-auto" name="div_sched_date">
                            <strong>Shipping Date:</strong>
                            <t t-if="o.state == 'done'">
                                <p t-field="o.date_done"/>
                            </t>
                            <t t-if="o.state != 'done'">
                                <p t-field="o.scheduled_date"/>
                           </t>
                        </div>
                    </div>
                    <table class="table table-sm" t-if="o.state!='done'" name="stock_move_table">
                        <thead>
                            <tr>
                                <th name="th_sm_product"><strong>Product</strong></th>
                                <th name="th_sm_ordered"><strong>Ordered</strong></th>
                                <th name="th_sm_quantity"><strong>Delivered</strong></th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-set="lines" t-value="o.move_ids.filtered(lambda x: x.product_uom_qty)"/>
                            <tr t-foreach="lines" t-as="move">
                                <td>
                                    <span t-field="move.product_id"/>
                                    <p t-if="move.description_picking != move.product_id.name and move.description_picking != move.product_id.display_name">
                                        <span t-field="move.description_picking"/>
                                    </p>
                                </td>
                                <td>
                                    <span t-field="move.product_uom_qty"/>
                                    <span t-field="move.product_uom"/>
                                </td>
                                <td>
                                    <span t-field="move.quantity_done"/>
                                    <span t-field="move.product_uom"/>
                                </td>
                            </tr>
                            <tr class="is-total text-center">
                                <td  colspan="1"> <strong>Total</strong>
                                </td>
                                <td style="position:relative;text-align:left">
                                    <t t-esc="'{0:,.2f}'.format(float(sum([p['product_uom_qty'] for p in lines])))"/></td>
                                <td style="position:relative;text-align:left">
                                    <span t-field="o.total_quantity"/>
                                </td>
                        </tr>
                        </tbody>
                    </table>
                    <table class="table table-sm mt48" t-if="o.move_line_ids and o.state=='done'" name="stock_move_line_table">
                        <t t-set="has_serial_number" t-value="False"/>
                        <t t-set="has_serial_number" t-value="o.move_line_ids.mapped('lot_id')" groups="stock.group_lot_on_delivery_slip"/>
                        <thead>
                            <tr>
                                <th name="th_sml_product"><strong>Product</strong></th>
                                <t name="lot_serial" t-if="has_serial_number">
                                    <th>
                                        Lot/Serial Number
                                    </th>
                                </t>
                                <th name="th_sml_qty_ordered" class="text-center" t-if="not has_serial_number">
                                    <strong>Ordered</strong>
                                </th>
                                <th name="th_sml_quantity" class="text-center"><strong>Delivered</strong></th>
                            </tr>
                        </thead>
                        <tbody>
                        <!-- This part gets complicated with different use cases (additional use cases in extensions of this report):
                                1. If serial numbers are used and set to print on delivery slip => print lines as is, otherwise group them by overlapping
                                    product + description + uom combinations
                                2. If any packages are assigned => split products up by package (or non-package) and then apply use case 1 -->
                            <!-- If has destination packages => create sections of corresponding products -->
                            <t t-if="o.has_packages" name="has_packages">
                                <t t-set="packages" t-value="o.move_line_ids.mapped('result_package_id')"/>
                                <t t-foreach="packages" t-as="package">
                                    <t t-call="stock.stock_report_delivery_package_section_line"/>
                                    <t t-set="package_move_lines" t-value="o.move_line_ids.filtered(lambda l: l.result_package_id == package)"/>
                                    <!-- If printing lots/serial numbers => keep products in original lines -->
                                    <t t-if="has_serial_number">
                                        <tr t-foreach="package_move_lines" t-as="move_line">
                                            <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                        </tr>
                                       <!--  <tr class="is-total text-center">
                                            <td  colspan="1"> <strong>Total</strong>
                                            </td>
                                            <td style="position:relative;text-align:left"><t t-esc="'{0:,.2f}'.format(float(sum([p['quantity'] for p in package_move_lines])))"/></td>
                                            <td style="position:relative;text-align:left">
                                                <span t-field="o.total_quantity"/>
                                            </td>
                                        </tr> -->

                                    </t>
                                    <!-- If not printing lots/serial numbers => merge lines with same product+description+uom -->
                                    <t t-else="">
                                        <t t-set="aggregated_lines" t-value="package_move_lines._get_aggregated_product_quantities(strict=True)"/>
                                        <t t-call="stock_analytic.stock_report_delivery_aggregated_move_lines"/>
                                    </t>
                                </t>
                                <!-- Make sure we do another section for package-less products if they exist -->
                                <t t-set="move_lines" t-value="o.move_line_ids.filtered(lambda l: not l.result_package_id)"/>
                                <t t-set="aggregated_lines" t-value="o.move_line_ids._get_aggregated_product_quantities(except_package=True)"/>
                                <t t-if="move_lines or aggregated_lines" name="no_package_move_lines">
                                    <t t-call="stock.stock_report_delivery_no_package_section_line" name="no_package_section"/>
                                    <t t-if="has_serial_number">
                                        <tr t-foreach="move_lines" t-as="move_line">
                                            <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                        </tr>
                                    </t>
                                    <t t-elif="aggregated_lines">
                                        <t t-call="stock_analytic.stock_report_delivery_aggregated_move_lines"/>
                                    </t>
                                </t>
                            </t>
                            <!-- No destination packages -->
                            <t t-else="">
                                <!-- If printing lots/serial numbers => keep products in original lines -->
                                <t t-if="has_serial_number">
                                    <tr t-foreach="o.move_line_ids" t-as="move_line">
                                        <t t-call="stock.stock_report_delivery_has_serial_move_line"/>
                                    </tr>
                                </t>
                                <!-- If not printing lots/serial numbers => merge lines with same product -->
                                <t t-else="" name="aggregated_move_lines">
                                    <t t-set="aggregated_lines" t-value="o.move_line_ids._get_aggregated_product_quantities()"/>
                                    <t t-call="stock_analytic.stock_report_delivery_aggregated_move_lines"/>
                                </t>
                            </t>
                        </tbody>
                    </table>
                    <t t-set="backorders" t-value="o.backorder_ids.filtered(lambda x: x.state not in ('done', 'cancel'))"/>
                    <t t-if="o.backorder_ids and backorders">
                        <p class="mt-5">
                            <span>Remaining quantities not yet delivered:</span>
                        </p>
                        <table class="table table-sm" name="stock_backorder_table" style="table-layout: fixed;">
                            <thead>
                                <tr>
                                    <th name="th_sb_product"><strong>Product</strong></th>
                                    <th/>
                                    <th name="th_sb_quantity" class="text-center"><strong>Quantity</strong></th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="backorders" t-as="backorder">
                                    <t t-set="bo_lines" t-value="backorder.move_ids.filtered(lambda x: x.product_uom_qty)"/>
                                    <tr t-foreach="bo_lines" t-as="bo_line">
                                        <td class="w-auto">
                                            <span t-field="bo_line.product_id"/>
                                            <p t-if="bo_line.description_picking != bo_line.product_id.name and bo_line.description_picking != bo_line.product_id.display_name">
                                                <span t-field="bo_line.description_picking"/>
                                            </p>
                                        </td>
                                        <td/>
                                        <td class="text-center w-auto">
                                            <span t-field="bo_line.product_uom_qty"/>
                                            <span t-field="bo_line.product_uom"/>
                                        </td>
                                    </tr>
                                </t>
                                <tr class="is-total text-center">
                                    <td  colspan="1"> <strong>Total</strong>
                                    </td>
                                    <td style="position:relative;text-align:left">
                                        <t t-esc="'{0:,.2f}'.format(float(sum([p['product_uom_qty'] for p in bo_lines])))"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </t>
                    <br></br>
                    <br></br>
                    <table class="table table-borderless" style="border:none;width: 100%;">
                        <tbody>
                            <tr>
                                <td>صرفت من قبل</td>
                                <td>......................................................</td>
                                <td> المستلم</td>
                                <td><span t-field="o.recipient_name"/></td>
                            </tr>
                            <tr>
                                <td>إعداد: </td>
                                <td> <t t-esc="env.user.name"/></td>
                                <td>مكان التسليم</td>
                                <td>......................................................</td>
                            </tr>
                            <tr>
                                <td>إسم السائق</td>
                                <td>......................................................</td>
                                <td>التوقيع</td>
                                <td>......................................................</td>
                            </tr>
                        </tbody>
                    </table>


                    <div t-if="o.signature" class="mt32 ml64 mr4" name="signature">
                        <div class="offset-8">
                            <strong>Signature</strong>
                        </div>
                        <div class="offset-8">
                            <img t-att-src="image_data_uri(o.signature)" style="max-height: 4cm; max-width: 8cm;"/>
                        </div>
                        <div class="offset-8 text-center">
                            <p t-field="o.partner_id.name"/>
                        </div>
                    </div>
                </div>
            </t>
         </t>
     </xpath>
 </template>

        <template id="stock_report_delivery_aggregated_move_lines">
            <t t-set="total_qty_ordered" t-value="0"/>
            <t t-set="total_qty_deliver" t-value="0"/>
            <tr t-foreach="aggregated_lines" t-as="line">
                <t t-set="total_qty_ordered" t-value="total_qty_ordered + aggregated_lines[line]['qty_ordered']"/>
                <t t-set="total_qty_deliver" t-value="total_qty_deliver + aggregated_lines[line]['qty_done']"/>
                <td>
                    <span t-esc="aggregated_lines[line]['name']"/>
                    <p t-if="aggregated_lines[line]['description']">
                        <span t-esc="aggregated_lines[line]['description']"/>
                    </p>
                </td>
                <td class="text-center" name="move_line_aggregated_qty_ordered">
                    <span t-esc="aggregated_lines[line]['qty_ordered']"
                        t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"/>
                    <span t-esc="aggregated_lines[line]['product_uom'].name"/>
                </td>
                <td class="text-center" name="move_line_aggregated_qty_done">
                    <t t-if="aggregated_lines[line]['qty_done']">
                        <span t-esc="aggregated_lines[line]['qty_done']"
                            t-options="{'widget': 'float', 'decimal_precision': 'Product Unit of Measure'}"/>
                        <span t-esc="aggregated_lines[line]['product_uom'].name"/>
                    </t>
                </td>
            </tr>
            <tr class="is-total text-center">
                <td style="position:relative;text-align:center" colspan="1">Total
                </td>
                
                <td style="position:relative;text-align:center">
                    <span t-out="total_qty_ordered"/>
                </td>
                <td style="position:relative;text-align:center">
                    <span t-out="total_qty_deliver"/>
                </td>
        </tr>
    </template>
</data>
</odoo>