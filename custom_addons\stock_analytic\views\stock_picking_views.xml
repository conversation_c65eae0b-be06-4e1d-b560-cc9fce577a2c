<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_picking_form" model="ir.ui.view">
        <field name="name">stock.picking.form</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <xpath
                expr="//field[@name='move_ids_without_package']/tree/field[@name='product_id']"
                position="after"
            >
                <field
                    name="analytic_distribution"
                    widget="analytic_distribution"
                    optional="hide"
                    groups="analytic.group_analytic_accounting"
                    options="{'product_field': 'product_id', 'business_domain': 'stock_move'}"
                />
            </xpath>
              <xpath expr="//field[@name='origin']" position="after">
                <field name="deliver_name"/>
                <field name="recipient_name"/>
              </xpath>
            <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='operations']/button[@name='action_put_in_pack']" position="after">
                 <group class="o_row oe_subtotal_footer oe_right" colspan="2" name="total_quantity">
                    <label for="total_quantity"/>
                    <field name="total_quantity" nolabel="1" colspan="2" readonly="1"/>
                  </group>
            </xpath>
            <xpath expr="//form[1]/sheet[1]/notebook[1]/page[@name='operations']/field[@name='move_ids_without_package']/tree[1]/field[@name='product_uom_qty']" position="attributes">
                <attribute name="sum">total</attribute>
              </xpath>
        </field>
    </record>
</odoo>
