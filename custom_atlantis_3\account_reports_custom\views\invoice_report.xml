<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="cubes_report_invoice_custom" inherit_id="account.report_invoice_document">

        <!--        <xpath expr="//div[@name='shipping_address_block']" position="replace">-->
        <!--            &lt;!&ndash; <div groups="account.group_delivery_invoice_address" name="shipping_address_block"></div> &ndash;&gt;-->
        <!--        </xpath>-->

        <!--        <xpath expr="//t[@t-set='information_block']" position="replace">-->
        <!--            &lt;!&ndash; <t t-set="information_block"></t> &ndash;&gt;-->
        <!--        </xpath>-->

        <!--        <xpath expr="//t[@t-set='address']" position="replace">-->
        <!--            &lt;!&ndash; <t t-set="address"></t> &ndash;&gt;-->
        <!--        </xpath>-->


        <!--        <xpath expr="//div[@name='address_not_same_as_shipping']" position="replace">-->
        <!--            &lt;!&ndash; <div class="col-6" name="address_not_same_as_shipping"></div> &ndash;&gt;-->
        <!--        </xpath>-->

        <!--        <xpath expr="//div[@name='address_same_as_shipping']" position="replace">-->
        <!--            &lt;!&ndash; <div class="offset-col-6 col-6" name="address_same_as_shipping"></div> &ndash;&gt;-->
        <!--        </xpath>-->

        <!--        <xpath expr="//div[@name='no_shipping']" position="replace">-->
        <!--            &lt;!&ndash; <div class="offset-col-6 col-6" name="no_shipping"></div> &ndash;&gt;-->
        <!--        </xpath>-->

        <!--        <xpath expr="//h2/span[text()='Invoice']/ancestor::h2" position="replace">-->
        <!--        </xpath>-->

        <xpath expr="//div[hasclass('page')]" position="before">
            <div style="height:38px;float:right;margin-top:-147px;">
                <h4>
                    <span t-if="not proforma"></span>
                    <span t-else="">PROFORMA</span>
                    <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'">Invoice</span>
                    <span t-elif="o.move_type == 'out_invoice' and o.state == 'draft'">Draft Invoice</span>
                    <span t-elif="o.move_type == 'out_invoice' and o.state == 'cancel'">Cancelled Invoice</span>
                    <span t-elif="o.move_type == 'out_refund' and o.state == 'posted'">Credit Note</span>
                    <span t-elif="o.move_type == 'out_refund' and o.state == 'draft'">Draft Credit Note</span>
                    <span t-elif="o.move_type == 'out_refund' and o.state == 'cancel'">Cancelled Credit Note</span>
                    <span t-elif="o.move_type == 'in_refund'">Vendor Credit Note</span>
                    <span t-elif="o.move_type == 'in_invoice'">Vendor Bill</span>
                    <span t-if="o.name != '/'" t-field="o.name">INV/2023/0001</span>
                </h4>
            </div>
            <div style="float:right;margin-top:0px">

                <t t-if="o.partner_shipping_id and (o.partner_shipping_id != o.partner_id)">
                    <t t-set="information_block">
                        <div style="float:right" groups="account.group_delivery_invoice_address"
                             name="shipping_address_block">
                            <span style="float:right;margin-top:0px;">
                                <strong style="float:right">Shipping Address:</strong>
                                <div style="float:right" t-field="o.partner_shipping_id"
                                     t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}'/>
                            </span>
                        </div>
                    </t>
                    <span style="float:right;margin-top:0px;">
                        <t t-set="address">
                            <address class="mb-0" t-field="o.partner_id"
                                     t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}'/>
                            <div t-if="o.partner_id.vat" id="partner_vat_address_not_same_as_shipping">
                                <t t-if="o.company_id.account_fiscal_country_id.vat_label"
                                   t-out="o.company_id.account_fiscal_country_id.vat_label" id="inv_tax_id_label"/>
                                <t t-else="">Tax ID</t>:
                                <span t-field="o.partner_id.vat"/>
                            </div>
                        </t>
                    </span>
                </t>
                <t t-elif="o.partner_shipping_id and (o.partner_shipping_id == o.partner_id)">
                    <t t-set="address">
                        <span style="float:right;margin-top:0px;">
                            <address class="mb-0" style="float:right" t-field="o.partner_id"
                                     t-options='{"widget": "contact", "fields": ["name"], "no_marker": True}'/>
                        </span>
                        <br></br>
                        <div style="float:right;margin-top:1px;margin-left:200px;" t-if="o.partner_id.vat"
                             id="partner_vat_address_same_as_shipping">
                            <span style="float:right">
                                <t t-if="o.company_id.account_fiscal_country_id.vat_label"
                                   t-out="o.company_id.account_fiscal_country_id.vat_label" id="inv_tax_id_label"/>
                                <t t-else="">Tax ID</t>:
                                <span t-field="o.partner_id.vat"/>
                            </span>
                        </div>
                    </t>
                </t>
                <t t-else="">
                    <t t-set="address">
                        <span style="float:right;margin-top:0px;">
                            <address class="mb-0" t-field="o.partner_id"
                                     t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}'/>
                        </span>
                        <div t-if="o.partner_id.vat" id="partner_vat_no_shipping">
                            <span style="float:right">
                                <t t-if="o.company_id.account_fiscal_country_id.vat_label"
                                   t-out="o.company_id.account_fiscal_country_id.vat_label" id="inv_tax_id_label"/>
                                <t t-else="">Tax ID</t>:
                                <span t-field="o.partner_id.vat"/>
                            </span>
                        </div>
                    </t>
                </t>

            </div>
        </xpath>
    </template>

    <template id="report_invoice_document_inherit_balance" inherit_id="cubes_tag_account.report_invoice_document_inherit_kajo">
        <xpath expr="//table[@name='previous_current_balance']" position="replace">
            <t t-if="o.print_show_balance">
                <table style="color:white; font-family:Verdana, Arial,sans-serif;" class="table table-sm">
                    <tr>
                        <td style="background-color: #202020; color: white; font-family:Verdana, Arial,sans-serif; width:33%">
                            <span>الرصيد السابق</span>
                        </td>
                        <td style="background-color: #202020;color: white;" class="text-end o_price_total">
                            <span t-field="o.previous_balance"/>
                        </td>
                    </tr>
                    <tr>
                        <td style="background-color: #202020; color: white; font-family:Verdana, Arial,sans-serif; width:33%">
                            <span>الرصيد الحالي</span>
                        </td>
                        <td style="background-color: #202020;color: white;" class="text-end o_price_total">
                            <span t-field="o.current_balance"/>
                        </td>
                    </tr>
                </table>
            </t>
        </xpath>
    </template>


</odoo>
