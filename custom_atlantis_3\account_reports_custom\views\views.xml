<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="account_reports_custom.list">
      <field name="name">account_reports_custom list</field>
      <field name="model">account_reports_custom.account_reports_custom</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="account_reports_custom.action_window">
      <field name="name">account_reports_custom window</field>
      <field name="res_model">account_reports_custom.account_reports_custom</field>
      <field name="view_mode">tree,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="account_reports_custom.action_server">
      <field name="name">account_reports_custom server</field>
      <field name="model_id" ref="model_account_reports_custom_account_reports_custom"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="account_reports_custom" id="account_reports_custom.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="account_reports_custom.menu_1" parent="account_reports_custom.menu_root"/>
    <menuitem name="Menu 2" id="account_reports_custom.menu_2" parent="account_reports_custom.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="account_reports_custom.menu_1_list" parent="account_reports_custom.menu_1"
              action="account_reports_custom.action_window"/>
    <menuitem name="Server to list" id="account_reports_custom" parent="account_reports_custom.menu_2"
              action="account_reports_custom.action_server"/>
-->
  </data>
</odoo>
