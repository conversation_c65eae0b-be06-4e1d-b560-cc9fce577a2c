# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.exceptions import ValidationError


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    are_all_pickings_done = fields.Bo<PERSON>an(compute="_compute_are_all_pickings_done")
    are_all_bills_posted = fields.Bo<PERSON>an(compute="_compute_are_all_bills_posted")

    def button_automate_all(self):
        self.ensure_one()

        if not self.env.user.has_group("arkan_purchase_customization.arkan_po_automation"):
            raise ValidationError("You do not have permission to perform this action.")

        # Confirm Order
        order_confirmed = False
        if self.state not in ["purchase", "done"]:
            order_confirmed = self.button_confirm()

        if order_confirmed or self.state == "purchase":
            # Validate Received Product
            for delivery in self.picking_ids.filtered(lambda picking: picking.state not in ["done"]):
                delivery.button_validate()
            # Create Vendor Bill if it doesn't exist
            if not self.invoice_ids:
                vendor_bills_action = self.action_create_invoice()
                if vendor_bills_action:
                    if vendor_bills_action.get("res_id", False):
                        vendor_bills_id = vendor_bills_action["res_id"]
                        if vendor_bills_id:
                            bill_id = self.env["account.move"].browse(vendor_bills_id)
                            if bill_id.state == "draft":
                                bill_id.invoice_date = fields.Date.today()
                                bill_id.action_post()

            # If they do exist, post the ones that aren't posted
            if self.invoice_ids:
                for bill in self.invoice_ids.filtered(lambda b: b.state in ["draft"]):
                    bill.invoice_date = fields.Date.today()
                    bill.action_post()

    @api.depends("picking_ids")
    def _compute_are_all_pickings_done(self):
        for rec in self:
            rec.are_all_pickings_done = all(picking.state in ["done"] for picking in self.picking_ids)

    @api.depends("invoice_ids")
    def _compute_are_all_bills_posted(self):
        for rec in self:
            rec.are_all_bills_posted = all(bill.state in ["posted"] for bill in self.invoice_ids)

    def button_confirm(self):
        order_confirmed = super(PurchaseOrder, self).button_confirm()
        if order_confirmed:
            # If order is confirmed, update sales price on product with sales price on 'purchase.order.line'
            for line in self.order_line:
                line.product_id.update({"list_price": line.list_price})
