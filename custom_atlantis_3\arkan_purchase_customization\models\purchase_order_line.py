from odoo import fields, models, api


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"

    list_price = fields.Float(string="Sales Price", default=1.0, digits="Product Price", help="Price at which the product is sold to customers.")

    @api.onchange("product_id")
    def _onchange_product_id_set_list_price(self):
        if self.product_id:
            self.list_price = self.product_id.list_price
