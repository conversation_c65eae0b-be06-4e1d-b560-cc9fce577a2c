<?xml version="1.0"?>
<odoo>
	<data>

		<!-- Inherit Form View to Modify it -->
		<record id="arkan_automate_button_purchase_order_form_view" model="ir.ui.view">
			<field name="name">arkan_automate_button_purchase_order_form_view</field>
			<field name="model">purchase.order</field>
			<field name="inherit_id" ref="purchase.purchase_order_form"/>
			<field name="arch" type="xml">

				<xpath expr="//button[@name='button_confirm']" position="after">
					<field name="are_all_pickings_done" invisible="True"/>
					<field name="are_all_bills_posted" invisible="True"/>
					<button
							name="button_automate_all"
							type="object"
							invisible="state in ['purchase','done'] and are_all_pickings_done and are_all_bills_posted"
							string="Automate All"
							context="{'validate_analytic': True}"
							class="oe_highlight"
							groups="arkan_purchase_customization.arkan_po_automation"
					/>
				</xpath>

				<xpath expr="//field[@name='price_unit']" position="before">
					<field name="list_price" readonly="state == 'purchase' or not product_id"/>
				</xpath>

			</field>
		</record>

	</data>
</odoo>
