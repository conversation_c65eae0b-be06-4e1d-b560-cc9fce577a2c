
.pos .popup.popup-password {
    width: 280px;
    height: 461px;
}
/*


.pos .required-products{
	background: #4968bc;
	padding: 5px;
	font-size: 17px;
	color: #ffff;
}

.pos .optional-products{
	background: #34b475;
	padding: 5px;
	font-size: 17px;
	color: #ffff;
}
.pos .product-div{
	margin: 10px;
	height: 135px;
	overflow: auto;
}
.pos .optional-div{
	margin: 10px;
	height: 135px;
	overflow: auto;
}

.pos .remove-product{
	position: absolute;
	top: 2px;
	right: 2px;
	vertical-align: top;
	color: white;
	line-height: 13px;
	background: #808186;
	padding: 2px 5px;
	border-radius: 2px;
}
.raghav {
	outline: 2px solid green !important;
}

.pos .required-product {
	position:relative;
	vertical-align: top;
	display: inline-block;
	line-height: 100px;
	font-size: 11px;
	margin: 8px !important;
	width: 122px;
	height:115px;
	background:#fff;
	border: 1px solid #e2e2e2;
	border-radius: 3px;
	border-bottom-width: 3px;
	overflow: hidden;
	cursor: pointer;
	outline: 2px solid green !important;
}
.pos .required-product:focus{
	outline:none;
}

.pos .required-product .product-img {
	position: relative;
	width: 120px;
	height: 100px;
	background: white;
	text-align: center;
}

.pos .required-product .product-img img {
	max-height: 100px;
	max-width:  120px;
	vertical-align: middle;
}


.pos .optional-product {
	position:relative;
	vertical-align: top;
	display: inline-block;
	line-height: 100px;
	font-size: 11px;
	margin: 8px !important;
	width: 122px;
	height:115px;
	background:#fff;
	border: 1px solid #e2e2e2;
	border-radius: 3px;
	border-bottom-width: 3px;
	overflow: hidden;
	cursor: pointer;
}
.pos .optional-product:focus{
	outline:none;
}

.pos .optional-product .product-img {
	position: relative;
	width: 120px;
	height: 100px;
	background: white;
	text-align: center;
}

.pos .optional-product .product-img img {
	max-height: 100px;
	max-width:  120px;
	vertical-align: middle;
}


.pos .optional-product .product-name {
    position: absolute;
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
            box-sizing: border-box;
    bottom:0;
    top:auto;
    line-height: 14px;
    width:100%;
    overflow: hidden;
    text-overflow: ellipsis;
    background: -webkit-linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    background:    -moz-linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    background:     -ms-linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    */
/* troublesome in latest webkit
    background:         linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    *//*

    */
/*background:#FFF;*//*

    padding: 3px;
    padding-top:15px;
}

.pos .required-product .product-name {
    position: absolute;
    -webkit-box-sizing: border-box;
       -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
            box-sizing: border-box;
    bottom:0;
    top:auto;
    line-height: 14px;
    width:100%;
    overflow: hidden;
    text-overflow: ellipsis;
    background: -webkit-linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    background:    -moz-linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    background:     -ms-linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    */
/* troublesome in latest webkit
    background:         linear-gradient(-90deg,rgba(255,255,255,0),rgba(255,255,255,1), rgba(255,255,255,1));
    *//*

    */
/*background:#FFF;*//*

    padding: 3px;
    padding-top:15px;
}



*/
