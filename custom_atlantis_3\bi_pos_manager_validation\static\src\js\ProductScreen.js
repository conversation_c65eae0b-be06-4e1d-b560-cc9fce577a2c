/** @odoo-module **/


import { _t } from "@web/core/l10n/translation";
import { ProductScreen } from "@point_of_sale/app/screens/product_screen/product_screen";
import { patch } from "@web/core/utils/patch";
import { usePos } from "@point_of_sale/app/store/pos_hook";

patch(ProductScreen.prototype, {
    setup() {
        super.setup(...arguments);
        this.pos = usePos();
      
    },
    async onNumpadClick(buttonValue) {
        let config = this.pos.config;
        let config_otp = config.one_time_valid;
        let result = true;
        let otp =this.pos.otp;
        let order = this.pos.get_order();
        if (this.pos.numpadMode == "quantity"){
            if (!["quantity", "discount", "price","Backspace"].includes(buttonValue)){
                if (config.qty_detail){
                    if(config_otp && !otp){
                        result = await order.checkPswd();
                    }
                    if (!config_otp){
                        result = await order.checkPswd();   
                    }
                }
            }
        }

        if(buttonValue === "quantity"){
        	if (config.qty_detail){
        		if(config_otp && !otp){
        			result = await order.checkPswd();
        		}
        		if (!config_otp){
        			result = await order.checkPswd();	
        		}
        	}
        }

        if(buttonValue === "discount"){
        	if (config.discount_app){
        		if(config_otp && !otp){
        			result = await order.checkPswd();
        		}
        		if (!config_otp){
        			result = await order.checkPswd();	
        		}
        	}
        }

        if(buttonValue === "price"){
        	if (config.price_change){
        		if(config_otp && !otp){
        			result = await order.checkPswd();
        		}
        		if (!config_otp){
        			result = await order.checkPswd();	
        		}
        	}
        }


        if(result){
            super.onNumpadClick(buttonValue);
        }

    },

    async updateSelectedOrderline({ buffer, key }) {
        let config = this.pos.config;
        let config_otp = config.one_time_valid;
        let result = true;
        let otp =this.pos.otp;
        let order = this.pos.get_order();
        if(key === "Backspace" || key === "Delete"){
        	if(config.order_line_delete){
                if(config_otp && !otp){
                    result = await order.checkPswd();
                }
                if(!config_otp){
                    result = await order.checkPswd();
                }
            }else if(config.qty_detail){
                if(config_otp && !otp){
                    result = await order.checkPswd();
                }
                if(!config_otp){
                    result = await order.checkPswd();
                }
            }
        }
    if(result){
        super.updateSelectedOrderline({ buffer, key });
    }
}



    });



