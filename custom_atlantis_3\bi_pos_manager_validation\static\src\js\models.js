/** @odoo-module */

import { PosStore } from "@point_of_sale/app/store/pos_store";
import { PosOrder } from "@point_of_sale/app/models/pos_order";
import { patch } from "@web/core/utils/patch";
import { ErrorPopup } from "@point_of_sale/app/errors/error_handlers";
import { NumberPopup } from "@point_of_sale/app/utils/input_popups/number_popup";
import { _t } from "@web/core/l10n/translation";
let check_pay = true;

patch(PosStore.prototype, {
    // @Override
    async _processData(loadedData) {
        await super._processData(...arguments);
        this.users = loadedData['users'];
    },
   
});

patch(PosOrder.prototype, {
    setup() {
        super.setup(...arguments);
//        console.log(this.pos.otp);
//        this.pos.otp = false
    },

    async pay() {
    	let config = this.pos.config;
		let config_otp = config.one_time_valid;
		let result = true;
//		let otp =this.pos.otp;
		let order = this.pos.get_order();

		if(config.payment_perm && check_pay){
//			if(config_otp && !otp){
//				result = await order.checkPswd();
//			}
			if(!config_otp){
				result = await order.checkPswd();
			}
		}
		if(result){
			super.pay();
		}
    },

	async checkPswd(){
		let self = this;
		let res = false;
		const { confirmed, payload } = await self.env.services.popup.add(NumberPopup, {
			title: _t('Manager Password'),
			isPassword: true,
		});
		if (confirmed) {
			let user_passd;
			let users = self.pos.config.user_id;
			for (let i = 0; i < self.pos.users.length; i++) {
				if (self.pos.users[i].id === users[0]) {
					user_passd = self.pos.users[i].pos_security_pin;
				}
			}
			if (payload == user_passd){
				res =  true;
				self.pos.otp = true;
			}else{
				self.env.services.popup.add(ErrorPopup, {
					title: _t('Invalid Password'),
					body: _t('Wrong Password'),
				});
				return false;
			}
		}
		return res;
	},

});