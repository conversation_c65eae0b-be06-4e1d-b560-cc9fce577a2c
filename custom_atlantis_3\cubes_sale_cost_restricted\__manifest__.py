# -*- coding: utf-8 -*-
{
    'name': "Cubes Sale Cost Restricted",

    'summary': "restricted  the user to sell in cost price and less than it (make like warring for that to popup)",

    'description': """
restricted  the user to sell in cost price and less than it (make like warring for that to popup)
make a access right to be given for user to sell with any price  
    """,

    'author': "Cubes",
    'website': "",

    'category': 'Sale',
    'version': '18.1',

    # any module necessary for this one to work correctly
    'depends': ['base', 'sale_management', 'point_of_sale'],

    # always loaded
    'data': [
        'security/security_group.xml',
        'views/assets.xml',
    ],
    'assets': {
        'point_of_sale._assets_pos': [
            'cubes_sale_cost_restricted/static/src/js/restrict_price.js',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',

}
