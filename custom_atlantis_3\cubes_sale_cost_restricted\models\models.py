from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class SaleOrderinherit(models.Model):
    _inherit = 'sale.order'

    def action_confirm(self):
        if self.env.user.has_group('cubes_sale_cost_restricted.group_restricted_sale_cost'):
            if any(line.price_unit <= line.product_id.standard_price for line in self.order_line):
                raise UserError(_("Sale price should be more than the cost price."))
        return super().action_confirm()

