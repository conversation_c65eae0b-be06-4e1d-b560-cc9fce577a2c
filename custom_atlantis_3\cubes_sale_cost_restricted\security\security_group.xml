<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="group_restricted_sale_cost" model="res.groups">
            <field name="name">Restricted Sale Cost Group</field>
        </record>
        <record id="group_pos_sale_cost_custom" model="res.groups">
            <field name="name">PoS Cost</field>
            <field name="category_id" ref="base.module_category_point_of_sale"/>
        </record>
    </data>
</odoo>