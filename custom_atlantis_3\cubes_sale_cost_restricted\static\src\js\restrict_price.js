/** @odoo-module */
import { patch } from "@web/core/utils/patch";
import { AlertDialog } from "@web/core/confirmation_dialog/confirmation_dialog";
import { PosStore } from "@point_of_sale/app/store/pos_store";
import { _t } from "@web/core/l10n/translation";

patch(PosStore.prototype, {
    async pay() {
        let order = this.get_order();
        let lines = order.get_orderlines();
        let restrict_order = false;
        let product_names = '';

        // Check the "Allow Override Warning" setting from pos.config
        let posConfig = this.config;
        let allowOverride = posConfig.allow_override_warning; // Fetch the boolean value

        if (order && lines.length > 0) {
            lines.forEach(function (line) {
                const product = line.get_product();
                if (product) {
                    const sale_price = line.get_display_price();
                    const cost_price = product.standard_price;
                    if (sale_price <= cost_price) {
                        restrict_order = true;
                        product_names += `\n ${product.display_name}`;
                    }
                }
            });
        } else {
            restrict_order = true;
        }

        // Reverse logic: Show warning when allow_override_warning is enabled (True)
        if (restrict_order && allowOverride) {
            let message = product_names
                ? `⚠️ Warning: The following products have a sale price less than or equal to cost price:${product_names}`
                : "❌ Order cannot be processed because of invalid pricing.";

            this.dialog.add(AlertDialog, {
                title: _t("⚠ Warning"),
                body: _t(message),
                confirmLabel: _t("OK"),
            });
        } else {
            super.pay();
        }
    },
});
