from odoo import models, fields, api


class SaleReportInherit(models.Model):
    _inherit = 'sale.report'

    brand_id = fields.Many2one('product.brand', string='Brand', readonly=True)
    employee_id = fields.Many2one('hr.employee', string='Employee', readonly=True)

    def _select_additional_fields(self):
        res = super()._select_additional_fields()
        res['brand_id'] = "t.brand_id"
        res['employee_id'] = "s.employee_id"
        return res

    def _group_by_sale(self):
        res = super()._group_by_sale()
        res += """, t.brand_id, s.employee_id"""
        return res
