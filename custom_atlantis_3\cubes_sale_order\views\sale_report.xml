<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Brand to Sale Report Search View -->
    <record id="view_order_product_search_inherit_brand" model="ir.ui.view">
        <field name="name">sale.report.search.inherit.brand</field>
        <field name="model">sale.report</field>
        <field name="inherit_id" ref="sale.view_order_product_search"/>
        <field name="arch" type="xml">
            <!-- Add brand field to searchable fields -->
            <xpath expr="//field[@name='date']" position="after">
                <field name="brand_id"/>
                <field name="employee_id"/>
            </xpath>

            <xpath expr="//filter[@name='Sales']" position="after">
                <filter string="Brand" name="brand_filter" domain="[('brand_id','!=', False)]"/>
                <filter string="Employee" name="employee_filter" domain="[('employee_id','!=', False)]"/>
            </xpath>

            <xpath expr="//filter[@name='Customer']" position="after">
                <filter string="Brand" name="brand" domain="[]" context="{'group_by':'brand_id'}"/>
                <filter string="Employee" name="employee" domain="[]" context="{'group_by':'employee_id'}"/>
            </xpath>
        </field>
    </record>

    <!-- Add Brand to Sale Report Pivot View -->
    <record id="view_order_product_pivot_inherit_brand" model="ir.ui.view">
        <field name="name">sale.report.pivot.inherit.brand</field>
        <field name="model">sale.report</field>
        <field name="inherit_id" ref="sale.view_order_product_pivot"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='team_id']" position="after">
                <field name="brand_id" type="row"/>
            </xpath>
        </field>
    </record>

    <!-- Add Brand to Sale Report Graph View -->
    <record id="view_order_product_graph_inherit_brand" model="ir.ui.view">
        <field name="name">sale.report.graph.inherit.brand</field>
        <field name="model">sale.report</field>
        <field name="inherit_id" ref="sale.view_order_product_graph"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_uom_qty']" position="before">
                <field name="brand_id"/>
            </xpath>
        </field>
    </record>
</odoo>