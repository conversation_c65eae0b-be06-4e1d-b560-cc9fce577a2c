from odoo import models, fields, api


class Account<PERSON>ove(models.Model):
    _inherit = 'account.move'

    previous_balance = fields.Monetary(compute='_compute_previous_balance')
    current_balance = fields.Monetary(related='partner_id.credit', string='Current Balance')

    @api.depends('current_balance', 'state')
    def _compute_previous_balance(self):
        for rec in self:
            rec.previous_balance = rec.current_balance - rec.amount_total
