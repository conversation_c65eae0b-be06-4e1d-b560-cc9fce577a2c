from odoo import models, fields, api
import logging
_logger = logging.getLogger(__name__)


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    product_length = fields.Float(string='Length',digits='Cubes Unit of Measure')
    product_width = fields.Float(string='Width',digits='Cubes Unit of Measure')
    product_qnt = fields.Integer(string='Quantity',default=1)
    tag_area = fields.Float('Area',
                            compute='_compute_tag_area',
                            store=True,
                            digits='Cubes Unit of Measure')

    quantity = fields.Float(
        string='Pick Quantity',
        compute='_compute_product_uom_qty',
        store=True,
        digits='Cubes Unit of Measure'
    )

    @api.depends('product_length', 'product_width', 'product_id')
    def _compute_tag_area(self):
        for line in self:
            if line.product_length and line.product_width:
                line.tag_area = line.product_length * line.product_width
            else:
                line.tag_area = 0.0

    @api.depends('tag_area', 'product_qnt')
    def _compute_product_uom_qty(self):
        for line in self:
            if line.tag_area and line.product_qnt:
                line.quantity = line.tag_area * line.product_qnt  # Type cast to integer
            else:
                line.quantity = 1.0
