<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="report_invoice_document_inherit" inherit_id="account.report_invoice_document">

<!--        <div id="informations" position="inside">-->
<!--            <div class="col-auto col-3 mw-100 mb-2" name="approved">-->
<!--                <strong>Approved:</strong>-->
<!--                <p class="m-0">.................</p>-->
<!--            </div>-->
<!--            <div class="col-auto col-3 mw-100 mb-2" name="recipient">-->
<!--                <strong>Recipient:</strong>-->
<!--                <p class="m-0">.................</p>-->
<!--            </div>-->
<!--        </div>-->
<!--        <xpath expr="//td[@name='td_quantity']/span[@t-field='line.product_uom_id']" position="replace"/>-->

        <xpath expr="//td[@name='td_quantity']" position="replace">
            <td name="td_quantity" class="text-end">
                <span t-field="line.quantity">3.00</span>
            </td>
        </xpath>

        <xpath expr="//th[@name='th_taxes']" position="replace">
        </xpath>

         <!-- </xpath> -->
            <xpath expr="//td[@name='td_taxes']" position="replace">
        </xpath>

        <xpath expr="//th[@name='th_description']" position="after">
            <th name="th_length" class="text-end">الطول</th>
            <th name="th_width" class="text-end">العرض</th>
        </xpath>

        <xpath expr="//td[@name='td_quantity']/span[1]" position="replace">
            <!-- <span t-field="line.product_qnt" /> -->
            <span t-esc="'{:,.3f}'.format(line.product_qnt)" />
        </xpath>


        <xpath expr="//th[@name='th_quantity']" position="after">
            <th name="th_pick_quantity" class="text-end">المساحة الإجمالية</th>
        </xpath>

        <xpath expr="//td[@name='td_quantity']" position="after">
            <td class="text-end">
                <span t-field="line.product_uom_id"  groups="uom.group_uom">units</span>
                <span t-esc="'{:,.3f}'.format(line.quantity)" />
            </td>
        </xpath>


        <!-- <xpath expr="//th[@name='th_quantity']" position="replace">
            <th name="th_pick_quantity" class="text-end">Total Area</th>
        </xpath>

         <xpath expr="//th[@name='th_pick_quantity']" position="before">
            <th name="th_quantity_product" class="text-end">Quantity</th>
        </xpath> -->

       <!--  <xpath expr="//td[@name='td_quantity']" position="before">

             <td class="text-end" style="text-align: center;">
                <span name="td_product_quantity" t-field="line.product_qnt" />
            </td>
        </xpath> -->

        <xpath expr="//td[@name='account_invoice_line_name']" position="after">
            <td class="text-end">
                <!-- <span t-field="line.product_length" /> -->
                <span t-esc="'{:,.3f}'.format(line.product_length)" />
            </td>
            <td class="text-end">
                <!-- <span t-field="line.product_width" /> -->
                <span t-esc="'{:,.3f}'.format(line.product_width)" />
            </td>
        </xpath>
        <xpath expr="//tbody[hasclass('invoice_tbody')]" position="before">
            <t t-set="total_product_length" t-value="0.0"/>
            <t t-set="total_product_width" t-value="0.0"/>
            <t t-set="total_quantity" t-value="0.0"/>
             <t t-set="total_tag_area" t-value="0.0"/>
            <t t-set="total_product_qnt" t-value="0.0"/>

        </xpath>


         <xpath expr="//tbody[hasclass('invoice_tbody')]" position="after">
            <!-- <tfoot> -->
                <tr>
                     <td width="50%">TOTAL</td>
                    <td class="text-left">
<!--                        <span t-esc="'{:,.3f}'.format(total_product_length)" />-->
                    </td>
                    <td class="text-left">
<!--                        <span t-esc="'{:,.3f}'.format(total_product_width)" />-->
                    </td>
                    <td class="text-right" style="text-align: center;">
                        <span t-esc="'{:,.3f}'.format(total_product_qnt)" />
                    </td>
                    <td class="text-left">
                        <span t-field="line.product_uom_id"  groups="uom.group_uom">units</span>
                        <span t-esc="'{:,.3f}'.format(total_quantity)" />
                    </td>
                     <td class="text-left">
<!--                        <span t-esc="'{:,.4f}'.format(total_tag_area)" />-->
                    </td>
<!--                    <td></td>-->
                    <td></td>
                </tr>
            <!-- </tfoot> -->
        </xpath>

        <xpath expr="//td[@name='td_quantity']" position="inside">
            <t t-set="total_product_length" t-value="total_product_length+line.product_length"/>
            <t t-set="total_product_width" t-value="total_product_width+line.product_width"/>
            <t t-set="total_product_qnt" t-value="total_product_qnt+line.product_qnt"/>
            <t t-set="total_quantity" t-value="total_quantity+line.quantity"/>
            <t t-set="total_tag_area" t-value="total_tag_area+line.tag_area"/>
        </xpath>

<!--         <table name="invoice_line_table" position="after">
            <table width="100%" class="table table-sm">
              <tr>
                    <td width="50%">TOTAL</td>
                    <td width="9%" class="text-center">
                        <span t-esc="'{:,.4f}'.format(total_product_length)" />
                    </td>
                    <td width="8%" class="text-center">
                        <span t-esc="'{:,.4f}'.format(total_product_width)" />
                    </td>
                    <td width="12.4%" class="text-center">
                        <span t-esc="'{:,.4f}'.format(total_quantity)" />
                    </td>
                    <td width="13.4%" class="text-center">
                        <span t-esc="'{:,.4f}'.format(total_tag_area)" />
                    </td>

                </tr>
            </table>
            <br />
        </table> -->

    </template>

</odoo>
