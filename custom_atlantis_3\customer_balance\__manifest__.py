{
    'name': 'Customer Balance',
    'version': '1.0',
    'category': 'Sales/Accounting',
    'summary': 'Show customer balance in sales orders, invoices and payments',
    'description': """
        This module adds a computed field to show customer balance in:
        - Sales Orders
        - Customer Invoices
        - Customer Payments
        - Partner Ledger Report
        - Updates automatically when customer changes
        - Color-coded balance (red for debt, green for credit/zero)
    """,
    'depends': ['sale', 'account', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'views/sale_order_views.xml',
        'views/account_move_views.xml',
        'views/account_payment_views.xml',
        'reports/partner_ledger_report.xml',
        'wizard/partner_ledger_wizard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'customer_balance/static/src/css/partner_ledger.css',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
} 