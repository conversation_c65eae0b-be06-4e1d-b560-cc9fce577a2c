from odoo import models, fields, api


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    partner_balance = fields.Monetary(
        string='Customer Balance',
        compute='_compute_partner_balance',
        store=False,
        help='Shows the current balance of the customer (positive means customer owes money)',
    )

    @api.depends('partner_id')
    def _compute_partner_balance(self):
        for payment in self:
            if payment.partner_id:
                domain = [
                    ('partner_id', '=', payment.partner_id.id),
                    ('account_id.account_type', '=', 'asset_receivable'),
                    ('move_id.state', '=', 'posted')
                ]
                
                move_lines = self.env['account.move.line'].search(domain)
                debit_sum = sum(move_lines.mapped('debit'))
                credit_sum = sum(move_lines.mapped('credit'))
                
                payment.partner_balance = debit_sum - credit_sum
            else:
                payment.partner_balance = 0.0 