from odoo import models, fields, api
from datetime import date


class PartnerLedgerLine(models.TransientModel):
    _name = 'partner.ledger.line'
    _description = 'Partner Ledger Line'
    _order = 'date asc, id asc'

    date = fields.Date(string='Date')
    name = fields.Char(string='Description')
    debit = fields.Monetary(string='Debit', default=0.0)
    credit = fields.Monetary(string='Credit', default=0.0)
    balance = fields.Monetary(string='Balance', default=0.0)
    is_balance_line = fields.Boolean(string='Is Balance Line', default=False)
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                default=lambda self: self.env.company.currency_id)
    partner_id = fields.Many2one('res.partner', string='Partner')
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user)
    session_id = fields.Char(string='Session ID')
    ending_balance = fields.Monetary(string='Ending Balance', compute='_compute_ending_balance')

    @api.depends('partner_id', 'session_id')
    def _compute_env_ref_lines(self):
        for record in self:
            # Get all lines for current session
            lines = self.search([
                ('session_id', '=', record.session_id)
            ], order='date asc, id asc')
            
            record.env_ref_lines = lines

    @api.depends('env_ref_lines.balance')
    def _compute_ending_balance(self):
        for record in self:
            lines = self.search([
                ('session_id', '=', record.session_id),
                ('is_balance_line', '=', True),
                ('name', '=', 'Ending Balance')
            ], limit=1)
            record.ending_balance = lines.balance if lines else 0.0

    env_ref_lines = fields.Many2many('partner.ledger.line', compute='_compute_env_ref_lines')

    def action_print_ledger(self):
        self.ensure_one()
        wizard = self.env['partner.ledger.wizard'].create({
            'partner_id': self.partner_id.id,
            'date_from': date(date.today().year, 1, 1),
            'date_to': fields.Date.today(),
        })
        return self.env.ref('customer_balance.action_partner_ledger_report').report_action(wizard)
 