from odoo import models, fields, api
from datetime import datetime, date, timedelta


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    partner_balance = fields.Monetary(
        string='Customer Balance',
        compute='_compute_partner_balance',
        store=False,
        help='Shows the current balance of the customer (positive means customer owes money)',
    )

    @api.depends('partner_id')
    def _compute_partner_balance(self):
        for order in self:
            if order.partner_id:
                # Get all account move lines for this partner
                domain = [
                    ('partner_id', '=', order.partner_id.id),
                    ('account_id.account_type', '=', 'asset_receivable'),
                    ('move_id.state', '=', 'posted')
                ]
                
                move_lines = self.env['account.move.line'].search(domain)
                debit_sum = sum(move_lines.mapped('debit'))
                credit_sum = sum(move_lines.mapped('credit'))
                
                order.partner_balance = debit_sum - credit_sum
            else:
                order.partner_balance = 0.0

    def action_view_partner_ledger(self):
        self.ensure_one()
        first_day = date(date.today().year, 1, 1)
        
        # Get the last transaction date
        last_transaction = self.env['account.move.line'].search([
            ('partner_id', '=', self.partner_id.id),
            ('account_id.account_type', '=', 'asset_receivable'),
            ('move_id.state', '=', 'posted'),
        ], order='date desc', limit=1)
        
        last_date = last_transaction.date if last_transaction else fields.Date.today()
        
        # Generate unique session ID for this view
        session_id = fields.Datetime.now().strftime('%Y%m%d%H%M%S') + f"-{self.env.user.id}-{self.partner_id.id}"
        
        # Clean up old ledger lines
        self.env['partner.ledger.line'].search([
            ('user_id', '=', self.env.user.id),
            ('session_id', '!=', session_id),
        ]).unlink()
        
        # Calculate opening balance
        opening_domain = [
            ('partner_id', '=', self.partner_id.id),
            ('account_id.account_type', '=', 'asset_receivable'),
            ('move_id.state', '=', 'posted'),
            ('date', '<', first_day),
        ]
        opening_lines = self.env['account.move.line'].search(opening_domain)
        opening_balance = sum(opening_lines.mapped('debit')) - sum(opening_lines.mapped('credit'))

        # Create opening balance line
        LedgerLine = self.env['partner.ledger.line']
        LedgerLine.create({
            'date': first_day,
            'name': 'Opening Balance',
            'debit': opening_balance if opening_balance > 0 else 0,
            'credit': -opening_balance if opening_balance < 0 else 0,
            'balance': opening_balance,
            'is_balance_line': True,
            'partner_id': self.partner_id.id,
            'user_id': self.env.user.id,
            'session_id': session_id,
        })

        # Get and create transaction lines
        domain = [
            ('partner_id', '=', self.partner_id.id),
            ('account_id.account_type', '=', 'asset_receivable'),
            ('move_id.state', '=', 'posted'),
            ('date', '>=', first_day),
            ('date', '<=', last_date)
        ]
        
        move_lines = self.env['account.move.line'].search(domain, order='date, id')
        balance = opening_balance
        for line in move_lines:
            balance += line.debit - line.credit
            LedgerLine.create({
                'date': line.date,
                'name': f"{line.move_id.name} - {line.name or ''}",
                'debit': line.debit,
                'credit': line.credit,
                'balance': balance,
                'is_balance_line': False,
                'partner_id': self.partner_id.id,
                'user_id': self.env.user.id,
                'session_id': session_id,
            })
        
        # Create ending balance line with last transaction date
        LedgerLine.create({
            'date': last_date,  # Use last transaction date
            'name': 'Ending Balance',
            'debit': 0,
            'credit': 0,
            'balance': balance,
            'is_balance_line': True,
            'partner_id': self.partner_id.id,
            'user_id': self.env.user.id,
            'session_id': session_id,
        })
        
        # Return the action
        action = self.env.ref('customer_balance.action_partner_ledger_line').read()[0]
        action.update({
            'domain': [
                ('session_id', '=', session_id)  # Only filter by session_id
            ],
            'context': {
                'default_partner_id': self.partner_id.id,
                'default_session_id': session_id,  # Add default session_id
                'create': False,
                'edit': False,
                'delete': False,
            },
            'target': 'current',
        })
        return action