from odoo import models


class PartnerLedgerReport(models.AbstractModel):
    _name = 'report.customer_balance.report_partner_ledger'
    _description = 'Partner Ledger Report'

    def _get_report_values(self, docids, data=None):
        docs = self.env['partner.ledger.wizard'].browse(docids)
        domain = [
            ('partner_id', '=', docs.partner_id.id),
            ('account_id.account_type', '=', 'asset_receivable'),
            ('move_id.state', '=', 'posted'),
            ('date', '>=', docs.date_from),
            ('date', '<=', docs.date_to),
        ]
        
        move_lines = self.env['account.move.line'].search(domain, order='date')
        
        # Calculate opening balance
        opening_domain = [
            ('partner_id', '=', docs.partner_id.id),
            ('account_id.account_type', '=', 'asset_receivable'),
            ('move_id.state', '=', 'posted'),
            ('date', '<', docs.date_from),
        ]
        opening_move_lines = self.env['account.move.line'].search(opening_domain)
        opening_balance = sum(opening_move_lines.mapped('debit')) - sum(opening_move_lines.mapped('credit'))
        
        # Prepare lines with running balance
        lines = []
        balance = opening_balance
        for line in move_lines:
            balance = balance + line.debit - line.credit
            lines.append({
                'date': line.date,
                'name': line.move_id.name + ' - ' + (line.name or ''),
                'debit': line.debit,
                'credit': line.credit,
                'balance': balance,
            })
            
        return {
            'docs': docs,
            'opening_balance': opening_balance,
            'lines': lines,
        } 