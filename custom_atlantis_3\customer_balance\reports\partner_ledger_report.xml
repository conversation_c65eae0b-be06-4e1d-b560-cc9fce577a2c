<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_partner_ledger">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.external_layout">
                    <div class="page">
                        <!-- Header Section -->
                        <div class="row mb-4">
                            <div class="col-12 text-center">
                                <h3 class="mt-2 mb-4">Partner Ledger Statement</h3>
                            </div>
                        </div>

                        <!-- Partner Info Section -->
                        <div class="row mb-4">
                            <div class="col-6">
                                <strong class="text-muted">Partner:</strong>
                                <p class="m-0" t-field="o.partner_id.name"/>
                                <p class="m-0" t-field="o.partner_id.street"/>
                                <p class="m-0" t-field="o.partner_id.city"/>
                                <p class="m-0" t-field="o.partner_id.country_id"/>
                            </div>
                            <div class="col-6 text-right">
                                <div class="mb-2">
                                    <strong class="text-muted">Period:</strong>
                                    <p class="m-0">
                                        From: <span t-field="o.date_from" class="ml-2"/>
                                    </p>
                                    <p class="m-0">
                                        To: <span t-field="o.date_to" class="ml-2"/>
                                    </p>
                                </div>
                                <div class="mt-3">
                                    <strong class="text-muted">Print Date:</strong>
                                    <p class="m-0" t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d')"/>
                                </div>
                            </div>
                        </div>

                        <!-- Transactions Table -->
                        <div class="row">
                            <div class="col-12">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr class="bg-light">
                                            <th class="text-center">Date</th>
                                            <th class="text-left">Description</th>
                                            <th class="text-right">Debit</th>
                                            <th class="text-right">Credit</th>
                                            <th class="text-right">Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Opening Balance Row -->
                                        <tr class="bg-light">
                                            <td colspan="4" class="text-left">
                                                <strong>Opening Balance</strong>
                                            </td>
                                            <td class="text-right">
                                                <strong t-esc="opening_balance" 
                                                    t-options='{"widget": "monetary", "display_currency": o.partner_id.currency_id}'
                                                    t-attf-class="#{opening_balance > 0 and 'text-danger' or 'text-success'}"/>
                                            </td>
                                        </tr>
                                        <!-- Transaction Lines -->
                                        <t t-foreach="lines" t-as="line">
                                            <tr>
                                                <td class="text-center">
                                                    <span t-esc="line['date']" t-options='{"widget": "date"}'/>
                                                </td>
                                                <td class="text-left">
                                                    <span t-esc="line['name']"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-if="line['debit']" t-esc="line['debit']" 
                                                        t-options='{"widget": "monetary", "display_currency": o.partner_id.currency_id}'/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-if="line['credit']" t-esc="line['credit']" 
                                                        t-options='{"widget": "monetary", "display_currency": o.partner_id.currency_id}'/>
                                                </td>
                                                <td class="text-right">
                                                    <strong t-esc="line['balance']" 
                                                        t-options='{"widget": "monetary", "display_currency": o.partner_id.currency_id}'
                                                        t-attf-class="#{line['balance'] > 0 and 'text-danger' or 'text-success'}"/>
                                                </td>
                                            </tr>
                                        </t>
                                        <!-- Final Balance Row -->
                                        <tr class="bg-light">
                                            <td colspan="4" class="text-right">
                                                <strong>Ending Balance</strong>
                                            </td>
                                            <td class="text-right">
                                                <strong t-esc="lines and lines[-1]['balance'] or opening_balance" 
                                                    t-options='{"widget": "monetary", "display_currency": o.partner_id.currency_id}'
                                                    t-attf-class="#{(lines and lines[-1]['balance'] or opening_balance) > 0 and 'text-danger' or 'text-success'}"/>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Footer Notes -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <p class="text-muted small">
                                    <strong>Note:</strong> This statement reflects all posted transactions for the specified period.
                                    Positive balance (red) indicates amount owed by partner. Negative balance (green) indicates credit balance.
                                </p>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <record id="action_partner_ledger_report" model="ir.actions.report">
        <field name="name">Partner Ledger</field>
        <field name="model">partner.ledger.wizard</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">customer_balance.report_partner_ledger</field>
        <field name="report_file">customer_balance.report_partner_ledger</field>
        <field name="binding_model_id" ref="model_partner_ledger_wizard"/>
        <field name="binding_type">report</field>
    </record>
</odoo> 