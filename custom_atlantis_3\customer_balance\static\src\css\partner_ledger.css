.o_partner_ledger tr[data-is_balance_line="true"] {
    background-color: #f8f9fa !important;
    font-weight: bold;
}

.o_partner_ledger tr[data-is_balance_line="true"] td {
    border-top: 2px solid #dee2e6 !important;
    border-bottom: 2px solid #dee2e6 !important;
}

.o_partner_ledger tr[data-name="Opening Balance"] {
    background-color: #e9ecef !important;
}

.o_partner_ledger tr[data-name="Ending Balance"] {
    background-color: #e9ecef !important;
}

.o_partner_ledger .text-danger {
    font-weight: bold;
}

.o_partner_ledger .text-success {
    font-weight: bold;
}

.o_partner_ledger td {
    padding: 0.5rem !important;
}

.o_partner_ledger th {
    background-color: #f8f9fa !important;
    font-weight: bold !important;
}

/* Opening Balance and Ending Balance rows */
.o_list_view .o_data_row[data-id] td[data-name="name"]:contains('Opening Balance'),
.o_list_view .o_data_row[data-id] td[data-name="name"]:contains('Ending Balance') {
    background-color: #e9ecef !important;
}

/* Make entire row have background color */
.o_list_view .o_data_row[data-id]:has(td[data-name="name"]:contains('Opening Balance')),
.o_list_view .o_data_row[data-id]:has(td[data-name="name"]:contains('Ending Balance')) {
    background-color: #e9ecef !important;
    font-weight: bold !important;
}

/* Headers */
.o_list_view thead tr th {
    background-color: #f8f9fa !important;
    font-weight: bold !important;
    padding: 8px !important;
}

/* All cells */
.o_list_view td {
    padding: 8px !important;
}

/* Monetary values */
.o_list_view td.o_monetary_cell {
    font-weight: 500 !important;
}

/* Positive values (debits) */
.o_list_view td.text-danger {
    color: #dc3545 !important;
    font-weight: bold !important;
}

/* Negative values (credits) */
.o_list_view td.text-success {
    color: #28a745 !important;
    font-weight: bold !important;
}

/* Balance line borders */
.o_list_view .o_data_row[data-id]:has(td[data-name="name"]:contains('Opening Balance')),
.o_list_view .o_data_row[data-id]:has(td[data-name="name"]:contains('Ending Balance')) {
    border-top: 2px solid #dee2e6 !important;
    border-bottom: 2px solid #dee2e6 !important;
}

/* Hover effect */
.o_list_view .o_data_row:hover {
    background-color: #f5f5f5 !important;
}

/* Balance line hover */
.o_list_view .o_data_row[data-id]:has(td[data-name="name"]:contains('Opening Balance')):hover,
.o_list_view .o_data_row[data-id]:has(td[data-name="name"]:contains('Ending Balance')):hover {
    background-color: #e2e6ea !important;
} 