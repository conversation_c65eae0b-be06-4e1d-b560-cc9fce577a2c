<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_inherit_customer_balance" model="ir.ui.view">
        <field name="name">account.move.form.inherit.customer.balance</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="partner_balance" 
                       decoration-danger="partner_balance &gt; 0"
                       decoration-success="partner_balance &lt;= 0"
                       invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')"/>
            </xpath>
        </field>
    </record>
</odoo> 