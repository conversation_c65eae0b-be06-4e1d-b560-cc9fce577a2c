<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List view for Partner Ledger -->
    <record id="view_partner_ledger_list" model="ir.ui.view">
        <field name="name">partner.ledger.list</field>
        <field name="model">partner.ledger.line</field>
        <field name="arch" type="xml">
            <list string="Partner Ledger" create="0" edit="0" delete="0">
                <field name="date" string="Date"/>
                <field name="name" string="Description"/>
                <field name="debit" sum="Total Debit" widget="monetary" decoration-danger="debit &gt; 0"/>
                <field name="credit" sum="Total Credit" widget="monetary" decoration-success="credit &gt; 0"/>
                <field name="balance" widget="monetary" decoration-danger="balance &gt; 0" decoration-success="balance &lt;= 0"/>
                <field name="currency_id" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Form view with Print Button -->
    <record id="view_partner_ledger_form" model="ir.ui.view">
        <field name="name">partner.ledger.form</field>
        <field name="model">partner.ledger.line</field>
        <field name="arch" type="xml">
            <form string="Partner Ledger" create="0" edit="0" delete="0">
                <header>
                    <button name="action_print_ledger" 
                            type="object" 
                            string="Print PDF" 
                            class="btn-primary"/>
                    <div class="oe_right oe_button_box" style="margin-left: 10px;">
                        <strong>Ending Balance: </strong>
                        <field name="ending_balance" 
                               class="oe_inline" 
                               readonly="1"
                               decoration-danger="ending_balance &gt; 0"
                               decoration-success="ending_balance &lt;= 0"
                               nolabel="1"/>
                    </div>
                </header>
                <sheet>
                    <group>
                        <field name="partner_id" invisible="1"/>
                    </group>
                    <notebook>
                        <page string="Ledger Lines">
                            <field name="env_ref_lines" mode="list" readonly="1">
                                <list>
                                    <field name="date" string="Date"/>
                                    <field name="name" string="Description"/>
                                    <field name="debit" sum="Total Debit" widget="monetary" decoration-danger="debit &gt; 0"/>
                                    <field name="credit" sum="Total Credit" widget="monetary" decoration-success="credit &gt; 0"/>
                                    <field name="balance" widget="monetary" decoration-danger="balance &gt; 0" decoration-success="balance &lt;= 0"/>
                                    <field name="currency_id" invisible="1"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for Partner Ledger -->
    <record id="action_partner_ledger_line" model="ir.actions.act_window">
        <field name="name">Partner Ledger</field>
        <field name="res_model">partner.ledger.line</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_partner_ledger_form"/>
        <field name="target">current</field>
    </record>

    <!-- Update Sales Order Form -->
    <record id="view_order_form_inherit_customer_balance" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.customer.balance</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_partner_ledger"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-money"
                        invisible="not partner_id">
                    <field name="partner_balance" 
                           widget="statinfo" 
                           string="Customer Balance"
                           decoration-danger="partner_balance &gt; 0"
                           decoration-success="partner_balance &lt;= 0"/>
                </button>
            </xpath>
        </field>
    </record>
</odoo> 