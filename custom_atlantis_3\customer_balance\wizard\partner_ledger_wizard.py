from odoo import api, fields, models
from datetime import datetime


class PartnerLedgerWizard(models.TransientModel):
    _name = 'partner.ledger.wizard'
    _description = 'Partner Ledger Report Wizard'

    partner_id = fields.Many2one('res.partner', string='Partner', required=True)
    date_from = fields.Date(string='Start Date', required=True)
    date_to = fields.Date(string='End Date', required=True)

    @api.model
    def default_get(self, fields):
        res = super(PartnerLedgerWizard, self).default_get(fields)
        res.update({
            'date_from': datetime.now().replace(day=1),
            'date_to': datetime.now(),
        })
        return res

    def print_report(self):
        self.ensure_one()
        return self.env.ref('customer_balance.action_partner_ledger_report').report_action(self) 