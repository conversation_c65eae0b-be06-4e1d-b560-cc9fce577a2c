<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Override the reference field in the invoice header section -->
    <template id="report_invoice_document_no_ref" inherit_id="account.report_invoice_document">
        <!-- Hide invoice number in the document -->
        <xpath expr="//span[@t-field='o.name']" position="attributes">
            <attribute name="t-if">not o.no_show_invoice_ref</attribute>
        </xpath>
        
        <!-- Hide reference field in the invoice information section -->
        <xpath expr="//div[@id='informations']/div[@name='reference']" position="attributes">
            <attribute name="t-if">o.ref and not o.no_show_invoice_ref</attribute>
        </xpath>
        
        <!-- Hide payment communication section -->
        <xpath expr="//div[hasclass('mb-3')][p[@name='payment_communication']]" position="attributes">
            <attribute name="t-if">o.move_type in ('out_invoice', 'in_refund') and o.payment_reference and not o.no_show_invoice_ref</attribute>
        </xpath>
    </template>
</odoo> 