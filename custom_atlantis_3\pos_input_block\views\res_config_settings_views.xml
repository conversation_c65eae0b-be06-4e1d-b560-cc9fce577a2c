<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">
            res.config.settings.view.form.inherit.pos.block.input.products
        </field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="point_of_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@id='pos_interface_section']" position="after">
                <div class="row mt16 o_settings_container">
                    <h2>POS Products List</h2>
                    <div class="col-12 col-lg-6 o_setting_box">
                        <div class="o_setting_left_pane">
                            <field name="input_block" readonly="False"/>
                        </div>
                        <div class="o_setting_right_pane">
                            <label for="input_block"/>
                            <div class="text-muted">
                                Block the Input when selecting the produtcs in POS Product Lists
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
