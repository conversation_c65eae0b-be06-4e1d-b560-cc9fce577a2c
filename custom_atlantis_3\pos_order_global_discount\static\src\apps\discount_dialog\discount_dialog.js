import { Dialog } from "@web/core/dialog/dialog";
import { _t } from "@web/core/l10n/translation";
import { useChildRef } from "@web/core/utils/hooks";
import { useState } from "@odoo/owl";
import { Component } from "@odoo/owl";

export class DiscountDialog extends Component {
    static template = "sh_pos_order_discount.DiscountDialog";
    static components = { Dialog };
    static props = {
        close: Function,
        title: { type: String, optional: true },
        body: { type: String, optional: true },
        confirm: { type: Function, optional: true },
        confirmLabel: { type: String, optional: true },
        confirmClass: { type: String, optional: true },
        cancel: { type: Function, optional: true },
        cancelLabel: { type: String, optional: true },
        dismiss: { type: Function, optional: true },
        currencySymbol: { type: String, optional: true },
    };

    static defaultProps = {
        confirmLabel: _t("Confirm"),
        cancelLabel: _t("Cancel"),
        confirmClass: "btn-primary",
        title: _t("Apply Discount"),
    };

    setup() {
        this.state = useState({
            discountType: "fixed",
            discountValue: 0
        });

        this.modalRef = useChildRef();
        this.isProcess = false;
    }

    async _confirm() {
        if (this.props.confirm) {
            await this.props.confirm({
                type: this.state.discountType,
                value: parseFloat(this.state.discountValue) || 0
            });
        }
        this.props.close();
    }

    async _cancel() {
        return this.execButton(this.props.cancel);
    }

    async _dismiss() {
        return this.execButton(this.props.dismiss || this.props.cancel);
    }

    setButtonsDisabled(disabled) {
        this.isProcess = disabled;
        if (!this.modalRef.el) {
            return;
        }
        for (const button of [...this.modalRef.el.querySelectorAll(".modal-footer button")]) {
            button.disabled = disabled;
        }
    }

    async execButton(callback) {
        if (this.isProcess) {
            return;
        }
        this.setButtonsDisabled(true);
        if (callback) {
            let shouldClose;
            try {
                shouldClose = await callback();
            } catch (e) {
                this.props.close();
                throw e;
            }
            if (shouldClose === false) {
                this.setButtonsDisabled(false);
                return;
            }
        }
        this.props.close();
    }
}