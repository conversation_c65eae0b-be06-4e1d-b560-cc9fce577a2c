<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

  <t t-name="sh_pos_order_discount.DiscountDialog">
    <Dialog size="'md'" title="props.title" modalRef="modalRef">
      <div class="discount-dialog-content">
        <t t-if="props.body">
          <p t-out="props.body" class="text-prewrap"/>
        </t>

        <div class="form-group">
          <label for="discountType" class="mb-2">Discount Type:</label>
          <select
            id="discountType"
            class="form-control mb-3"
            t-model="state.discountType"
          >
            <option value="fixed">Fixed Amount (<t t-esc="props.currencySymbol || '$'"/>)</option>
            <option value="percentage">Percentage (%)</option>
          </select>
        </div>

        <div class="form-group">
          <label for="discountValue" class="mb-2">
            Discount Value:
          </label>
          <input
            id="discountValue"
            class="form-control"
            type="number"
            min="0"
            step="0.01"
            t-att-max="state.discountType === 'percentage' ? 100 : null"
            t-model="state.discountValue"
            placeholder="Enter discount amount"
          />
        </div>
      </div>

      <t t-set-slot="footer">
        <button class="btn" t-att-class="props.confirmClass" t-on-click="_confirm" t-esc="props.confirmLabel"/>
        <button class="btn btn-secondary" t-on-click="_cancel" t-esc="props.cancelLabel"/>
      </t>
    </Dialog>
  </t>

</templates>