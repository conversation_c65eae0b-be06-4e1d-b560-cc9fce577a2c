import { _t } from "@web/core/l10n/translation";
import { AlertDialog } from "@web/core/confirmation_dialog/confirmation_dialog";
import { ControlButtons } from "@point_of_sale/app/screens/product_screen/control_buttons/control_buttons";
import { patch } from "@web/core/utils/patch";
import { DiscountDialog } from "../../../discount_dialog/discount_dialog";
import { usePos } from "@point_of_sale/app/store/pos_hook";
import { useService } from "@web/core/utils/hooks";


patch(ControlButtons.prototype, {
    setup() {
        super.setup();
        this.pos = usePos();
        this.dialog = useService("dialog");
        this.notification = useService("notification");
    },

    async clickGlobalDiscount() {
        const order = this.pos.get_order();
        if (!order || order.get_orderlines().length === 0) {
            this.notification.add(_t("Cannot apply discount to empty order"), {
                type: 'danger'
            });
            return;
        }

        const discountProduct = this.pos.config.discount_product_id;
        if (!discountProduct) {
            this.dialog.add(AlertDialog, {
                title: _t("No discount product found"),
                body: _t(
                    "The discount product seems misconfigured. Make sure it is flagged as 'Can be Sold' and 'Available in Point of Sale'."
                ),
            });
            return;
        }

        await this.dialog.add(DiscountDialog, {
            title: _t("Apply Discount"),
            currencySymbol: this.pos.currency.symbol,
            confirm: async ({ type, value }) => {
                try {
                    await this.apply_fixed_discount(type, value);
                    this.notification.add(
                        _t("Discount applied successfully"),
                        { type: 'success' }
                    );
                } catch (error) {
                    this.notification.add(error.message, { type: 'danger' });
                    throw error;
                }
            }
        });
    },

    async apply_fixed_discount(type, value) {
        const order = this.pos.get_order();
        const discountProduct = this.pos.config.discount_product_id;

        // Remove existing discount lines
        const lines = order.get_orderlines();
        lines.filter(line => line.get_product().id === discountProduct[0])
             .forEach(line => line.delete());

        let discountAmount;
        if (type === "fixed") {
            discountAmount = -Math.abs(value);
        } else {
            const baseAmount = order.calculate_base_amount(
                lines.filter(line => line.isGlobalDiscountApplicable())
            );
            discountAmount = -(baseAmount * value / 100);
        }

        if (discountAmount < 0) {
            await this.pos.addLineToCurrentOrder(
                {
                    product_id: discountProduct,
                    price_unit: discountAmount,
                    extras: {
                        discount_type: type,
                        discount_value: value
                    }
                },
                {
                    merge: false
                }
            );

            // Set discount description
            const discountLine = order.get_orderlines().find(
                line => line.get_product().id === discountProduct[0]
            );
            if (discountLine) {
                discountLine.set_discount_description(
                    type === "fixed"
                        ? `${this.pos.currency.symbol}${Math.abs(value)} Discount`
                        : `${value}% Discount`
                );
            }
        }
    },
});