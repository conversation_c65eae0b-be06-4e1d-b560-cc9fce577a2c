/* Product List View Styles */
.product-list.list-view {
    background-color: #fff;
}

.product-list-header {
    font-size: 0.875rem;
    color: #495057;
    z-index: 5;
}

.product-list-item {
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.product-list-item:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.product-list-item.selected {
    background-color: #e7f5ff;
    border-left: 3px solid #228be6;
}

/* Text truncation */
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Column styling */
.product-name-col {
    color: #212529;
}

.product-variants-col {
    color: #6c757d;
}

/* Quantity Badge - Primary Style */
.quantity-badge {
    display: inline-block;
    min-width: 28px;
    padding: 6px 8px;
    font-size: 1rem;
    line-height: 1;
    color: #fff !important;
    background-color: #2c3e50;
    border-radius: 14px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.product-list-item:hover .quantity-badge {
    background-color: #3d566e;
    transform: scale(1.05);
}

.product-list-item.selected .quantity-badge {
    background-color: #228be6;
}

.product-unitprice-col {
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .product-list-item,
    .product-list-header {
        gap: 1rem;
    }

    .product-name-col {
        flex: 1 1 35% !important;
    }

    .product-variants-col {
        flex: 1 1 25% !important;
    }

    .quantity-badge {
        font-size: 0.9rem;
        padding: 5px 7px;
        min-width: 24px;
    }
}