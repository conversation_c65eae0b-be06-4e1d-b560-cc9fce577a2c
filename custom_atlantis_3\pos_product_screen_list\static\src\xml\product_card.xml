<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Override ProductCard for list view -->
    <t t-name="pos_product_list.ProductCard" owl="1"
       t-inherit="point_of_sale.ProductCard" t-inherit-mode="extension">

        <!-- Remove product image -->
        <xpath expr="//div[hasclass('product-img')]" position="replace"/>

        <!-- Modify article element -->
        <xpath expr="//article" position="attributes">
            <attribute
                    name="class">product-list-item d-flex flex-row align-items-center w-100 px-3 py-2 border-bottom</attribute>
        </xpath>

        <!-- Replace content structure -->
        <xpath expr="//div[hasclass('product-content')]" position="replace">
            <div class="d-flex flex-row justify-content-between align-items-center w-100 gap-3">
                <!-- Product Name (40%) -->
                <div class="product-name-col text-truncate pe-2" style="flex: 1 1 40%;">
                    <span class="product-name fw-medium" t-esc="props.name"/>
                </div>

                <!-- Quantity Column (15%) -->
                <div class="product-qty-col text-center pe-2" style="flex: 0 0 15%;">
                    <span t-if="props.productCartQty"
                          class="quantity-badge fw-bold"
                          t-esc="props.productCartQty"/>
                </div>

            </div>
        </xpath>
    </t>

    <!-- Modify ProductScreen container - SAFEST APPROACH -->
    <t t-name="pos_product_list.ProductScreen" owl="1"
       t-inherit="point_of_sale.ProductScreen" t-inherit-mode="extension">

        <!-- Target the specific product list div using its parent structure -->
        <xpath expr="//div[hasclass('rightpane')]//div[hasclass('position-relative')]//div[contains(@t-attf-class, 'product-list')]"
               position="attributes">
            <attribute name="class">product-list list-view d-flex flex-column w-100 overflow-y-auto px-0</attribute>
        </xpath>

    </t>

</templates>