import { _t } from "@web/core/l10n/translation";
import { TicketScreen } from "@point_of_sale/app/screens/ticket_screen/ticket_screen";
import { patch } from "@web/core/utils/patch";

patch(TicketScreen.prototype, {

    /**
     * @override
     * Add product search field.
     */
    _getSearchFields() {
        return Object.assign({}, super._getSearchFields(...arguments), {
            PRODUCT: {
                repr: (order) => order.getProductName(),
                displayName: _t("Product"),
                modelFields: ['lines.product_id'],
            },
        });
    },

});
