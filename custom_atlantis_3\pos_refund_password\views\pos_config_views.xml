<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="pos_config_view_form" model="ir.ui.view">
        <field name="name">pos.config.view.form.inherit.pos.refund.password</field>
        <field name="model">pos.config</field>
        <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
        <field name="arch" type="xml">
            <setting id="other_devices" position="after">
                <setting id="refund_security">
                    <div class="row">
                        <label for="refund_security"/>
                        <field name="refund_security" widget="char"/>
                        <div class="text-muted mb16">
                            Set a security password on refund on pos screen
                        </div>
                    </div>
                </setting>
            </setting>
        </field>
    </record>
</odoo>
