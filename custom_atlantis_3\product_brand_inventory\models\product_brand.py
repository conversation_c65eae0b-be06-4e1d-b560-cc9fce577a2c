# -*- coding: utf-8 -*-
#############################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author: Cybrosys Techno Solutions(<https://www.cybrosys.com>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU AFFERO GENERAL PUBLIC LICENSE (AGPL v3) for more details.
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
#############################################################################
from odoo import api, fields, models


class ProductBrand(models.Model):
    """Model for adding brand details"""
    _name = 'product.brand'
    _description = "Product brand details"

    name = fields.Char(String="Name", help="Name of the brand")
    brand_image = fields.Binary(String="Brand Image",
                                help="Brand image of product")
    member_ids = fields.One2many('product.template',
                                 'brand_id',
                                 string="Products",
                                 help="Number of Products")
    product_count = fields.Char(String='Product Count',
                                help="Number of products",
                                compute='_compute_product_count', store=True)

    @api.depends('member_ids')
    def _compute_product_count(self):
        """Compute number of product added in product brand"""
        self.product_count = len(self.member_ids)
