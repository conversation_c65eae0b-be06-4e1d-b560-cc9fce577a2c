<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Action for Product Brand -->
    <record id="product_brand_action" model="ir.actions.act_window">
        <field name="name">Product Brand</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">list,form</field>
    </record>
    <!-- Product Brand Form view -->
    <record id="product_brand_view_form" model="ir.ui.view">
        <field name="name">product.brand.view.form</field>
        <field name="model">product.brand</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <field name="brand_image" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"
                               string="Brand Name"/>
                        <h1>
                            <field name="name"/>
                        </h1>
                        <div name="options_active"/>
                    </div>
                    <group>
                        <field name="product_count" style="width:40px;"/>
                    </group>
                    <notebook>
                        <page string="Products">
                            <field name="member_ids" widget="many2many"
                                   options="{'not_delete': True}">
                                <kanban quick_create="false" create="true"
                                        delete="true">
                                    <field name="id"/>
                                    <field name="name"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div class="oe_kanban_global_click"
                                                 style="max-width: 200px">
                                                <div class="o_kanban_record_top">
                                                    <img t-att-src="kanban_image('product.template', 'image_128', record.id.raw_value)"
                                                         class="oe_avatar oe_kanban_avatar_smallbox o_image_40_cover mb0"
                                                         alt="Avatar"/>
                                                    <div class="o_kanban_record_headings ml8">
                                                        <strong class="o_kanban_record_title">
                                                            <field name="name"/>
                                                        </strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <!-- Menu of Product Brands -->
    <menuitem id="product_brand_menu"
              name="Product Brands"
              action="product_brand_action"
              parent="stock.menu_stock_inventory_control"
              sequence="2"/>
</odoo>
