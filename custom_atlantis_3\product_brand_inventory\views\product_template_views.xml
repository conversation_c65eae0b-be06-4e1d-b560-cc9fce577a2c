<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Form view edited of product template -->
    <record id="product_template_form_view" model="ir.ui.view">
        <field name="name">
            product.template.form.view.inherit.product.brand.inventory
        </field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="categ_id" position="after">
                <field name="brand_id"/>
            </field>
        </field>
    </record>
    <!-- Group by for Brand in Product Template Search -->
    <record id="product_template_search_view" model="ir.ui.view">
        <field name="name">
            product.template.search.view.inherit.product.brand.inventory
        </field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <filter string="Brand" name="Brand"
                        context="{'group_by':'brand_id'}"/>
            </xpath>
        </field>
    </record>
</odoo>
