<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--   Brand Group By in Stock On Hand -->
    <record id="quant_search_view" model="ir.ui.view">
        <field name="name">quant.search.view.inherit.product.brand.inventory
        </field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.quant_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='company']" position="after">
                <filter string='Brand' name="brand" domain="[]"
                        context="{'group_by': 'brand_id'}"/>
            </xpath>
        </field>
    </record>
</odoo>
