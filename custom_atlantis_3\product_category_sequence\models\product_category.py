from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ProductCategory(models.Model):
    _inherit = 'product.category'

    prefix = fields.Char(string='Prefix')

    @api.constrains('prefix')
    def _check_unique_prefix(self):
        if self.prefix:
            for record in self:
                if not record.prefix.isdigit():
                    raise ValidationError("Prefix must be a numeric value.")
                duplicate_prefix = self.search([
                    ('id', '!=', record.id),
                    ('prefix', '=', record.prefix)
                ])
                if duplicate_prefix:
                    raise ValidationError(
                        f"Prefix '{record.prefix}' is already in use by another product category. Prefix must be unique.")
