from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    @api.model
    def create(self, vals):
        if 'categ_id' in vals:
            category = self.env['product.category'].browse(vals['categ_id'])
            if not category.prefix:
                raise ValidationError("Please set a prefix for the product category.")

            last_product = self.search([
                ('categ_id', '=', vals['categ_id'])
            ], order='default_code desc', limit=1)

            if last_product:
                last_ref = last_product.default_code
                last_sequence = int(last_ref[len(category.prefix):])
                next_sequence = last_sequence + 1
            else:
                next_sequence = 1
            vals['default_code'] = f"{category.prefix}{str(next_sequence).zfill(4)}"
            vals['barcode'] = vals['default_code']
        return super(ProductTemplate, self).create(vals)
