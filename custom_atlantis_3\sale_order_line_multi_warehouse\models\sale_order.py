# -- coding: utf-8 --
# Part of Softhealer Technologies.
from odoo import models, fields, api


class ShSaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    qty_available_forcasted = fields.Text(string="ط§ظ„ظƒظ…ظٹط© ظپظٹ ط§ظ„ط·ط±ظٹظ‚",compute='get_qty_available_forcasted')
    warehouse_qtys = fields.Text(related='product_id.warehouse_quantity', string='ط§ظ„ظƒظ…ظٹط© ط§ظ„ظ…طھط§ط­ط©')



    @api.onchange('product_id')
    def get_qty_available_forcasted(self):
        for elem in self:
            purchase_quantity = 0
            elem.qty_available_forcasted = '0'
            if elem.product_id:
                quantity_count = self.env['purchase.order.line'].search([('state','=', 'purchase'), ('product_id','=',elem.product_id.id)])
                # t_quantity = {}
                for ele in quantity_count:
                    purchase_quantity +=ele.product_qty
                elem.qty_available_forcasted = str(purchase_quantity)
            else:
                elem.qty_available_forcasted = '0'



class ProductTemplate(models.Model):
    _inherit = "product.template"

    warehouse_quantity = fields.Text(compute='_get_warehouse_quantity', string='Quantity per warehouse')

    def _get_warehouse_quantity(self):
        for record in self:
            warehouse_quantity_text = ''
            product_id = self.env['product.product'].sudo().search([('product_tmpl_id', '=', record.id)])
            if product_id:
                quant_ids = self.env['stock.quant'].sudo().search([('product_id','=',product_id[0].id),('location_id.usage','=','internal')])
                t_warehouses = {}
                for quant in quant_ids:
                    if quant.location_id:
                        if quant.location_id not in t_warehouses:
                            t_warehouses.update({quant.location_id:0})
                        t_warehouses[quant.location_id] += quant.quantity

                tt_warehouses = {}
                for location in t_warehouses:
                    warehouse = False
                    location1 = location
                    while (not warehouse and location1):
                        warehouse_id = self.env['stock.warehouse'].sudo().search([('lot_stock_id','=',location1.id)])
                        if len(warehouse_id) > 0:
                            warehouse = True
                        else:
                            warehouse = False
                        location1 = location1.location_id
                    if warehouse_id:
                        if warehouse_id.name not in tt_warehouses:
                            tt_warehouses.update({warehouse_id.name:0})
                        tt_warehouses[warehouse_id.name] += t_warehouses[location]

                for item in tt_warehouses:
                    if tt_warehouses[item] != 0:
                        warehouse_quantity_text =  warehouse_quantity_text + '\n' + item + ': ' + str(tt_warehouses[item])
                record.warehouse_quantity = warehouse_quantity_text