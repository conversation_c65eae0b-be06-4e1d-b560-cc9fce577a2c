{
    'name': 'Sale Order Payment',
    'version': '********.0',
    'category': 'Sales/Sales',
    'summary': 'Register payments from sale orders',
    'description': """
        This module allows users to register payments directly from sale orders using Odoo's standard payment functionality.
    """,
    'depends': ['sale_management', 'account', 'stock'],
    'data': [
        'security/sale_payment_security.xml',
        'security/ir.model.access.csv',
        'views/sale_order_views.xml',
        'views/res_users_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
    'assets': {
        'web.assets_backend': [
            # Add any JS/CSS files if needed
        ],
    },
} 