from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class ResUsers(models.Model):
    _inherit = 'res.users'

    allow_so_payment_button = fields.Boolean(
        string='Show Payment Button in Sales Order',
        help='Allow user to see payment button in sales orders'
    )
    allow_auto_delivery_confirm = fields.Boolean(
        string='Auto Confirm Delivery Orders',
        help='Automatically confirm delivery orders when confirming sales orders'
    )

    @api.onchange('allow_so_payment_button')
    def _onchange_allow_so_payment_button(self):
        payment_group = self.env.ref('sale_order_payment.group_allow_so_payment_button', raise_if_not_found=False)
        if payment_group:
            if self.allow_so_payment_button:
                self.sudo().write({'groups_id': [(4, payment_group.id)]})
            else:
                self.sudo().write({'groups_id': [(3, payment_group.id)]})

    @api.onchange('allow_auto_delivery_confirm')
    def _onchange_allow_auto_delivery_confirm(self):
        delivery_group = self.env.ref('sale_order_payment.group_allow_auto_delivery_confirm', raise_if_not_found=False)
        if delivery_group:
            if self.allow_auto_delivery_confirm:
                self.sudo().write({'groups_id': [(4, delivery_group.id)]})
            else:
                self.sudo().write({'groups_id': [(3, delivery_group.id)]})

    def write(self, vals):
        res = super(ResUsers, self).write(vals)
        if 'allow_so_payment_button' in vals:
            payment_group = self.env.ref('sale_order_payment.group_allow_so_payment_button', raise_if_not_found=False)
            if payment_group:
                if vals['allow_so_payment_button']:
                    self.sudo().write({'groups_id': [(4, payment_group.id)]})
                else:
                    self.sudo().write({'groups_id': [(3, payment_group.id)]})
        if 'allow_auto_delivery_confirm' in vals:
            delivery_group = self.env.ref('sale_order_payment.group_allow_auto_delivery_confirm', raise_if_not_found=False)
            if delivery_group:
                if vals['allow_auto_delivery_confirm']:
                    self.sudo().write({'groups_id': [(4, delivery_group.id)]})
                else:
                    self.sudo().write({'groups_id': [(3, delivery_group.id)]})
        return res 