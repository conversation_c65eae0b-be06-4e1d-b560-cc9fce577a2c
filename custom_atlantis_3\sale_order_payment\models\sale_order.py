from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    payment_count = fields.Integer(string='Payment Count', compute='_compute_payment_count')

    def _compute_payment_count(self):
        for order in self:
            order.payment_count = len(self.env['account.payment'].search([
                ('payment_reference', 'ilike', order.name)
            ]))

    def action_confirm(self):
        res = super().action_confirm()
        
        for order in self:
            payment_group = self.env.ref('sale_order_payment.group_allow_so_payment_button')
            if payment_group in self.env.user.groups_id:
                if not order.invoice_ids:
                    invoice = order._create_invoices()
                    invoice.action_post()
                
            delivery_group = self.env.ref('sale_order_payment.group_allow_auto_delivery_confirm')
            if order.picking_ids and delivery_group in self.env.user.groups_id:
                for picking in order.picking_ids.filtered(lambda p: p.state not in ['done', 'cancel']):
                    if picking.state in ['draft', 'waiting', 'confirmed']:
                        picking.action_confirm()
                        picking.action_assign()
                    
                    for move in picking.move_ids:
                        if move.state not in ['done', 'cancel']:
                            move.quantity = move.product_uom_qty
                    
                    if picking.state not in ['done', 'cancel']:
                        picking.button_validate()
        
        return res

    def action_register_payment(self):
        self.ensure_one()
        _logger.info("Registering payment for order %s", self.name)
        
        payment_group = self.env.ref('sale_order_payment.group_allow_so_payment_button')
        if payment_group not in self.env.user.groups_id:
            raise UserError(_('You are not allowed to register payments from sales orders.'))
            
        if not self.partner_id:
            raise UserError(_('Please select a customer first.'))
            
        invoice = self.invoice_ids.filtered(lambda x: x.state == 'posted' and x.payment_state != 'paid')
        if not invoice:
            raise UserError(_('No open invoice found for payment registration.'))
            
        # Get default journal
        journal = self.env['account.journal'].search([
            ('company_id', '=', self.company_id.id),
            ('type', 'in', ['bank', 'cash'])
        ], limit=1)
        
        if not journal:
            raise UserError(_('Please configure a payment journal first.'))
            
        # Get currency
        currency_id = invoice.currency_id.id or self.company_id.currency_id.id
        
        action = {
            'name': _('Register Payment'),
            'res_model': 'account.payment.register',
            'view_mode': 'form',
            'context': {
                'active_model': 'account.move',
                'active_ids': invoice.ids,
                'default_payment_type': 'inbound',
                'default_partner_type': 'customer',
                'default_partner_id': self.partner_id.id,
                'default_amount': invoice[0].amount_residual,
                'default_payment_reference': self.name,
                'default_currency_id': currency_id,
                'default_journal_id': journal.id,
                'dont_redirect_to_payments': True,
            },
            'target': 'new',
            'type': 'ir.actions.act_window',
        }
        return action

    def action_view_payments(self):
        self.ensure_one()
        payments = self.env['account.payment'].search([
            ('payment_reference', 'ilike', self.name)
        ])
        return {
            'name': _('Payments'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'view_mode': 'list,form',
            'domain': [('id', 'in', payments.ids)],
            'context': {'create': False},
        }