<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_order_form_inherit_payment" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.payment</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="priority">99</field>
        <field name="arch" type="xml">
            <button name="action_confirm" position="after">
                <button name="action_register_payment" 
                        type="object" 
                        string="Register Payment"
                        class="btn btn-primary" 
                        groups="sale_order_payment.group_allow_so_payment_button"
                        invisible="state != 'sale' or invoice_status != 'invoiced'"/>
            </button>
            <div name="button_box" position="inside">
                <button class="oe_stat_button" 
                        name="action_view_payments" 
                        type="object" 
                        icon="fa-money"
                        invisible="payment_count == 0">
                    <field name="payment_count" widget="statinfo" string="Payments"/>
                </button>
            </div>
        </field>
    </record>
</odoo> 