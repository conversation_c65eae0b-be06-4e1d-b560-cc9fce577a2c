# QR Code Printing for Resort Cards

## Overview
This feature allows printing QR codes and customer names on preprinted resort cards. The QR code contains the card's barcode number, which can be scanned for quick customer identification in POS systems.

## Features Added

### 1. QR Code Print Reports
- **Standard QR Print**: General purpose QR code printing on A4 paper
- **PS80_79 QR Print**: Specialized format for PS80_79 preprinted cards

### 2. Report Templates
- `card_qr_print_template`: Standard template for multiple cards per page
- `card_qr_print_ps80_79_template`: Precise positioning for PS80_79 cards

### 3. New Buttons in Card Form
- **طباعة QR**: Print QR code using standard template
- **طباعة QR (PS80_79)**: Print QR code using PS80_79 specific template

## Usage

### From Card Form View
1. Open any resort card record
2. Click either "طباعة QR" or "طباعة QR (PS80_79)" button
3. The PDF report will be generated with:
   - QR code containing the card barcode
   - Customer name in Arabic
   - Proper positioning for preprinted cards

### From List View
1. Select one or more cards from the list
2. Use the "Print" menu to access QR code reports
3. Choose the appropriate template for your card type

## Card Positioning

### PS80_79 Cards
- **Card Size**: 76mm x 75mm
- **Customer Name**: Left side, bottom area (6mm from left, 15mm from bottom)
- **QR Code**: Right side, bottom area (8mm from right, 12mm from bottom)
- **QR Code Size**: 20mm x 20mm

### Standard Cards
- **Card Size**: 85.60mm x 53.98mm (standard credit card size)
- **Customer Name**: Left side, bottom area
- **QR Code**: Right side, bottom area
- **QR Code Size**: 22mm x 22mm

## Technical Details

### QR Code Generation
- Uses Odoo's built-in barcode generation: `/report/barcode/?type=QR&value=CARD_BARCODE`
- QR code contains the card's barcode number
- Size optimized for scanning from typical distances

### Paper Formats
- **Standard**: Uses A4 paper format
- **PS80_79**: Custom paper format (80mm x 79mm) with minimal margins

### Files Added/Modified
1. `report/card_qr_print_report.xml` - Report definitions and templates
2. `models/resort_card.py` - Added print action methods
3. `views/card_management_views.xml` - Added print buttons
4. `__manifest__.py` - Added report to data files
5. `tests/test_card_management.py` - Added QR code printing tests

## Customization

### Adjusting Positions
To adjust the positioning of elements on the card, modify the CSS styles in the report templates:

```xml
<!-- Customer Name positioning -->
<div class="customer-name" style="
    position: absolute;
    left: 6mm;        <!-- Distance from left edge -->
    bottom: 15mm;     <!-- Distance from bottom edge -->
    width: 28mm;      <!-- Width of text area -->
    height: 10mm;     <!-- Height of text area -->
    ...
">
```

### QR Code Size
To change QR code size, modify both the container size and the image parameters:

```xml
<!-- QR Code container -->
<div class="qr-code" style="
    width: 20mm;      <!-- Container width -->
    height: 20mm;     <!-- Container height -->
    ...
">
    <!-- QR Code image -->
    <img t-att-src="'/report/barcode/?type=QR&amp;value=' + qr_code_data + '&amp;width=160&amp;height=160'" 
         style="max-width: 20mm; max-height: 20mm; ..."/>
</div>
```

## Testing

Run the included tests to verify functionality:

```bash
# Run specific test
python -m pytest custom_atlantis_tesing_5/card_management/tests/test_card_management.py::TestCardManagement::test_qr_code_printing

# Run all card management tests
python -m pytest custom_atlantis_tesing_5/card_management/tests/
```

## Troubleshooting

### QR Code Not Generating
- Ensure the card has a valid barcode
- Check that the barcode field is not empty
- Verify Odoo's barcode generation is working: test with `/report/barcode/?type=QR&value=test`

### Positioning Issues
- Check the CSS positioning values in the template
- Ensure the paper format matches your printer settings
- Test print with a single card first before batch printing

### Print Quality
- Use high-quality PDF settings
- Ensure printer DPI is set correctly (96 DPI recommended)
- Check that QR code size is appropriate for scanning distance

## Support
For issues or customization requests, refer to the card management module documentation or contact the development team.
