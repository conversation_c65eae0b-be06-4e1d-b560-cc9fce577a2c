# Card Management Module for Odoo 15

## Overview

This module provides a simplified card management system for resort facilities where each card represents a customer. The system focuses on easy card creation and management with automatic customer record creation behind the scenes.

## Key Concept

**Card = Customer**: Each card directly represents one customer. When a card is lost, the old customer record is archived and a new card/customer is created.

## Features

### Simple Card Creation
- **Customer Name** (required)
- **Phone Number** (optional)
- **Card Barcode** (required, numeric only)
- Automatic customer record creation in background

### Card Management
- **Active**: Card is active and can be used
- **Inactive**: Card is temporarily disabled
- **Lost**: Card is lost and archived (customer also archived)
- Search by card number, customer name, or phone

### Balance Management
- Uses Odoo's standard customer credit system for card balances
- Top-up functionality through standard customer payments
- Real-time balance display

### POS Integration
- Leverages existing POS barcode scanning for customer identification
- Uses the standard customer barcode rule (pattern: 042) for client identification
- Customers are identified in POS by scanning their card barcode
- Customer balance/credit is displayed in POS

## Installation

1. Copy the `card_management` folder to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Card Management" module

## Usage

### Creating a New Card

1. Go to **Card Management > Resort Cards**
2. Click **Create**
3. Fill in the simple form:
   - **Customer Name**: Required field
   - **Phone Number**: Optional field
   - **Card Barcode**: Required numeric field
4. Save - a customer record is automatically created in the background

### Managing Cards

From the card list or form, you can:
- **Top Up Card**: Add money to the card balance
- **Deactivate**: Temporarily disable a card
- **Reactivate**: Re-enable a disabled card
- **Report Lost**: Archive the card and customer (for lost cards)
- **View Customer**: See the full customer record

### Top-up Card Balance

1. Open a card record or use the "Top Up Card" button
2. This opens the standard Odoo payment registration form
3. Register the payment - this will increase the customer's credit (card balance)

### Using Cards in POS

1. In POS, scan the customer's card barcode
2. The customer will be automatically identified and selected
3. The customer's available balance/credit will be displayed
4. Process the sale using the customer's credit or other payment methods

### Viewing Card Information

- **Card Management Menu**: View all customers with cards
- **Customers with Cards**: Filtered view of customers who have cards assigned
- **Card Status**: Manage different card status types (Admin only)

## Technical Details

### Models

- **card.status**: Manages different card status types
- **res.partner**: Extended with card management fields

### Key Fields Added to res.partner

- `card_status_id`: Current card status
- `card_assigned_date`: When the card was assigned
- `card_notes`: Additional notes about the card
- `has_card`: Computed field indicating if customer has a card
- `card_balance`: Computed field showing customer credit as card balance

### Barcode Format

- Cards must have numeric barcodes only
- Uses the existing `barcode` field on `res.partner`
- Integrates with POS barcode scanning system

### Security

- Regular users can view card information
- POS Managers can manage card status and create new status types

## Dependencies

- base
- point_of_sale  
- account
- contacts

## Notes

- The module uses Odoo's standard customer credit system for card balances
- All top-ups are recorded as standard customer payments for easy tracking
- POS integration works with existing barcode scanning without modifications
- Card barcodes must be unique across all customers
- Numeric-only barcode format is enforced
