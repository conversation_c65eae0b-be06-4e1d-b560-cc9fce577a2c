# -*- coding: utf-8 -*-
{
    'name': 'Card Management',
    'version': '********.0',
    'category': 'Point of Sale',
    'summary': 'Resort Card Management System for POS',
    'description': """
Card Management System for Resort
=================================

This module provides a card management system for resort facilities where:
- Customers purchase cards with unique barcodes
- Cards can be topped up with money using standard Odoo customer payments
- Cards are used in POS for payments using customer credit system
- Card status management (active/inactive/lost/stolen)
- Integration with existing POS barcode scanning for customer identification

Features:
- Card assignment to customers using barcode field
- Top-up functionality using standard customer payments
- Card status management
- POS integration for customer identification via barcode scanning
- Uses existing Odoo customer credit system
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'base',
        'point_of_sale',
        'account',
        'contacts',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/card_status_data.xml',
        'data/card_sequence_data.xml',
        'data/barcode_rules.xml',
        'views/res_partner_views.xml',
        'views/res_users_views.xml',
        'views/pos_order_views.xml',
        'wizard/daily_report_wizard_views.xml',
        'wizard/card_reissue_wizard_views.xml',
        'views/card_management_views.xml',
        'views/cashier_session_views.xml',
        'report/topup_reports.xml',
        'report/topup_receipt_template.xml',
        'report/session_thermal_template.xml',
        'report/daily_cashier_report.xml',
        'report/card_balance_report.xml',
        'report/card_qr_print_report.xml',
    ],
    'demo': [
        'demo/card_management_demo.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'card_management/static/src/js/card_kiosk.js',
            'card_management/static/src/js/card_kiosk_enter.js',
        ],
        'point_of_sale.assets': [
            'card_management/static/src/js/pos_customer_barcode.js',
            'card_management/static/src/js/pos_customer_balance.js',
            'card_management/static/src/js/pos_hide_buttons.js',
            'card_management/static/src/js/pos_ticket_screen.js',
            'card_management/static/src/js/pos_refund_security.js',
            'card_management/static/src/js/pos_order_refund_tracking.js',
            'card_management/static/src/js/pos_disable_customer_button.js',
            'card_management/static/src/js/pos_block_customer_events.js',
            'card_management/static/src/css/pos_customer_balance.css',
            'card_management/static/src/css/pos_hide_buttons.css',
            'card_management/static/src/css/pos_disable_customer.css',
            'card_management/static/src/xml/pos_templates.xml',
            'card_management/static/src/xml/pos_hide_buttons.xml',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
}
