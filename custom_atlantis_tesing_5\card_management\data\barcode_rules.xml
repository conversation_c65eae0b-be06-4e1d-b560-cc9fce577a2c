<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!-- Resort Customer Card Barcode Rule for 99 prefix -->
    <record id="barcode_rule_resort_card_99" model="barcode.rule">
        <field name="name">Resort Customer Cards (99)</field>
        <field name="barcode_nomenclature_id" ref="barcodes.default_barcode_nomenclature"/>
        <field name="sequence">35</field>
        <field name="type">client</field>
        <field name="encoding">any</field>
        <field name="pattern">99{NNNNNNNNNNNN}</field>
    </record>
</odoo>
