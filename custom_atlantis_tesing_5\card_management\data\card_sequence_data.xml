<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Card Number Sequence -->
        <record id="seq_resort_card_number" model="ir.sequence">
            <field name="name">Resort Card Number</field>
            <field name="code">resort.card.number</field>
            <field name="prefix">RC</field>
            <field name="padding">8</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="implementation">standard</field>
            <field name="company_id" eval="False"/>
        </record>

        <!-- Customer Card Barcode Sequence (99 prefix + 12 digits) -->
        <record id="seq_resort_card_barcode" model="ir.sequence">
            <field name="name">Resort Card Barcode</field>
            <field name="code">resort.card.barcode</field>
            <field name="prefix">99</field>
            <field name="padding">12</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="implementation">standard</field>
            <field name="company_id" eval="False"/>
        </record>

    </data>
</odoo>
