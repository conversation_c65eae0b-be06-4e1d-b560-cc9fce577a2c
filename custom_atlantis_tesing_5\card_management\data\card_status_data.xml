<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Card Status Records -->
        <record id="card_status_active" model="card.status">
            <field name="name">Active</field>
            <field name="code">active</field>
            <field name="sequence">10</field>
            <field name="description">Card is active and can be used</field>
            <field name="color">10</field>
        </record>

        <record id="card_status_inactive" model="card.status">
            <field name="name">Inactive</field>
            <field name="code">inactive</field>
            <field name="sequence">20</field>
            <field name="description">Card is inactive and cannot be used</field>
            <field name="color">7</field>
        </record>

        <record id="card_status_lost" model="card.status">
            <field name="name">Lost</field>
            <field name="code">lost</field>
            <field name="sequence">30</field>
            <field name="description">Card has been reported as lost</field>
            <field name="color">3</field>
        </record>



    </data>
</odoo>
