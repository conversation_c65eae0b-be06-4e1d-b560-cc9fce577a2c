# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* card_management
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-15 20:00+0000\n"
"PO-Revision-Date: 2025-08-15 20:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"

# ===== MODEL NAMES =====
#. module: card_management
#: model:ir.model,name:card_management.model_card_status
msgid "Card Status"
msgstr "حالة البطاقة"

#. module: card_management
#: model:ir.model,name:card_management.model_card_topup
msgid "Card Top-up"
msgstr "شحن البطاقة"

#. module: card_management
#: model:ir.model,name:card_management.model_resort_card
msgid "Resort Card"
msgstr "بطاقة المنتجع"

#. module: card_management
#: model:ir.model,name:card_management.model_card_kiosk
msgid "Card Kiosk"
msgstr "كشك البطاقة"

#. module: card_management
#: model:ir.model,name:card_management.model_daily_report_wizard
msgid "Daily Report Wizard"
msgstr "معالج التقرير اليومي"

#. module: card_management
#: model:ir.model,name:card_management.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

# ===== MENU ITEMS =====
#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_card_management_main
msgid "Card Management"
msgstr "إدارة البطاقات"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_card_management_root
msgid "Operations"
msgstr "العمليات"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_resort_cards
msgid "Resort Cards"
msgstr "بطاقات المنتجع"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_card_topups
msgid "Card Top-ups"
msgstr "شحن البطاقات"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_quick_topup
msgid "Quick Top-up"
msgstr "شحن سريع"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_card_kiosk
msgid "Balance Kiosk"
msgstr "كشك الرصيد"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_daily_report
msgid "Daily Report"
msgstr "التقرير اليومي"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_card_management_config
msgid "Configuration"
msgstr "الإعدادات"

#. module: card_management
#: model:ir.ui.menu,name:card_management.menu_card_status
msgid "Card Status"
msgstr "حالة البطاقة"

# ===== FIELD DESCRIPTIONS =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_topup_amount
msgid "Top-up Amount"
msgstr "مبلغ الشحن"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_customer_name
msgid "Customer"
msgstr "العميل"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_card_barcode
msgid "Card #"
msgstr "رقم البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_payment_method
msgid "Payment Method"
msgstr "طريقة الدفع"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_cashier_id
msgid "Cashier"
msgstr "الكاشير"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_status_name
msgid "Status Name"
msgstr "اسم الحالة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_status_code
msgid "Status Code"
msgstr "رمز الحالة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_status_sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_status_active
msgid "Active"
msgstr "نشط"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_status_description
msgid "Description"
msgstr "الوصف"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_name
msgid "Card Name"
msgstr "اسم البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_barcode
msgid "Card Barcode"
msgstr "باركود البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_customer_id
msgid "Customer"
msgstr "العميل"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_card_status_id
msgid "Card Status"
msgstr "حالة البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_initial_amount
msgid "Initial Top-up Amount"
msgstr "مبلغ الشحن الأولي"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_card_balance
msgid "Card Balance"
msgstr "رصيد البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_create_date
msgid "Card Created"
msgstr "تاريخ إنشاء البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_res_partner_card_status_id
msgid "Card Status"
msgstr "حالة البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_res_partner_card_assigned_date
msgid "Card Assigned Date"
msgstr "تاريخ تعيين البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_res_partner_card_notes
msgid "Card Notes"
msgstr "ملاحظات البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_res_partner_has_card
msgid "Has Card"
msgstr "لديه بطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_res_partner_card_balance
msgid "Card Balance"
msgstr "رصيد البطاقة"

# ===== PAYMENT METHODS =====
#. module: card_management
#: selection:card.topup,payment_method:0
msgid "Cash"
msgstr "نقدي"

#. module: card_management
#: selection:card.topup,payment_method:1
msgid "Card"
msgstr "بطاقة"

#. module: card_management
#: selection:card.topup,payment_method:2
msgid "Bank Transfer"
msgstr "تحويل بنكي"

# ===== CARD STATUS VALUES =====
#. module: card_management
#: model:card.status,name:card_management.card_status_active
msgid "Active"
msgstr "نشط"

#. module: card_management
#: model:card.status,name:card_management.card_status_inactive
msgid "Inactive"
msgstr "غير نشط"

#. module: card_management
#: model:card.status,name:card_management.card_status_lost
msgid "Lost"
msgstr "مفقود"

#. module: card_management
#: model:card.status,name:card_management.card_status_stolen
msgid "Stolen"
msgstr "مسروق"

#. module: card_management
#: model:card.status,name:card_management.card_status_damaged
msgid "Damaged"
msgstr "تالف"

# ===== WIZARD FIELDS =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_daily_report_wizard_date_from
msgid "From Date"
msgstr "من تاريخ"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_daily_report_wizard_date_to
msgid "To Date"
msgstr "إلى تاريخ"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_daily_report_wizard_cashier_id
msgid "Cashier"
msgstr "الكاشير"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_daily_report_wizard_report_type
msgid "Report Type"
msgstr "نوع التقرير"

#. module: card_management
#: selection:daily.report.wizard,report_type:0
msgid "Summary Only"
msgstr "ملخص فقط"

#. module: card_management
#: selection:daily.report.wizard,report_type:1
msgid "Detailed Report"
msgstr "تقرير مفصل"

# ===== KIOSK FIELDS =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_kiosk_card_barcode
msgid "Scan Card"
msgstr "امسح البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_kiosk_balance_display
msgid "Balance"
msgstr "الرصيد"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_kiosk_status_message
msgid "Status"
msgstr "الحالة"

# ===== BUTTONS AND ACTIONS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Top Up Card"
msgstr "شحن البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Deactivate"
msgstr "إلغاء التفعيل"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Reactivate"
msgstr "إعادة التفعيل"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Report Lost"
msgstr "الإبلاغ عن فقدان"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_quick_topup_form
msgid "Process Top-up"
msgstr "معالجة الشحن"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_quick_topup_form
msgid "Cancel"
msgstr "إلغاء"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_daily_report_wizard_form
msgid "Generate Report"
msgstr "إنشاء التقرير"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_kiosk_form
msgid "Check Balance"
msgstr "فحص الرصيد"

# ===== FORM TITLES =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Resort Card"
msgstr "بطاقة المنتجع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Card Top-up"
msgstr "شحن البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_quick_topup_form
msgid "Quick Card Top-up"
msgstr "شحن البطاقة السريع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_daily_report_wizard_form
msgid "Daily Cashier Report"
msgstr "تقرير الكاشير اليومي"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_kiosk_form
msgid "Card Balance Kiosk"
msgstr "كشك رصيد البطاقة"

# ===== HELP TEXT =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_daily_report_wizard_form
msgid "Generate Daily Report"
msgstr "إنشاء التقرير اليومي"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_daily_report_wizard_form
msgid "Generate cashier report for top-up transactions"
msgstr "إنشاء تقرير الكاشير لمعاملات شحن البطاقات"

# ===== REPORT ACTIONS =====
#. module: card_management
#: model:ir.actions.report,name:card_management.action_topup_thermal_receipt
msgid "Top-up Thermal Receipt"
msgstr "إيصال شحن البطاقة الحراري"

#. module: card_management
#: model:ir.actions.report,name:card_management.action_daily_cashier_report
msgid "Daily Cashier Report"
msgstr "تقرير الكاشير اليومي"

# ===== THERMAL RECEIPT TEMPLATE =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "CARD TOP-UP RECEIPT"
msgstr "إيصال شحن البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "Date:"
msgstr "التاريخ:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "Receipt #:"
msgstr "رقم الإيصال:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "Previous Balance:"
msgstr "الرصيد السابق:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "New Balance:"
msgstr "الرصيد الجديد:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "Thank you for visiting!"
msgstr "شكراً لزيارتكم!"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "Keep this receipt for your records"
msgstr "احتفظ بهذا الإيصال لسجلاتك"

# ===== DAILY CASHIER REPORT TEMPLATE =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Daily Cashier Report"
msgstr "تقرير الكاشير اليومي"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Card Top-up Transactions"
msgstr "معاملات شحن البطاقات"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Cashier:"
msgstr "الكاشير:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Date:"
msgstr "التاريخ:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Report Generated:"
msgstr "تاريخ إنشاء التقرير:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Total Transactions:"
msgstr "إجمالي المعاملات:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Total Amount:"
msgstr "إجمالي المبلغ:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Average per Transaction:"
msgstr "متوسط المعاملة:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Summary by Payment Method"
msgstr "ملخص حسب طريقة الدفع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Payment Method"
msgstr "طريقة الدفع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Count"
msgstr "العدد"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Total Amount"
msgstr "إجمالي المبلغ"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Transaction Details"
msgstr "تفاصيل المعاملات"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Time"
msgstr "الوقت"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Receipt #"
msgstr "رقم الإيصال"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Customer"
msgstr "العميل"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Card #"
msgstr "رقم البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Amount"
msgstr "المبلغ"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Status"
msgstr "الحالة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "TOTAL"
msgstr "الإجمالي"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Cash Reconciliation"
msgstr "تسوية النقدية"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Total Cash Collected:"
msgstr "إجمالي النقدية المحصلة:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Card Payments:"
msgstr "مدفوعات البطاقة:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Bank Transfers:"
msgstr "التحويلات البنكية:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "TOTAL EXPECTED:"
msgstr "الإجمالي المتوقع:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Cashier Signature"
msgstr "توقيع الكاشير"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Supervisor Signature"
msgstr "توقيع المشرف"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Date: _______________"
msgstr "التاريخ: _______________"

# ===== STATES AND STATUS =====
#. module: card_management
#: selection:card.topup,state:0
msgid "Draft"
msgstr "مسودة"

#. module: card_management
#: selection:card.topup,state:1
msgid "Done"
msgstr "تم"

#. module: card_management
#: selection:card.topup,state:2
msgid "Cancelled"
msgstr "ملغي"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.daily_cashier_report
msgid "Completed"
msgstr "مكتمل"

# ===== PLACEHOLDERS AND LABELS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_daily_report_wizard_form
msgid "All Cashiers"
msgstr "جميع الكاشيرين"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_kiosk_form
msgid "Scan your card barcode"
msgstr "امسح باركود البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_quick_topup_form
msgid "Scan Card Barcode"
msgstr "امسح باركود البطاقة"

# ===== ERROR MESSAGES =====
#. module: card_management
#: code:addons/card_management/models/resort_card.py:0
msgid "Card barcode must be unique!"
msgstr "باركود البطاقة يجب أن يكون فريداً!"

#. module: card_management
#: code:addons/card_management/models/card_status.py:0
msgid "Status code must be unique!"
msgstr "رمز الحالة يجب أن يكون فريداً!"

# ===== HELP TEXT AND DESCRIPTIONS =====
#. module: card_management
#: model:ir.model.fields,help:card_management.field_resort_card_initial_amount
msgid "Amount to add to card when creating (optional)"
msgstr "المبلغ المراد إضافته للبطاقة عند الإنشاء (اختياري)"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_resort_card_card_balance
msgid "Current card balance (customer credit)"
msgstr "رصيد البطاقة الحالي (رصيد العميل)"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_resort_card_active
msgid "Uncheck to archive this card"
msgstr "ألغ التحديد لأرشفة هذه البطاقة"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_res_partner_card_status_id
msgid "Current status of the customer card"
msgstr "الحالة الحالية لبطاقة العميل"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_res_partner_card_assigned_date
msgid "Date when the card was assigned to this customer"
msgstr "تاريخ تعيين البطاقة لهذا العميل"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_res_partner_card_notes
msgid "Additional notes about the card"
msgstr "ملاحظات إضافية حول البطاقة"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_res_partner_has_card
msgid "Indicates if customer has a card assigned"
msgstr "يشير إلى ما إذا كان العميل لديه بطاقة مخصصة"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_res_partner_card_balance
msgid "Current card balance (customer credit)"
msgstr "رصيد البطاقة الحالي (رصيد العميل)"

#. module: card_management
#: model:ir.model.fields,help:card_management.field_daily_report_wizard_cashier_id
msgid "Leave empty for all cashiers"
msgstr "اتركه فارغاً لجميع الكاشيرين"

# ===== CONFIRMATION MESSAGES =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "This will archive the card and customer. Are you sure?"
msgstr "سيتم أرشفة البطاقة والعميل. هل أنت متأكد؟"

# ===== CURRENCY SYMBOL =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.topup_thermal_receipt
msgid "KD"
msgstr "د.ك"

# ===== GROUPS AND TABS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_res_partner_form_inherit
msgid "Card Information"
msgstr "معلومات البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Card Details"
msgstr "تفاصيل البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Top-up Information"
msgstr "معلومات الشحن"

# ===== NOTEBOOK PAGES =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Transaction Info"
msgstr "معلومات المعاملة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Payment Details"
msgstr "تفاصيل الدفع"

# ===== MISSING FIELD LABELS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Customer Name"
msgstr "اسم العميل"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Phone Number"
msgstr "رقم الهاتف"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Initial Top-up Amount (Optional)"
msgstr "مبلغ الشحن الأولي (اختياري)"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Card Barcode"
msgstr "باركود البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Card Number"
msgstr "رقم البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Top-up Amount"
msgstr "مبلغ الشحن"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Receipt Printed"
msgstr "طباعة الإيصال"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Payment Record"
msgstr "سجل الدفع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Created on"
msgstr "تاريخ الإنشاء"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Notes"
msgstr "ملاحظات"

# ===== PLACEHOLDERS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Scan or enter card number"
msgstr "امسح أو أدخل رقم البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Enter amount"
msgstr "أدخل المبلغ"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Additional notes..."
msgstr "ملاحظات إضافية..."

# ===== SEARCH STRINGS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_search
msgid "Resort Cards"
msgstr "بطاقات المنتجع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Card Top-ups"
msgstr "شحن البطاقات"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_search
msgid "Active Cards"
msgstr "البطاقات النشطة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_search
msgid "Inactive Cards"
msgstr "البطاقات غير النشطة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_search
msgid "Lost Cards"
msgstr "البطاقات المفقودة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Draft"
msgstr "مسودة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Completed"
msgstr "مكتمل"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Cancelled"
msgstr "ملغي"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Today"
msgstr "اليوم"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "This Week"
msgstr "هذا الأسبوع"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "State"
msgstr "الحالة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_search
msgid "Date"
msgstr "التاريخ"

# ===== BUTTON LABELS IN CUSTOMER FORM =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_partner_form_card_actions
msgid "Assign"
msgstr "تعيين"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_partner_form_card_actions
msgid "Card"
msgstr "بطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_partner_form_card_action_buttons
msgid "Reactivate Card"
msgstr "إعادة تفعيل البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_partner_form_card_action_buttons
msgid "Deactivate Card"
msgstr "إلغاء تفعيل البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_partner_form_card_action_buttons
msgid "Report Lost"
msgstr "الإبلاغ عن فقدان"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_partner_form_card_action_buttons
msgid "Report Stolen"
msgstr "الإبلاغ عن سرقة"

# ===== CUSTOMER SEARCH FILTERS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_res_partner_filter_card_management
msgid "Has Card"
msgstr "لديه بطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_res_partner_filter_card_management
msgid "Stolen Cards"
msgstr "البطاقات المسروقة"

# ===== KIOSK MESSAGES =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_kiosk_form
msgid "Sorry, we couldn't find your card"
msgstr "عذراً، لم نتمكن من العثور على بطاقتك"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_kiosk_form
msgid "Please check your card number or contact customer service"
msgstr "يرجى التحقق من رقم بطاقتك أو الاتصال بخدمة العملاء"

# ===== HELP TEXT =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.action_card_topups
msgid "Create your first card top-up"
msgstr "إنشاء أول شحن بطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.action_card_topups
msgid "Process card top-ups for customers. Scan card number and enter amount to add money to their account."
msgstr "معالجة شحن البطاقات للعملاء. امسح رقم البطاقة وأدخل المبلغ لإضافة المال إلى حسابهم."

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.action_quick_topup
msgid "Quick card top-up for cashiers"
msgstr "شحن البطاقة السريع للكاشيرين"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.action_quick_topup
msgid "Scan customer card and process top-up payments quickly and easily."
msgstr "امسح بطاقة العميل ومعالجة مدفوعات الشحن بسرعة وسهولة."

# ===== ADDITIONAL FIELD LABELS FROM FORMS =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_phone
msgid "Phone Number"
msgstr "رقم الهاتف"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_notes
msgid "Notes"
msgstr "ملاحظات"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_receipt_printed
msgid "Receipt Printed"
msgstr "طباعة الإيصال"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_payment_id
msgid "Payment Record"
msgstr "سجل الدفع"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_create_date
msgid "Created on"
msgstr "تاريخ الإنشاء"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_state
msgid "State"
msgstr "الحالة"

# ===== CARD CREATED FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_card_created
msgid "Card Created"
msgstr "تاريخ إنشاء البطاقة"

# ===== ADDITIONAL MISSING TRANSLATIONS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Card Top-up"
msgstr "شحن البطاقة"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_resort_card_form
msgid "Resort Card"
msgstr "بطاقة المنتجع"

# ===== BOOLEAN FIELD VALUES =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "Yes"
msgstr "نعم"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.view_card_topup_form
msgid "No"
msgstr "لا"

# ===== CARD BARCODE FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_card_barcode
msgid "Card Barcode"
msgstr "باركود البطاقة"

# ===== PHONE FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_phone
msgid "Phone"
msgstr "الهاتف"

# ===== EXACT FIELD LABELS FROM MODEL DEFINITIONS =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_name
msgid "Customer Name"
msgstr "اسم العميل"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_phone
msgid "Phone Number"
msgstr "رقم الهاتف"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_barcode
msgid "Card Barcode"
msgstr "باركود البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_initial_amount
msgid "Initial Top-up Amount"
msgstr "مبلغ الشحن الأولي"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_card_barcode
msgid "Card Number"
msgstr "رقم البطاقة"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_customer_name
msgid "Customer Name"
msgstr "اسم العميل"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_topup_amount
msgid "Top-up Amount"
msgstr "مبلغ الشحن"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_payment_method
msgid "Payment Method"
msgstr "طريقة الدفع"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_cashier_id
msgid "Cashier"
msgstr "الكاشير"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_receipt_printed
msgid "Receipt Printed"
msgstr "طباعة الإيصال"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_payment_id
msgid "Payment Record"
msgstr "سجل الدفع"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_create_date
msgid "Created on"
msgstr "تاريخ الإنشاء"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_notes
msgid "Notes"
msgstr "ملاحظات"

# ===== CARD CREATED FIELD EXACT MATCH =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_create_date
msgid "Card Created"
msgstr "تاريخ إنشاء البطاقة"

# ===== DISPLAY NAME FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_display_name
msgid "Display Name"
msgstr "اسم العرض"

# ===== CARD FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_topup_card_id
msgid "Card"
msgstr "البطاقة"

# ===== CUSTOMER FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_resort_card_partner_id
msgid "Customer"
msgstr "العميل"

# ===== COLOR INDEX FIELD =====
#. module: card_management
#: model:ir.model.fields,field_description:card_management.field_card_status_color
msgid "Color Index"
msgstr "فهرس اللون"

# ===== POS INTERFACE TRANSLATIONS =====
#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Resort Card Scanned"
msgstr "تم مسح بطاقة المنتجع"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Customer: "
msgstr "العميل: "

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Available Balance: "
msgstr "الرصيد المتاح: "

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Card Not Found"
msgstr "البطاقة غير موجودة"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "This card is not registered in the system. Please check the card number or register the card first."
msgstr "هذه البطاقة غير مسجلة في النظام. يرجى التحقق من رقم البطاقة أو تسجيل البطاقة أولاً."

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Card Scanned (Offline)"
msgstr "تم مسح البطاقة (غير متصل)"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Note: Using cached balance data"
msgstr "ملاحظة: استخدام بيانات الرصيد المحفوظة"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Connection Error"
msgstr "خطأ في الاتصال"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Unable to verify card. Please check your connection and try again."
msgstr "غير قادر على التحقق من البطاقة. يرجى التحقق من الاتصال والمحاولة مرة أخرى."

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Insufficient Balance"
msgstr "رصيد غير كافٍ"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Required Amount: "
msgstr "المبلغ المطلوب: "

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Please visit the cashier to top-up your card."
msgstr "يرجى زيارة الكاشير لشحن بطاقتك."

# ===== POS TEMPLATE TRANSLATIONS =====
#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.pos_templates
msgid "Card Scanned Successfully"
msgstr "تم مسح البطاقة بنجاح"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.pos_templates
msgid "Available Balance:"
msgstr "الرصيد المتاح:"

#. module: card_management
#: model_terms:ir.ui.view,arch_db:card_management.pos_templates
msgid "Continue"
msgstr "متابعة"

# ===== ADDITIONAL POS MESSAGES =====
#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "OK"
msgstr "موافق"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Cancel"
msgstr "إلغاء"

#. module: card_management
#: code:addons/card_management/static/src/js/pos_customer_barcode.js:0
msgid "Close"
msgstr "إغلاق"
