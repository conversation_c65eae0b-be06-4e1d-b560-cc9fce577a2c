# -*- coding: utf-8 -*-

def migrate(cr, version):
    """Clean up card.pin.log model references after removal"""
    
    # Remove ir.model.data entries for card.pin.log
    cr.execute("""
        DELETE FROM ir_model_data 
        WHERE model = 'card.pin.log'
    """)
    
    # Remove ir.model entries for card.pin.log
    cr.execute("""
        DELETE FROM ir_model 
        WHERE model = 'card.pin.log'
    """)
    
    # Remove ir.model.fields entries for card.pin.log
    cr.execute("""
        DELETE FROM ir_model_fields 
        WHERE model = 'card.pin.log'
    """)
    
    # Remove ir.model.access entries for card.pin.log
    cr.execute("""
        DELETE FROM ir_model_access 
        WHERE model_id IN (
            SELECT id FROM ir_model WHERE model = 'card.pin.log'
        )
    """)
    
    # Remove any remaining references in ir_ui_view
    cr.execute("""
        DELETE FROM ir_ui_view 
        WHERE model = 'card.pin.log'
    """)
    
    # Remove any remaining references in ir_act_window
    cr.execute("""
        DELETE FROM ir_act_window 
        WHERE res_model = 'card.pin.log'
    """)
    
    # Remove any menu items pointing to card.pin.log
    cr.execute("""
        DELETE FROM ir_ui_menu 
        WHERE action IN (
            SELECT CONCAT('ir.actions.act_window,', id) 
            FROM ir_act_window 
            WHERE res_model = 'card.pin.log'
        )
    """)
    
    # Drop the table if it exists
    cr.execute("""
        DROP TABLE IF EXISTS card_pin_log CASCADE
    """)
    
    print("✅ Successfully cleaned up card.pin.log model references")
