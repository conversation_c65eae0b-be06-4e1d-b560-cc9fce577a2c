# -*- coding: utf-8 -*-
# Temporary empty model to satisfy database references during cleanup

from odoo import models, fields

class CardPinLogTemp(models.Model):
    _name = 'card.pin.log'
    _description = 'Temporary PIN Log (To be removed)'
    
    # Minimal fields to satisfy any existing references
    name = fields.Char('Name', default='Temporary')
    
    def unlink(self):
        # Allow deletion of all records
        return super().unlink()
