# -*- coding: utf-8 -*-

from odoo import models, fields, api


class CardStatus(models.Model):
    _name = 'card.status'
    _description = 'Card Status'
    _order = 'sequence, name'

    name = fields.Char('اسم الحالة', required=True, translate=True)
    code = fields.Char('رمز الحالة', required=True)
    sequence = fields.Integer('التسلسل', default=10)
    active = fields.Boolean('نشط', default=True)
    description = fields.Text('الوصف', translate=True)
    color = fields.Integer('فهرس اللون', default=0)

    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Status code must be unique!'),
    ]

    @api.model
    def get_default_active_status(self):
        """Get the default active status for new cards"""
        return self.search([('code', '=', 'active')], limit=1)

    @api.model
    def get_inactive_status(self):
        """Get the inactive status"""
        return self.search([('code', '=', 'inactive')], limit=1)

    @api.model
    def get_lost_status(self):
        """Get the lost status"""
        return self.search([('code', '=', 'lost')], limit=1)

    @api.model
    def get_stolen_status(self):
        """Get the stolen status"""
        return self.search([('code', '=', 'stolen')], limit=1)
