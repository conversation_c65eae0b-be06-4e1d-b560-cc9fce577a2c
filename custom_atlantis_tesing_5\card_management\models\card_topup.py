# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class CardTopup(models.Model):
    _name = 'card.topup'
    _description = 'Card Top-up Transaction'
    _order = 'create_date desc'
    _rec_name = 'display_name'

    # Card Information
    card_barcode = fields.Char(
        string='رقم البطاقة',
        required=True,
        help='Scan or enter the card barcode'
    )
    card_id = fields.Many2one(
        'resort.card',
        string='البطاقة',
        help='Found card record'
    )
    customer_name = fields.Char(
        string='اسم العميل',
        related='card_id.name',
        readonly=True,
        help='Customer name from card'
    )
    
    # Payment Information
    topup_amount = fields.Float(
        string='مبلغ الشحن',
        required=True,
        digits=(16, 2),
        help='Amount to add to the card'
    )
    payment_journal_id = fields.Many2one(
        'account.journal',
        string='طريقة الدفع',
        domain="[('type', 'in', ['cash', 'bank'])]",
        required=True,
        help='Journal for payment processing'
    )
    
    # Transaction Info
    payment_id = fields.Many2one(
        'account.payment',
        string='سجل الدفع',
        readonly=True,
        help='Generated payment record'
    )
    state = fields.Selection([
        ('draft', 'مسودة'),
        ('done', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ], default='draft', string='الحالة', readonly=True)

    notes = fields.Text(string='ملاحظات')

    # Cashier tracking
    cashier_id = fields.Many2one(
        'res.users',
        string='الكاشير',
        default=lambda self: self.env.user,
        required=True,
        help='The cashier who processed this top-up'
    )
    receipt_printed = fields.Boolean(
        string='طباعة الإيصال',
        default=False,
        help='Whether the thermal receipt was printed'
    )

    # Computed Fields
    display_name = fields.Char(
        string='اسم العرض',
        compute='_compute_display_name',
        store=True
    )

    @api.depends('card_barcode', 'topup_amount', 'create_date')
    def _compute_display_name(self):
        """Compute display name for top-up"""
        for topup in self:
            if topup.card_barcode and topup.topup_amount:
                topup.display_name = f"Card {topup.card_barcode} - {topup.topup_amount:.2f}"
            else:
                topup.display_name = f"Top-up {topup.id or 'New'}"

    @api.onchange('card_barcode')
    def _onchange_card_barcode(self):
        """Find card when barcode is entered"""
        if self.card_barcode:
            card = self.env['resort.card'].search([
                ('barcode', '=', self.card_barcode),
                ('active', '=', True)
            ], limit=1)
            
            if card:
                self.card_id = card.id
                # Check if card is active
                if card.card_status_id.code != 'active':
                    raise UserError(_('This card is not active. Current status: %s') % card.card_status_id.name)
            else:
                self.card_id = False
                raise UserError(_('Card not found. Please check the barcode.'))
        else:
            self.card_id = False

    @api.constrains('topup_amount')
    def _check_topup_amount(self):
        """Validate top-up amount"""
        for topup in self:
            if topup.topup_amount <= 0:
                raise ValidationError(_('Top-up amount must be greater than zero.'))

    def action_process_topup(self):
        """Process the top-up payment"""
        self.ensure_one()
        
        if not self.card_id:
            raise UserError(_('Please scan a valid card first.'))
        
        if self.topup_amount <= 0:
            raise UserError(_('Please enter a valid top-up amount.'))
        
        if self.state != 'draft':
            raise UserError(_('This top-up has already been processed.'))
        
        # Create payment record
        payment_vals = {
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.card_id.partner_id.id,
            'amount': self.topup_amount,
            'date': fields.Date.today(),
            'payment_reference': f'Card top-up {self.card_barcode} - {self.payment_journal_id.name}',
            'journal_id': self.payment_journal_id.id,
        }
        
        payment = self.env['account.payment'].create(payment_vals)
        payment.action_post()  # Confirm the payment
        
        # Update top-up record
        self.write({
            'payment_id': payment.id,
            'state': 'done',
        })

        # Refresh the card balance
        self.card_id._compute_card_balance()

        # Create transaction record
        self.env['card.transaction'].create_transaction(
            card_id=self.card_id.id,
            transaction_type='topup',
            amount=self.topup_amount,
            description=f'Card top-up via {self.payment_journal_id.name}',
            topup_id=self.id,
            payment_id=payment.id
        )

        # Mark receipt as printed (will be printed automatically)
        self.receipt_printed = True

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'type': 'success',
                'message': f'Top-up successful! Added {self.topup_amount:.2f} to card {self.card_barcode}',
                'sticky': False,
                'next': {
                    'type': 'ir.actions.act_window_close',
                }
            }
        }

    def action_cancel(self):
        """Cancel the top-up"""
        self.ensure_one()
        if self.state == 'done':
            raise UserError(_('Cannot cancel a completed top-up.'))
        
        self.state = 'cancelled'

    def action_reset_to_draft(self):
        """Reset to draft state"""
        self.ensure_one()
        if self.state == 'done' and self.payment_id:
            raise UserError(_('Cannot reset a top-up with confirmed payment.'))

        self.state = 'draft'

    def _get_receipt_customer_name(self):
        """Get customer name for thermal receipt"""
        return self.customer_name or (self.card_id.name if self.card_id else "Card Holder")

    def _get_receipt_company_name(self):
        """Get company name for thermal receipt"""
        return self.env.company.name or "RESORT"

    def _get_receipt_currency_symbol(self):
        """Get currency symbol for thermal receipt"""
        # Option 1: Use company currency name (KWD, USD, EUR)
        return self.env.company.currency_id.name

        # Option 2: Use translatable "KD" that becomes "د.ك" in Arabic
        # return _("KD")

        # Option 3: Use currency symbol (might not display well on thermal)
        # return self.env.company.currency_id.symbol

    def action_print_receipt(self):
        """Print thermal receipt"""
        self.ensure_one()

        # Calculate balances for receipt
        previous_balance = abs(self.card_id.partner_id.credit) - self.topup_amount
        new_balance = abs(self.card_id.partner_id.credit)

        # Mark as printed
        self.receipt_printed = True

        # Use standard report action without custom data
        # The template will access company via standard context
        return self.env.ref('card_management.action_topup_thermal_receipt').report_action(self)

    @api.model
    def get_daily_report_data(self, date_from, date_to, cashier_id=None):
        """Get data for daily cashier report"""
        domain = [
            ('create_date', '>=', date_from),
            ('create_date', '<=', date_to),
            ('state', '=', 'done'),
        ]

        if cashier_id:
            domain.append(('cashier_id', '=', cashier_id))

        topups = self.search(domain, order='create_date')

        # Calculate summary data
        total_amount = sum(topups.mapped('topup_amount'))
        average_amount = total_amount / len(topups) if topups else 0

        # Payment method summary
        payment_summary = []
        for method in ['cash', 'card', 'bank']:
            method_topups = topups.filtered(lambda t: t.payment_method == method)
            if method_topups:
                payment_summary.append({
                    'method': method,
                    'count': len(method_topups),
                    'total': sum(method_topups.mapped('topup_amount'))
                })

        # Cash reconciliation
        cash_total = sum(topups.filtered(lambda t: t.payment_method == 'cash').mapped('topup_amount'))
        card_total = sum(topups.filtered(lambda t: t.payment_method == 'card').mapped('topup_amount'))
        bank_total = sum(topups.filtered(lambda t: t.payment_method == 'bank').mapped('topup_amount'))

        return {
            'topups': topups,
            'total_amount': total_amount,
            'average_amount': average_amount,
            'payment_summary': payment_summary,
            'cash_total': cash_total,
            'card_total': card_total,
            'bank_total': bank_total,
            'cashier_name': topups[0].cashier_id.name if topups else '',
            'report_date': date_from.strftime('%d/%m/%Y') if date_from == date_to else f"{date_from.strftime('%d/%m/%Y')} - {date_to.strftime('%d/%m/%Y')}",
        }
