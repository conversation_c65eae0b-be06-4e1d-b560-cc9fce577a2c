# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class CardTransaction(models.Model):
    _name = 'card.transaction'
    _description = 'Card Transaction History'
    _order = 'transaction_date desc'
    _rec_name = 'display_name'

    # Card Information
    card_id = fields.Many2one(
        'resort.card',
        string='البطاقة',
        required=True,
        help='Card involved in transaction'
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='العميل',
        related='card_id.partner_id',
        store=True,
        help='Customer who owns the card'
    )
    
    # Transaction Details
    transaction_type = fields.Selection([
        ('topup', 'شحن البطاقة'),
        ('purchase', 'شراء'),
        ('refund', 'استرداد'),
        ('adjustment', 'تعديل'),
    ], string='نوع المعاملة', required=True)
    
    amount = fields.Float(
        string='المبلغ',
        required=True,
        digits=(16, 2),
        help='Transaction amount (positive for credits, negative for debits)'
    )
    
    balance_before = fields.Float(
        string='الرصيد قبل المعاملة',
        digits=(16, 2),
        help='Card balance before this transaction'
    )
    
    balance_after = fields.Float(
        string='الرصيد بعد المعاملة',
        digits=(16, 2),
        help='Card balance after this transaction'
    )
    
    transaction_date = fields.Datetime(
        string='تاريخ المعاملة',
        default=fields.Datetime.now,
        required=True
    )
    
    description = fields.Char(
        string='الوصف',
        help='Transaction description'
    )
    
    # Reference Fields
    topup_id = fields.Many2one(
        'card.topup',
        string='سجل الشحن',
        help='Related top-up record if applicable'
    )
    
    pos_order_id = fields.Many2one(
        'pos.order',
        string='طلب نقطة البيع',
        help='Related POS order if applicable'
    )
    
    payment_id = fields.Many2one(
        'account.payment',
        string='سجل الدفع',
        help='Related payment record if applicable'
    )
    
    # User tracking
    user_id = fields.Many2one(
        'res.users',
        string='المستخدم',
        default=lambda self: self.env.user,
        required=True,
        help='User who processed this transaction'
    )
    
    # Computed Fields
    display_name = fields.Char(
        string='اسم العرض',
        compute='_compute_display_name',
        store=True
    )
    
    @api.depends('transaction_type', 'amount', 'transaction_date')
    def _compute_display_name(self):
        """Compute display name for transaction"""
        for transaction in self:
            type_name = dict(transaction._fields['transaction_type'].selection)[transaction.transaction_type]
            transaction.display_name = f"{type_name} - {transaction.amount:.2f} - {transaction.transaction_date.strftime('%Y-%m-%d %H:%M')}"
    
    @api.model
    def create_transaction(self, card_id, transaction_type, amount, description=None, 
                          topup_id=None, pos_order_id=None, payment_id=None):
        """
        Create a new card transaction record
        
        Args:
            card_id: ID of the card
            transaction_type: Type of transaction ('topup', 'purchase', 'refund', 'adjustment')
            amount: Transaction amount (positive for credits, negative for debits)
            description: Optional description
            topup_id: Related top-up record ID
            pos_order_id: Related POS order ID
            payment_id: Related payment record ID
        
        Returns:
            Created transaction record
        """
        card = self.env['resort.card'].browse(card_id)
        if not card.exists():
            raise ValidationError(_('Card not found'))
        
        # Get current balance
        current_balance = card.card_balance
        balance_before = current_balance
        balance_after = current_balance + amount
        
        # Create transaction record
        transaction = self.create({
            'card_id': card_id,
            'transaction_type': transaction_type,
            'amount': amount,
            'balance_before': balance_before,
            'balance_after': balance_after,
            'description': description,
            'topup_id': topup_id,
            'pos_order_id': pos_order_id,
            'payment_id': payment_id,
        })
        
        return transaction
    
    @api.model
    def get_card_transaction_history(self, card_id, date_from=None, date_to=None):
        """
        Get transaction history for a specific card
        
        Args:
            card_id: ID of the card
            date_from: Start date filter (optional)
            date_to: End date filter (optional)
        
        Returns:
            List of transaction records
        """
        domain = [('card_id', '=', card_id)]
        
        if date_from:
            domain.append(('transaction_date', '>=', date_from))
        if date_to:
            domain.append(('transaction_date', '<=', date_to))
        
        return self.search(domain)
