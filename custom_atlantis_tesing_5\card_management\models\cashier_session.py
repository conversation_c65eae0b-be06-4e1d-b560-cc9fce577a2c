# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class SessionPaymentJournal(models.TransientModel):
    """Transient model to display payment journal breakdown in list view"""
    _name = 'session.payment.journal'
    _description = 'تفصيل طرق الدفع للجلسة'
    _order = 'amount desc'

    session_id = fields.Many2one('cashier.session', string='الجلسة')
    journal_name = fields.Char(string='طريقة الدفع', required=True)
    amount = fields.Float(string='المبلغ', digits=(16, 2))
    currency_id = fields.Many2one('res.currency', string='العملة')
    transaction_count = fields.Integer(string='عدد المعاملات')
    is_summary = fields.Boolean(string='خط الملخص', default=False)


class CashierSession(models.Model):
    _name = 'cashier.session'
    _description = 'جلسة الكاشير لتتبع الأموال'
    _order = 'start_time desc'
    _rec_name = 'display_name'

    # Basic Session Info
    user_id = fields.Many2one(
        'res.users',
        string='الكاشير',
        required=True,
        default=lambda self: self.env.user,
        help='المستخدم الذي فتح هذه الجلسة'
    )

    start_time = fields.Datetime(
        string='بداية الجلسة',
        required=True,
        default=fields.Datetime.now,
        help='متى تم بدء الجلسة'
    )

    end_time = fields.Datetime(
        string='نهاية الجلسة',
        help='متى تم إنهاء الجلسة'
    )

    state = fields.Selection([
        ('open', 'مفتوحة'),
        ('closed', 'مغلقة'),
    ], string='الحالة', default='open', required=True)
    
    # Computed Fields
    display_name = fields.Char(
        string='اسم الجلسة',
        compute='_compute_display_name',
        store=True
    )

    duration = fields.Float(
        string='المدة (ساعات)',
        compute='_compute_duration',
        help='مدة الجلسة بالساعات'
    )

    total_money_collected = fields.Float(
        string='إجمالي الأموال المحصلة',
        compute='_compute_money_totals',
        digits=(16, 2),
        help='إجمالي الأموال المحصلة خلال هذه الجلسة'
    )
    
    # Removed cash_collected and digital_collected - using payment_journal_breakdown instead
    
    cards_created_count = fields.Integer(
        string='البطاقات المنشأة',
        compute='_compute_transaction_counts',
        help='عدد البطاقات الجديدة المنشأة في هذه الجلسة'
    )

    topups_count = fields.Integer(
        string='عمليات الشحن المنجزة',
        compute='_compute_transaction_counts',
        help='عدد عمليات الشحن المعالجة في هذه الجلسة'
    )

    # Currency
    currency_id = fields.Many2one(
        'res.currency',
        string='العملة',
        default=lambda self: self.env.company.currency_id,
        help='عملة الشركة'
    )

    # Removed dashboard fields

    # Payment journal breakdown
    payment_journal_ids = fields.One2many(
        'session.payment.journal',
        'session_id',
        string='طرق الدفع',
        compute='_compute_payment_journals',
        help='تفصيل حسب طرق الدفع المستخدمة'
    )

    @api.depends('user_id', 'start_time')
    def _compute_display_name(self):
        """حساب اسم العرض للجلسة"""
        for session in self:
            if session.start_time and session.user_id:
                date_str = session.start_time.strftime('%Y-%m-%d %H:%M')
                session.display_name = f"{session.user_id.name} - {date_str}"
            else:
                session.display_name = 'جلسة جديدة'

    @api.depends('start_time', 'end_time')
    def _compute_duration(self):
        """Compute session duration in hours"""
        for session in self:
            if session.start_time and session.end_time:
                delta = session.end_time - session.start_time
                session.duration = delta.total_seconds() / 3600.0
            else:
                session.duration = 0.0

    def _compute_money_totals(self):
        """Compute money collected during this session"""
        for session in self:
            try:
                if not session.start_time or not session.user_id:
                    session.total_money_collected = 0.0
                    continue

                # Get end time (current time if session still open)
                end_time = session.end_time or fields.Datetime.now()

                # Find all card transactions in this time period by this user
                domain = [
                    ('create_uid', '=', session.user_id.id),
                    ('create_date', '>=', session.start_time),
                    ('create_date', '<=', end_time),
                ]

                # Get card creations (initial amounts)
                cards = self.env['resort.card'].search(domain)
                card_money = sum(card.initial_amount for card in cards if card.initial_amount)

                # Get top-ups
                topups = self.env['card.topup'].search(domain)
                topup_money = sum(topup.topup_amount for topup in topups if topup.topup_amount)

                # Total money
                total = card_money + topup_money
                session.total_money_collected = total

            except Exception as e:
                # If computation fails, set to zero
                session.total_money_collected = 0.0

    def _compute_transaction_counts(self):
        """Compute transaction counts during this session"""
        for session in self:
            try:
                if not session.start_time or not session.user_id:
                    session.cards_created_count = 0
                    session.topups_count = 0
                    continue

                # Get end time (current time if session still open)
                end_time = session.end_time or fields.Datetime.now()

                # Count transactions in this time period by this user
                domain = [
                    ('create_uid', '=', session.user_id.id),
                    ('create_date', '>=', session.start_time),
                    ('create_date', '<=', end_time),
                ]

                session.cards_created_count = self.env['resort.card'].search_count(domain)
                session.topups_count = self.env['card.topup'].search_count(domain)

            except Exception as e:
                # If computation fails, set to zero
                session.cards_created_count = 0
                session.topups_count = 0

    @api.model
    def get_current_session(self, user_id=None):
        """Get current open session for user"""
        if not user_id:
            user_id = self.env.user.id
        
        return self.search([
            ('user_id', '=', user_id),
            ('state', '=', 'open')
        ], limit=1)

    @api.model
    def action_start_session(self):
        """بدء جلسة جديدة"""
        # التحقق من وجود جلسة مفتوحة للمستخدم
        existing_session = self.get_current_session(self.env.user.id)
        if existing_session:
            # إرجاع الجلسة الموجودة بدلاً من خطأ
            return {
                'type': 'ir.actions.act_window',
                'name': 'الجلسة الحالية',
                'res_model': 'cashier.session',
                'res_id': existing_session.id,
                'view_mode': 'form',
                'target': 'current',
                'views': [(self.env.ref('card_management.view_cashier_session_form').id, 'form')],
                'context': {
                    'create': False,
                    'edit': False,
                    'delete': False,
                    'duplicate': False,
                    'form_view_initial_mode': 'readonly',
                },
                'flags': {
                    'mode': 'readonly',
                }
            }

        # إنشاء جلسة جديدة
        session = self.create({
            'user_id': self.env.user.id,
            'start_time': fields.Datetime.now(),
            'state': 'open',
        })

        return {
            'type': 'ir.actions.act_window',
            'name': 'تم بدء الجلسة',
            'res_model': 'cashier.session',
            'res_id': session.id,
            'view_mode': 'form',
            'target': 'current',
            'views': [(self.env.ref('card_management.view_cashier_session_form').id, 'form')],
            'context': {
                'create': False,
                'edit': False,
                'delete': False,
                'duplicate': False,
                'form_view_initial_mode': 'readonly',
            },
            'flags': {
                'mode': 'readonly',
            }
        }

    def action_end_session(self):
        """إنهاء الجلسة الحالية"""
        self.ensure_one()
        if self.state != 'open':
            raise UserError(_('الجلسة مغلقة بالفعل.'))

        self.write({
            'end_time': fields.Datetime.now(),
            'state': 'closed',
        })

        # Just refresh the current form to show the updated state
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_print_session_report(self):
        """طباعة تقرير الجلسة على الورق الحراري"""
        self.ensure_one()
        return self.env.ref('card_management.action_session_thermal_report').report_action(self)

    def refresh_session_data(self):
        """إجبار تحديث جميع الحقول المحسوبة"""
        self.ensure_one()
        # إبطال التخزين المؤقت لإجبار إعادة الحساب
        self.invalidate_cache()
        # تشغيل إعادة حساب الحقول المحسوبة
        self._compute_payment_journals()
        self._compute_transaction_counts()
        self._compute_money_totals()
        return True



    def _get_payment_journal_data(self):
        """Get payment journal breakdown data"""
        if not self.start_time or not self.user_id:
            return {}

        # Get end time (current time if session still open)
        end_time = self.end_time or fields.Datetime.now()

        # Group by payment journal
        journal_data = {}

        # Find all customer payments created during this session
        payment_domain = [
            ('create_uid', '=', self.user_id.id),
            ('create_date', '>=', self.start_time),
            ('create_date', '<=', end_time),
            ('payment_type', '=', 'inbound'),
            ('partner_type', '=', 'customer'),
            ('state', '=', 'posted'),
        ]

        payments = self.env['account.payment'].search(payment_domain)

        # Process payments by journal
        for payment in payments:
            # Check if this payment is related to card topups
            is_card_payment = False

            # Check if payment reference contains card-related keywords
            if payment.payment_reference:
                ref_lower = payment.payment_reference.lower()
                if any(keyword in ref_lower for keyword in ['card', 'top-up', 'topup', 'initial']):
                    is_card_payment = True

            # Also check if payment is linked to topup records
            if not is_card_payment:
                topup_with_payment = self.env['card.topup'].search([
                    ('payment_id', '=', payment.id)
                ], limit=1)
                if topup_with_payment:
                    is_card_payment = True

            # Only include card-related payments
            if is_card_payment:
                journal_name = payment.journal_id.name
                if journal_name not in journal_data:
                    journal_data[journal_name] = {'amount': 0.0, 'count': 0}
                journal_data[journal_name]['amount'] += payment.amount
                journal_data[journal_name]['count'] += 1

        return journal_data

    @api.model
    def get_daily_summary(self, date=None):
        """Get daily summary of all sessions"""
        if not date:
            date = fields.Date.today()
        
        # Get all sessions for the date
        sessions = self.search([
            ('start_time', '>=', f'{date} 00:00:00'),
            ('start_time', '<=', f'{date} 23:59:59'),
        ])
        
        summary = {
            'date': date,
            'total_money': sum(sessions.mapped('total_money_collected')),
            'total_cards': sum(sessions.mapped('cards_created_count')),
            'total_topups': sum(sessions.mapped('topups_count')),
            'sessions': sessions,
        }
        
        return summary

    # Removed dashboard computation method

    def _compute_payment_journals(self):
        """Compute payment method breakdown by journals"""
        for session in self:
            # Clear existing records
            session.payment_journal_ids.unlink()

            if not session.start_time or not session.user_id:
                continue

            try:
                # Get payment data
                journal_data = session._get_payment_journal_data()

                # Create records for each payment method
                records_to_create = []
                total_amount = 0.0
                total_transactions = 0

                for journal_name, data in journal_data.items():
                    records_to_create.append({
                        'session_id': session.id,
                        'journal_name': journal_name,
                        'amount': data['amount'],
                        'currency_id': session.currency_id.id,
                        'transaction_count': data['count'],
                        'is_summary': False,
                    })
                    total_amount += data['amount']
                    total_transactions += data['count']

                # Add summary line if there's data
                if journal_data:
                    records_to_create.append({
                        'session_id': session.id,
                        'journal_name': 'TOTAL',
                        'amount': total_amount,
                        'currency_id': session.currency_id.id,
                        'transaction_count': total_transactions,
                        'is_summary': True,
                    })

                # Create all records at once
                if records_to_create:
                    self.env['session.payment.journal'].create(records_to_create)

            except Exception as e:
                # Create error record
                self.env['session.payment.journal'].create({
                    'session_id': session.id,
                    'journal_name': f'Error: {str(e)}',
                    'amount': 0.0,
                    'currency_id': session.currency_id.id,
                    'transaction_count': 0,
                    'is_summary': False,
                })

    # Removed action_show_current_session method
