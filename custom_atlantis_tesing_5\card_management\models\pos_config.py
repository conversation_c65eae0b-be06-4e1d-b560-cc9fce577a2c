from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    # Card Management Settings
    enable_card_management = fields.<PERSON><PERSON><PERSON>(
        string='تفعيل إدارة البطاقات',
        default=True,
        help='Enable card management features in POS'
    )
    
    require_customer_for_payment = fields.<PERSON><PERSON>an(
        string='يتطلب عميل للدفع',
        default=True,
        help='Require customer selection before payment'
    )

    # Refund Security Settings
    require_manager_approval_for_refunds = fields.<PERSON><PERSON>an(
        string='Manager Approval for Refunds',
        default=True,
        help='Require manager barcode approval for all refunds'
    )


class PosSession(models.Model):
    _inherit = 'pos.session'

    def _loader_params_pos_config(self):
        """Load refund security config for POS"""
        result = super()._loader_params_pos_config()
        result['search_params']['fields'].extend(['require_manager_approval_for_refunds'])
        return result
