# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class PosOrder(models.Model):
    _inherit = 'pos.order'

    def action_pos_order_paid(self):
        """Override to create transaction records when order is paid"""
        result = super().action_pos_order_paid()

        # Create transaction records for card customers
        for order in self:
            if order.partner_id:
                # Check if customer has a card
                card = self.env['resort.card'].search([
                    ('partner_id', '=', order.partner_id.id),
                    ('active', '=', True)
                ], limit=1)

                if card:
                    # Check if this is a refund (negative amount)
                    if order.amount_total < 0:
                        # This is a refund - add money back to card
                        self.env['card.transaction'].create_transaction(
                            card_id=card.id,
                            transaction_type='refund',
                            amount=abs(order.amount_total),  # Positive for refunds
                            description=f'POS Refund - Order {order.name}',
                            pos_order_id=order.id
                        )
                    elif order.amount_total > 0:
                        # This is a purchase - deduct money from card
                        self.env['card.transaction'].create_transaction(
                            card_id=card.id,
                            transaction_type='purchase',
                            amount=-order.amount_total,  # Negative for purchases
                            description=f'POS Purchase - Order {order.name}',
                            pos_order_id=order.id
                        )

        return result

    card_id = fields.Many2one(
        'resort.card',
        string='Card Used',
        help='Card used for this transaction (if any)'
    )
