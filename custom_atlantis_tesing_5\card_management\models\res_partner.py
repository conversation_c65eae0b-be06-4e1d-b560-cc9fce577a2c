# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # Card Management Fields
    card_status_id = fields.Many2one(
        'card.status',
        string='حالة البطاقة',
        help='Current status of the customer card'
    )
    card_assigned_date = fields.Datetime(
        string='تاريخ تعيين البطاقة',
        help='Date when the card was assigned to this customer'
    )
    card_notes = fields.Text(
        string='ملاحظات البطاقة',
        help='Additional notes about the card'
    )
    has_card = fields.Bo<PERSON>an(
        string='لديه بطاقة',
        compute='_compute_has_card',
        store=True,
        help='Indicates if customer has a card assigned'
    )
    card_balance = fields.Monetary(
        string='رصيد البطاقة',
        compute='_compute_card_balance',
        help='Current card balance (customer credit)'
    )

    @api.depends('barcode')
    def _compute_has_card(self):
        """Compute if customer has a card based on barcode field"""
        for partner in self:
            partner.has_card = bool(partner.barcode)

    def _compute_card_balance(self):
        """Compute card balance using customer credit"""
        for partner in self:
            partner.card_balance = partner.credit

    @api.model
    def create(self, vals):
        """Override create to set default card status when barcode is assigned"""
        partner = super(ResPartner, self).create(vals)
        if partner.barcode and not partner.card_status_id:
            default_status = self.env['card.status'].get_default_active_status()
            if default_status:
                partner.card_status_id = default_status.id
                partner.card_assigned_date = fields.Datetime.now()
        return partner

    def write(self, vals):
        """Override write to handle card status changes"""
        result = super(ResPartner, self).write(vals)
        
        # If barcode is being assigned for the first time
        if 'barcode' in vals:
            for partner in self:
                if partner.barcode and not partner.card_status_id:
                    default_status = self.env['card.status'].get_default_active_status()
                    if default_status:
                        partner.card_status_id = default_status.id
                        partner.card_assigned_date = fields.Datetime.now()
                elif not partner.barcode:
                    # If barcode is removed, set status to inactive
                    inactive_status = self.env['card.status'].get_inactive_status()
                    if inactive_status:
                        partner.card_status_id = inactive_status.id
        
        return result

    def action_assign_card(self):
        """Action to assign a new card to customer"""
        self.ensure_one()
        if self.barcode:
            raise UserError(_('Customer already has a card assigned.'))
        
        return {
            'name': _('Assign Card'),
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'form_view_initial_mode': 'edit'}
        }

    def action_deactivate_card(self):
        """Action to deactivate customer card"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        inactive_status = self.env['card.status'].get_inactive_status()
        if inactive_status:
            self.card_status_id = inactive_status.id
        return True

    def action_report_lost_card(self):
        """Action to report card as lost"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        lost_status = self.env['card.status'].get_lost_status()
        if lost_status:
            self.card_status_id = lost_status.id
        return True

    def action_report_stolen_card(self):
        """Action to report card as stolen"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        stolen_status = self.env['card.status'].get_stolen_status()
        if stolen_status:
            self.card_status_id = stolen_status.id
        return True

    def action_reactivate_card(self):
        """Action to reactivate customer card"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))
        
        active_status = self.env['card.status'].get_default_active_status()
        if active_status:
            self.card_status_id = active_status.id
        return True

    def action_topup_card(self):
        """Action to top up customer card (register payment)"""
        self.ensure_one()
        if not self.barcode:
            raise UserError(_('Customer does not have a card assigned.'))

        return {
            'name': _('Top Up Card - Register Payment'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_payment_type': 'inbound',
                'default_partner_type': 'customer',
                'default_amount': 0.0,
            }
        }

    @api.constrains('barcode')
    def _check_barcode_format(self):
        """Validate barcode format (numeric only)"""
        for partner in self:
            if partner.barcode and not partner.barcode.isdigit():
                raise ValidationError(_('Card barcode must contain only numeric characters.'))
