from odoo import models, fields, api


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    @api.model
    def get_manager_by_barcode(self, barcode):
        """Find manager employee by barcode"""
        if not barcode:
            return False

        # Find employee with this barcode
        employee = self.search([('barcode', '=', barcode)], limit=1)

        if not employee:
            return False

        # Check if this employee's user is a POS manager
        if employee.user_id and employee.user_id.has_group('point_of_sale.group_pos_manager'):
            return {
                'id': employee.id,
                'name': employee.name,
                'user_id': employee.user_id.id,
                'is_manager': True
            }

        return False


class PosOrder(models.Model):
    _inherit = 'pos.order'

    refund_approved_by = fields.Many2one(
        'hr.employee',
        string='Refund Approved By',
        help='Manager who approved this refund',
        readonly=True
    )

    @api.model
    def _order_fields(self, ui_order):
        """Include refund approval data when creating orders"""
        fields = super()._order_fields(ui_order)
        if ui_order.get('refund_approved_by'):
            fields['refund_approved_by'] = ui_order['refund_approved_by']
        return fields
