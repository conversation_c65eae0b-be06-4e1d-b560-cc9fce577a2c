# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class ResortCard(models.Model):
    _name = 'resort.card'
    _description = 'Resort Card'
    _order = 'name'
    _rec_name = 'display_name'

    # Card Information
    name = fields.Char(
        string='اسم العميل',
        required=True,
        help='Name of the customer who owns this card'
    )
    phone = fields.Char(
        string='رقم الهاتف',
        help='Customer phone number (optional)'
    )
    barcode = fields.Char(
        string='رقم البطاقة',
        copy=False,
        help='Unique card number (auto-generated if empty)'
    )
    
    # Technical Fields
    partner_id = fields.Many2one(
        'res.partner',
        string='العميل',
        help='Linked customer record'
    )
    card_status_id = fields.Many2one(
        'card.status',
        string='حالة البطاقة',
        default=lambda self: self._get_default_status(),
        help='Current status of the card'
    )

    # Top-up Fields
    initial_amount = fields.Float(
        string='مبلغ الشحن الأولي',
        digits=(16, 2),
        help='Amount to add to card when creating (optional)'
    )

    payment_journal_id = fields.Many2one(
        'account.journal',
        string='طريقة الدفع',
        domain="[('type', 'in', ['cash', 'bank'])]",
        help='Journal for initial payment (required if initial amount > 0)'
    )

    # Computed Fields
    display_name = fields.Char(
        string='اسم العرض',
        compute='_compute_display_name',
        store=True
    )
    card_balance = fields.Float(
        string='رصيد البطاقة',
        compute='_compute_card_balance',
        digits=(16, 2),
        help='Current card balance (customer credit)'
    )

    card_status_code = fields.Char(
        string='Status Code',
        related='card_status_id.code',
        readonly=True,
        help='Status code for visibility conditions'
    )

    # Dates
    create_date = fields.Datetime(
        string='تاريخ إنشاء البطاقة',
        readonly=True
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Uncheck to archive this card'
    )

    _sql_constraints = [
        ('barcode_uniq', 'unique(barcode)', 'Card barcode must be unique!'),
    ]

    @api.depends('name', 'barcode')
    def _compute_display_name(self):
        """Compute display name for card"""
        for card in self:
            card.display_name = f"{card.name} - {card.barcode}" if card.name and card.barcode else card.name or card.barcode or 'New Card'

    @api.depends('partner_id.credit')
    def _compute_card_balance(self):
        """Compute card balance from partner credit (show as positive for customers)"""
        for card in self:
            if card.partner_id:
                # Convert negative credit to positive balance for customer display
                card.card_balance = abs(card.partner_id.credit) if card.partner_id.credit < 0 else card.partner_id.credit
            else:
                card.card_balance = 0.0

    def _get_default_status(self):
        """Get default active status"""
        return self.env['card.status'].search([('code', '=', 'active')], limit=1)

    # Note: No barcode validation needed since card numbers are auto-generated

    @api.constrains('initial_amount', 'payment_journal_id')
    def _check_payment_journal(self):
        """Validate payment journal when initial amount is provided"""
        for card in self:
            if card.initial_amount > 0 and not card.payment_journal_id:
                raise ValidationError(_('Payment journal is required when initial amount is provided.'))

    @api.model
    def create(self, vals):
        """Override create to automatically create customer and process initial payment"""
        # Generate card number if not provided or is default
        if not vals.get('barcode') or vals.get('barcode') == 'NEW':
            try:
                sequence = self.env['ir.sequence'].next_by_code('resort.card.barcode')
                if not sequence:
                    # Fallback if sequence doesn't exist yet (99 prefix + 12 digits)
                    card_count = self.search_count([])
                    sequence = f"99{str(card_count + 1).zfill(12)}"
                vals['barcode'] = sequence
                print(f"Generated card number: {sequence}")  # Debug
            except Exception as e:
                # Ultimate fallback (99 prefix + timestamp)
                import time
                timestamp = str(int(time.time()))[-12:]  # Last 12 digits of timestamp
                sequence = f"99{timestamp}"
                vals['barcode'] = sequence
                print(f"Fallback card number: {sequence}, Error: {e}")  # Debug

        card = super(ResortCard, self).create(vals)
        card._create_customer()

        # Process initial top-up if amount provided
        if card.initial_amount > 0:
            card._process_initial_topup()

        return card

    def write(self, vals):
        """Override write to update customer information"""
        result = super(ResortCard, self).write(vals)
        
        # Update customer if name, phone, or barcode changed
        if any(field in vals for field in ['name', 'phone', 'barcode']):
            for card in self:
                card._update_customer()
        
        return result

    def _create_customer(self):
        """Create corresponding customer record"""
        self.ensure_one()
        if not self.partner_id:
            partner_vals = {
                'name': self.name,
                'phone': self.phone,
                'barcode': self.barcode,
                'is_company': False,
                'customer_rank': 1,
                'supplier_rank': 0,
            }
            partner = self.env['res.partner'].create(partner_vals)
            self.partner_id = partner.id

    def _update_customer(self):
        """Update corresponding customer record"""
        self.ensure_one()
        if self.partner_id:
            self.partner_id.write({
                'name': self.name,
                'phone': self.phone,
                'barcode': self.barcode,
            })

    def _process_initial_topup(self):
        """Process initial top-up payment"""
        self.ensure_one()
        if not self.partner_id or self.initial_amount <= 0:
            return

        # Create payment record
        payment_vals = {
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.partner_id.id,
            'amount': self.initial_amount,
            'date': fields.Date.today(),
            'payment_reference': f'Initial top-up for card {self.barcode}',
            'journal_id': self.payment_journal_id.id if self.payment_journal_id else None,
        }

        payment = self.env['account.payment'].create(payment_vals)
        payment.action_post()  # Confirm the payment

        # Create transaction record
        self.env['card.transaction'].create_transaction(
            card_id=self.id,
            transaction_type='topup',
            amount=self.initial_amount,
            description=f'Initial card top-up via {self.payment_journal_id.name}',
            payment_id=payment.id
        )

        # Refresh the card balance and clear initial amount and journal
        self._compute_card_balance()
        self.initial_amount = 0.0
        self.payment_journal_id = False

        # Force refresh of the form view
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_topup_card(self):
        """View all payments for this card's customer"""
        self.ensure_one()
        if not self.partner_id:
            raise UserError(_('No customer linked to this card.'))

        return {
            'name': f'Payments for {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'view_mode': 'tree,form',
            'domain': [('partner_id', '=', self.partner_id.id)],
            'context': {
                'default_partner_id': self.partner_id.id,
                'default_payment_type': 'inbound',
                'default_partner_type': 'customer',
            }
        }

    def action_report_lost(self):
        """Report card as lost (ready for reissue)"""
        self.ensure_one()
        lost_status = self.env['card.status'].search([('code', '=', 'lost')], limit=1)
        if lost_status:
            self.card_status_id = lost_status.id
        else:
            raise UserError(_('Lost status not found. Please contact administrator.'))

        # Don't archive - keep card active for reissue process
        # The card will be archived only when a new card is issued

        # Return action to reload the form view to update button visibility
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'resort.card',
            'res_id': self.id,
            'view_mode': 'form',
            'views': [(False, 'form')],
            'target': 'current',
            'context': {
                'show_notification': True,
                'notification_message': _('Card has been reported as lost. You can now reissue a new card using the "Reissue Card" button.'),
                'notification_type': 'warning'
            }
        }



    def action_view_customer(self):
        """View linked customer record"""
        self.ensure_one()
        if not self.partner_id:
            raise UserError(_('No customer linked to this card.'))
        
        return {
            'name': _('Customer'),
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner',
            'res_id': self.partner_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_deactivate(self):
        """Deactivate card"""
        self.ensure_one()
        inactive_status = self.env['card.status'].search([('code', '=', 'inactive')], limit=1)
        if inactive_status:
            self.card_status_id = inactive_status.id

        # Return action to reload the form view to update button visibility
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'resort.card',
            'res_id': self.id,
            'view_mode': 'form',
            'views': [(False, 'form')],
            'target': 'current',
        }

    def action_reactivate(self):
        """Reactivate card"""
        self.ensure_one()
        active_status = self.env['card.status'].search([('code', '=', 'active')], limit=1)
        if active_status:
            self.card_status_id = active_status.id

        # Return action to reload the form view to update button visibility
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'resort.card',
            'res_id': self.id,
            'view_mode': 'form',
            'views': [(False, 'form')],
            'target': 'current',
        }

    def _get_transaction_history(self):
        """Get transaction history for this card"""
        self.ensure_one()

        # Get all card transactions
        transactions = self.env['card.transaction'].search([
            ('card_id', '=', self.id)
        ], order='transaction_date desc')

        return transactions

    def action_print_balance_report(self):
        """Print card balance report"""
        self.ensure_one()
        return self.env.ref('card_management.action_card_balance_report').report_action(self)

    def action_print_qr_code(self):
        """Print card QR code for preprinted cards"""
        self.ensure_one()
        return self.env.ref('card_management.action_card_qr_print_report').report_action(self)

    def action_print_qr_code_ps80_79(self):
        """Print card QR code for PS80_79 preprinted cards"""
        self.ensure_one()
        return self.env.ref('card_management.action_card_qr_print_ps80_79').report_action(self)

    def get_card_pin(self):
        """Get the expected PIN for this card"""
        self.ensure_one()

        # Try to get PIN from phone number (last 4 digits)
        if self.phone:
            phone_digits = ''.join(filter(str.isdigit, self.phone))
            if len(phone_digits) >= 4:
                return phone_digits[-4:]

        # Fallback: use last 4 digits of card barcode
        if self.barcode and len(self.barcode) >= 4:
            return self.barcode[-4:]

        # Final fallback: default PIN
        return "0000"

    def action_show_pin(self):
        """Show the expected PIN for this card (for testing)"""
        self.ensure_one()
        pin = self.get_card_pin()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Card PIN'),
                'message': _('Expected PIN for this card: %s') % pin,
                'type': 'info',
                'sticky': True,
            }
        }

    def validate_pin(self, entered_pin):
        """Validate entered PIN against card PIN"""
        self.ensure_one()
        expected_pin = self.get_card_pin()
        return str(entered_pin) == str(expected_pin)

    @api.model
    def validate_card_pin(self, card_id, entered_pin):
        """API method to validate PIN from POS"""
        try:
            card = self.browse(card_id)
            if not card.exists():
                return {'success': False, 'message': 'Card not found'}

            if card.validate_pin(entered_pin):
                return {'success': True, 'message': 'PIN validated successfully'}
            else:
                return {'success': False, 'message': 'Invalid PIN'}

        except Exception as e:
            return {'success': False, 'message': f'Validation error: {str(e)}'}

    def action_reissue_card(self):
        """Reissue a new card for lost cards"""
        self.ensure_one()

        if self.card_status_id.code != 'lost':
            raise UserError(_('Card reissue is only available for lost cards.'))

        # Open wizard to get new card barcode
        return {
            'name': _('Reissue Card'),
            'type': 'ir.actions.act_window',
            'res_model': 'card.reissue.wizard',
            'view_mode': 'form',
            'views': [(False, 'form')],
            'target': 'new',
            'context': {
                'default_old_card_id': self.id,
                'default_customer_name': self.name,
                'default_phone': self.phone,
                'default_current_balance': self.card_balance,
            }
        }
