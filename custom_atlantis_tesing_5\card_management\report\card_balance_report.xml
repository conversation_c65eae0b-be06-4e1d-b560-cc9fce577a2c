<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Card Balance Report Template -->
    <template id="card_balance_report">
        <t t-call="web.basic_layout">
            <div class="page" style="padding: 20px; font-family: Arial, sans-serif;">

                <!-- Report Header -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 style="color: #2E86AB; margin-bottom: 10px;">Card Balance Report</h2>
                        <h4 style="color: #666; margin-bottom: 20px;">Transaction History</h4>
                    </div>
                </div>

                <!-- Card Information -->
                <t t-foreach="docs" t-as="card">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #2E86AB;">
                                <div class="row">
                                    <div class="col-6">
                                        <h5 style="margin: 0; color: #2E86AB;">Card Information</h5>
                                        <p style="margin: 5px 0;"><strong>Customer Name:</strong> <span t-field="card.name"/></p>
                                        <p style="margin: 5px 0;"><strong>Card Barcode:</strong> <span t-field="card.barcode"/></p>
                                        <p style="margin: 5px 0;"><strong>Phone:</strong> <span t-field="card.phone"/></p>
                                    </div>
                                    <div class="col-6 text-right">
                                        <h5 style="margin: 0; color: #2E86AB;">Current Balance</h5>
                                        <h3 style="margin: 10px 0; color: #28a745;">
                                            <span t-esc="'{:,.2f}'.format(card.card_balance)"/> JD
                                        </h3>
                                        <p style="margin: 5px 0;"><strong>Status:</strong> <span t-field="card.card_status_id.name"/></p>
                                        <p style="margin: 5px 0;"><strong>Created:</strong> <span t-esc="card.create_date.strftime('%Y-%m-%d')"/></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transaction History -->
                    <div class="row">
                        <div class="col-12">
                            <h5 style="color: #2E86AB; margin-bottom: 15px;">Transaction History</h5>
                            
                            <t t-set="transactions" t-value="card._get_transaction_history()"/>
                            
                            <t t-if="transactions">
                                <table class="table table-striped" style="font-size: 12px;">
                                    <thead style="background: #2E86AB; color: white;">
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th class="text-right">Amount</th>
                                            <th class="text-right">Balance Before</th>
                                            <th class="text-right">Balance After</th>
                                            <th>User</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-foreach="transactions" t-as="transaction">
                                            <tr>
                                                <td><span t-esc="transaction.transaction_date.strftime('%Y-%m-%d %H:%M')"/></td>
                                                <td>
                                                    <span t-if="transaction.transaction_type == 'topup'" 
                                                          class="badge badge-success">Top-up</span>
                                                    <span t-elif="transaction.transaction_type == 'purchase'" 
                                                          class="badge badge-primary">Purchase</span>
                                                    <span t-elif="transaction.transaction_type == 'refund'" 
                                                          class="badge badge-warning">Refund</span>
                                                    <span t-else="" 
                                                          class="badge badge-secondary">Adjustment</span>
                                                </td>
                                                <td><span t-field="transaction.description"/></td>
                                                <td class="text-right">
                                                    <span t-if="transaction.amount >= 0" style="color: #28a745;">
                                                        +<span t-esc="'{:,.2f}'.format(transaction.amount)"/>
                                                    </span>
                                                    <span t-else="" style="color: #dc3545;">
                                                        <span t-esc="'{:,.2f}'.format(transaction.amount)"/>
                                                    </span>
                                                </td>
                                                <td class="text-right"><span t-esc="'{:,.2f}'.format(transaction.balance_before)"/></td>
                                                <td class="text-right"><span t-esc="'{:,.2f}'.format(transaction.balance_after)"/></td>
                                                <td><span t-field="transaction.user_id.name"/></td>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </t>
                            
                            <t t-else="">
                                <div class="alert alert-info text-center">
                                    <h6>No transactions found for this card</h6>
                                    <p>This card has no transaction history yet.</p>
                                </div>
                            </t>
                        </div>
                    </div>

                    <!-- Summary Section -->
                    <t t-if="transactions">
                        <div class="row mt-4">
                            <div class="col-12">
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                    <h6 style="color: #2E86AB; margin-bottom: 15px;">Transaction Summary</h6>
                                    <div class="row">
                                        <div class="col-3 text-center">
                                            <h6>Total Top-ups</h6>
                                            <h5 style="color: #28a745;">
                                                <span t-esc="'{:,.2f}'.format(sum(t.amount for t in transactions if t.transaction_type == 'topup'))"/> JD
                                            </h5>
                                        </div>
                                        <div class="col-3 text-center">
                                            <h6>Total Purchases</h6>
                                            <h5 style="color: #dc3545;">
                                                <span t-esc="'{:,.2f}'.format(abs(sum(t.amount for t in transactions if t.transaction_type == 'purchase')))"/> JD
                                            </h5>
                                        </div>
                                        <div class="col-3 text-center">
                                            <h6>Total Refunds</h6>
                                            <h5 style="color: #ffc107;">
                                                <span t-esc="'{:,.2f}'.format(sum(t.amount for t in transactions if t.transaction_type == 'refund'))"/> JD
                                            </h5>
                                        </div>
                                        <div class="col-3 text-center">
                                            <h6>Current Balance</h6>
                                            <h5 style="color: #2E86AB;">
                                                <span t-esc="'{:,.2f}'.format(card.card_balance)"/> JD
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>

                    <!-- Report Footer -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <hr/>
                            <p style="color: #666; font-size: 11px;">
                                Report generated on <span t-esc="datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')"/> 
                                by <span t-esc="user.name"/>
                            </p>
                        </div>
                    </div>
                </t>
            </div>
        </t>
    </template>
</odoo>
