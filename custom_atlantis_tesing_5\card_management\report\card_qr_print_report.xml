<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Custom Paper Format for Card Printing -->
    <record id="paperformat_card_ps80_79" model="report.paperformat">
        <field name="name">Card PS80_79 Format</field>
        <field name="default" eval="False"/>
        <field name="format">custom</field>
        <field name="page_height">79</field>
        <field name="page_width">80</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">2</field>
        <field name="margin_bottom">2</field>
        <field name="margin_left">2</field>
        <field name="margin_right">2</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">0</field>
        <field name="dpi">96</field>
    </record>

    <!-- Card QR Code Print Report Action -->
    <record id="action_card_qr_print_report" model="ir.actions.report">
        <field name="name">Card QR Code Print</field>
        <field name="model">resort.card</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">card_management.card_qr_print_template</field>
        <field name="report_file">card_management.card_qr_print_template</field>
        <field name="binding_model_id" ref="model_resort_card"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="base.paperformat_a4"/>
    </record>

    <!-- Card QR Code Print Report Action for PS80_79 -->
    <record id="action_card_qr_print_ps80_79" model="ir.actions.report">
        <field name="name">Card QR Code Print (PS80_79)</field>
        <field name="model">resort.card</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">card_management.card_qr_print_ps80_79_template</field>
        <field name="report_file">card_management.card_qr_print_ps80_79_template</field>
        <field name="binding_model_id" ref="model_resort_card"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_card_ps80_79"/>
    </record>

    <!-- Card QR Code Print Template -->
    <template id="card_qr_print_template">
        <t t-call="web.basic_layout">
            <div class="page" style="margin: 0; padding: 0; font-family: Arial, sans-serif;">
                
                <!-- Process each card -->
                <t t-foreach="docs" t-as="card">
                    
                    <!-- Card dimensions based on standard card size (85.60 × 53.98 mm) -->
                    <!-- Using CSS to position elements precisely for preprinted cards -->
                    <div class="card-container" style="
                        width: 85.60mm; 
                        height: 53.98mm; 
                        position: relative; 
                        margin: 10mm auto; 
                        border: 1px dashed #ccc; 
                        page-break-after: always;
                        background: transparent;
                    ">
                        
                        <!-- Customer Name positioned in the left area (based on screenshot) -->
                        <div class="customer-name" style="
                            position: absolute;
                            left: 8mm;
                            bottom: 12mm;
                            width: 30mm;
                            height: 8mm;
                            font-size: 11px;
                            font-weight: bold;
                            color: #000;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            word-wrap: break-word;
                            line-height: 1.1;
                            direction: rtl;
                        ">
                            <span t-field="card.name"/>
                        </div>

                        <!-- QR Code positioned in the right area (based on screenshot) -->
                        <div class="qr-code" style="
                            position: absolute;
                            right: 10mm;
                            bottom: 10mm;
                            width: 22mm;
                            height: 22mm;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">
                            <!-- Generate QR code with card barcode -->
                            <t t-set="qr_code_data" t-value="card.barcode"/>
                            <t t-if="qr_code_data">
                                <img t-att-src="'/report/barcode/?type=QR&amp;value=' + qr_code_data + '&amp;width=180&amp;height=180'"
                                     style="max-width: 22mm; max-height: 22mm; width: auto; height: auto;"/>
                            </t>
                        </div>
                        
                        <!-- Card number text (small, for reference) -->
                        <div class="card-number" style="
                            position: absolute;
                            left: 5mm;
                            top: 5mm;
                            font-size: 8px;
                            color: #666;
                        ">
                            <span t-field="card.barcode"/>
                        </div>
                        
                    </div>
                    
                </t>
                
            </div>
        </t>
    </template>

    <!-- Alternative template for multiple cards per page -->
    <template id="card_qr_print_template_multiple">
        <t t-call="web.basic_layout">
            <div class="page" style="margin: 0; padding: 10mm; font-family: Arial, sans-serif;">
                
                <!-- Grid layout for multiple cards per page (2x4 = 8 cards per A4) -->
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10mm;">
                    
                    <t t-foreach="docs" t-as="card">
                        
                        <!-- Individual card -->
                        <div class="card-container" style="
                            width: 85.60mm; 
                            height: 53.98mm; 
                            position: relative; 
                            border: 1px dashed #ccc; 
                            background: transparent;
                        ">
                            
                            <!-- Customer Name -->
                            <div class="customer-name" style="
                                position: absolute;
                                left: 5mm;
                                bottom: 8mm;
                                width: 35mm;
                                height: 10mm;
                                font-size: 10px;
                                font-weight: bold;
                                color: #000;
                                text-align: center;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                word-wrap: break-word;
                                line-height: 1.1;
                            ">
                                <span t-field="card.name"/>
                            </div>
                            
                            <!-- QR Code -->
                            <div class="qr-code" style="
                                position: absolute;
                                right: 8mm;
                                bottom: 8mm;
                                width: 20mm;
                                height: 20mm;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            ">
                                <t t-set="qr_code_data" t-value="card.barcode"/>
                                <t t-if="qr_code_data">
                                    <img t-att-src="'/report/barcode/?type=QR&amp;value=' + qr_code_data + '&amp;width=150&amp;height=150'" 
                                         style="max-width: 20mm; max-height: 20mm; width: auto; height: auto;"/>
                                </t>
                            </div>
                            
                            <!-- Card number -->
                            <div class="card-number" style="
                                position: absolute;
                                left: 5mm;
                                top: 3mm;
                                font-size: 7px;
                                color: #666;
                            ">
                                <span t-field="card.barcode"/>
                            </div>
                            
                        </div>
                        
                    </t>
                    
                </div>
                
            </div>
        </t>
    </template>

    <!-- PS80_79 Card Template - Exact positioning for preprinted cards -->
    <template id="card_qr_print_ps80_79_template">
        <t t-call="web.basic_layout">
            <div class="page" style="margin: 0; padding: 0; font-family: Arial, sans-serif;">

                <!-- Process each card -->
                <t t-foreach="docs" t-as="card">

                    <!-- Card container for PS80_79 dimensions -->
                    <div class="card-container" style="
                        width: 76mm;
                        height: 75mm;
                        position: relative;
                        margin: 0;
                        padding: 0;
                        background: transparent;
                        page-break-after: always;
                    ">

                        <!-- Customer Name positioned for PS80_79 card -->
                        <div class="customer-name" style="
                            position: absolute;
                            left: 6mm;
                            bottom: 15mm;
                            width: 28mm;
                            height: 10mm;
                            font-size: 10px;
                            font-weight: bold;
                            color: #000;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            word-wrap: break-word;
                            line-height: 1.1;
                            direction: rtl;
                        ">
                            <span t-field="card.name"/>
                        </div>

                        <!-- QR Code positioned for PS80_79 card -->
                        <div class="qr-code" style="
                            position: absolute;
                            right: 8mm;
                            bottom: 12mm;
                            width: 20mm;
                            height: 20mm;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">
                            <!-- Generate QR code with card barcode -->
                            <t t-set="qr_code_data" t-value="card.barcode"/>
                            <t t-if="qr_code_data">
                                <img t-att-src="'/report/barcode/?type=QR&amp;value=' + qr_code_data + '&amp;width=160&amp;height=160'"
                                     style="max-width: 20mm; max-height: 20mm; width: auto; height: auto;"/>
                            </t>
                        </div>

                        <!-- Debug info (remove in production) -->
                        <div class="debug-info" style="
                            position: absolute;
                            top: 2mm;
                            left: 2mm;
                            font-size: 6px;
                            color: #999;
                        ">
                            Card: <span t-field="card.barcode"/>
                        </div>

                    </div>

                </t>

            </div>
        </t>
    </template>

</odoo>
