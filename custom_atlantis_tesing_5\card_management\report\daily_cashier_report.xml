<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Daily Cashier Report Template -->
    <template id="daily_cashier_report">
        <t t-call="web.basic_layout">
            <div class="page" style="padding: 20px; font-family: Arial, sans-serif;">

                <!-- Report Title -->
                <div class="row">
                    <div class="col-12 text-center">
                        <h2>Daily Cashier Report</h2>
                        <h4>Card Top-up Transactions</h4>
                    </div>
                </div>

                <!-- Report Info -->
                <div class="row mt-4">
                    <div class="col-6">
                        <strong>Cashier:</strong> <span t-esc="cashier_name"/><br/>
                        <strong>Date:</strong> <span t-esc="report_date"/><br/>
                        <strong>Report Generated:</strong> <span t-esc="datetime.datetime.now().strftime('%d/%m/%Y %H:%M')"/>
                    </div>
                    <div class="col-6 text-right">
                        <strong>Total Transactions:</strong> <span t-esc="len(docs)"/><br/>
                        <strong>Total Amount:</strong> <span t-esc="'%.3f' % total_amount"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/><br/>
                        <strong>Average per Transaction:</strong> <span t-esc="'%.3f' % average_amount"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/>
                    </div>
                </div>

                <!-- Summary Section -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h4>Summary by Payment Method</h4>
                        <table class="table table-sm table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Payment Method</th>
                                    <th class="text-right">Count</th>
                                    <th class="text-right">Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="payment_summary" t-as="method">
                                    <tr>
                                        <td t-esc="method['method'].title()"/>
                                        <td class="text-right" t-esc="method['count']"/>
                                        <td class="text-right">
                                            <span t-esc="'%.3f' % method['total']"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Transaction Details -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Transaction Details</h4>
                        <table class="table table-sm table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Time</th>
                                    <th>Receipt #</th>
                                    <th>Customer</th>
                                    <th>Card #</th>
                                    <th class="text-right">Amount</th>
                                    <th>Payment Method</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="docs" t-as="topup">
                                    <tr>
                                        <td>
                                            <span t-esc="topup.create_date.strftime('%H:%M') if topup.create_date else ''"/>
                                        </td>
                                        <td>TU-<span t-esc="topup.id"/></td>
                                        <td t-esc="topup.customer_name or topup.card_id.name or 'N/A'"/>
                                        <td t-esc="topup.card_barcode"/>
                                        <td class="text-right">
                                            <span t-esc="'%.3f' % topup.topup_amount"/> <span t-esc="topup.env.company.currency_id.name"/>
                                        </td>
                                        <td t-esc="topup.payment_journal_id.name"/>
                                        <td>
                                            <span t-if="topup.state == 'done'" class="badge badge-success">Completed</span>
                                            <span t-if="topup.state == 'draft'" class="badge badge-warning">Draft</span>
                                            <span t-if="topup.state == 'cancelled'" class="badge badge-danger">Cancelled</span>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <th colspan="4">TOTAL</th>
                                    <th class="text-right">
                                        <span t-esc="'%.3f' % total_amount"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/>
                                    </th>
                                    <th colspan="2"></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Cash Reconciliation Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Cash Reconciliation</h4>
                        <table class="table table-sm table-bordered" style="width: 50%;">
                            <tr>
                                <td><strong>Total Cash Collected:</strong></td>
                                <td class="text-right">
                                    <span t-esc="'%.3f' % cash_total"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Card Payments:</strong></td>
                                <td class="text-right">
                                    <span t-esc="'%.3f' % card_total"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Bank Transfers:</strong></td>
                                <td class="text-right">
                                    <span t-esc="'%.3f' % bank_total"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/>
                                </td>
                            </tr>
                            <tr class="table-active">
                                <td><strong>TOTAL EXPECTED:</strong></td>
                                <td class="text-right">
                                    <strong><span t-esc="'%.3f' % total_amount"/> <span t-esc="docs[0].env.company.currency_id.name if docs else 'KWD'"/></strong>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Signatures -->
                <div class="row mt-5">
                    <div class="col-6">
                        <div style="border-top: 1px solid #000; width: 200px; margin-top: 50px;">
                            <strong>Cashier Signature</strong><br/>
                            <span t-esc="cashier_name"/>
                        </div>
                    </div>
                    <div class="col-6 text-right">
                        <div style="border-top: 1px solid #000; width: 200px; margin-top: 50px; margin-left: auto;">
                            <strong>Supervisor Signature</strong><br/>
                            Date: _______________
                        </div>
                    </div>
                </div>

            </div>
        </t>
    </template>
</odoo>
