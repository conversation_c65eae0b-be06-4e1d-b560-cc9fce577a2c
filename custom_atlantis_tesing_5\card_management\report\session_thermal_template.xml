<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Session Thermal Report Template for 80mm Paper -->
    <template id="session_thermal_report">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <div class="page" style="width: 80mm; font-family: Arial, 'Tahoma', sans-serif; font-size: 12px; margin: 0; padding: 5mm;">
                    <meta charset="UTF-8"/>

                <!-- Header -->
                <div style="text-align: center; margin-bottom: 15px;">
                    <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">
                        <t t-if="o.env.company.name">
                            <span t-esc="o.env.company.name"/>
                        </t>
                        <t t-else="">
                            إدارة بطاقات المنتجع
                        </t>
                    </div>
                    <div style="font-size: 14px; font-weight: bold; margin-bottom: 3px;">
                        تقرير الجلسة
                    </div>
                    <div style="font-size: 10px;">
                        <t t-if="o.env.company.street">
                            <span t-esc="o.env.company.street"/>
                        </t>
                        <t t-if="o.env.company.city">
                            <br/><span t-esc="o.env.company.city"/>
                        </t>
                        <t t-if="o.env.company.phone">
                            <br/>هاتف: <span t-esc="o.env.company.phone"/>
                        </t>
                    </div>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Print Info -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>تاريخ الطباعة:</td>
                            <td style="text-align: right;">
                                <span t-esc="datetime.datetime.now().strftime('%d/%m/%Y')"/>
                            </td>
                        </tr>
                        <tr>
                            <td>وقت الطباعة:</td>
                            <td style="text-align: right;">
                                <span t-esc="datetime.datetime.now().strftime('%H:%M:%S')"/>
                            </td>
                        </tr>
                        <tr>
                            <td>طُبع بواسطة:</td>
                            <td style="text-align: right;">
                                <span t-esc="o.env.user.name"/>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Session Info -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>رقم الجلسة:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <span t-esc="o.id"/>
                            </td>
                        </tr>
                        <tr>
                            <td>الكاشير:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <span t-esc="o.user_id.name"/>
                            </td>
                        </tr>
                        <tr>
                            <td>حالة الجلسة:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <t t-if="o.state == 'open'">مفتوحة</t>
                                <t t-elif="o.state == 'closed'">مغلقة</t>
                                <t t-else=""><span t-esc="o.state"/></t>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Session Times -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>وقت الفتح:</td>
                            <td style="text-align: right;">
                                <span t-field="o.start_time" t-options="{'widget': 'datetime', 'format': 'dd/MM/yyyy HH:mm'}"/>
                            </td>
                        </tr>
                        <tr>
                            <td>وقت الإغلاق:</td>
                            <td style="text-align: right;">
                                <t t-if="o.end_time">
                                    <span t-field="o.end_time" t-options="{'widget': 'datetime', 'format': 'dd/MM/yyyy HH:mm'}"/>
                                </t>
                                <t t-else="">
                                    لا تزال مفتوحة
                                </t>
                            </td>
                        </tr>
                        <tr>
                            <td>المدة:</td>
                            <td style="text-align: right;">
                                <t t-if="o.duration > 0">
                                    <span t-esc="'%.1f ساعة' % o.duration"/>
                                </t>
                                <t t-else="">
                                    -
                                </t>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Transaction Summary -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>البطاقات الجديدة المنشأة:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <span t-esc="o.cards_created_count"/>
                            </td>
                        </tr>
                        <tr>
                            <td>عمليات الشحن المعالجة:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <span t-esc="o.topups_count"/>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Payment Methods -->
                <div style="margin-bottom: 10px;">
                    <div style="font-size: 12px; font-weight: bold; text-align: center; margin-bottom: 8px;">
                        طرق الدفع
                    </div>

                    <t t-set="journal_data" t-value="o._get_payment_journal_data()"/>
                    <t t-set="total_amount" t-value="0"/>

                    <t t-if="journal_data">
                        <table style="width: 100%; font-size: 10px;">
                            <t t-foreach="journal_data.items()" t-as="journal_item">
                                <t t-set="journal_name" t-value="journal_item[0]"/>
                                <t t-set="journal_info" t-value="journal_item[1]"/>
                                <t t-set="total_amount" t-value="total_amount + journal_info['amount']"/>
                                <tr>
                                    <td style="padding: 2px 0; max-width: 35mm; word-wrap: break-word;">
                                        <span t-esc="journal_name"/>
                                    </td>
                                    <td style="text-align: center; padding: 2px 0;">
                                        <span t-esc="journal_info['count']"/> معاملة
                                    </td>
                                    <td style="text-align: right; padding: 2px 0;">
                                        <span t-esc="'%.3f' % journal_info['amount']"/> <span t-esc="o.currency_id.name"/>
                                    </td>
                                </tr>
                            </t>
                        </table>

                        <div style="border-bottom: 1px solid #000; margin: 5px 0;"></div>

                        <!-- Total -->
                        <table style="width: 100%; font-size: 11px; font-weight: bold;">
                            <tr>
                                <td>إجمالي المحصل:</td>
                                <td style="text-align: right; font-size: 12px;">
                                    <span t-esc="'%.3f' % total_amount"/> <span t-esc="o.currency_id.name"/>
                                </td>
                            </tr>
                        </table>
                    </t>
                    <t t-else="">
                        <div style="text-align: center; font-style: italic; color: #666;">
                            لم يتم تسجيل أي مدفوعات
                        </div>
                    </t>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Footer -->
                <div style="text-align: center; margin-top: 10px; font-size: 10px;">
                    <div>تقرير الجلسة</div>
                    <div style="margin-top: 5px;">
                        احتفظ بهذا التقرير لسجلاتك
                    </div>
                    <div style="margin-top: 10px; font-size: 8px;">
                        مدعوم بواسطة نظام إدارة البطاقات
                    </div>
                </div>

                <!-- Cut line -->
                <div style="text-align: center; margin-top: 15px; font-size: 10px;">
                    ✂ --------------------------------- ✂
                </div>

                </div>
            </t>
        </t>
    </template>
</odoo>
