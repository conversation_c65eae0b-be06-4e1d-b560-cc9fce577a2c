<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Thermal Receipt Template for 80mm Paper -->
    <template id="topup_thermal_receipt">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <div class="page" style="width: 80mm; font-family: Arial, 'Tahoma', sans-serif; font-size: 12px; margin: 0; padding: 5mm;">
                    <meta charset="UTF-8"/>

                    <!-- Header with Logo -->
                    <div style="text-align: center; margin-bottom: 10px;">
                        <!-- Company Logo -->
                        <t t-if="o.env.company.logo">
                            <div style="margin-bottom: 5px;">
                                <img t-att-src="image_data_uri(o.env.company.logo)"
                                     style="max-width: 60mm; max-height: 20mm; height: auto;"/>
                            </div>
                        </t>

                        <!-- Company Name -->
                        <div style="font-size: 16px; font-weight: bold; margin-bottom: 3px;">
                            <span t-esc="o._get_receipt_company_name()"/>
                        </div>
                        <!-- Company Address -->
                        <t t-if="o.env.company.street or o.env.company.city">
                            <div style="font-size: 11px; margin-bottom: 2px;">
                                <t t-if="o.env.company.street">
                                    <span t-esc="o.env.company.street"/>
                                </t>
                                <t t-if="o.env.company.city">
                                    <t t-if="o.env.company.street">, </t>
                                    <span t-esc="o.env.company.city"/>
                                </t>
                            </div>
                        </t>

                        <!-- Company Contact -->
                        <div style="font-size: 11px; margin-bottom: 2px;">
                            <t t-if="o.env.company.phone">
                                هاتف: <span t-esc="o.env.company.phone"/>
                            </t>
                            <t t-if="o.env.company.email">
                                <t t-if="o.env.company.phone"> | </t>
                                <span t-esc="o.env.company.email"/>
                            </t>
                        </div>

                        <!-- VAT Number if available -->
                        <t t-if="o.env.company.vat">
                            <div style="font-size: 10px; margin-bottom: 2px;">
                                ضريبة القيمة المضافة: <span t-esc="o.env.company.vat"/>
                            </div>
                        </t>
                    <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>
                    <div style="font-size: 14px; font-weight: bold;">
                        إيصال شحن البطاقة
                    </div>
                </div>

                <!-- Transaction Details -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>التاريخ:</td>
                            <td style="text-align: right;">
                                <span t-field="o.create_date" t-options="{'widget': 'datetime', 'format': 'dd/MM/yyyy HH:mm'}"/>
                            </td>
                        </tr>
                        <tr>
                            <td>رقم الإيصال:</td>
                            <td style="text-align: right;">TU-<span t-field="o.id"/></td>
                        </tr>
                        <tr>
                            <td>الكاشير:</td>
                            <td style="text-align: right;">
                                <t t-if="o.cashier_id and o.cashier_id.name != 'OdooBot'">
                                    <span t-esc="o.cashier_id.name"/>
                                </t>
                                <t t-else="">
                                    <span t-esc="o.env.user.name"/>
                                </t>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Customer & Card Info -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>العميل:</td>
                            <td style="text-align: right; font-weight: bold; word-wrap: break-word; max-width: 40mm;">
                                <span t-esc="o._get_receipt_customer_name()"/>
                            </td>
                        </tr>
                        <tr>
                            <td>رقم البطاقة:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <span t-field="o.card_barcode"/>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Financial Details -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>مبلغ الشحن:</td>
                            <td style="text-align: right; font-weight: bold;">
                                <span t-esc="'%.3f' % o.topup_amount"/> <span t-esc="o._get_receipt_currency_symbol()"/>
                            </td>
                        </tr>
                        <tr>
                            <td>الرصيد السابق:</td>
                            <td style="text-align: right;">
                                <t t-set="previous_balance" t-value="abs(o.card_id.partner_id.credit) - o.topup_amount"/>
                                <span t-esc="'%.3f' % previous_balance"/> <span t-esc="o._get_receipt_currency_symbol()"/>
                            </td>
                        </tr>
                        <tr style="border-top: 1px solid #000;">
                            <td style="font-weight: bold;">الرصيد الجديد:</td>
                            <td style="text-align: right; font-weight: bold; font-size: 13px;">
                                <t t-set="new_balance" t-value="abs(o.card_id.partner_id.credit)"/>
                                <span t-esc="'%.3f' % new_balance"/> <span t-esc="o._get_receipt_currency_symbol()"/>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Payment Method -->
                <div style="margin-bottom: 10px;">
                    <table style="width: 100%; font-size: 11px;">
                        <tr>
                            <td>Payment Method:</td>
                            <td style="text-align: right; text-transform: capitalize;">
                                <span t-field="o.payment_method"/>
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="border-bottom: 1px dashed #000; margin: 5px 0;"></div>

                <!-- Footer -->
                <div style="text-align: center; margin-top: 10px; font-size: 10px;">
                    <div>شكراً لزيارتكم!</div>
                    <div style="margin-top: 5px;">
                        احتفظ بهذا الإيصال لسجلاتك
                    </div>
                    <div style="margin-top: 10px; font-size: 8px;">
                        مدعوم بواسطة نظام إدارة البطاقات
                    </div>
                </div>

                <!-- Cut line -->
                <div style="text-align: center; margin-top: 15px; font-size: 10px;">
                    ✂ --------------------------------- ✂
                </div>

                </div>
            </t>
        </t>
    </template>
</odoo>
