<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Custom Paper Format for 80mm Thermal (Define first) -->
    <record id="paperformat_thermal_80mm" model="report.paperformat">
        <field name="name">80mm Thermal Paper</field>
        <field name="default" eval="False"/>
        <field name="format">custom</field>
        <field name="page_height">297</field>
        <field name="page_width">80</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">2</field>
        <field name="margin_bottom">2</field>
        <field name="margin_left">1</field>
        <field name="margin_right">1</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">0</field>
        <field name="dpi">96</field>
    </record>

    <!-- Thermal Receipt Report -->
    <record id="action_topup_thermal_receipt" model="ir.actions.report">
        <field name="name">Top-up Thermal Receipt</field>
        <field name="model">card.topup</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">card_management.topup_thermal_receipt</field>
        <field name="report_file">card_management.topup_thermal_receipt</field>
        <field name="binding_model_id" ref="model_card_topup"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_thermal_80mm"/>
    </record>

    <!-- Session Report Thermal -->
    <record id="action_session_thermal_report" model="ir.actions.report">
        <field name="name">تقرير الجلسة الحراري</field>
        <field name="model">cashier.session</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">card_management.session_thermal_report</field>
        <field name="report_file">card_management.session_thermal_report</field>
        <field name="binding_model_id" ref="model_cashier_session"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_thermal_80mm"/>
    </record>



    <!-- Daily Cashier Report -->
    <record id="action_daily_cashier_report" model="ir.actions.report">
        <field name="name">Daily Cashier Report</field>
        <field name="model">card.topup</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">card_management.daily_cashier_report</field>
        <field name="report_file">card_management.daily_cashier_report</field>
        <field name="binding_model_id" ref="model_card_topup"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="base.paperformat_euro"/>
    </record>

    <!-- Card Balance Report -->
    <record id="action_card_balance_report" model="ir.actions.report">
        <field name="name">Card Balance Report</field>
        <field name="model">resort.card</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">card_management.card_balance_report</field>
        <field name="report_file">card_management.card_balance_report</field>
        <field name="binding_model_id" ref="model_resort_card"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="base.paperformat_euro"/>
    </record>
</odoo>
