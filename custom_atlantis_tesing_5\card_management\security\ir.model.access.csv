id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_card_status_user,card.status.user,model_card_status,base.group_user,1,0,0,0
access_card_status_manager,card.status.manager,model_card_status,point_of_sale.group_pos_manager,1,1,1,1
access_resort_card_user,resort.card.user,model_resort_card,point_of_sale.group_pos_user,1,1,1,0
access_resort_card_manager,resort.card.manager,model_resort_card,point_of_sale.group_pos_manager,1,1,1,1
access_card_kiosk_user,card.kiosk.user,model_card_kiosk,base.group_user,1,1,1,1
access_card_topup_user,card.topup.user,model_card_topup,point_of_sale.group_pos_user,1,1,1,0
access_card_topup_manager,card.topup.manager,model_card_topup,point_of_sale.group_pos_manager,1,1,1,1
access_daily_report_wizard_user,daily.report.wizard.user,model_daily_report_wizard,point_of_sale.group_pos_user,1,1,1,1
access_daily_report_wizard_manager,daily.report.wizard.manager,model_daily_report_wizard,point_of_sale.group_pos_manager,1,1,1,1
access_card_transaction_user,card.transaction.user,model_card_transaction,base.group_user,1,0,0,0
access_card_transaction_manager,card.transaction.manager,model_card_transaction,point_of_sale.group_pos_manager,1,1,1,1
access_cashier_session_user,cashier.session.user,model_cashier_session,base.group_user,1,1,1,0
access_cashier_session_manager,cashier.session.manager,model_cashier_session,point_of_sale.group_pos_manager,1,1,1,1
access_card_reissue_wizard_user,card.reissue.wizard.user,model_card_reissue_wizard,point_of_sale.group_pos_user,1,1,1,1
access_card_reissue_wizard_manager,card.reissue.wizard.manager,model_card_reissue_wizard,point_of_sale.group_pos_manager,1,1,1,1
access_card_reissue_log_user,card.reissue.log.user,model_card_reissue_log,base.group_user,1,0,0,0
access_card_reissue_log_manager,card.reissue.log.manager,model_card_reissue_log,point_of_sale.group_pos_manager,1,1,1,1
access_session_payment_journal_user,session.payment.journal.user,model_session_payment_journal,base.group_user,1,1,1,1
access_session_payment_journal_manager,session.payment.journal.manager,model_session_payment_journal,point_of_sale.group_pos_manager,1,1,1,1
