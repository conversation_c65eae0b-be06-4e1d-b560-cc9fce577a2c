/* Customer Balance Widget Styles */
.customer-balance-widget {
    width: 100%;
    padding: 10px;
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.customer-balance-display {
    display: flex;
    justify-content: center;
    align-items: center;
}

.balance-card {
    background: linear-gradient(135deg, #2E7D32, #4CAF50);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    min-width: 300px;
}

.balance-card i {
    font-size: 18px;
    color: #E8F5E8;
}

.customer-name {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
}

.balance-amount {
    font-size: 18px;
    font-weight: 700;
    background: rgba(255,255,255,0.2);
    padding: 4px 12px;
    border-radius: 4px;
}

/* Customer info in ActionpadWidget (keypad area) */
.pos .actionpad .customer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.pos .actionpad .customer-name {
    font-size: 14px;
    font-weight: 600;
    text-align: center;
}

.pos .actionpad .customer-balance {
    font-size: 11px;
    font-weight: 500;
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    text-align: center;
    white-space: nowrap;
}

/* Card Scan Popup Styles */
.card-scan-popup {
    max-width: 400px;
}

.card-scan-popup .popup-header {
    background: #2E7D32;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-scan-popup .popup-header h3 {
    margin: 0;
    font-size: 18px;
}

.card-scan-popup .popup-body {
    padding: 20px;
    text-align: center;
}

.customer-info h4 {
    color: #2E7D32;
    font-size: 20px;
    margin-bottom: 15px;
}

.balance-info {
    background: #E8F5E8;
    padding: 15px;
    border-radius: 6px;
    margin-top: 10px;
}

.balance-info .label {
    display: block;
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.balance-info .balance {
    display: block;
    color: #2E7D32;
    font-size: 24px;
    font-weight: bold;
}

.card-scan-popup .popup-footer {
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #eee;
}

.card-scan-popup .button.confirm {
    background: #2E7D32;
    color: white;
    border: none;
    padding: 10px 30px;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
}

.card-scan-popup .button.confirm:hover {
    background: #1B5E20;
}

/* Manager Approval Popup Styles */
.manager-approval-popup {
    max-width: 450px;
}

.manager-approval-popup .popup-header {
    background: #FF9800;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.manager-approval-popup .popup-header h3 {
    margin: 0;
    font-size: 18px;
}

.manager-approval-popup .popup-body {
    padding: 20px;
    text-align: center;
}

.manager-approval-popup .popup-body p {
    margin-bottom: 20px;
    color: #333;
    font-size: 16px;
}

.manager-approval-popup .manager-barcode-input {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    border: 2px solid #ddd;
    border-radius: 6px;
    text-align: center;
    letter-spacing: 2px;
    font-family: monospace;
    background: #f8f9fa;
}

.manager-approval-popup .manager-barcode-input:focus {
    border-color: #FF9800;
    outline: none;
    box-shadow: 0 0 5px rgba(255, 152, 0, 0.3);
    background: white;
}

.manager-approval-popup .popup-footer {
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #eee;
}

.manager-approval-popup .button {
    padding: 10px 20px;
    margin: 0 5px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
}

.manager-approval-popup .button.cancel {
    background: #6c757d;
    color: white;
}

.manager-approval-popup .button.confirm {
    background: #FF9800;
    color: white;
}

.manager-approval-popup .button:hover {
    opacity: 0.9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .balance-card {
        min-width: 250px;
        padding: 10px 15px;
        font-size: 14px;
    }

    .customer-name {
        font-size: 14px;
    }

    .balance-amount {
        font-size: 16px;
        padding: 3px 8px;
    }
}
