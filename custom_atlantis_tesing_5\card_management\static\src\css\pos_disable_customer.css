/* Make customer selection button read-only (show but disable) */
.pos .actionpad .set-customer {
    opacity: 0.8 !important;
    cursor: pointer !important;
    background-color: #e9ecef !important;
    color: #495057 !important;
    border: 1px solid #ced4da !important;
}

/* Add security icon to customer button */
.pos .actionpad .set-customer:before {
    content: "🔒 ";
    font-size: 12px;
}

/* Hover effect for read-only customer button */
.pos .actionpad .set-customer:hover {
    background-color: #dee2e6 !important;
    transform: none !important;
}

/* Make customer name click read-only */
.pos .order-info .client-line {
    pointer-events: none !important;
    cursor: default !important;
}

/* Hide customer search in product screen */
.pos .searchbox .search-customer {
    display: none !important;
}

/* Make customer selection in order widget read-only */
.pos .order-widget .client-line .client-name {
    pointer-events: none !important;
    cursor: default !important;
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
}

/* Make customer selection buttons read-only */
.pos .control-button.set-customer,
.pos .control-button.set-partner {
    pointer-events: none !important;
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    background-color: #f8f9fa !important;
    color: #6c757d !important;
}

/* Security message styling */
.pos-security-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    text-align: center;
    font-weight: bold;
}

/* Make customer display read-only */
.pos .order-info .client-line .client-name {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    color: #6c757d !important;
}

/* Hide customer edit/change buttons */
.pos .client-line .edit-customer,
.pos .client-line .change-customer {
    display: none !important;
}

/* Block all customer selection screens and popups */
.pos .screen.clientlist-screen,
.pos .screen.partner-list-screen,
.pos .popup.customer-popup,
.pos .popup.partner-popup {
    display: none !important;
}

/* Disable customer list buttons completely */
.pos .button.set-customer,
.pos .control-button.set-customer,
.pos .actionpad-widget .set-customer {
    pointer-events: none !important;
    position: relative !important;
}

/* Add overlay to prevent clicks */
.pos .button.set-customer::after,
.pos .control-button.set-customer::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
}
