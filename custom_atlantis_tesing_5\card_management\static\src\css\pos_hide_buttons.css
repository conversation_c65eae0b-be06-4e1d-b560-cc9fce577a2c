/* Hide POS buttons that are not needed */

/* Hide Info, Refund, and Quotation/Order buttons in the top bar */
.pos .control-buttons .control-button[data-name="info"],
.pos .control-buttons .control-button[data-name="refund"],
.pos .control-buttons .control-button[data-name="quotation"],
.pos .control-buttons .control-button[data-name="order"],
.pos .control-buttons .control-button[data-name="quotation_order"] {
    display: none !important;
}

/* Alternative selectors for different POS versions */
.pos .control-buttons button[title*="Info"],
.pos .control-buttons button[title*="Refund"],
.pos .control-buttons button[title*="Quotation"],
.pos .control-buttons button[title*="Order"] {
    display: none !important;
}

/* Hide buttons by text content */
.pos .control-buttons button:contains("Info"),
.pos .control-buttons button:contains("Refund"),
.pos .control-buttons button:contains("Quotation"),
.pos .control-buttons button:contains("Order") {
    display: none !important;
}

/* Hide info button on products */
.pos .product .product-info-button,
.pos .product .info-button,
.pos .product-list .product .info-icon,
.pos .product-list .product .product-info,
.pos .product-list .product .fa-info,
.pos .product-list .product .fa-info-circle {
    display: none !important;
}

/* Hide product info overlay/popup trigger */
.pos .product .product-img .info-overlay,
.pos .product .product-img .info-icon {
    display: none !important;
}

/* Specific selectors for Odoo 15 POS */
.pos .control-button.o_info_button,
.pos .control-button.o_refund_button,
.pos .control-button.o_quotation_button,
.pos .control-button.o_order_button {
    display: none !important;
}

/* Hide by class names commonly used in POS */
.pos .btn-info-order,
.pos .btn-refund,
.pos .btn-quotation,
.pos .btn-order-info {
    display: none !important;
}

/* Hide specific POS control buttons by common patterns */
.pos-content .control-buttons .btn:nth-child(1), /* Usually Info */
.pos-content .control-buttons .btn:nth-child(2), /* Usually Refund */
.pos-content .control-buttons .btn:nth-child(3)  /* Usually Quotation/Order */ {
    display: none !important;
}

/* Alternative approach - hide by icon classes */
.pos .control-buttons .fa-info,
.pos .control-buttons .fa-info-circle,
.pos .control-buttons .fa-undo,
.pos .control-buttons .fa-file-text,
.pos .control-buttons .fa-file-o {
    display: none !important;
}

/* Hide parent buttons containing these icons */
.pos .control-buttons button .fa-info,
.pos .control-buttons button .fa-info-circle,
.pos .control-buttons button .fa-undo,
.pos .control-buttons button .fa-file-text,
.pos .control-buttons button .fa-file-o {
    display: none !important;
}

/* More specific targeting for product info buttons */
.pos .product-list .product .product-content .info-button,
.pos .product-grid .product .info-btn,
.pos .product-item .product-info-btn {
    display: none !important;
}

/* Hide any button with "info" in the class name within POS */
.pos [class*="info-btn"],
.pos [class*="info-button"],
.pos [class*="btn-info"] {
    display: none !important;
}

/* Hide Customer List Screen buttons */

/* Hide Create Customer button */
.pos .clientlist-screen .button.new-customer {
    display: none !important;
}

/* Hide settle due button (shopping cart icon) in customer list */
.pos .clientlist-screen .customer-due {
    display: none !important;
}

/* Hide edit/external link button in customer list */
.pos .clientlist-screen .client-line-open-link {
    display: none !important;
}

/* Hide the corresponding table headers */
.pos .clientlist-screen th.customer-due {
    display: none !important;
}

.pos .clientlist-screen th.client-line-open-link {
    display: none !important;
}
