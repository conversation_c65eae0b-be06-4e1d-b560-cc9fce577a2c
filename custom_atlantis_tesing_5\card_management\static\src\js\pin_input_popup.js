odoo.define('card_management.PinInputPopup', function (require) {
    'use strict';

    const AbstractAwaitablePopup = require('point_of_sale.AbstractAwaitablePopup');
    const Registries = require('point_of_sale.Registries');

    class PinInputPopup extends AbstractAwaitablePopup {
        constructor() {
            super(...arguments);
            this.state = {
                pin: '',
                attempts: 0,
                maxAttempts: 3,
                isBlocked: false,
                showPin: false
            };
        }

        setup() {
            super.setup();
        }

        mounted() {
            super.mounted();
            // Focus on the PIN input (with safety check)
            const pinInput = this.el.querySelector('.pin-input');
            if (pinInput) {
                pinInput.focus();
            }
        }

        get cardBarcode() {
            return this.props.cardBarcode || (this.props.card ? this.props.card.barcode : 'Unknown Card');
        }

        get maskedPin() {
            return this.state.showPin ? this.state.pin : '•'.repeat(this.state.pin.length);
        }

        get isValidPin() {
            return this.state.pin.length === 4 && /^\d{4}$/.test(this.state.pin);
        }

        get remainingAttempts() {
            return this.state.maxAttempts - this.state.attempts;
        }

        onPinInput(event) {
            let value = event.target.value.replace(/\D/g, ''); // Only digits
            if (value.length <= 4) {
                this.state.pin = value;
                this.trigger('update');
            }
        }

        onKeypadClick(digit) {
            if (this.state.pin.length < 4) {
                this.state.pin += digit;
                this.trigger('update');
            }
        }

        onClearPin() {
            this.state.pin = '';
            this.trigger('update');
        }

        onBackspace() {
            this.state.pin = this.state.pin.slice(0, -1);
            this.trigger('update');
        }

        togglePinVisibility() {
            this.state.showPin = !this.state.showPin;
            this.trigger('update');
        }

        async onConfirm() {
            if (!this.isValidPin) {
                this.showPopup('ErrorPopup', {
                    title: this.env._t('Invalid PIN'),
                    body: this.env._t('Please enter a 4-digit PIN.'),
                });
                return;
            }

            // Validate PIN with server
            try {
                const result = await this.env.services.rpc({
                    model: 'resort.card',
                    method: 'validate_card_pin',
                    args: [this.props.card.id, this.state.pin],
                });

                if (result.success) {
                    // PIN is correct
                    this.confirm({ pin: this.state.pin, validated: true });
                } else {
                    // PIN is incorrect
                    this.state.attempts++;
                    
                    if (this.state.attempts >= this.state.maxAttempts) {
                        // Too many failed attempts
                        this.state.isBlocked = true;
                        this.showPopup('ErrorPopup', {
                            title: this.env._t('Card Blocked'),
                            body: this.env._t('Too many failed PIN attempts. Please ask a manager for assistance.'),
                        });
                        this.cancel();
                    } else {
                        // Show error and allow retry
                        this.showPopup('ErrorPopup', {
                            title: this.env._t('Incorrect PIN'),
                            body: this.env._t(`Incorrect PIN. ${this.remainingAttempts} attempts remaining.`),
                        });
                        this.state.pin = '';
                        this.render();
                    }
                }
            } catch (error) {
                this.showPopup('ErrorPopup', {
                    title: this.env._t('Validation Error'),
                    body: this.env._t('Unable to validate PIN. Please try again.'),
                });
            }
        }

        async onManagerOverride() {
            // Show manager barcode input
            const { confirmed, payload: managerBarcode } = await this.showPopup('TextInputPopup', {
                title: this.env._t('Manager Override'),
                body: this.env._t('Scan or enter manager barcode to override PIN requirement'),
                placeholder: this.env._t('Manager barcode...'),
            });

            if (confirmed && managerBarcode) {
                // Validate manager barcode (reuse existing manager validation logic)
                try {
                    const managerData = await this._validateManagerBarcode(managerBarcode);
                    if (managerData) {
                        // Log manager override
                        await this.env.services.rpc({
                            model: 'card.pin.log',
                            method: 'log_manager_override',
                            args: [this.props.card.id, this.env.pos.user.id, `Manager override by ${managerData.name}`],
                        });

                        this.confirm({ 
                            pin: null, 
                            validated: true, 
                            managerOverride: true,
                            manager: managerData 
                        });
                    } else {
                        this.showPopup('ErrorPopup', {
                            title: this.env._t('Invalid Manager'),
                            body: this.env._t('Invalid manager barcode.'),
                        });
                    }
                } catch (error) {
                    this.showPopup('ErrorPopup', {
                        title: this.env._t('Validation Error'),
                        body: this.env._t('Unable to validate manager barcode.'),
                    });
                }
            }
        }

        async _validateManagerBarcode(barcode) {
            try {
                // Find employee with matching barcode
                const employees = this.env.pos.employees || [];
                const manager = employees.find(emp => emp.barcode === barcode);

                if (manager && manager.user_id) {
                    // For simplicity, accept any employee with barcode as manager
                    // In production, you might want to check specific groups
                    return {
                        id: manager.id,
                        name: manager.name,
                        user_id: manager.user_id[0]
                    };
                }

                // If no employee found, try to find user directly by barcode
                const users = await this.env.services.rpc({
                    model: 'res.users',
                    method: 'search_read',
                    args: [[['barcode', '=', barcode]]],
                    kwargs: { fields: ['id', 'name'], limit: 1 }
                });

                if (users && users.length > 0) {
                    return {
                        id: users[0].id,
                        name: users[0].name,
                        user_id: users[0].id
                    };
                }

                return null;
            } catch (error) {
                console.error('Manager validation error:', error);
                return null;
            }
        }
    }

    PinInputPopup.template = 'PinInputPopup';
    PinInputPopup.defaultProps = {
        confirmText: 'Confirm',
        cancelText: 'Cancel',
        title: 'Enter PIN',
        body: '',
    };

    Registries.Component.add(PinInputPopup);

    return PinInputPopup;
});
