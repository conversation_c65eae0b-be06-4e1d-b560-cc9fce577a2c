odoo.define('card_management.pos_block_customer_events', function (require) {
'use strict';

var core = require('web.core');
var Gui = require('point_of_sale.Gui');

// Block all customer-related events at the GUI level
var _super_gui = Gui.prototype;
Gui.include({
    
    // Block customer list screen
    show_screen: function(screen_name, params) {
        if (screen_name === 'clientlist' || screen_name === 'partnerlist') {
            this.show_popup('error', {
                title: 'Customer Selection Disabled',
                body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
            });
            return;
        }
        return _super_gui.show_screen.call(this, screen_name, params);
    },

    // Block customer popups
    show_popup: function(name, options) {
        if (name === 'customer' || name === 'partner' || name === 'clientlist') {
            return _super_gui.show_popup.call(this, 'error', {
                title: 'Customer Selection Disabled',
                body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
            });
        }
        return _super_gui.show_popup.call(this, name, options);
    }
});

// Block customer selection at the document level
document.addEventListener('DOMContentLoaded', function() {
    // Block clicks on customer-related elements
    document.addEventListener('click', function(event) {
        var target = event.target;
        
        // Check if click is on customer selection elements
        if (target.classList.contains('set-customer') ||
            target.classList.contains('client-name') ||
            target.classList.contains('customer-button') ||
            target.closest('.set-customer') ||
            target.closest('.client-line')) {
            
            // Check if this is a PIN-validated customer display
            var orderWidget = target.closest('.order-widget');
            if (orderWidget) {
                var currentOrder = window.posmodel && window.posmodel.get_order();
                var currentCustomer = currentOrder && currentOrder.get_client();
                
                if (currentCustomer && currentCustomer._pinValidated) {
                    // Allow display of validated customer info
                    return;
                }
            }
            
            // Block the click
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
            
            // Show security message
            if (window.posmodel && window.posmodel.gui) {
                window.posmodel.gui.show_popup('error', {
                    title: 'Customer Selection Disabled',
                    body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
                });
            }
            
            return false;
        }
    }, true); // Use capture phase to catch events early
});

});
