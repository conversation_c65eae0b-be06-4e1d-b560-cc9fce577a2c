odoo.define('card_management.pos_customer_balance', function (require) {
'use strict';

var PosComponent = require('point_of_sale.PosComponent');
var ProductScreen = require('point_of_sale.ProductScreen');
var ActionpadWidget = require('point_of_sale.ActionpadWidget');
var Registries = require('point_of_sale.Registries');
var { useListener } = require('web.custom_hooks');

// Customer Balance Widget
class CustomerBalanceWidget extends PosComponent {
    constructor() {
        super(...arguments);
        useListener('order-changed', this.render);
        useListener('client-changed', this.render);
    }
    
    get currentOrder() {
        return this.env.pos.get_order();
    }
    
    get customer() {
        return this.currentOrder ? this.currentOrder.get_client() : null;
    }
    
    get customerBalance() {
        if (this.customer && this.customer.credit !== undefined) {
            // Convert negative credit to positive balance for display
            return Math.abs(this.customer.credit || 0);
        }
        return 0;
    }
    
    get formattedBalance() {
        return this.env.pos.format_currency(this.customerBalance);
    }
    
    get hasCustomer() {
        return !!this.customer;
    }
    
    get customerName() {
        return this.customer ? this.customer.name : '';
    }
}

CustomerBalanceWidget.template = 'CustomerBalanceWidget';

// Extend ProductScreen to include customer balance
const PosResCardProductScreen = (ProductScreen) =>
    class extends ProductScreen {
        constructor() {
            super(...arguments);
        }
        
        // Override to trigger balance update when customer changes
        _onClickCustomer() {
            super._onClickCustomer();
            // The balance widget will automatically update via useListener
        }
    };

// Extend ActionpadWidget to show customer balance
const PosCardActionpadWidget = (ActionpadWidget) =>
    class extends ActionpadWidget {
        constructor() {
            super(...arguments);
            useListener('order-changed', this.render);
            useListener('client-changed', this.render);
        }

        get client() {
            const originalClient = super.client;
            if (originalClient && originalClient.credit !== undefined) {
                // Create a modified client object with balance in name
                const balance = Math.abs(originalClient.credit || 0);
                const balanceText = `الرصيد : ${Math.round(balance)}`;

                // Create a completely new object using JSON to avoid any reference issues
                try {
                    const newClient = JSON.parse(JSON.stringify(originalClient));
                    newClient.name = `${originalClient.name}\n${balanceText}`;
                    return newClient;
                } catch (error) {
                    // If JSON parsing fails, return original client
                    console.warn('Failed to create client copy:', error);
                    return originalClient;
                }
            }
            return originalClient;
        }
    };

Registries.Component.extend(ProductScreen, PosResCardProductScreen);
Registries.Component.extend(ActionpadWidget, PosCardActionpadWidget);
Registries.Component.add(CustomerBalanceWidget);

return {
    CustomerBalanceWidget,
    PosResCardProductScreen,
    PosCardActionpadWidget,
};

});
