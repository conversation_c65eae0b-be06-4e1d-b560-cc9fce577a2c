odoo.define('card_management.pos_disable_customer_button', function (require) {
'use strict';

var ActionpadWidget = require('point_of_sale.ActionpadWidget');
var ProductScreen = require('point_of_sale.ProductScreen');
var models = require('point_of_sale.models');
var Registries = require('point_of_sale.Registries');

// Extend ActionpadWidget to disable customer selection
const PosDisableCustomerActionpad = (ActionpadWidget) =>
    class extends ActionpadWidget {
        constructor() {
            super(...arguments);
        }

        // Override customer button click to prevent bypass
        async _onClickCustomer() {
            // Check if customer is already selected
            const currentCustomer = this.currentOrder.get_client();

            if (currentCustomer && currentCustomer._pinValidated) {
                // Customer already selected with PIN - show info popup
                const balance = Math.abs(currentCustomer.credit || 0);
                const formattedBalance = this.env.pos.format_currency(balance);

                this.showPopup('ConfirmPopup', {
                    title: 'Current Customer Information',
                    body: `Customer: ${currentCustomer.name}\n` +
                          `Available Balance: ${formattedBalance}\n\n` +
                          `To change customer, scan their card and enter PIN.`,
                });
            } else {
                // No customer or not PIN validated - show security message
                this.showPopup('ErrorPopup', {
                    title: 'Customer Selection Required',
                    body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
                });
            }

            // Do not call super._onClickCustomer() to prevent customer list opening
            return;
        }

        // Override partner selection to prevent manual customer setting
        async _onClickPartner() {
            // Same security message
            this.showPopup('ErrorPopup', {
                title: 'Customer Selection Disabled',
                body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
            });

            // Do not call super._onClickPartner() to prevent partner list opening
            return;
        }

        // Override any customer search functionality
        _searchCustomer() {
            this.showPopup('ErrorPopup', {
                title: 'Customer Search Disabled',
                body: 'For security reasons, customer search is disabled.\n\nPlease scan the customer\'s card to proceed.',
            });
            return;
        }

        // Disable customer name click
        _onCustomerNameClick() {
            this.showPopup('ErrorPopup', {
                title: 'Customer Selection Disabled',
                body: 'Customer information is read-only for security.\n\nTo change customer, scan their card and enter PIN.',
            });
            return;
        }
    };

// Extend ProductScreen to disable customer selection features
const PosDisableCustomerProductScreen = (ProductScreen) =>
    class extends ProductScreen {
        constructor() {
            super(...arguments);
        }

        // Override ALL customer selection methods
        async _setCustomer(customer) {
            // Only allow customer setting through barcode scan with PIN
            if (customer && !customer._pinValidated) {
                this.showPopup('ErrorPopup', {
                    title: 'Customer Selection Disabled',
                    body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
                });
                return false;
            }

            // If PIN was validated, allow setting customer
            return super._setCustomer(customer);
        }

        // Block customer list opening
        async _onClickCustomer() {
            const currentCustomer = this.currentOrder.get_client();

            if (currentCustomer && currentCustomer._pinValidated) {
                // Customer already selected with PIN - show info popup
                const balance = Math.abs(currentCustomer.credit || 0);
                const formattedBalance = this.env.pos.format_currency(balance);

                this.showPopup('ConfirmPopup', {
                    title: 'Current Customer Information',
                    body: `Customer: ${currentCustomer.name}\n` +
                          `Available Balance: ${formattedBalance}\n\n` +
                          `To change customer, scan their card and enter PIN.`,
                });
            } else {
                // No customer or not PIN validated - show security message
                this.showPopup('ErrorPopup', {
                    title: 'Customer Selection Required',
                    body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
                });
            }
            return false;
        }

        // Block partner list opening
        async _onClickPartner() {
            this.showPopup('ErrorPopup', {
                title: 'Customer Selection Disabled',
                body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
            });
            return false;
        }

        // Disable customer search
        _onSearchCustomer() {
            this.showPopup('ErrorPopup', {
                title: 'Customer Search Disabled',
                body: 'For security reasons, customer search is disabled.\n\nPlease scan the customer\'s card to proceed.',
            });
            return false;
        }

        // Override any customer selection from partner list
        selectCustomer(customer) {
            if (customer && !customer._pinValidated) {
                this.showPopup('ErrorPopup', {
                    title: 'Customer Selection Disabled',
                    body: 'For security reasons, customers can only be selected by scanning their card and entering PIN.\n\nPlease scan the customer\'s card to proceed.',
                });
                return false;
            }
            return super.selectCustomer(customer);
        }
    };

// Override Order model to prevent unauthorized customer setting
var _super_order = models.Order.prototype;
models.Order = models.Order.extend({
    set_client: function(client) {
        // Only allow setting customer if PIN was validated or client is null (clearing)
        if (client && !client._pinValidated) {
            console.warn('Attempted to set customer without PIN validation - blocked');
            return false;
        }
        return _super_order.set_client.call(this, client);
    },
});

Registries.Component.extend(ActionpadWidget, PosDisableCustomerActionpad);
Registries.Component.extend(ProductScreen, PosDisableCustomerProductScreen);

return {
    PosDisableCustomerActionpad,
    PosDisableCustomerProductScreen,
};

});
