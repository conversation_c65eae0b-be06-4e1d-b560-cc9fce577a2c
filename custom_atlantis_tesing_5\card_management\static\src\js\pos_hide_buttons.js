odoo.define('card_management.pos_hide_buttons', function (require) {
'use strict';

var ProductScreen = require('point_of_sale.ProductScreen');
var ProductItem = require('point_of_sale.ProductItem');
var Registries = require('point_of_sale.Registries');

// Remove specific control buttons by their exact component names
const PosHideButtonsProductScreen = (ProductScreen) =>
    class extends ProductScreen {

        // Override the control buttons to exclude unwanted ones
        get controlButtons() {
            const buttons = super.controlButtons || [];

            // Filter out the buttons we want to hide by their component names
            return buttons.filter(button => {
                const componentName = button.component.name;

                // Hide these specific buttons:
                // - ProductInfoButton (Info button)
                // - RefundButton (Refund button)
                // - SetSaleOrderButton (Quotation/Order button)
                // - SubmitOrderButton (Order button from restaurant)
                // - RewardButton (Reward button from loyalty)
                const buttonsToHide = [
                    'ProductInfoButton',
                    'RefundButton',
                    'SetSaleOrderButton',
                    'SubmitOrderButton',
                    'RewardButton'
                ];

                return !buttonsToHide.includes(componentName);
            });
        }
    };

// Hide the info button on individual products
const PosHideButtonsProductItem = (ProductItem) =>
    class extends ProductItem {

        // Override the onProductInfoClick to do nothing
        async onProductInfoClick() {
            // Do nothing - effectively disables the info button
            return;
        }
    };

Registries.Component.extend(ProductScreen, PosHideButtonsProductScreen);
Registries.Component.extend(ProductItem, PosHideButtonsProductItem);

return {
    PosHideButtonsProductScreen,
    PosHideButtonsProductItem,
};

});
