odoo.define('card_management.pos_order_refund_tracking', function (require) {
'use strict';

const models = require('point_of_sale.models');

// Extend Order model to include refund approval tracking
const _super_order = models.Order.prototype;
models.Order = models.Order.extend({

    export_as_JSON: function() {
        const json = _super_order.export_as_JSON.call(this);

        // Add refund approval data if this is a refund order
        if (this.is_refund_order()) {
            // First try order-level data
            if (this.refund_approved_by) {
                json.refund_approved_by = this.refund_approved_by;
            }
            // Fallback to POS-level data
            else if (this.pos.refund_approved_by) {
                json.refund_approved_by = this.pos.refund_approved_by;
            }
        }

        return json;
    },

    is_refund_order: function() {
        // Check if this order contains refund items (negative quantities)
        return this.orderlines.some(line => line.quantity < 0);
    }
});

return models.Order;

});
