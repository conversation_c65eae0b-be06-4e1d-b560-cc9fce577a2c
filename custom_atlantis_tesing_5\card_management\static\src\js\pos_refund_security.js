odoo.define('card_management.pos_refund_security', function (require) {
'use strict';

const TicketScreen = require('point_of_sale.TicketScreen');
const Registries = require('point_of_sale.Registries');
const { Gui } = require('point_of_sale.Gui');

// Extend TicketScreen to add manager barcode approval for refunds
const PosRefundSecurityTicketScreen = (TicketScreen) =>
    class extends TicketScreen {

        async _onDoRefund() {
            console.log('Refund security check - Config:', this.env.pos.config.require_manager_approval_for_refunds);

            // Check if manager approval is required
            if (this.env.pos.config.require_manager_approval_for_refunds) {
                const managerData = await this._requestManagerApproval();
                if (!managerData) {
                    return; // Block refund if manager approval failed
                }

                // Store manager data for the refund
                this._storeManagerApproval(managerData);
            }

            // Proceed with original refund logic
            const result = await super._onDoRefund(...arguments);

            // After successful refund, navigate back to order screen
            if (result !== false) {
                // Navigate to ProductScreen (main order screen)
                this.showScreen('ProductScreen');
            }

            return result;
        }

        async _requestManagerApproval() {
            try {
                // Show barcode input popup
                const popupPromise = this.showPopup('TextInputPopup', {
                    title: this.env._t('مطلوب موافقة المدير'),
                    body: this.env._t('يرجى مسح باركود المدير لاعتماد هذا الاسترداد'),
                    placeholder: this.env._t('امسح أو أدخل باركود المدير...'),
                    startingValue: '',
                });

                // Make input password-style after popup is shown
                setTimeout(() => {
                    const inputField = document.querySelector('.popup input[type="text"]');
                    if (inputField) {
                        inputField.type = 'password';
                        inputField.style.letterSpacing = '2px';
                        inputField.style.fontFamily = 'monospace';
                        inputField.style.textAlign = 'center';
                        inputField.style.fontSize = '16px';
                        inputField.style.background = '#f8f9fa';
                        inputField.style.border = '2px solid #FF9800';
                        inputField.focus(); // Refocus after styling
                    }
                }, 100);

                const { confirmed, payload: scannedBarcode } = await popupPromise;

                if (!confirmed || !scannedBarcode) {
                    this.showPopup('ErrorPopup', {
                        title: this.env._t('تم إلغاء الاسترداد'),
                        body: this.env._t('موافقة المدير مطلوبة للاستردادات'),
                    });
                    return false;
                }

                // Validate manager barcode
                const managerData = await this._validateManagerBarcode(scannedBarcode);

                if (managerData) {
                    console.log(`Refund approved by manager: ${managerData.name}`);
                    return managerData; // Return the manager data instead of just true
                } else {
                    // Show error message
                    this.showPopup('ErrorPopup', {
                        title: this.env._t('باركود مدير غير صحيح'),
                        body: this.env._t('الباركود الممسوح غير صحيح أو لا ينتمي إلى مدير'),
                    });
                    return false;
                }

            } catch (error) {
                console.error('Error in manager approval:', error);
                this.showPopup('ErrorPopup', {
                    title: this.env._t('خطأ في الموافقة'),
                    body: this.env._t('حدث خطأ أثناء موافقة المدير. يرجى المحاولة مرة أخرى.'),
                });
                return false;
            }
        }

        async _validateManagerBarcode(barcode) {
            try {
                // Call server to validate manager barcode using employee model
                const result = await this.rpc({
                    model: 'hr.employee',
                    method: 'get_manager_by_barcode',
                    args: [barcode],
                });

                return result;
            } catch (error) {
                console.error('Error validating manager barcode:', error);
                return false;
            }
        }

        _storeManagerApproval(managerData) {
            // Store manager approval data in POS for future refund orders
            this.env.pos.refund_approved_by = managerData.id;
            this.env.pos.refund_approved_by_name = managerData.name;

            // Also store in the current order if it exists
            const currentOrder = this.env.pos.get_order();
            if (currentOrder) {
                currentOrder.refund_approved_by = managerData.id;
                currentOrder.refund_approved_by_name = managerData.name;
            }
        }
    };

Registries.Component.extend(TicketScreen, PosRefundSecurityTicketScreen);

return TicketScreen;

});
