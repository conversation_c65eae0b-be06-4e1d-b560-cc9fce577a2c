odoo.define('card_management.pos_ticket_screen', function (require) {
'use strict';

var TicketScreen = require('point_of_sale.TicketScreen');
var Registries = require('point_of_sale.Registries');

// Hide delete button from orders list
const CardManagementTicketScreen = (TicketScreen) =>
    class extends TicketScreen {
        
        // Override shouldHideDeleteButton to always hide the delete button
        shouldHideDeleteButton(order) {
            // Always hide the delete button for all orders
            return true;
        }
    };

Registries.Component.extend(TicketScreen, CardManagementTicketScreen);

return CardManagementTicketScreen;

});
