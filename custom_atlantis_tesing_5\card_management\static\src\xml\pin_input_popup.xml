<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="PinInputPopup">
        <div class="popup pin-input-popup">
            <div class="popup-content">
                <!-- Header -->
                <div class="popup-header">
                    <h3 class="popup-title">
                        <i class="fa fa-lock" style="color: #FF9800; margin-right: 8px;"></i>
                        Card Security Verification
                    </h3>
                </div>

                <!-- Card Info (No customer name for security) -->
                <div class="card-info" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                    <div>
                        <strong>Card Number:</strong><br/>
                        <span t-esc="cardBarcode" style="font-family: monospace; font-size: 18px; color: #666;"/>
                    </div>
                </div>

                <!-- PIN Input Section -->
                <div class="pin-section" style="text-align: center; margin-bottom: 20px;">
                    <p style="margin-bottom: 15px; color: #666;">
                        Enter the 4-digit PIN (last 4 digits of phone number)
                    </p>
                    
                    <!-- PIN Display -->
                    <div class="pin-display" style="margin-bottom: 20px;">
                        <input type="text"
                               class="pin-input"
                               t-att-value="state.showPin ? state.pin : maskedPin"
                               t-on-input="onPinInput"
                               maxlength="4"
                               style="font-size: 24px; text-align: center; letter-spacing: 8px; width: 200px; padding: 10px; border: 2px solid #FF9800; border-radius: 8px; font-family: monospace;"
                               placeholder="••••"/>
                        
                        <button class="btn btn-sm btn-outline-secondary" 
                                t-on-click="togglePinVisibility"
                                style="margin-left: 10px;">
                            <i t-att-class="state.showPin ? 'fa fa-eye-slash' : 'fa fa-eye'"></i>
                        </button>
                    </div>

                    <!-- Virtual Keypad -->
                    <div class="virtual-keypad" style="display: inline-block;">
                        <div class="keypad-row" style="margin-bottom: 10px;">
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('1')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">1</button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('2')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">2</button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('3')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">3</button>
                        </div>
                        <div class="keypad-row" style="margin-bottom: 10px;">
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('4')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">4</button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('5')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">5</button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('6')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">6</button>
                        </div>
                        <div class="keypad-row" style="margin-bottom: 10px;">
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('7')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">7</button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('8')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">8</button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('9')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">9</button>
                        </div>
                        <div class="keypad-row">
                            <button class="keypad-btn" t-on-click="onClearPin" style="width: 60px; height: 60px; margin: 5px; font-size: 14px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                                <i class="fa fa-times"></i>
                            </button>
                            <button class="keypad-btn" t-on-click="() => this.onKeypadClick('0')" style="width: 60px; height: 60px; margin: 5px; font-size: 18px; border: 1px solid #ddd; border-radius: 8px; background: white;">0</button>
                            <button class="keypad-btn" t-on-click="onBackspace" style="width: 60px; height: 60px; margin: 5px; font-size: 14px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                                <i class="fa fa-backspace"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Attempts Warning -->
                <div t-if="state.attempts > 0" class="attempts-warning" style="text-align: center; margin-bottom: 15px;">
                    <div class="alert alert-warning" style="display: inline-block; padding: 8px 15px;">
                        <i class="fa fa-exclamation-triangle"></i>
                        <span t-esc="remainingAttempts"/> attempts remaining
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="popup-footer" style="text-align: center; border-top: 1px solid #ddd; padding-top: 15px;">
                    <button class="btn btn-primary" 
                            t-on-click="onConfirm"
                            t-att-disabled="!isValidPin || state.isBlocked"
                            style="margin-right: 10px; min-width: 100px;">
                        <i class="fa fa-check"></i> Confirm
                    </button>
                    
                    <button class="btn btn-secondary" 
                            t-on-click="cancel"
                            style="margin-right: 10px; min-width: 100px;">
                        <i class="fa fa-times"></i> Cancel
                    </button>
                    
                    <button class="btn btn-warning" 
                            t-on-click="onManagerOverride"
                            style="min-width: 120px;">
                        <i class="fa fa-user-shield"></i> Manager Override
                    </button>
                </div>

                <!-- Help Text -->
                <div class="help-text" style="text-align: center; margin-top: 15px; font-size: 12px; color: #666;">
                    <p>
                        <i class="fa fa-info-circle"></i>
                        PIN is the last 4 digits of the customer's phone number.<br/>
                        If you don't know the PIN, ask a manager for assistance.
                    </p>
                </div>
            </div>
        </div>
    </t>

</templates>
