<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!-- Customer Balance Widget Template -->
    <t t-name="CustomerBalanceWidget" owl="1">
        <div class="customer-balance-widget" t-if="hasCustomer">
            <div class="customer-balance-display">
                <div class="balance-card">
                    <i class="fa fa-credit-card"/>
                    <span class="customer-name" t-esc="customerName"/>
                    <span class="balance-amount" t-esc="formattedBalance"/>
                </div>
            </div>
        </div>
    </t>

    <!-- Extend ProductScreen to include balance widget -->
    <t t-name="ProductScreen" t-inherit="point_of_sale.ProductScreen" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('pos-topheader')]" position="after">
            <CustomerBalanceWidget />
        </xpath>
    </t>

    <!-- Customer Card Scan Success Popup -->
    <t t-name="CardScanSuccessPopup" owl="1">
        <div class="popup card-scan-popup">
            <div class="popup-header">
                <h3><i class="fa fa-check-circle text-success"/> تم مسح البطاقة بنجاح</h3>
            </div>
            <div class="popup-body">
                <div class="customer-info">
                    <h4 t-esc="props.customer.name"/>
                    <div class="balance-info">
                        <span class="label">الرصيد المتاح:</span>
                        <span class="balance" t-esc="props.formattedBalance"/>
                    </div>
                </div>
            </div>
            <div class="popup-footer">
                <button class="button confirm" t-on-click="confirm">
                    متابعة
                </button>
            </div>
        </div>
    </t>

</templates>
