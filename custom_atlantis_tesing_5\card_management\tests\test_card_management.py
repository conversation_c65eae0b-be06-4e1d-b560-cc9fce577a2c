# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError, UserError


class TestCardManagement(TransactionCase):

    def setUp(self):
        super(TestCardManagement, self).setUp()
        self.card_model = self.env['resort.card']
        self.partner_model = self.env['res.partner']
        self.card_status_model = self.env['card.status']

    def test_card_creation(self):
        """Test card creation and customer auto-creation"""
        # Create a new card
        card = self.card_model.create({
            'name': 'Test Customer',
            'phone': '******-0001',
            'barcode': '12345',
        })

        # Card should be created successfully
        self.assertEqual(card.name, 'Test Customer')
        self.assertEqual(card.phone, '******-0001')
        self.assertEqual(card.barcode, '12345')

        # Customer should be automatically created
        self.assertTrue(card.partner_id)
        self.assertEqual(card.partner_id.name, 'Test Customer')
        self.assertEqual(card.partner_id.phone, '******-0001')
        self.assertEqual(card.partner_id.barcode, '12345')

        # Card status should be active by default
        active_status = self.card_status_model.get_default_active_status()
        self.assertEqual(card.card_status_id, active_status)

    def test_barcode_validation(self):
        """Test barcode format validation"""
        # Numeric barcode should work
        card = self.card_model.create({
            'name': 'Test Customer',
            'barcode': '12345',
        })
        self.assertEqual(card.barcode, '12345')

        # Non-numeric barcode should raise validation error
        with self.assertRaises(ValidationError):
            self.card_model.create({
                'name': 'Test Customer 2',
                'barcode': 'ABC123',
            })

    def test_card_status_changes(self):
        """Test card status management"""
        # Create card
        card = self.card_model.create({
            'name': 'Test Customer',
            'barcode': '12345',
        })
        active_status = self.card_status_model.get_default_active_status()
        self.assertEqual(card.card_status_id, active_status)

        # Test deactivate card
        card.action_deactivate()
        inactive_status = self.card_status_model.get_inactive_status()
        self.assertEqual(card.card_status_id, inactive_status)

        # Test reactivate
        card.action_reactivate()
        self.assertEqual(card.card_status_id, active_status)

        # Test report lost (should archive card)
        card.action_report_lost()
        lost_status = self.card_status_model.get_lost_status()
        self.assertEqual(card.card_status_id, lost_status)
        self.assertFalse(card.active)  # Card should be archived
        self.assertFalse(card.partner_id.active)  # Customer should be archived

    def test_card_balance_computation(self):
        """Test card balance computation"""
        # Create card
        card = self.card_model.create({
            'name': 'Test Customer',
            'barcode': '12345',
        })

        # Card balance should equal customer credit
        self.assertEqual(card.card_balance, card.partner_id.credit)

    def test_barcode_uniqueness(self):
        """Test that barcode must be unique"""
        # Create first card
        self.card_model.create({
            'name': 'Test Customer 1',
            'barcode': '12345',
        })

        # Creating second card with same barcode should fail
        with self.assertRaises(Exception):  # Should raise integrity error
            self.card_model.create({
                'name': 'Test Customer 2',
                'barcode': '12345',
            })

    def test_card_update_customer(self):
        """Test that updating card updates customer"""
        # Create card
        card = self.card_model.create({
            'name': 'Test Customer',
            'phone': '******-0001',
            'barcode': '12345',
        })

        # Update card information
        card.write({
            'name': 'Updated Customer',
            'phone': '******-9999',
        })

        # Customer should be updated too
        self.assertEqual(card.partner_id.name, 'Updated Customer')
        self.assertEqual(card.partner_id.phone, '******-9999')
