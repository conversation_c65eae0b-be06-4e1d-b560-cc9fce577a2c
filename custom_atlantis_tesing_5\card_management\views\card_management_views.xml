<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Card Status Views -->
    <record id="view_card_status_tree" model="ir.ui.view">
        <field name="name">card.status.tree</field>
        <field name="model">card.status</field>
        <field name="arch" type="xml">
            <tree string="حالة البطاقة">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="description"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_card_status_form" model="ir.ui.view">
        <field name="name">card.status.form</field>
        <field name="model">card.status</field>
        <field name="arch" type="xml">
            <form string="حالة البطاقة">
                <sheet>
                    <widget name="web_ribbon" title="مؤرشف" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <group>
                        <group>
                            <field name="name" string="الاسم"/>
                            <field name="code" string="الكود"/>
                            <field name="sequence" string="التسلسل"/>
                        </group>
                        <group>
                            <field name="active" string="نشط"/>
                            <field name="color" widget="color" string="اللون"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" string="الوصف"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Card Status Actions -->
    <record id="action_card_status" model="ir.actions.act_window">
        <field name="name">حالة البطاقة</field>
        <field name="res_model">card.status</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء حالة بطاقة جديدة
            </p>
            <p>
                تحديد حالات مختلفة لبطاقات العملاء مثل نشط، غير نشط، مفقود، مسروق، إلخ.
            </p>
        </field>
    </record>

    <!-- Resort Card Views -->
    <record id="view_resort_card_tree" model="ir.ui.view">
        <field name="name">resort.card.tree</field>
        <field name="model">resort.card</field>
        <field name="arch" type="xml">
            <tree string="بطاقات المنتجع">
                <field name="name"/>
                <field name="phone"/>
                <field name="barcode"/>
                <field name="card_status_id"/>
                <field name="card_balance"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <record id="view_resort_card_form" model="ir.ui.view">
        <field name="name">resort.card.form</field>
        <field name="model">resort.card</field>
        <field name="arch" type="xml">
            <form string="بطاقة المنتجع">
                <header>
                    <button name="action_topup_card"
                            string="شحن البطاقة"
                            type="object"
                            class="btn-primary"/>
                    <button name="action_print_balance_report"
                            string="تقرير الرصيد"
                            type="object"
                            class="btn-info"/>
                    <button name="action_print_qr_code"
                            string="طباعة QR"
                            type="object"
                            class="btn-secondary"
                            help="Print QR code for preprinted cards"/>
                    <button name="action_print_qr_code_ps80_79"
                            string="طباعة QR (PS80_79)"
                            type="object"
                            class="btn-secondary"
                            help="Print QR code for PS80_79 preprinted cards"/>
                    <button name="action_deactivate"
                            string="إلغاء التفعيل"
                            type="object"
                            class="btn-secondary"
                            attrs="{'invisible': [('card_status_code', '!=', 'active')]}"/>
                    <button name="action_reactivate"
                            string="إعادة التفعيل"
                            type="object"
                            class="btn-success"
                            attrs="{'invisible': [('card_status_code', '=', 'active')]}"/>
                    <button name="action_report_lost"
                            string="الإبلاغ عن فقدان"
                            type="object"
                            class="btn-danger"
                            attrs="{'invisible': [('card_status_code', '=', 'lost')]}"
                            confirm="هل أنت متأكد من الإبلاغ عن فقدان هذه البطاقة؟"/>
                    <button name="action_reissue_card"
                            string="إعادة إصدار البطاقة"
                            type="object"
                            class="btn-warning"
                            attrs="{'invisible': [('card_status_code', '!=', 'lost')]}"/>
                    <button name="action_show_pin"
                            string="عرض رقم PIN"
                            type="object"
                            class="btn-info"
                            help="عرض رقم PIN المتوقع للاختبار"/>
                    <field name="card_status_id" widget="statusbar" statusbar_visible="active,inactive,lost"/>
                </header>
                <sheet>
                    <!-- Invisible fields for attrs conditions -->
                    <field name="card_status_code" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_customer"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-user">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">عرض</span>
                                <span class="o_stat_text">العميل</span>
                            </div>
                        </button>
                        <button name="action_topup_card"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-credit-card">
                            <field name="card_balance" widget="statinfo" string="عرض المدفوعات"/>
                        </button>
                    </div>

                    <group>
                        <group>
                            <field name="name" string="اسم العميل" placeholder="اسم العميل"/>
                            <field name="phone" string="رقم الهاتف" placeholder="رقم الهاتف"/>
                            <field name="initial_amount" string="مبلغ الشحن الأولي" placeholder="مبلغ الشحن الأولي (اختياري)"/>
                            <field name="payment_journal_id"
                                   string="طريقة الدفع"
                                   attrs="{'invisible': [('initial_amount', '=', 0)], 'required': [('initial_amount', '>', 0)]}"/>
                        </group>
                        <group>
                            <field name="barcode" string="رقم البطاقة" readonly="1"/>
                            <field name="create_date" string="تاريخ الإنشاء" readonly="1"/>
                        </group>
                    </group>

                    <group string="المعلومات التقنية" groups="base.group_no_one">
                        <field name="partner_id" readonly="1" string="الشريك"/>
                        <field name="active" string="نشط"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_resort_card_search" model="ir.ui.view">
        <field name="name">resort.card.search</field>
        <field name="model">resort.card</field>
        <field name="arch" type="xml">
            <search string="بطاقات المنتجع">
                <field name="name" string="اسم العميل"/>
                <field name="phone" string="الهاتف"/>
                <field name="barcode" string="رقم البطاقة"/>
                <separator/>
                <filter string="البطاقات النشطة" name="active_cards" domain="[('card_status_id.code', '=', 'active')]"/>
                <filter string="البطاقات غير النشطة" name="inactive_cards" domain="[('card_status_id.code', '=', 'inactive')]"/>
                <filter string="البطاقات المفقودة" name="lost_cards" domain="[('card_status_id.code', '=', 'lost')]"/>
                <separator/>
                <filter string="المؤرشف" name="archived" domain="[('active', '=', False)]"/>
                <group expand="0" string="تجميع حسب">
                    <filter string="حالة البطاقة" name="group_status" context="{'group_by': 'card_status_id'}"/>
                    <filter string="تاريخ الإنشاء" name="group_create_date" context="{'group_by': 'create_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Card Top-up Views -->
    <record id="view_card_topup_tree" model="ir.ui.view">
        <field name="name">card.topup.tree</field>
        <field name="model">card.topup</field>
        <field name="arch" type="xml">
            <tree string="شحن البطاقات" decoration-success="state=='done'" decoration-muted="state=='cancelled'">
                <field name="create_date"/>
                <field name="card_barcode"/>
                <field name="customer_name"/>
                <field name="topup_amount"/>
                <field name="payment_journal_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_card_topup_form" model="ir.ui.view">
        <field name="name">card.topup.form</field>
        <field name="model">card.topup</field>
        <field name="arch" type="xml">
            <form string="شحن البطاقة">
                <header>
                    <button name="action_process_topup"
                            string="تأكيد"
                            type="object"
                            class="btn-primary"
                            attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_print_receipt"
                            string="طباعة الإيصال"
                            type="object"
                            class="btn-success"
                            attrs="{'invisible': [('state', '!=', 'done')]}"/>
                    <button name="action_cancel"
                            string="إلغاء"
                            type="object"
                            class="btn-secondary"
                            attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_reset_to_draft"
                            string="إعادة تعيين إلى مسودة"
                            type="object"
                            class="btn-secondary"
                            attrs="{'invisible': [('state', '!=', 'cancelled')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,done"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>شحن البطاقة</h1>
                    </div>

                    <group>
                        <group>
                            <field name="card_barcode" string="رقم البطاقة" placeholder="امسح أو أدخل رقم البطاقة" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="customer_name" string="اسم العميل" readonly="1"/>
                            <field name="topup_amount" string="مبلغ الشحن" placeholder="أدخل المبلغ" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                        <group>
                            <field name="payment_journal_id" string="طريقة الدفع" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="cashier_id" string="الكاشير" readonly="1"/>
                            <field name="receipt_printed" string="طباعة الإيصال" readonly="1"/>
                            <field name="payment_id" string="سجل الدفع" readonly="1" attrs="{'invisible': [('payment_id', '=', False)]}"/>
                            <field name="create_date" string="تاريخ الإنشاء" readonly="1"/>
                        </group>
                    </group>

                    <group string="ملاحظات">
                        <field name="notes" string="ملاحظات" nolabel="1" placeholder="ملاحظات إضافية..."/>
                    </group>

                    <field name="card_id" invisible="1"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_card_topup_search" model="ir.ui.view">
        <field name="name">card.topup.search</field>
        <field name="model">card.topup</field>
        <field name="arch" type="xml">
            <search string="شحن البطاقات">
                <field name="card_barcode" string="رقم البطاقة"/>
                <field name="customer_name" string="العميل"/>
                <separator/>
                <filter string="مسودة" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="مكتمل" name="done" domain="[('state', '=', 'done')]"/>
                <filter string="ملغي" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <separator/>
                <filter string="اليوم" name="today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="هذا الأسبوع" name="week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="تجميع حسب">
                    <filter string="طريقة الدفع" name="group_payment_method" context="{'group_by': 'payment_journal_id'}"/>
                    <filter string="الحالة" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="التاريخ" name="group_date" context="{'group_by': 'create_date'}"/>
                </group>
            </search>
        </field>
    </record>



    <!-- Card Kiosk Views -->
    <record id="view_card_kiosk_form" model="ir.ui.view">
        <field name="name">card.kiosk.form</field>
        <field name="model">card.kiosk</field>
        <field name="arch" type="xml">
            <form string="كشك رصيد البطاقة" class="o_kiosk_form">
                <sheet>
                    <!-- Scan State -->
                    <div attrs="{'invisible': [('display_state', '!=', 'scan')]}" class="text-center">
                        <div class="o_kiosk_header">
                            <h1 style="color: #2E7D32; font-size: 48px; margin-bottom: 30px;">
                                <i class="fa fa-credit-card"/> فحص رصيد البطاقة
                            </h1>
                        </div>
                        <div class="o_kiosk_content">
                            <h2 style="color: #666; margin-bottom: 40px;">امسح بطاقتك للتحقق من الرصيد</h2>
                            <div style="text-align: center;">
                                <field name="card_barcode"
                                       placeholder="امسح بطاقتك هنا..."
                                       style="font-size: 28px; text-align: center; padding: 20px; width: 450px; border: 3px solid #2E7D32; border-radius: 10px;"
                                       autofocus="1"
                                       options="{'no_open': True}"
                                       attrs="{'onkeypress': 'if(event.keyCode==13){this.onchange();}'}"/>
                            </div>
                            <div style="text-align: center; margin-top: 20px;">
                                <button name="action_check_balance"
                                        string="فحص الرصيد"
                                        type="object"
                                        class="btn-primary"
                                        style="font-size: 20px; padding: 15px 40px; background: #2E7D32; border: none; border-radius: 8px; color: white;"/>
                            </div>
                            <p style="color: #999; margin-top: 30px; font-size: 18px;">
                                <i class="fa fa-barcode"/> ببساطة امسح بطاقتك - سيظهر الرصيد تلقائياً
                            </p>
                        </div>
                    </div>

                    <!-- Balance Display State -->
                    <div attrs="{'invisible': [('display_state', '!=', 'show_balance')]}" class="text-center">
                        <div class="o_kiosk_header">
                            <h1 style="color: #2E7D32; font-size: 36px; margin-bottom: 20px;">
                                <i class="fa fa-check-circle"/> رصيد البطاقة
                            </h1>
                        </div>
                        <div class="o_kiosk_balance" style="background: #E8F5E8; padding: 40px; border-radius: 15px; margin: 20px; text-align: center;">
                            <div style="text-align: center;">
                                <field name="card_balance"
                                       widget="monetary"
                                       options="{'currency_field': 'currency_id', 'precision': 0}"
                                       readonly="1"
                                       style="display: inline-block; text-align: center; font-size: 96px; font-weight: bold; border: none; background: transparent; color: #1B5E20; min-width: 400px;"/>
                            </div>
                            <h3 style="color: #2E7D32; margin-top: 15px; font-size: 24px;">الرصيد المتاح</h3>
                        </div>
                        <div style="margin-top: 30px;">
                            <p style="color: #666; font-size: 18px;">
                                الحالة: <field name="card_status" readonly="1" style="font-weight: bold;"/>
                            </p>
                            <p style="color: #666; font-size: 16px;" attrs="{'invisible': [('last_transaction_date', '=', False)]}">
                                آخر معاملة: <field name="last_transaction_date" readonly="1"/>
                            </p>
                        </div>
                    </div>

                    <!-- Card Not Found State -->
                    <div attrs="{'invisible': [('display_state', '!=', 'card_not_found')]}" class="text-center">
                        <div class="o_kiosk_header">
                            <h1 style="color: #D32F2F; font-size: 36px; margin-bottom: 20px;">
                                <i class="fa fa-exclamation-triangle"/> البطاقة غير موجودة
                            </h1>
                        </div>
                        <div style="background: #FFEBEE; padding: 30px; border-radius: 15px; margin: 20px;">
                            <h2 style="color: #C62828;">عذراً، لم نتمكن من العثور على بطاقتك</h2>
                            <p style="color: #666; font-size: 18px; margin-top: 20px;">
                                يرجى التحقق من رقم بطاقتك أو الاتصال بخدمة العملاء
                            </p>
                        </div>
                    </div>

                    <!-- Card Inactive State -->
                    <div attrs="{'invisible': [('display_state', '!=', 'card_inactive')]}" class="text-center">
                        <div class="o_kiosk_header">
                            <h1 style="color: #FF9800; font-size: 36px; margin-bottom: 20px;">
                                <i class="fa fa-pause-circle"/> البطاقة غير نشطة
                            </h1>
                        </div>
                        <div style="background: #FFF3E0; padding: 30px; border-radius: 15px; margin: 20px;">
                            <h2 style="color: #E65100;">بطاقتك غير نشطة حالياً</h2>
                            <p style="color: #666; font-size: 18px; margin-top: 20px;">
                                الحالة: <field name="card_status" readonly="1" style="font-weight: bold;"/>
                            </p>
                            <p style="color: #666; font-size: 18px;">
                                يرجى الاتصال بخدمة العملاء للمساعدة
                            </p>
                        </div>
                    </div>

                    <!-- Hidden Fields -->
                    <field name="display_state" invisible="1"/>
                    <field name="card_found" invisible="1"/>
                </sheet>

                <!-- Footer Buttons -->
                <footer attrs="{'invisible': [('display_state', '=', 'scan')]}">
                    <button name="action_scan_new_card"
                            string="مسح بطاقة أخرى"
                            type="object"
                            class="btn-primary"
                            style="font-size: 18px; padding: 10px 30px;"/>
                    <button name="action_close_kiosk"
                            string="إغلاق"
                            type="object"
                            class="btn-secondary"
                            style="font-size: 18px; padding: 10px 30px;"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Resort Card Actions -->
    <record id="action_resort_cards" model="ir.actions.act_window">
        <field name="name">بطاقات المنتجع</field>
        <field name="res_model">resort.card</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء أول بطاقة منتجع
            </p>
            <p>
                إنشاء بطاقات للعملاء مع أسمائهم وهواتفهم وباركود البطاقة.
                كل بطاقة تنشئ تلقائياً سجل عميل لتكامل نقطة البيع.
            </p>
        </field>
    </record>

    <!-- Card Top-up Actions -->
    <record id="action_card_topups" model="ir.actions.act_window">
        <field name="name">شحن البطاقات</field>
        <field name="res_model">card.topup</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                إنشاء أول شحن بطاقة
            </p>
            <p>
                معالجة شحن البطاقات للعملاء. امسح رقم البطاقة وأدخل المبلغ لإضافة المال إلى حسابهم.
            </p>
        </field>
    </record>

    <!-- Quick Cashier Top-up Action -->
    <record id="action_quick_topup" model="ir.actions.act_window">
        <field name="name">شحن سريع</field>
        <field name="res_model">card.topup</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'form_view_initial_mode': 'edit'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                شحن البطاقة السريع للكاشيرين
            </p>
            <p>
                امسح بطاقة العميل ومعالجة مدفوعات الشحن بسرعة وسهولة.
            </p>
        </field>
    </record>

    <!-- Card Kiosk Action -->
    <record id="action_card_kiosk" model="ir.actions.act_window">
        <field name="name">كشك رصيد البطاقة</field>
        <field name="res_model">card.kiosk</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'form_view_initial_mode': 'edit'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                كشك رصيد البطاقة
            </p>
            <p>
                كشك خدمة ذاتية للعملاء للتحقق من رصيد بطاقتهم عن طريق مسح البطاقة.
            </p>
        </field>
    </record>



    <!-- Menu Items -->
    <!-- Root Menu for Card Management -->
    <menuitem id="menu_card_management_main"
              name="إدارة البطاقات"
              sequence="50"/>

    <!-- Sub-menu for operations -->
    <menuitem id="menu_card_management_root"
              name="العمليات"
              parent="menu_card_management_main"
              sequence="10"/>

    <menuitem id="menu_resort_cards"
              name="بطاقات المنتجع"
              parent="menu_card_management_root"
              action="action_resort_cards"
              sequence="10"/>

    <menuitem id="menu_card_topups"
              name="شحن البطاقات"
              parent="menu_card_management_root"
              action="action_card_topups"
              sequence="15"/>

    <menuitem id="menu_quick_topup"
              name="شحن سريع"
              parent="menu_card_management_root"
              action="action_quick_topup"
              sequence="16"/>

    <menuitem id="menu_card_kiosk"
              name="كشك الرصيد"
              parent="menu_card_management_root"
              action="action_card_kiosk"
              sequence="17"/>

    <menuitem id="menu_daily_report"
              name="التقرير اليومي"
              parent="menu_card_management_root"
              action="action_daily_report_wizard"
              sequence="18"/>

    <menuitem id="menu_card_reissue_log"
              name="سجل إعادة إصدار البطاقات"
              parent="menu_card_management_root"
              action="action_card_reissue_log"
              sequence="19"/>

    <!-- Configuration Menu -->
    <menuitem id="menu_card_management_config"
              name="الإعدادات"
              parent="menu_card_management_main"
              sequence="20"
              groups="point_of_sale.group_pos_manager"/>

    <menuitem id="menu_card_status"
              name="حالة البطاقة"
              parent="menu_card_management_config"
              action="action_card_status"
              sequence="10"
              groups="point_of_sale.group_pos_manager"/>

</odoo>
