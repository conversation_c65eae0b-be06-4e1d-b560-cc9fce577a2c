<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Cashier Session Form View -->
    <record id="view_cashier_session_form" model="ir.ui.view">
        <field name="name">cashier.session.form</field>
        <field name="model">cashier.session</field>
        <field name="arch" type="xml">
            <form string="جلسة الكاشير" create="false" edit="false" delete="false" duplicate="false">
                <header>
                    <button name="action_end_session" string="إنهاء الجلسة" type="object"
                            class="btn-success" attrs="{'invisible': [('state', '!=', 'open')]}"/>
                    <button name="action_print_session_report" string="طباعة تقرير الجلسة" type="object"
                            class="btn-info" icon="fa-print"/>
                    <field name="state" widget="statusbar" readonly="1"/>
                </header>
                
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="معلومات الجلسة">
                            <field name="user_id" readonly="1" string="الكاشير"/>
                            <field name="start_time" readonly="1" string="وقت البداية"/>
                            <field name="end_time" readonly="1" string="وقت النهاية"/>
                            <field name="duration" widget="float_time" readonly="1" string="مدة الجلسة"/>
                        </group>

                        <group string="الأموال المحصلة">
                            <field name="currency_id" invisible="1"/>
                            <field name="total_money_collected" readonly="1" widget="monetary" options="{'currency_field': 'currency_id'}" string="إجمالي المبلغ"/>
                        </group>

                    </group>

                    <!-- Payment Methods Breakdown List -->
                    <group string="طرق الدفع المستخدمة" colspan="4">
                        <field name="payment_journal_ids" nolabel="1">
                            <tree string="طرق الدفع" create="false" edit="false" delete="false"
                                  decoration-bf="is_summary" decoration-success="is_summary">
                                <field name="journal_name" string="طريقة الدفع"/>
                                <field name="transaction_count" string="عدد المعاملات"/>
                                <field name="amount" string="المبلغ" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="is_summary" invisible="1"/>
                            </tree>
                        </field>
                    </group>
                    
                    <group>
                        <group string="ملخص المعاملات">
                            <field name="cards_created_count" readonly="1" string="البطاقات المنشأة"/>
                            <field name="topups_count" readonly="1" string="عمليات الشحن"/>
                        </group>
                    </group>
                    
                    <!-- Session Report View -->
                    <div attrs="{'invisible': [('state', '!=', 'closed')]}" class="alert alert-success">
                        <h3>تم إنهاء الجلسة!</h3>
                        <p><strong>إجمالي الأموال المحصلة:</strong> <field name="total_money_collected" readonly="1" widget="monetary" options="{'currency_field': 'currency_id'}"/></p>
                        <p><strong>البطاقات المنشأة:</strong> <field name="cards_created_count" readonly="1"/></p>
                        <p><strong>عمليات الشحن المعالجة:</strong> <field name="topups_count" readonly="1"/></p>
                        <p><strong>مدة الجلسة:</strong> <field name="duration" widget="float_time" readonly="1"/></p>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Cashier Session Tree View -->
    <record id="view_cashier_session_tree" model="ir.ui.view">
        <field name="name">cashier.session.tree</field>
        <field name="model">cashier.session</field>
        <field name="arch" type="xml">
            <tree string="جلسات الكاشير" decoration-success="state=='closed'" decoration-info="state=='open'" create="false" edit="false" delete="false">
                <field name="display_name" string="اسم الجلسة"/>
                <field name="user_id" string="الكاشير"/>
                <field name="start_time" string="وقت البداية"/>
                <field name="end_time" string="وقت النهاية"/>
                <field name="duration" widget="float_time" string="المدة"/>
                <field name="currency_id" invisible="1"/>
                <field name="total_money_collected" widget="monetary" options="{'currency_field': 'currency_id'}" string="إجمالي المبلغ"/>
                <field name="cards_created_count" string="البطاقات المنشأة"/>
                <field name="topups_count" string="عمليات الشحن"/>
                <field name="state" string="الحالة"/>
            </tree>
        </field>
    </record>

    <!-- Cashier Session Search View -->
    <record id="view_cashier_session_search" model="ir.ui.view">
        <field name="name">cashier.session.search</field>
        <field name="model">cashier.session</field>
        <field name="arch" type="xml">
            <search string="البحث في الجلسات">
                <field name="user_id" string="الكاشير"/>
                <field name="start_time" string="وقت البداية"/>
                <filter string="الجلسات المفتوحة" name="open" domain="[('state', '=', 'open')]"/>
                <filter string="الجلسات المغلقة" name="closed" domain="[('state', '=', 'closed')]"/>
                <filter string="اليوم" name="today" domain="[('start_time', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('start_time', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <group expand="0" string="تجميع حسب">
                    <filter string="المستخدم" name="group_user" context="{'group_by': 'user_id'}"/>
                    <filter string="التاريخ" name="group_date" context="{'group_by': 'start_time:day'}"/>
                    <filter string="الحالة" name="group_status" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action to Start New Session (must be defined first) -->
    <record id="action_start_new_session" model="ir.actions.server">
        <field name="name">بدء جلسة جديدة</field>
        <field name="model_id" ref="model_cashier_session"/>
        <field name="state">code</field>
        <field name="code">
            action = model.action_start_session()
        </field>
    </record>

    <!-- Action for Cashier Sessions -->
    <record id="action_cashier_session" model="ir.actions.act_window">
        <field name="name">جلسات الكاشير</field>
        <field name="res_model">cashier.session</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_today': 1, 'create': False, 'edit': False, 'delete': False, 'duplicate': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد جلسات!
            </p>
            <p>
                ابدأ جلسة جديدة لتتبع تحصيل الأموال.
            </p>
        </field>
    </record>

    <!-- Server Action for My Session Logic -->
    <record id="action_my_session_logic" model="ir.actions.server">
        <field name="name">منطق جلستي</field>
        <field name="model_id" ref="model_cashier_session"/>
        <field name="state">code</field>
        <field name="code">
# Check if user has an open session
current_session = model.get_current_session(env.user.id)

if current_session:
    # Force refresh of computed fields
    current_session.refresh_session_data()

    # User has open session - show it using the same action as "All Sessions"
    # This ensures consistent behavior and context
    action = {
        'type': 'ir.actions.act_window',
        'name': 'جلستي الحالية',
        'res_model': 'cashier.session',
        'res_id': current_session.id,
        'view_mode': 'form',
        'target': 'current',
        'views': [(env.ref('card_management.view_cashier_session_form').id, 'form')],
        'context': {
            'create': False,
            'edit': False,
            'delete': False,
            'duplicate': False,
            'form_view_initial_mode': 'readonly',
        },
        'flags': {
            'mode': 'readonly',
        }
    }
else:
    # No open session - start new one
    action = model.action_start_session()
        </field>
    </record>

    <!-- Menu Action for My Session - DEPRECATED, using server action instead -->
    <!-- This record is kept for reference but not used -->
    <record id="action_my_current_session_deprecated" model="ir.actions.act_window">
        <field name="name">جلستي (مهجور)</field>
        <field name="res_model">cashier.session</field>
        <field name="view_mode">form</field>
        <field name="domain">[('user_id', '=', uid), ('state', '=', 'open')]</field>
        <field name="context">{'create': False, 'edit': False, 'delete': False, 'duplicate': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                لا توجد جلسة نشطة!
            </p>
            <p>
                <a type="action" name="%(action_my_session_logic)d" class="btn btn-primary">بدء جلسة جديدة</a>
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_cashier_sessions_root"
              name="جلسات الكاشير"
              parent="menu_card_management_root"
              sequence="50"/>

    <menuitem id="menu_my_session"
              name="جلستي"
              parent="menu_cashier_sessions_root"
              action="action_my_session_logic"
              sequence="10"/>

    <menuitem id="menu_all_sessions"
              name="جميع الجلسات"
              parent="menu_cashier_sessions_root"
              action="action_cashier_session"
              sequence="20"/>

</odoo>
