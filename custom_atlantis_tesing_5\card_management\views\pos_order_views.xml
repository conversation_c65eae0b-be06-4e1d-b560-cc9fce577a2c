<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Refund Approval field to POS Order form -->
    <record id="view_pos_order_form_refund_approval" model="ir.ui.view">
        <field name="name">pos.order.form.refund.approval</field>
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_pos_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='order_fields']" position="inside">
                <field name="refund_approved_by"
                       readonly="1"
                       string="Refund Approved By"
                       attrs="{'invisible': [('refund_approved_by', '=', False)]}"/>
            </xpath>
        </field>
    </record>
</odoo>
