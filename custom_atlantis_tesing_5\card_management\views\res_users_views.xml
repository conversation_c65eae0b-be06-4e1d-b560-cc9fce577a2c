<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Manager barcode is now handled via Employee barcode field -->

    <!-- Add Manager Barcode field to POS Config form -->
    <record id="view_pos_config_form_refund_security" model="ir.ui.view">
        <field name="name">pos.config.form.refund.security</field>
        <field name="model">pos.config</field>
        <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='other_devices']" position="after">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_left_pane">
                        <field name="require_manager_approval_for_refunds"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="require_manager_approval_for_refunds"/>
                        <div class="text-muted">
                            Require manager barcode approval for all refunds in POS
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
