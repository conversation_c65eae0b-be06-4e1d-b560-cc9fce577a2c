# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import datetime, date


class DailyReportWizard(models.TransientModel):
    _name = 'daily.report.wizard'
    _description = 'Daily Cashier Report Wizard'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=fields.Date.today
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.today
    )
    cashier_id = fields.Many2one(
        'res.users',
        string='Cashier',
        help='Leave empty for all cashiers'
    )
    report_type = fields.Selection([
        ('summary', 'Summary Only'),
        ('detailed', 'Detailed Report'),
    ], string='Report Type', default='detailed', required=True)

    def action_print_report(self):
        """Generate and print the daily report"""
        self.ensure_one()
        
        # Get report data
        topup_model = self.env['card.topup']
        
        # Build domain
        domain = [
            ('create_date', '>=', self.date_from),
            ('create_date', '<=', self.date_to),
            ('state', '=', 'done'),
        ]
        
        if self.cashier_id:
            domain.append(('cashier_id', '=', self.cashier_id.id))
        
        # Get top-ups
        topups = topup_model.search(domain, order='create_date')
        
        if not topups:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'warning',
                    'message': 'No transactions found for the selected criteria.',
                    'sticky': False,
                }
            }
        
        # Calculate summary data
        total_amount = sum(topups.mapped('topup_amount'))
        average_amount = total_amount / len(topups) if topups else 0
        
        # Payment method summary
        payment_summary = []
        for method in ['cash', 'card', 'bank']:
            method_topups = topups.filtered(lambda t: t.payment_method == method)
            if method_topups:
                payment_summary.append({
                    'method': method,
                    'count': len(method_topups),
                    'total': sum(method_topups.mapped('topup_amount'))
                })
        
        # Cash reconciliation
        cash_total = sum(topups.filtered(lambda t: t.payment_method == 'cash').mapped('topup_amount'))
        card_total = sum(topups.filtered(lambda t: t.payment_method == 'card').mapped('topup_amount'))
        bank_total = sum(topups.filtered(lambda t: t.payment_method == 'bank').mapped('topup_amount'))
        
        # Prepare report data
        data = {
            'docs': topups,
            'total_amount': total_amount,
            'average_amount': average_amount,
            'payment_summary': payment_summary,
            'cash_total': cash_total,
            'card_total': card_total,
            'bank_total': bank_total,
            'cashier_name': self.cashier_id.name if self.cashier_id else 'All Cashiers',
            'report_date': self.date_from.strftime('%d/%m/%Y') if self.date_from == self.date_to else f"{self.date_from.strftime('%d/%m/%Y')} - {self.date_to.strftime('%d/%m/%Y')}",
            'company': self.env.company,
        }
        
        # Use with_context to ensure company is available in template
        return self.env.ref('card_management.action_daily_cashier_report').with_context(
            company_id=self.env.company.id
        ).report_action(topups, data=data)

    def action_view_transactions(self):
        """View transactions in list view"""
        self.ensure_one()
        
        # Build domain
        domain = [
            ('create_date', '>=', self.date_from),
            ('create_date', '<=', self.date_to),
            ('state', '=', 'done'),
        ]
        
        if self.cashier_id:
            domain.append(('cashier_id', '=', self.cashier_id.id))
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Daily Transactions',
            'res_model': 'card.topup',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {
                'search_default_group_cashier': 1,
            }
        }
