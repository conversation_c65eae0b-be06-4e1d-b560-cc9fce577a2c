<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Daily Report Wizard Form -->
    <record id="view_daily_report_wizard_form" model="ir.ui.view">
        <field name="name">daily.report.wizard.form</field>
        <field name="model">daily.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Daily Cashier Report">
                <sheet>
                    <div class="oe_title">
                        <h1>Generate Daily Report</h1>
                        <p>Generate cashier report for top-up transactions</p>
                    </div>

                    <group>
                        <group>
                            <field name="date_from"/>
                            <field name="date_to"/>
                        </group>
                        <group>
                            <field name="cashier_id" placeholder="All Cashiers"/>
                            <field name="report_type"/>
                        </group>
                    </group>

                    <div class="alert alert-info" role="alert">
                        <strong>Report includes:</strong>
                        <ul>
                            <li>Summary of all top-up transactions</li>
                            <li>Payment method breakdown (Cash, Card, Bank)</li>
                            <li>Cash reconciliation totals</li>
                            <li>Detailed transaction list</li>
                        </ul>
                    </div>
                </sheet>

                <footer>
                    <button name="action_print_report" 
                            string="Print Report" 
                            type="object" 
                            class="btn-primary"/>
                    <button name="action_view_transactions" 
                            string="View Transactions" 
                            type="object" 
                            class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Daily Report Action -->
    <record id="action_daily_report_wizard" model="ir.actions.act_window">
        <field name="name">Daily Cashier Report</field>
        <field name="res_model">daily.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_daily_report_wizard_form"/>
    </record>
</odoo>
