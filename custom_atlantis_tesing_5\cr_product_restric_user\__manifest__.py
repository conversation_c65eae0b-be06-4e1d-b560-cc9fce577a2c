# -*- coding: utf-8 -*-
# Part of Creyox Technologies
{
    "name": "Product Restriction on Users | User Product Access Control | Product & Category Access Restriction | Product Visibility Control for Users",
    "author": "Creyox Technologies",
    "website": "https://www.creyox.com",
    "support": "<EMAIL>",
    "version": "15.0.0.1",
    "summary":
        """
        The Product Restriction on Users Odoo App is designed to provide businesses with greater control over product 
        visibility within the Odoo system. This app allows administrators to restrict access to specific products or 
        product categories based on the roles and permissions of individual users. By assigning particular products or 
        product categories to users, businesses can streamline their sales processes, limit access to sensitive items, 
        and prevent unauthorized sales.
        
        Product Restriction on Users
        Product Restriction on Users in odoo
        User Product Access Control
        User Product Access Control in odoo
        Product & Category Access Restriction
        Product & Category Access Restriction in odoo
        Product Visibility Control for Users
        Product Visibility Control for Users in odoo
        User-Based Product Restrictions
        User-Based Product Restrictions in odoo
        Product and Category Visibility Management
        Product and Category Visibility Management in odoo
        Restricted Product Access for Users
        Restricted Product Access for Users in odoo
        Category and Product Access Control
        Category and Product Access Control in odoo
        User-Specific Product Restriction
        User-Specific Product Restriction in odoo
        
        How can I restrict products for certain users in Odoo?
        Odoo app for user-specific product visibility control
        Restrict product categories for specific users in Odoo
        How to manage product visibility based on user roles in Odoo?
        Odoo product restriction app for user-based access control
        Limit product access by user or role in Odoo
        Odoo app to assign product and category permissions to users
        Odoo product access control for user groups
        How to manage user access to sensitive products in Odoo?
        Odoo app for restricting access to product categories
        Product and category restriction by user in Odoo
        Control product visibility by user role in Odoo
        How to limit product listings for certain users in Odoo?
        """,
    "sequence": 10,
    "description":
        """
        The Product Restriction on Users Odoo App is designed to provide businesses with greater control over product 
        visibility within the Odoo system. This app allows administrators to restrict access to specific products or 
        product categories based on the roles and permissions of individual users. By assigning particular products or 
        product categories to users, businesses can streamline their sales processes, limit access to sensitive items, 
        and prevent unauthorized sales.

        Product Restriction on Users
        Product Restriction on Users in odoo
        User Product Access Control
        User Product Access Control in odoo
        Product & Category Access Restriction
        Product & Category Access Restriction in odoo
        Product Visibility Control for Users
        Product Visibility Control for Users in odoo
        User-Based Product Restrictions
        User-Based Product Restrictions in odoo
        Product and Category Visibility Management
        Product and Category Visibility Management in odoo
        Restricted Product Access for Users
        Restricted Product Access for Users in odoo
        Category and Product Access Control
        Category and Product Access Control in odoo
        User-Specific Product Restriction
        User-Specific Product Restriction in odoo

        How can I restrict products for certain users in Odoo?
        Odoo app for user-specific product visibility control
        Restrict product categories for specific users in Odoo
        How to manage product visibility based on user roles in Odoo?
        Odoo product restriction app for user-based access control
        Limit product access by user or role in Odoo
        Odoo app to assign product and category permissions to users
        Odoo product access control for user groups
        How to manage user access to sensitive products in Odoo?
        Odoo app for restricting access to product categories
        Product and category restriction by user in Odoo
        Control product visibility by user role in Odoo
        How to limit product listings for certain users in Odoo?
        """,
    "category": "Sales",
    "price": 0,
    "currency": "USD",
    "license": "AGPL-3",
    "depends": ["base","product"],
    "data": [
    "security/product_security.xml",
    "views/res_users.xml",
    ],
    "installable": True,
    "auto_install": False,
    "application": True,
    "images": ["static/description/banner.png"],
}
