<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_cr_res_users_form_inherit_for_product_restriction" model="ir.ui.view">
        <field name="name">Product.Restrictions</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="Allow Products" name="allow_products">
                    <group>
                        <field name="cr_restriction_on" widget="radio"/>
                        <field name="cr_product_template_ids" widget="many2many_tags" attrs="{'invisible': [('cr_restriction_on','!=','product')]}"/>
                        <field name="cr_product_category_ids" widget="many2many_tags" attrs="{'invisible': [('cr_restriction_on','!=','category')]}"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>