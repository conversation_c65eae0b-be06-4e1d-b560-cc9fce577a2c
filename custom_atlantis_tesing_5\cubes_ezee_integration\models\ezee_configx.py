from odoo import models, fields
import requests
import csv
import json
from io import StringIO
from dateutil.relativedelta import relativedelta
import logging
_logger = logging.getLogger(__name__)

import requests
import json
import xml.etree.ElementTree as ET
from datetime import datetime
import pytz
from datetime import datetime




class EzeeConfig(models.Model):
    _name = 'ezee.config'
    _description = 'Ezee Configuration'

    url = fields.Char(string='URL', required=True, default='https://live.ipms247.com')
    authcode = fields.Char(string='Auth Code', required=True, default="4919098461335208ad-aa16-11ee-b")
    hotel_code = fields.Char(string='Hotel Code', required=True, default="36917")
    days_before = fields.Integer(string="Pull Days Before", default=2)
    days_after = fields.Integer(string="Pull Days After", default=2)
    company_id = fields.Many2one('res.company', string="Company")

    def action_get_rooms_list(self):
        url = "https://live.ipms247.com/index.php/page/service.pos2pms"

        payload = "<?xml version=\"1.0\" standalone=\"yes\"?>\n<request>\n    <auth>4919098461335208ad-aa16-11ee-b</auth>\n    <oprn>roomlist</oprn>\n</request>"
        headers = {
            'Content-Type': 'application/json',
        }
        response = requests.request("POST", url, headers=headers, data=payload)
        root = ET.fromstring(response.text)

        roomrows_dict = {
            "roomrows": [
                {
                    "guestname": row.find("guestname").text,
                    "guestemail": row.find("guestemail").text,
                    "guestmobile": row.find("guestmobile").text,
                    "adult": int(row.find("adult").text),
                    "child": int(row.find("child").text),
                    "arrival": row.find("arrival").text,
                    "departure": row.find("departure").text,
                    "masterfolio": int(row.find("masterfolio").text),
                    "room": int(row.find("room").text),
                    "roomtype": row.find("roomtype").text,
                    "ratetype": row.find("ratetype").text,
                    "remarks": row.find("remarks").text,
                    "resno": int(row.find("resno").text),
                    "ea_foliounkid": int(row.find("ea_foliounkid").text),
                }
                for row in root.findall("./roomrows/row")
            ]
        }
        return roomrows_dict

    def cron_pull_bills_old(self):
        rooms = self.action_get_rooms_list()['roomrows']
        config = self.sudo().search([], limit=1)
        for c in config:
            url = c.url or ""
            authcode = c.authcode or ""
            hotel_code = c.hotel_code or ""

            if url and authcode and hotel_code:
                api_url = f"{url}/index.php/page/service.posting"

                payload = {
                    "authcode": authcode,
                    "hotel_code": hotel_code,
                    "fromdate": str(fields.Date.today() - relativedelta(days=c.days_before)),
                    "todate": str(fields.Date.today() + relativedelta(days=c.days_after))
                }

                headers = {
                    'Content-Type': 'application/json',
                }

                response = requests.post(api_url, headers=headers, data=json.dumps(payload))

                # Handle the response as needed
                csv_file = StringIO(response.text)

                # Use the csv.reader to parse the CSV data
                csv_reader = csv.reader(csv_file)

                # Iterate through rows and print values
                for i, row in enumerate(csv_reader):
                    if i == 0:
                        # Skip the header row
                        continue

                    # Access values as needed
                    unique_id, hotel_name, hotel_code, folio_no, date, voucher_no, invoice_number, guest_name, bill_to_name, guest_gst_number, state, phone_number, mobile_number, entry_type, particular, qty, currency, amount, gst_rate, cgst_tax_amount, sgst_tax_amount, igst_tax_amount, service_tax, luxury_tax, discount, adjustment, total, is_advance_deposit, is_inclusion, posted_by = row[:30]

                    check_account_move = self.env['account.move'].sudo().search([
                        ('ezee_unique_id','=',unique_id)
                    ])
                    if check_account_move:
                        continue

                    # Timezone
                    libya_timezone = pytz.timezone('Africa/Tripoli')
                    libya_datetime_str = "2024-04-16 12:30:45"  # Example datetime string
                    libya_datetime = datetime.strptime(libya_datetime_str, '%Y-%m-%d %H:%M:%S')
                    libya_datetime = libya_timezone.localize(libya_datetime)

                    # Convert Libya time to UTC
                    utc_timezone = pytz.utc
                    utc_datetime = libya_datetime.astimezone(utc_timezone)
                    # Example output
                    print("Libya Datetime:", libya_datetime)
                    print("UTC Datetime:", utc_datetime)

                    account_vals = {
                        'move_type': 'out_invoice',
                        'company_id': c.company_id.id or False,
                        'invoice_date': utc_datetime,
                        'ezee_unique_id': unique_id,
                        'ezee_hotel_name': hotel_name,
                        'ezee_hotel_code': hotel_code,
                        'ezee_folio_no': folio_no,
                        'ezee_date': date,
                        'ezee_voucher_no': voucher_no,
                        'ezee_invoice_number': invoice_number,
                        'ezee_guest_name': guest_name,
                        'ezee_bill_to_name': bill_to_name,
                        'ezee_guest_gst_number': guest_gst_number,
                        'ezee_state': state,
                        'ezee_phone_number': phone_number,
                        'ezee_mobile_number': mobile_number,
                        'ezee_entry_type': entry_type,
                        'ezee_particular': particular,
                        'ezee_currency': currency,
                        'ezee_gst_rate': gst_rate,
                        'ezee_cgst_tax_amount': cgst_tax_amount,
                        'ezee_sgst_tax_amount': sgst_tax_amount,
                        'ezee_igst_tax_amount': igst_tax_amount,
                        'ezee_service_tax': service_tax,
                        'ezee_luxury_tax': luxury_tax,
                        'ezee_adjustment': adjustment,
                        'ezee_is_advance_deposit': is_advance_deposit,
                        'ezee_is_inclusion': is_inclusion,
                        'ezee_posted_by': posted_by,
                    }
                    account_move = self.env['account.move'].sudo().search([
                        ('ezee_folio_no', '=', folio_no)
                    ], limit=1)
                    if not account_move:
                        account_move = self.env['account.move'].sudo().with_company(c.company_id.id).create(account_vals)

                    for room in rooms:
                        if int(room.get('masterfolio')) == int(folio_no):
                            account_move.ezee_room_no = room.get('ezee_room_no')

                    if account_move:
                        account_move.invoice_line_ids = [(0,0,{
                            'name': particular,
                            'quantity': qty or 1,
                            'price_unit': amount,
                            'price_subtotal': total,
                            'discount': discount,
                        })]

    def cron_pull_bills(self):
        config = self.sudo().search([], limit=1)
        for c in config:
            url = c.url or ""
            authcode = c.authcode or ""
            hotel_code = c.hotel_code or ""

            if url and authcode and hotel_code:
                api_url = f"{url}/booking/reservation_api/listing.php?request_type=BookingList&HotelCode=36917&APIKey=4919098461335208ad-aa16-11ee-b&arrival_from=%s&arrival_to=%s" % (str(fields.Date.today() - relativedelta(days=c.days_before)),str(fields.Date.today() + relativedelta(days=c.days_after)))
                payload = json.dumps({
                    "auth": "4919098461335208ad-aa16-11ee-b",
                    "oprn": "roomlist"
                })
                headers = {
                    'Content-Type': 'application/json',
                }
                response = requests.request("POST", api_url, headers=headers, data=payload)
                booking_list = response.json().get('BookingList')

                api_url = f"{url}/index.php/page/service.posting"

                payload = {
                    "authcode": authcode,
                    "hotel_code": hotel_code,
                    "fromdate": str(fields.Date.today() - relativedelta(days=c.days_before)),
                    "todate": str(fields.Date.today() + relativedelta(days=c.days_after))
                }

                headers = {
                    'Content-Type': 'application/json',
                }

                response = requests.post(api_url, headers=headers, data=json.dumps(payload))

                # Handle the response as needed
                csv_file = StringIO(response.text)

                # Use the csv.reader to parse the CSV data
                csv_reader = csv.reader(csv_file)

                # Iterate through rows and print values
                for i, row in enumerate(csv_reader):
                    if i == 0:
                        # Skip the header row
                        continue

                    # Access values as needed
                    unique_id, hotel_name, hotel_code, folio_no, date, voucher_no, invoice_number, guest_name, bill_to_name, guest_gst_number, state, phone_number, mobile_number, entry_type, particular, qty, currency, amount, gst_rate, cgst_tax_amount, sgst_tax_amount, igst_tax_amount, service_tax, luxury_tax, discount, adjustment, total, is_advance_deposit, is_inclusion, posted_by = row[
                                                                                                                                                                                                                                                                                                                                                                                                   :30]

                    check_account_move = self.env['account.move'].sudo().search([
                        ('ezee_unique_id', '=', unique_id)
                    ])
                    if check_account_move:
                        continue

                    account_vals = {
                        'move_type': 'out_invoice',
                        'company_id': c.company_id.id or False,
                        'invoice_date': date,
                        'ezee_unique_id': unique_id,
                        'ezee_hotel_name': hotel_name,
                        'ezee_hotel_code': hotel_code,
                        'ezee_folio_no': folio_no,
                        'ezee_date': date,
                        'ezee_voucher_no': voucher_no,
                        'ezee_invoice_number': invoice_number,
                        'ezee_guest_name': guest_name,
                        'ezee_bill_to_name': bill_to_name,
                        'ezee_guest_gst_number': guest_gst_number,
                        'ezee_state': state,
                        'ezee_phone_number': phone_number,
                        'ezee_mobile_number': mobile_number,
                        'ezee_entry_type': entry_type,
                        'ezee_particular': particular,
                        'ezee_currency': currency,
                        'ezee_gst_rate': gst_rate,
                        'ezee_cgst_tax_amount': cgst_tax_amount,
                        'ezee_sgst_tax_amount': sgst_tax_amount,
                        'ezee_igst_tax_amount': igst_tax_amount,
                        'ezee_service_tax': service_tax,
                        'ezee_luxury_tax': luxury_tax,
                        'ezee_adjustment': adjustment,
                        'ezee_is_advance_deposit': is_advance_deposit,
                        'ezee_is_inclusion': is_inclusion,
                        'ezee_posted_by': posted_by,
                    }
                    account_move = self.env['account.move'].sudo().search([
                        ('ezee_folio_no', '=', folio_no)
                    ], limit=1)

                    for booking in booking_list:
                        if int(booking.get('FolioNo')) == int(folio_no):
                            account_move_data = {
                                'ezee_reservation_no': booking.get('ReservationNo', ''),
                                'ezee_guest_name': booking.get('GuestName', ''),
                                'ezee_arrival_date': booking.get('ArrivalDate', ''),
                                'ezee_departure_date': booking.get('DepartureDate', ''),
                                'ezee_reservation_date': booking.get('ReservationDate', ''),
                                'ezee_room': booking.get('Room', ''),
                                'ezee_room_short_code': booking.get('RoomShortCode', ''),
                                'ezee_reservation_guarantee': booking.get('ReservationGuarantee', ''),
                                'ezee_source': booking.get('Source', ''),
                                'ezee_voucher_no': booking.get('VoucherNo', ''),
                                'ezee_mobile': booking.get('Mobile', ''),
                                'ezee_address': booking.get('Address', ''),
                                'ezee_email': booking.get('Email', ''),
                                'ezee_country': booking.get('Country', ''),
                                'ezee_adult': int(booking.get('Adult', 0)),
                                'ezee_child': int(booking.get('Child', 0)),
                                'ezee_phone': booking.get('Phone', ''),
                                'ezee_no_of_guest': booking.get('NoOfGuest', 0),
                                'ezee_no_of_nights': int(booking.get('NoOfNights', 0)),
                                'ezee_salutation': booking.get('salutation', ''),
                                'ezee_first_name': booking.get('FirstName', ''),
                                'ezee_last_name': booking.get('LastName', ''),
                                'ezee_due_amount': float(booking.get('DueAmount', 0)),
                                'ezee_deposit': float(booking.get('Deposit', 0)),
                                'ezee_status': booking.get('Status', ''),
                                'ezee_booking_status': booking.get('BookingStatus', ''),
                                'ezee_transaction_status': booking.get('TransactionStatus', ''),
                                'ezee_total_tax': float(booking.get('Total Tax', 0)),
                                'ezee_total_inclusive_tax': float(booking.get('TotalInclusiveTax', 0)),
                                'ezee_total_exclusive_tax': float(booking.get('TotalExclusivTax', 0)),
                                'ezee_other_revenue_exclusive_tax': float(booking.get('OtherRevenueExclusiveTax', 0)),
                                'ezee_other_revenue_inclusive_tax': float(booking.get('OtherRevenueInclusiveTax', 0)),
                                'ezee_folio_no': booking.get('FolioNo', ''),
                                # 'ezee_base_rate_exclusive_tax': float(
                                #     booking.get('BaseRateExclusiveTax', {}).get('2024-03-05', 0)),
                                # 'ezee_base_rate_inclusive_tax': float(
                                #     booking.get('BaseRateInclusiveTax', {}).get('2024-03-05', 0)),
                                'ezee_payment_type': booking.get('PaymentType', ''),
                                'ezee_rate_plan': booking.get('RatePlan', ''),
                                'ezee_arrival_time': booking.get('ArrivalTime', ''),
                                'ezee_departure_time': booking.get('DepartureTime', ''),
                                'ezee_room_no': booking.get('RoomNo', ''),
                                'ezee_bed_type': booking.get('BedType', ''),
                            }
                            departure_date_str = booking.get('DepartureDate', '')
                            departure_date = datetime.strptime(departure_date_str,
                                                               '%Y-%m-%d').date() if departure_date_str else None

                            # Check if departure_date is greater than today's date
                            if departure_date and departure_date > datetime.now().date():
                                continue
                            if not account_move:
                                account_move = self.env['account.move'].sudo().with_company(c.company_id.id).create(account_vals)
                            account_move.sudo().write(account_move_data)
                    if account_move:
                        if account_move.unique_ids.filtered(lambda x: x.name == unique_id):
                            continue
                        product_id = self.env['product.product'].sudo().search([
                            ('name', '=', particular),
                        ], limit=1)
                        if not product_id:
                            product_id = self.env['product.product'].sudo().create({
                                'name': particular
                            })
                        if particular not in ['Cash','Cash [Refund]']:
                            if particular == 'Room Charges':
                                if not account_move.invoice_line_ids.filtered(lambda x: x.product_id.id == product_id.id):
                                    account_move.invoice_line_ids = [(0, 0, {
                                        'product_id': product_id.id,
                                        'name': particular,
                                        'quantity': qty or 1,
                                        'price_unit': abs(float(amount)),
                                        'price_subtotal': abs(float(total)),
                                        'discount': discount,
                                    })]
                                else:
                                    lines = account_move.invoice_line_ids.filtered(lambda x: x.product_id.id == product_id.id)
                                    for line in lines:
                                        new_qty = line.quantity + 1
                                        new_total = line.price_subtotal + abs(float(amount))
                                        account_move.line_ids.filtered(lambda x: x.product_id.id == product_id.id).with_context(check_move_validity=False).unlink()
                                        account_move.invoice_line_ids = [(0, 0, {
                                            'product_id': product_id.id,
                                            'name': particular,
                                            'quantity': new_qty,
                                            'price_unit': abs(float(amount)),
                                            'price_subtotal': new_total,
                                            'discount': discount,
                                        })]
                            else:
                                account_move.invoice_line_ids = [(0, 0, {
                                    'product_id': product_id.id,
                                    'name': particular,
                                    'quantity': qty or 1,
                                    'price_unit': abs(float(amount)),
                                    'price_subtotal': abs(float(total)),
                                    'discount': discount,
                                })]
                        account_move.unique_ids = [(0,0,{
                            'name': unique_id
                        })]
