<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="group_product_category_restrict_feature" model="res.groups">
        <field name="name">Enable Product Category Feature</field>
    </record>

    <record id="group_access_of_assign_product_category" model="res.groups">
        <field name="name">Access of Assign Product Category in Company</field>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

     <record id="product_rule_restriction" model="ir.rule">
        <field name="name">Products  For Company</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="domain_force">[('categ_id', 'in', user.company_id.product_category_ids.ids +[False])  ]</field>
        <field name="groups" eval="[(4, ref('cubes_restriction_product_category_per_caompany.group_product_category_restrict_feature'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="active" eval="True"/>
    </record>
     <record id="product_category_rule_restriction" model="ir.rule">
        <field name="name">Products Category For Company</field>
        <field name="model_id" ref="product.model_product_category"/>
        <field name="domain_force">[('id', 'in', user.company_ids.product_category_ids.ids +[False]) ]</field>
        <field name="groups" eval="[(4, ref('cubes_restriction_product_category_per_caompany.group_product_category_restrict_feature'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="active" eval="True"/>
    </record>

</odoo>
