# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel_laundry
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-12 11:39+0000\n"
"PO-Revision-Date: 2020-08-12 11:39+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "Automatic Declaration"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__hotel_laundry__state__canceled
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Cancel"
msgstr "_Cancelar"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__canceled
msgid "Canceled"
msgstr "Cancelado"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__category_id
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__category_id1
msgid "Category"
msgstr "Categoria"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__company_id
msgid "Company"
msgstr "Companhia"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Confirm"
msgstr "Confirmar"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__hotel_laundry__state__confirmed
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__confirmed
msgid "Confirmed"
msgstr "Confirmado"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__cost_price
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__cost_price
msgid "Cost Price"
msgstr "Preço de custo"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__cost_rate
msgid "Cost Rate"
msgstr "Taxa de Custo"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__cost_subtotal
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__cost_subtotal
msgid "Cost Sub Total"
msgstr "Total de sub custo"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__create_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__create_uid
msgid "Created by"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__create_date
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__create_date
msgid "Created on"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Customer Return"
msgstr "Retornado ao Cliente"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__customer_returned
msgid "Customer Returned"
msgstr "Retornado ao Cliente"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__tax_id
msgid "Customer Taxes"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__display_name
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__display_name
msgid "Display Name"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__done
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Done"
msgstr "Feito"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__hotel_laundry__state__draft
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__draft
msgid "Draft"
msgstr "Projecto"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__hotel_laundry__state__edit
msgid "Edit"
msgstr "Editar"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "Extra Info"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__request_type__from_room
msgid "From Room"
msgstr "A partir do quarto"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__partner_id
msgid "Guest Name"
msgstr "Nome do convidado"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "History"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
msgid "Hotel Lanudry Service"
msgstr "Serviço de lavanderia do Hotel"

#. module: hotel_laundry
#: model:ir.actions.act_window,name:hotel_laundry.hotel_laundry_view
#: model:ir.model,name:hotel_laundry.model_hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_tree_view
msgid "Hotel Laundry"
msgstr "Hotel Lavanderia"

#. module: hotel_laundry
#: model:ir.model,name:hotel_laundry.model_hotel_laundry_picking_memory
msgid "Hotel Laundry Picking Memory"
msgstr "Memória de coleta de lavanderia do Hotel "

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__hotel_laundry_service_id
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
msgid "Hotel Laundry Service"
msgstr "Serviço de lavanderia do Hotel"

#. module: hotel_laundry
#: model:res.groups,name:hotel_laundry.group_laundry_user
msgid "Hotel Management / Laundry User"
msgstr "Gestão Hoteleira / Usuário da lavanderia"

#. module: hotel_laundry
#: model:res.groups,name:hotel_laundry.group_laundry_manager
msgid "Hotel Management/ Laundry Manager"
msgstr "Gestão Hoteleira / Gerente da lavanderia"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__id
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__id
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__id
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__id
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__id
msgid "ID"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__request_type__internal
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__service_type__internal
msgid "Internal"
msgstr "Interno"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__invoice_ids
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "Invoice Lines"
msgstr "Linhas de Fatura"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__invoice_state
msgid "Invoicing"
msgstr "Facturação"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__is_chargable
msgid "Is Chargable"
msgstr "É cobrado"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__item_id
msgid "Item"
msgstr "Item"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__item_id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__item_id_ref
msgid "Items"
msgstr "Items"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
msgid "Lanundry Service Items Info"
msgstr "Informações de itens de serviço de lavandaria"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product____last_update
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line____last_update
msgid "Last Modified on"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__write_uid
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__write_date
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__laundry_items_id
msgid "Laundry Items"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "Laundry Line"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "Laundry Lines"
msgstr ""

#. module: hotel_laundry
#: model:ir.model,name:hotel_laundry.model_laundry_management
#: model:ir.ui.menu,name:hotel_laundry.laundry_management_menu
msgid "Laundry Management"
msgstr "Gestão de lavanderia"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__laundry_service_product_line_ids
msgid "Laundry Product Service Line"
msgstr "Linha de serviço de produtos de lavanderia"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Laundry Return"
msgstr "Lavandaria - Devolvido"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__laundry_returned
msgid "Laundry Returned"
msgstr "Laundry Returned"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__laundry_service_id
#: model:ir.ui.menu,name:hotel_laundry.laundry_service_submenu
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_picking_memory_form
msgid "Laundry Service"
msgstr "Serviço de lavanderia"

#. module: hotel_laundry
#: model:ir.actions.act_window,name:hotel_laundry.laundry_management_view
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_tree_view1
msgid "Laundry Service Configuration"
msgstr "Configuração do serviço de lavandaria"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
msgid "Laundry Service Info"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
msgid "Laundry Service Items"
msgstr "itens de serviço de lavandaria"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__laundry_service_line_id
msgid "Laundry Service Line"
msgstr ""

#. module: hotel_laundry
#: model:ir.model,name:hotel_laundry.model_laundry_service_product
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__laundry_service_product_ids
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Laundry Service Product"
msgstr "Produto do serviço de lavandaria"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Laundry Service Product Info"
msgstr "Informação de produto de serviço lavanderia"

#. module: hotel_laundry
#: model:ir.ui.menu,name:hotel_laundry.laundry_service_request
msgid "Laundry Service Request"
msgstr "Solicitação de serviço de lavandaria"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__laundry_service_ids
msgid "Laundry Services"
msgstr "Serviços de Lavanderia"

#. module: hotel_laundry
#: model:ir.model,name:hotel_laundry.model_hotel_laundry_services_items
msgid "Laundry services Items Details"
msgstr "Detalhes de Itens de serviços de lavanderia"

#. module: hotel_laundry
#: model:ir.model,name:hotel_laundry.model_hotel_laundry_services
msgid "Laundry services in hotel"
msgstr "Serviços de lavanderia no hotel"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "Manual Description"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__move_id
msgid "Move"
msgstr "Mover"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking__product_return_moves
msgid "Moves"
msgstr "Mover"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__name
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__name
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__name
msgid "Name"
msgstr "Nome"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__hotel_laundry_picking__invoice_state__none
msgid "No invoicing"
msgstr "Sem facturação"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__name
msgid "Order Reference"
msgstr "Referência de ordem"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__pricelist_id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__pricelist_id
msgid "Pricelist"
msgstr "Lista de Preços"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__product_id
msgid "Product"
msgstr "Produto"

#. module: hotel_laundry
#: model:ir.model,name:hotel_laundry.model_laundry_service_product_line
msgid "Product Line show all items details"
msgstr "Linha de produtos mostrar todos os detalhes dos itens"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_picking_memory_tree
msgid "Product Moves"
msgstr "Movimentos de produto"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_picking_form
msgid "Provide the quantities of the returned products."
msgstr "Fornece as quantidades dos produtos retornados."

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__quantity
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__qty
msgid "Quantity"
msgstr "quantidade"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__date_order
msgid "Request Date"
msgstr "Data de solicitação"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__deadline_date
msgid "Request Deadline"
msgstr "Prazo de solicitação"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__request_type
msgid "Request Type"
msgstr "Tipo de solicitação"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__user_id
msgid "Responsible"
msgstr "Responsável"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_picking_form
msgid "Return"
msgstr "Voltar"

#. module: hotel_laundry
#: model:ir.actions.act_window,name:hotel_laundry.hotel_laundry_picking_action_form
#: model:ir.model,name:hotel_laundry.model_hotel_laundry_picking
msgid "Return Picking"
msgstr "Retorno de colheita"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_picking_form
msgid "Return lines"
msgstr "Linhas de retorno"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Room Info"
msgstr "Informacoes do Quarto"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__room_number
msgid "Room No"
msgstr "Quarto n º"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services_items__sale_price
msgid "Sale Price"
msgstr "Preço de venda"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__sale_subtotal
msgid "Sale Sub Total"
msgstr "Subtotal de venda"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__sales_price
msgid "Sales Price"
msgstr "Preço de venda"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__sales_rate
msgid "Sales Rate"
msgstr "Taxa de vendas"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__sale_subtotal
msgid "Sales Sub Total"
msgstr "Subtotal de vendas"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Send to Laundry"
msgstr "Enviar para lavanderia"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__state__sent_to_laundry
msgid "Sent to Laundry"
msgstr "Enviar para lavanderia"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__laundry_services_id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__laundry_services_id
msgid "Service Name"
msgstr "Nome do serviço"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Service Product Line Info"
msgstr "Informação de linha de produto de serviço"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__service_type
msgid "Service Type"
msgstr "Tipo de serviço"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__shop_id
msgid "Shop Name"
msgstr "Nome da loja"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__state
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__state
msgid "State"
msgstr "Estado"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.view_hotel_folio_form_inherit_laundry
msgid "States"
msgstr "Estado"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__amount_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__supplier_id
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__supplier_id
msgid "Supplier"
msgstr "Fornecedor"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__supplier_id
msgid "Supplier Id"
msgstr "Fornecedor ID"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Supplier Info"
msgstr "Informações do fornecedor"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry__partner_id
msgid "Supplier Name"
msgstr "Nome do fornecedor"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product__cost_tax_id
msgid "Supplier Taxes"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__supplier_id_temp
msgid "Supplier Temp Id"
msgstr "Id temporária de fornecedor"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__amount_tax
msgid "Tax"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__laundry_management__service_type__third_party
msgid "Third Party"
msgstr "Terceiros"

#. module: hotel_laundry
#: model:ir.model.fields,help:hotel_laundry.field_laundry_service_product__cost_rate
msgid ""
"This column will compute cost price based on the pricelist linked to "
"selected supplier"
msgstr "Esta coluna irá calcular o preço de custo com base na lista de preços ligados ao fornecedor selecionado"

#. module: hotel_laundry
#: model:ir.model.fields,help:hotel_laundry.field_laundry_service_product__sales_rate
#: model:ir.model.fields,help:hotel_laundry.field_laundry_service_product_line__sales_price
msgid ""
"This column will compute cost price based on the pricelist selected at "
"header part"
msgstr "Esta coluna irá calcular o preço de custo com base na lista de preços selecionada na parte do cabeçalho"

#. module: hotel_laundry
#: model:ir.model.fields.selection,name:hotel_laundry.selection__hotel_laundry_picking__invoice_state__2binvoiced
msgid "To be refunded/invoiced"
msgstr "A ser devolvido/facturado"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_management__amount_total
msgid "Total"
msgstr "Total"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_service_product_line_tree_view
msgid "Total Of Sale Subtotal"
msgstr "Total de venda Subtotal"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_management_form_view1
msgid "Total Of Sale Subtotals"
msgstr "Total de venda Subtotal"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_laundry_service_product_line__qty_uom
msgid "UOM"
msgstr "UOM"

#. module: hotel_laundry
#: model:product.product,uom_name:hotel_laundry.laundry_services_id
#: model:product.template,uom_name:hotel_laundry.laundry_services_id_product_template
msgid "Units"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_form_view
msgid "Update"
msgstr "Atualizar"

#. module: hotel_laundry
#: model:product.product,name:hotel_laundry.laundry_services_id
#: model:product.template,name:hotel_laundry.laundry_services_id_product_template
msgid "Washing Clothes"
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,help:hotel_laundry.field_laundry_management__room_number
msgid ""
"Will show list of currently occupied room no that belongs to selected shop."
msgstr ""

#. module: hotel_laundry
#: model:ir.model.fields,help:hotel_laundry.field_laundry_management__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user."
msgstr "Irá mostrar a lista de lojas que pertencem à empresas permitidas do usuário conectado."

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_picking_memory__wizard_id
msgid "Wizard"
msgstr "Assistente"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.hotel_laundry_picking_form
msgid "_Cancel"
msgstr "_Cancelar"

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_service_product_line_form_view123
msgid "laundry Service"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_service_product_line_form_view
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_service_product_line_tree_view
msgid "laundry Service Product Line"
msgstr ""

#. module: hotel_laundry
#: model_terms:ir.ui.view,arch_db:hotel_laundry.laundry_service_product_line_form_view12345
msgid "laundry Services Item"
msgstr "itens de serviço de lavandaria"

#. module: hotel_laundry
#: model:ir.model.fields,field_description:hotel_laundry.field_hotel_laundry_services__laundry_services_items_ids
msgid "laundry service items"
msgstr "itens de serviço de lavandaria"
