# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel_management
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-13 04:49+0000\n"
"PO-Revision-Date: 2020-08-13 04:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_agent_commission_invoice_line
msgid " Commision Invoice Line"
msgstr "Ligne Facture Commission"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "(Compute)"
msgstr "(Calculer)"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "(Update History)"
msgstr "Mise ê jour"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__01
msgid "1 AM"
msgstr "1:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__13
msgid "1 PM"
msgstr "13:00"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "1.00"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__10
msgid "10 AM"
msgstr "10:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__22
msgid "10 PM"
msgstr "22:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__11
msgid "11 AM"
msgstr "11:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__23
msgid "11 PM"
msgstr "23:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__24
msgid "12 Mid Night"
msgstr "Mi-nuit"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__12
msgid "12 Noon"
msgstr "Midi"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__02
msgid "2 AM"
msgstr "2:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__14
msgid "2 PM"
msgstr "14:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__name__24hour
msgid "24 Hours"
msgstr "24 heures"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__03
msgid "3 AM"
msgstr "3:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__15
msgid "3 PM"
msgstr "15:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__04
msgid "4 AM"
msgstr "4:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__16
msgid "4 PM"
msgstr "16:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__05
msgid "5 AM"
msgstr "5:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__17
msgid "5 PM"
msgstr "17:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__06
msgid "6 AM"
msgstr "6:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__18
msgid "6 PM"
msgstr "18:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__07
msgid "7 AM"
msgstr "7:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__19
msgid "7 PM"
msgstr "19:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__08
msgid "8 AM"
msgstr "8:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__20
msgid "8 PM"
msgstr "20:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__21
msgid "9  PM"
msgstr "21:00"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__time__09
msgid "9 AM"
msgstr "9:00"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid ""
"<strong style=\"font-family :Times New Roman ;line-height: 200%; font-size: "
"30px;\">Kitchen Order Ticket</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong> Date : </strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong> Invoice Address : </strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
msgid "<strong>#No</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Authorized Signatory</strong>"
msgstr "Signature"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Cashier : </strong>"
msgstr "Caissier"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Check In</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Check Out</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
msgid "<strong>Check-In-Date</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
msgid "<strong>Check-Out-Date</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Date</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Days</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Food Item List</strong>"
msgstr "Liste de Produits Alimentaires "

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Food Item</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Guest Name</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Guest's Signature</strong>"
msgstr "Signature Client"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_room_report
msgid "<strong>No. of Times used</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Order List</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Order Number</strong>"
msgstr "Nombre Commande"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>PLEASE RETURN YOUR KEY ON DEPARTURE.</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Particulers</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Pax</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Qty</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Rate</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Reservation No.</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Room Accommodation Invoice</strong>"
msgstr "Facture Hébergement"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_room_report
msgid "<strong>Room No.</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Room No</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Room Number</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Room Type</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>SN</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Served By</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>SubTotal</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Table Information</strong>"
msgstr "Information Table"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Table Number</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Tax :</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Total Charges :</strong>"
msgstr "Total des charges"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Waiter Name</strong>"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__product_type
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__product_type
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_version
msgid "A/B Testing"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__unsplash_access_key
msgid "Access Key"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__access_warning
msgid "Access warning"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__qty_delivered_method
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__qty_delivered_method
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_taxcloud
msgid "Account TaxCloud"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_accountant
msgid "Accounting"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_ids
msgid "Activities"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__activity
msgid "Activity"
msgstr "activité"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_state
msgid "Activity State"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_kitchen_order_tickets
msgid "Add BOM to restaurant module"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__snailmail_cover
msgid "Add a Cover Page"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_order_list
msgid "Add restaurant inventory"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Address"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__adults
msgid "Adults"
msgstr "Adultes"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__adv_amount
msgid "Advance Amount"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__deposit_cost2
#: model_terms:ir.ui.view,arch_db:hotel_management.advance_payment_wizard1
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Advance Payment"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_advance_payment_wizard
msgid "Advance Payment Detail Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.act_advance_payment_entry1
msgid "Advance Payment Entry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_sale_pricelist
msgid "Advanced Pricelists"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_stock_landed_costs
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__agent_id
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__agent
#: model:ir.model.fields,field_description:hotel_management.field_res_users__agent
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__via__agent
#: model_terms:ir.ui.view,arch_db:hotel_management.view_partner_property_form_commission
msgid "Agent"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_agent_commission_invoice
msgid "Agent Commision Invoice"
msgstr "Facture Commissionnaire"

#. module: hotel_management
#: model:ir.ui.menu,name:hotel_management.menu_agent_commission
msgid "Agent Commission"
msgstr "Commissionnaire"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__name
msgid "Agent Commission ID"
msgstr "Identifiant Agent intermédiaire"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_agent_commission_invoice_form_view
#: model:ir.ui.menu,name:hotel_management.menu_agent_commission_invoice
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoice_tree_view
msgid "Agent Commission Invoice"
msgstr "Facture  Agent intermédiaire"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoice_line_tree_view
msgid "Agent Commission Invoice Line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__agent_invoice_ids
msgid "Agent Invoices"
msgstr "Factures Agent"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__alias_domain
msgid "Alias Domain"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_cancel
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_cancel
msgid "All Cancelled Reservation"
msgstr "Réservations annulées"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_confirm
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_confirm
msgid "All Confirm Reservation"
msgstr "Toutes les réservation confirmées"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_done
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_done
msgid "All Done Reservation"
msgstr "Toutes les réservations effectuées"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_draft11
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_draft
msgid "All Draft Reservation"
msgstr "Toutes les réservations brouillon"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_all
msgid "All Reservation"
msgstr "Toutes les réservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_product_margin
msgid "Allow Product Margin"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_check_printing
msgid "Allow check printing and deposits"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_base_import
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__amt
msgid "Amount"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_analytic_accounting
msgid "Analytic Accounting"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_analytic_tags
msgid "Analytic Tags"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__analytic_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__analytic_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__analytic_line_ids
msgid "Analytic lines"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Apply"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__approved_by
msgid "Approved By"
msgstr "Approuvé par"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Arrival Departure Report"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Arrival Time"
msgstr ""

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.report_arrival_dept_guest
msgid "Arrival/Depart Guest List"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Assign"
msgstr "Assigner"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__assign_to
msgid "Assign Method"
msgstr "Methode assignation"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__state__assign
msgid "Assigned"
msgstr "Assigné"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__assigned_internal
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__assigned_third_party
msgid "Assigned To"
msgstr "Assigné"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_google_drive
msgid "Attach Google documents to any record"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Automatic Declaration"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__avl_state
msgid "Availability Status"
msgstr "Statut disponibilité"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__available_threshold
msgid "Availability Threshold"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_tables__avl_state__available
msgid "Available"
msgstr "Disponible"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_menucard__product_nature__bot
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_kitchen_order_tickets__product_nature__bot
#: model:ir.model.fields.selection,name:hotel_management.selection__product_template__product_nature__bot
#: model:ir.ui.menu,name:hotel_management.menu_view_hotel_restaurant_kitchen_order_tickets_bot_inherit
msgid "BOT"
msgstr "TDB"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_kitchen_order_tickets_tree_bot
msgid "BOT List"
msgstr "Liste TDB"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__ordernobot
msgid "BOT Number"
msgstr "Numéro TDB"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_view_hotel_restaurant_kitchen_order_tickets_form_tree_bot
msgid "BOT Order List"
msgstr "Liste de Commandes TDB"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Base Currency Amt("
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Basic Info"
msgstr "Information basique"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_picking_batch
msgid "Batch Pickings"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_invoice_extract
msgid "Bill Digitalization"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__res_partner__reservation_warn__block
msgid "Blocking Message"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_tables__avl_state__book
msgid "Booked"
msgstr "Réservé"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Booked Room"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_gantt111
msgid "Booking Details"
msgstr "Détails de réservation"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_room_booking_history_tree
#: model:ir.ui.menu,name:hotel_management.menu_hotel_room_booking_history_tree
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_room_form_inherit_rentalss
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_tree1
msgid "Booking History"
msgstr "Historique Réservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__booking_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__booking_id
msgid "Booking Ref"
msgstr "Réf. de réservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__book_id
msgid "Booking Ref."
msgstr "RÀf. de rÀservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_budget
msgid "Budget Management"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_mrp_byproducts
msgid "By-Products"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cdn_url
msgid "CDN Base URL"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cdn_filters
msgid "CDN Filters"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_updatable
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_updatable
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_updatable
msgid "Can Edit Product"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_tables__state__canceled
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_room_booking_history__state__cancle
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__state__cancel
#: model_terms:ir.ui.view,arch_db:hotel_management.advance_payment_wizard1
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.deposit_journal_entry_wizard1
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
#: model_terms:ir.ui.view,arch_db:hotel_management.view_folio_invoice_transfer_wizard
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Cancel"
msgstr "Annuler"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Cancel Reservation"
msgstr "Annuler Réservation"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_cancel_foilo_wizard
msgid "Cancel Wizard"
msgstr "Annuler assistance"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__agent_commission_invoice__state__cancel
msgid "Canceled"
msgstr "Annuler"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__state__cancel
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_order__state__cancel
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__cancel
msgid "Cancelled"
msgstr "Annulé"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__capacity
msgid "Capacity"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cart_recovery_mail_template
msgid "Cart Recovery Email"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__tax_exigibility
msgid "Cash Basis"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_cash_rounding
msgid "Cash Rounding"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_uom_category_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_uom_category_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_uom_category_id
msgid "Category"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_stock_change_standard_price
msgid "Change Standard Price"
msgstr "Modifier le prix standard"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_search
msgid "Check In"
msgstr "Arrivé"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_search
msgid "Check Out"
msgstr "Départ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_in
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_in_date
msgid "CheckIn Date"
msgstr "Date d'arrivée"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_checkin_details_report
msgid "CheckIn Detail"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
msgid "CheckIn Guest List"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "CheckIn List"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__check_out
msgid "CheckOut"
msgstr "départ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_out
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_out_date
msgid "CheckOut Date"
msgstr "Date de départ"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_checkout_details_report
msgid "CheckOut Detail"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "CheckOut List"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__checkin
msgid "Checkin Date"
msgstr "Date d'arrivée"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Checkout"
msgstr "Départ"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.checkout_configuration_action
#: model:ir.model,name:hotel_management.model_checkout_configuration
#: model_terms:ir.ui.view,arch_db:hotel_management.checkout_configuration_form
#: model_terms:ir.ui.view,arch_db:hotel_management.checkout_configuration_tree
msgid "Checkout Configuration"
msgstr "Configuration départ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__checkout
msgid "Checkout Date"
msgstr "Date de départ"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
msgid "Checkout Guest List"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.checkout_configuration_form
msgid "Checkout Info"
msgstr "Info départ"

#. module: hotel_management
#: model:ir.ui.menu,name:hotel_management.checkout_configuration
msgid "Checkout Policy"
msgstr "Condition départ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__name
msgid "Checkout Time"
msgstr "Heure départ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__childs
msgid "Children"
msgstr "Enfants"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_id_master
msgid "Clients ID details"
msgstr "Détails ID Clients"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_resv_id_details
msgid "Clients ID details during reservation"
msgstr "Détails pendant réservation client"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_pad
msgid "Collaborative Pads"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__agent_comm
msgid "Commision"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__commission_percentage
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__commission_percentage
msgid "Commission %"
msgstr "Commission %"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__commission_amt
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "Commission Amount"
msgstr "Montant commission"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__commission_line_id
msgid "Commission ID"
msgstr "Identifiant Commission"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "Commission Line"
msgstr "Ligne Commission"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__commission
#: model:ir.model.fields,field_description:hotel_management.field_res_users__commission
msgid "Commission Percentage"
msgstr "Pourcentage commission"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_menucard__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__company_id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__company_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__company_id
msgid "Company"
msgstr "Société"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_informations
msgid "Company Informations"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__company_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_name
msgid "Company Name"
msgstr "Nom Société"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__complaint
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__complaint
msgid "Complaint"
msgstr "Plainte"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Configure Hotel Management"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_social_network
msgid "Configure Social Network"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__state__confirm
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_room_booking_history__state__confirm
#: model:ir.model.fields.selection,name:hotel_management.selection__issue_material_details__state__confirm
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_management.cancel_foilo_wizard_form_view
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Confirm"
msgstr "Confirmer"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__confirmation_template_id
msgid "Confirmation Email"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__agent_commission_invoice__state__confirm
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_order__state__confirm
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_tables__state__confirmed
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__state__confirmed
msgid "Confirmed"
msgstr "Confirmé"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__product_uom_category_id
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__product_uom_category_id
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__country_id
msgid "Country"
msgstr "Pays"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_country_group_ids
msgid "Country Groups"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Create Folio"
msgstr "Créer Fiche de réservation"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.view_folio_invoice_transfer_wizard
msgid "Create Invoice"
msgstr "Créer facture"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.deposit_journal_entry_wizard1
msgid "Create Journal Entry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_crm_iap_lead_website
msgid "Create Leads/Opportunities from your website's traffic"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__lead_mining_in_pipeline
msgid "Create a lead mining request directly from the opportunity pipeline."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_id_master__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__create_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__create_date
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__create_date
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__create_date
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__create_date
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__create_date
#: model:ir.model.fields,field_description:hotel_management.field_id_master__create_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__create_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__create_date
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__create_date
msgid "Created on"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_account_move__create_date
msgid "Creation Date"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_res_currency
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__currency_id
msgid "Currency"
msgstr "Devise"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__checkout_configuration__name__custom
msgid "Custom"
msgstr "Client"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__report_footer
msgid "Custom Report Footer"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__time
msgid "Custom Time"
msgstr "Heure Client"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_custom_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_custom_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__order_partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__order_partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__order_partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__partner_id
msgid "Customer"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__arrival_dept_guest_wizard__arrival_dept__arrival
msgid "Customer Arrival"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_arrival_dept_guest_wizard
#: model:ir.ui.menu,name:hotel_management.menu_action_arrival_dept_guest_wizard_id
msgid "Customer Arrival/ Departure List"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__arrival_dept_guest_wizard__arrival_dept__depart
msgid "Customer Departure"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__cname
msgid "Customer Name"
msgstr "Nom du Client"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_dhl
msgid "DHL Connector"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_arrival_dept_guest_wizard
msgid "Daily Customer Arrival/ Departure List"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__url
#: model_terms:ir.ui.view,arch_db:hotel_management.dashboard_url_form
#: model_terms:ir.ui.view,arch_db:hotel_management.dashboard_url_tree
msgid "Dashboard URL"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.dashboard_url_configuration_action
msgid "Dashboard URL Configuration"
msgstr "Configuration du Lien Tableau de Bord"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.database_configuration_action
#: model:ir.model,name:hotel_management.model_database_configuration
#: model:ir.ui.menu,name:hotel_management.database_configuration_submenu
#: model_terms:ir.ui.view,arch_db:hotel_management.database_configuration_form
#: model_terms:ir.ui.view,arch_db:hotel_management.database_configuration_tree
msgid "Database Configuration"
msgstr "Configuration Base de donnée"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__name
msgid "Database Name"
msgstr "Nom Base de donnée"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__current_date
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Date"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__date_order
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__date
msgid "Date Ordered"
msgstr "Date Commande"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__date_birth
msgid "Date of Birth"
msgstr "Date de naissance"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__user_default_rights
msgid "Default Access Rights"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__crm_alias_prefix
msgid "Default Alias Name for Leads"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__purchase_tax_id
msgid "Default Purchase Tax"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__crm_default_team_id
msgid "Default Sales Team"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__crm_default_team_id
msgid "Default Sales Team for new leads created through the Contact Us form."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__crm_default_user_id
msgid "Default Salesperson"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_default_image
msgid "Default Social Share Image"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__default_sale_order_template_id
msgid "Default Template"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_invoice_terms
msgid "Default Terms & Conditions"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__incoterm_id
msgid "Default incoterm"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_default_lang_id
msgid "Default language"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_default_lang_code
msgid "Default language code"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__crm_default_user_id
msgid "Default salesperson for new leads created through the Contact Us form."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_delivered_manual
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_delivered_manual
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_delivered
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_delivered
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_delivered
msgid "Delivered Quantity"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_tracking_lot
msgid "Delivery Packages"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Departure Time"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__deposit_recv_acc
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__deposit_recv_acc
msgid "Deposit Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__deposit_cost1
msgid "Deposit Cost"
msgstr "Cout d'arrhes"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.act_deposit_journal_entry1
msgid "Deposit Journal Entry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_deposit_journal_entry_wizard1
msgid "Deposit_journal_entry Detail Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__desc
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__name
msgid "Description"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__location_dest_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__dest_locatiion
msgid "Destination Location"
msgstr "Destination"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__digest_id
msgid "Digest Email"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__digest_emails
msgid "Digest Emails"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__via__direct
msgid "Direct"
msgstr "Directe"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__discount
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__discount
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__discount
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__discount
msgid "Discount (%)"
msgstr "Remise(%)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__display_name
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__display_name
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__display_name
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__display_name
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__display_name
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__display_name
#: model:ir.model.fields,field_description:hotel_management.field_id_master__display_name
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__display_name
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__display_name
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_arrival_dept_guest__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_report_view__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkin_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkout_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_room_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_monthly_occupency_report_view__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_roomwise_guestwise_report_view__display_name
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__display_qty_widget
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__display_qty_widget
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__display_qty_widget
msgid "Display Qty Widget"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__qr_code
msgid "Display SEPA QR code"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__display_type
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__display_type
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__display_type
msgid "Display Type"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__website_logo
msgid "Display this logo on the website."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__test
msgid "Do nat make separate Invoices"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__external_report_layout_id
msgid "Document Template"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__client_id
msgid "Document Type"
msgstr "Type Document"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__agent_commission_invoice__state__done
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__state__done
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_order__state__done
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_room_booking_history__state__done
#: model:ir.model.fields.selection,name:hotel_management.selection__issue_material_details__state__done
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__state__done
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__done
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Done"
msgstr "Effectué"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__is_downpayment
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__is_downpayment
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__agent_commission_invoice__state__draft
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__state__draft
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_order__state__draft
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_tables__state__draft
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_room_booking_history__state__draft
#: model:ir.model.fields.selection,name:hotel_management.selection__issue_material_details__state__draft
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__dummy
msgid "Dummy"
msgstr "Factice"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_reports
msgid "Dynamic Reports"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_tables__state__edit
msgid "Edit"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__template_id
msgid "Email Template"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__confirmation_template_id
msgid "Email sent to the customer once the order is paid."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auth_signup_reset_password
msgid "Enable password reset from Login page"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__lead_enrich_auto
msgid "Enrich lead automatically"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_crm_iap_lead_enrich
msgid ""
"Enrich your leads automatically with company data based on their email "
"address."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__name
msgid "Event type"
msgstr "Type Evènement"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_account_move__exchange_rate
msgid "Exchange Rate"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__recv_acc
msgid "Expense Account"
msgstr "Compte de dépenses"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__external_email_server_default
msgid "External Email Servers"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_no_variant_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_no_variant_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_facebook
msgid "Facebook Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__fail_counter
msgid "Fail Mail"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__favicon
msgid "Favicon"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_resv_id_details__gender__f
msgid "Female"
msgstr "Feminin"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "Fill The Dates"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
msgid "Fill The Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_fiscal_year
msgid "Fiscal Years"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__flag1
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__flag
msgid "Flag"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__folio_id
msgid "Folio"
msgstr "Fiche"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__folio_id
msgid "Folio Id"
msgstr "ID Fiche"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__folio_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__laundry_line_ids
msgid "Folio Ref"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_folio_invoice_transfer_wizard
msgid "Folio invoice transfer Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__folio_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__folio_id
msgid "Folio ref"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_hotel_folio_calendar_view
msgid "Folios"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Food Line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__food_lines
msgid "Food Lines"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_project_forecast
msgid "Forecasts"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__free_qty_today
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__free_qty_today
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__free_qty_today
msgid "Free Qty Today"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__date_start
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__date_start
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__start_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__date_start
msgid "From Date"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__gds_id
msgid "GDS ID"
msgstr "ID GDS"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__gender
msgid "Gender"
msgstr "Sexe"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_order_form_inherit1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_order_form_inherit_pragtech
msgid "Generate KOT/BOT"
msgstr "Générer le TDC/TDB"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_crm_iap_lead
msgid "Generate new leads based on their country, industries, size, etc."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_github
msgid "GitHub Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_analytics_key
msgid "Google Analytics Key"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_management_client_id
msgid "Google Client ID"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_management_client_secret
msgid "Google Client Secret"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_google_maps
msgid "Google Maps"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_maps_api_key
msgid "Google Maps API Key"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_google_spreadsheet
msgid "Google Spreadsheet"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__source__guest
msgid "Guest"
msgstr "Client"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__partner_name
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Guest Name"
msgstr "Nom Occupant"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_accounting_entries
msgid "Has Accounting Entries"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "History"
msgstr "Historique"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.report_hotel_folio1
msgid "Hotel Boarding Invoice"
msgstr "Facture"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio
msgid "Hotel Folio Inherit for Electricity Meter Reading"
msgstr ""

#. module: hotel_management
#: model:ir.ui.menu,name:hotel_management.hotel_management_reporting_analysis_menu
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Hotel Management"
msgstr "Gestion Hôtélière"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_hotel_reservation_user
msgid "Hotel Management / Reseravation User"
msgstr "Gestion Hôtel / Employé Réservation"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_bot_user
msgid "Hotel Management / Restaurant BOT User"
msgstr "Gestion Hôtélière / Restaurant TDB Utilisateur"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_kot_user
msgid "Hotel Management / Restaurant KOT User"
msgstr "Gestion Hôtélière / Restaurant TDC Utilisateur"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_user
msgid "Hotel Management / Restaurant User"
msgstr "Gestion Hôtel / Employé Restaurant"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_hotel_reservation_manager
msgid "Hotel Management/ Reseravation Manager"
msgstr "Gestion Hôtel / Manager Réservation"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_manager
msgid "Hotel Management/ Restaurant Manager"
msgstr "Gestion Hôtel / Manager Restaurant"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__reservation_warn
#: model:ir.model.fields,field_description:hotel_management.field_res_users__reservation_warn
msgid "Hotel Reservation"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_wizard
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "Hotel Reservation Report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_room_booking_history
msgid "Hotel Room Booking History"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_menucard
msgid "Hotel menucard Inherit "
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_reservation_wizard
msgid "Hotel reservation Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__rr_line_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__rr_line_id
msgid "Housekeeping line id"
msgstr "ID Ligne Buanderie"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__id
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__id
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__id
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__id
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__id
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__id
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__id
#: model:ir.model.fields,field_description:hotel_management.field_id_master__id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__id
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_arrival_dept_guest__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_report_view__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkin_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkout_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_room_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_monthly_occupency_report_view__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_roomwise_guestwise_report_view__id
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__id
msgid "ID"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__name
msgid "ID Card Number"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_id_master__id_code
msgid "ID Code"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "ID Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__id_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__id_line_ids
msgid "ID Line"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.id_master_action
#: model:ir.ui.menu,name:hotel_management.id_master_submenu
#: model_terms:ir.ui.view,arch_db:hotel_management.id_master_form
#: model_terms:ir.ui.view,arch_db:hotel_management.id_master_tree
msgid "ID Master"
msgstr "ID Document"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_id_master__name
msgid "ID Name"
msgstr "ID Nom"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_resv_id_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_resv_id_details_tree
msgid "Identification Details"
msgstr "Détails Identification"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_needaction
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_has_error
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__social_default_image
msgid "If set, replaces the company logo as the default social share image."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_sale_shop__shop_img
msgid "Image"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__progress
msgid "In Progress"
msgstr "En cours"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_reservation_order
msgid "Includes Hotel Reservation Order"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_order
msgid "Includes Hotel Restaurant Order"
msgstr "Inclure les commandes de Restauration"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_reservation
msgid "Includes Hotel Restaurant Reservation"
msgstr "Inclure les réservations de Restauration"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_tables
msgid "Includes Hotel Restaurant Table"
msgstr "Inclure les tables de Restauration"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_display_incoterm
msgid "Incoterms"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_instagram
msgid "Instagram Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_website_sale_stock
msgid "Installs the \"Website Delivery Information\" application"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__assign_to__intern
msgid "Internal"
msgstr "Interne"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__source__intern
msgid "Internal Observation"
msgstr "Observation Interne"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__source__internal_reservation
msgid "Internal Reservation"
msgstr "Réservation Interne"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_intrastat
msgid "Intrastat"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_sale_stock
msgid "Inventory"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__inventory_availability
msgid "Inventory Availability"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_account_move
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__commission_line
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__invoice_lines
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__invoice_lines
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__invoice_lines
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Invoice Lines"
msgstr "Lignes de facture"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_payment
msgid "Invoice Online Payment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__invoice_status
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__invoice_status
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__invoice_status
msgid "Invoice Status"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__invoiced
#: model:ir.model.fields.selection,name:hotel_management.selection__agent_commission_invoice__state__invoiced
msgid "Invoiced"
msgstr "Facturé"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account
msgid "Invoicing"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Is Checkin"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Is Checkout"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__is_mto
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__is_mto
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__is_mto
msgid "Is Mto"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__is_downpayment
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__is_downpayment
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__is_downpayment
msgid "Is a down payment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__is_expense
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__is_expense
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__is_expense
msgid "Is expense"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__is_expense
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__is_expense
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material1
msgid "Issue"
msgstr "Publication"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_issue_material
#: model:ir.actions.act_window,name:hotel_management.open_issue_material_details_tree_new1
#: model:ir.model,name:hotel_management.model_issue_material
#: model:ir.ui.menu,name:hotel_management.menu_open_issue_material_details_new1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_tree
msgid "Issue Material"
msgstr "Matériel d'émission"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_issue_material_details
msgid "Issue Material Details"
msgstr "Détails Matériel d'émission"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__name
msgid "Issue Slip"
msgstr "Bordereau d'émission"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__issuing_auth
msgid "Issuing Authority"
msgstr "Délivré par"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__product_id
msgid "Item Name"
msgstr "Nom Article"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__journal_id
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__journal_id
msgid "Journal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_menucard__product_nature__kot
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_kitchen_order_tickets__product_nature__kot
#: model:ir.model.fields.selection,name:hotel_management.selection__product_template__product_nature__kot
#: model:ir.ui.menu,name:hotel_management.menu_view_hotel_restaurant_kitchen_order_tickets_inherit
msgid "KOT"
msgstr "TDC"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_kitchen_order_tickets_tree
msgid "KOT List"
msgstr "Liste Commande Restaurant"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_view_hotel_restaurant_kitchen_order_tickets_form_tree
msgid "KOT Order List"
msgstr "Liste de Commandes TDB"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_reservation_order_kot
msgid "Kitchen Order Ticket"
msgstr "Tichet de Cuisine"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_res_order_kot
msgid "Kitchen Order Tickets"
msgstr "Tichet de Cuisine"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_auth_ldap
msgid "LDAP Authentication"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__language_ids
msgid "Languages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice____last_update
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration____last_update
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url____last_update
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration____last_update
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1____last_update
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history____last_update
#: model:ir.model.fields,field_description:hotel_management.field_id_master____last_update
#: model:ir.model.fields,field_description:hotel_management.field_issue_material____last_update
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details____last_update
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_arrival_dept_guest____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_report_view____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkin_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkout_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_room_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_monthly_occupency_report_view____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_roomwise_guestwise_report_view____last_update
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard____last_update
msgid "Last Modified on"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_id_master__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__write_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__write_date
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__write_date
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__write_date
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__write_date
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__write_date
#: model:ir.model.fields,field_description:hotel_management.field_id_master__write_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__write_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__write_date
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Laundry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__laundry_invoice_ids
msgid "Laundry Related Invoices"
msgstr "Factures Liées à la Buanderie"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Laundry Related invoices"
msgstr "Factures Liées à la Buanderie"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__predictive_lead_scoring_fields
msgid "Lead Scoring Frequency Fields"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__predictive_lead_scoring_fields_str
msgid "Lead Scoring Frequency Fields in String"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__predictive_lead_scoring_start_date
msgid "Lead Scoring Starting Date"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__predictive_lead_scoring_start_date_str
msgid "Lead Scoring Starting Date in String"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__customer_lead
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__customer_lead
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__customer_lead
msgid "Lead Time"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_use_lead
msgid "Leads"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__line_id
msgid "Line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_links
msgid "Link Trackers"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__linked_line_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__linked_line_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__linked_line_id
msgid "Linked Order Line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_linkedin
msgid "LinkedIn Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__login_password
#: model:ir.model.fields,field_description:hotel_management.field_res_users__login_password
msgid "Login Password"
msgstr "Mot de Passe de l'Indentifiant"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__currency_id
msgid "Main currency of the company."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_resv_id_details__gender__m
msgid "Male"
msgstr "Masculin"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_inter_company_rules
msgid "Manage Inter Company"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__generate_lead_from_alias
msgid "Manual Assignation of Emails"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Manual Description"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_margin
msgid "Margins"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__info_id
msgid "Material Id"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material1
msgid "Material Issue"
msgstr "Problème Matériel"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.max_hotel_room_report
msgid "Max Room Detail"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__reservation_msg
#: model:ir.model.fields,field_description:hotel_management.field_res_users__reservation_msg
msgid "Message for Hotel Reservation"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_ids
msgid "Messages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_delivered_method
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_delivered_method
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_monthly_occupancy_wizard_report
#: model:ir.actions.report,name:hotel_management.monthly_occupency_qweb
#: model:ir.ui.menu,name:hotel_management.menu_action_monthly_occupancy_report
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Monthly Occupancy Report"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_multi_warehouses
msgid "Multi-Warehouses"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_multi_website
msgid "Multi-website"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__product_pricelist_setting
msgid ""
"Multiple prices: Pricelists with fixed price rules by product,\n"
"Advanced rules: enables advanced price rules for pricelists."
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "My Button"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_shop_form_inherit
msgid "Name"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__name
msgid "Name Inv Lines"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__name_short
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__name_short
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__name_short
msgid "Name Short"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Net Amt("
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.cancel_foilo_wizard_form_view
msgid "No"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__res_partner__reservation_warn__no-message
msgid "No Message"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__note
msgid "Note"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__number_of_days
msgid "Number Of Days"
msgstr "Nombre de jours"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__number_of_rooms
msgid "Number Of Rooms"
msgstr "Nombre de chambres	"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__active_user_count
msgid "Number of Active Users"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_count
msgid "Number of Companies"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__language_count
msgid "Number of Languages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__customer_lead
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__customer_lead
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__cart_abandoned_delay
msgid "Number of hours after which the cart is considered abandoned."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_language_count
msgid "Number of languages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__sale_order_option_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__sale_order_option_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__sale_order_option_ids
msgid "Optional Products Lines"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__option_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__option_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__option_line_ids
msgid "Options Linked"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_restaurant_order__state__order
#: model_terms:ir.ui.view,arch_db:hotel_management.view_restaurant_order_filter_inherit
msgid "Order Done"
msgstr "Commande effectuée"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_kitchen_order_tickets_form_inheritance
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_reservation_form_inherit1
msgid "Order List"
msgstr "Liste de commande"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__order_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__order_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__order_id
msgid "Order Reference"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__state
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__state
msgid "Order State"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__state
msgid "Order Status"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_packaging
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_packaging
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_packaging
msgid "Package"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "Paid"
msgstr "Payé"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__paperformat_id
msgid "Paper format"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_res_partner
msgid "Partner"
msgstr "Partenaire"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__partner_id
msgid "Partner Name"
msgstr "Nom Partenaire"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__password
msgid "Password"
msgstr "Mot de passe"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.advance_payment_wizard1
msgid "Payment"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__payment_date
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__payment_date
msgid "Payment Date"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__account_move_ids
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__account_move_ids
msgid "Payment Details"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Percentage"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_plaid
msgid "Plaid Connector"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material1
msgid "Plz choose the Warehouse Location"
msgstr "Veuillez Choisir l'entrepot"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__access_url
msgid "Portal Access URL"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__price
msgid "Price"
msgstr "Prix"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_reduce
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_reduce
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_reduce
msgid "Price Reduce"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_reduce_taxexcl
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_reduce_taxexcl
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_reduce_taxinc
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_reduce_taxinc
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__pricelist_id
msgid "Pricelist"
msgstr "Tarifs"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_product_pricelist
msgid "Pricelists"
msgstr "Tarifs"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__product_pricelist_setting
msgid "Pricelists Method"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_is_print
msgid "Print"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__snailmail_color
msgid "Print In Color"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "Print Report"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_id
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__product_product_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__product_id
msgid "Product"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_product_configurator
msgid "Product Configurator"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__product_line_ids
msgid "Product Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__product_id
msgid "Product ID"
msgstr "ID Produit"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__name
msgid "Product Name"
msgstr "Nom Produit"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_h_activity__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_menucard__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_amenities__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_services__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_product_product__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_product_template__product_nature
msgid "Product Nature"
msgstr "Nature du Produit"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_packaging
msgid "Product Packagings"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__repair_ids
msgid "Product Replacement info"
msgstr "Info Produit de remplacement"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Product Requirement"
msgstr "Exigence Produit"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_product_template
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_template_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_template_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_template_id
msgid "Product Template"
msgstr "Modèle d'article"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_type
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_type
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_type
msgid "Product Type"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__product_line_id
msgid "Product line id"
msgstr "ID Ligne Produit"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__qty
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__qty
msgid "Qty"
msgstr "Qté"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_available_today
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_available_today
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_available_today
msgid "Qty Available Today"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_to_deliver
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_to_deliver
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_quality_control
msgid "Quality"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_uom_qty
msgid "Quantity"
msgstr "Quantité"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__draft
msgid "Quotation"
msgstr "Devis"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_quotation_builder
msgid "Quotation Builder"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__sent
msgid "Quotation Sent"
msgstr "Devis envoyé"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_sale_order_template
msgid "Quotation Templates"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Rate("
msgstr "Taux("

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__activity__repair
msgid "Repair"
msgstr "Réparation"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Repair / Repalcement Info"
msgstr "Réparation/Info Remplacement"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__rr_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__rr_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__rr_line_ids
msgid "Repair / Replacement Info"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_tree
msgid "Repair Housekeeping"
msgstr "Réparation Menagère"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__activity__replaced
msgid "Replace"
msgstr "Remplacer"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__arrival_dept
msgid "Report For"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "Report Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__name
msgid "Req No"
msgstr "Numéro Demande"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__request_id
msgid "Request Number"
msgstr "Numéro Demande"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_rr_housekeeping_form_tree_new1
#: model:ir.ui.menu,name:hotel_management.menu_open_rr_housekeeping_form_tree_new1
msgid "Request for Repair / Replacement"
msgstr "Demande Réparation / Remplacement"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__requested_by
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__requested_by_partner
msgid "Requested By"
msgstr "Demandé par"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_hotel_reservation_form_tree11
#: model:ir.model,name:hotel_management.model_hotel_reservation
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_procurement_jit
#: model:ir.ui.menu,name:hotel_management.main_menu_hotel_reservation_tree_all
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_graph
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_tree1
msgid "Reservation"
msgstr "Réservation"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_reservation_details_report
msgid "Reservation Detail"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__reservation_id
msgid "Reservation Id"
msgstr "ID Réservation"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_reservation_line
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__reservation_line
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_from
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_tree
msgid "Reservation Line"
msgstr "Ligne réservation"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "Reservation List"
msgstr "Liste de Réservations"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__reservation_no
msgid "Reservation No"
msgstr "Numéro Réservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__reservation_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__reservation_id
msgid "Reservation Ref"
msgstr "Ref Réservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__total_tax
msgid "Reservation Tax"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_procurement_jit
msgid ""
"Reserving products manually in delivery orders or by running the scheduler "
"is advised to better manage priorities in case of long customer lead times "
"or/and frequent stock-outs."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Restaurant"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Restaurant Lines"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__room_no
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__history_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__room_no
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Room No"
msgstr "Numéro chambre"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__room_number
msgid "Room Number"
msgstr "Numéro chambre"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Room Rate("
msgstr "Taux Chambre"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room__room_folio_ids
msgid "Room Rental History"
msgstr "Historique Location Chambre"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__categ_id
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_from
msgid "Room Type"
msgstr "Type chambre"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_room_report
msgid "Room Usage Report"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "Room Used Maximum"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_room_guestwise_wizard_report
#: model:ir.ui.menu,name:hotel_management.menu_action_room_guestwise_report
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Room and Guestwise Report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_room_guestwise_wizard
msgid "Room wise Guest wise Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.roomwise_guestwise_qweb
msgid "Roomwise and Guestwise"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__route_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__route_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__route_id
msgid "Route"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__stock_sms_confirmation_template_id
msgid "SMS Template"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__stock_move_sms_validation
msgid "SMS Validation with stock move"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__stock_sms_confirmation_template_id
msgid "SMS sent to the customer once the order is done."
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_sale_order
msgid "Sale Order Inherit "
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.sale_view_order_tree1
msgid "Sales Orders"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__salesteam_id
msgid "Sales Team"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__salesman_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__salesman_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__salesman_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__salesperson_id
msgid "Salesperson"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__scheduled_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__scheduled_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__scheduled_date
msgid "Scheduled Date"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_search
msgid "Search Booking History"
msgstr "Recherher Historique des Réservation"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__security_lead
msgid "Security Lead Time"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__access_token
msgid "Security Token"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_is_email
msgid "Send Email"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_is_snailmail
msgid "Send by Post"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__sequence
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__sequence
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__sequence
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__name
msgid "Sequence"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Serial No."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__service_cost
msgid "Service Cost"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Set to Draft"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery
msgid "Shipping Costs"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__sale_delivery_settings
msgid "Shipping Management"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_h_activity__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_menucard__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_amenities__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_services__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_product_product__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_product_template__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__shop_id
msgid "Shop"
msgstr "Boutique"

#. module: hotel_management
#: model:ir.model.constraint,message:hotel_management.constraint_checkout_configuration_shop_id_uniq
msgid "Shop must be unique !"
msgstr "Boutique doit être unique !"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__show_effect
msgid "Show Effect"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes (B2C)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_show_line_subtotals_tax_excluded
msgid "Show line subtotals without taxes (B2B)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_snailmail_account
msgid "Snailmail"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__source
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__source
msgid "Source"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__location_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__source_locatiion
msgid "Source Location"
msgstr "Lieu Source"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__source_origin
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__source_origin
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__source_origin
msgid "Source Origin"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_product_email_template
msgid "Specific Email"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__specific_user_account
msgid "Specific User Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__states
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__state
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__state
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__state
msgid "State"
msgstr "Etat"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "States"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__state
msgid "Status"
msgstr "Statut"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__move_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__move_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__move_ids
msgid "Stock Moves"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_stock_multi_locations
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__sub_total1
msgid "Sub Total"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "Sub Total :"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_subtask_project
msgid "Sub-tasks"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__amount_subtotal
msgid "SubTotal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__amount_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__price_subtotal
msgid "Subtotal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__name
msgid "Table number"
msgstr "Numéro Table"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Tariff"
msgstr "Tarif"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__amount_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__amount_tax
msgid "Tax"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "Tax :"
msgstr "Taxe"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_from
msgid "Tax On Product"
msgstr "Taxe sur Produit"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__amount_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__taxes_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__amount_tax
msgid "Taxes"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_form_enable_metadata
msgid "Technical data on contact form"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__display_type
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__display_type
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__chart_template_id
msgid "Template"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_terms
msgid "Terms & Conditions"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__currency_exchange_journal_id
msgid ""
"The accounting journal where automatic exchange differences will be "
"registered"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio__amount_untaxed
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__total_tax
#: model:ir.model.fields,help:hotel_management.field_sale_order__amount_untaxed
msgid "The amount without tax."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"               This is useful if you install accounting after having used invoicing for some time and\n"
"               don't want to reconcile all the past payments with bank statements."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio__amount_tax
#: model:ir.model.fields,help:hotel_management.field_sale_order__amount_tax
msgid "The tax amount."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_pos_adyen
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_pos_mercury
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__rr_housekeeping__assign_to__third_party
msgid "Third Party"
msgstr "Tiers"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_account_batch_payment
msgid ""
"This allows you grouping payments into a single batch and eases the reconciliation process.\n"
"-This installs the account_batch_payment module."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_sale_shop__shop_img
msgid "This field holds the image for this shop, limited to 1024x1024px"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__source__through_gds
msgid "Through GDS"
msgstr "Via GDS"

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__hotel_reservation__source__through_web
msgid "Through Web"
msgstr "Via internet"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__date_end
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__end_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__date_end
msgid "To Date"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields.selection,name:hotel_management.selection__sale_order__state__sale
msgid "To Invoice"
msgstr "À facturer"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__total_amt
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__amount_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__amount_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__amount_total
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__amount_total
msgid "Total"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "Total :"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__total_advance
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__total_advance
msgid "Total Advance Payment"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Total Amt("
msgstr "Total mnt("

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__tour_cost
msgid "Total Cost"
msgstr "Total de prix de revient"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Gross"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Laundry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__remaining_amt
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__remaining_amt
msgid "Total Remaining Amount"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Rent"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__total_cost1
msgid "Total Reservation cost"
msgstr "Total Coût de réservation"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Rest"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Service"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_tax
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Tax"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__trans_folio_id
msgid "Transfer Folio Ref"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_folio_invoice_transfer_wizard
#: model_terms:ir.ui.view,arch_db:hotel_management.view_folio_invoice_transfer_wizard
msgid "Transfer Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__transfer_invoice_ids
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__transfer_invoice_ids
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Transfer Invoice Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_base_gengo
msgid "Translate Your Website with Gengo"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Transport"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__transport_line_ids
msgid "Transport Line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__transport_invoice_ids
msgid "Transport Related Invoices"
msgstr "Factures Liées au Transport"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Transport Related invoices"
msgstr "Factures Liées au Transport"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_twitter
msgid "Twitter Account"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__uom
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__uom
msgid "UOM"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_unit
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_unit
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_unit
msgid "Unit Price"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_uom
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_uom
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_uom
msgid "Unit of Measure"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_uom
msgid "Units of Measure"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_unread
msgid "Unread Messages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__amount_untaxed
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__untaxed_amt
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Montant HT"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__untaxed_amount_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__untaxed_amount_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr ""

#. module: hotel_management
#: model:ir.actions.server,name:hotel_management.update_reservation_room
msgid "Update  Reservation  "
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_sale
msgid "Update history"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_project_rating
msgid "Use Rating on Project"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_batch_payment
msgid "Use batch payments"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__website_country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__user_name
msgid "User Name"
msgstr "Nom utilisateur"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__valid_from
msgid "Valid From"
msgstr "Applicable à partir du"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__valid_to
msgid "Valid To"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_product_variant
msgid "Variants"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__via
msgid "Via"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__virtual_available_at_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__virtual_available_at_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.deposit_journal_entry_wizard1
msgid "Visa Journal Entry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__waitername1
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__waiter_name1
msgid "Waiter Name"
msgstr "Nom du Serveur"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__warehouse_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__warehouse_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__warehouse_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_warehouse_id
msgid "Warehouse"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__warning_stock
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__warning_stock
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__warning_stock
#: model:ir.model.fields.selection,name:hotel_management.selection__res_partner__reservation_warn__warning
msgid "Warning"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_warning_account
msgid "Warnings in Invoices"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_company_id
msgid "Website Company"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_domain
msgid "Website Domain"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_logo
msgid "Website Logo"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_name
msgid "Website Name"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation_line__room_number
msgid "Will list out all the rooms that belong to selected shop."
msgstr "Donnera la liste de toutes les pièces de la boutique sélectionné."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_order__partner_id
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_reservation__cname
msgid "Will show customer name corresponding to selected room no."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_reservation__room_no
msgid ""
"Will show list of currently occupied room no that belongs to selected shop.	"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_order__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_reservation__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user."
msgstr "Affichera la liste des boutiques de l'utilisateur connecté."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_checkout_configuration__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assign a shop to configure shop-wise check out policy."
msgstr "Affichera la liste des boutiques de l'utilisateur connecté. \n"
" - Assigner une boutique pour configurer les conditions de départ"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_h_activity__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_menucard__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_room__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_room_amenities__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_services__shop_id
#: model:ir.model.fields,help:hotel_management.field_product_product__shop_id
#: model:ir.model.fields,help:hotel_management.field_product_template__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assigning a shop will make product exclusive for selected shop."
msgstr "Affichera la liste des boutiques  de l'utilisateur connecté. \n"
" -Assigner une boutique lui rendra l'exclusivité du rpoduit."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_tables__shop_id
msgid ""
"Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assigning shop name to which this table no belongs to."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_workorder
msgid "Work Orders"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.cancel_foilo_wizard_form_view
msgid "Yes"
msgstr "Oui"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__website_form_enable_metadata
msgid "You can choose to log technical data like IP, User Agent ,..."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_youtube
msgid "Youtube Account"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_arrival_dept_guest
msgid "arrival dept guest"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_cancel_on_datecheck
msgid "cancel folio"
msgstr "Annuler Fiche"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "currency exchange rate :"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_dashboard_url
msgid "dashboard url"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_sale_delivery
msgid "eCommerce Shipping Costs"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__folio_id
msgid "folio_id"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__transport_line_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__food_line_id
msgid "food_line_id"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_food_line
msgid "hotel Food line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_service_line
msgid "hotel Service line"
msgstr "Ligne de service"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio_laundry_line
msgid "hotel folio laundry line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio_transport_line
msgid "hotel folio transport line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio_line
msgid "hotel folio1 room line"
msgstr "Fiche1 Chambre"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_management_config_settings
msgid "hotel management config settings"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_report_view
msgid "hotel report view"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_room_report
msgid "hotel reservation room report"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__laundry_line_id
msgid "laundry ref"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_monthly_occupency_report_view
msgid "monthly 1occupency report view"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_monthly_occupancy_wizard
msgid "monthly occupancy wizard"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "or"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_product_product_line
msgid "product product line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__previous_qty
msgid "quantities"
msgstr "Quantité"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__product_qty
msgid "quantity"
msgstr "Quantité"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_checkin_report
msgid "report hotel_management hotel_reservation_checkin_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_checkout_report
msgid "report hotel_management hotel_reservation_checkout_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_report
msgid "report hotel_management hotel_reservation_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_room
msgid "room Inherit "
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__category_id
msgid "room category"
msgstr "Catégorie Chambre"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_roomwise_guestwise_report_view
msgid "roomwise guestwise report view"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping_line
msgid "rr housekeeping line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping_line_wizard
msgid "rr_housekeeping_line_wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping_wizard
msgid "rr_housekeeping_wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_sale_shop
msgid "sale shop used for territory name"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping
msgid "test"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__total
msgid "total"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_id
msgid "website"
msgstr ""
