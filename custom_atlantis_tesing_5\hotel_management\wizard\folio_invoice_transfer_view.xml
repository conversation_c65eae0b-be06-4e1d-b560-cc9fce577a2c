<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
		<!--Visa Journal Entry Wizard form view  -->
		<record model="ir.ui.view" id="view_folio_invoice_transfer_wizard">
			<field name="name">folio.invoice.transfer.wizard_form</field>
			<field name="model">folio.invoice.transfer.wizard</field>
			<field name="arch" type="xml">
				<form string="Transfer Invoice" version="7.0">
					<group colspan="4" col="4">
					<field name="folio_id" readonly ="1"/>
					<field name="trans_folio_id" invisible="1"/>
					</group>
					<footer>
						<button icon="fa-times" special="cancel" string="Cancel" />
						<button string="Create Invoice" name="transfer_process" type="object"/>
					</footer>
				</form>
			</field>
		</record>

		<record id="action_folio_invoice_transfer_wizard" model="ir.actions.act_window">
			<field name="name">Transfer Invoice</field>
			<field name="res_model">folio.invoice.transfer.wizard</field>
			<!--<field name="src_model">hotel.folio</field>-->
			<field name="type">ir.actions.act_window</field>
			<!--<field name="view_type">form</field>-->
			<field name="view_mode">form</field>
			<!-- <field name="auto_refresh" eval="1" /> -->
			<field name="target">new</field>
			<field name="context">{'ids':active_id}</field>
		</record>
	</data>
</odoo>