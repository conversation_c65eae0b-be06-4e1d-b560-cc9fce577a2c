<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_issue_material1" model="ir.ui.view">
            <field name="name">Issue Material</field>
            <field name="model">issue.material</field>
            <field name="arch" type="xml">
                <form string="Material Issue">
                    <separator colspan="4" string="Plz choose the Warehouse Location"/>
                    <field name="location_id"  required="0"/>
                    <field name="location_dest_id" domain="[('usage','=','view')]"/>
                    <separator colspan="4"/>
                    <group colspan="2" col="4">
                        <button name="check_stock" string="Issue" type="object" icon="terp-stock_effects-object-colorize"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="action_issue_material" model="ir.actions.act_window">
            <field name="name">Issue Material</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">issue.material</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_issue_material1"/>
            <field name="context">{}</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
