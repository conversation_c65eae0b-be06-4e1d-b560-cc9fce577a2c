#overlay{
	position:absolute;
	top:0;
	left:0;
	z-index:190;
	width:100%;
	height:auto;
	background-color:#151410;
}

#lightbox{
	position:absolute;
	top:20px;
	left:0;
	width:100%;
	z-index:9999999999;
	text-align:center;
	color:#151410;
	line-height:0;
}

#lightbox a, #lightbox a:hover {
	border-bottom:none;
	color:#151410;
	text-decoration:underline;
}

#lightbox a img{ border:none; }

#outerImageContainer{
	width:auto;
	height:auto; /* without this line error in IE8 detected */
	margin:0 auto;
	position:relative;
}

#lightboxImage{
	width:100%;
	height:100%;
}

#imageContainerMain{
	margin:0 auto;
	overflow:visible;
	position:relative;
	font-size:0;/* ie fix - big info bar*/
}

#imageContainer{
	width:10px;
	height:10px;
	margin:0 auto;
	overflow:hidden;
	background-color:#fff;
	position:relative;
	font-size:0;/* ie fix - big info bar*/
}

#loading{
	position:absolute;
	top:40%;
	left:0%;
	height:25%;
	width:100%;
	text-align:center;
	font-size:10px;
	z-index:1;
}
#loadingLink {
	display:block;
	margin:0 auto;
	padding:0;
	width:32px;
	height:32px;
	background:url(../images/loading.gif) center center no-repeat;
	text-indent:-9999px;
}
#hoverNav{
	position:absolute;
	top:0;
	left:0;
	height:100%;
	width:100%;
	z-index:10;
}
#imageContainer>#hoverNav{ left:0;}
#prevLinkImg, #nextLinkImg{
	top:0;
	width:63px;
	height:100%;
	position:absolute;
	z-index:20;
	outline-style:none;
	display:block;
	text-indent:-9999px;
	}
* html #prevLinkImg,* html #nextLinkImg{
	background-image:url(data:image/gif;base64,AAAA); /* Trick IE into showing hover */
}
#prevLinkImg { left: 0; }
#nextLinkImg { right: 0; }
#prevLinkImg:hover,#prevLinkImg.hover, #prevLinkImg:visited:hover { 
	background:url(../images/prev.png) left 14%  center no-repeat; 
}
#nextLinkImg:hover,#nextLinkImg.hover, #nextLinkImg:visited:hover { 
	background:url(../images/next.png) right 14%  center no-repeat; 
}


#imageDataContainer{
	font:10px Verdana, Helvetica, sans-serif;
	background-color:#fff;
	width:100%	;
}

#imageData{
	overflow:hidden;
	width:100%;
}
#imageDetails{ width:70%; float:left; text-align:left; 	padding:10px 10px 0 10px;}
#caption{ font-weight:bold; display:block;}
#numberDisplay{ display:block; float:left; padding-right:10px; margin-top:3px;}
#detailsNav{display:block; float:left;   padding:0; }	
#prevLinkDetails, #nextLinkDetails, #slideShowControl{ background-repeat:no-repeat; outline-style:none; display:block; float:left;}
#prevLinkDetails { margin:3px; width:16px;height:16px; background:url(../images/prevlabel.gif) left center;}
#nextLinkDetails { margin:3px; width:16px;height:16px; background:url(../images/nextlabel.gif) right center;}

#slideShowControl.started{
	background-image:url(../images/pause.gif);
}

#slideShowControl{
	display:block;
	width:16px; 
	height:16px; 
	float:left;
	margin:3px;
	background:url(../images/start.gif);
	background-position:center center;
	background-repeat:no-repeat;
	}

#close{
	padding:10px 10px 0 0;
	float:right;
}
#closeLink {
	display:block; outline-style:none; margin:0; padding:0; text-decoration:none; 
	width:66px;
	height:32px;
	background:url(../images/close.gif) no-repeat;
}	
		
	
.clearfix:after { content:"."; display:block; height:0; clear:both; visibility:hidden;}
* html>body .clearfix {display:inline-block; width:100%;}

* html .clearfix {
	/* Hides from IE-mac \*/
	height:1%;
	/* End hide from IE-mac */
}	
	

#outerImageFrame{
	border:solid 1px red;
	height:100%;
	width:100%;
	position:absolute;
}


#outerImageContainer{
	overflow:visible;
}
#outerImageContainer td{
	text-align:center;
	padding:0;
}

#lightboxFrameBody{
	background-color:#fff;
	border:solid 1px #fff; /* hack for opera table small cell width */
}

#outerImageContainer td, #outerImageContainer tr{
	font-size:0;
	border:0 none;
}

/* frame style */
#outerImageContainer td.tl, #outerImageContainer td.br{
	height:10px;
	width:10px;
}

#outerImageContainer td.tl{
	background-image:url(../images/borderCorners.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderCorners.png', sizingMethod='scale');
}
#outerImageContainer td.tc{
	background-image:url(../images/borderHoriz.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderHoriz.png', sizingMethod='scale');
}
#outerImageContainer td.ml{
	background-image:url(../images/borderVert.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderVert.png', sizingMethod='scale');
}
#outerImageContainer td.mr{
	background-image:url(../images/borderVert.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderVert.png', sizingMethod='scale');
}
#outerImageContainer td.bc{
	background-image:url(../images/borderHoriz.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderHoriz.png', sizingMethod='scale');
}
.vlb{display:none;}
#outerImageContainer td.tr{
	background-image:url(../images/borderCorners.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderCorners.png', sizingMethod='scale');
}
#outerImageContainer td.bl{
	background-image:url(../images/borderCorners.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderCorners.png', sizingMethod='scale');
}
#outerImageContainer td.br{
	background-image:url(../images/borderCorners.png);
	_background-image:none;
	_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='engine/images/borderCorners.png', sizingMethod='scale');
}




#vlightbox1 {
    width: 330px;
}
#vlightbox1 .vlightbox1 {
    display: inline-block;
    font-family: Trebuchet,Tahoma,Arial,sans-serif;
    font-size: 11px;
    font-weight: normal;
    margin: 5px;
    opacity: 0.87;
    position: relative;
    text-align: center;
    text-decoration: none;
    vertical-align: top;
    width: 100px;
}
#vlightbox1 .vlightbox1 a {
    margin: 0;
}
#vlightbox1 .vlightbox1:hover, #vlightbox1 .vlightbox1 a:hover {
    opacity: 1;
}
#vlightbox1 .vlightbox1 img {
    border: medium none;
    display: block;
    margin: 0;
	max-width:322px;
}
#vlightbox1 .vlightbox1 div {
    display: none;
}



#vlightbox1 .vlightbox2 img {
    width: 106px !important;
}