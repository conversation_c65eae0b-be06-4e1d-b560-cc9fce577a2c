/* line 7, ../sass/lightbox.sass */
body:after {
  content: url(website/images/close.png) url(website/images/loading.gif) url(website/images/prev.png) url(website/images/next.png);
  display: none;
}

/* line 11, ../sass/lightbox.sass */
.lightboxOverlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: black;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  opacity: 0.8;
  display: none;
}

/* line 20, ../sass/lightbox.sass */
.lightbox {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 10000;
  text-align: center;
  line-height: 0;
  font-weight: normal;
}
/* line 28, ../sass/lightbox.sass */
.lightbox .lb-image {
  display: block;
  height: auto;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
}
/* line 32, ../sass/lightbox.sass */
.lightbox a img {
  border: none;
}

/* line 35, ../sass/lightbox.sass */
.lb-outerContainer {
  position: relative;
  background-color: white;
  *zoom: 1;
  width: 250px;
  height: 250px;
  margin: 0 auto;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}
/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.lb-outerContainer:after {
  content: "";
  display: table;
  clear: both;
}

/* line 44, ../sass/lightbox.sass */
.lb-container {
  padding: 4px;
}

/* line 47, ../sass/lightbox.sass */
.lb-loader {
  position: absolute;
  top: 43%;
  left: 0%;
  height: 25%;
  width: 100%;
  text-align: center;
  line-height: 0;
}

/* line 56, ../sass/lightbox.sass */
.lb-cancel {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto;
  background: url(../images/loading.gif) no-repeat;
}

/* line 63, ../sass/lightbox.sass */
.lb-nav {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 10;
}

/* line 71, ../sass/lightbox.sass */
.lb-container > .nav {
  left: 0;
}

/* line 74, ../sass/lightbox.sass */
.lb-nav a {
  outline: none;
}

/* line 77, ../sass/lightbox.sass */
.lb-prev, .lb-next {
  width: 49%;
  height: 100%;
  cursor: pointer;
  /* Trick IE into showing hover */
  display: block;
}

/* line 84, ../sass/lightbox.sass */
.lb-prev {
  left: 0;
  float: left;
}
/* line 87, ../sass/lightbox.sass */
.lb-prev:hover {
  background:url(../../images/prev.png) left 48% no-repeat;
}

/* line 90, ../sass/lightbox.sass */
.lb-next {
  right: 0;
  float: right;
}
/* line 93, ../sass/lightbox.sass */
.lb-next:hover {
 background:url(../../images/next.png)  right 48% no-repeat;
}

/* line 96, ../sass/lightbox.sass */
.lb-dataContainer {
  margin: 0 auto;
  padding-top: 5px;
  *zoom: 1;
  width: 100%;
  -moz-border-radius-bottomleft: 4px;
  -webkit-border-bottom-left-radius: 4px;
  border-bottom-left-radius: 4px;
  -moz-border-radius-bottomright: 4px;
  -webkit-border-bottom-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.lb-dataContainer:after {
  content: "";
  display: table;
  clear: both;
}

/* line 103, ../sass/lightbox.sass */
.lb-data {
  padding: 0 4px;
  color: #bbbbbb;
}
/* line 106, ../sass/lightbox.sass */
.lb-data .lb-details {
  width: 85%;
  float: left;
  text-align: left;
  line-height: 1.1em;
}
/* line 111, ../sass/lightbox.sass */
.lb-data .lb-caption {
  font-size: 13px;
  font-weight: bold;
  line-height: 1em;
}
/* line 115, ../sass/lightbox.sass */
.lb-data .lb-number {
  display: block;
  clear: left;
  padding-bottom: 1em;
  font-size: 12px;
  color: #999999;
}
/* line 121, ../sass/lightbox.sass */
.lb-data .lb-close {
  display: block;
  float: right;
  width: 30px;
  height: 30px; background:url(../../images/close.png)  top right no-repeat;
  text-align: right;
  outline: none;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=70);
  opacity: 0.7;
}
/* line 130, ../sass/lightbox.sass */
.lb-data .lb-close:hover {
  cursor: pointer;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
  opacity: 1;
}


.imagescoloumn {width:100%; height: 150px; float: left; margin:auto; }

.roomspercoloumn {width:100%; height:auto; float:left; margin:auto; padding:2px; margin-top:10px; border-bottom: dashed; border-bottom-color: #CCCCCC; border-bottom-width:1px;  } 

.book_containt {clear:both; width:100%; boder-bottom:1px solid #ccc; padding:2px; }

.book_fullcol {width:100%; border: 1px solid #C8C8D3; padding:8px; float:left; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }  

.book_fullcol label {width:40%; border-right:solid 1px #ccc;  }  

.book_fullcol img { margin-top:15%;}  

.book_fullcol h2 {font-size: 22px;
font-weight: 600;
color: #7C7BAD; }

.bookroom_headingbg { background-color:#7C7BAD; padding:5px; color:#FFFFFF; font-size:14px; }