
/* line 22, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
html {
  line-height: 1;
}

/* line 24, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
ol, ul {
  list-style: none;
}

/* line 26, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* line 28, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
caption, th, td {
  text-align: left;
  font-weight: normal;
  vertical-align: middle;
}

/* line 30, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
q, blockquote {
  quotes: none;
}
/* line 103, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
q:before, q:after, blockquote:before, blockquote:after {
  content: "";
  content: none;
}

/* line 32, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
a img {
  border: none;
}

/* line 116, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/reset/_utilities.scss */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary {
  display: block;
}

/* Typography
 *----------------------------------------------- */


/* line 17, ../sass/screen.sass */
h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1.2em;
  color: #fdf485;
}

/* line 26, ../sass/screen.sass */
h1 {
  font-size: 72px;
  line-height: 1em;
}

/* line 30, ../sass/screen.sass */
h2 {
  font-size: 48px;
  line-height: 1.2em;
  margin-bottom: 0.3em;
}

/* line 35, ../sass/screen.sass */
h3 {
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: #e5d404;
  margin-bottom: 0.3em;
}

/* line 42, ../sass/screen.sass */
h4 {
  font-size: 18px;
}

/* line 45, ../sass/screen.sass */
p {
  line-height: 1.4em;
  margin-bottom: 1em;
}

/* line 49, ../sass/screen.sass */
ol {
  list-style-type: decimal;
}

/* line 52, ../sass/screen.sass */
ul, ol {
  margin: 0 0 1.25em 0;
}

/* line 55, ../sass/screen.sass */

/* line 60, ../sass/screen.sass */
li.last-list-item {
  border-bottom: none;
}

/* line 63, ../sass/screen.sass */
dt {
  font-weight: bold;
}

/* line 66, ../sass/screen.sass */
dd {
  margin-bottom: 1.625em;
}

/* line 69, ../sass/screen.sass */
strong {
  font-weight: bold;
}

/* line 72, ../sass/screen.sass */
i {
  font-style: italic;
}

/* line 75, ../sass/screen.sass */
pre {
  padding: 10px;
  margin-top: 5px;
  margin-bottom: 10px;
  background-color: #2b2b2b;
  font: 13px "Andale Mono", "DejaVu Sans Mono", monospace;
  line-height: 1.5em;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  overflow-x: auto;
}

/* line 85, ../sass/screen.sass */
code, kbd {
  padding: 4px;
  color: #ac8053;
  background-color: #2b2b2b;
  font: 13px "Andale Mono", "DejaVu Sans Mono", monospace;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}

/* line 92, ../sass/screen.sass */
code {
  position: relative;
  top: -1px;
}

/* line 96, ../sass/screen.sass */
pre code {
  top: 0;
  padding: 0;
  background: transparent;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  font-size: 13px;
}

/* line 103, ../sass/screen.sass */
a {
  color: #00bfa8;
  text-decoration: none;
}
/* line 106, ../sass/screen.sass */
a:hover {
  color: #59ffeb;
}

/* line 109, ../sass/screen.sass */
::-moz-selection,
::selection {
  background: #ff8000;
  color: white;
}

/* line 114, ../sass/screen.sass */
.sub-point {
  display: block;
  font-size: 14px;
}
/* line 117, ../sass/screen.sass */
.sub-point code {
  font-size: 12px;
  padding: 2px;
}

/* -- Layout ------------------------------------------------------------------ */
/* line 124, ../sass/screen.sass */


/* line 128, ../sass/screen.sass */
.wrapper {
  max-width: 740px;
  margin: 0 auto;
}

/* line 132, ../sass/screen.sass */
.section {
  padding: 0 0 40px 0;
  margin-bottom: 40px;
  *zoom: 1;
  border-bottom: 4px solid #373737;
}
/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.section:after {
  content: "";
  display: table;
  clear: both;
}
/* line 137, ../sass/screen.sass */
.section.last {
  border-bottom: none;
}

/* line 140, ../sass/screen.sass */
.section-header {
  text-align: center;
}

/* line 143, ../sass/screen.sass */
.section-subheader {
  margin-top: -0.6em;
  margin-bottom: 1em;
  text-align: center;
  font-size: 24px;
}

/* line 149, ../sass/screen.sass */
.row {
  *zoom: 1;
}
/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.row:after {
  content: "";
  display: table;
  clear: both;
}

/* -- Sections ------------------------------------------------------------------ */
/* -- Intro -- */
/* line 156, ../sass/screen.sass */
.intro-section {
  text-align: center;
}

/* line 159, ../sass/screen.sass */
.logo {
  color: white;
  margin-bottom: 0.05em;
}
/* line 162, ../sass/screen.sass */
.logo .version {
  color: #fdf485;
}

/* line 165, ../sass/screen.sass */
.author {
  margin-top: -9px;
  padding-left: 23px;
  line-height: 1.2em;
}

/* line 170, ../sass/screen.sass */
.author-links {
  font-size: 16px;
}

/* line 173, ../sass/screen.sass */
.lead {
  font-size: 22px;
}

/* -- Examples -- */
/* line 177, ../sass/screen.sass */
.examples-section {
  text-align: center;
}

/* line 180, ../sass/screen.sass */
.image-row {
  *zoom: 1;
  margin-bottom: 20px;
  margin-left:10px;
}
/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.image-row:after {
  content: "";
  display: table;
  clear: both;
}

/* line 184, ../sass/screen.sass */
.example-image-link {
  display: inline-block;
  margin: 0 10px 10px 10px;
  line-height: 0;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  
  -webkit-transition: all 0.1s ease-out;
  -moz-transition: all 0.1s ease-out;
  -o-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
  width:190px;
}

.example-image-link img {width:100%; }
/* line 191, ../sass/screen.sass */
.example-image-link:hover {
 
}


.examplenew-image-link {
  display: inline-block;
  
  line-height: 0;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  border: 1px solid #5e5e5e;
  -webkit-transition: all 0.1s ease-out;
  -moz-transition: all 0.1s ease-out;
  -o-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
  
}
/* line 191, ../sass/screen.sass */
.examplenew-image-link:hover {
  border: 1px solid #00bfa8;
}






















/* line 194, ../sass/screen.sass */
.example-image {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
}

/* -- Download -- */
/* line 199, ../sass/screen.sass */
.download-section {
  text-align: center;
}

/* line 202, ../sass/screen.sass */
.download-button {
  display: block;
  max-width: 300px;
  margin: 0 auto 20px auto;
  padding-top: 20px;
  padding-bottom: 10px;
  background-color: #2b2b2b;
  border: 4px solid #444444;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: all 0.1s ease-out;
  -moz-transition: all 0.1s ease-out;
  -o-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
  *zoom: 1;
}
/* line 38, ../../../../.rvm/gems/ruby-1.9.3-p392/gems/compass-0.12.2/frameworks/compass/stylesheets/compass/utilities/general/_clearfix.scss */
.download-button:after {
  content: "";
  display: table;
  clear: both;
}
/* line 213, ../sass/screen.sass */
.download-button:hover {
  border-color: #00bfa8;
  background-color: #444444;
}
/* line 216, ../sass/screen.sass */
.download-button .file {
  font-size: 36px;
  color: white;
  line-height: 1em;
}
/* line 220, ../sass/screen.sass */
.download-button .version {
  font-size: 24px;
  color: #00bfa8;
}

/* -- Sharing -- */
/* line 226, ../sass/screen.sass */
.sharing {
  position: fixed;
  top: 20px;
  right: 0;
}

/* -- Donate -- */
/* line 233, ../sass/screen.sass */
.donate-button-form {
  text-align: center;
}

/* line 235, ../sass/screen.sass */
.donate-button {
  border: 4px solid rgba(0, 0, 0, 0);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: all 0.1s ease-out;
  -moz-transition: all 0.1s ease-out;
  -o-transition: all 0.1s ease-out;
  transition: all 0.1s ease-out;
}
/* line 239, ../sass/screen.sass */
.donate-button:hover {
  background-color: #444444;
  border-color: #00bfa8;
}


  /* line 250, ../sass/screen.sass */
  h1 {
    font-size: 48px;
  }

  /* line 252, ../sass/screen.sass */
  h2 {
    font-size: 26px;
  }

  /* line 254, ../sass/screen.sass */
  h3 {
    font-size: 16px;
  }

  /* line 256, ../sass/screen.sass */
  ol {
    list-style-position: inside;
  }

  /* line 258, ../sass/screen.sass */
  code,
  kbd,
  pre,
  pre code {
    font-size: 11px;
  }

  /* line 263, ../sass/screen.sass */
  .sub-point {
    font-size: 12px;
  }
  /* line 265, ../sass/screen.sass */
  .sub-point code {
    font-size: 9px;
  }

  /* line 267, ../sass/screen.sass */
  .lead {
    font-size: 16px;
  }

  /* line 269, ../sass/screen.sass */
  .section {
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  /* line 272, ../sass/screen.sass */
  .author {
    margin-top: -5px;
    padding-left: 30px;
  }

  /* line 275, ../sass/screen.sass */
  .author-links {
    font-size: 12px;
  }
}
