# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
from datetime import datetime, timedelta
import calendar


class MonthlyReservationExcel(http.Controller):

    @http.route('/hotel_reports/monthly_reservation_excel', type='http', auth='user')
    def monthly_reservation_excel(self, month, year, **kwargs):
        """Generate Excel file for monthly reservation schedule."""
        # Convert parameters to integers
        month = int(month)
        year = int(year)
        
        # Create a model instance to use its methods
        report_model = request.env['report.hotel_reports.monthly_reservation_schedule_report']
        
        # Get month name
        month_date = datetime(year, month, 1)
        month_name = month_date.strftime('%B')
        actual_days_in_month = calendar.monthrange(year, month)[1]
        
        # Date range for the report
        date_from = datetime(year, month, 1)
        date_to = datetime(year, month, actual_days_in_month, 23, 59, 59)
        
        # Get all rooms
        domain = []
        rooms = request.env['hotel.room'].search(domain)
        
        # Get all reservations for the month
        reservations = request.env['hotel.reservation'].search([
            ('state', 'in', ['draft', 'confirm', 'done']),
        ])
        
        # Build the rows data
        rows = []
        for room in rooms:
            room_data = {
                'room_name': room.name,
                'days': []
            }
            
            # Initialize all days as unoccupied
            for day in range(1, 32):  # We use 31 days for all months to keep the table consistent
                is_valid_day = day <= actual_days_in_month
                room_data['days'].append({
                    'day': day,
                    'occupied': 0,
                    'guest': '',
                    'guest_name': '',
                    'is_first_day': False,
                    'color': '#FFFFFF' if is_valid_day else '#F5F5F5',  # Light gray for non-existing days
                    'state': False,
                    'reservation_id': False
                })
            
            # Mark occupied days from reservations
            for reservation in reservations:
                for line in reservation.reservation_line:
                    if line.room_number.id == room.product_id.id:
                        checkin = line.checkin
                        checkout = line.checkout
                        
                        # Skip if the reservation is outside our month
                        if checkout < date_from or checkin > date_to:
                            continue
                        
                        # Determine the color based on the reservation state
                        color = '#FFE4B5'  # Draft - Light orange
                        if reservation.state == 'confirm':
                            color = '#98FB98'  # Confirmed - Light green
                        elif reservation.state == 'done':
                            color = '#87CEEB'  # Done (checked in) - Light blue
                        
                        # Calculate the days of stay within our month
                        start_day = max(checkin, date_from)
                        end_day = min(checkout, date_to)
                        
                        # Mark each day of the stay
                        current_day = start_day
                        is_first_day = True
                        while current_day <= end_day:
                            day_idx = current_day.day - 1
                            if 0 <= day_idx < 31:  # Make sure we're within our array bounds
                                room_data['days'][day_idx].update({
                                    'occupied': 1,
                                    'guest': f"{reservation.partner_id.name} - {reservation.reservation_no}",
                                    'guest_name': reservation.partner_id.name if reservation.partner_id.name else '?',
                                    'is_first_day': is_first_day,
                                    'color': color,
                                    'state': reservation.state,
                                    'reservation_id': reservation.id
                                })
                            
                            # Move to the next day
                            current_day += timedelta(days=1)
                            is_first_day = False
            
            rows.append(room_data)
        
        # Prepare the output stream for the Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('Monthly Reservation Schedule')
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True, 
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#f8f9fa',
            'border': 1
        })
        
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'left'
        })
        
        date_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'right'
        })
        
        room_format = workbook.add_format({
            'bold': True, 
            'align': 'left',
            'bg_color': '#f8f9fa',
            'border': 1
        })
        
        # Status color formats with text alignment
        draft_format = workbook.add_format({
            'bg_color': '#FFE4B5', 
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True,
            'font_size': 8
        })
        
        confirmed_format = workbook.add_format({
            'bg_color': '#98FB98', 
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True,
            'font_size': 8
        })
        
        checkin_format = workbook.add_format({
            'bg_color': '#87CEEB', 
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True,
            'font_size': 8
        })
        
        empty_format = workbook.add_format({'bg_color': '#FFFFFF', 'border': 1})
        extra_day_format = workbook.add_format({'bg_color': '#F5F5F5', 'border': 1})
        
        # Set column widths
        worksheet.set_column(0, 0, 20)  # Room name column
        worksheet.set_column(1, 31, 5)  # Day columns
        
        # Write title
        worksheet.merge_range(0, 0, 0, 1, 'Monthly Reservation Schedule', title_format)
        worksheet.write(0, 2, f"{month_name} {year}", date_format)
        
        # Write legend
        worksheet.write(1, 0, 'Legend:', workbook.add_format({'bold': True}))
        worksheet.write(1, 1, 'Draft', draft_format)
        worksheet.write(1, 2, 'Confirmed', confirmed_format)
        worksheet.write(1, 3, 'Checked In', checkin_format)
        
        # Write headers
        row = 3
        worksheet.write(row, 0, 'Room', header_format)
        for day in range(1, 32):
            is_valid_day = day <= actual_days_in_month
            cell_format = header_format if is_valid_day else workbook.add_format({
                'bold': True, 'align': 'center', 'bg_color': '#F5F5F5', 'border': 1
            })
            worksheet.write(row, day, day, cell_format)
        
        # Process each room
        row = 4
        for room_data in rows:
            worksheet.write(row, 0, room_data['room_name'], room_format)
            
            # Process days
            current_reservation_id = None
            merge_start = None
            
            for day in range(1, 32):
                day_idx = day - 1
                day_data = room_data['days'][day_idx]
                
                # Format selection
                if day > actual_days_in_month:
                    cell_format = extra_day_format
                    worksheet.write(row, day, '', cell_format)
                    continue
                
                if not day_data['occupied']:
                    # Empty cell
                    worksheet.write(row, day, '', empty_format)
                    continue
                
                # Get cell format based on reservation state
                if day_data['state'] == 'draft':
                    cell_format = draft_format
                elif day_data['state'] == 'confirm':
                    cell_format = confirmed_format
                elif day_data['state'] == 'done':
                    cell_format = checkin_format
                else:
                    cell_format = empty_format
                
                # Check if a new reservation starts or if it's the first day
                if day_data['reservation_id'] != current_reservation_id:
                    # Write out any pending merged cell first
                    if merge_start is not None and merge_start < day - 1:
                        # We have a multi-day reservation to merge
                        prev_day_data = room_data['days'][merge_start - 1]
                        guest_name = prev_day_data['guest_name']
                        guest_info = prev_day_data['guest']
                        
                        if day_data['state'] == 'draft':
                            prev_cell_format = draft_format
                        elif day_data['state'] == 'confirm':
                            prev_cell_format = confirmed_format
                        elif day_data['state'] == 'done':
                            prev_cell_format = checkin_format
                        else:
                            prev_cell_format = empty_format
                        
                        worksheet.merge_range(row, merge_start, row, day - 1, guest_name, prev_cell_format)
                        worksheet.write_comment(row, merge_start, guest_info, {'width': 200, 'height': 80})
                    
                    # Start a new reservation tracking
                    current_reservation_id = day_data['reservation_id']
                    merge_start = day
                    
                    # If it's just a single day or the last day, write it directly
                    if day == 31 or not room_data['days'][day_idx + 1]['occupied'] or room_data['days'][day_idx + 1]['reservation_id'] != current_reservation_id:
                        worksheet.write(row, day, day_data['guest_name'], cell_format)
                        worksheet.write_comment(row, day, day_data['guest'], {'width': 200, 'height': 80})
                        current_reservation_id = None
                        merge_start = None
            
            # Handle any pending merge at the end of the row
            if merge_start is not None and merge_start < 31:
                day_data = room_data['days'][merge_start - 1]
                guest_name = day_data['guest_name']
                guest_info = day_data['guest']
                
                if day_data['state'] == 'draft':
                    cell_format = draft_format
                elif day_data['state'] == 'confirm':
                    cell_format = confirmed_format
                elif day_data['state'] == 'done':
                    cell_format = checkin_format
                else:
                    cell_format = empty_format
                
                worksheet.merge_range(row, merge_start, row, 31, guest_name, cell_format)
                worksheet.write_comment(row, merge_start, guest_info, {'width': 200, 'height': 80})
            
            row += 1
        
        # Close the workbook
        workbook.close()
        
        # Prepare the HTTP response with the Excel file
        output.seek(0)
        filename = f"Monthly_Reservation_Schedule_{month_name}_{year}.xlsx"
        
        return request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        ) 