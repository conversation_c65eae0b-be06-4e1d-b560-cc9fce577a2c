# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import datetime, timedelta
import calendar
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class HotelReports(models.Model):
    _name = 'hotel.reports'
    _description = 'Hotel Reports'
    
    name = fields.Char(string='Name')

class MonthlyReservationWizard(models.TransientModel):
    _name = 'monthly.reservation.wizard'
    _description = 'Monthly Reservation Schedule Wizard'

    month = fields.Selection([
        ('1', 'January'),
        ('2', 'February'),
        ('3', 'March'),
        ('4', 'April'),
        ('5', 'May'),
        ('6', 'June'),
        ('7', 'July'),
        ('8', 'August'),
        ('9', 'September'),
        ('10', 'October'),
        ('11', 'November'),
        ('12', 'December'),
    ], string='Month', required=True, default=lambda self: str(datetime.now().month))
    
    year = fields.Integer(string='Year', required=True, default=lambda self: datetime.now().year)
    
    @api.model
    def get_month_name(self, month_number):
        """Get the month name from month number."""
        if not month_number or not str(month_number).isdigit():
            return ''
        month_int = int(month_number)
        if month_int < 1 or month_int > 12:
            return ''
        
        month_names = {
            1: 'January', 2: 'February', 3: 'March', 4: 'April',
            5: 'May', 6: 'June', 7: 'July', 8: 'August',
            9: 'September', 10: 'October', 11: 'November', 12: 'December'
        }
        return month_names.get(month_int, '')
    
    def print_report(self):
        """Generate the monthly reservation schedule PDF report."""
        self.ensure_one()
        month = int(self.month)
        year = int(self.year)
        
        # Return the report action
        return {
            'type': 'ir.actions.report',
            'report_name': 'hotel_reports.monthly_reservation_schedule_report',
            'report_type': 'qweb-pdf',
            'context': {
                'month': month,
                'year': year,
            },
        }
    
    def export_excel(self):
        """Export monthly reservation schedule as Excel file."""
        self.ensure_one()
        
        # Get the base URL
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url = f"{base_url}/hotel_reports/monthly_reservation_excel?month={self.month}&year={self.year}"
        
        # Return action to redirect to URL
        return {
            'type': 'ir.actions.act_url',
            'url': url,
            'target': 'self',
        }

class MonthlyReservationReport(models.AbstractModel):
    _name = 'report.hotel_reports.monthly_reservation_schedule_report'
    _description = 'Monthly Reservation Schedule Report'
    
    @api.model
    def _get_report_values(self, docids, data=None):
        """Get report values"""
        # Get month and year from context or use current month/year
        month = self.env.context.get('month', datetime.now().month)
        year = self.env.context.get('year', datetime.now().year)
        shop_id = self.env.context.get('shop_id', False)
        
        # Get the number of days in the selected month
        days_in_month = calendar.monthrange(year, month)[1]
        
        # Create a datetime object for the first day of the month
        date_from = datetime(year, month, 1)
        # And the last day of the month
        date_to = datetime(year, month, days_in_month, 23, 59, 59)
        
        # Get all rooms
        domain = []
        if shop_id:
            domain.append(('shop_id', '=', shop_id))
        
        rooms = self.env['hotel.room'].search(domain)
        
        # If docids is provided, filter rooms to only those selected
        if docids:
            rooms = rooms.filtered(lambda r: r.id in docids)
        
        # Get all reservations for the month
        reservations = self.env['hotel.reservation'].search([
            ('state', 'in', ['draft', 'confirm', 'done']),
            ('shop_id', '=', shop_id) if shop_id else ('id', '!=', False),
        ])
        
        # Build the rows data
        rows = []
        for room in rooms:
            room_data = {
                'room_name': room.name,
                'days': []
            }
            
            # Initialize all days as unoccupied
            for day in range(1, 32):  # We use 31 days for all months to keep the table consistent
                is_valid_day = day <= days_in_month
                room_data['days'].append({
                    'day': day,
                    'occupied': 0,
                    'guest': '',
                    'guest_name': '',
                    'is_first_day': False,
                    'color': '#FFFFFF' if is_valid_day else '#F5F5F5'  # Light gray for non-existing days
                })
            
            # Mark occupied days from reservations
            for reservation in reservations:
                for line in reservation.reservation_line:
                    if line.room_number.id == room.product_id.id:
                        checkin = line.checkin
                        checkout = line.checkout
                        
                        # Skip if the reservation is outside our month
                        if checkout < date_from or checkin > date_to:
                            continue
                        
                        # Determine the color based on the reservation state
                        color = '#FFE4B5'  # Draft - Light orange
                        if reservation.state == 'confirm':
                            color = '#98FB98'  # Confirmed - Light green
                        elif reservation.state == 'done':
                            color = '#87CEEB'  # Done (checked in) - Light blue
                        
                        # Calculate the days of stay within our month
                        start_day = max(checkin, date_from)
                        end_day = min(checkout, date_to)
                        
                        # Mark each day of the stay
                        current_day = start_day
                        is_first_day = True
                        while current_day <= end_day:
                            day_idx = current_day.day - 1
                            if 0 <= day_idx < 31:  # Make sure we're within our array bounds
                                room_data['days'][day_idx].update({
                                    'occupied': 1,
                                    'guest': f"{reservation.partner_id.name} - {reservation.reservation_no}",
                                    'guest_name': reservation.partner_id.name[:8] if reservation.partner_id.name else '?',
                                    'is_first_day': is_first_day,
                                    'color': color
                                })
                            
                            # Move to the next day
                            current_day += timedelta(days=1)
                            is_first_day = False
            
            rows.append(room_data)
        
        # Get the month name
        month_name = date_from.strftime('%B')
        
        return {
            'rows': rows,
            'month_name': month_name,
            'year': year,
            'actual_days_in_month': days_in_month
        } 