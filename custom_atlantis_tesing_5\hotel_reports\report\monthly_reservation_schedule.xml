<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Main template that will be rendered by the report engine -->
    <template id="monthly_reservation_schedule_report">
        <t t-call="web.html_container">
            <div class="article" style="margin: 0; padding: 0;">
                <div class="page p-0" style="margin: 0; padding: 0;">
                    <div style="margin: 0; padding: 0;">
                        <!-- Header with title and month -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <div style="font-weight: bold; font-size: 14px;">
                                Monthly Reservation Schedule
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 12px; font-weight: bold; margin-bottom: 2px;"><t t-esc="month_name"/> <t t-esc="year"/></div>
                                <!-- Compact Legend -->
                                <div style="font-size: 9px; white-space: nowrap;">
                                    <span style="display: inline-block; width: 10px; height: 10px; background-color: #FFE4B5; border: 1px solid #ddd; margin-right: 2px; vertical-align: middle;"></span>
                                    <span style="vertical-align: middle;">Draft</span>
                                    <span style="margin: 0 3px; color: #ccc; vertical-align: middle;">|</span>
                                    <span style="display: inline-block; width: 10px; height: 10px; background-color: #98FB98; border: 1px solid #ddd; margin-right: 2px; vertical-align: middle;"></span>
                                    <span style="vertical-align: middle;">Confirmed</span>
                                    <span style="margin: 0 3px; color: #ccc; vertical-align: middle;">|</span>
                                    <span style="display: inline-block; width: 10px; height: 10px; background-color: #87CEEB; border: 1px solid #ddd; margin-right: 2px; vertical-align: middle;"></span>
                                    <span style="vertical-align: middle;">Checked In</span>
                                </div>
                            </div>
                        </div>
                        
                        <table class="table table-bordered table-sm" style="margin: 0; padding: 0; border-collapse: collapse; table-layout: fixed;">
                            <colgroup>
                                <col style="width: 100px;"/>
                                <t t-foreach="range(1, 32)" t-as="day">
                                    <col style="width: 32px;"/>
                                </t>
                            </colgroup>
                            <thead>
                                <tr>
                                    <th style="width: 100px; padding: 1px; font-size: 13px; background-color: #f8f9fa;">Room</th>
                                    <!-- Generate day headers -->
                                    <t t-foreach="range(1, 32)" t-as="day">
                                        <t t-set="is_extra_day" t-value="actual_days_in_month and day > actual_days_in_month"/>
                                        <th class="text-center" t-attf-style="width: 32px; padding: 1px; font-size: 11px; background-color: #{is_extra_day and '#F5F5F5' or '#f8f9fa'};">
                                            <t t-esc="day"/>
                                        </th>
                                    </t>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- No data message -->
                                <t t-if="not rows or len(rows) == 0">
                                    <tr>
                                        <td colspan="32" class="text-center" style="padding: 1px !important; font-size: 11px;">
                                            <em>No room data available for this month.</em>
                                        </td>
                                    </tr>
                                </t>
                                
                                <!-- Room data rows -->
                                <t t-foreach="rows" t-as="row">
                                    <tr>
                                        <td style="width: 100px; padding: 1px 2px; font-size: 13px; background-color: #f8f9fa; font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            <t t-esc="row.get('room_name', '')"/>
                                        </td>
                                        <!-- Generate day cells -->
                                        <t t-foreach="row.get('days', [])" t-as="day_data">
                                            <t t-set="occupied" t-value="day_data.get('occupied', 0)"/>
                                            <t t-set="guest" t-value="day_data.get('guest', '')"/>
                                            <t t-set="color" t-value="day_data.get('color', '#FFFFFF')"/>
                                            
                                            <!-- Cell with color coding based on occupation status -->
                                            <td class="text-center" t-attf-style="background-color: #{color}; #{occupied == 1 and 'cursor: pointer;' or ''} width: 32px; height: 32px; padding: 1px; border: 1px solid #dee2e6;">
                                                <t t-if="occupied == 1">
                                                    <div t-att-title="guest" style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;">
                                                        <div style="font-size: 10px; max-width: 30px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                            <t t-if="day_data.get('is_first_day', False)">
                                                                <span style="color: #333;"><t t-esc="day_data.get('guest_name', '')"/></span>
                                                            </t>
                                                            <t t-else="">
                                                                <span style="color: #666;">―</span>
                                                            </t>
                                                        </div>
                                                    </div>
                                                </t>
                                                <t t-else="">
                                                    <span>&#160;</span>
                                                </t>
                                            </td>
                                        </t>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo> 