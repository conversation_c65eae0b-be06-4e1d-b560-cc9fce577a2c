<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Wizard Form View -->
    <record id="view_monthly_reservation_wizard_form" model="ir.ui.view">
        <field name="name">monthly.reservation.wizard.form</field>
        <field name="model">monthly.reservation.wizard</field>
        <field name="arch" type="xml">
            <form string="Monthly Reservation Schedule">
                <group>
                    <field name="month" required="1"/>
                    <field name="year" required="1"/>
                </group>
                <footer>
                    <button name="print_report" string="Print PDF" type="object" class="btn-primary"/>
                    <button name="export_excel" string="Export Excel" type="object" class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Wizard Action -->
    <record id="action_monthly_reservation_wizard" model="ir.actions.act_window">
        <field name="name">Monthly Reservation Schedule</field>
        <field name="res_model">monthly.reservation.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu Item that opens the wizard -->
    <menuitem id="menu_monthly_reservation_schedule" 
              name="Monthly Reservation Schedule" 
              parent="hotel.hotel_report_menu"
              action="action_monthly_reservation_wizard"
              sequence="20"/>
</odoo> 