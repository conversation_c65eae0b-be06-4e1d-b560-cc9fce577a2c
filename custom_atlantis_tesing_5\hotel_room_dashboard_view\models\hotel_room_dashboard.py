# -*- coding: utf-8 -*-
import pytz

from odoo import models, fields, api
from odoo.exceptions import UserError
from dateutil.relativedelta import relativedelta
import time
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import *
import pytz
import math

utc_time = datetime.utcnow()
import logging

_logger = logging.getLogger(__name__)


class hotel_room_dashboard(models.Model):
    """ Class for showing Rooms Dashboard"""
    _name = 'hotel.room.dashboard'
    _description = 'Room Dashboard'

    name = fields.Char('Name')

    def open_dashboard(self):
        return {
            'type': 'ir.actions.act_url',
            'url': '/hotel_room_dashboard/web/',
            'target': 'self',
        }

    def get_company_id(self):
        return self._context.get('allowed_company_ids')


class hotel_reservation(models.Model):
    _inherit = 'hotel.reservation'

    """ Inherited to set default values in reservation form"""

    @api.onchange('pricelist_id')
    def onchange_pricelist_id(self):
        """Override to handle dashboard context more gracefully"""
        self.show_update_pricelist = True
        
        # Check if we have the required context for the original logic
        if ('checkin' in self._context and 'checkout' in self._context and 
            'hotel_resource' in self._context and 'shop_id' in self._context):
            
            room_obj = self.env['product.product']
            room_brw = room_obj.search([('id', '=', self._context['hotel_resource'])])
            
            # Get shop and validate it has a pricelist
            shop = self.env['sale.shop'].browse(int(self._context['shop_id']))
            if not shop.exists():
                return {}
                
            # If shop doesn't have a pricelist, try to set a default one
            if not shop.pricelist_id:
                # Try to find a default pricelist for the company
                default_pricelist = self.env['product.pricelist'].search([
                    ('company_id', '=', shop.company_id.id)
                ], limit=1)
                
                if default_pricelist:
                    # Set the pricelist on the shop
                    shop.write({'pricelist_id': default_pricelist.id})
                    pricelist = default_pricelist.id
                else:
                    # Create a default pricelist if none exists
                    default_pricelist = self.env['product.pricelist'].create({
                        'name': f'Default Pricelist - {shop.name}',
                        'company_id': shop.company_id.id,
                        'currency_id': shop.company_id.currency_id.id,
                    })
                    shop.write({'pricelist_id': default_pricelist.id})
                    pricelist = default_pricelist.id
            else:
                pricelist = shop.pricelist_id.id
                
            # Continue with the original logic
            ctx = self._context and self._context.copy() or {}
            ctx.update({'date': self._context['checkin']})
            from dateutil import parser
            day_count1 = parser.isoparse(self._context['checkout']) - parser.isoparse(self._context['checkin'])

            day_count2 = day_count1.total_seconds()
            _logger.info("DIFF SECONDS===>>>>>>>>>{}".format(day_count2))
            day_count2 = day_count2 / 86400
            day_count2 = "{:.2f}".format(day_count2)
            day_count2 = math.ceil(float(day_count2))

            _logger.info("SELF CONTEXT====>>>>>{}".format(self._context['checkin']))
            res_line = {
                'categ_id': room_brw.categ_id.id,
                'room_number': room_brw.id,
                'checkin': parser.isoparse(self._context['checkin']),
                'checkout': parser.isoparse(self._context['checkout']),
                'number_of_days': int(day_count2),
                'price': self.env['product.pricelist'].with_context(ctx).price_get(room_brw.id, 1)[pricelist]
            }
            if self.reservation_line:
                self.reservation_line.write(res_line)
            else:
                _logger.info("RES LINE===>>>>>>>>>>>>>>>{}".format(res_line))
                self.reservation_line = [[0, 0, res_line]]
                
        # Continue with the rest of the original method
        if not self.pricelist_id:
            return {}
        if not self.reservation_line:
            return {}
        if len(self.reservation_line) != 1:
            warning = {
                'title': _('Pricelist Warning!'),
                'message': _(
                    'If you change the pricelist of this order (and eventually the currency), prices of existing order lines will not be updated.')
            }
            return {'warning': warning}

    def action_folio_confirm(self):
        search_id = self.env['hotel.folio'].search([('reservation_id', '=', self.id)])
        if search_id and search_id.state == 'draft':
            search_id.action_confirm()
        return True

    @api.model
    def get_data(self, shop_id):
        print('==========', shop_id)
        if shop_id:
            check_in = self.env['hotel.folio'].search([('state', '=', 'draft'), ('shop_id', '=', int(shop_id))])
            check_out = self.env['hotel.folio'].search([('state', '=', 'check_out'), ('shop_id', '=', int(shop_id))])
            total = self.env['hotel.folio'].search([('shop_id', '=', int(shop_id))])
            booked = self.env['hotel.folio'].search([('state', '!=', 'check_out'), ('shop_id', '=', int(shop_id))])
            return {
                'check_in': len(check_in),
                'check_out': len(check_out),
                'total': len(total),
                'booked': len(booked),
            }
        else:
            return {
                'check_in': '',
                'check_out': '',
                'total': '',
                'booked': '',
            }

    def action_folio_checkout(self):
        search_id = self.env['hotel.folio'].search([('reservation_id', '=', self.id)])
        if search_id and search_id.state == 'sale':
            search_id.action_checkout()

    def action_folio_done(self):
        search_id = self.env['hotel.folio'].search([('reservation_id', '=', self.id)])
        if search_id and search_id.state == 'check_out':
            search_id.action_done()

    def write(self, vals):

        # print("ffffffffffffffffffffffffffff", vals)

        return super(hotel_reservation, self).write(vals)

    def get_folio_status(self):
        folio_record = self.env['hotel.folio'].search([('reservation_id', '=', self.id)])

        if folio_record:
            return (folio_record.state, folio_record.id)

        return False

    def get_view_folio(self):

        folio_view = self.env['ir.ui.view'].search(
            [('xml_id', '=', 'hotel.view_hotel_folio1_form'), ('model', '=', 'hotel.folio')], limit=1)

        return folio_view.id

    # def update_reservation_old(self, resourceId, description):
    #     print("fffffffffffffffff", resourceId, description)

    def update_reservation_line(self, description, start, end, resourceId, start_only_date, end_only_date):
        # print("resourceId::::::::::::", description, resourceId, start1, end1, old_id, start_only_date, end_only_date)
        _logger.info("UPDATE RESERVATION LINE===>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        reservation = self.env['hotel.reservation'].search([('reservation_no', '=', description)])

        # print("reservation::::::::::::::::", reservation, resourceId)

        if resourceId:
            room_id = self.env['product.product'].search([('id', '=', resourceId)])
            # print("room_id:::::::::", room_id)
        for line_id in reservation:
            for line in line_id.reservation_line:
                if line.room_number.id == room_id.id:

                    # print("line_id::::::::::::", line_id.folio_id)
                    if start:
                        line.write({'checkin': start})
                    if end:
                        line.write({'checkout': end})

                if reservation.state == 'confirm':
                    # print("reservation:::::::::::::::::", reservation.state)

                    hotel_history = self.env['hotel.room.booking.history'].search([('booking_id', '=', reservation.id)])
                    # print("hotel_history::::::::::::::::::", hotel_history)
                    for hotel_history_line in hotel_history:
                        # print("hotel_history_line.product_id.id::::::::;", hotel_history_line.product_id,
                        #       line.room_number.name)
                        if hotel_history_line.product_id == line.room_number.id and hotel_history.booking_id.id == line.line_id.id:
                            if hotel_history_line.name == line.room_number.name:
                                if start:
                                    hotel_history_line.write({"check_in": start})
                                    hotel_history_line.write({"check_in_date": start_only_date})
                                if end:
                                    hotel_history_line.write({"check_out": end})
                                    hotel_history_line.write({"check_out_date": end_only_date})

        if reservation:
            folio = self.env['hotel.folio'].search([('reservation_id', '=', reservation.reservation_no)])
            # print("folio:::::::::::::", folio)

            for folio_line in folio.room_lines:
                # print("folio_line::::::::::;", folio_line.product_id, room_id.id)

                if folio_line.product_id.id == room_id.id:
                    if start:
                        folio_line.write({'checkin_date': start})
                    if end:
                        folio_line.write({'checkout_date': end})
                    folio_line.on_change_checkout()

    def update_room(self, description, resourceId, start1, end1, old_id, start_only_date, end_only_date):
        # print("resourceId::::::::::::", description, resourceId, start1, end1, old_id, start_only_date, end_only_date)
        # print("ffffffffffffff",)
        _logger.info("UPDATE ROOM===>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
        if resourceId:
            room_id = self.env['product.product'].search([('id', '=', resourceId)])
            # print("room_id:::::::::", room_id)
        if old_id:
            room_id_old = self.env['product.product'].search([('id', '=', old_id)])
            # print("room_id:::::::::", room_id_old)

        reservation = self.env['hotel.reservation'].search([('reservation_no', '=', description)])

        # print("rreservation::::::::::::::::", reservation, resourceId)

        if resourceId:
            room_id = self.env['product.product'].search([('id', '=', resourceId)])
            # print("room_id:::::::::", room_id)
        for line_id in reservation:
            for line in line_id.reservation_line:

                if room_id_old:
                    if line.room_number.id == room_id_old.id:

                        # print("line_id::::::::::::", line_id.folio_id)
                        if start1:
                            line.write({'checkin': start1})
                        if end1:
                            line.write({'checkout': end1})

                        if room_id:
                            line.write({'room_number': room_id.id})
                            line.write({'categ_id': room_id.categ_id.id})
                if reservation.state != 'draft':
                    # print("reservation:::::::::::.::::::",reservation.state)

                    hotel_history = self.env['hotel.room.booking.history'].search([('booking_id', '=', reservation.id)])
                    hotel_room = self.env['hotel.room'].search([('product_id', '=', room_id.id)])
                    # print("hotel_history::::::::::::::::::", hotel_history)
                    for hotel_history_line in hotel_history:
                        # print("hotel_history_line.product_id.id::::::::;",hotel_history_line.product_id,room_id_old.id)
                        if hotel_history_line.product_id == room_id_old.id and hotel_history.booking_id.id == line.line_id.id:
                            # print("hhhhhhhhhhhhhhhhhhhhhhhhhhhhhh")
                            if start1:
                                hotel_history_line.write({"check_in": start1})
                                hotel_history_line.write({"check_in_date": start_only_date})
                            if end1:
                                hotel_history_line.write({"check_out": end1})
                                hotel_history_line.write({"check_out_date": end_only_date})
                            if hotel_room:
                                hotel_history_line.write({"history_id": hotel_room.id})
                                hotel_history_line.write({"name": hotel_room.name})
                            if room_id:
                                hotel_history_line.write({"product_id": room_id.id})
                                hotel_history_line.write({"category_id": room_id.categ_id.id})

        if reservation:
            folio = self.env['hotel.folio'].search([('reservation_id', '=', reservation.reservation_no)])
            # print("folio:::::::::::::", folio)

            for line in folio.room_lines:
                # print("line:::::::::", line)
                if room_id_old:
                    if line.product_id.id == room_id_old.id:
                        # print("line:::::::::::::::::::::::", line)

                        if room_id:
                            line.write({"product_id": room_id.id})
                            line.write({"name": room_id.name})
                            line.write({"categ_id": room_id.categ_id.id})

                        if end1:
                            # print("ffffffffffffffffffffffff2", end1)
                            line.write({'checkout_date': end1})

                        if start1:
                            # print("ffffffffffffffffffffffff", start1)
                            line.write({'checkin_date': start1})

                        line.on_change_checkout()

                else:
                    if room_id:
                        line.write({"product_id": room_id.id})
                        line.write({"name": room_id.name})
                        line.write({"categ_id": room_id.categ_id.id})

                    if end1:
                        # print("ffffffffffffffffffffffff2", end1)
                        line.write({'checkout_date': end1})

                    if start1:
                        # print("ffffffffffffffffffffffff", start1)
                        line.write({'checkin_date': start1})
                    line.on_change_checkout()

    def set_checkin_checkout(self, vals):
        _logger.info("CHECK IN CHECKOUT===>>>>>>>>>>{}".format(vals))
        if self.env.user:
            user_id = self.env.user
            tz = pytz.timezone(user_id.tz)
            time_difference = tz.utcoffset(utc_time).total_seconds()
            _logger.info("#################{}".format(self._context))
            if self._context.get('shop_id'):
                checkout_policy_id = self.env['checkout.configuration'].search(
                    [('shop_id', '=', self._context.get('shop_id'))])
                _logger.info("Checkout Policy===>>>{}".format(checkout_policy_id))

                if checkout_policy_id.name == 'custom':
                    time = int(checkout_policy_id.time)

                    checkin = vals.get('checkin')
                    check_in_from_string = fields.Datetime.from_string(vals.get('checkin'))
                    checkin = datetime(check_in_from_string.year, check_in_from_string.month, check_in_from_string.day)
                    checkin = checkin + timedelta(hours=int(time))

                    checkout = vals.get('checkout')
                    checkout_from_string = fields.Datetime.from_string(vals.get('checkout'))
                    checkout = datetime(checkout_from_string.year, checkout_from_string.month, checkout_from_string.day)
                    checkout = checkout + timedelta(hours=int(time))

                    checkout = checkout - timedelta(seconds=time_difference)
                    checkin = checkin - timedelta(seconds=time_difference)

                    _logger.info("\n\n\n\n\nVALS=====>>>>>>>>>>>>>>>>>>{}".format(checkout))
                    vals.update({'checkout': checkout, 'checkin': checkin})
        return vals
    def get_view_reserve(self):
        view_id = self.env.ref('hotel_management.view_hotel_reservation_form1').id
        return view_id


class RrHousekeeping(models.Model):
    """ Class for showing housekeeping Maintanance Dashboard"""
    _inherit = 'rr.housekeeping'

    def maintance_housekiping(self):
        maintance_dict_list = []
        for rec in self.search([]):
            if rec.state == 'assign':
                maintance_dict = {}
                maintance_dict.update({
                    'id': rec.id,
                    'date': rec.date.date(),
                    'room_no': [1, rec.room_no.name],
                    'state': "assign"
                })
                maintance_dict_list.append(maintance_dict)
        return maintance_dict_list


    
