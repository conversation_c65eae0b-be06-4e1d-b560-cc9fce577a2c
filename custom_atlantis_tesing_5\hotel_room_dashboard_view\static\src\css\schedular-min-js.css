.fc-resource-area .fc-cell-content,
.fc-timeline .fc-cell-text {
	padding-left: 4px;
	padding-right: 4px
}

.fc-resource-area th>div,
.fc-scroller-canvas,
.fc-scroller-canvas>.fc-content,
.fc-scroller-clip {
	position: relative
}

.fc-scroller-clip,
.fc-timeline .fc-cell-content,
tr.fc-collapsed>td,
tr.fc-transitioning>td {
	overflow: hidden
}

.fc-no-scrollbars {
	background: rgba(255, 255, 255, 0)
}

.fc-timeline .fc-body .fc-divider.ui-widget-header,
.fc-timeline .fc-body .ui-widget-content {
	background-image: none
}

.fc-no-scrollbars::-webkit-scrollbar {
	width: 0;
	height: 0
}

.fc-scroller-canvas {
	box-sizing: border-box;
	min-height: 100%
}

.fc-scroller-canvas>.fc-bg {
	z-index: 1
}

.fc-scroller-canvas>.fc-content {
	z-index: 2;
	border-style: solid;
	border-width: 0
}

.ui-widget .fc-scroller-canvas>.fc-content {
	border-color: transparent
}

.fc-scroller-canvas.fc-gutter-left>.fc-content {
	border-left-width: 1px;
	margin-left: -1px
}

.fc-scroller-canvas.fc-gutter-right>.fc-content {
	border-right-width: 1px;
	margin-right: -1px
}

.fc-scroller-canvas.fc-gutter-top>.fc-content {
	border-top-width: 1px;
	margin-top: -1px
}

.fc-rtl .fc-timeline {
	direction: rtl
}

.fc-timeline .fc-divider {
	width: 3px;
	border-style: double
}

.fc-timeline .fc-head>tr>.fc-divider {
	border-bottom: 0
}

.fc-timeline .fc-body>tr>.fc-divider {
	border-top: 0
}

.fc-scrolled .fc-head .fc-scroller {
	z-index: 2
}

.fc-timeline.fc-scrolled .fc-head .fc-scroller {
	box-shadow: 0 3px 4px rgba(0, 0, 0, .075)
}

.fc-timeline .fc-body .fc-scroller {
	z-index: 1
}

.fc-timeline .fc-scroller-canvas>div>div>table,
.fc-timeline .fc-scroller-canvas>div>table {
	border-style: hidden
}

.fc-timeline .fc-scroller-canvas>.fc-content>.fc-rows>table {
	border-bottom-style: none
}

.fc-timeline td,
.fc-timeline th {
	white-space: nowrap
}

.fc-timeline .fc-col-resizer {
	cursor: col-resize
}

.fc-timeline th {
	vertical-align: middle
}

.fc-timeline .fc-head .fc-cell-content {
	padding-top: 3px;
	padding-bottom: 3px
}

.fc-resource-area {
	width: 30%
}

.fc-resource-area col {
	width: 40%;
	min-width: 70px
}

.fc-resource-area col.fc-main-col {
	width: 60%
}

.fc-flat .fc-expander-space {
	display: none
}

.fc-ltr .fc-resource-area tr>* {
	text-align: left
}

.fc-rtl .fc-resource-area tr>* {
	text-align: right
}

.fc-resource-area .fc-super th {
	text-align: center
}

.fc-resource-area th .fc-cell-content {
	position: relative;
	z-index: 1
}

.fc-resource-area th .fc-col-resizer {
	position: absolute;
	z-index: 2;
	top: 0;
	bottom: 0;
	width: 5px
}

.fc-ltr .fc-resource-area th .fc-col-resizer {
	right: -3px
}

.fc-rtl .fc-resource-area th .fc-col-resizer {
	left: -3px
}

tr.fc-transitioning>td>div {
	transition: margin-top .2s
}

tr.fc-collapsed>td>div {
	margin-top: -10px
}

.fc-body .fc-resource-area .fc-cell-content {
	position: relative;
	padding-top: 8px;
	padding-bottom: 8px
}

.fc-time-area .fc-bgevent,
.fc-time-area .fc-highlight,
.fc-time-area .fc-slats {
	position: absolute;
	top: 0;
	bottom: 0
}

.fc-no-overlap .fc-body .fc-resource-area .fc-cell-content {
	padding-top: 5px;
	padding-bottom: 5px
}

.fc-resource-area .fc-icon {
	width: 1em;
	font-size: .9em;
	vertical-align: middle;
	margin-top: -1%
}

.fc-resource-area .fc-expander {
	cursor: pointer;
	color: #666
}

.fc-time-area col {
	min-width: 2.2em
}

.fc-ltr .fc-time-area .fc-chrono th {
	text-align: left
}

.fc-rtl .fc-time-area .fc-chrono th {
	text-align: right
}

.fc-time-area .fc-slats {
	z-index: 1;
	left: 0;
	right: 0
}

.fc-time-area .fc-slats table {
	height: 100%
}

.fc-time-area .fc-slats .fc-minor {
	border-style: dotted
}

.fc-time-area .fc-slats td {
	border-width: 0 1px
}

.fc-ltr .fc-time-area .fc-slats td {
	border-right-width: 0
}

.fc-rtl .fc-time-area .fc-slats td {
	border-left-width: 0
}

.fc-time-area .fc-bgevent-container,
.fc-time-area .fc-highlight-container {
	position: absolute;
	z-index: 2;
	top: 0;
	bottom: 0;
	width: 0
}

.fc-ltr .fc-time-area .fc-bgevent-container,
.fc-ltr .fc-time-area .fc-helper-container,
.fc-ltr .fc-time-area .fc-highlight-container {
	left: 0
}

.fc-rtl .fc-time-area .fc-bgevent-container,
.fc-rtl .fc-time-area .fc-helper-container,
.fc-rtl .fc-time-area .fc-highlight-container {
	right: 0
}

.fc-time-area .fc-rows {
	position: relative;
	z-index: 3
}

.fc-time-area .fc-rows .ui-widget-content {
	background: 0 0
}

.fc-time-area .fc-rows td>div {
	position: relative
}

.fc-time-area .fc-rows .fc-bgevent-container,
.fc-time-area .fc-rows .fc-highlight-container {
	z-index: 1
}

.fc-time-area .fc-event-container {
	position: relative;
	z-index: 2;
	width: 0
}

.fc-time-area .fc-helper-container {
	position: absolute;
	z-index: 3;
	top: 0
}

.fc-time-area .fc-event-container {
	padding-bottom: 8px;
	top: -1px
}

.fc-time-area tr:first-child .fc-event-container {
	top: 0
}

.fc-no-overlap .fc-time-area .fc-event-container {
	padding-bottom: 0;
	top: 0
}

.fc-timeline .fc-now-indicator {
	z-index: 3;
	top: 0
}

.fc-time-area .fc-now-indicator-arrow {
	margin: 0 -6px;
	border-width: 6px 5px 0;
	border-left-color: transparent;
	border-right-color: transparent
}

.fc-time-area .fc-now-indicator-line {
	margin: 0 -1px;
	bottom: 0;
	border-left-width: 1px
}

.fc-timeline-event {
	position: absolute;
	border-radius: 0;
	padding: 2px 0;
	margin-bottom: 1px
}

.fc-no-overlap .fc-timeline-event {
	padding: 5px 0;
	margin-bottom: 0
}

.fc-ltr .fc-timeline-event {
	margin-right: 1px
}

.fc-rtl .fc-timeline-event {
	margin-left: 1px
}

.fc-timeline-event .fc-content {
	padding: 0 1px;
	white-space: nowrap;
	overflow: hidden
}

.fc-timeline-event .fc-time {
	font-weight: 700;
	padding: 0 1px
}

.fc-rtl .fc-timeline-event .fc-time {
	display: inline-block
}

.fc-timeline-event .fc-title {
	position: relative;
	padding: 0 1px
}

.fc-timeline-event.fc-selected .fc-bg {
	display: none
}

.fc-ltr .fc-timeline-event .fc-title {
	padding-left: 10px;
	margin-left: -8px
}

.fc-rtl .fc-timeline-event .fc-title {
	padding-right: 10px;
	margin-right: -8px
}

.fc-ltr .fc-timeline-event.fc-not-start .fc-title {
	margin-left: -2px
}

.fc-rtl .fc-timeline-event.fc-not-start .fc-title {
	margin-right: -2px
}

.fc-body .fc-time-area .fc-following,
.fc-timeline-event.fc-not-start .fc-title {
	position: relative
}

.fc-body .fc-time-area .fc-following:before,
.fc-timeline-event.fc-not-start .fc-title:before {
	content: "";
	position: absolute;
	top: 50%;
	margin-top: -5px;
	border: 5px solid #000;
	border-top-color: transparent;
	border-bottom-color: transparent;
	opacity: .5
}

.fc-ltr .fc-body .fc-time-area .fc-following:before,
.fc-ltr .fc-timeline-event.fc-not-start .fc-title:before {
	border-left: 0;
	left: 2px
}

.fc-rtl .fc-body .fc-time-area .fc-following:before,
.fc-rtl .fc-timeline-event.fc-not-start .fc-title:before {
	border-right: 0;
	right: 2px
}

.fc-license-message {
	position: absolute;
	z-index: 99999;
	bottom: 1px;
	left: 1px;
	background: #eee;
	border-color: #ddd;
	border-style: solid;
	border-width: 1px 1px 0 0;
	padding: 2px 4px;
	font-size: 12px;
	border-top-right-radius: 3px
}

.fc-toolbar.fc-header-toolbar {
  margin-bottom: 0.5em;
}

.fc-left {
  margin-bottom: -10px;
  margin-left: 10px;
}

.fc-button {
  font-size: 18px;
  margin-top: 10px;
  font-family: open sans;
  font-weight: bold;
}

.fc-button:disabled {
  opacity: 1;
}

.fc-cell-text {
  font-family: sans serif;
  color: black;
}

.fc-timeline-event.record-state-confirm {
  background-color: #a65742 !important;
  border-color: #a65742 !important;
}

.fc-timeline-event.record-state-done {
  background-color: #2ecc71 !important;
  border-color: #2ecc71 !important;
}

.fc-timeline-event.record-state-desc {
  background-color: grey !important;
  border-color: grey !important;
}

.fc-timeline-event.record-state-draft {
  background-color: #3498db !important;
  border-color: #3498db !important;
  left: 90px;
  right: -155px;
}

.fc-timeline-event.record-state-check-out {
  background-color: #F87B1C !important;
  border-color: #F87B1C !important;
}
