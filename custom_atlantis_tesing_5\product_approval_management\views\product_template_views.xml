<?xml version="1.0" encoding="UTF-8"?>
<odoo>
<!-- Confirm ,Reset button and state field in the product form page-->
    <record id="product_template_form_view" model="ir.ui.view">
        <field name="name">product.template.view.form.inherit.product.approval.management</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <header>
                <button name="action_confirm_product_approval" type="object"
                        attrs="{'invisible': [('approve_state','!=','draft')]}"
                        string="Confirm"
                        groups="product_approval_management.product_template_approver"/>
                <button name="action_reset_product_approval" type="object"
                        attrs="{'invisible': [('approve_state','!=','confirmed')]}"
                        string="Reset To Draft"
                        groups="product_approval_management.product_template_approver"/>
                <field name="approve_state" widget="statusbar"
                       statusbar_visible="draft,confirmed"/>
            </header>
        </field>
    </record>
<!--    Search and group by option in the product-->
    <record id="product_template_search_view" model="ir.ui.view">
        <field name="name">product.template.view.search.inherit.product.approval.management</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
              <xpath expr="//search/group/filter[@name='type']" position="after">
                <filter string="Status" name="approve_state" context="{'group_by':'approve_state'}"/>
            </xpath>
              <xpath expr="//search/filter[@name='consumable']" position="after">
                <filter string="Status" name="approve_state"  domain="[('approve_state', 'in', ['draft', 'confirmed'])]"/>
            </xpath>
        </field>
    </record>
<!--    Bulk product confirm by client action-->
    <record id="action_product_approval_management" model="ir.actions.server">
        <field name="name">Confirm</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="code">model.action_confirm_products()</field>
        <field name="binding_model_id" ref="product.model_product_template"/>
        <field name="groups_id" eval="[(4, ref('product_approval_management.product_template_approver'))]"/>
    </record>
</odoo>
