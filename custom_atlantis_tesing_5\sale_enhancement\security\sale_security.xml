<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.rule" id="sale_shop_comp_rule">
            <field name="name">Sale Shop multi-company</field>
            <field name="model_id" ref="model_sale_shop"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
        </record>
    </data>
</odoo>