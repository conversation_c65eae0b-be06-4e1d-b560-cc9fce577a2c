<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="group_journal_restrict_feature" model="res.groups">
        <field name="name">Enable Journal Restrict Feature</field>
    </record>

    <record id="group_access_of_assign_user" model="res.groups">
        <field name="name">Access of Assign User in Journal</field>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

</odoo>
