# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_no_negative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-11-15 15:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: stock_no_negative
#. odoo-python
#: code:addons/stock_no_negative/models/stock_quant.py:0
#, python-format
msgid " lot {}"
msgstr ""

#. module: stock_no_negative
#: model:ir.model.fields,field_description:stock_no_negative.field_product_category__allow_negative_stock
#: model:ir.model.fields,field_description:stock_no_negative.field_product_product__allow_negative_stock
#: model:ir.model.fields,field_description:stock_no_negative.field_product_template__allow_negative_stock
#: model:ir.model.fields,field_description:stock_no_negative.field_stock_location__allow_negative_stock
msgid "Allow Negative Stock"
msgstr "Negative Lagerbestände erlauben"

#. module: stock_no_negative
#: model:ir.model.fields,help:stock_no_negative.field_product_category__allow_negative_stock
msgid ""
"Allow negative stock levels for the stockable products attached to this "
"category. The options doesn't apply to products attached to sub-categories "
"of this category."
msgstr ""
"Negative Lagerbestände für lagerbare Produkte dieser Kategorie erlauben. "
"Diese Option gilt nicht für Produkte, die zu Unterkategorien dieser "
"Kategorie gehören."

#. module: stock_no_negative
#: model:ir.model.fields,help:stock_no_negative.field_stock_location__allow_negative_stock
msgid ""
"Allow negative stock levels for the stockable products attached to this "
"location."
msgstr ""
"Negative Lagerbestände für die lagerfähigen Produkte erlauben, die mit "
"diesem Ort verbunden sind."

#. module: stock_no_negative
#: model:ir.model.fields,help:stock_no_negative.field_product_product__allow_negative_stock
#: model:ir.model.fields,help:stock_no_negative.field_product_template__allow_negative_stock
msgid ""
"If this option is not active on this product nor on its product category and"
" that this product is a stockable product, then the validation of the "
"related stock moves will be blocked if the stock level becomes negative with"
" the stock move."
msgstr ""
"Wenn diese Option weder für dieses Produkt noch für seine Produktkategorie "
"aktiviert ist und es sich bei diesem Produkt um ein lagerfähiges Produkt "
"handelt, dann wird die Validierung der damit verbundenen Lagerbewegungen "
"blockiert, wenn der Lagerbestand durch die Lagerbewegung negativ wird."

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_stock_location
msgid "Inventory Locations"
msgstr "Lagerorte"

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_product_category
msgid "Product Category"
msgstr "Produktkategorie"

#. module: stock_no_negative
#: model:ir.model,name:stock_no_negative.model_stock_quant
msgid "Quants"
msgstr "Quanten"

#. module: stock_no_negative
#. odoo-python
#: code:addons/stock_no_negative/models/stock_quant.py:0
#, python-format
msgid ""
"You cannot validate this stock operation because the stock level of the "
"product '{name}'{name_lot} would become negative ({q_quantity}) on the stock"
" location '{complete_name}' and negative stock is not allowed for this "
"product and/or location."
msgstr ""
"Diese Aktion kann nicht ausgeführt werden, da der Lagerbestand von "
"'{name}'{name_lot} im Lager '{complete_name}' negativ werden würde "
"({q_quantity}). Negative Lagerbestände sind für dieses Produkt oder in "
"diesem Lager nicht möglich."
