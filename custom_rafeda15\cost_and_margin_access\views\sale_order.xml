<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="sale_order_form_view_margin_inherit">
            <field name="name">sale.order.form.view.margin.inherit</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order"/>
            <field name="arch" type="xml">
                <xpath expr="//label[@for='margin']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.margin_group</attribute>
                </xpath>
                <xpath expr="//field[@name='margin']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.margin_group</attribute>
                </xpath>
                <xpath expr="//span[@class='oe_inline']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.margin_group</attribute>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="sale_order_form_view_margin_inherit_order">
            <field name="name">sale.order.form.view.margin.inherit.order</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='margin']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.margin_group</attribute>
                </xpath>
                <xpath expr="//field[@name='margin_percent']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.margin_group</attribute>
                </xpath>
                <xpath expr="//field[@name='order_line']/form//field[@name='purchase_price']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.cost_price_group</attribute>
                </xpath>
                <xpath expr="//field[@name='order_line']/tree//field[@name='purchase_price']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.cost_price_group</attribute>
                </xpath>
            </field>
        </record>

        <record model="ir.ui.view" id="sale_order_form_view_margin_inherit_order_line">
            <field name="name">sale.order.form.view.margin.inherit.order.line</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='order_line']/form//field[@name='purchase_price']" position="attributes">
                    <attribute name="groups">cost_and_margin_access.cost_price_group</attribute>
                </xpath>
            </field>
        </record>

    </data>
</odoo>
