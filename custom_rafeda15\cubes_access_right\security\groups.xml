<odoo>
    <data>

        <record id="product_cost_group" model="res.groups">
            <field name="name">Product Cost</field>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="product_purchase_group" model="res.groups">
            <field name="name">Product Purchase Tab</field>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

    </data>
</odoo>