<odoo>
    <data>

        <record id="product_only_form_view_access_inherit" model="ir.ui.view">
            <field name="name">product.only.form.view.global.discount.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='purchase']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_purchase_group</attribute>
                </xpath>

                <xpath expr="//label[@for='standard_price']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_cost_group</attribute>
                </xpath>

                <xpath expr="//div[@name='standard_price_uom']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_cost_group</attribute>
                </xpath>

            </field>
        </record>

        <record id="product_template_tree_inherit" model="ir.ui.view">
            <field name="name">product.template.tree.inherit</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                 <xpath expr="//field[@name='standard_price']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_cost_group</attribute>
                </xpath>
            </field>
        </record>

        <record id="product_normal_form_view_access_inherit" model="ir.ui.view">
            <field name="name">product.only.form.view.global.discount.inherit</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_normal_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='purchase']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_purchase_group</attribute>
                </xpath>

                <xpath expr="//label[@for='standard_price']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_cost_group</attribute>
                </xpath>

                <xpath expr="//div[@name='standard_price_uom']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_cost_group</attribute>
                </xpath>

            </field>
        </record>

        <record id="product_product_tree_inherit" model="ir.ui.view">
            <field name="name">product.product.tree.inherit</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_product_tree_view"/>
            <field name="arch" type="xml">
                 <xpath expr="//field[@name='standard_price']" position="attributes">
                    <attribute name="groups">cubes_access_right.product_cost_group</attribute>
                </xpath>
            </field>
        </record>



    </data>
</odoo>