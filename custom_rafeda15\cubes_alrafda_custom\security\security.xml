<odoo>
    <data>
        <record model="ir.module.category" id="module_unit_price_readonly">
            <field name="name">Unit Price Readonly</field>
            <field name="description">Unit Price Readonly</field>
            <field name="sequence">45</field>
        </record>

        <record id="group_sale_price_readonly" model="res.groups">
            <field name="name">Sale Price Readonly</field>
            <field name="category_id" ref="module_unit_price_readonly"/>
        </record>

        <record id="group_invoice_price_readonly" model="res.groups">
            <field name="name">Invoice Price Readonly</field>
            <field name="category_id" ref="module_unit_price_readonly"/>
        </record>

          <record id="sale_margin_group" model="res.groups">
        <field name="name">Sale Margin</field>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>


    </data>
</odoo>