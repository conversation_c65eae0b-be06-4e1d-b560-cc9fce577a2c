<?xml version="1.0"?>
<odoo>

    <record id="view_move_form_inherited_x1" model="ir.ui.view">
        <field name="name">account.move.form.x1</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="partner_credit_x"/>
            </xpath>


            <xpath expr="//notebook/page[@id='invoice_tab']/field[@name='invoice_line_ids']/tree/field[@name='price_unit']" position="replace">
                <field name="readonly_group_invoice" invisible="1"/>
                <field name="price_unit"
                       attrs="{'readonly': [('readonly_group_invoice', '=', True)]}"
                       string="Price"/>
            </xpath>
            <xpath expr="//notebook/page[@id='invoice_tab']/field[@name='invoice_line_ids']/form//field[@name='price_unit']" position="replace">
                <field name="readonly_group_invoice" invisible="1"/>
                <field name="price_unit"
                       attrs="{'readonly': [('readonly_group_invoice', '=', True)]}"
                       string="Price"/>
            </xpath>


        </field>
    </record>

</odoo>
