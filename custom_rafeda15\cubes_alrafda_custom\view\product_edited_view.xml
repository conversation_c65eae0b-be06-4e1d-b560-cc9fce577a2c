<?xml version="1.0"?>
<odoo>

    <record id="product_template_form_view_inherited_x1" model="ir.ui.view">
        <field name="name">product.template.form.x1</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='standard_price_uom']" position="replace">

                <div name="standard_price_uom" groups="base.group_user"
                     attrs="{'invisible': [('product_variant_count', '&gt;', 1), ('is_product_variant', '=', False)], 'readonly': [('readonly_group', '=', True)]}"
                     class="o_row">
                    <field name="standard_price" widget='monetary'
                           options="{'currency_field': 'cost_currency_id', 'field_digits': True}"
                            attrs="{'readonly': [('readonly_group', '=', True)]}"/>
                    <span groups="uom.group_uom" class="oe_read_only">per
                        <field name="uom_name"/>
                    </span>
                </div>

                <field name="readonly_group" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='list_price']" position="attributes">
                <attribute name="attrs">{'readonly': [('readonly_group', '=', True)]}</attribute>
            </xpath>
        </field>
    </record>

    <record id="product_normal_form_view_inherited_x1" model="ir.ui.view">
        <field name="name">product.product.form.x1</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='list_price']" position="before">

                <field name="readonly_group" invisible="1"/>

            </xpath>
            <xpath expr="//field[@name='list_price']" position="attributes">
                <attribute name="attrs">{'readonly': ['|',('product_variant_count', '&gt;', 1),('readonly_group', '=', True)]}</attribute>
            </xpath>
            <xpath expr="//field[@name='lst_price']" position="attributes">
                <attribute name="attrs">{'readonly': [('readonly_group', '=', True)]}</attribute>
            </xpath>
            <xpath expr="//field[@name='categ_id']" position="before">
                <field name="last_purchase_price"/>
                <field name="last_purchase_currency"/>
            </xpath>
        </field>
    </record>

</odoo>
