<?xml version="1.0"?>
<odoo>


    <menuitem id="stock.menu_valuation" name="Inventory Report"
              groups="cubes_alrafda_stock_groups.group_inventory_report"
              parent="stock.menu_warehouse_report" sequence="100"
              action="stock.action_view_quants"/>


    <menuitem id="stock.menu_action_inventory_tree"
              name="Inventory Adjustments"
              groups="cubes_alrafda_stock_groups.group_inventory_update"
              parent="stock.menu_stock_warehouse_mgmt" sequence="30"
              action="stock.action_view_inventory_tree"/>

    <record id="cubes_alrafda_form_view_procurement_button" model="ir.ui.view">
        <field name="name">product.template.alrafda.cubes</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="stock.product_template_form_view_procurement_button"/>
        <field name="arch" type="xml">

            <xpath expr="//header/button[2]" position="attributes">
                <attribute name="groups">cubes_alrafda_stock_groups.group_inventory_update</attribute>
            </xpath>
            <xpath expr="//header/button[3]" position="attributes">
                <attribute name="groups">cubes_alrafda_stock_groups.group_inventory_update</attribute>
            </xpath>


        </field>
    </record>


    <record id="cubes_alrafda_view_stock_quant_tree_inventory" model="ir.ui.view">
        <field name="name">stock.quant.alrafda.cubes</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_tree_inventory_editable"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='inventory_quantity']" position="attributes">
                <attribute name="groups">cubes_alrafda_stock_groups.group_inventory_update</attribute>
            </xpath>
            <xpath expr="//button[@name='action_set_inventory_quantity']" position="attributes">
                <attribute name="groups">cubes_alrafda_stock_groups.group_inventory_update</attribute>
            </xpath>
        </field>
    </record>


</odoo>

