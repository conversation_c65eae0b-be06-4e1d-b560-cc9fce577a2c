<?xml version="1.0"?>
<odoo>

    <record id="view_stock_quant_tree_editable_x1" model="ir.ui.view">
        <field name="name">stock.quant.tree.x10</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="ah_product_category"/>
            </xpath>
        </field>
    </record>

    <record id="quant_search_view_x1" model="ir.ui.view">
        <field name="name">stock.quant.search.x1</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.quant_search_view"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='productgroup']" position="after">
                <filter string='Product Category' name="productcateg" domain="[]"
                        context="{'group_by': 'ah_product_category'}"/>
            </xpath>
        </field>
    </record>

    <record id="stock.action_view_quants" model="ir.actions.server">
        <field name="code">action = model.with_context(
                search_default_locationgroup=1,
                search_default_productcateg=1,
            ).action_view_quants()
        </field>
    </record>

</odoo>
