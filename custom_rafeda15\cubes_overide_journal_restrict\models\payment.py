# -*- coding: utf-8 -*-
# Part of Softhealer Technologies.

from odoo import api, fields, models

class AccountPayment(models.Model):
    _inherit = 'account.payment'

    def get_journals(self):
        journals = self.env['account.journal'].search([('type', 'in', ('bank','cash'))])
        lst_journals = []
        for elem in journals:
            lst_journals.append((str(elem.id), str(elem.name)))
        # print(lst_journals)

        return lst_journals

    @api.model
    def call_super(self):
        return self.sudo(1).get_journals()

    select_dst_journal = fields.Selection(call_super, string= "Select Destination Journals")

    destination_journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Destination Journal',
        # domain="[('type', 'in', ('bank','cash')), ('company_id', '=', company_id), ('id', '!=', journal_id)]",
        check_company=True,
    )

    @api.onchange('select_dst_journal')
    def change_destination_account_id(self):
        get_journal = self.with_user(1).env['account.journal'].search([('id','=',int(self.select_dst_journal))])
        self.destination_journal_id = get_journal



