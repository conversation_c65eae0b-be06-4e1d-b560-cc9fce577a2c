<odoo>
    <data>
        <record id="view_product_template_inherited_form" model="ir.ui.view">
            <field name="name">product_template_inherited_form</field>
            <field name="model">product.template</field>
            <field name="type">form</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="groups_id" eval="[(6, 0, [ref('product_management_group_edit')])]"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="attributes">
                    <attribute name="edit">0</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_product_product_inherited_form" model="ir.ui.view">
            <field name="name">product.product.inherited.form</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_normal_form_view"/>
            <field name="groups_id" eval="[(6, 0, [ref('product_management_group_edit')])]"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="attributes">
                    <attribute name="edit">0</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>