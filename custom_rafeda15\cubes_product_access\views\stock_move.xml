<?xml version="1.0"?>
<odoo>

    <!-- <record id="stock_view_order_form_inherited" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="has_create_product" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='product_id']" position="after">
                <field name="copy_product_id"
                       column_invisible="parent.has_create_product"
                       options="{'no_create': True, 'no_open': True}"/>
            </xpath>

            <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='product_id']"  position="attributes">
                <attribute name="column_invisible">not parent.has_create_product</attribute>
            </xpath>

        </field>
    </record> -->

</odoo>
