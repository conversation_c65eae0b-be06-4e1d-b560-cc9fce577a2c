<?xml version="1.0"?>
<odoo>

    <record id="view_order_form_inherited_x1" model="ir.ui.view">
        <field name="name">sale.order.form.x1</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/form/group/group/field[@name='product_uom_readonly']" position="before">
<!--                <field name="qty_other_stock_available" invisible="1"/>-->
                <field name="warehouse_quantity1"/>
                <field name="qty_available_forcasted" />

            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='product_uom_qty']" position="before">
<!--                <field name="qty_other_stock_available" invisible="1"/>-->
                <field name="warehouse_quantity1"/>
                <field name="qty_available_forcasted" />

            </xpath>
            <!--            <xpath expr="//field[@name='product_uom_readonly']" position="after">-->
            <!--                <field name="qty_other_stock_available"/>-->
            <!--            </xpath>-->
        </field>
    </record>
      <!--Extending existing form view for Quantity per Warehouse field-->
    <record id="product_template_form_view_inherit_warehouse_quantity" model="ir.ui.view">
        <field name="name">product.template.common.form.warehouse.quantity</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='type']" position="after">
                  <field name="warehouse_quantity" invisible="1"/>
            </xpath>
        </field>
    </record>

    <!--Extending existing kanban view for Quantity per Warehouse field-->
    <record id="product_template_kanban_view_inherit_warehouse_quantity" model="ir.ui.view">
        <field name="name">product.template.kanban.warehouse.quantity</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_kanban_view"/>
        <field name="arch" type="xml">
            <div name="product_lst_price" position="after">
                <field name="warehouse_quantity" invisible="1"/>
            </div>
        </field>
    </record>

</odoo>
