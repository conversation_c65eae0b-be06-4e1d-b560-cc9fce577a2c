=====================
Product Brand Manager
=====================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:0af72380688f727e73693c613601f8634e238fd0fea326213bd97937a6906783
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fbrand-lightgray.png?logo=github
    :target: https://github.com/OCA/brand/tree/15.0/product_brand
    :alt: OCA/brand
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/brand-15-0/brand-15-0-product_brand
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/brand&target_branch=15.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows odoo users to easily manage product brands.

**Table of contents**

.. contents::
   :local:

Usage
=====

To create a new brand:

#. Go to **Sales > Configuration > Products > Product Brands**.
#. You can set its logo, associate a partner and add a description.

*Note:* You must have the **Sales** addon installed.

To add a product to a brand:

#. Go to the product itself and edit.
#. Below the product's name there is a Brand field where you can pick the one
   the product belongs to.

To see the sales report based on brand dimension:

#. Go to **Sales > Reporting > Sales**.
#. There you can **Group by** brand or add it as a dimension in the pivot view.

To see the invoice report based on brand dimension:

#. Go to **Invoicing > Reporting > Management > Invoice Analysis**.
#. There you can **Group by** brand or add it as a dimension in the pivot view.

Known issues / Roadmap
======================

* Add a field with brands associated to a Customer or Supplier on
  the Customers/Suppliers Form View.
* Fix smart button alignment in brand form view

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/brand/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/brand/issues/new?body=module:%20product_brand%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* NetAndCo
* Akretion
* Prisnet Telecommunications SA
* MONK Software
* SerpentCS Pvt. Ltd.
* Tecnativa
* Kaushal Prajapati

Contributors
~~~~~~~~~~~~

* Mathieu Lemercier <<EMAIL>>
* Franck Bret <<EMAIL>>
* Seraphine Lantible <<EMAIL>>
* Gunnar Wagner <<EMAIL>>
* Leonardo Donelli <<EMAIL>>
* Serpent Consulting Services Pvt. Ltd. <<EMAIL>>
* Marcelo Pickler <<EMAIL>>
* Andrius Laukavičius <<EMAIL>> (Boolit)
* Daniel Campos <<EMAIL>>
* `Tecnativa <https://www.tecnativa.com>`_

  * David Vidal
  * João Marques

* Kaushal Prajapati <<EMAIL>>
* `Jarsa <https://www.jarsa.com>`_

  * Alan Ramos
* `NuoBiT Solutions <https://www.nuobit.com>`_

  * Eric Antones <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/brand <https://github.com/OCA/brand/tree/15.0/product_brand>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
