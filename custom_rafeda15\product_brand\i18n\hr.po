# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_brand
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-07-29 02:55+0000\n"
"PO-Revision-Date: 2017-07-29 02:55+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2017\n"
"Language-Team: Croatian (https://www.transifex.com/oca/teams/23907/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_product_brand
#: model:ir.model.fields,field_description:product_brand.field_account_invoice_report__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_template__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_sale_report__product_brand_id
#: model_terms:ir.ui.view,arch_db:product_brand.product_template_form_brand_add
#: model_terms:ir.ui.view,arch_db:product_brand.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_order_product_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_template_search_brand
msgid "Brand"
msgstr "Brand"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__name
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Brand Name"
msgstr "Naziv branda"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_brand_products
#: model:ir.model.fields,field_description:product_brand.field_product_brand__product_ids
msgid "Brand Products"
msgstr "Proizvodi branda"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__description
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Description"
msgstr "Opis"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__id
msgid "ID"
msgstr "ID"

#. module: product_brand
#: model:ir.model,name:product_brand.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand____last_update
msgid "Last Modified on"
msgstr "Zadnje ažurirano"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
#, fuzzy
msgid "Logo"
msgstr "Logo sličica"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__logo
msgid "Logo File"
msgstr "Logo sličica"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__products_count
msgid "Number of products"
msgstr "Broj proizvoda"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__partner_id
msgid "Partner"
msgstr "Partner"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_single_product_brand
#: model:ir.model,name:product_brand.model_product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.product_brand_search_form_view
msgid "Product Brand"
msgstr "Brand proizvoda"

#. module: product_brand
#: model:ir.ui.menu,name:product_brand.menu_product_brand
msgid "Product Brands"
msgstr "Brandovi proizvoda"

#. module: product_brand
#: model:ir.model,name:product_brand.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
msgid "Products"
msgstr "Proizvodi"

#. module: product_brand
#: model:ir.model,name:product_brand.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,help:product_brand.field_product_template__product_brand_id
msgid "Select a brand for this product"
msgstr "Odaberite brand za ovaj proizvod"

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_brand__partner_id
msgid "Select a partner for this brand if any."
msgstr "Odaberite partnera za ovaj brand ako ga ima."

#~ msgid "product.brand"
#~ msgstr "product.brand"
