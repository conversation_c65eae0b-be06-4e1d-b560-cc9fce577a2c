# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_brand
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-07-29 02:55+0000\n"
"PO-Revision-Date: 2023-06-08 09:11+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_product_brand
#: model:ir.model.fields,field_description:product_brand.field_account_invoice_report__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_template__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_sale_report__product_brand_id
#: model_terms:ir.ui.view,arch_db:product_brand.product_template_form_brand_add
#: model_terms:ir.ui.view,arch_db:product_brand.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_order_product_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_template_search_brand
msgid "Brand"
msgstr "Marca"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__name
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Brand Name"
msgstr "Marchio"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_brand_products
#: model:ir.model.fields,field_description:product_brand.field_product_brand__product_ids
msgid "Brand Products"
msgstr "Prodotti della Marca"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_date
msgid "Created on"
msgstr "Creato il"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__description
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Description"
msgstr "Descrizione"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__id
msgid "ID"
msgstr ""

#. module: product_brand
#: model:ir.model,name:product_brand.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand____last_update
msgid "Last Modified on"
msgstr "Ultima Modifica il"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
#, fuzzy
msgid "Logo"
msgstr "File del Logo"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__logo
msgid "Logo File"
msgstr "File del Logo"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__products_count
msgid "Number of products"
msgstr "Numero di prodotti"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__partner_id
msgid "Partner"
msgstr "Partner"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_single_product_brand
#: model:ir.model,name:product_brand.model_product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.product_brand_search_form_view
msgid "Product Brand"
msgstr "Marca del prodotto"

#. module: product_brand
#: model:ir.ui.menu,name:product_brand.menu_product_brand
msgid "Product Brands"
msgstr "Marche del prodotto"

#. module: product_brand
#: model:ir.model,name:product_brand.model_product_template
msgid "Product Template"
msgstr ""

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
msgid "Products"
msgstr "Prodotti"

#. module: product_brand
#: model:ir.model,name:product_brand.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,help:product_brand.field_product_template__product_brand_id
msgid "Select a brand for this product"
msgstr "Scegliere una marca per questo prodotto"

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_brand__partner_id
msgid "Select a partner for this brand if any."
msgstr "Scegliere un partner per questa marca ove presente."
