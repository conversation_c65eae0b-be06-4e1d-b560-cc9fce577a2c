# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_brand
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2019-02-12 02:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: nb_NO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 3.4\n"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_product_brand
#: model:ir.model.fields,field_description:product_brand.field_account_invoice_report__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_template__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_sale_report__product_brand_id
#: model_terms:ir.ui.view,arch_db:product_brand.product_template_form_brand_add
#: model_terms:ir.ui.view,arch_db:product_brand.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_order_product_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_template_search_brand
msgid "Brand"
msgstr "Merke"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__name
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Brand Name"
msgstr "Merkenavn"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_brand_products
#: model:ir.model.fields,field_description:product_brand.field_product_brand__product_ids
msgid "Brand Products"
msgstr "Merkeprodukter"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__description
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Description"
msgstr "Beskrivelse"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__id
msgid "ID"
msgstr "ID"

#. module: product_brand
#: model:ir.model,name:product_brand.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Fakturastatistikk"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand____last_update
msgid "Last Modified on"
msgstr "Sist modifisert"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
msgid "Logo"
msgstr "Logo"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__logo
msgid "Logo File"
msgstr "Logo-fil"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__products_count
msgid "Number of products"
msgstr "Antall produkter"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__partner_id
msgid "Partner"
msgstr "Partner"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_single_product_brand
#: model:ir.model,name:product_brand.model_product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.product_brand_search_form_view
msgid "Product Brand"
msgstr "Produktmerke"

#. module: product_brand
#: model:ir.ui.menu,name:product_brand.menu_product_brand
msgid "Product Brands"
msgstr "Produktmerker"

#. module: product_brand
#: model:ir.model,name:product_brand.model_product_template
msgid "Product Template"
msgstr "Produktmal"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
msgid "Products"
msgstr "Produkter"

#. module: product_brand
#: model:ir.model,name:product_brand.model_sale_report
msgid "Sales Analysis Report"
msgstr "Salgsanalyse-rapport"

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,help:product_brand.field_product_template__product_brand_id
msgid "Select a brand for this product"
msgstr "Velg et merke for produktet"

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_brand__partner_id
msgid "Select a partner for this brand if any."
msgstr "Velg en partner for produktet, om noen."

#~ msgid "product.brand"
#~ msgstr "produkt.merke"
