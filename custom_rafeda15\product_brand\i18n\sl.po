# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_brand
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-27 03:49+0000\n"
"PO-Revision-Date: 2023-03-31 21:30+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Slovenian (https://www.transifex.com/oca/teams/23907/sl/)\n"
"Language: sl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3;\n"
"X-Generator: Weblate 4.14.1\n"

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_product_brand
#: model:ir.model.fields,field_description:product_brand.field_account_invoice_report__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_product_template__product_brand_id
#: model:ir.model.fields,field_description:product_brand.field_sale_report__product_brand_id
#: model_terms:ir.ui.view,arch_db:product_brand.product_template_form_brand_add
#: model_terms:ir.ui.view,arch_db:product_brand.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_order_product_search
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_template_search_brand
msgid "Brand"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__name
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Brand Name"
msgstr ""

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_brand_products
#: model:ir.model.fields,field_description:product_brand.field_product_brand__product_ids
msgid "Brand Products"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__description
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
msgid "Description"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__id
msgid "ID"
msgstr "ID"

#. module: product_brand
#: model:ir.model,name:product_brand.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand____last_update
msgid "Last Modified on"
msgstr "Zadnja sprememba"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__write_date
msgid "Last Updated on"
msgstr "Zadnja posodobitev"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
msgid "Logo"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__logo
msgid "Logo File"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__products_count
msgid "Number of products"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,field_description:product_brand.field_product_brand__partner_id
msgid "Partner"
msgstr ""

#. module: product_brand
#: model:ir.actions.act_window,name:product_brand.action_open_single_product_brand
#: model:ir.model,name:product_brand.model_product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.product_brand_search_form_view
msgid "Product Brand"
msgstr ""

#. module: product_brand
#: model:ir.ui.menu,name:product_brand.menu_product_brand
msgid "Product Brands"
msgstr ""

#. module: product_brand
#: model:ir.model,name:product_brand.model_product_template
msgid "Product Template"
msgstr "Predloga proizvoda"

#. module: product_brand
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_form
#: model_terms:ir.ui.view,arch_db:product_brand.view_product_brand_kanban
msgid "Products"
msgstr ""

#. module: product_brand
#: model:ir.model,name:product_brand.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_product__product_brand_id
#: model:ir.model.fields,help:product_brand.field_product_template__product_brand_id
msgid "Select a brand for this product"
msgstr ""

#. module: product_brand
#: model:ir.model.fields,help:product_brand.field_product_brand__partner_id
msgid "Select a partner for this brand if any."
msgstr ""
