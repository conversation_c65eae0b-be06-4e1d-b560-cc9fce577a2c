# Copyright 2009 NetAndCo (<http://www.netandco.net>).
# <AUTHOR> <EMAIL>
# Copyright 2014 prisnet.ch Ser<PERSON>ine <PERSON>ble <<EMAIL>>
# Copyright 2016 Serpent Consulting Services Pvt. Ltd.
# Copyright 2018 <PERSON> <<EMAIL>>
# Copyright 2019 <PERSON><PERSON><PERSON> <<EMAIL>>
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html)

from odoo import fields, models


class ProductTemplate(models.Model):
    _inherit = "product.template"

    product_brand_id = fields.Many2one(
        "product.brand", string="Brand", help="Select a brand for this product"
    )
