<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="restrictions.list">
      <field name="name">restrictions list</field>
      <field name="model">restrictions.restrictions</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="restrictions.action_window">
      <field name="name">restrictions window</field>
      <field name="res_model">restrictions.restrictions</field>
      <field name="view_mode">tree,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="restrictions.action_server">
      <field name="name">restrictions server</field>
      <field name="model_id" ref="model_restrictions_restrictions"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="restrictions" id="restrictions.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="restrictions.menu_1" parent="restrictions.menu_root"/>
    <menuitem name="Menu 2" id="restrictions.menu_2" parent="restrictions.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="restrictions.menu_1_list" parent="restrictions.menu_1"
              action="restrictions.action_window"/>
    <menuitem name="Server to list" id="restrictions" parent="restrictions.menu_2"
              action="restrictions.action_server"/>
-->
  </data>
</odoo>