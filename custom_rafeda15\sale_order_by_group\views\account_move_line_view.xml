<?xml version="1.0" encoding="utf-8" ?>
<odoo>



<!--General <PERSON>-->
    <record id="view_move_line_tree_grouped_x1" model="ir.ui.view">
        <field name="name">account.move.line.form.branch.x3</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree_grouped"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="branch_id"/>
            </xpath>
        </field>
    </record>

<!--    Journal Entreys &ndash;&gt;-->
    <record id="view_move_line_tree_x1" model="ir.ui.view">
        <field name="name">account.move.line.form.branch.x2</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="branch_id"/>
            </xpath>
        </field>
    </record>


<!--    Partner Ladger-->

    <record id="view_move_line_tree_grouped_partner_x1" model="ir.ui.view">
        <field name="name">account.move.line.form.branch.x1</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree_grouped_partner"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="branch_id"/>
            </xpath>
        </field>
    </record>


    <record id="view_account_move_line_filter_x1" model="ir.ui.view">
        <field name="name">account.move.line.search.branch.x1</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_account_move_line_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="branch_id"/>
            </xpath>

            <xpath expr="//filter[@name='group_by_partner']" position="after">
                <filter string="الفرع" name="group_by_branch_id" domain="[]" context="{'group_by': 'branch_id'}"/>
            </xpath>
        </field>
    </record>




</odoo>
<!--attrs="{'readonly':[('branch_id','!=',False)]}"-->