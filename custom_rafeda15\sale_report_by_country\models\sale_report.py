# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _
from odoo.osv import expression



class SaleReportEdited(models.Model):
    _inherit = "sale.report"

    product_country_id = fields.Many2one('product.product.country', string='بلد المنشاء', readonly=True)
    brand_id = fields.Many2one('product.product.brand', string='الشركة المصنعة', readonly=True)

    def _query(self, with_clause='', fields={}, groupby='', from_clause=''):
        fields['product_country_id'] = ", t.country_id as product_country_id"
        groupby += ', t.country_id'

        fields['brand_id'] = ", t.brand_id as brand_id"
        groupby += ', t.brand_id'
        return super(SaleReportEdited, self)._query(with_clause, fields, groupby, from_clause)


