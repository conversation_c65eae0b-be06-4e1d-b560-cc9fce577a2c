<?xml version="1.0"?>
<odoo>



    <record id="view_order_form_inherited_x2" model="ir.ui.view">
        <field name="name">sale.order.form.x2</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/form/group/group/field[@name='current_original_number']" position="after">
                <field name="product_country_id" invisible="1"/>
                <field name="brand_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/tree/field[@name='current_original_number']" position="after">
                <field name="product_country_id" invisible="1"/>
                <field name="brand_id" invisible="1"/>
            </xpath>
        </field>
    </record>


    <record id="view_order_product_search_x1" model="ir.ui.view">
        <field name="name">sale.report.search.x1</field>
        <field name="model">sale.report</field>
        <field name="inherit_id" ref="sale.view_order_product_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='industry_id']" position="after">
                <field name="brand_id"/>
                <field name="product_country_id"/>
            </xpath>
            <xpath expr="//filter[@name='status']" position="after">
                <filter string="الشركة المصنعة" name="brand_id" context="{'group_by':'brand_id'}"/>
                <filter string="بلد المنشاء" name="product_country_id" context="{'group_by':'product_country_id'}"/>
            </xpath>
        </field>
    </record>

</odoo>
