================
Tracking Manager
================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:13d96cd19605f36b67a3a22430566aed7a49ae0a8ed1ffc1ac63308a4a92a570
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fserver--tools-lightgray.png?logo=github
    :target: https://github.com/OCA/server-tools/tree/15.0/tracking_manager
    :alt: OCA/server-tools
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/server-tools-15-0/server-tools-15-0-tracking_manager
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/server-tools&target_branch=15.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows to track all fields on every model that has a chatter, including one2many and many2many ones. This excludes the computed, readonly, related fields by default.
In addition, line changes of a one2many field can be tracked (e.g. product_uom_qty of an order_line in a sale order).

**Table of contents**

.. contents::
   :local:

Usage
=====

- In setting > models: select a model
- Check "Active" under Custom Tracking.
- You have two options - 1) manually configure tracked fields one by one, or 2) determine tracked fields based on a specific domain.
- For 1) manually configure tracked fields one by one
  - Click on Tracked Fields smart button, and select/unselect Custom Tracking.
- For 2) determine tracked fields based on a specific domain
  - Select "Automatic configuration", and then set the domain accordingly.
  - Click "Update" for the domain to take effect.

.. image:: https://raw.githubusercontent.com/OCA/server-tools/15.0/tracking_manager/static/description/model_view.png

- Then select the fields to track

.. image:: https://raw.githubusercontent.com/OCA/server-tools/15.0/tracking_manager/static/description/fields.png

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/server-tools/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/server-tools/issues/new?body=module:%20tracking_manager%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Akretion

Contributors
~~~~~~~~~~~~

* Kévin Roche <<EMAIL>>
* Sébastien BEAU <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-Kev-Roche| image:: https://github.com/Kev-Roche.png?size=40px
    :target: https://github.com/Kev-Roche
    :alt: Kev-Roche
.. |maintainer-sebastienbeau| image:: https://github.com/sebastienbeau.png?size=40px
    :target: https://github.com/sebastienbeau
    :alt: sebastienbeau

Current `maintainers <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-Kev-Roche| |maintainer-sebastienbeau| 

This module is part of the `OCA/server-tools <https://github.com/OCA/server-tools/tree/15.0/tracking_manager>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
