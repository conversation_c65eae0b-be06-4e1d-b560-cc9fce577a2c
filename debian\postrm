#!/bin/sh

set -e

ODOO_LIB_DIR=/var/lib/odoo
ODOO_USER="odoo"
ODOO_GROUP="odoo"

case "${1}" in
    remove)
        deluser --quiet --system $ODOO_USER || true
        delgroup --quiet --system --only-if-empty $ODOO_GROUP || true
        ;;

    purge)
        if [ -d "$ODOO_LIB_DIR" ]; then
            rm -rf $ODOO_LIB_DIR
        fi
        ;;

    upgrade|failed-upgrade|abort-install|abort-upgrade|disappear)
        ;;

esac

#DEBHELPER#

exit 0
