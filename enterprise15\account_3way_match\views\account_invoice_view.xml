<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_invoice_form_inherit" model="ir.ui.view">
        <field name="name">account.move.form.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@id='other_tab']//field[@name='fiscal_position_id']" position='after'>
                <label for="release_to_pay_manual" attrs="{'invisible': [('move_type', 'not in', ('in_invoice', 'in_refund'))]}"/>
                <div class="o_row" col="4" attrs="{'invisible': [('move_type', 'not in', ('in_invoice', 'in_refund'))]}">
                    <field name="release_to_pay" attrs="{'invisible': True}" />
                    <field name="release_to_pay_manual"/>
                    <label class="font-weight-bold" for="force_release_to_pay" attrs="{'invisible': [('force_release_to_pay', '=', False)]}"/>
                    <field name="force_release_to_pay" attrs="{'invisible': [('force_release_to_pay', '=', False)]}"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="account_invoice_filter_inherit_account_3way_match" model="ir.ui.view">
        <field name="name">account.invoice.select.inherit.account_3way_match</field>
        <field name="mode">primary</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='late']" position='after'>
                <separator/>
                <filter name="bills_to_pay" string="Bills to Pay" domain="['&amp;', '&amp;', ('state', '=', 'posted'), ('payment_state', 'in', ('not_paid', 'partial')), ('release_to_pay','in', ('yes', 'exception'))]"/>
                <filter name="exception" string="Bills in Exception" domain="[('release_to_pay','=', 'exception')]"/>
                <separator/>
            </xpath>
        </field>
    </record>

     <!--This action has been redefined and the account_invoice_filter_inherit_account_3way_match
     created in order to only display 'bills_to_pay' and 'exception' filters
     in the view related to vendor bills, as it makes no sense to propose them
     in the view related to sales invoices, which share the same model.-->
     <record id="account.action_move_in_invoice_type" model="ir.actions.act_window">
        <field name="search_view_id" ref="account_invoice_filter_inherit_account_3way_match"/>
    </record>

     <record id="account.action_move_in_refund_type" model="ir.actions.act_window">
        <field name="search_view_id" ref="account_invoice_filter_inherit_account_3way_match"/>
    </record>

</odoo>
