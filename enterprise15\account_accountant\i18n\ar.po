# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> تسوية "

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">نصيحة: قم بتحديث بنود اليومية بالجملة</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">نصيحة: اعثر على محاسب ليقوم بتسجيل شركة المحاسبة "
"الخاصة بك</b>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""
"<br>تعرف على كيفية إدارة فواتير عملائك في <b>قائمة العملاء/الفواتير</b> "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr "<i>قم بإقفال كافة قيود اليومية</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr "<i>قم بإقفال قيود معينة من اليومية</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr "<span class=\"o_form_label\">السنة المالية</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">اعثر على محاسب</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr "<span class=\"tip_button_text\">قم بتسجيل شركة المحاسبة الخاصة بك</span>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr "<strong><b>عمل رائع!</b> لقد تخطيت كافة مراحل هذه الجولة.</strong>"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "يجب أن تتضمن التسوية بنديْ حركة على الأقل. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_account
#, python-format
msgid "Account"
msgstr "حساب"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "مجموعات حساب"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr "الفواتير المتوقعة للحساب"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "أداة تسوية الحسابات "

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "علامات تصنيف الحساب "

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "المحاسبة "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr "إقفال الفترة المحاسبية "

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr "تواريخ إقفال الحسابات "

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "إضافة علامة تصنيف جديدة "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr "تاريخ إقفال كافة المستخدمين "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "تمت مطابقة كافة الفواتير والمدفوعات. الأرصدة الخاصة بحساباتك سليمة. "

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "السماح بإنشاء سنوات مالية أطول أو أقصر من عام"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Amount"
msgstr "المبلغ"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "حساب تحليلي"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "علامات التصنيف التحليلية. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "المحاسبة الأنجلو-ساكسونية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__attachment_ids
msgid "Attachments"
msgstr "المرفقات "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "تحركات النقد والبنوك "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "التسوية البنكية"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
msgid "Bank Statement"
msgstr "كشف حساب البنك"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Cancel"
msgstr "إلغاء"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "تغيير تاريخ الإقفال"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Check & validate the bill. If no vendor has been found, add one before "
"validating."
msgstr ""
"تحقق من الفاتورة وقم بتصديقها. في حال عدم إيجادك لمورّد، قم بإضافة واحد قبل "
"التصديق. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "التحقق من الكل "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "تأكد من عدم وجود بنود في كشف الحساب البنكي "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Check them"
msgstr "تفقدهم "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr "اختر بند لعرض مرفقاته"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "اختر حساب قيد مقابل أو قم بإنشاء عملية شطب "

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "انقر هنا لإنشاء سنة مالية جديدة."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"انقر هنا لإيجاد محاسب أو إذا كنت ترغب في إدراج خدماتك المحاسبية على أودو "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "إغلاق "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "إغلاق الكشف"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "شركات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
msgid "Company"
msgstr "شركة "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "تهانينا، لقد انتهيت!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""
"تهانينا، لقد انتهيت! لقد قمت بتسوية %s معاملة في %s، والذي يعادل %s معاملةً "
"في الثانية. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr "قم بربط مصرفك لتتمكن من رؤية أحدث معاملاتك. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "قيم مقابلة"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Create Reconciliation Model"
msgstr "قم بإنشاء أداة التسوية "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "أنشئ حساب قيد مقابل "

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "إنشاء مجموعة حساب جديدة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "إنشاء نموذج "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"قم بإنشاء فاتورة المورّد الأولى. <br/><br/><i>نصيحة: إذا لم يكن لديكا فاتورة"
" في متناول اليد، فبإمكانك الاستعانة بنموذج الفاتورة الخاص بنا.</i> "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "مطابقة العملاء / الموردين"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "تحديد السنوات المالية التي تزيد أو تقل عن السنة."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Description"
msgstr "الوصف"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "الملخص"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_accountant
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "لا تملك صلاحيات الوصول. تخط هذه البيانات لبريد الملخص للمستخدم. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "تاريخ الاستحقاق"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "تاريخ الانتهاء، ضمن السنة المالية. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"سوف يكون لكل فاتورة وعملية دفع قبل هذا التاريخ حالة ’من تطبيق الفوترة‘ والتي"
" ستخفي كافة القيود المحاسبية المتعلقة بها. استخدم هذا الخيار بعد تثبيت تطبيق"
" المحاسبة إذا كنت تستخدم تطبيق الفوترة وحده من قبل، قبل إدخالك لكافة بياناتك"
" المحاسبية الفعلية في أودو. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "رابط خارجي"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "قم بالتصفية حسب الحساب أو العلامة أو الشريك أو المبلغ،... "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "سنة مالية"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "السنة المالية 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "السنوات المالية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "آخر أيام السنة المالية"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "آخر شهور السنة المالية"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"قم باختيار سجلات متعددة من أي نافذة عرض القائمة، وستصبح القائمة قابلة "
"للتحرير. إذا قمت بتحديث إحدى الخلايا، يتم تحديث كافة السجلات المختارة دفعة "
"واحدة. استخدم هذه الخاصية لتحديث عدة بنود في اليومية من دفتر الأستاذ العام "
"أو أي نافذة عرض لليومية. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "من الآن فصاعداً، قد ترغب في: "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Get back to the dashboard using your previous path…"
msgstr "عد إلى لوحة البيانات باستخدام المسار السابق... "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "الذهاب إلى كشوفات الحساب البنكي "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr "اذهب إلى الفوترة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "عمل رائع!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Great! Let’s continue."
msgstr "رائع! فلنكمل الآن. "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
msgid "ID"
msgstr "المُعرف"

#. module: account_accountant
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"تاريخ السنة المالية غير صحيح: اليوم المدخل غير موجود في هذا الشهر. "
"الشهر:%s;اليوم:%s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "الحد الأدنى لتبديل الفوترة "

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr "يلزم تحديد حساب ودفتر يومية لإنشاء عملية شطب. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "العناصر"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_journal
#, python-format
msgid "Journal"
msgstr "اليومية "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr "تاريخ إقفال قيود اليومية "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "القيود اليومية المُراد تسويتها"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "حساب المؤشر الرئيسي للأداء للقيمة النقدية للبنك "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Label"
msgstr "بطاقة عنوان"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "اليوم الأخير"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "آخر تسوية:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr "فلنقم بأتمتة فواتيرك ومعاملاتك البنكية وعملياتك المحاسبية. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "فلنعد إلى لوحة البيانات. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s reconcile the fetched bank transactions."
msgstr "لنقم بتسوية معاملات البنك التي تم إحضارها. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr "فلنرَ كيف تبدو إحدى الفواتير في طريقة عرض الاستمارة. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""
"فلنقم باستخدام الذكاء الاصطناعي لتعبئة الاستمارة. <br/><br/><i>نصيحة: انتظر "
"لبضع ثوان ثم حاول مجدداً.</i> "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "تحميل المزيد... ("

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "تاريخ الإقفال لكافة المستخدمين"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "تاريخ الإقفال لغير المرشدين "

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "تواريخ الإقفال"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr "إقفال الإدارة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr "العمليات اليدوية "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"Match statement with existing lines on receivable/payable accounts<br>* "
"Black line: existing journal entry that should be matched<br>* Blue lines: "
"existing payment that should be matched"
msgstr ""
"قم بمطابقة كشوفات الحسابات مع البنود الموجودة بالفعل في الحسابات مستحقة "
"القبض وحسابات الدائنين <br>* الخط الأسود: بند يومية موجود بالفعل بحاجة إلى "
"المطابقة <br>* الخطوط الزرقاء: دفعة موجودة بالفعل بحاجة إلى المطابقة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""
"المطابقة مع القيود التي لا تندرج ضمن الحسابات مستحقة القبض أو حسابات "
"الدائنين. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "مطابقة متنوعة"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "تعديل النماذج "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "تحريك المرفق "

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
msgid "Name"
msgstr "الاسم"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "جديد"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "No attachments linked."
msgstr "لم يتم ربط أي مرفقات. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"لايمكن للمستخدمين تعديل قيود اليومية المرتبطة بضرائب سابقة وشاملة لهذا "
"التاريخ. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"لا يمكن لأي من المستخدمين، بما في ذلك المرشدين، تعديل حسابات سابقة وشاملة "
"لهذا التاريخ. استخدمه لإغلاق سنة مالية، على سبيل المثال. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Note"
msgstr "ملاحظة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "لا شيء لتفعله!"

#. module: account_accountant
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "مديرو الفوترة وحدهم المصرح لهم بتغيير تواريخ الإقفال! "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"وحدهم المستخدمون ذوو صلاحية 'المرشد' بمقدورهم تحرير حسابات سابقة قبل وحتى "
"هذا التاريخ. استخدمه لإغلاق فترة داخل سنة مالية مفتوحة، على سبيل المثال. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "حساب مفتوح"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Partner"
msgstr "الشريك"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "ادفع "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "مطابقة المدفوعات"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "حالة الدفع قبل التحويل "

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
msgid "Payments"
msgstr "المدفوعات"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "مطابقة المدفوعات"

#. module: account_accountant
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "لا يمكن مطابقة المدفوعات دون عميل "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr "اختر تاريخاً لإقفاله "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr "توقع حسابات فواتير الموردين "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "ضبط الإعدادات المعدة مسبقاً "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification prior to the defined date "
"for all users. As a closed period, all accounting operations are prohibited."
msgstr ""
"يمنع إنشاء قيود اليومية أو تعديلها قبل التاريخ المحدد لكافة المستخدمين، "
"وباعتبارها فترة مقفلة، فإن كافة العمليات المحاسبية محظورة. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation prior to the defined date. Except for "
"Advisors users."
msgstr ""
"يمنع إنشاء قيود اليومية قبل التاريخ المحدد، باستثناء المستخدمين المرشدين. "

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification prior to the defined date (Journal Entries"
" involving taxes). The Tax Return Lock Date is automatically set when the "
"corresponding Journal Entry is posted."
msgstr ""
"يَمنع تعديل إقرار الضرائب قبل التاريخ المحدد (قيود اليومية التي تتضمن "
"الضرائب). يتم تحديد تاريخ إقفال إقرار الضرائب تلقائياً عندما يتم نشر قيد "
"اليومية المقابل له. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Process this transaction."
msgstr "معالجة هذه المعاملة. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/models/account_move.py:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.server,name:account_accountant.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree_grouped
#, python-format
msgid "Reconcile"
msgstr "تسوية"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_manual_reconciliation
#: model:ir.ui.menu,name:account_accountant.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "التسوية"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "التسوية في كشوفات الحساب البنكية "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "سجّل تكاليف البضاعة المباعة في قيود اليومية الخاصة بك "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "الرقم المرجعي "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "المتبقي"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "حفظ"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "حفظ وفتح ملف جديد"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "اختر الشريك"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "اختر شريكاً أو اختر حساب قيد مناظر "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "الإعدادات"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "تخطي"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "بعض الحقول غير محددة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "تاريخ البداية، ضمن السنة المالية. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "السعر يشمل الضريبة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr "تاريخ إغلاق الضريبة"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr "تاريخ إقفال الإقرار الضريبي "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Taxes"
msgstr "الضرائب"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_payment__payment_state_before_switch
msgid ""
"Technical field to keep the value of payment_state when switching from "
"invoicing to accounting (using invoicing_switch_threshold setting field). It"
" allows keeping the former payment state, so that we can restore it if the "
"user misconfigured the switch date and wants to change it."
msgstr ""
"حقل تقني لإبقاء قيمة payment_state عند الانتقال من تطبيق الفوترة إلى تطبيق "
"المحاسبة (استخدام حقل إعداد invoicing_switch_threshold). يسمح بإبقاء حالة "
"الدفع السابقة حتى نتمكن من استعادتها في حال فشل المستخدم في تهيئة تاريخ "
"التحويل بشكل صحيح ورغبته في تغييره. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "هذا في المتوسط"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "لا يعد المبلغ %s مبلغاً جزئياً صالحاً. "

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "يجب ألا يقع تاريخ الانتهاء قبل تاريخ البداية. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr "لن يتم اعتبار الفواتير حتى هذا التاريخ كقيود محاسبية "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the accounts on vendor bill lines based on "
"history of previous bills"
msgstr ""
"سيحاول النظام توقع حسابات فواتير مورديك بناءً على سجل الفواتير السابق. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "لا يوجد شيء لتسويته. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"تعرض هذه الصفحة كافة المعاملات البنكية بانتظار التسوية وتقدمها بواجهة أنيقة."
" "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "هذه الدفعة مُسجلة ولكن لم يتم تسويتها. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This was the model that generated the lines suggested"
msgstr "هذا هو النموذج الذي قام بتوليد البنود المقترحة "

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "نصيحة: قم بتحديث قيود اليومية بالجملة "

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "نصيحة: ابحث عن محاسب أو قم بتسجيل شركتك المحاسبة الخاصة بك "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To Check"
msgstr "للتفقد"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "لتسريع عملية التسوية، حدد"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr "معاملة"

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Transfer Accounts"
msgstr "حسابات وسيطة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#, python-format
msgid "Validate"
msgstr "تصديق "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "تأكيد "

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Write-Off"
msgstr "شطب"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "تاريخ الشطب "

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"لا يمكن أن يكون هناك تداخل بين سنتين ماليتين، الرجاء تصحيح تواريخ بدء و/أو "
"انتهاء سنواتك المالية. "

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reconcile the payable and receivable accounts of multiple "
"partners together at the same time."
msgstr ""
"لا يمكن تسوية الحسابات مستحقة القبض وحسابات الدائنين لعدة شركاء في آن واحد. "

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "You have suspense account moves that match this invoice."
msgstr "لديك تحركات حساب معلق تتطابق مع هذه الفاتورة."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "لقد قمت بتسوية "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "ومتابعة العملاء"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "مثلًا: الرسوم البنكية "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "تمت تسويتها تلقائيًا."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr "تسوية "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "نماذج التسوية"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "متبقيين)"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "ثوان لكل معاملة. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr "بنود كشف الحساب"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "للتفقد"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "to mark this invoice as paid."
msgstr "لتحديد هذه الفاتورة كمدفوعة. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "معاملات في "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "الفواتير غير المدفوعة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "قيود غير مسوّاة "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "فواتير المورد "
