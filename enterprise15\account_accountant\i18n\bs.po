# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_accountant
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Inter-Banks Transfers</span>"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Grupe konta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Računovodstvene oznake"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Account used when transferring between banks"
msgstr "Konto prilikom prenosa sredstava između banki"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Računovodstvo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Anglo-Saksonsko Računovodstvo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Cancel"
msgstr "Otkaži"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than a year"
msgstr ""

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: account_accountant
#: code:addons/account_accountant/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Zadnji datum fiskalne godine"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Zadnji mjesec fiskalne godine"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__transfer_account_id
msgid ""
"Intermediary account used when moving money from a liquidity account to "
"another"
msgstr ""
"Prenosni konto korišćen prilikom prenosa novca sa jednog konta likvidnosti "
"na drugi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Posljednji dan"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last day of your fiscal year, for automatic opening entries"
msgstr ""
"Zadnji dan fiskalne godine, za automatsko otvaranje stavki početnog stanja"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tour.js:130
#, python-format
msgid "Let's start with a new customer invoice."
msgstr "Započnimo sa novom fakturom kupca."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Datum zaključavanja za sve korisnike"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Datum zaključavanja osim za Računovodstvene Savjetnike"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Datumi zaključavanja"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Lock your Fiscal Period"
msgstr "Zaključajte vaš fiskalni period"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Nijedan korisnik, uključujući savjetnike, ne može uređivati knjiženja prije "
"uključujući ovaj datum. Koristite ovo da na primjer zaključate fiskalnu "
"godinu."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Samo korisnici sa 'Savjetnik' grupom može uređivati knjiženja prije i "
"uključujući ovaj datum. Koristite ovu opciju na primjer da zaključate "
"otvorenu fiskalnu godinu."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Only users with the Adviser role can edit accounts prior to and inclusive of"
" this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Samo korisnici sa Savjetnik grupom može uređivati knjiženja prije i "
"uključujući ovaj datum. Koristite ovo na primjer da zaključate otvorenu "
"fiskalnu godinu."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr ""

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_product_product_categories
msgid "Product Categories"
msgstr "Kategorije proizvoda"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tour.js:125
#, python-format
msgid ""
"Ready to discover your new favorite <b>accounting app</b>? Get started by "
"clicking here."
msgstr ""
"Spremni da istražite vašu novu omiljenu <b>računovodstvenu aplikaciju</b>? "
"Započnimo klikom ovdje."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Evidentirajte troškove prodane robe u vašu glavnu knjigu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Sačuvaj"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict vendor bill accounts based on history of "
"previous bills."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__transfer_account_id
msgid "Transfer Account"
msgstr "Konto prenosa novčanih sredstava"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tour.js:135
#, python-format
msgid ""
"Use the path to quickly click back to <b>previous screens</b>, without "
"reloading the page."
msgstr ""
"Koristite putanje da se brzo vratite na <b>prethodni ekran</b>, bez "
"osvježavanja stranice."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tour.js:139
#, python-format
msgid ""
"Your reports are available in real time. <i>No need to close a fiscal year "
"to get a Profit &amp; Loss statement or view the Balance Sheet.</i>"
msgstr ""
"Vaši izvještaji su dostupni u stvarnom vremenu. <i>Nema potrebe da zatvorite"
" poslovnu godinu kako bi ste dobili izvještaj o Dobitku i Gubitku ili "
"vidjeli Bruto Billans.</i>"
