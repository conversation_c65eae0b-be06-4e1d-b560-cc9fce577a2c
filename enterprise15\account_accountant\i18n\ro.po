# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://www.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Reconciliază"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">Sfat: Elemente jurnal de actualizare în masă</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">Sfat: Găsiți un contabil sau înregistrați firma "
"contabilă</b>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""
"<br>Vedeți cum să gestionați facturile clienților dvs. în <b>meniul</b> "
"Clienți/Facturi"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr "<i>Blocați toate intrările în jurnal</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr "<i>Blocați intrările specifice în jurnal</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr "<span class=\"o_form_label\">An Fiscal</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">Găsiți un contabil</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr "<span class=\"tip_button_text\">Înregistrați firma contabilă</span>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Bună treabă!</b>Ați parcurs toate etapele acestui tur.</strong>"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "O reconciliere trebuie să cuprindă cel puțin 2 linii."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_account
#, python-format
msgid "Account"
msgstr "Cont"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Grupuri conturi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr "Facturi Predictive Contabilitate"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Widget Reconciliere Cont"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Etichete contabile"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Contabilitate"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr "Închiderea perioadei contabile"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr "Date închidere contabilitate"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Adaugă o nouă etichetă"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr "Data blocării tuturor utilizatorilor"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Toate facturile și plățile au fost reconciliate, balanța contabilă este "
"curată. "

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr ""
"Permiteți definirea anilor fiscali pentru mai mult sau mai puțin de un an"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Amount"
msgstr "Valoare"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Contul analitic"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "Etichete Analitice."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Contabilitate anglo-saxonă"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__attachment_ids
msgid "Attachments"
msgstr "Atașamente"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Mișcări Bancare și Numerar"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Reconciliere bancă"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
msgid "Bank Statement"
msgstr "Extras bancă"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Cancel"
msgstr "Anulează"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Modificați data de siguranță"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Check & validate the bill. If no vendor has been found, add one before "
"validating."
msgstr ""
"Verificați și validați factura. Dacă nu a fost găsit niciun furnizor, "
"adăugați unul înainte de validare."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "Verificați toate"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "Verificați dacă nu aveți linii de extras bancare de"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Check them"
msgstr "Verificați-le"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr "Alegeți o linie pentru a previzualiza fișierele atașate."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Alegeți omologul sau Creați o pierdere"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Clic aici pentru a crea un nou an fiscal."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"Faceți clic aici pentru a găsi un contabil sau dacă doriți să listați "
"serviciile contabile pe Odoo"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Închide"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "Închide extrasul"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
msgid "Company"
msgstr "Companie"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Felicitări, ai terminat!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""
"Felicitări, ai terminat! Ați reconciliat %s tranzacții în %s. Aceasta este "
"în medie %s secunde pe tranzacție."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr "Conectați-vă banca pentru a putea beneficia de ultimele tranzacții"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Valorile contrapartidei"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Create Reconciliation Model"
msgstr "Creare Model Reconciliere"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "Creați un echivalent"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Crează un nou grup de cont"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Creează model"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Creați-vă prima factură de furnizor. <br/><br/><i>Sfat: dacă nu aveți una la"
" îndemână, utilizați factură de exemplul.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
msgid "Created on"
msgstr "Creat în"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "Potrivire Client / Furnizor"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Date"
msgstr "Dată"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Definiți anii fiscali de mai mult sau mai puțin de un an"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Description"
msgstr "Descriere"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Rezumat"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: account_accountant
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Nu aveți acces, săriți aceste date pentru e-mail-ul de digerare al "
"utilizatorului"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "Data scadenței"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "Dată sfârșit"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Data de încheiere, inclusă în anul fiscal."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Fiecare plată și factură înainte de această dată va primi statutul „Din "
"facturare”, ascunzând toate înregistrările contabile aferente acesteia. "
"Utilizați această opțiune după instalarea contabilității dacă utilizați "
"numai \"Facturarea înainte\", înainte de a importa toate datele dvs. "
"contabile efective în Odoo."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Link Extern"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "Filtrează după cont, etichetă, partener, valoare, ..."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "An fiscal"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "An Fiscal 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Anii fiscali"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Ultima Zi An Fiscal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Ultima Lună An Fiscal"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"Din orice vizualizare de listă, selectați mai multe înregistrări și lista "
"devine modificabilă. Dacă actualizați o celulă, înregistrările selectate "
"sunt actualizate simultan. Utilizați această caracteristică pentru a "
"actualiza mai multe intrări în jurnal din Registrul General sau orice "
"vizualizare în Jurnal."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "De acum, poate să doriți:"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Get back to the dashboard using your previous path…"
msgstr "Mergeți înapoi la dashboard folosind calea anterioară"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Mergi la extrase bancă"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr "Mergeți la facturare"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Bună treabă!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Great! Let’s continue."
msgstr "Minunat! Hai să continuăm."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"Date incorecte an fiscal: ziua este în afara intervalului de lună. Luna: %s;"
" Ziua: %s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Facturarea pragului de comutare"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Este obligatorii să fie specificat un cont și un jurnal pentru a crea o "
"pierdere (write-off)."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Elemente"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_journal
#, python-format
msgid "Journal"
msgstr "Jurnal"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr "Data închiderii jurnalului"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Notă contabilă"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
msgid "Journal Item"
msgstr "Element jurnal"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Journal Items"
msgstr "Elemente jurnal"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Elemente ale jurnalului de Reconciliat"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "KPI Valoare numerar cont bancar"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Label"
msgstr "Eticheta"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Ultima Zi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Ultima reconciliere :"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""
"Haideți să automatizăm facturie, trazacțiile bancare si procesele de "
"contabilitate ale dvs. "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Haideți sa mergem înapoi la dashboard."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s reconcile the fetched bank transactions."
msgstr "Să reconciliem tranzacțiile bancare preluate."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr "Să vedem cum arată o factură în vizualizarea formularului."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""
"Haideți să folosim AI să completeze formularul<br/><br/><i>Sfat: dacă OCR nu"
" a fost finalizat încă, mai așteptați câteva secunde și încercați din "
"nou.</i>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Încarcă mai mult ... ("

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Data de siguranță pentru toți utilizatorii"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Dată de blocare pentru non cosultanți"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Date Siguranță"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr "Management închidere"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Operațiuni manuale"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"Match statement with existing lines on receivable/payable accounts<br>* "
"Black line: existing journal entry that should be matched<br>* Blue lines: "
"existing payment that should be matched"
msgstr ""

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr "Potrivire cu intrări care nu provin din conturi de încasări / plăți"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "Potrivirea diversă"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Modificați modelele"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Mutați atașamentul"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
msgid "Name"
msgstr "Nume"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "Nou(ă)"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "No attachments linked."
msgstr "Nu există nici un atașament."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"Niciun utilizator nu poate edita înregistrările din jurnal legate de o taxă "
"înainte și inclusiv pentru această dată."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Niciun utilizator, inclusiv Consultanți, nu pot edita conturi anterior și "
"inclusiv acestei date. Folosiți-l pentru blocarea anului fiscal, de exemplu."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Note"
msgstr "Notă"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Nimic de făcut!"

#. module: account_accountant
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"Doar administratorii de facturare au voie să modifice datele de blocare!"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Numai utilizatorii cu rolul \"Export contabil\" poate edita conturi anterior"
" și inclusiv acestei date. Folosiți-l pentru blocare perioadă în interiorul "
"unui an fiscal deschis, de exemplu."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Sold deschis"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Partner"
msgstr "Partener"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "Plătește-ți"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Potrivirea plății"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Stare de plată înainte de comutare"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
msgid "Payments"
msgstr "Plăți"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Reconciliere plăți"

#. module: account_accountant
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "Plățile fără client nu pot fi corelate"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr "Alege o dată pentru blocare"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr "Previzionați conturile de factură ale vânzătorului"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "Presetări config"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification prior to the defined date "
"for all users. As a closed period, all accounting operations are prohibited."
msgstr ""
"Împiedică crearea sau modificarea înregistrărilor contabile înainte de data "
"definită pentru toți utilizatorii. Într-o perioadă închisă, toate "
"operațiunile contabile sunt interzise."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation prior to the defined date. Except for "
"Advisors users."
msgstr ""

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification prior to the defined date (Journal Entries"
" involving taxes). The Tax Return Lock Date is automatically set when the "
"corresponding Journal Entry is posted."
msgstr ""
"Împiedică modificarea declarațiilor fiscale înainte de data definită (în "
"registrările contabile care implică taxe). Data de blocare a declarației "
"fiscale este setată automat atunci când înregistrarea corespunzătoare este "
"postată."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Process this transaction."
msgstr "Procesați această tranzacție."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/models/account_move.py:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.server,name:account_accountant.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree_grouped
#, python-format
msgid "Reconcile"
msgstr "Reconciliază"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_manual_reconciliation
#: model:ir.ui.menu,name:account_accountant.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Reconciliere"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "Reconciliere pe extrase bancă"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr ""
"Înregistrați costul mărfurilor vândute în înregistrările dvs. din jurnal"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "Rezidual"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Salvează"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Salvează & Nou"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Selectează Partenerul"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Selectați un partener sau alegeți un omolog"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "Setări"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Omite"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Unele câmpuri sunt nedefinite"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "Dată început"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Data de începere, inclusă în anul fiscal."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "Taxă inclusă in preț"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr "Date Siguranță"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr "Data de închidere a raportului fiscal"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Taxes"
msgstr "Taxe"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_payment__payment_state_before_switch
msgid ""
"Technical field to keep the value of payment_state when switching from "
"invoicing to accounting (using invoicing_switch_threshold setting field). It"
" allows keeping the former payment state, so that we can restore it if the "
"user misconfigured the switch date and wants to change it."
msgstr ""
"Câmp tehnic pentru păstrarea valorii payment_state la trecerea de la "
"facturare la contabilitate (folosind câmpul de configurare "
"invoicing_switch_threshold). Permite păstrarea stării de plată anterioare, "
"astfel încât să o putem restabili dacă utilizatorul a configurat greșit data"
" comutării și dorește să o schimbe."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "Asta e, în medie."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "Suma%s nu este o sumă parțială validă"

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "Data de încheiere nu trebuie să fie anterioară datei de început."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""
"Facturile până la această dată nu vor fi luate în considerare ca "
"înregistrări contabile"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the accounts on vendor bill lines based on "
"history of previous bills"
msgstr ""
"Sistemul va încerca să prezică conturile pe liniile de factură ale "
"furnizorilor, pe baza istoricului facturilor anterioare"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Aici nu mai este nimic de reconciliat."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Această pagină afișează toate tranzacțiile bancare care urmează să fi "
"reconciliate și oferă o interfață elegantă că să facă acest lucru."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Această plată este înregistrată dar nu este reconciliată"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This was the model that generated the lines suggested"
msgstr "Acesta a fost modelul care a generat liniile sugerate"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Sfat: Elemente jurnal de actualizare în masă"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "Sfat: Găsiți un contabil sau înregistrați firma contabilă"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To Check"
msgstr "De verificat"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "Pentru a accelera reconcilierea, definiți"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr "Tranzacție"

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Transfer Accounts"
msgstr "Conturi de transfer"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#, python-format
msgid "Validate"
msgstr "Validează"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "Verificați"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Write-Off"
msgstr "Pierdere (write-off)"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Data Remitere"

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Nu puteți avea o suprapunere între doi ani fiscali, vă rugăm să corectați "
"datele de început și / sau de încheiere ale exercițiilor dvs. fiscale."

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reconcile the payable and receivable accounts of multiple "
"partners together at the same time."
msgstr ""
"Nu puteți reconcilia simultan conturile de plătit și de primit de la mai "
"mulți parteneri."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "You have suspense account moves that match this invoice."
msgstr "Aveți mișcări contabile temporare care se potrivesc acestei facturi."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Ai reconciliat"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "și urmărirea clienților"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "de exemplu: Taxe bancare"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "au fost reconciliați automat."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr "reconciliat"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "modele reconciliere"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "ceea ce a rămas)"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "secunde per tranzacție."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr "linii extras"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "De verificat"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "to mark this invoice as paid."
msgstr "să marcați factura ca fiind plătită complet"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "tranzacții în"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "facturi neplătite "

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "intrările neconciliate"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "facturi furnizori"
