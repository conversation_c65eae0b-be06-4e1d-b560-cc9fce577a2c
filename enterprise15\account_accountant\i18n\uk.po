# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Ukrainian (https://www.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Узгодити"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=\"tip_title\">Порада: Масове оновлення записів у журналі</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">Порада: Знайдіть бухгалтера чи бухгалтерську "
"компанію</b>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<br>See how to manage your customer invoices in the "
"<b>Customers/Invoices</b> menu"
msgstr ""
"<br>Перегляньте, як керувати рахунками ваших клієнтів в меню "
"<b>Клієнти/Рахунки</b>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock all journal entries</i>"
msgstr "<i>Заблокувати всі записи журналу</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock specific journal entries</i>"
msgstr "<i>Заблокувати конкретні записи журналу</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Fiscal Year</span>"
msgstr "<span class=\"o_form_label\">Звітний період</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">Знайдіть рахунок</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr ""
"<span class=\"tip_button_text\">Зареєструйте вашу бухгалтерську "
"компанію</span>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr "<strong><b>Молодці!</b> Ви пройшли усі кроки цього туру.</strong>"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Узгодження включає в себе як мінімум 2 рядки проведення."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_account
#, python-format
msgid "Account"
msgstr "Рахунок"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Групи рахуків"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_account_predictive_bills
msgid "Account Predictive Bills"
msgstr "Прогнозування рахунків"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Віджет узгодження рахунку"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Теги рахунків"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_accounting
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.res_partner_view_form
msgid "Accounting"
msgstr "Бухоблік"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Accounting Period Closing"
msgstr "Закриття бухгалтерського періоду"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Accounting closing dates"
msgstr "Дати закриття бухобліку"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Додати новий тег"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "All Users Lock Date"
msgstr "Усі дати блокування користувачів"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "Всі рахунки та платежі узгоджено, баланс рахунків є вірним."

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Дозволити визначати звітні періоди більше або менше року"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Amount"
msgstr "Сума"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Аналітичний рах."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "Теги Аналітики."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Англо-Саксонський бухоблік"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__attachment_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__attachment_ids
msgid "Attachments"
msgstr "Прикріплення"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Банківські та готівкові переміщення"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Узгодження банківських виписок"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
msgid "Bank Statement"
msgstr "Банківська виписка"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Cancel"
msgstr "Скасувати"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Змінити дату блокування"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Check & validate the bill. If no vendor has been found, add one before "
"validating."
msgstr ""
"Перевірте та підтвердіть рахунок. Якщо не знайдено жодного постачальника, "
"додайте одного перед підтвердженням."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "Перевірити все"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "Переконайтеся, що у вас немає рядків банківських виписок"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Check them"
msgstr "Перевірте їх"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "Choose a line to preview its attachments."
msgstr "Оберіть рядок, щоби переглянути його прикріплення."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Виберіть партнера або створіть списання"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Натисніть тут, щоби створити новий звітний період."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"Натисніть тут, щоб знайти бухгалтера або якщо ви хочете розмістити свої "
"бухгалтерські послуги на Odoo"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Закрити"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "Закрити виписку"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
msgid "Company"
msgstr "Компанія"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Вітаємо, ви все завершили!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""
"Вітаємо, ви все завершили! Ви узгодили %s тразакції в %s. Це в середньому %s"
" секунд на транзакцію."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Connect your bank and get your latest transactions."
msgstr "Підключіть ваш банк та отримуйте інформацію про  останні транзакції."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Протилежні значення"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Create Reconciliation Model"
msgstr "Створіть модель узгодження"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "Створити протилежний"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Створити нову групу рахунку"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Створити модель"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Створіть ваш перший рахунок постачальника.<br/><br/><i>Порада: Якщо у вас "
"нема навіть одно в наявності, використовуйте наш приклад рахунку.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
msgid "Created by"
msgstr "Створив"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
msgid "Created on"
msgstr "Створено на"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "Співставлення клієнта/постачальника"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Date"
msgstr "Дата"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Визначіть звітний період більше або менше одного року"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Description"
msgstr "Опис"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Огляд"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: account_accountant
#: code:addons/account_accountant/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Не маєте доступу, пропустіть ці дані для електронного оглядового "
"повідомлення користувача"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "Установлений термін"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
msgid "End Date"
msgstr "Кінцева дата"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Дата закінчення, включена у звітний період."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Кожен платіж і рахунок-фактура до цієї дати отримає статус \"З виставлення "
"рахунка\", приховуючи всі бухгалтерські записи, пов’язані з ним. "
"Використовуйте цю опцію після встановлення бухгалтерського обліку, якщо "
"раніше ви використовували лише модуль Виставлення рахунків, перш ніж "
"імпортувати всі фактичні дані бухгалтерського обліку в Odoo."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Зовнішнє посилання"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "Фільтр на рахунку, мітці, партнері, сумі,..."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
msgid "Fiscal Year"
msgstr "Звітний період"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Звітний період 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Звітні періоди"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Останній день звітного періоду"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Останній місяць звітного періоду"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"З будь-якого перегляду списку, оберіть кілька записів і список можна буде "
"редагувати. Якщо ви оновите мобільний, обрані записи оновлюються усі одразу."
" Використовуйте функцію для оновлення кілько записів у журналі із головної "
"бухгалтерської книги або будь-якого перегляду журналу."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "Відтепер ви, можливо, захочете:"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Get back to the dashboard using your previous path…"
msgstr "Поверніться на панель приладів, використовуючи ваш попередній шлях…"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Перейти до банківських виписок"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Go to invoicing"
msgstr "Перейдіть до виставлення рахунків"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Гарна робота!"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Great! Let’s continue."
msgstr "Чудово! Давайте продовжувати."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: code:addons/account_accountant/models/res_config_settings.py:0
#, python-format
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %s; Day: "
"%s"
msgstr ""
"Неправильна дата звітного періоду: день виходить за рамки місяця. Місяць: "
"%s; День: %s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Поріг перемикача рахунків"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Обов’язково потрібно вказати рахунок та журнал, щоб створити списання."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Елементи"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_accountant.model_account_journal
#, python-format
msgid "Journal"
msgstr "Журнал"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__period_lock_date
msgid "Journal Entries Lock Date"
msgstr "Дата блокування записів журналу"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Journal Items"
msgstr "Елементи журналу"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_action.js:0
#: model:ir.actions.client,name:account_accountant.action_manual_reconcile
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Проведення для узгодження"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "Значення Kpi готівки банківського рахунку"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Label"
msgstr "Мітка"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Останній день"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date____last_update
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year____last_update
msgid "Last Modified on"
msgstr "Останні зміни на"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Останнє узгодження:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s automate your bills, bank transactions and accounting processes."
msgstr ""
"Давайте автоматизуємо ваші рахунки, банківські транзакції та процес "
"бухобліку."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Давайте повернемося на панель приладів."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s reconcile the fetched bank transactions."
msgstr "Давайте узгодимо отримані банківські транзакції."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Let’s see how a bill looks like in form view."
msgstr "Давайте поглянемо, як виглядає рахунок у перегляді форми."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid ""
"Let’s use AI to fill in the form<br/><br/><i>Tip: If the OCR is not done "
"yet, wait a few more seconds and try again.</i>"
msgstr ""
"Використовуйте AI, щоби заповнити форму <br/><br/><i>Порада: Якщо OCR ще не "
"виконано, зачекайте кілька секунд і спробуйте знову.</i>"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Завантажити ще... ("

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid "Lock Date for All Users"
msgstr "Дата блокування для всіх користувачів"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__period_lock_date
msgid "Lock Date for Non-Advisers"
msgstr "Дата блокування для користувачів"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Дати блокування"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Management Closing"
msgstr "Закриття управління"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Ручні операції"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"Match statement with existing lines on receivable/payable accounts<br>* "
"Black line: existing journal entry that should be matched<br>* Blue lines: "
"existing payment that should be matched"
msgstr ""
"Порівняйте виписку з наявними рядками на рахунках дебіторської/кредиторської"
" заборгованості<br>* Чорний рядок: наявна запис у журналі, який має "
"відповідати<br>* Синій рядок: існуючі платежі, які слід узгодити"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""
"Співставте записи, які не з рахунків дебіторської/кредиторської "
"заборгованості"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "Інші співставлення"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Змінити моделі"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Перемістити прикріплення"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
msgid "Name"
msgstr "Назва"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "Новий"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/move_line_list_view.js:0
#, python-format
msgid "No attachments linked."
msgstr "Немає вкладених файлів."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__tax_lock_date
msgid ""
"No users can edit journal entries related to a tax prior and inclusive of "
"this date."
msgstr ""
"Жоден користувач не може редагувати записи журналу, пов'язані з податком до "
"цієї дати."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__fiscalyear_lock_date
msgid ""
"No users, including Advisers, can edit accounts prior to and inclusive of "
"this date. Use it for fiscal year locking for example."
msgstr ""
"Жоден користувач, включаючи продавців, не може редагувати рахунки включно до"
" цієї дати. Можете використовувати цю дату для фіксації звітного періоду."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Note"
msgstr "Примітка"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Немає нічого до виконання!"

#. module: account_accountant
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Лише адміністратори рахунків можуть перевіряти дати блокування!"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__period_lock_date
msgid ""
"Only users with the 'Adviser' role can edit accounts prior to and inclusive "
"of this date. Use it for period locking inside an open fiscal year, for "
"example."
msgstr ""
"Тільки користувачі, що мають роль продавця можуть редагувати рахунки включно"
" до цієї дати. Можете використовувати цю дату для фіксації проміжного "
"балансу в межах фінального звітного періоду."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Відкрити баланс"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Partner"
msgstr "Партнер"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "Оплатіть ваш"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Співставлення платежів"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_payment__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Стан оплати перед перемиканням"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
msgid "Payments"
msgstr "Платежі"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Співставлення платежів"

#. module: account_accountant
#: code:addons/account_accountant/models/account_payment.py:0
#, python-format
msgid "Payments without a customer can't be matched"
msgstr "Платежі без клієнта не збігаються"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Pick a date to lock"
msgstr "Оберіть дату блокування"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill accounts"
msgstr "Прогнозування рахунків постачальників"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "Налаштування"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Prevents Journal Entry creation or modification prior to the defined date "
"for all users. As a closed period, all accounting operations are prohibited."
msgstr ""
"Запобігає створенню або зміні запису журналу до визначеної дати для всіх "
"користувачів. Оскільки період закритий, усі бухгалтерські операції "
"заборонені."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__period_lock_date
msgid ""
"Prevents Journal entries creation prior to the defined date. Except for "
"Advisors users."
msgstr ""
"Запобігає створенню записів у журналі до визначеної дати. За винятком "
"користувачів Advisors."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Prevents Tax Returns modification prior to the defined date (Journal Entries"
" involving taxes). The Tax Return Lock Date is automatically set when the "
"corresponding Journal Entry is posted."
msgstr ""
"Запобігає зміні податкових декларацій до визначеної дати (записи в журналі, "
"що стосуються податків). Дата блокування податкової декларації "
"встановлюється автоматично, коли оприлюднюється відповідний запис у журналі."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
#, python-format
msgid "Process this transaction."
msgstr "Обробіть цю транзакцію."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/models/account_move.py:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.server,name:account_accountant.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree_grouped
#, python-format
msgid "Reconcile"
msgstr "Узгодити"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_manual_reconciliation
#: model:ir.ui.menu,name:account_accountant.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Узгодження"

#. module: account_accountant
#: model:ir.actions.client,name:account_accountant.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "Узгодження банківської виписки"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Запишіть вартість проданих товарів у ваших журнальних записах"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Реф."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "Залишок"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Зберегти"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Зберегти та Створити нове"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Оберіть партнера"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Оберіть партнера або вкажіть рахунок"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "Налаштування"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Пропустити"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Деякі поля не визначені"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date"
msgstr "Початкова дата"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Початкова дата, включена у звітний період."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "Податок включений у ціну"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__tax_lock_date
msgid "Tax Lock Date"
msgstr "Дата блокування податку"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Tax Return Lock Date"
msgstr "Дата блокування податкової декларації"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Taxes"
msgstr "Податки"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_move__payment_state_before_switch
#: model:ir.model.fields,help:account_accountant.field_account_payment__payment_state_before_switch
msgid ""
"Technical field to keep the value of payment_state when switching from "
"invoicing to accounting (using invoicing_switch_threshold setting field). It"
" allows keeping the former payment state, so that we can restore it if the "
"user misconfigured the switch date and wants to change it."
msgstr ""
"Технічне поле для збереження значення payment_state під час перемикання з "
"виставлення рахунку на бухоблік (using invoicing_switch_threshold setting "
"field). Це дозволяє зберегти попередній стан платежу, щоб ми могли відновити"
" його, якщо користувач неправильно налаштував дату переходу та хоче змінити "
"її."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "Це в середньому"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "Сума %s не є дійсною частковою сумою"

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid "The ending date must not be prior to the starting date."
msgstr "Дата закінчення не повинна бути до дати початку."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""
"Рахунки до цієї дати не будуть враховуватися як бухгалтерські проводки"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the accounts on vendor bill lines based on "
"history of previous bills"
msgstr ""
"Система намагатиметься передбачити рахунки в рядках рахунків постачальників "
"на основі історії попередніх рахунків"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Немає нічого для узгодження."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"На цій сторінці відображаються всі транзакції банку, які потрібно узгодити, "
"і забезпечує точний інтерфейс для цього."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Цей платіж зареєстровано, але не узгоджено."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This was the model that generated the lines suggested"
msgstr "Це була модель, що створила запропоновані рядки"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Порада: Масове оновлення записів у журналі"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "Порада: знайдіть бухгалтера або зареєструйте свою бухгалтерську фірму"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To Check"
msgstr "Перевірити"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "Щоби прискорити узгодження, визначте"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr "Операція"

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid "Transfer Accounts"
msgstr "Рахунки переміщення"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#, python-format
msgid "Validate"
msgstr "Підтвердити"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "Перевірити"

#. module: account_accountant
#: code:addons/account_accountant/models/reconciliation_widget.py:0
#, python-format
msgid "Write-Off"
msgstr "Списання"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Дата списання"

#. module: account_accountant
#: code:addons/account_accountant/models/account_fiscal_year.py:0
#, python-format
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Не можна перекривати два звітні періоди, виправте початкову та/або кінцеву "
"дату звітного періоду."

#. module: account_accountant
#: code:addons/account_accountant/models/account_move.py:0
#, python-format
msgid ""
"You cannot reconcile the payable and receivable accounts of multiple "
"partners together at the same time."
msgstr ""
"Ви не можете узгодити одночасно кредиторські та дебіторські рахунки кількох "
"партнерів."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "You have suspense account moves that match this invoice."
msgstr "У вас є проведення рахунку, які відповідають цьому рахунку."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Ви узгодили"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "та підписані клієнти"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "наприклад, банківська комісія"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "було узгоджено автоматично."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr "узгодити"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "моделі узгодження"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "залишилось)"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "секунд на операцію."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr "рядки виписки"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "перевірити"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "to mark this invoice as paid."
msgstr "позначити цей рахунок, як оплачений."

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "транзакція в"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "неоплачені рахунки"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "неузгоджені записи"

#. module: account_accountant
#. openerp-web
#: code:addons/account_accountant/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "рахунки постачальників"
