<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="base.user_root" model="res.users">
            <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
        </record>

        <record id="base.user_admin" model="res.users">
            <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
        </record>

        <!-- To change the categoy name from Invoicing -> Accounting-->
        <record model="ir.module.category" id="base.module_category_accounting_accounting">
            <field name="name">Accounting</field>
        </record>

        <record id="account.group_account_readonly" model="res.groups">
            <field name="name">Auditor</field>
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>

        <record id="account.group_account_user" model="res.groups">
            <field name="name">Accountant</field>
            <field name="implied_ids" eval="[(4, ref('account.group_account_invoice')), (4, ref('account.group_account_readonly'))]"/>
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>

        <record id="account.group_account_manager" model="res.groups">
            <field name="name">Advisor</field>
            <field name="implied_ids" eval="[(3, ref('account.group_account_invoice')), (4, ref('account.group_account_user'))]"/>
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>

        <record id="account.group_account_invoice" model="res.groups">
            <field name="category_id" ref="base.module_category_accounting_accounting"/>
        </record>

        <record id="group_fiscal_year" model="res.groups">
            <field name="name">Allow to define fiscal years of more or less than a year</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>


</odoo>
