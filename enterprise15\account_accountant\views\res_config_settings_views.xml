<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.account.accountant</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div data-key="account" position="attributes">
                <attribute name="data-string">Accounting</attribute>
                <attribute name="string">Accounting</attribute>
            </div>
            <div id="fiscalyear" position="replace">
                <div class="col-12 col-lg-6" id="fiscalyear" style="padding-left: 0; padding-right: 0;">
                    <div class="col-12 col-lg-12 o_setting_box">
                        <div class="o_setting_left_pane"/>
                        <div class="o_setting_right_pane">
                            <span class="o_form_label">Fiscal Year</span>
                            <div class="content-group">
                                <div class="row mt16">
                                    <label for="fiscalyear_last_month" string="Last Day" class="col-lg-3 o_light_label"/>
                                    <field name="fiscalyear_last_month" style="width: 100px; margin-right: 8px;"/>
                                    <field name="fiscalyear_last_day" class="oe_inline"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-12 o_setting_box">
                        <div class="o_setting_left_pane">
                            <field name="group_fiscal_year"/>
                        </div>
                        <div class="o_setting_right_pane">
                            <label for="group_fiscal_year"/>
                            <div class="text-muted">
                                Define fiscal years of more or less than one year
                            </div>
                            <div class="mt16">
                                <button name="%(account_accountant.actions_account_fiscal_year)d" icon="fa-arrow-right"
                                        type="action" string="Fiscal Years" class="btn-link"
                                        attrs="{'invisible': [('group_fiscal_year', '=', False)]}"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-6" style="padding-left: 0; padding-right: 0;" groups="account.group_account_manager">
                    <div class="col-12 col-lg-12 o_setting_box">
                        <div class="o_setting_left_pane"/>
                        <div class="o_setting_right_pane">
                            <label for="invoicing_switch_threshold"/>
                            <div class="text-muted">
                                The invoices up to this date will not be taken into account as accounting entries
                            </div>
                            <field name="invoicing_switch_threshold"/>
                        </div>
                    </div>
                </div>
            </div>
            <div id="dynamic_report" position="attributes">
                <attribute name="invisible">0</attribute>
            </div>
            <div id="bank_cash" position="inside">
                <div class="col-12 col-lg-6 o_setting_box" groups="base.group_no_one">
                    <div class="o_setting_left_pane">
                        <field name="use_anglo_saxon"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="use_anglo_saxon"/>
                        <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." role="img" aria-label="Values set here are company-specific." groups="base.group_multi_company"/>
                        <div class="text-muted">
                            Record cost of goods sold in your journal entries
                        </div>
                    </div>
                </div>
            </div>
            <xpath expr="//div[@id='account_vendor_bills']" position="inside">
                <div class="col-12 col-md-6 o_setting_box">
                    <div class="o_setting_left_pane">
                        <field name="module_account_predictive_bills"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="module_account_predictive_bills" string='Predict vendor bill accounts'/>
                        <div class="text-muted">
                            The system will try to predict the accounts on vendor bill lines based on history of previous bills
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
