<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <record id="view_account_reconcile_model_widget_wizard" model="ir.ui.view">
        <field name="name">account.reconcile.model.form</field>
        <field name="model">account.reconcile.model</field>
        <field name="arch" type="xml">
            <form>
                <div class="oe_title">
                    <label for="name"/>
                    <h1><field name="name" placeholder="e.g. Bank Fees"/></h1>
                </div>

                <group>
                    <group>
                        <field name="to_check"/>
                    </group>
                </group>

                <group string="Counterpart Values">
                    <field name="line_ids" nolabel="1">
                        <tree editable="bottom">
                            <field name="account_id"/>
                            <field name="amount_type"/>
                            <field name="amount_string"/>
                            <field name="tax_ids" widget="many2many_tags"/>
                            <field name="analytic_account_id" groups="analytic.group_analytic_tags"/>
                            <field name="analytic_tag_ids" groups="analytic.group_analytic_tags" widget="many2many_tags"/>
                            <field name="show_force_tax_included" invisible="1"/>
                            <field name="force_tax_included" attrs="{'invisible': [('show_force_tax_included', '=', False)]}" widget="boolean_toggle"/>
                            <field name="company_id" invisible="1"/>
                            <field name="label"/>
                        </tree>
                    </field>
                </group>

                <footer>
                    <button string="Validate" class="btn-primary" special="save" data-hotkey="v"/>
                    <button string="Cancel" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>