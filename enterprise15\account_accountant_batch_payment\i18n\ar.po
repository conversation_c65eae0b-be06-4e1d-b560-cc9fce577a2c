# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant_batch_payment
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "أداة تسوية الحسابات "

#. module: account_accountant_batch_payment
#. openerp-web
#: code:addons/account_accountant_batch_payment/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Batch Payments"
msgstr "الدفعات المجمعة "

#. module: account_accountant_batch_payment
#. openerp-web
#: code:addons/account_accountant_batch_payment/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter..."
msgstr "عامل تصفية..."

#. module: account_accountant_batch_payment
#. openerp-web
#: code:addons/account_accountant_batch_payment/static/src/js/account_batch_payment_reconciliation.js:0
#, python-format
msgid ""
"Some journal items from the selected batch payment are already selected in "
"another reconciliation : "
msgstr ""
"بعض عناصر اليومية في الدفعة المجمعة المحددة تم تحديدها بالفعل في عملية تسوية"
" أخرى: "
