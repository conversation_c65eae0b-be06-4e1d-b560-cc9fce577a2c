# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant_batch_payment
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account_accountant_batch_payment
#. openerp-web
#: code:addons/account_accountant_batch_payment/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Batch Payments"
msgstr ""

#. module: account_accountant_batch_payment
#. openerp-web
#: code:addons/account_accountant_batch_payment/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter..."
msgstr "Φίλτρο..."

#. module: account_accountant_batch_payment
#. openerp-web
#: code:addons/account_accountant_batch_payment/static/src/js/account_batch_payment_reconciliation.js:0
#, python-format
msgid ""
"Some journal items from the selected batch payment are already selected in "
"another reconciliation : "
msgstr ""
