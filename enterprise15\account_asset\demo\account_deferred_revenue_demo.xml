<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data noupdate="1">
    <!-- Creating demo data that require looking for accounts -->
        <record id="account_asset_model_sale" model="account.asset">
            <field name="journal_id" model="account.journal" search="[
                ('type', '=', 'general'),
                ('id', '!=', obj().env.user.company_id.currency_exchange_journal_id.id)]"/>
            <field name="name">Revenue Recognition Maintenance Contract - 1 Years</field>
            <field name="method_number">12</field>
            <field name="method_period">1</field>
            <field name="account_asset_id" model="account.account" search="[
                ('user_type_id', '=', ref('account.data_account_type_fixed_assets'))]"/>
            <field name="account_depreciation_id" model="account.account" search="[
                ('user_type_id', '=', ref('account.data_account_type_fixed_assets'))]"/>
            <field name="account_depreciation_expense_id" model="account.account" search="[
                ('user_type_id', '=', ref('account.data_account_type_revenue')),
                ('tag_ids', 'in', [ref('account.account_tag_operating')])]"/>
            <field name="asset_type">sale</field>
            <field name="state">model</field>
        </record>
    </data>
</odoo>
