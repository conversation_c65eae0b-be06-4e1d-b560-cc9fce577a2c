# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr " (نسخة)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "عدد قيود الإهلاك"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "عدد الزيادات الإجمالية"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "عدد قيود الإهلاك المُرحلة"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "%s created from invoice"
msgstr "%s تم انشاؤها من الفاتورة"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s m"
msgstr "%s شهر "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s y"
msgstr "%s سنة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "(prorata entry)"
msgstr "(القيد النسبي) "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred expense has been created for this move:"
msgstr "تم إنشاء نفقة مؤجلة لهذه الحركة: "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred revenue has been created for this move:"
msgstr "تم إنشاء إيرادات مؤجلة لهذه الحركة: "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A document linked to %s has been deleted: "
msgstr "تم حذف مستند مرتبط بـ%s : "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "A gross increase has been created"
msgstr "تم إنشاء زيادة إجمالية "

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "حساب"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Account Asset Counterpart"
msgstr "نظير أصل الحساب "

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_assets_report
msgid "Account Assets Report"
msgstr "تقرير أصول الحساب "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
msgid "Account Depreciation"
msgstr "إهلاك الحساب "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
msgid "Account Depreciation Expense"
msgstr "نفقة إهلاك الحساب "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"يستخدم نوع الحساب في استخراج التقارير وفقاً للتقارير القانونية المطلوبة في "
"كل دولة، وتضع القواعد اللازمة لإقفال سنة مالية وإنشاء القيد الافتتاحي للسنة "
"المالية اللاحقة."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "الحساب المُستخدَم لقيود الإهلاك، لتخفيض قيمة الأصل. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr "الحساب المستخدم للقيود الدورية لتسجيل جزء من الأصل كنفقة. "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to recognize the expense"
msgstr "الحساب المُستَخدم لتمييز النفقة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to recognize the revenue"
msgstr "الحساب المُستَخدم لتمييز الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to record the deferred expense"
msgstr "الحساب المُستخدم لتسجيل النفقة المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to record the deferred income"
msgstr "الحساب المُستَخدم لتسجيل الدخل المؤجل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "الحساب المُستخدَم لتسجيل شراء الأصل بسعره الأصلي. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr "الحساب المُستخدَم لكتابة عنصر اليومية في حالة الربح "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"الحساب المُستخدَم لكتابة عنصر اليومية في حال الربح عند بيع أحد الأصول "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "الحساب المُستخدَم لكتابة عنصر اليومية في حال الخسارة "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"الحساب المُستخدَم لكتابة عنصر اليومية في حال الخسارة عند بيع أحد الأصول "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "المحاسبة "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
#, python-format
msgid "Acquisition Date"
msgstr "تاريخ الاستحواذ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__action
msgid "Action"
msgstr "إجراء"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "الإجراء المطلوب"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "نشط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same account"
msgstr "يجب أن تكون كافة البنود من نفس الحساب "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same move type"
msgstr "يجب أن تكون كافة البنود من نفس نوع الحركة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be posted"
msgstr "يجب أن تكون كافة البنود مُرحّلة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "استيراد المبلغ الذي تم إهلاكه بالفعل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr "يكون للأصل أصل رئيسي عندما يكون نتيجة لزيادة القيمة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "An asset has been created for this move:"
msgstr "تم إنشاء أصل لهذه الحركة: "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_analytic_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_tag_ids
msgid "Analytic Tag"
msgstr "علامة التصنيف التحليلية "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Archived"
msgstr "مؤرشف"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__purchase
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#, python-format
msgid "Asset"
msgstr "الأصل"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "حساب الأصل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Asset Gross Increase Account"
msgstr "حساب الزيادة الكلية للأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "اسم عرض مُعرِّف الأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids_display_name
msgid "Asset Ids Display Name"
msgstr "اسم عرض مُعرِّفات الأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
msgid "Asset Linked"
msgstr "الأصول المربوطة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_manually_modified
msgid "Asset Manually Modified"
msgstr "تم تعديل الأصل يدوياً "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Asset Model"
msgstr "نموذج الأصل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "اسم نموذج الأصل "

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Asset Models"
msgstr "نماذج الأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "اسم الأصل"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Options"
msgstr "خيارات الأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_asset_type
msgid "Asset Type"
msgstr "نوع الأصل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_value_change
msgid "Asset Value Change"
msgstr "تغير قيمة الأصل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "قيم الأصول "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "تم إنشاء الأصل"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_move_line__asset_ids
msgid "Asset created from this Journal Item"
msgstr "تم إنشاء الأصل من عنصر اليومية هذا "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset paused"
msgstr "تم إيقاف الأصل مؤقتاً "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "لقد تم بيع أو التصرف في الأصل. القيد المحاسبي في انتظار التصديق. "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Asset unpaused"
msgstr "تم تعطيل الإيقاف المؤقت للأصل "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Asset(s)"
msgstr "الأصل (الأصول) "

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "إثبات الأصل/الإيرادات "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_purchase_tree
#, python-format
msgid "Assets"
msgstr "الأصول"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "الأصول والإيرادات"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "الأصول المُقفلة"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "الأصول المسودة والجارية"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "أتمتة الأصل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Expense"
msgstr "أتمتة النفقة المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Revenue"
msgstr "أتمتة الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "الأتمتة "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
#, python-format
msgid "Book Value"
msgstr "القيمة الدفترية "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "يمكن إنشاء الأصل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Cancel"
msgstr "إلغاء"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Category of asset"
msgstr "فئة الأصل"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Characteristics"
msgstr "الصفات "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "الفروع"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"اختر الطريقة لاستخدامها لاحتساب عدد بنود الإهلاك.\n"
"  * القيمة الثابتة: يتم احتسابها على أساس: القيمة الكلية / عدد الإهلاكات\n"
"  * المنخفضة: يتم احتسابها على أساس: القيمة المتبقية * عامل الانخفاض\n"
"  * الانخفاض ثم قيمة ثابتة: مثل القيمة المنخفضة ولكن مع قيمة إهلاك دنيا تساوي القيمة الثابتة. "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "مغلق"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__company_id
msgid "Company"
msgstr "الشركة"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "حساب الإهلاك"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Compute Expense"
msgstr "حساب النفقات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Compute Revenue"
msgstr "حساب الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "تأكيد"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree_grouped_inherit
msgid "Create Asset"
msgstr "إنشاء الأصل "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "إنشاء وتصديق "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "إنشاء في مسودة "

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "إنشاء أصل جديد "

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "إنشاء نموذج أصل جديد "

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_form
msgid "Create new deferred expense"
msgstr "إنشاء نفقة مؤجلة جديدة "

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_model_form
msgid "Create new deferred expense model"
msgstr "إنشاء نموذج نفقة مؤجلة جديدة "

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_form
msgid "Create new deferred revenue"
msgstr "إنشاء إيراد مؤجل جديد "

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_model_form
msgid "Create new deferred revenue model"
msgstr "إنشاء نموذج إيراد مؤجل جديد "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "الإهلاك التراكمي "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Cumulative Expense"
msgstr "النفقات التراكمية "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Cumulative Revenue"
msgstr "الإيرادات التراكمية "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "العملة"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "الحالي "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "القيم الحالية "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_id
msgid "Customer Invoice"
msgstr "فاتورة العميل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "التاريخ"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Dec. then Straight"
msgstr "انخفاض ثم ثبات "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
#, python-format
msgid "Declining"
msgstr "منخفض "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "عامل الانخفاض "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "الانخفاض ثم الثبات في خط مستقيم "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__expense
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__expense
#, python-format
msgid "Deferred Expense"
msgstr "النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Deferred Expense Account"
msgstr "حساب النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Amount"
msgstr "مبلغ النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Model"
msgstr "نموذج النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Model name"
msgstr "اسم نموذج النفقات المؤجلة "

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_expense_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_model_recognition
msgid "Deferred Expense Models"
msgstr "نماذج النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Options"
msgstr "خيارات النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense name"
msgstr "اسم النفقات المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Expense(s)"
msgstr "النفقة (النفقات) المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_expense_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Deferred Expenses"
msgstr "النفقات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Deferred Expenses Models"
msgstr "نماذج النفقات المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__sale
#, python-format
msgid "Deferred Revenue"
msgstr "الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Deferred Revenue Account"
msgstr "حساب الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Amount"
msgstr "مبلغ الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Model"
msgstr "نموذج الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Model name"
msgstr "اسم نموذج الإيرادات المؤجلة "

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_model_recognition
msgid "Deferred Revenue Models"
msgstr "نماذج الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Options"
msgstr "خيارات الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue name"
msgstr "اسم الإيرادات المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Revenue(s)"
msgstr "الإيراد (الإيرادات) المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
#, python-format
msgid "Deferred Revenues"
msgstr "الإيرادات المؤجلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Deferred Revenues Models"
msgstr "نماذج الإيرادات المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred expense"
msgstr "النفقة المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred expense created"
msgstr "تم إنشاء نفقة مؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred revenue"
msgstr "الإيرادات المؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred revenue created"
msgstr "تم إنشاء الإيرادات المؤجلة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "المبلغ القابل للإهلاك "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_remaining_value
msgid "Depreciable Value"
msgstr "القيمة القابلة للإهلاك "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "المبلغ المُهلَك "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Depreciation"
msgstr "إهلاك"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "حساب الإهلاك"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "اللائحة الاستهلاكية "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "تاريخ الإهلاك"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "بنود الإهلاك"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "طريقة الإهلاك"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_number_import
msgid "Depreciation Number Import"
msgstr "استيراد رقم الإهلاك "

#. module: account_asset
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "جدول الإهلاك "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Depreciation Table Report"
msgstr "تقرير جدول الإهلاك "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "تم تعديل اللائحة الاستهلاكية "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s posted (%s)"
msgstr "قيد الإهلاك %s تم ترحيله (%s)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s reversed (%s)"
msgstr "قيد الإهلاك %s تم عكسه (%s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "أصل حساب العرض "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_model_choice
msgid "Display Model Choice"
msgstr "عرض اختيار النموذج "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal"
msgstr "التصرف "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "تاريخ التصرف "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "حركة التصرف "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "حركات التصرف "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__dispose
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Dispose"
msgstr "تصرف "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "مسودة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__draft_asset_ids
msgid "Draft Asset"
msgstr "مسودة أصل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "المدة"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Duration / Rate"
msgstr "المدة / المعدل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciation Schedule"
msgstr "جدول الإهلاك الموجود بالفعل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciations"
msgstr "الإهلاكات الموجودة بالفعل "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Expense"
msgstr "النفقة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Expense Account"
msgstr "حساب النفقات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Board"
msgstr "لوحة النفقات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Date"
msgstr "تاريخ النفقات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Lines"
msgstr "بنود النفقات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Expense Name"
msgstr "اسم النفقة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Recognition"
msgstr "إثبات النفقة "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "First Depreciation"
msgstr "الإهلاك الأول "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "First Depreciation Date"
msgstr "تاريخ أول إهلاك"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date_import
msgid "First Depreciation Date Import"
msgstr "استيراد تاريخ أول إهلاك "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "First Recognition Date"
msgstr "تاريخ أول إثبات "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "حساب الأصل الثابت "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "قم باختيار أيقونة رائعة، مثال: fa-tasks "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "مرجع عرض الاستمارة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__gain
msgid "Gain"
msgstr "الربح "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "حساب الربح "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_or_loss
msgid "Gain Or Loss"
msgstr "الربح أو الخسارة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "قيمة الربح "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Gross Increase"
msgstr "الزيادة الإجمالية "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "قيمة الزيادة الإجمالية "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Group By..."
msgstr "تجميع حسب.."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "المُعرف"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "أيقونة للإشارة إلى نشاط مستثنى. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata
msgid ""
"If set, specifies the start date for the first period's computation. By "
"default, it is set to the day's date rather than the Start Date of the "
"fiscal year."
msgstr ""
"إذا تم تعيينه، فإنه يقوم بتحديد تاريخ البدء لحساب الفترة الأولى. يتم التعيين"
" لتاريخ اليوم افتراضياً عوضاً عن تاريخ بدء السنة المالية. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model
msgid ""
"If this is selected, an expense/revenue will be created automatically when "
"Journal Items on this account are posted."
msgstr ""
"إذا قمت بتحديده، سوف يتم إنشاء نفقة/إيراد تلقائياً عندما تُرحّل عناصر "
"اليومية في هذا الحساب. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date_import
msgid ""
"In case of an import from another software, provide the first depreciation "
"date in it."
msgstr "في حال الاستيراد من برنامج آخر، قم بتقديم تاريخ أول إهلاك فيه. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__depreciation_number_import
msgid ""
"In case of an import from another software, provide the number of "
"depreciations already done before starting with Odoo."
msgstr ""
"في حال الاستيراد من برنامج آخر، قم بتقديم عدد الإهلاكات التي تم تنفيذها "
"بالفعل قبل البدء مع أودو. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"في حال الاستيراد من برنامج آخر، قد تحتاج إلى استخدام هذا الحقل حتى يكون لديك"
" تقرير جدول الإهلاك المناسب. هذه هي القيمة التي تم استهلاكها بالفعل مع كون "
"القيود غير محسوبة لهذا النموذج "

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid ""
"In percent.<br>For a linear method, the depreciation rate is computed per "
"year.<br>For a declining method, it is the declining factor"
msgstr ""
"بالنسبة.<br>يتم حساب معدل الإهلاك على أساس سنوي في الطريقة "
"الخطية.<br>بالنسبة لطريقة الانخفاض، يكون هو عامل الانخفاض "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Increase Accounts"
msgstr "زيادة الحسابات "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_line_id
msgid "Invoice Line"
msgstr "بند الفاتورة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "لا يمكنك إهلاك المبلغ الذي تخطط للحصول عليه. "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Journal"
msgstr "اليومية "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Journal Entries"
msgstr "قيود اليومية "

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
#, python-format
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"Journal Items of {account} should have a label in order to generate an asset"
msgstr ""
"يجب أن يكون لعناصر اليومية لـ {account} علامة تصنيف حتى تتمكن من إنشاء أصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Linear"
msgstr "خطي "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__loss
msgid "Loss"
msgstr "خسارة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "حساب الخسارة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "إدارة العناصر "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
#, python-format
msgid "Method"
msgstr "طريقة"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "نموذج "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification"
msgstr "تعديل"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification reason"
msgstr "سبب إجراء التعديل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "تعديل"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#, python-format
msgid "Modify Asset"
msgstr "تعديل الأصل"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "تعديل الإهلاكات"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Modify Expense"
msgstr "تعديل النفقة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Modify Revenue"
msgstr "تعديل الإيرادات "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "شهور"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "أصول متعددة لكل بند "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"سوف يتم إنشاء عدة عناصر أصول بناء على كمية بنود الفاتورة عوضاً عن أصل شامل "
"واحد. "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__need_date
msgid "Need Date"
msgstr "بحاجة إلى تاريخ "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "New Values"
msgstr "القيم الجديدة "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "مبلغ متبقي جديد للأصل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "مبلغ مسترد جديد للأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Next Period Expense"
msgstr "نفقة الفترة التالية "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Next Period Revenue"
msgstr "إيرادات الفترة التالية "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__no
msgid "No"
msgstr "لا"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "No asset account"
msgstr "لا يوجد حساب للأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "ليس مبلغاً قابلاً للإهلاك "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "ليست قيمة قابلة للإهلاك "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"لاحظ أن هذا التاريخ لا يغير من كيفية احتساب قيد اليومية الأول في حالة الأصول"
" التي تحتسب من تاريخ الشراء. كل ما يفعله هو تغيير تاريخ المحاسبة. "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__number_asset_ids
msgid "Number Asset"
msgstr "ترقيم الأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Number of Depreciations"
msgstr "عدد الإهلاكات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "عدد الشهور في الفترة"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Number of Recognitions"
msgstr "عدد الإثباتات "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "عدد الأصول التي تم إنشاؤها لزيادة قيمة الأصل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "عدد قيود الإهلاك (مُرحّلة أو غير مُرحّلة) "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل التي بها أخطاء في التسليم "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "قيد الانتظار "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.line_caret_options
msgid "Open Asset"
msgstr "أصل مفتوح "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "القيمة الأصلية "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "الرئيسي "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
msgid "Pause"
msgstr "إيقاف مؤقت "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_pause
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#, python-format
msgid "Pause Asset"
msgstr "إيقاف الأصل مؤقتاً "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Pause Depreciation"
msgstr "إيقاف الإهلاك مؤقتاً "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__date
msgid "Pause date"
msgstr "تاريخ الإيقاف المؤقت "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Period length"
msgstr "طول المدة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "القيود المُرحّلة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "التاريخ النسبي "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata
msgid "Prorata Temporis"
msgstr "التناسب الزمني"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__purchase
msgid "Purchase: Asset"
msgstr "المشتريات: الأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Reason"
msgstr "السبب"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Related Expenses"
msgstr "النفقات ذات الصلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Related Purchase"
msgstr "عمليات الشراء ذات الصلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Related Sales"
msgstr "المبيعات ذات الصلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Report of reversal for {name}"
msgstr "تقرير العكس لـ {name} "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Reset to running"
msgstr "إعادة التعيين كجارٍ "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Residual Amount to Recognize"
msgstr "المبلغ المتبقي لإثباته "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "متابعة الإهلاك "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#, python-format
msgid "Revenue"
msgstr "الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Revenue Account"
msgstr "حساب الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Board"
msgstr "لوحة الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Date"
msgstr "تاريخ الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Lines"
msgstr "بنود الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
msgid "Revenue Name"
msgstr "اسم الإيرادات "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Recognition"
msgstr "إثبات الإيرادات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__reversal_move_id
msgid "Reversal Move"
msgstr "عكس الحركة "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"قم بعكس قيود الإهلاك المُرحّلة إلى المستقبل حتى تتمكن من تعديل الإهلاك "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "جاري"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Sale"
msgstr "المبيعات"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__sale
msgid "Sale: Revenue Recognition"
msgstr "البيع: إثبات الإيراد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save Model"
msgstr "حفظ النموذج "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Save model"
msgstr "حفظ النموذج "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "تحديد بند الفاتورة "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Sell"
msgstr "بيع "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
#, python-format
msgid "Sell Asset"
msgstr "بيع الأصل "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Sell or Dispose"
msgstr "بيع أو تصرف "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set manually the original values or"
msgstr "قم بتعيين القيم يدوياً أو "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "تعيين كمسودة"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "التعيين كجارٍ "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr ""
"عرض كافة السجلات التي يسبق تاريخ الإجراء التالي فيها تاريخ اليوم الجاري "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Some fields are missing {}"
msgstr "بعض الحقول مفقودة {}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Some required values are missing"
msgstr "بعض القيم المطلوبة مفقودة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Start Depreciating"
msgstr "بدء الإهلاك "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "الحالة"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة بناءً على الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مضى\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخططة: الأنشطة المستقبلية. "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "خط مستقيم "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"إجمالي قيمة الإهلاك، القيمة المستردة والقيمة الدفترية لكافة عناصر زيادة "
"القيمة "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__can_create_asset
msgid ""
"Technical field specifying if the account can generate asset depending on "
"it's type. It is used in the account form view."
msgstr ""
"حقل تقني لتحديد إذا ما كان بمقدور الحساب إنشاء أصل بناء على نوعه. يُستَخدم "
"ذلك في عرض استمارة الحساب. "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_value
msgid ""
"Technical field to know if we should display the fields for the creation of "
"gross increase asset"
msgstr ""
"حقل تقني لمعرفة إذا ما كان يجب عرض الحقول لإنشاء أصل الزيادة الإجمالية "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_or_loss
msgid ""
"Technical field to know is there was a gain or a loss in the selling of the "
"asset"
msgstr "حقل تقني لمعرفة إذا ما كان هناك ربح أو خسارة عند بيع الأصل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "المدة الزمنية بين عمليتي إهلاك "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "الأصل بانتظار التعديل من قِبَل مُعالج الربط "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "الفروع هي الأرباح بالقيمة لهذا الأصل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_id
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr "فاتورة التصرف ضرورية حتى يتم إنشاء قيد إقفال اليومية "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "عدد الإهلاكات المطلوبة لإهلاك الأصل "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "The remaining value on the last depreciation line must be 0"
msgstr "يجب أن تكون القيمة المتبقية في آخر بند إهلاك تساوي 0 "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_line_id
msgid "There are multiple lines that could be the related to this asset"
msgstr "توجد العديد من البنود التي يمكن أن تكون ذات صلة بهذا الأصل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_value_change
msgid ""
"This is a technical field set to true when this move is the result of the "
"changing of value of an asset"
msgstr ""
"يكون هذا الحقل التقني صحيحاً عندما تكون هذه الحركة نتيجة لتغير قيمة الأصل "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_manually_modified
msgid ""
"This is a technical field stating that a depreciation line has been manually"
" modified. It is used to recompute the depreciation table of an "
"asset/deferred revenue."
msgstr ""
"يوضح هذا الحقل التقني أنه تم تعديل بند الإهلاك يدوياً، ويُستَخدم لحساب جدول "
"الإهلاك للأصل/الإيراد المؤجل. "

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset_reversed_widget.js:0
#, python-format
msgid "This move has been reversed"
msgstr "لقد تم عكس هذه الحركة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "نشاطات اليوم"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Total"
msgstr "الإجمالي"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Trying to pause an asset without any future depreciation line"
msgstr "محاولة إيقاف الأصل بشكل مؤقت دون أي بند إهلاك مستقبلي "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred expense"
msgstr "التحويل كنفقة مؤجلة "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred revenue"
msgstr "التحويل كإيراد مؤجل "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as an asset"
msgstr "التحويل كأصل "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__user_type_id
msgid "Type of the account"
msgstr "نوع الحساب "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى المسجل."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عداد الرسائل غير المقروءة "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value decrease for: %(asset)s"
msgstr "نقصان القيمة لـ : %(asset)s"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value increase for: %(asset)s"
msgstr "زيادة القيمة لـ : %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"عندما يتم إنشاء أصل، يكون في وضع 'المسودة'.\n"
"إذا تم تأكيد الأصل، تتغير الحالة إلى  'جاري' ويمكن ترحيل بنود الإهلاك في المحاسبة.\n"
"يمكن تعيين الحالة 'قيد الانتظار' يدوياً عندما ترغب في إيقاف إهلاك أصل من الأصول بشكل مؤقت لفترة من الزمن.\n"
"يمكنك إقفال الأصل يدوياً عند انتهاء الإهلاك. إذا تم ترحيل آخر بند من بنود الإهلاك، ينتقل الأصل تلقائياً إلى تلك الحالة. "

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "سنوات"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot archive a record that is not closed"
msgstr "لا يمكنك أرشفة سجل غير مقفل "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_sell.py:0
#, python-format
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"لا يمكنك أتمتة قيد اليومية لأصل ذي زيادة إجمالية جارية. الرجاء استخدام "
"'التصرف' في الزيادة (الزيادات). "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"لا يمكنك إنشاء أصل من البنود التي تحتوي على رصيد وخصم في الحساب أو بقيمة "
"فارغة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"لا يمكنك حذف أصل مرتبط بقيود مُرحّلة. \n"
"عليك إما تأكيد الأصل ثم بيعه أو التخلص منه أو إلغاء قيود اليومية المرتبطة. "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr "لا يمكنك حذف ملف في حالة %s."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot pause an asset with posted depreciation lines in the "
"future.without reverting them."
msgstr ""
"لا يمكنك إيقاف الأصل مؤقتاً مع بنود إهلاك مرحّلة في المستقبل دون ردها. "

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an entry having a posted deferred revenue/expense"
msgstr "لا يمكنك إعادة التعيين لصنع مسودة قيد بها إيرادات/نفقات مؤجلة مرحّلة "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "e.g. Annual Subscription"
msgstr "مثال: الاشتراك السنوي "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "e.g. Annually Paid Insurance"
msgstr "مثال: تأمين مدفوع سنوياً "

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "مثال: كمبيوتر محمول"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "select the related purchases"
msgstr "قم بتحديد عمليات الشراء ذات الصلة "

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "this move"
msgstr "هذه الحركة "
