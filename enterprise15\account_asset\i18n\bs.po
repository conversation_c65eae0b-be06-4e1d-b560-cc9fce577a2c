# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_asset
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:429
#, python-format
msgid " (copy)"
msgstr " (kopija)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:571
#, python-format
msgid " (grouped)"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr ""

#. module: account_asset
#: model:ir.actions.server,name:account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:account_asset.account_asset_cron
#: model:ir.cron,name:account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:52
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__active
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__active
msgid "Active"
msgstr "Aktivan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:639
#, python-format
msgid "Amount"
msgstr "Iznos"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Iznos stavki amortizacija"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__account_analytic_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__account_analytic_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__analytic_tag_ids
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__analytic_tag_ids
msgid "Analytic Tag"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "Osnovna sredstva"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Konto osnovnog sredstva"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line__asset_category_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "Kategorija osnovnog sredstva"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_depreciation_line
msgid "Asset Depreciation Line"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Trajanje osnovnih sredstava za uređivanje"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line__asset_end_date
msgid "Asset End Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__name
msgid "Asset Name"
msgstr "Naziv osnovnog sredstva"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line__asset_start_date
msgid "Asset Start Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__name
#: model:ir.model.fields,field_description:account_asset.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model_terms:ir.ui.view,arch_db:account_asset.res_config_settings_view_form
msgid "Asset Types"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "Kategorija osnovnog sredstva"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:296
#, python-format
msgid "Asset created"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:344
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:account_asset.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "Osnovna sredstva"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_asset_report
#: model:ir.model,name:account_asset.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:account_asset.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:account_asset.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Analiza osnovnih sredstava"

#. module: account_asset
#: model:ir.model,name:account_asset.model_asset_depreciation_confirmation_wizard
msgid "Assets Depreciation Confirmation wizard"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr ""

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Osnovna sredstva sa statusom 'zatvoreno'"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Osnovno sredstvo u stanju 'u pripremi' ili 'otvoreno'"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "Osnovna sredstva u pripremi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "Osnovno sredtvo u toku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__open_asset
msgid "Auto-Confirm Assets"
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,date_first_depreciation:0
#: selection:account.asset.category,date_first_depreciation:0
msgid "Based on Last Day of Purchase Period"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Otkaži"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Category"
msgstr "Kategorija"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"Označite ovo ako želite da automatski potvrdite osnovna sredstva ove "
"kategorije kada su kreirani od fakture."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__method
#: model:ir.model.fields,help:account_asset.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"Odaberite metodu za izračunavanje iznosa stavki amortizacije.\n"
"  * Linearno: Izračunato na osnovu: Bruto vrijednosti / Broj amortizacija\n"
"  * Silazno: Izračunato na osnovu: Preostala vrijednost * Silazni faktor"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,help:account_asset.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"Odaberite period za koji želite da automatski proknjižite stavke "
"amortizacija osnovnih sredstava u toku"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
msgid "Close"
msgstr "Zatvori"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Zatvoreno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__company_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "Kompanija"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__method
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__method
msgid "Computation Method"
msgstr "Metoda izračunavanja"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "Izračunaj osnovno sredstvo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Portvrdi"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Asset Moves"
msgstr "Kreirana kretanja amortizacije"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_depreciation_confirmation_wizard.py:20
#, python-format
msgid "Created Revenue Moves"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:639
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__currency_id
#, python-format
msgid "Currency"
msgstr "Valuta"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Tekući"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr "Trenutna amortizacija"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Datum"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "Datum nabave osnovnog sredstva"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "Datum amortizacije"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:account_asset.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "Odgođeni tip prihoda"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "Odloženi prihodi"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Degressive"
msgstr "Silazno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr "Silazni faktor"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "Amortizacija"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "Kontrolna tabla amortizacije"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__depreciation_nbr
msgid "Depreciation Count"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr "Datum amortizacije"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__date_first_depreciation
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__date_first_depreciation
msgid "Depreciation Dates"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr "Zapis amortizacije"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "Stavke amortizacije"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "Metoda amortizacije"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr "Naziv amortizacije"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:85
#, python-format
msgid "Depreciation board modified"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:642
#, python-format
msgid "Depreciation line posted."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:299
#, python-format
msgid "Disposal Move"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:302
#, python-format
msgid "Disposal Moves"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:621
#, python-format
msgid "Document closed."
msgstr ""

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "U pripremi"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__method_end
msgid "Ending Date"
msgstr "Završni datum"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_end
msgid "Ending date"
msgstr "Završni datum"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "Napredni filteri..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid "First Depreciation Date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Generiši stavke"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr "Bruto iznos"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__value
msgid "Gross Value"
msgstr "Bruto vrijednost"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "Grupiši po"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Grupiši po..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__id
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the asset date (purchase date) instead of the first January / Start "
"date of fiscal year"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__installment_nbr
msgid "Installment Count"
msgstr ""

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Faktura"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka fakture"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Iznos je koji planirate imati a ne možete amortizovati."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "Stavke"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__journal_id
msgid "Journal"
msgstr "Dnevnik knjiženja"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:463
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "Dnevnički zapisi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: account_asset
#: selection:account.asset.asset,method:0
#: selection:account.asset.category,method:0
msgid "Linear"
msgstr "Linearno"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: account_asset
#: selection:account.asset.asset,date_first_depreciation:0
msgid "Manual"
msgstr "Ručno"

#. module: account_asset
#: selection:account.asset.category,date_first_depreciation:0
msgid "Manual (Defaulted on Purchase Date)"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Izmijeni"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_modify
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Uredi osnovno srestvo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_invoice_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr "Sljedeći period amortizacije"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_asset_asset_report
msgid "No content"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__note
msgid "Note"
msgstr "Zabilješka"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "Broj amortizacija"

#. module: account_asset
#: selection:account.asset.asset,method_time:0
#: selection:account.asset.category,method_time:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "Broj mjeseci u periodu"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:641
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__partner_id
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Period Length"
msgstr "Trajanje perioda"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr ""

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr ""

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:49
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__move_check
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "Proknjižen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr "Proknjiženi iznos"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "Proknjižene stavke amortizacije"

#. module: account_asset
#: model:ir.model,name:account_asset.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr "Prorata Temporis"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:392
#, python-format
msgid ""
"Prorata temporis can be applied only for the \"number of depreciations\" "
"time method."
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "Nabava"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr ""

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Purchase: Asset"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Reason"
msgstr "Razlog"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__code
msgid "Reference"
msgstr "Referenca"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "Ostatak"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "Preostala vrijednost"

#. module: account_asset
#: selection:account.asset.asset,state:0 selection:asset.asset.report,state:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "Izvodi se"

#. module: account_asset
#: selection:account.asset.category,type:0
msgid "Sale: Revenue Recognition"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "Prodaja"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "Vrijednost likvidacije"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "Pretraži kategoriju osnovnog sredstva"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "Postavi u pripremu"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Ovdje navedite vrijeme između dvije amortizacije, u mjesecima"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr "Stanje osnovnog sredstva"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__state
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__state
msgid "Status"
msgstr "Status"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr "Vremenski period između dvije amortizacije , u mjesecima"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,help:account_asset.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Broj amortizacija potrebnih za amortizaciju osnovnog sredstva"

#. module: account_asset
#: code:addons/account_asset/models/account_invoice.py:62
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be 0."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_category__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be based on the last day of the purchase month or the purchase year (depending on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the purchase date."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be based on the last day of the purchase month or the purchase year (depending on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the purchase date.\n"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:506
#, python-format
msgid ""
"This depreciation is already linked to a journal entry. Please post or "
"delete it."
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period of asset/revenue recognition as well."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__method_time
msgid "Time Method"
msgstr "Vremenska metoda"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__type
#: model:ir.model.fields,field_description:account_asset.field_account_asset_category__type
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_search
msgid "Type"
msgstr "Tip"

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset.js:55
#, python-format
msgid "Unposted"
msgstr "Neproknjiženo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr "Neproknjiženi iznos"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Dobavljač"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_asset__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_asset__state
#: model:ir.model.fields,help:account_asset.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Kada je osnovno sredstvo kreirano, nalazi se u statusu 'U pripremi'.\n"
"Ako je osnovno sredstvo potvrđeno, status prelazi u 'U toku' i stavke amortizacije mogu biti knjižene u računovodstvu.\n"
"Možete ručno da zatvorite osnovno sredstvo kada je amortizavija završena. Ako je zadnja stavka amortizavije knjižena, osnovno sredstvo automatski odlazi u taj status."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_asset_report__name
msgid "Year"
msgstr "Godina"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:136
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:133
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:650
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:652
#, python-format
msgid "You cannot delete posted installment lines."
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_category_form
msgid "months"
msgstr "mjeseci"
