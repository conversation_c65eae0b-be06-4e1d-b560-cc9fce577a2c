# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr " (kopi)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# Afskrivnings posteringer"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# Bruttoforhøjelser"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# Posterede afskrivnings posteringer"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "%s created from invoice"
msgstr "%s oprettet fra faktura"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s m"
msgstr "%s m"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s y"
msgstr "%s y"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "(prorata entry)"
msgstr "(prorata postering)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred expense has been created for this move:"
msgstr "Der er oprettet en udskudt udgift for denne bevægelse:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred revenue has been created for this move:"
msgstr "Der er oprettet en udskudt indtægt for denne bevægelse:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A document linked to %s has been deleted: "
msgstr "Et dokument forbundet til %s er blevet slettet:"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "A gross increase has been created"
msgstr "En bruttoforhøjelse er blevet oprettet"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "Konto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Account Asset Counterpart"
msgstr "Konto aktiv modstykke"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_assets_report
msgid "Account Assets Report"
msgstr "Konto aktiver rapport"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
msgid "Account Depreciation"
msgstr "Konto afskrivelse"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
msgid "Account Depreciation Expense"
msgstr "Konto afskrivelse udgift"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Konto type bliver brugt til informations brug, for at generere "
"landsspecifikke juridiske rapporter, og laver regler for at lukke et "
"regnskabsår samt oprette startposter."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Konto, der anvendes i afskrivningsposterne, for at reducere aktivværdien."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Konto, der anvendes i periodiske poster, til registrering af en del af "
"anlægsaktiv som omkostning."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to recognize the expense"
msgstr "Konto brugt til at genkende udgiften"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to recognize the revenue"
msgstr "Konto brugt til at genkende indtægten"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to record the deferred expense"
msgstr "Konto, der bruges til at registrere den udskudte udgift"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to record the deferred income"
msgstr "Konto der bruges til at registrere den udskudte indtægt"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""
"Konto bruges til at registrere køb af anlægsaktiv til dets oprindelige pris."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr ""
"Konto der bruges til at postere journal aktivet, i tilfælde af gevinst"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"Konto som bruges til at postere journal aktivet, i tilfælde af gevinst ved "
"salg af et aktiv"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "Konto der bruges til at postere et journal aktiv, i tilfælde af tab"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"Konto der bruges til at postere et journal aktiv, i tilfælde af tab ved salg"
" af et aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "Regnskab"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
#, python-format
msgid "Acquisition Date"
msgstr "Dato for erhvervelse"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__action
msgid "Action"
msgstr "Handling"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "Aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitets Type Ikon"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same account"
msgstr "Alle linjer bør være fra samme konto"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same company"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same move type"
msgstr "Alle linjerne bør være fra samme bevægelsestype"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be posted"
msgstr "Alle linjer bør være posteret"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "Allerede Forældet Mængde Import"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr ""
"Et aktiv har en den nedarver fra, når resultatet er en gevinst i værdi"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "An asset has been created for this move:"
msgstr "Et aktiv er blevet oprettet for denne bevægelse:"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_analytic_id
msgid "Analytic Account"
msgstr "Analysekonto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Analytisk nøgleord"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Archived"
msgstr "Arkiveret"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__purchase
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#, python-format
msgid "Asset"
msgstr "Aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Konto for aktiver"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Asset Gross Increase Account"
msgstr "Aktiv bruttoforøgelse konto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "Aktiv ID Visningsnavn"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids_display_name
msgid "Asset Ids Display Name"
msgstr "Aktiv ID'er Visningsnavne"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
msgid "Asset Linked"
msgstr "Aktiv linket"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_manually_modified
msgid "Asset Manually Modified"
msgstr "Aktiv manuelt modificeret"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Asset Model"
msgstr "Aktiv model"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "Aktiv modelnavn"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Asset Models"
msgstr "Aktivmodeller"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "Navn på aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Options"
msgstr "Aktiver Optioner"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_asset_type
msgid "Asset Type"
msgstr "Type"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_value_change
msgid "Asset Value Change"
msgstr "Aktiv værdi ændring"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "Aktiv værdier"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "Aktiv oprettet"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_move_line__asset_ids
msgid "Asset created from this Journal Item"
msgstr "Aktiv oprettet fra dette journal aktiv"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset paused"
msgstr "Aktiv pauset"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "Solgt eller bortskaffet aktiv. Posteringslinje afventer godkendelse."

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Asset unpaused"
msgstr "Aktiv ikke pauset"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Asset(s)"
msgstr "Aktiv(er)"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Aktiv/omsætning anerkendelse"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_purchase_tree
#, python-format
msgid "Assets"
msgstr "Aktiver"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Aktiver og omsætning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Lukkede aktiver"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Aktiver med status kladde og åben"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "Automatiser aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Expense"
msgstr "Automatiser udskudt udgift"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Revenue"
msgstr "Automatiser udskudt indtægt"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "Automatisering"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
#, python-format
msgid "Book Value"
msgstr "Bog værdi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "Kan oprette aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Cancel"
msgstr "Annullér"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Category of asset"
msgstr "Kategorier af aktiver"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Characteristics"
msgstr "Kendetegn"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "Underkonti"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"Vælg metoden der skal bruges til at udregne mængden af forældede linjer.\n"
"* Lige Linje: Udregnet på grundlag af: Bruttoværdi / Antallet af Forældelser\n"
"* Nedadgående: Udregnet på grundlag af: Restværdi * Nedadgående Faktor\n"
"* Nedadgående derefter Lige Linje: Som Nedadgående men med en minimum forældelses værdi lig med værdien på den lige linje."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Lukket"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "Beregn afskrivning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Compute Expense"
msgstr "Udregn Udgift"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Compute Revenue"
msgstr "Beregn indtægter"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "Bekræft"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree_grouped_inherit
msgid "Create Asset"
msgstr "Opret aktiv"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "Opret og godkend"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "Opret som kladde"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr ""

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr ""

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_form
msgid "Create new deferred expense"
msgstr ""

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_model_form
msgid "Create new deferred expense model"
msgstr ""

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_form
msgid "Create new deferred revenue"
msgstr ""

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_model_form
msgid "Create new deferred revenue model"
msgstr ""

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Akkumuleret afskrivning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Cumulative Expense"
msgstr "Kumulativ udgift"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Cumulative Revenue"
msgstr "Kumulativ indtægt"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Aktuel"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "Nuværende værdier"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_id
msgid "Customer Invoice"
msgstr "Kundefaktura"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Dato"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Dec. then Straight"
msgstr "Nedadgående derefter Lige"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
#, python-format
msgid "Declining"
msgstr "Nedadgående"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "Nedadgående Faktor"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "Nedadgående derefter Lige Linje"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__expense
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__expense
#, python-format
msgid "Deferred Expense"
msgstr "Udskudte udgifter"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Deferred Expense Account"
msgstr "Udskudt udgift konto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Amount"
msgstr "Udskudt udgift konto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Model"
msgstr "Udskudt udgift model"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Model name"
msgstr "Udskudt udgift model navn"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_expense_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_model_recognition
msgid "Deferred Expense Models"
msgstr "Udskudte udgifter modeller"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Options"
msgstr "Udskudt Udgift Optioner"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense name"
msgstr "Udskudt udgift navn"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Expense(s)"
msgstr "Udskudte udgift(er)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_expense_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Deferred Expenses"
msgstr "Udskudte udgifter"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Deferred Expenses Models"
msgstr "Udskudte udgifter modeller"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__sale
#, python-format
msgid "Deferred Revenue"
msgstr "Udskudt omsætning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Deferred Revenue Account"
msgstr "Konto for udskudt omsætning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Amount"
msgstr "Udskudt indtægt mængde"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Model"
msgstr "Udskudt indtægt model"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Model name"
msgstr "Udskudt indtægt model navn"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_model_recognition
msgid "Deferred Revenue Models"
msgstr "Udskudt omsætning modeller"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Options"
msgstr "Udskudt Indtægt Optioner"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue name"
msgstr "Navn på udskudt indtægt"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Revenue(s)"
msgstr "Udskudt indtægt(er)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
#, python-format
msgid "Deferred Revenues"
msgstr "Udskudt omsætning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Deferred Revenues Models"
msgstr "Udskudt omsætning modeller"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred expense"
msgstr "Udskudt udgift"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred expense created"
msgstr "Udskudt udgift oprettet"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred revenue"
msgstr "Udskudt indtægt"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred revenue created"
msgstr "Udskudt indtægt oprettet"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "Afskrivelig mængde"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_remaining_value
msgid "Depreciable Value"
msgstr "Afskrivelig værdi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "Forældet Mængde"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Depreciation"
msgstr "Afskrivning"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "Afskrivelses konto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "Afskrivningskort"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "Afskrivnings dato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "Afskrivningslinjer"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "Afskrivningsmetode"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_number_import
msgid "Depreciation Number Import"
msgstr "Forældet Antal Import"

#. module: account_asset
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "Afskrivningsplan"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Depreciation Table Report"
msgstr "Afskrivelse tabel rapport"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "Afskrivningskort ændret"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s posted (%s)"
msgstr "Afskrivelse postering %s blev posteret (%s)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s reversed (%s)"
msgstr "Afskrivelse postering %s blev tilbagekaldt (%s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "Vis konto aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_model_choice
msgid "Display Model Choice"
msgstr "Vis model valg"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal"
msgstr "Disponibel"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "Disponibel dato"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "Bortskaffelsespost"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "Bortskaffelsesposter"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__dispose
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Dispose"
msgstr "Disponer"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "Udkast"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__draft_asset_ids
msgid "Draft Asset"
msgstr "Kladde aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "Varighed"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Duration / Rate"
msgstr ""

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciation Schedule"
msgstr "Eksisterende afskrivningsplan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciations"
msgstr "Eksisterende Forældelser"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Expense"
msgstr "Omkostning"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Expense Account"
msgstr "Vareforbrugskonto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Board"
msgstr "Udgift opslagstavle"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Date"
msgstr "Udgifts dato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Lines"
msgstr "Udgiftslinjer"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Expense Name"
msgstr "Udgift navn"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Recognition"
msgstr "Udgift genkendelse"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "First Depreciation"
msgstr "Først afskrivelse"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "First Depreciation Date"
msgstr "Første afskrivningsdato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date_import
msgid "First Depreciation Date Import"
msgstr "Første Forældelses Dato Import"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "First Recognition Date"
msgstr "Først genkendelses dato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "Fast aktiv konto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "Formula visning ref"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "Fremtidige aktiviteter"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__gain
msgid "Gain"
msgstr "Gevinst"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "Gevinst konto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_or_loss
msgid "Gain Or Loss"
msgstr "Gevinst eller tab"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "Gevinst værdi"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Gross Increase"
msgstr "Bruttoforøgelse"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "Bruttoforøgelse værdi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Group By..."
msgstr "Gruppér efter..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata
msgid ""
"If set, specifies the start date for the first period's computation. By "
"default, it is set to the day's date rather than the Start Date of the "
"fiscal year."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model
msgid ""
"If this is selected, an expense/revenue will be created automatically when "
"Journal Items on this account are posted."
msgstr ""
"Hvis dette er valgt, vil en udgift/indtægt blive oprettet automatisk når "
"Journal artikler på denne konto er posteret."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date_import
msgid ""
"In case of an import from another software, provide the first depreciation "
"date in it."
msgstr ""
"I tilfælde af en import fra anden software, angiv den første forældelses "
"dato i den."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__depreciation_number_import
msgid ""
"In case of an import from another software, provide the number of "
"depreciations already done before starting with Odoo."
msgstr ""
"I tilfælde af en import fra anden software, angiv antallet af forældelser "
"allerede udført før du begynder med Odoo."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"I tilfælde af en import fra anden software, skal du muligvis bruge dette "
"felt til at få den korrekte forældelses tabel rapport. Dette er den værdi "
"der allerede var forældet med posteringer ikke udregnet fra denne model"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid ""
"In percent.<br>For a linear method, the depreciation rate is computed per "
"year.<br>For a declining method, it is the declining factor"
msgstr ""
"I procent.<br>For en lineær metode er forældelses raten udregner per "
"år.<br>For en nedadgående metode, er det den nedadgående faktor"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Increase Accounts"
msgstr "Forøgelses konti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_line_id
msgid "Invoice Line"
msgstr "Fakturalinje"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Det er det beløb, du planlægger at få, du ikke kan afskrive."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Journal"
msgstr "Journal"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Journal Entries"
msgstr "Journalposter"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "Postering"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
#, python-format
msgid "Journal Items"
msgstr "Journalposter"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"Journal Items of {account} should have a label in order to generate an asset"
msgstr ""
"Journal aktiver på {account} bør have et mærkat, for at kunne generere et "
"aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "Overskredet aktiviteter"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Linear"
msgstr "Lineær"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__loss
msgid "Loss"
msgstr "Tab"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "Tabskonto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "Vedhæftning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "Administrer Genstande"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
#, python-format
msgid "Method"
msgstr "Metode"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "Model"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification"
msgstr "Ændring"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification reason"
msgstr "Modificerings grundlag"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Rediger"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#, python-format
msgid "Modify Asset"
msgstr "Rediger aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "Rediger afskrivning"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Modify Expense"
msgstr "Modificer udgift"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Modify Revenue"
msgstr "Ændre indtjening"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "Måneder"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "Flere Aktiver per Linje"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"Flere aktiver vil blive generet afhængig af regningslinje mængden i stedet "
"for 1 global aktiv."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mine Aktiviteter Deadline"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__need_date
msgid "Need Date"
msgstr "Skal bruge en dato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "New Values"
msgstr "Nye værdier"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "Ny restmængde for aktivet"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "Nyt bjærgningsbeløb for aktivet"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste Aktivitet Kalender Arrangement"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Next Period Expense"
msgstr "Næste periode udgift"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Next Period Revenue"
msgstr "Næste periodeomsætning"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__no
msgid "No"
msgstr "Nej"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "No asset account"
msgstr "Ingen aktiv konto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "Ikke afskrivelig mængde"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "Ikke afskrivelig værdi"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"Bemærk at denne dato ikke ændre udregningen af den første journal postering,"
" i tilfælde af prorata temporis aktiver. Den ændre blot den bogførings dato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__number_asset_ids
msgid "Number Asset"
msgstr "Tal aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Number of Depreciations"
msgstr "Antal afskrivninger"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "Antal måneder i en periode"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Number of Recognitions"
msgstr "Antal genkendelser"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "Antal af aktiver der er sat til at forøge værdien på aktivet"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "Antal af afskrivelses posteringer (posteret eller ej)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antal meddelser der kræver handling"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "Antal ulæste beskeder"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "I bero"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.line_caret_options
msgid "Open Asset"
msgstr "Åben aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "Original værdi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "Overordnet"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
msgid "Pause"
msgstr "Pause"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_pause
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#, python-format
msgid "Pause Asset"
msgstr "Pause aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Pause Depreciation"
msgstr "Pause afskrivelse"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__date
msgid "Pause date"
msgstr "Pause dato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Period length"
msgstr "Periode længde"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "Posteret posteringer"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "Prorata dato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata
msgid "Prorata Temporis"
msgstr "Prorata Temporis"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__purchase
msgid "Purchase: Asset"
msgstr "Indkøb: Aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Reason"
msgstr "Årsag"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Related Expenses"
msgstr "Relateret udgifter"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Related Purchase"
msgstr "Relateret indkøb"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Related Sales"
msgstr "Relateret salg"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Report of reversal for {name}"
msgstr "Rapport for tilbageføring for {name}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Reset to running"
msgstr "Nulstil til kørende"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Residual Amount to Recognize"
msgstr "Restmængde at genkende"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "Genoptag afskrivelse"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#, python-format
msgid "Revenue"
msgstr "Indtægter"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Revenue Account"
msgstr "Indtægt konto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Board"
msgstr "Indtægts oversigt"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Date"
msgstr "Indtægtsdato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Lines"
msgstr "Indtægtslinjer"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
msgid "Revenue Name"
msgstr "Indtægts navn"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Recognition"
msgstr "Metode til indtægtsføring"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__reversal_move_id
msgid "Reversal Move"
msgstr "Tilbageførsel bevægelse"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"Tilbagefør afskrivelses posteringer posteret i fremtiden, for at modificerer"
" afskrivningen"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "Kører"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Sale"
msgstr "Salg"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__sale
msgid "Sale: Revenue Recognition"
msgstr "Salg: Omsætningsgodkendelse"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save Model"
msgstr "Gem model"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Save model"
msgstr "Gem model"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "Vælg faktura linje"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Sell"
msgstr "Sælg"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
#, python-format
msgid "Sell Asset"
msgstr "Sælg aktiv"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Sell or Dispose"
msgstr "Sælg eller bortskaf"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set manually the original values or"
msgstr "Manuel angivelse af original værdier eller"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "Sæt til kladde"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "Sæt til løbende"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "Vis alle poster, hvor den næste aktivitetsdato er før i dag"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Some fields are missing {}"
msgstr "Visse felter mangler {}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Some required values are missing"
msgstr "Visse påkrævede værdier mangler"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Start Depreciating"
msgstr "Start Forældelse"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "Status"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "Lige Linje"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"Summen af afskrivelses værdien, bjærgnings værdien, og bog værdien for alle "
"værdi forøgende aktiver"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__can_create_asset
msgid ""
"Technical field specifying if the account can generate asset depending on "
"it's type. It is used in the account form view."
msgstr ""
"Teknisk felt som specificere, om kontoen kan oprette aktiver, alt afhængig "
"af dens type. Det bruges i konto formular visningen."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_value
msgid ""
"Technical field to know if we should display the fields for the creation of "
"gross increase asset"
msgstr ""
"Teknisk felt til at lade os vide, om vi skal vise feltet for oprettelsen af "
"et bruttoforøgelse aktiv"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_or_loss
msgid ""
"Technical field to know is there was a gain or a loss in the selling of the "
"asset"
msgstr ""
"Teknisk felt for at vide, om der er en gevinst eller et tab ved salg af "
"aktivet"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "Tid mellem to afskrivelser"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "Aktivet der skal modificeres af denne guide"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "Nedarvingerne er forøgelsen i værdi for dette aktiv"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_id
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr ""
"Bortskaffelses fakturaen skal bruges, for at oprette en afsluttende journal "
"postering."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Antallet af afskrivninger der er nødvendigt for at afskrive dit aktiv"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "The remaining value on the last depreciation line must be 0"
msgstr "Den tilbageværende værdi på den sidste afskrivnings linje skal være 0"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_line_id
msgid "There are multiple lines that could be the related to this asset"
msgstr "Der er flere linjer, der kunne være relateret til dette aktiv."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_value_change
msgid ""
"This is a technical field set to true when this move is the result of the "
"changing of value of an asset"
msgstr ""
"Dette er et teknisk felt, angivet som sandt hvis bevægelsen er resultatet af"
" en ændring i værdien på et aktiv"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_manually_modified
msgid ""
"This is a technical field stating that a depreciation line has been manually"
" modified. It is used to recompute the depreciation table of an "
"asset/deferred revenue."
msgstr ""
"Dette er et teknisk felt der angiver, at en afskrivelses linje er blevet "
"modificeret manuelt. Den bruges til at genberegne afskrivelses tabellen for "
"et aktiv/udskudt indtægt."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset_reversed_widget.js:0
#, python-format
msgid "This move has been reversed"
msgstr "Denne bevægelse er blevet tilbageført"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Total"
msgstr "I alt"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Trying to pause an asset without any future depreciation line"
msgstr "Prøver at pause et aktiv uden nogen fremtidig afskrivelses linje"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred expense"
msgstr "Vend som en udskudt udgift"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred revenue"
msgstr "Vend som en udskudt indtægt"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as an asset"
msgstr "Vend som et aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__user_type_id
msgid "Type of the account"
msgstr "Kontotypen"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread
msgid "Unread Messages"
msgstr "Ulæste beskeder"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Ulæste beskedtæller"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value decrease for: %(asset)s"
msgstr "Værdi sænkning for: %(asset)s"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value increase for: %(asset)s"
msgstr "Værdi forøgelse for: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Website kommunikations historik"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Når et aktiv er oprettet, er dens status 'Kladde'.\n"
"Hvis aktivet godkendes, ændres status til 'Løbende', og afskrivelses linjerne kan posteres i regnskabet.\n"
"'I bero' status kan sættes manuelt, når du vil pause afskrivelsen på et aktiv i et stykke tid.\n"
"Du kan manuelt afslutte et aktiv, når afskrivelsen er fuldført. Hvis den sidste linje af afskrivelsen er posteret, overgår aktivet automatisk til den status."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "År"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot archive a record that is not closed"
msgstr "Du kan ikke arkivere et datasæt der ikke er lukket"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_sell.py:0
#, python-format
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"Du kan ikke automatisere journalføringen for et aktiv, der har en løbende "
"bruttostigning. Brug 'Bortskaf' i stigningen."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"Du kan ikke oprette et aktiv fra linjer der indeholder kredit eller debit på"
" kontoen, eller med en nul-mængde."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"Du kan ikke slette et aktiv som er forbundet til posteringer.\n"
"Du bør enten bekræfte aktivet, og derefter sælge eller bortskaffe den, eller annullere de forbundne journalpostering."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr "Du kan ikke slette et dokument, der er i %s fase."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot pause an asset with posted depreciation lines in the "
"future.without reverting them."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an entry having a posted deferred revenue/expense"
msgstr ""
"Du kan ikke nulstille en postering med en posteret udskudt indtægt/udgift "
"til kladde"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "e.g. Annual Subscription"
msgstr "f.eks. Årlig abonnement"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "e.g. Annually Paid Insurance"
msgstr "f.eks. Årlig betalt forsikring"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "f.eks. bærbar iBook"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "select the related purchases"
msgstr "vælg de relaterede indkøb"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "this move"
msgstr "denne bevægelse"
