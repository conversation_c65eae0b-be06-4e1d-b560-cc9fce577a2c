# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# moham<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> pourrez<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <PERSON> <<EMAIL>>, 2022
# <PERSON>gaux, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>roo<PERSON>, 2022
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr "(کپی)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# ثبت سند های استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# افزایش های ناخالص"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# ثبت سند های استهلاک ارسال شده"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "%s created from invoice"
msgstr "%s ایجاد شده از فاکتور"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s m"
msgstr "%s m"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s y"
msgstr "%s y"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "(prorata entry)"
msgstr "(ورودی متناسب)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred expense has been created for this move:"
msgstr "یک هزینه معوق برای این انتقال ایجاد شده است:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred revenue has been created for this move:"
msgstr "یک درآمود معوق برای این انتقال ایجاد شده است:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A document linked to %s has been deleted: "
msgstr "یک سند لینک شده به %s حذف شده است: "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "A gross increase has been created"
msgstr "یک افزایش ناخالص ایجاد شده است"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "حساب"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Account Asset Counterpart"
msgstr "مکمل حساب دارایی"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_assets_report
msgid "Account Assets Report"
msgstr "گزارش حساب دارایی ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
msgid "Account Depreciation"
msgstr "حساب استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
msgid "Account Depreciation Expense"
msgstr "حساب هزینه استهلاک"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"نوع حسابی که به منظور اطلاعات استفاده می‌شود، برای ایجاد گزارش‌های قانونی "
"خاص کشور، و مجموعه‌‌ای از قوانین برای بستن یک سال مالی و ایجاد سند افتتاحیه."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "حساب استفاده شده در ورودی‌های استهلاک، برای کاهش ارزش دارایی."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"حساب استفاده شده در ثبت سند های دوره ای، برای ثبت بخشی از دارایی به صورت "
"هزینه."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to recognize the expense"
msgstr "حساب مورد استفاده برای شناسایی هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to recognize the revenue"
msgstr "حساب مورد استفاده برای شناسایی درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to record the deferred expense"
msgstr "حساب مورد استفاده برای ثبت هزینه های معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to record the deferred income"
msgstr "حساب مورد استفاده برای ثبت درآمد معوق"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "حساب مورد استفاده برای ثبت خرید دارایی با قیمت اولیه آن."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr "حساب مورد استفاده برای آیتم روزنامه در حالت سود کردن"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"حساب مورد استفاده برای نوشتن آیتم روزنامه در حالت سود هنگام فروش یک دارایی"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "حساب مورد استفاده برای نوشتن آیتم روزنامه در حالت زیان"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"حساب مورد استفاده برای نوشتن آیتم روزنامه در حالت زیان هنگام فروش یک دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "حسابداری"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
#, python-format
msgid "Acquisition Date"
msgstr "تاریخ خرید"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__action
msgid "Action"
msgstr "اقدام"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "فعال"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "فعالیت‌ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "نمای استثنایی فعالیت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "وضعیت فعالیت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "آیکون نوع فعالیت"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same account"
msgstr "تمام سطر ها باید از یک حساب یکسان باشند"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same company"
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same move type"
msgstr "همه سطرها باید از یک نوع انتقال باشند"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be posted"
msgstr "همه سطر ها باید ارسال شده باشند"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "مبلغ استهلاک از قبل خروجی گرفته شده است"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr "یک دارایی دارای والد است وقتی نتیجه افزایش ارزش باشد"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "An asset has been created for this move:"
msgstr "یک دارایی برای این انتقال ایجاد شده است:"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_analytic_id
msgid "Analytic Account"
msgstr "حساب تحلیلی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_tag_ids
msgid "Analytic Tag"
msgstr "برچسب تحلیلی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Archived"
msgstr "بایگانی شده"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__purchase
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#, python-format
msgid "Asset"
msgstr "دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "حساب دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Asset Gross Increase Account"
msgstr "حساب افزایش ناخالص دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "نام نمایشی شناسه دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids_display_name
msgid "Asset Ids Display Name"
msgstr "نام نمایشی شناسه های دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
msgid "Asset Linked"
msgstr "دارایی لینک شده"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_manually_modified
msgid "Asset Manually Modified"
msgstr "دارایی دستی تغییر داده شده"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Asset Model"
msgstr "مدل دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "نام مدل دارایی"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Asset Models"
msgstr "مدل های دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "نام دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Options"
msgstr "گزینه‌های دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_asset_type
msgid "Asset Type"
msgstr "نوع دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_value_change
msgid "Asset Value Change"
msgstr "تغییر ارزش دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "ارزش های دارایی"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "دارایی ایجاد شده"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_move_line__asset_ids
msgid "Asset created from this Journal Item"
msgstr "دارایی از این آیتم روزنامه ایجاد شده است"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset paused"
msgstr "دارایی متوقف شد"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "دارایی فروخته شده یا اسقاط شده. ثبت سند حسابداری منتظر تایید است."

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Asset unpaused"
msgstr "دارایی متوقف نشد"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Asset(s)"
msgstr "دارایی(ها)"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "تشخیص دارایی/درآمد"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_purchase_tree
#, python-format
msgid "Assets"
msgstr "دارایی‌ها"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "دارایی‌ها و درآمدها"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "دارایی‌ها در حالت بسته شده"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "دارایی در پیشنویس و ایالات باز"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "اتوماسیون کردن دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Expense"
msgstr "اتوماسیون کردن هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Revenue"
msgstr "اتوماسیون کردن درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "اتوماسیون"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
#, python-format
msgid "Book Value"
msgstr "ارزش دفتری"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "می تواند دارایی ایجاد کند"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Cancel"
msgstr "لغو"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Category of asset"
msgstr "طبقه‌بندی دارایی"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Characteristics"
msgstr "خصوصیات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "فرزندان"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"روش مورد استفاده برای محاسبه مقدار سطرهای استهلاک را انتخاب کنید.\n"
"   * خط مستقیم: بر اساس: ارزش ناخالص / تعداد استهلاک ها محاسبه می شود\n"
"   * کاهشی: محاسبه شده بر اساس: ارزش باقیمانده * ضریب نزولی\n"
"   * کاهش پس از آن خط مستقیم: مانند نزولی اما با حداقل ارزش استهلاک معادل ارزش خط مستقیم."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "بسته شد"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__company_id
msgid "Company"
msgstr "شرکت"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "محاسبه استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Compute Expense"
msgstr "محاسبه هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Compute Revenue"
msgstr "محاسبه درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "تایید"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree_grouped_inherit
msgid "Create Asset"
msgstr "ایجاد دارایی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "ایجاد و تایید"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "ایجاد در پیشنویس"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "ایجاد دارایی جدید"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "ایجاد مدل دارایی جدید"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_form
msgid "Create new deferred expense"
msgstr "ایجاد هزینه معوق جدید"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_model_form
msgid "Create new deferred expense model"
msgstr "مدل هزینه معوق جدید ایجاد کنید"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_form
msgid "Create new deferred revenue"
msgstr "ایجاد درآمد معوق جدید"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_model_form
msgid "Create new deferred revenue model"
msgstr "ایجاد مدل جدید درآمد معوق"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "ایجاد شده در"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "استهلاک انباشته"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Cumulative Expense"
msgstr "هزینه انباشته"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Cumulative Revenue"
msgstr "درآمد انباشته"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "ارز"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "جاری"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "ارزش های فعلی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_id
msgid "Customer Invoice"
msgstr "فاکتور مشتری"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "تاریخ"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Dec. then Straight"
msgstr "دسامبر و سپس مستقیم"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
#, python-format
msgid "Declining"
msgstr "کاهشی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "ضریب کاهش"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "کاهشی سپس خط مستقیم"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__expense
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__expense
#, python-format
msgid "Deferred Expense"
msgstr "هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Deferred Expense Account"
msgstr "حساب هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Amount"
msgstr "مقدار هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Model"
msgstr "مدل هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Model name"
msgstr "نام مدل هزینه معوق"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_expense_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_model_recognition
msgid "Deferred Expense Models"
msgstr "مدل های هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Options"
msgstr "گزینه های هزینه معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense name"
msgstr "نام هزینه معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Expense(s)"
msgstr "هزینه(ها) معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_expense_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Deferred Expenses"
msgstr "هزینه های معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Deferred Expenses Models"
msgstr "مدل های هزینه های معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__sale
#, python-format
msgid "Deferred Revenue"
msgstr "درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Deferred Revenue Account"
msgstr "حساب درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Amount"
msgstr "مقدار درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Model"
msgstr "مدل درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Model name"
msgstr "نام مدل درآمد معوق"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_model_recognition
msgid "Deferred Revenue Models"
msgstr "مدل های درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Options"
msgstr "گزینه‌های درآمد معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue name"
msgstr "نام درآمد معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Revenue(s)"
msgstr "درآمد(های) معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
#, python-format
msgid "Deferred Revenues"
msgstr "درآمدهای معوق"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Deferred Revenues Models"
msgstr "مدل های درآمد های معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred expense"
msgstr "هزینه معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred expense created"
msgstr "هزینه معوق ایجاد شد"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred revenue"
msgstr "درآمد معوق"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred revenue created"
msgstr "درآمد معوق ایجاد شد"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "مقدار قابل استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_remaining_value
msgid "Depreciable Value"
msgstr "ارزش قابل استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "مقدار استهلاک شده"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Depreciation"
msgstr "استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "حساب استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "بورد استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "تاریخ استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "سطرهای استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "روش استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_number_import
msgid "Depreciation Number Import"
msgstr "ورود شماره استهلاک"

#. module: account_asset
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "برنامه ریزی استهلاک"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Depreciation Table Report"
msgstr "گزارش جدول استهلاک"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "بورد استهلاک تغییر یافته"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s posted (%s)"
msgstr "ثبت سند استهلاک %s ارسال شده (%s)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s reversed (%s)"
msgstr "ثبت سند استهلاک %s رزرو شده (%s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "نمایش حساب دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_model_choice
msgid "Display Model Choice"
msgstr "نمایش انتخاب مدل"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal"
msgstr "اسقاط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "تاریخ اسقاط"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "انتقال اسقاط"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "انتقالهای اسقاط"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__dispose
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Dispose"
msgstr "اسقاط"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "پیشنویس"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__draft_asset_ids
msgid "Draft Asset"
msgstr "دارایی پیشنویس"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "مدت"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Duration / Rate"
msgstr "مدت زمان / نرخ"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciation Schedule"
msgstr "برنامه استهلاک موجود"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciations"
msgstr "استهلاک های موجود"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Expense"
msgstr "هزینه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Expense Account"
msgstr "حساب هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Board"
msgstr "بورد هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Date"
msgstr "تاریخ هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Lines"
msgstr "سطر های هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Expense Name"
msgstr "نام هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Recognition"
msgstr "شناسایی هزینه"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "First Depreciation"
msgstr "اولین استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "First Depreciation Date"
msgstr "تاریخ اولین استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date_import
msgid "First Depreciation Date Import"
msgstr "تاریخ ورودی استهلاک اول"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "First Recognition Date"
msgstr "تاریخ اولین شناسایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "حساب دارایی ثابت"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "دنبال‌کنندگان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "آیکون فونت عالی به عبارتی fa-tasks"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "شماره مرجع نمای فرم"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "فعالیت‌های آتی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__gain
msgid "Gain"
msgstr "سود"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "حساب سود"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_or_loss
msgid "Gain Or Loss"
msgstr "سود یا زیان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "ارزش سود"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Gross Increase"
msgstr "افزایش ناخالص"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "ارزش افزایش ناخالص"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Group By..."
msgstr "گروه بتدی بر اساس ..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "دارای پیام"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "شناسه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "آیکون"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "آیکون برای نشان دادن یک فعالیت استثنا."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "در صورت انتخاب این گزینه، برخی پیام ها خطای تحویل دارند."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata
msgid ""
"If set, specifies the start date for the first period's computation. By "
"default, it is set to the day's date rather than the Start Date of the "
"fiscal year."
msgstr ""

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model
msgid ""
"If this is selected, an expense/revenue will be created automatically when "
"Journal Items on this account are posted."
msgstr ""
"اگر این مورد انتخاب شود، با ارسال آیتم روزنامه در این حساب، هزینه/درآمدی "
"به‌طور خودکار ایجاد می‌شود."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date_import
msgid ""
"In case of an import from another software, provide the first depreciation "
"date in it."
msgstr "در صورت ورود از نرم افزار دیگری تاریخ استهلاک اول را در آن قید کنید."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__depreciation_number_import
msgid ""
"In case of an import from another software, provide the number of "
"depreciations already done before starting with Odoo."
msgstr ""
"در صورت ورود از نرم افزار دیگری، تعداد استهلاک های انجام شده را قبل از شروع "
"Odoo ارائه دهید."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"در صورت ورود از نرم افزار دیگری، ممکن است لازم باشد از این قسمت برای داشتن "
"گزارش جدول استهلاک مناسب استفاده کنید. این مقداری است که قبلاً با ورودی های "
"محاسبه نشده از این مدل مستهلک شده است"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid ""
"In percent.<br>For a linear method, the depreciation rate is computed per "
"year.<br>For a declining method, it is the declining factor"
msgstr ""
"بر حسب درصد.<br>برای روش خطی، نرخ استهلاک در هر سال محاسبه می‌شود.<br>برای "
"روش نزولی، این عامل نزولی است."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Increase Accounts"
msgstr "حساب های افزایش"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_line_id
msgid "Invoice Line"
msgstr "سطر فاکتور"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "دنبال می کند"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr ""
"مقداری است که برنامه‌ریزی می‌کنید داشته باشید که نمی‌توانید مستهلک کنید."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Journal"
msgstr "روزنامه"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Journal Entries"
msgstr "داده های روزنامه"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "داده روزنامه"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "آیتم روزنامه"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
#, python-format
msgid "Journal Items"
msgstr "آیتم‌های دفترروزنامه‌"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"Journal Items of {account} should have a label in order to generate an asset"
msgstr "برای ایجاد دارایی، آیتم روزنامه {account} باید دارای برچسب باشند"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "آخرین به روز رسانی در"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "فعالیت‌های دیرشده"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Linear"
msgstr "خطی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__loss
msgid "Loss"
msgstr "زیان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "حساب زیان"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "پیوست اصلی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "مدیریت آیتم‌ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "پیام‌ها"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
#, python-format
msgid "Method"
msgstr "روش"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "مدل"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification"
msgstr "تغییر دادن"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification reason"
msgstr "دلیل تغییر دادن"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "تغییر"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#, python-format
msgid "Modify Asset"
msgstr "تغییر دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "تغییر استهلاک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Modify Expense"
msgstr "تغییر هزینه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Modify Revenue"
msgstr "درآمد تغییر"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "ماه‌ها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "دارایی های متعدد در هر سظز"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"بسته به مقدار سطر صورتحساب به جای 1 دارایی جهانی، چندین مورد دارایی ایجاد "
"خواهد شد."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "موعد فعالیت من"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__need_date
msgid "Need Date"
msgstr "تاریخ نیاز"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "New Values"
msgstr "مقادیر جدید"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "مقدار باقیمانده جدید برای دارایی"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "مقدار نجات یافته جدید برای دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "رویداد تقویم فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "خلاصه فعالیت بعدی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "نوع فعالیت بعدی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Next Period Expense"
msgstr "هزینه دوره بعدی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Next Period Revenue"
msgstr "درآمد دوره بعدی"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__no
msgid "No"
msgstr "خیر"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "No asset account"
msgstr "بدون حساب دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "مقدار قابل استهلاک نیست"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "ارزش قابل استهلاک نیست"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"توجه داشته باشید که این تاریخ در مورد دارایی‌های prorata temporis، محاسبه "
"اولین سند رزنامه را تغییر نمی‌دهد. به سادگی تاریخ حسابداری خود را تغییر می "
"دهد"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__number_asset_ids
msgid "Number Asset"
msgstr "شماره دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Number of Depreciations"
msgstr "تعداد استهلاکها"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "تعداد ماه‌ها در دوره"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Number of Recognitions"
msgstr "تعداد شناسایی ها"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "تعداد دارایی های ساخته شده برای افزایش ارزش دارایی"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "تعداد ثبت سند های استهلاک (ارسال شده یا نشده)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "تعداد پیام ها که نیاز به عمل"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "تعداد پیام‌های خوانده نشده"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "در انتظار"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.line_caret_options
msgid "Open Asset"
msgstr "باز کردن دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "مقدار اولیه"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "والد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
msgid "Pause"
msgstr "مکث"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_pause
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#, python-format
msgid "Pause Asset"
msgstr "مکث دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Pause Depreciation"
msgstr "مکث استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__date
msgid "Pause date"
msgstr "تاریخ مکث"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Period length"
msgstr "طول دوره"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "ثبت سند های ارسال شده"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "تاریخ سرشکن"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata
msgid "Prorata Temporis"
msgstr ""

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__purchase
msgid "Purchase: Asset"
msgstr "خرید: دارایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Reason"
msgstr "علت"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Related Expenses"
msgstr "هزینه های مربوطه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Related Purchase"
msgstr "خرید مربوطه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Related Sales"
msgstr "خرید مربوطه"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Report of reversal for {name}"
msgstr "گزارش برگشت برای {name}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Reset to running"
msgstr "بر گرداندن به در حال اجرا"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Residual Amount to Recognize"
msgstr "مقدار باقیمانده برای شناسایی"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "کاربر مسئول"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "از سرگیری استهلاک"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#, python-format
msgid "Revenue"
msgstr "درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Revenue Account"
msgstr "حساب درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Board"
msgstr "بورد درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Date"
msgstr "تاریخ درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Lines"
msgstr "سطر های درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
msgid "Revenue Name"
msgstr "نام درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Recognition"
msgstr "شناسایی درآمد"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__reversal_move_id
msgid "Reversal Move"
msgstr "حرکت معکوس"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"به منظور اصلاح استهلاک، ورودی های استهلاک ارسال شده در آینده را معکوس کنید"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "در حال اجرا"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Sale"
msgstr "فروش"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__sale
msgid "Sale: Revenue Recognition"
msgstr "فروش: تشخیص درآمد"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save Model"
msgstr "مدل ذخیره"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Save model"
msgstr "مدل ذخیره"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "انتخاب سطر فاکتور"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Sell"
msgstr "فروش"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
#, python-format
msgid "Sell Asset"
msgstr "فروش دارایی"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Sell or Dispose"
msgstr "فروش یا اسقاط"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set manually the original values or"
msgstr "دستی ارزش اولیه را تعیین کن یا"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "تبدیل به پیشنویس"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "تعیین به عنوان در حال اجرا"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "تمام رکوردهایی که تاریخ اقدام بعدی آن قبل از امروز است را نشان بده"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Some fields are missing {}"
msgstr "بعضی فیلد ها موجود نیستند {}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Some required values are missing"
msgstr "بعضی مقادیر مورد نیاز موجود نیستند"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Start Depreciating"
msgstr "شروع استهلاک"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "وضعیت"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"وضعیت بر اساس فعالیت‌ها\n"
"سررسید شده: تاریخ سررسید گذشته است\n"
"امروز: تاریخ فعالیت امروز است\n"
"برنامه‌ریزی شده: فعالیت‌های آینده."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "خط مستقیم"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"مجموع ارزش استهلاک پذیر، ارزش نجات یافته و ارزش دفتری همه اقلام افزایش می "
"یابد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__can_create_asset
msgid ""
"Technical field specifying if the account can generate asset depending on "
"it's type. It is used in the account form view."
msgstr ""
"فیلد فنی که مشخص می کند آیا حساب می تواند بسته به نوع آن دارایی ایجاد کند یا"
" خیر. در نمای فرم حساب کاربری استفاده می شود."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_value
msgid ""
"Technical field to know if we should display the fields for the creation of "
"gross increase asset"
msgstr ""
"فیلد فنی برای اینکه بدانیم آیا باید فیلدهایی را برای ایجاد دارایی افزایش "
"ناخالص نمایش دهیم"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_or_loss
msgid ""
"Technical field to know is there was a gain or a loss in the selling of the "
"asset"
msgstr ""
"فیلد فنی برای دانستن این است که در فروش دارایی سود یا زیان وجود داشته است"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "میزان زمان بین دو استهلاک"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "دارایی که باید تغییر داده شود توسط این ویزارد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "زیر سطرها سود ارزش این دارایی هستند"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_id
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr "فاکتور دارایی ها برای ایجاد سند اختتامیه مورد نیاز است."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "تعداد استهلاکهای مورد نیاز برای مستهلک شدن دارایی شما"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "The remaining value on the last depreciation line must be 0"
msgstr "مقدار باقیمانده در آخرین خط استهلاک باید 0 باشد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_line_id
msgid "There are multiple lines that could be the related to this asset"
msgstr "چندین خط وجود دارد که می تواند مربوط به این دارایی باشد"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_value_change
msgid ""
"This is a technical field set to true when this move is the result of the "
"changing of value of an asset"
msgstr ""
"زمانی که این انتقال نتیجه تغییر ارزش دارایی باشد، این یک فیلد فنی است که روی"
" true تنظیم شده است"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_manually_modified
msgid ""
"This is a technical field stating that a depreciation line has been manually"
" modified. It is used to recompute the depreciation table of an "
"asset/deferred revenue."
msgstr ""
"این یک فیلد فنی است که بیان می کند یک خط استهلاک به صورت دستی اصلاح شده است."
" برای محاسبه مجدد جدول استهلاک یک دارایی/درآمد معوق استفاده می شود."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset_reversed_widget.js:0
#, python-format
msgid "This move has been reversed"
msgstr "این انتقال معکوس شده است"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "فعالیت‌های امروز"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Total"
msgstr "جمع کل"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Trying to pause an asset without any future depreciation line"
msgstr "تلاش برای توقف موقت یک دارایی بدون هیچ خط استهلاک آتی"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred expense"
msgstr "به هزینه معوق تبدیل کن"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred revenue"
msgstr "درآمد معوق تبدیل کن"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as an asset"
msgstr "به دارایی تبدیل کن"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__user_type_id
msgid "Type of the account"
msgstr "نوع حساب"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع فعالیت استثنایی برای رکورد."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread
msgid "Unread Messages"
msgstr "پیام های ناخوانده"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "شمارنده پیام‌های خوانده‌نشده"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value decrease for: %(asset)s"
msgstr "کاهش ارزش برای: %(asset)s"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value increase for: %(asset)s"
msgstr "افزایش ارزش برای: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "پیام‌های وب‌سایت"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباط با وبسایت"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"هنگامی که یک دارایی ایجاد می شود، وضعیت \"پیش نویس\" است.\n"
"در صورت تایید دارایی، وضعیت به حالت \"در حال اجرا\" می رود و خطوط استهلاک می تواند در حسابداری ثبت شود.\n"
"هنگامی که می‌خواهید استهلاک دارایی را برای مدتی متوقف کنید، وضعیت «در انتظار» را می‌توان به صورت دستی تنظیم کرد.\n"
"وقتی استهلاک تمام شد، می‌توانید به صورت دستی یک دارایی را ببندید. اگر آخرین خط استهلاک پست شود، دارایی به طور خودکار در آن وضعیت قرار می گیرد."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "سال"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot archive a record that is not closed"
msgstr "شما نمی توانید رکوردی که بسته نشده بایگانی کنید"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_sell.py:0
#, python-format
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"شما نمی توانید برای یک ثبت سند روزنامه برای یک دارایی که یک افزایش ناخالص "
"جاری دارد، اتوماسیون بگذارید. لطفا از 'مستهلک کن' برای افزایش(ها) استفاده "
"کنید."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"شما نمی توانید دارایی را از خطوط حاوی بستانکاری و بدهی حساب یا با مبلغ صفر "
"ایجاد کنید"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"شما نمی‌توانید یک دارایی مرتبط با سندهای ثبت شضده را حذف کنید. \n"
"شما باید دارایی را تأیید کنید، بفروشید یا آن را واگذار کنید و یا اینکه سندهای دفتر روزنامه‌ مربوطه را لغو کنید."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr "شما نمی توانید یک سند که در حالت %s است را حذف کنید."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot pause an asset with posted depreciation lines in the "
"future.without reverting them."
msgstr ""

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an entry having a posted deferred revenue/expense"
msgstr ""
"شما نمی توانید یک ثبت سند که دارای یک درآمد/هزینه معوق است را به پیشنویس "
"برگردانید"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "e.g. Annual Subscription"
msgstr "مثلا اشتراک سالانه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "e.g. Annually Paid Insurance"
msgstr "مثلا بیمه پرداخت شده سالانه"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "برای مثال لب تاب آی بوک"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "select the related purchases"
msgstr "خریدهای مربوطه را انتخاب کنید"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "this move"
msgstr "این انتقال"
