# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr " (copia)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "N. registrazioni ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "N. incrementi lordi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "N. registrazioni ammortamento confermate"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "%s created from invoice"
msgstr "%s creato dalla fattura"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s m"
msgstr "%s m"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s y"
msgstr "%s a"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "(prorata entry)"
msgstr "(registrazione pro rata)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred expense has been created for this move:"
msgstr "È stato creato un risconto attivo per il movimento:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred revenue has been created for this move:"
msgstr "È stato creato un risconto passivo per il movimento:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A document linked to %s has been deleted: "
msgstr "È stato eliminato un documento collegato a %s: "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "A gross increase has been created"
msgstr "È stato creato un incremento lordo"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "Conto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Account Asset Counterpart"
msgstr "Contropartita cespite conto"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_assets_report
msgid "Account Assets Report"
msgstr "Rendiconto cespiti conto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
msgid "Account Depreciation"
msgstr "Ammortamento conto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
msgid "Account Depreciation Expense"
msgstr "Costo di ammortamento conto"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Tipo di conto viene usato a scopo informativo, per generare resoconti legali"
" specifici per paese, impostare le regole di chiusura di un anno fiscale e "
"generare le registrazioni di apertura."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Conto usato nelle registrazioni di ammortamento, per diminuire il valore del"
" cespite."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Conto usato nelle registrazioni periodiche, per registrare una parte del "
"cespite come costo."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to recognize the expense"
msgstr "Conto utilizzato per riconoscere i costi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to recognize the revenue"
msgstr "Conto utilizzato per riconoscere i ricavi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to record the deferred expense"
msgstr "Conto usato per registrare il risconto attivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to record the deferred income"
msgstr "Conto usato per registrare il risconto passivo"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""
"Conto usato per registrare l'acquisto del cespite al suo prezzo originale."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr ""
"Conto utilizzato per registrare il movimento contabile in caso di "
"plusvalenza"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"Conto utilizzato per registrare il movimento contabile in caso di "
"plusvalenza da vendita di un cespite"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr ""
"Conto utilizzato per registrare il movimento contabile in caso di "
"minusvalenza"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"Conto utilizzato per registrare il movimento contabile in caso di "
"minusvalenza da vendita di un cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "Contabilità"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
#, python-format
msgid "Acquisition Date"
msgstr "Data di acquisizione"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__action
msgid "Action"
msgstr "Azione"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "Attivo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo di attività"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same account"
msgstr "Le righe devono provenire tutte dallo stesso conto"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same company"
msgstr "Tutte le righe devono appartenere alla stessa azienda"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same move type"
msgstr "Le righe devono provenire tutte dallo stesso tipo di movimento"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be posted"
msgstr "Le righe devono essere tutte confermate"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "Importazione importi già ammortizzati"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr ""
"Un cespite ha un cespite primario quando è il risultato del valore della "
"plusvalenza"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "An asset has been created for this move:"
msgstr "È stato creato un cespite per il movimento:"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_analytic_id
msgid "Analytic Account"
msgstr "Conto analitico"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Etichetta analitica"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Archived"
msgstr "In archivio"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__purchase
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#, python-format
msgid "Asset"
msgstr "Cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Conto cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Asset Gross Increase Account"
msgstr "Conto incremento lordo cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "Nome visualizzato ID cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids_display_name
msgid "Asset Ids Display Name"
msgstr "Nome visualizzato ID cespiti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
msgid "Asset Linked"
msgstr "Cespite collegato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_manually_modified
msgid "Asset Manually Modified"
msgstr "Cespite modificato manualmente"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Asset Model"
msgstr "Modello cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "Nome modello cespite"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Asset Models"
msgstr "Modelli cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "Nome cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Options"
msgstr "Opzioni cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_asset_type
msgid "Asset Type"
msgstr "Tipo cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_value_change
msgid "Asset Value Change"
msgstr "Variazione valore cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "Valori cespite"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "Cespite creato"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_move_line__asset_ids
msgid "Asset created from this Journal Item"
msgstr "Cespite creato dal movimento contabile"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset paused"
msgstr "Cespite sospeso"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""
"Cespite venduto o dismesso. Registrazioni contabili in attesa di conferma."

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Asset unpaused"
msgstr "Cespite riattivato"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Asset(s)"
msgstr "Cespite/i"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Riconoscimento cespite/ricavo"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_purchase_tree
#, python-format
msgid "Assets"
msgstr "Cespiti"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Cespiti e ricavi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Cespiti con stato chiuso"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Cespiti con stato bozza e aperto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "Cespite automatico"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Expense"
msgstr "Risconto attivo automatico"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Revenue"
msgstr "Risconto passivo automatico"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "Automazione"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
#, python-format
msgid "Book Value"
msgstr "Valore contabile"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "Può creare il cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Cancel"
msgstr "Annulla"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Category of asset"
msgstr "Categoria del cespite"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Characteristics"
msgstr "Caratteristiche"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "Secondari"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"Scegliere il metodo da utilizzare per calcolare l'importo delle righe di ammortamento.\n"
"  * Linea retta: calcolato in base a Valore lordo / Numero di ammortamenti\n"
"  * Decrescente: calcolato in base a Valore residuo * Fattore decrescente\n"
"  * Decrescente poi linea retta: come decrescente ma con un valore di ammortamento minimo pari al valore a linea retta."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Chiuso"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__company_id
msgid "Company"
msgstr "Azienda"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "Calcola ammortamento"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Compute Expense"
msgstr "Calcola costo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Compute Revenue"
msgstr "Calcola ricavo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "Conferma"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree_grouped_inherit
msgid "Create Asset"
msgstr "Crea cespite"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "Crea e valida"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "Crea in bozza"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "Crea un nuovo accentramento"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "Crea un nuovo modello di asset"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_form
msgid "Create new deferred expense"
msgstr "Crea nuovi risconti attivi"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_model_form
msgid "Create new deferred expense model"
msgstr "Crea nuovo modello di risconti attivi"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_form
msgid "Create new deferred revenue"
msgstr "Creare nuovi ricavi differiti"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_model_form
msgid "Create new deferred revenue model"
msgstr "Crea un nuovo modello di ricavi differiti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Ammortamento cumulativo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Cumulative Expense"
msgstr "Costo cumulativo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Cumulative Revenue"
msgstr "Ricavo cumulativo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Attuale"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "Valori attuali"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_id
msgid "Customer Invoice"
msgstr "Fattura cliente"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Data"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Dec. then Straight"
msgstr "Dec. poi linea retta"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
#, python-format
msgid "Declining"
msgstr "Decrescente"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "Fattore decrescente"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "Decrescente poi linea retta"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__expense
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__expense
#, python-format
msgid "Deferred Expense"
msgstr "Risconto attivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Deferred Expense Account"
msgstr "Conto risconti attivi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Amount"
msgstr "Importo risconto attivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Model"
msgstr "Modello per risconto attivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Model name"
msgstr "Nome modello risconto attivo"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_expense_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_model_recognition
msgid "Deferred Expense Models"
msgstr "Modelli per risconto attivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Options"
msgstr "Opzioni risconto attivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense name"
msgstr "Nome risconto attivo"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Expense(s)"
msgstr "Risconti attivi"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_expense_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Deferred Expenses"
msgstr "Risconti attivi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Deferred Expenses Models"
msgstr "Modelli per risconti attivi"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__sale
#, python-format
msgid "Deferred Revenue"
msgstr "Risconto passivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Deferred Revenue Account"
msgstr "Conto risconti passivi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Amount"
msgstr "Importo risconto passivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Model"
msgstr "Modello per risconto passivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Model name"
msgstr "Nome modello risconto passivo"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_model_recognition
msgid "Deferred Revenue Models"
msgstr "Modelli per risconto passivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Options"
msgstr "Opzioni risconto passivo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue name"
msgstr "Nome risconto passivo"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Revenue(s)"
msgstr "Risconti passivi"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
#, python-format
msgid "Deferred Revenues"
msgstr "Risconti passivi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Deferred Revenues Models"
msgstr "Modelli per risconti passivi"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred expense"
msgstr "Risconto attivo"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred expense created"
msgstr "Risconto attivo creato"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred revenue"
msgstr "Risconto passivo"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred revenue created"
msgstr "Risconto passivo creato"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "Importo ammortizzabile"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_remaining_value
msgid "Depreciable Value"
msgstr "Valore ammortizzabile"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "Importo ammortizzato"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Depreciation"
msgstr "Ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "Conto di ammortamento"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "Tabella ammortamenti"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "Data ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "Righe ammortamento"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "Metodo di ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_number_import
msgid "Depreciation Number Import"
msgstr "Numero ammortamenti importati"

#. module: account_asset
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "Piano di ammortamento"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Depreciation Table Report"
msgstr "Rendiconto tabella ammortamenti"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "Tabella ammortamenti modificata"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s posted (%s)"
msgstr "Registrazione di ammortamento %s confermata (%s)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s reversed (%s)"
msgstr "Registrazione di ammortamento %s stornata (%s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "Visualizza cespite conto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_model_choice
msgid "Display Model Choice"
msgstr "Visualizza scelta modello"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal"
msgstr "Dismissione"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "Data dismissione"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "Movimento di dismissione"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "Movimenti di dismissione"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__dispose
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Dispose"
msgstr "Dismetti"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "Bozza"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__draft_asset_ids
msgid "Draft Asset"
msgstr "Cespite in bozza"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "Durata"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Duration / Rate"
msgstr "Durata / Tasso"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciation Schedule"
msgstr "Piano di ammortamento esistente"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciations"
msgstr "Ammortamenti esistenti"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Expense"
msgstr "Costo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Expense Account"
msgstr "Conto di costo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Board"
msgstr "Tabella costi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Date"
msgstr "Data costo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Lines"
msgstr "Righe costi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Expense Name"
msgstr "Nome costo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Recognition"
msgstr "Riconoscimento costi"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "First Depreciation"
msgstr "Primo ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "First Depreciation Date"
msgstr "Data primo ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date_import
msgid "First Depreciation Date Import"
msgstr "Data primo ammortamento importato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "First Recognition Date"
msgstr "Data primo riconoscimento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "Conto immobilizzazioni"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "Rif. vista scheda"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "Attività future"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__gain
msgid "Gain"
msgstr "Plusvalenza"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "Conto plusvalenza"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_or_loss
msgid "Gain Or Loss"
msgstr "Plusvalenza o minusvalenza"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "Valore plusvalenza"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Gross Increase"
msgstr "Incremento lordo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "Valore incremento lordo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Group By..."
msgstr "Raggruppa per..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata
msgid ""
"If set, specifies the start date for the first period's computation. By "
"default, it is set to the day's date rather than the Start Date of the "
"fiscal year."
msgstr ""
"Se impostato, specifica la data di inizio per il calcolo del primo periodo. "
"Per impostazione predefinita, è impostata alla data del giorno piuttosto che"
" alla data di inizio dell'anno fiscale."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model
msgid ""
"If this is selected, an expense/revenue will be created automatically when "
"Journal Items on this account are posted."
msgstr ""
"Se selezionata, viene creato automaticamente un costo/ricavo quando vengono "
"confermati i movimenti contabili sul conto."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date_import
msgid ""
"In case of an import from another software, provide the first depreciation "
"date in it."
msgstr ""
"In caso di importazione da altro software, fornire la data del primo "
"ammortamento."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__depreciation_number_import
msgid ""
"In case of an import from another software, provide the number of "
"depreciations already done before starting with Odoo."
msgstr ""
"In caso di importazione da altro software, prima di iniziare con Odoo "
"fornire il numero di ammortamenti già effettuati."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"In caso di importazione da un altro software, potrebbe essere necessario "
"utilizzare questo campo per avere il resoconto corretto della tabella di "
"ammortamento. Si tratta del valore già ammortizzato con registrazioni non "
"calcolate da questo modello."

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid ""
"In percent.<br>For a linear method, the depreciation rate is computed per "
"year.<br>For a declining method, it is the declining factor"
msgstr ""
"In percentuale.<br>Nel metodo lineare il tasso di ammortamento viene "
"calcolato per anno.<br>Nel metodo decrescente è il fattore di diminuzione"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Increase Accounts"
msgstr "Conti incremento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_line_id
msgid "Invoice Line"
msgstr "Riga fattura"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "È l'importo previsto che non è possibile ammortizzare."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Journal"
msgstr "Registro"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Journal Entries"
msgstr "Registrazioni contabili"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Movimento contabile"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
#, python-format
msgid "Journal Items"
msgstr "Movimenti contabili"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"Journal Items of {account} should have a label in order to generate an asset"
msgstr ""
"Per generale un cespite i movimenti contabili di {account} devono avere "
"un'etichetta"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "Attività in ritardo"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Linear"
msgstr "Lineare"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__loss
msgid "Loss"
msgstr "Minusvalenza"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "Conto minusvalenza"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "Gestione movimenti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
#, python-format
msgid "Method"
msgstr "Metodo"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "Modello"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification"
msgstr "Modifica"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification reason"
msgstr "Motivo della modifica"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Modifica"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#, python-format
msgid "Modify Asset"
msgstr "Modifica cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "Modifica ammortamento"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Modify Expense"
msgstr "Modifica costo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Modify Revenue"
msgstr "Modifica ricavo"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "Mesi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "Cespiti multipli per riga"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"Vengono generati movimenti cespiti multipli, invece di 1 cespite globale, in"
" funzione della quantità della riga fattura fornitore."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mia attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__need_date
msgid "Need Date"
msgstr "Data di necessità"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "New Values"
msgstr "Nuovi valori"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "Nuovo importo residuo per il cespite"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "Nuovo valore di recupero per il cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Prossimo evento del calendario delle attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Tipologia prossima attività"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Next Period Expense"
msgstr "Costo prossimo periodo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Next Period Revenue"
msgstr "Ricavo prossimo periodo"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__no
msgid "No"
msgstr "No"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "No asset account"
msgstr "Nessun conto cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "Importo non ammortizzabile"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "Valore non ammortizzabile"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"Nota: questa data non altera il calcolo della prima registrazione contabile "
"in caso di cespiti pro rata temporis, cambia semplicemente la data "
"contabile."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__number_asset_ids
msgid "Number Asset"
msgstr "Numero cespiti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Number of Depreciations"
msgstr "Numero di ammortamenti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "Numero di mesi in un periodo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Number of Recognitions"
msgstr "Numero di riconoscimenti"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "Numero di cespiti creati per incrementarne il valore"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "Numero delle registrazioni di ammortamento (confermate o no)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "Numero di messaggi non letti"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "In attesa"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.line_caret_options
msgid "Open Asset"
msgstr "Apri cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "Valore originale"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "Primario"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
msgid "Pause"
msgstr "Sospendi"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_pause
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#, python-format
msgid "Pause Asset"
msgstr "Sospendi cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Pause Depreciation"
msgstr "Sospendi ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__date
msgid "Pause date"
msgstr "Data di sospensione"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Period length"
msgstr "Lunghezza periodo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "Registrazioni confermate"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "Data pro rata"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata
msgid "Prorata Temporis"
msgstr "Pro rata temporis"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__purchase
msgid "Purchase: Asset"
msgstr "Acquisto: cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Reason"
msgstr "Causale"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Related Expenses"
msgstr "Costi correlati"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Related Purchase"
msgstr "Acquisti correlati"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Related Sales"
msgstr "Vendite correlate"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Report of reversal for {name}"
msgstr "Resoconto di storno per {name}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Reset to running"
msgstr "Reimposta a in corso"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Residual Amount to Recognize"
msgstr "Importo residuo da riconoscere"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "Riprendi ammortamento"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#, python-format
msgid "Revenue"
msgstr "Ricavo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Revenue Account"
msgstr "Conto di ricavo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Board"
msgstr "Tabella ricavi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Date"
msgstr "Data ricavo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Lines"
msgstr "Righe ricavi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
msgid "Revenue Name"
msgstr "Nome ricavo"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Recognition"
msgstr "Riconoscimento ricavi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__reversal_move_id
msgid "Reversal Move"
msgstr "Movimento di storno"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"Per modificare l'ammortamento stornare le relative registrazioni confermate "
"nel futuro"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "In corso"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Sale"
msgstr "Vendita"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__sale
msgid "Sale: Revenue Recognition"
msgstr "Vendite: riconoscimento ricavi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save Model"
msgstr "Salva modello"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Save model"
msgstr "Salva modello"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "Seleziona riga fattura"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Sell"
msgstr "Vendi"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
#, python-format
msgid "Sell Asset"
msgstr "Vendi cespite"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Sell or Dispose"
msgstr "Vendi o dismetti"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set manually the original values or"
msgstr "Impostare manualmente i valori originali o"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "Imposta a bozza"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "Imposta a in corso"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "Mostra tutti i record con data prossima azione precedente a oggi"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Some fields are missing {}"
msgstr "Mancano alcuni campi {}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Some required values are missing"
msgstr "Mancano alcuni campi obbligatori"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Start Depreciating"
msgstr "Avvia ammortamento"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "Stato"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato basato sulle attività\n"
"In ritardo: scadenza già superata\n"
"Oggi: attività in data odierna\n"
"Pianificato: attività future."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "Linea retta"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"Somma di valore ammortizzabile, valore di recupero e valore contabile di "
"tutti i movimenti di incremento del valore"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__can_create_asset
msgid ""
"Technical field specifying if the account can generate asset depending on "
"it's type. It is used in the account form view."
msgstr ""
"Campo tecnico che specifica se il conto può generare un cespite a seconda "
"della sua tipologia. Viene utilizzato nella vista scheda del conto."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_value
msgid ""
"Technical field to know if we should display the fields for the creation of "
"gross increase asset"
msgstr ""
"Campo tecnico per sapere se devono essere visualizzati i campi per la "
"creazione del cespite di incremento lordo"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_or_loss
msgid ""
"Technical field to know is there was a gain or a loss in the selling of the "
"asset"
msgstr ""
"Campo tecnico per sapere se dalla vendita di un cespite è stata ottenuta una"
" plusvalenza o una minusvalenza"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "Quantità di tempo fra due ammortamenti"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "Cespite da modificare con la procedura guidata"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "I cespiti secondari sono le plusvalenze in valore del cespite"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_id
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr ""
"Per generare la registrazione contabile di chiusura è necessaria la fattura "
"di dismissione."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Numero di ammortamenti necessari per ammortizzare il cespite"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "The remaining value on the last depreciation line must be 0"
msgstr "Il valore residuo dell'ultima riga di ammortamento deve essere 0"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_line_id
msgid "There are multiple lines that could be the related to this asset"
msgstr "È presente più di una riga che può essere correlata al cespite"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_value_change
msgid ""
"This is a technical field set to true when this move is the result of the "
"changing of value of an asset"
msgstr ""
"Capo tecnico impostato a vero quando il movimento è conseguenza della "
"variazione di valore di un cespite"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_manually_modified
msgid ""
"This is a technical field stating that a depreciation line has been manually"
" modified. It is used to recompute the depreciation table of an "
"asset/deferred revenue."
msgstr ""
"Campo tecnico che indica che una riga di ammortamento è stata modificata in "
"modo manuale. Viene utilizzato per ricalcolare la tabella di ammortamento di"
" un cespite/risconto passivo."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset_reversed_widget.js:0
#, python-format
msgid "This move has been reversed"
msgstr "Il movimento è stato stornato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "Attività odierne"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Total"
msgstr "Totale"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Trying to pause an asset without any future depreciation line"
msgstr ""
"Tentativo di sospensione di un cespite senza alcuna riga di ammortamento "
"futuro"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred expense"
msgstr "Gira come risconto attivo"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred revenue"
msgstr "Gira come risconto passivo"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as an asset"
msgstr "Gira a cespite"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__user_type_id
msgid "Type of the account"
msgstr "Tipo di conto"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread
msgid "Unread Messages"
msgstr "Messaggi non letti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Numero messaggi non letti"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value decrease for: %(asset)s"
msgstr "Diminuzione di valore per: %(asset)s"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value increase for: %(asset)s"
msgstr "Incremento di valore per: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Quando viene creato un cespite, lo stato è «Bozza».\n"
"Se il cespite viene confermato, lo stato passa a «In corso» e le righe di ammortamento possono essere confermate in contabilità.\n"
"Lo stato «In attesa» può essere impostato manualmente quando si vuole sospendere per qualche tempo l'ammortamento di un cespite.\n"
"Quando l'ammortamento è terminato è possibile chiudere manualmente un cespite. Se viene confermata l'ultima riga di ammortamento, il cespite passa automaticamente allo stato «Chiuso»."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "Anni"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot archive a record that is not closed"
msgstr "Impossibile archiviare un record che non è chiuso"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_sell.py:0
#, python-format
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"Impossibile automatizzare la registrazione contabile di un cespite con un "
"incremento lordo in corso. Utilizzare \"Dismetti\" negli incrementi."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"Impossibile creare un cespite da righe contenenti dare e avere sul conto o "
"con un importo nullo."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"Impossibile eliminare un cespite collegato a registrazioni confermate.\n"
"Il cespite deve essere confermato, quindi venduto o dismesso, oppure devono essere annullate le relative registrazioni."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr "Impossibile eliminare un documento con stato %s."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot pause an asset with posted depreciation lines in the "
"future.without reverting them."
msgstr ""
"Impossibile sospendere un cespite con righe di ammortamento future "
"confermate senza stornarle."

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an entry having a posted deferred revenue/expense"
msgstr ""
"Impossibile reimpostare a bozza una registrazione con un risconto "
"attivo/passivo confermato"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "e.g. Annual Subscription"
msgstr "es. Abbonamento annuale"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "e.g. Annually Paid Insurance"
msgstr "es. Assicurazione con pagamento annuale"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "es. Portatile iBook"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "select the related purchases"
msgstr "seleziona i relativi acquisti"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "this move"
msgstr "questo movimento"
