# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# Batmu<PERSON><PERSON>anbat <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# hish, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Bayarkhuu Bataa, 2023
# Baskhuu <PERSON>huu <<EMAIL>>, 2023
# Torbat Jargalsaikhan, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Torbat Jargalsaikhan, 2023\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr "(Хуулбарлах)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# Элэгдлийн бичилтүүд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# Капиталжсан нэмэгдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# Батлагдсан элэгдлийн бичилтүүд"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "%s created from invoice"
msgstr "%s нэхэмжлэлээс үүссэн"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s m"
msgstr "%s с"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "%s y"
msgstr "%s ж"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "(prorata entry)"
msgstr "(эхний удаагийн бичилт)"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred expense has been created for this move:"
msgstr "Энэхүү гүйлгээгээр урьдчилж тооцсон зардлын бүртгэл үүслээ:"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A deferred revenue has been created for this move:"
msgstr "Энэхүү гүйлгээгээр урьдчилж тооцсон орлогын бүртгэл үүслээ."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "A document linked to %s has been deleted: "
msgstr "%s-тай холбогдсон баримт устгагдлаа: "

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "A gross increase has been created"
msgstr "Капиталжуулалт бүхий хөрөнгө бүртгэгдлээ"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "Данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Account Asset Counterpart"
msgstr "Хөрөнгийн харьцсан данс"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_assets_report
msgid "Account Assets Report"
msgstr "Хөрөнгийн тайлангийн данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
msgid "Account Depreciation"
msgstr "Хуримтлагдсан элэгдлийн данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
msgid "Account Depreciation Expense"
msgstr "Элэгдлийн зардлын данс"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__user_type_id
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Дансны бүлэг нь тухайн улс орны хуулиас хамаарсан тайлангуудыг "
"боловсруулахад хэрэглэгдэхээс гадна санхүүгийн жил өндөрлөх үеийн автомат "
"бичилтүүдийн дүрэм болж хэрэглэгдэнэ."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Хөрөнгийн элэгдэлийн бичилт буюу хөрөнгийн өртөгийг хорогдуулах гүйлгээнд "
"ашиглагдах данс."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Тогтмол хугацаагаар хөрөнгийн хэсгийг зардалд тусгах ажил гүйлгээнд "
"хэрэглэгдэх данс."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to recognize the expense"
msgstr "Зардал бүрдүүлэхэд ашиглагдах данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to recognize the revenue"
msgstr "Орлого бүрдүүлэхэд ашиглах данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Account used to record the deferred expense"
msgstr "Урьдчилж тооцсон зардал бүртгэхэд ашиглах данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Account used to record the deferred income"
msgstr "Орлого хуримтлуулалт бүртгэх данс"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr "Хөрөнгийг худалдаж авсан үнэ буюу анхны өртөгийг бүртгэх данс."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr "Олз бүртгэх үед ашиглагдах данс"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr "Хөрөнгийг зарж борлуулах үед үүсэх олзыг бүртгэхэд ашиглагдах данс"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "Гарз бүртгэх үед ашиглагдах данс"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr "Хөрөнгийг зарж борлуулах үед үүсэх гарзыг бүртгэхэд ашиглагдах данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "Санхүү"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
#, python-format
msgid "Acquisition Date"
msgstr "Худалдаж авсан огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__action
msgid "Action"
msgstr "Үйлдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Үйлдэл шаардсан"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "Идэвхитэй"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "Ажилбар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ажилбарын тайлбар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "Ажилбарын төлөв"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ажилбарын төрлийн зураг"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be from the same account"
msgstr "Бүх мөрүүд ижил данстай байх ёстой"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "All the lines should be from the same move type"
msgstr "Бүх мөрүүд ижил гүйлгээний төрөлтэй байх ёстой"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "All the lines should be posted"
msgstr "Бүх мөрүүд батлагдсан байх ёстой"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "Элэгдлийн эхний үлдэгдэл дүн"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr ""
"Өртөгийг нэмэгдүүлж буй тохиолдол бол энэхүү хөрөнгө нь эцэг хөрөнгөтэй байх"
" болно"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "An asset has been created for this move:"
msgstr "Энэхүү гүйлгээгээр үндсэн хөрөнгийн бүртгэл үүслээ."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_analytic_id
msgid "Analytic Account"
msgstr "Шинжилгээний данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Шинжилгээний пайз"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Archived"
msgstr "Архивласан"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__purchase
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#, python-format
msgid "Asset"
msgstr "Хөрөнгө"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Хөрөнгийн данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Asset Gross Increase Account"
msgstr "Хөрөнгийн өртөг нэмэгдүүлэх данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "Хөрөнгийн дэлгэрэнгүй нэр"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids_display_name
msgid "Asset Ids Display Name"
msgstr "Хөрөнгүүдийн дэлгэрэнгүй нэр"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
msgid "Asset Linked"
msgstr "Хөрөнгө холбогдсон"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_manually_modified
msgid "Asset Manually Modified"
msgstr "Хөрөнгийг гараар өөрчилсөн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Asset Model"
msgstr "Хөрөнгийн загвар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "Хөрөнгийн загварын нэр"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Asset Models"
msgstr "Хөрөнгийн загвар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "Хөрөнгийн нэр"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Options"
msgstr "Хөрөнгийн сонголтууд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_asset_type
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_asset_type
msgid "Asset Type"
msgstr "Хөрөнгийн төрөл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_value_change
msgid "Asset Value Change"
msgstr "Хөрөнгийн өртөг өөрчлөлт"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "Хөрөнгийн утгууд"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "Хөрөнгө бүртгэгдсэн"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_move_line__asset_ids
msgid "Asset created from this Journal Item"
msgstr "Хөрөнгө нь энэхүү журналын бичилтээс үүссэн"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset paused"
msgstr "Хөрөнгө царцсан"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""
"Хөрөнгө зарагдсан эсвэл актлагдсан. Санхүүгийн бичилт нь батлахыг хүлээж буй"
" төлөвт байна."

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Asset unpaused"
msgstr "Хөрөнгийн царцалтыг арилгасан"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Asset(s)"
msgstr "Хөрөнгүүд"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Хөрөнгө/Урьдчилсан орлого"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_purchase_tree
#, python-format
msgid "Assets"
msgstr "Хөрөнгө"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Хөрөнгө ба Урьдчилсан орлого"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Хаагдсан төлөвтэй хөрөнгүүд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Ноорог болон нээлттэй төлөвтэй хөрөнгүүд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Хавсралтын тоо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "Хөрөнгө үүсгэх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Expense"
msgstr "Урьдчилсан зардал үүсгэх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Deferred Revenue"
msgstr "Урьдчилсан орлого үүсгэх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "Автоматжуулалт"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
#, python-format
msgid "Book Value"
msgstr "Үлдэгдэл өртөг"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "Хөрөнгө үүсгэж болох эсэх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Cancel"
msgstr "Цуцлах"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Category of asset"
msgstr "Хөрөнгийн ангилал"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Characteristics"
msgstr "Үзүүлэлтүүд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "Дэд"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"Элэгдлийн мөрийг тооцоолох аргачлалыг дараахаас сонгоно.\n"
"  * Шулуун шугам: Тэнцүү дүнгээр элэгдэнэ. Элэгдэх дүн: Анхны өртөг / Элэгдэх тоо\n"
"  * Бууруулах: Элэгдэх дүн: Үлдэгдэл өртөг * Элэгдэх коэффицент\n"
"  * Шулуун шугмаар бууруулах: Бууруулах аргатай төстэй боловч хамгийн бага элэгдэх дүн нь шулуун шугмын элэгдэх дүнтэй тэнцүү байна."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Хаагдсан"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__company_id
msgid "Company"
msgstr "Компани"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "Элэгдэл тооцох"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Compute Expense"
msgstr "Зардал тооцоолох"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Compute Revenue"
msgstr "Орлого тооцоолох"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "Батлах"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree_grouped_inherit
msgid "Create Asset"
msgstr "Хөрөнгө үүсгэх"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "Үүсгээд батлах"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "Нооргоор үүсгэх"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "Шинэ хөрөнгө үүсгэх"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "Шинэ хөрөнгийн загвар үүсгэх"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_form
msgid "Create new deferred expense"
msgstr "Шинэ урьдчилж тооцсон зардал бүртгэх"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_expense_model_form
msgid "Create new deferred expense model"
msgstr "Шинэ урьдчилж тооцсон зардлын загвар бүртгэх"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_form
msgid "Create new deferred revenue"
msgstr "Шинэ урьдчилж тооцсон орлого бүртгэх"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_revenue_model_form
msgid "Create new deferred revenue model"
msgstr "Шинэ урьдчилж тооцсон орлогын загвар бүртгэх"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Хуримтлагдсан элэгдэл"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Cumulative Expense"
msgstr "Хуримтлагдсан элэгдлийн зардал"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Cumulative Revenue"
msgstr "Хуримтлагдсан орлого"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "Валют"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Одоогийн"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "Одоогийн өртөг"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_id
msgid "Customer Invoice"
msgstr "Захиалагчийн нэхэмжлэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Огноо"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Dec. then Straight"
msgstr "Шулуунаар бууруулах"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
#, python-format
msgid "Declining"
msgstr "Бууруулах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "Бууруулах коэффицент"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "Шулуун шугмаар бууруулах"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__expense
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__expense
#, python-format
msgid "Deferred Expense"
msgstr "Хойшлуулсан зардал"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Deferred Expense Account"
msgstr "Хойшлуулсан зардлын данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Amount"
msgstr "Хойшлуулсан зардлын данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Model"
msgstr "Хойшлуулсан зардлын загвар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense Model name"
msgstr "Хойшлуулсан зардлын загварын нэр"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_expense_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_model_recognition
msgid "Deferred Expense Models"
msgstr "Хойшлуулсан зардлын загвар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Expense Options"
msgstr "Урьдчилж тооцсон зардлын сонголтууд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Deferred Expense name"
msgstr "Хойшлуулсан зардлын нэр"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Expense(s)"
msgstr "Хойшлуулсан зардал(ууд)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_expense_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_expense_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Deferred Expenses"
msgstr "Хойшлуулсан зардал"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Deferred Expenses Models"
msgstr "Хойшлуулсан зардлын загвар"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__asset_type__sale
#, python-format
msgid "Deferred Revenue"
msgstr "Хойшлуулсан орлого"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Deferred Revenue Account"
msgstr "Хойшлуулсан орлогын данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Amount"
msgstr "Хойшлуулсан орлогын дүн"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Model"
msgstr "Хойшлуулсан орлогын загвар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue Model name"
msgstr "Хойшлуулсан орлогын загварын нэр"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_model_recognition
msgid "Deferred Revenue Models"
msgstr "Хойшлуулсан орлогын загвар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Deferred Revenue Options"
msgstr "Урьдчилж тооцсон орлогын сонголтууд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Deferred Revenue name"
msgstr "Хойшлуулсан орлогын нэр"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred Revenue(s)"
msgstr "Хойшлуулсан орлого(ууд)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_revenue_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_revenue_recognition
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
#, python-format
msgid "Deferred Revenues"
msgstr "Хойшлуулсан орлогууд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Deferred Revenues Models"
msgstr "Хойшлуулсан орлогын загвар"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred expense"
msgstr "Хойшлуулсан зардал"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred expense created"
msgstr "Хойшлуулсан зардал үүслээ"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Deferred revenue"
msgstr "Хойшлуулсан орлого"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Deferred revenue created"
msgstr "Хойшлуулсан орлого үүслээ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "Элэгдвэл зохих дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_payment__asset_remaining_value
msgid "Depreciable Value"
msgstr "Элэгдэх өртөг"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "Элэгдсэн эхний үлдэгдэл"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Depreciation"
msgstr "Элэгдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "Элэгдлийн данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "Элэгдлийн самбар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "Элэгдлийн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "Элэгдлийн мөр"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "Элэгдэх арга"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_number_import
msgid "Depreciation Number Import"
msgstr "Өмнөх системд элэгдсэн тоо"

#. module: account_asset
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "Элэгдлийн төлөвлөгөө"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Depreciation Table Report"
msgstr "Элэгдлийн хүснэгтийн тайлан"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "Элэгдэлийн самбар өөрчлөгдсөн"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s posted (%s)"
msgstr "Элэгдлийн гүйлгээ %s батлагдсан (%s)"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Depreciation entry %s reversed (%s)"
msgstr "Элэгдлийн гүйлгээ %s буцаагдсан (%s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "Хөрөнгийн дансыг харуулах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_model_choice
msgid "Display Model Choice"
msgstr "Загварын сонголтыг харуулах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal"
msgstr "Устгал"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "Устгалын огноо"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "Устгалын гүйлгээ"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "Устгалын гүйлгээ"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__dispose
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Dispose"
msgstr "Актлах"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "Ноорог"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__draft_asset_ids
msgid "Draft Asset"
msgstr "Ноорог хөрөнгө"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "Үргэлжлэх хугацаа"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Duration / Rate"
msgstr "Хугацаа / Харьцаа"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciation Schedule"
msgstr "Эхний үлдэгдлийн элэгдсэн тоо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Existing Depreciations"
msgstr "Эхний үлдэгдийн элэгдсэн тоо"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_tree
#, python-format
msgid "Expense"
msgstr "Зардал"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
msgid "Expense Account"
msgstr "Зардлын данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Board"
msgstr "Зардлын самбар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Date"
msgstr "Зардлын огноо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Lines"
msgstr "Зардлын мөрүүд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
msgid "Expense Name"
msgstr "Зардлын нэр"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Expense Recognition"
msgstr "Зардал хүлээн зөвшөөрөх"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "First Depreciation"
msgstr "Анхны элэгдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "First Depreciation Date"
msgstr "Анхны элэгдэх огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__first_depreciation_date_import
msgid "First Depreciation Date Import"
msgstr "Элэгдлийн эхний үлдэгдэлийн анх элэгдсэн огноо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "First Recognition Date"
msgstr "Анхны элэгдлийн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "Хөрөнгийн данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "Дагагчид"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Дагагчид (Харилцагчид)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon ж.ш. fa-tasks"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "Форм харагдац"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "Ирээдүйн үйл ажиллагаанууд"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__gain
msgid "Gain"
msgstr "Олз"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "Олзын данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__gain_or_loss
msgid "Gain Or Loss"
msgstr "Олз ба гарз"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "Олзын утга"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#, python-format
msgid "Gross Increase"
msgstr "Өртөг нэмэгдүүлэлт"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "Өртөг нэмэгдүүлэлтийн дүн"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Group By..."
msgstr "Бүлэглэх..."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "Мессежтэй"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Дүрс"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ажилбар дээр сануулга гарсныг илэрхийлэх зураг."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr "Хэрэв сонгогдсон бол, шинэ зурвасууд таны анхаарлыг шаардана."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Үүнийг сонговол алдаа үүсэх үед зурвасууд ирнэ."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata
msgid ""
"If set, specifies the start date for the first period's computation. By "
"default, it is set to the day's date rather than the Start Date of the "
"fiscal year."
msgstr ""
"Хэрэв тохируулсан бол энэ нь мөчлөгийн эхлэх огноог тодорхойлно. Анхны утга "
"нь санхүүгийн жилийн эхлэх огноо биш харин тухайн өдрийн огноогоор "
"тохируулагдсан байдаг."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model
msgid ""
"If this is selected, an expense/revenue will be created automatically when "
"Journal Items on this account are posted."
msgstr ""
"Үүнийг сонговол, энэхүү дансанд бичигдсэн ажил гүйлгээнээс автоматаар "
"урьдчилж тооцсон зардал/орлогын бүртгэл үүснэ.."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date_import
msgid ""
"In case of an import from another software, provide the first depreciation "
"date in it."
msgstr ""
"Өөр системээс хөрөнгийн үлдэгдлээ импорт хийж оруулж буй тохиолдолд өмнөх "
"систем дээр эхний удаа элэгдэл бүртгэсэн огноог энэ талбарт оруулна."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__depreciation_number_import
msgid ""
"In case of an import from another software, provide the number of "
"depreciations already done before starting with Odoo."
msgstr ""
"Өмнөх системийн хөрөнгийн үлдэгдлийг импорт хийх тохиолдолд, тухайн хөрөнгө "
"нь Odoo-д бүртгэгдэхээс өмнө нь хэдэн удаа элэгдсэн болохыг тодорхойлж өгнө."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"Өөр системээс үндсэн хөрөнгийн эхний үлдэгдэл зөөж оруулсан тохиолдолд, "
"өмнөх системд элэгдсэн дүнгээс үргэлжүүлэн шинэ систем дээр элэгдүүлэх "
"зорилгоор уг талбарт элэгдлийн эхний үлдэгдлийг оруулна"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid ""
"In percent.<br>For a linear method, the depreciation rate is computed per "
"year.<br>For a declining method, it is the declining factor"
msgstr ""
"Хувь.<br>Шулуун шугмын аргын үед элэгдлийн харьцаа нь жил бүр тусдаа "
"тооцогдоно.<br>Бууруулах аргын үед энэхүү утга нь элэгдлийн коэффицент болно"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Increase Accounts"
msgstr "Өртөг нэмэгдүүлэх данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__invoice_line_id
msgid "Invoice Line"
msgstr "Нэхэмжлэлийн мөр"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "Дагагч эсэх"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Энэ бол ашиглаж дуусах үед элэгдэхгүй үлдээхээр төлөвлөж буй дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_expense_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Journal"
msgstr "Журнал"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Journal Entries"
msgstr "Ажил гүйлгээ"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Журналын бичилт"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
#, python-format
msgid "Journal Items"
msgstr "Журналын бичилтүүд"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"Journal Items of {account} should have a label in order to generate an asset"
msgstr ""
"Журналын бичилтүүд {account} нь хөрөнгө бүртгэхийн тулд лавлах дугаартай "
"байх ёстой"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause____last_update
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell____last_update
#: model:ir.model.fields,field_description:account_asset.field_asset_modify____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "Хоцорсон ажилбар"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Linear"
msgstr "Шугаман"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__loss
msgid "Loss"
msgstr "Алдагдал"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "Алдагдлын данс"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr "Үндсэн хавсралт"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "Ширхэгээр удирдах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Алдаа үүссэн талаарх зурвас"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "Зурвасууд"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
#, python-format
msgid "Method"
msgstr "Арга"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "Модел"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification"
msgstr "Өөрчлөлт"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modification reason"
msgstr "Өөрчлөлтийн шалтгаан"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Өөрчлөх"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
#, python-format
msgid "Modify Asset"
msgstr "Хөрөнгө засварлах"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "Элэгдэл өөрчлөх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Modify Expense"
msgstr "Зардал өөрчлөх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Modify Revenue"
msgstr "Орлого өөрчлөх"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "Сар"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "Нэхэмжлэлээс мөрөөс олон хөрөнгө авах"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"Худалдан авалтын нэхэмжлэлийн мөр дээр бичигдсэн тоо хэмжээнд үндэслэн "
"ширхэг тус бүрээр нь үндсэн хөрөнгө болгож бүртгэж авна. Хэрэв энэ тохиргоог"
" хийгээгүй тохиолдолд нэхэмжлэлийн мөрөөс багц дүнгээр 1 ширхэг хөрөнгө "
"бүртгэгдэнэ."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Миний ажилбарын эцсийн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__need_date
msgid "Need Date"
msgstr "Хэрэгцээт огноо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "New Values"
msgstr "Шинэ утга"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "Хөрөнгийн шинэ үлдэгдэл өртөг"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "Хөрөнгийн шинэ элэгдэхгүй үлдэх дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календар дээрх дараагийн Үйл ажиллагаа"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дараагийн ажилбарын эцсийн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Дараагийн ажилбарын гарчиг"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Дараагийн ажилбарын төрөл"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Next Period Expense"
msgstr "Дараагийн мөчлөгийн зардал"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Next Period Revenue"
msgstr "Дараагийн мөчлөгийн орлого"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__gain_or_loss__no
msgid "No"
msgstr "Үгүй"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "No asset account"
msgstr "Хөрөнгийн данс алга"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "Элэгдэхгүй үлдэх дүн"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "Үлдэх өртөг"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__first_depreciation_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"Эхний элэгдлийг худалдан авсан огноогоос эхлэх тохиргоотой үед энэхүү огноо "
"нь эхний элэгдлийн гүйлгээнд нөлөөлөхгүй болохыг анхаарна уу. Энэ зөвхөн "
"санхүүгийн огноо өөрчилнө"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__number_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_payment__number_asset_ids
msgid "Number Asset"
msgstr "Хөрөнгийн тоо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Үйлдлийн тоо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
msgid "Number of Depreciations"
msgstr "Элэгдэх тоо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "Мөчлөг тутмын сарын тоо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Number of Recognitions"
msgstr "Хэрэгжилтийн тоо"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "Хөрөнгийн өртөгийг нэмэгдүүлэхээр бүртгэгдсэн дэд хөрөнгийн тоо"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "Элэгдлийн гүйлгээний тоо (батлагдсан болон батлагдаагүй)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Алдааны тоо"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Үйлдэл шаардсан зурвасын тоо"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Алдааны мэдэгдэл бүхий зурвасын тоо"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_unread_counter
msgid "Number of unread messages"
msgstr "Уншаагүй зурвасын тоо"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "Царцаасан"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.line_caret_options
msgid "Open Asset"
msgstr "Хөрөнгийг нээх"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "Анхны өртөг"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "Эцэг"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
msgid "Pause"
msgstr "Түр зогсоох"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_pause
#: model_terms:ir.ui.view,arch_db:account_asset.asset_pause_form
#, python-format
msgid "Pause Asset"
msgstr "Хөрөнгө царцаах"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Pause Depreciation"
msgstr "Элэгдэл царцаах"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_pause__date
msgid "Pause date"
msgstr "Царцаах огноо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_expense_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_purchase_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
msgid "Period length"
msgstr "Мөчлөгийн урт"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "Батлагдсан гүйлгээ"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "Авсан өдрийн огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata
msgid "Prorata Temporis"
msgstr "Авсан өдрөөс элэгдэх"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__purchase
msgid "Purchase: Asset"
msgstr "Худалдан авалт: Хөрөнгө"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Reason"
msgstr "Шалтгаан"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "Related Expenses"
msgstr "Холбогдох зардлууд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Related Purchase"
msgstr "Холбогдох худалдан авалт"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Related Sales"
msgstr "Холбогдох борлуулалт"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Report of reversal for {name}"
msgstr "Буцаалтын тайлан {name}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Reset to running"
msgstr "Элэгдүүлж эхлэх"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Residual Amount to Recognize"
msgstr "Хэрэгжүүлэх дүнгийн үлдэгдэл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Эд хариуцагч"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "Элэгдлүүлж эхлэх"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#, python-format
msgid "Revenue"
msgstr "Орлого"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_revenue_model_search
msgid "Revenue Account"
msgstr "Орлогын данс"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Board"
msgstr "Орлогын самбар"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Date"
msgstr "Орлогын огноо"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Lines"
msgstr "Орлогын мөрүүд"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_sale_tree
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_sale_tree
msgid "Revenue Name"
msgstr "Орлогын нэр"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "Revenue Recognition"
msgstr "Орлого хүлээн зөвшөөрөх"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__reversal_move_id
#: model:ir.model.fields,field_description:account_asset.field_account_payment__reversal_move_id
msgid "Reversal Move"
msgstr "Эсрэг гүйлгээ"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"Элэгдсэн дүнг өөрчлөхийн тулд ирээдүйн огноогоор элэгдлийн буцаалт бүхий "
"гүйлгээ бүртгэж батална"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "Ажиллаж байгаа"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS илгээлтийн алдаа"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Sale"
msgstr "Борлуулалт"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__asset_type__sale
msgid "Sale: Revenue Recognition"
msgstr "Борлуулалт: Хүлээн зөвшөөрөгдсөн"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save Model"
msgstr "Загвар болгох"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Save model"
msgstr "Загвар болгох"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_sell__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "Нэхэмжлэлийн мөр сонгох"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset_sell__action__sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
msgid "Sell"
msgstr "Зарах"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_account_asset_sell
#: model_terms:ir.ui.view,arch_db:account_asset.asset_sell_form
#, python-format
msgid "Sell Asset"
msgstr "Хөрөнгө зарах"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Sell or Dispose"
msgstr "Зарах эсвэл актлах"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set manually the original values or"
msgstr "Анхны өртөгийг гараар тохируулах эсвэл"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "Ноорог болгох"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "Ашиглагдаж буй болгох"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Өнөөдрийг хүртэлх хугацаанд дараагийн ажилбарын огноо нь тохируулагдсан бүх "
"тэмдэглэлүүд"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Some fields are missing {}"
msgstr "Зарим талбаруудын утга алга {}"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Some required values are missing"
msgstr "Зарим шаардлагатай утгууд алга"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Start Depreciating"
msgstr "Элэгдэж эхлэх огноо"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "Төлөв"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Ажилбаруудын төлөв байдал\n"
"Хоцорсон: Гүйцэтгэх огноо нь аль хэдий нь өнгөрсөн\n"
"Өнөөдөр: Өнөөдөр гүйцэтгэх ёстой\n"
"Төлөвлөгдсөн: Ирээдүйд гүйцэтгэх ажилбарууд"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "Шулуун шугам"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"Элэгдэх ёстой дүн, нэмэгдүүлсэн өртөг, элэгдэхгүй үлдэх өртөгийн нийлбэр"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__can_create_asset
msgid ""
"Technical field specifying if the account can generate asset depending on "
"it's type. It is used in the account form view."
msgstr ""
"Уг данс нь төрлөөсөө хамааран хөрөнгө бүртгэх боломжтой эсэхийг илэрхийлэх "
"техникийн талбар. Дансны форм дэлгэц дээр ашиглагдана."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_value
msgid ""
"Technical field to know if we should display the fields for the creation of "
"gross increase asset"
msgstr ""
"Өртөг нэмэгдүүлэх хөрөнгө бүртгэх талбаруудыг дэлгэцэнд харуулах эсэхийг "
"тодорхойлон техникийн талбар"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__gain_or_loss
msgid ""
"Technical field to know is there was a gain or a loss in the selling of the "
"asset"
msgstr ""
"Хөрөнгийг борлуулахад олз эсвэл гарз үүссэн эсэхийг тодорхойлох техникийн "
"талбар"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "Хоёр элэгдэл хоорондын хугацаа"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "Хөрөнгө энэ визардаар засварлагдах болно"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "Энэ хөрөнгийн өртөгийг нэмэгдүүлж буй дэд хөрөнгүүд"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_id
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr ""
"Устгал хийх үед хаалтын журналын бичилт үүсгэхийн тулд нэхэмжлэл бүртгэх "
"шаардлагатай."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr ""
"Хөрөнгийг ашиглах хугацаанд нийт хэдэн удаа элэгдүүлэх шаардлагатайг "
"илэрхийлнэ"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "The remaining value on the last depreciation line must be 0"
msgstr "Элэгдлийн сүүлийн мөр дээрх үлдэгдэл өртөг 0 байх ёстой"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset_sell__invoice_line_id
msgid "There are multiple lines that could be the related to this asset"
msgstr "Энэ хөрөнгөнд холбоотой хэд хэдэн мөрүүд байна"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_move__asset_value_change
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_value_change
msgid ""
"This is a technical field set to true when this move is the result of the "
"changing of value of an asset"
msgstr ""
"Энэхүү ажил гүйлгээ нь хөрөнгийн өртөгийн өөрчлөлтөөс улбаатайг илэрхийлэх "
"техник талбар"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_bank_statement_line__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_move__asset_manually_modified
#: model:ir.model.fields,help:account_asset.field_account_payment__asset_manually_modified
msgid ""
"This is a technical field stating that a depreciation line has been manually"
" modified. It is used to recompute the depreciation table of an "
"asset/deferred revenue."
msgstr ""
"Элэгдлийн мөрийг гараар засварласан эсэхийг илэрхийлэх техникийн талбар. Энэ"
" нь хөрөнгө болон хойшлуулсан орлогын элэгдлийн сарбарыг дахин тооцоолоход "
"ашиглагдана."

#. module: account_asset
#. openerp-web
#: code:addons/account_asset/static/src/js/account_asset_reversed_widget.js:0
#, python-format
msgid "This move has been reversed"
msgstr "Энэ гүйлгээ нь буцаагдсан байна"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "Өнөөдрийн ажилбар"

#. module: account_asset
#: code:addons/account_asset/report/account_assets_report.py:0
#, python-format
msgid "Total"
msgstr "Нийт дүн"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "Trying to pause an asset without any future depreciation line"
msgstr "Хөрөнгийг цаашид элэгдэхгүйгээр царцаах гэж байна"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred expense"
msgstr "Хойшлуулсан зардал болгох"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as a deferred revenue"
msgstr "Хойшлуулсан орлого болгох"

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid "Turn as an asset"
msgstr "Хөрөнгө болгох"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__user_type_id
msgid "Type of the account"
msgstr "Дансны төрөл"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Бичлэг дээрх асуудал бүхий ажилбарын төрөл"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread
msgid "Unread Messages"
msgstr "Уншаагүй зурвас"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Уншаагүй зурвасын тоолуур"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value decrease for: %(asset)s"
msgstr "Өртөг хорогдуулалт: %(asset)s"

#. module: account_asset
#: code:addons/account_asset/wizard/asset_modify.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Value increase for: %(asset)s"
msgstr "Өртөг нэмэгдүүлэлт: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Вебсайтын зурвас"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Вебсайтын харилцааны түүх"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Хөрөнгө бүртгэгдэхдээ 'Ноорог' төлөвтэй байна.\n"
"Хөрөнгийг батлавал төлөв нь 'Явагдаж буй' болнох бөгөөд элэгдлийн мөрүүд батлагдах санхүүгийн тайланд тусах боломжтой болно.\n"
"Хэрэв та хөрөнгийн элэгдлийг түр хугацаанд зогсоохыг хүсвэл хөрөнгийг 'Царцаасан' төлөвт гараар шилжүүлэх боломжтой\n"
"Элэгдэж дууссан хөрөнгийг гараар хаана. Хэрэв сүүлийн элэгдлийн мөр батлагдвал хөрөнгө автоматаар Хаагдсан төлөвт шилжинэ."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "Жил"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot archive a record that is not closed"
msgstr "Та хаагдаагүй хөрөнгийг архивлах боломжгүй"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_sell.py:0
#, python-format
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"Өртөг нэмэгдүүлсэн хөрөнгө дээр автомат гүйлгээ болгон тохируулах боломжгүй."
" Та эхлээд нэмэгдүүлсэн өртөг бүхий хөрөнгө дээр 'Хөрөнгө хаах' үйлдэл хийнэ"
" үү."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"Та кредит болон дебет аль аль нь утгатай эсвэл ямар ч дүнгүй журналын "
"бичилтээс хөрөнгө үүсгэх боломжгүй"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"Та ямар нэг батлагдсан хөрөнгөтэй холбогдсон ажил гүйлгээг устгах боломжгүй."
" Үүний оронд хөрөнгийг нь зарах эсвэл актлах үйлдэл хийх эсвэл холбогдсон "
"ажил гүйлгээг цуцлах боломжтой."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr "Та %s төлөвтэй баримтыг устгах боломжгүй."

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid ""
"You cannot pause an asset with posted depreciation lines in the "
"future.without reverting them."
msgstr ""
"Та ирээдүйн элэгдлийг нь үүсгэсэн хөрөнгийг цуцлахгүйгээр түр зогсоох "
"боломжгүй."

#. module: account_asset
#: code:addons/account_asset/models/account_move.py:0
#, python-format
msgid ""
"You cannot reset to draft an entry having a posted deferred revenue/expense"
msgstr ""
"Та хойшлогдсон орлого/зарлагаас үүссэн бичилтийг ноорог болгох боломжгүй."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_revenue_form
msgid "e.g. Annual Subscription"
msgstr "ж.ш. Жилээр нь төлсөн үйлчилгээ"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_expense_form
msgid "e.g. Annually Paid Insurance"
msgstr "ж.ш. Жилээр нь төлсөн даатгал"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "ж. Laptop iBook"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "select the related purchases"
msgstr "холбоотой худалдан авалтыг сонгох"

#. module: account_asset
#: code:addons/account_asset/models/account_asset.py:0
#, python-format
msgid "this move"
msgstr "энэ гүйлгээ"
