<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.ui.view" id="asset_sell_form">
        <field name="name">wizard.asset.sell.form</field>
        <field name="model">account.asset.sell</field>
        <field name="arch" type="xml">
            <form string="Sell Asset">
                <field name="select_invoice_line_id" invisible="1"/>
                <field name="gain_or_loss" invisible="1"/>
                <field name="company_id" invisible="1"/>
                <group>
                    <group>
                        <field name="action"/>
                        <field name="invoice_id" options="{'no_create': True}" attrs="{'invisible': [('action', '!=', 'sell')], 'required': [('action', '=', 'sell')]}"/>
                        <field name="invoice_line_id" options="{'no_create': True}" attrs="{'invisible': [('select_invoice_line_id', '=', False)], 'required': [('select_invoice_line_id', '=', True)]}"/>
                    </group>
                    <group>
                        <field name="gain_account_id" attrs="{'invisible': [('gain_or_loss', '!=', 'gain')], 'required': [('gain_or_loss', '=', 'gain')]}"/>
                        <field name="loss_account_id" attrs="{'invisible': [('gain_or_loss', '!=', 'loss')], 'required': [('gain_or_loss', '=', 'loss')]}"/>
                    </group>
                </group>
                <footer>
                    <button name="do_action" string="Sell" type="object" class="btn-primary" attrs="{'invisible': [('action', '!=', 'sell')]}" data-hotkey="q"/>
                    <button name="do_action" string="Dispose" type="object" class="btn-primary" attrs="{'invisible': [('action', '!=', 'dispose')]}" data-hotkey="w"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
