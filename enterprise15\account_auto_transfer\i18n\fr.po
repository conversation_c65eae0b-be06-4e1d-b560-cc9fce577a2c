# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_auto_transfer
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "<span> to </span>"
msgstr "<span> à </span>"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model
msgid "Account Transfer Model"
msgstr "Modèle de transfert de compte"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model_line
msgid "Account Transfer Model Line"
msgstr "Ligne de modèle de transfert de compte"

#. module: account_auto_transfer
#: model:ir.actions.server,name:account_auto_transfer.ir_cron_auto_transfer_ir_actions_server
#: model:ir.cron,cron_name:account_auto_transfer.ir_cron_auto_transfer
#: model:ir.cron,name:account_auto_transfer.ir_cron_auto_transfer
msgid "Account automatic transfers : Perform transfers"
msgstr "Transferts automatiques de compte : Effectuer des transferts"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Activate"
msgstr "Activer"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these analytic accounts to the destination account"
msgstr ""
"Ajoute une condition pour transférer uniquement la somme des lignes des "
"comptes d'origine qui correspondent à ces comptes analytiques vers le compte"
" de destination"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these partners to the destination account"
msgstr ""
"Ajoute une condition pour transférer uniquement la somme des lignes des "
"comptes d'origine qui correspondent à ces partenaires vers le compte de "
"destination."

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Analytic Filter"
msgstr "Filtre analytique"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Automated Transfer"
msgstr "Transfert automatisé"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (%s%% from account %s)"
msgstr "Transfert automatique (%s%%du compte %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (-%s%%)"
msgstr "Transfert automatique (-%s%%)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"Automatic Transfer (entries with analytic account(s): %s and partner(s): %s)"
msgstr ""
"Transfert Automatique (pièces avec compte(s) analytique(s) : %s et "
"partenaire(s): %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (entries with analytic account(s): %s)"
msgstr "Transfert automatique (entrées avec compte(s) analytique(s): %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (entries with partner(s): %s)"
msgstr "Transfert automatique (entrées avec partenaire(s) : %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"Automatic Transfer (from account %s with analytic account(s): %s and "
"partner(s): %s)"
msgstr ""
"Transfert Automatique (depuis le Compte %s avec compte(s) analytique(s): %s "
"et partenaire(s): %s) "

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (from account %s with analytic account(s): %s)"
msgstr ""
"Transfert automatique (depuis le compte %s avec un ou des comptes "
"analytiques: %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (from account %s with partner(s): %s)"
msgstr "Transfert Automatique (du Compte %s avec le(s) Partenaire(s): %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (to account %s)"
msgstr "Transfert automatique (vers le compte %s)"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.transfer_model_action
#: model:ir.ui.menu,name:account_auto_transfer.menu_auto_transfer
msgid "Automatic Transfers"
msgstr "Transferts automatiques"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company"
msgstr "Société"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company related to this journal"
msgstr "Société liée à ce journal"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Compute Transfer"
msgstr "Calculer le Transfert"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_date
msgid "Created on"
msgstr "Créé le"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Description"
msgstr "Description"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__account_id
msgid "Destination Account"
msgstr "Compte de destination"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__line_ids
msgid "Destination Accounts"
msgstr "Comptes de destination"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__journal_id
msgid "Destination Journal"
msgstr "Journal de Destination"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Disable"
msgstr "Désactiver"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__disabled
msgid "Disabled"
msgstr "Désactivé"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__display_name
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__frequency
msgid "Frequency"
msgstr "Fréquence"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.generated_transfers_action
msgid "Generated Entries"
msgstr "Entrées générées"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids
msgid "Generated Moves"
msgstr "Mouvements générés"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__id
msgid "ID"
msgstr "ID"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Journal"
msgstr "Journal"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model____last_update
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__month
msgid "Monthly"
msgstr "Mensuel"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids_count
msgid "Move Ids Count"
msgstr "Nombre d'identifiants de mouvement"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Move Model"
msgstr "Déplacer le Modèle"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_tree
msgid "Move Models"
msgstr "Déplacer les Modèles"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__name
msgid "Name"
msgstr "Nom"

#. module: account_auto_transfer
#: model:ir.model.constraint,message:account_auto_transfer.constraint_account_transfer_model_line_unique_account_by_transfer_model
msgid "Only one account occurrence by transfer model"
msgstr "Une seule occurrence de compte par modèle de transfert"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__account_ids
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Origin Accounts"
msgstr "Comptes d'Origine"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_bank_statement_line__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_move__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_payment__transfer_model_id
msgid "Originating Model"
msgstr "Modèle d'origine"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Partner Filter"
msgstr "Filtre partenaire"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percent"
msgstr "Pourcent"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Percent (%)"
msgstr "Pourcent (%)"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent_is_readonly
msgid "Percent Is Readonly"
msgstr "Le pourcentage est en lecture seule"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__percent
msgid ""
"Percentage of the sum of lines from the origin accounts will be transferred "
"to the destination account"
msgstr ""
"Pourcentage de la somme des lignes des comptes d'origine qui sera transféré "
"sur le compte de destination"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Period"
msgstr "Période"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__quarter
msgid "Quarterly"
msgstr "Trimestriel"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__in_progress
msgid "Running"
msgstr "En cours"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_start
msgid "Start Date"
msgstr "Date de début"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__state
msgid "State"
msgstr "État"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_stop
msgid "Stop Date"
msgstr "Date d'Arrêt"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "The analytic filter %s is duplicated"
msgstr "Le filtre analytique %s est dupliqué"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"The partner filter %s in combination with the analytic filter %s is "
"duplicated"
msgstr ""
"Le filtre partenaire %s en combinaison avec le filtre analytique %s est "
"dupliqué"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "The partner filter %s is duplicated"
msgstr "Le filtre partenaire %s est dupliqué"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "The total percentage (%s) should be less or equal to 100 !"
msgstr "Le pourcentage total (%s) doit être inférieur ou égal à 100 !"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__total_percent
msgid "Total Percent"
msgstr "Pourcentage total"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__transfer_model_id
msgid "Transfer Model"
msgstr "Transférer le Modèle"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Transfers"
msgstr "Transferts"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__year
msgid "Yearly"
msgstr "Annuel"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"You cannot delete an automatic transfer that has draft moves attached "
"('%s'); delete them before deleting this transfer."
msgstr ""
"Vous ne pouvez pas supprimer un transfert automatique auquel sont attachés "
"des mouvements brouillon ('%s') ; supprimez-les avant de supprimer ce "
"transfert."

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"You cannot delete an automatic transfer that has posted moves attached "
"('%s')."
msgstr ""
"Vous ne pouvez pas supprimer un transfert automatique auquel sont attachés "
"des mouvements comptabilisés ('%s')."

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "e.g. Monthly Expense Transfer"
msgstr "Par exemple, le transfert des dépenses mensuelles"
