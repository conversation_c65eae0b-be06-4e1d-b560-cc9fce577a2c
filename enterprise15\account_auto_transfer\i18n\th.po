# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_auto_transfer
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "<span> to </span>"
msgstr "<span> ถึง </span>"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model
msgid "Account Transfer Model"
msgstr "โมเดลการโอนบัญชี"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model_line
msgid "Account Transfer Model Line"
msgstr "บรรทัดโมเดลการโอนบัญชี"

#. module: account_auto_transfer
#: model:ir.actions.server,name:account_auto_transfer.ir_cron_auto_transfer_ir_actions_server
#: model:ir.cron,cron_name:account_auto_transfer.ir_cron_auto_transfer
#: model:ir.cron,name:account_auto_transfer.ir_cron_auto_transfer
msgid "Account automatic transfers : Perform transfers"
msgstr ""

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Activate"
msgstr "เปิดใช้งาน"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these analytic accounts to the destination account"
msgstr ""
"เพิ่มเงื่อนไขเพื่อการโอนเฉพาะผลรวมของรายการจากบัญชีต้นทางที่ตรงกับบัญชีวิเคราะห์เหล่านี้ไปยังบัญชีปลายทาง"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these partners to the destination account"
msgstr ""
"เพิ่มเงื่อนไขในการโอนเฉพาะผลรวมของบรรทัดจากบัญชีต้นทางที่ตรงกับพาร์ทเนอร์เหล่านี้ไปยังบัญชีปลายทาง"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Analytic Filter"
msgstr "ตัวกรองการวิเคราะห์"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Automated Transfer"
msgstr "การโอนอัตโนมัติ"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (%s%% from account %s)"
msgstr "โอนอัตโนมัติ (%s%% จากบัญชี %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (-%s%%)"
msgstr "โอนอัตโนมัติ (-%s%%)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"Automatic Transfer (entries with analytic account(s): %s and partner(s): %s)"
msgstr "การโอนอัตโนมัติ (รายการที่มีบัญชีวิเคราะห์: %s และพาร์ทเนอร์: %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (entries with analytic account(s): %s)"
msgstr "การโอนอัตโนมัติ (รายการที่มีบัญชีวิเคราะห์: %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (entries with partner(s): %s)"
msgstr "การโอนอัตโนมัติ (รายการร่วมกับพาร์ทเนอร์: %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"Automatic Transfer (from account %s with analytic account(s): %s and "
"partner(s): %s)"
msgstr ""
"การโอนอัตโนมัติ (จากบัญชี %s พร้อมบัญชีวิเคราะห์: %s และพาร์ทเนอร์: %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (from account %s with analytic account(s): %s)"
msgstr "โอนอัตโนมัติ (จากบัญชี %s พร้อมบัญชีวิเคราะห์: %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (from account %s with partner(s): %s)"
msgstr "โอนอัตโนมัติ (จากบัญชี %s กับพาร์ทเนอร์ : %s)"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "Automatic Transfer (to account %s)"
msgstr "การโอนอัตโนมัติ (เข้าบัญชี %s)"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.transfer_model_action
#: model:ir.ui.menu,name:account_auto_transfer.menu_auto_transfer
msgid "Automatic Transfers"
msgstr "การโอนอัตโนมัติ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company"
msgstr "บริษัท"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company related to this journal"
msgstr "บริษัทที่เกี่ยวข้องกับสมุดรายวันนี้"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Compute Transfer"
msgstr "การถ่ายโอนคอมพิวเตอร์"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Description"
msgstr "รายละเอียด"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__account_id
msgid "Destination Account"
msgstr "บัญชีปลายทาง"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__line_ids
msgid "Destination Accounts"
msgstr "บัญชีปลายทาง"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__journal_id
msgid "Destination Journal"
msgstr "สมุดรายวันปลายทาง"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Disable"
msgstr "ปิดการใช้งาน"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__disabled
msgid "Disabled"
msgstr "Disabled"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__display_name
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__frequency
msgid "Frequency"
msgstr "ความถี่"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.generated_transfers_action
msgid "Generated Entries"
msgstr "รายการที่สร้างขึ้น"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids
msgid "Generated Moves"
msgstr "การย้ายที่สร้างขึ้น"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__id
msgid "ID"
msgstr "รหัส"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Journal"
msgstr "สมุดรายวัน"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึกบัญชี"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model____last_update
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__month
msgid "Monthly"
msgstr "รายเดือน"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids_count
msgid "Move Ids Count"
msgstr "การย้ายจำนวนรหัส"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Move Model"
msgstr "การย้ายโมเดล"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_tree
msgid "Move Models"
msgstr "การย้ายโมเดล"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__name
msgid "Name"
msgstr "ชื่อ"

#. module: account_auto_transfer
#: model:ir.model.constraint,message:account_auto_transfer.constraint_account_transfer_model_line_unique_account_by_transfer_model
msgid "Only one account occurrence by transfer model"
msgstr "มีเพียงบัญชีเดียวที่เกิดขึ้นตามโมเดลการโอน"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__account_ids
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Origin Accounts"
msgstr "บัญชีต้นฉบับ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_bank_statement_line__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_move__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_payment__transfer_model_id
msgid "Originating Model"
msgstr "โมเดลต้นฉบับ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Partner Filter"
msgstr "ตัวกรองพาร์ทเนอร์"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percent"
msgstr "เปอร์เซ็นต์"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Percent (%)"
msgstr "เปอร์เซ็นต์ (%)"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent_is_readonly
msgid "Percent Is Readonly"
msgstr "เปอร์เซ็นต์เป็นแบบอ่านอย่างเดียว"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__percent
msgid ""
"Percentage of the sum of lines from the origin accounts will be transferred "
"to the destination account"
msgstr "เปอร์เซ็นต์ของผลรวมบรรทัดจากบัญชีต้นทางจะถูกโอนไปยังบัญชีปลายทาง"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Period"
msgstr "ช่วงเวลา"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__quarter
msgid "Quarterly"
msgstr "รายไตรมาส"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__in_progress
msgid "Running"
msgstr "กำลังทำงาน"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_start
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__state
msgid "State"
msgstr "สถานะ"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_stop
msgid "Stop Date"
msgstr "วันที่หยุด"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "The analytic filter %s is duplicated"
msgstr "ตัวกรองการวิเคราะห์ %s ซ้ำกัน"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"The partner filter %s in combination with the analytic filter %s is "
"duplicated"
msgstr "ตัวกรองพาร์ทเนอร์ %s ร่วมกับตัวกรองการวิเคราะห์ %s ซ้ำกัน"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "The partner filter %s is duplicated"
msgstr "ตัวกรองพาร์ทเนอร์ %s ซ้ำกัน"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid "The total percentage (%s) should be less or equal to 100 !"
msgstr ""

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__total_percent
msgid "Total Percent"
msgstr "เปอร์เซ็นต์รวม"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__transfer_model_id
msgid "Transfer Model"
msgstr "โมเดลการโอน"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Transfers"
msgstr "โอนย้าย"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__year
msgid "Yearly"
msgstr "รายปี"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"You cannot delete an automatic transfer that has draft moves attached "
"('%s'); delete them before deleting this transfer."
msgstr ""
"คุณไม่สามารถลบการโอนอัตโนมัติที่มีการแนบร่างการเคลื่อนไหว ('%s') ได้ "
"ลบออกก่อนที่จะลบการโอนนี้"

#. module: account_auto_transfer
#: code:addons/account_auto_transfer/models/transfer_model.py:0
#, python-format
msgid ""
"You cannot delete an automatic transfer that has posted moves attached "
"('%s')."
msgstr ""
"คุณไม่สามารถลบการโอนอัตโนมัติที่มีการโพสต์การเคลื่อนไหวที่แนบมาได้ ('%s')"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "e.g. Monthly Expense Transfer"
msgstr "เช่น การโอนค่าใช้จ่ายรายเดือน"
