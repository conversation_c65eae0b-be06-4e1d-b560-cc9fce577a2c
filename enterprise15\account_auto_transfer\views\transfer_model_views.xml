<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SEARCH VIEW -->
    <record model="ir.ui.view" id="view_generated_transfer_search">
        <field name="name">account.auto.transfer.search</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_move_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="transfer_model_id"/>
            </xpath>
        </field>
    </record>

    <!-- ACTIONS -->
    <!--    MOVE MODELS (FROM MENU) -->
    <record model="ir.actions.act_window" id="transfer_model_action">
        <field name="name">Automatic Transfers</field>
        <field name="res_model">account.transfer.model</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!--    GENERATED MOVES -->
    <record model="ir.actions.act_window" id="generated_transfers_action">
        <field name="name">Generated Entries</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.move</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="account.view_move_tree"/>
        <field name="search_view_id" ref="view_generated_transfer_search"/>
    </record>

    <!-- MENU -->
    <menuitem id="menu_auto_transfer"
              action="transfer_model_action"
              parent="account.menu_finance_entries_management" sequence="30"/>

    <!-- VIEWS -->
    <!--    TREE -->
    <record model="ir.ui.view" id="view_transfer_model_tree">
        <field name="name">account.auto.transfer.model.tree</field>
        <field name="model">account.transfer.model</field>
        <field name="arch" type="xml">
            <tree string="Move Models" sample="1">
                <field name="name"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="date_start"/>
                <field name="date_stop"/>
                <field name="frequency"/>
            </tree>
        </field>
    </record>
    <!--    FORM -->
    <record model="ir.ui.view" id="view_transfer_model_form">
        <field name="name">account.auto.transfer.model.form</field>
        <field name="model">account.transfer.model</field>
        <field name="arch" type="xml">
            <form string="Move Model" class="o_auto_transfer_form">
                <header>
                    <button name="action_activate" type="object"
                            string="Activate" class="oe_highlight"
                            attrs="{'invisible': ['|', ('state', '=', 'in_progress'), ('id', '=', False)]}"/>
                    <button name="action_perform_auto_transfer" type="object"
                            string="Compute Transfer" class="oe_highlight"
                            attrs="{'invisible': ['|', ('state', '!=', 'in_progress'), ('id', '=', False)]}"/>
                    <button name="action_disable" type="object"
                            string="Disable"
                            attrs="{'invisible': ['|', ('state', '!=', 'in_progress'), ('id', '=', False)]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div name="button_box" class="oe_button_box" attrs="{'invisible': [('id', '=', False)]}">
                        <button class="oe_stat_button" type="action"
                                name="%(generated_transfers_action)d" icon="fa-book"
                                context="{'search_default_transfer_model_id': active_id, 'default_transfer_model_id': active_id}">
                            <field name="move_ids_count" string="Transfers" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" string="Description"/>
                        <h1>
                            <field name="name" placeholder="e.g. Monthly Expense Transfer"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <label for="date_start" string="Period"/>
                            <div>
                                <field name="date_start" class="oe_inline"/>
                                <span> to </span>
                                <field name="date_stop" class="oe_inline"/>
                            </div>
                            <field name="frequency"/>
                        </group>
                        <group>
                            <field name="journal_id" string="Journal" domain="[('type', '=', 'general')]"/>
                        </group>
                    </group>
                    <group>
                        <group string="Origin Accounts">
                            <field name="account_ids" colspan="4" nolabel="1">
                                <tree>
                                    <field name="code"/>
                                    <field name="name"/>
                                    <field name="user_type_id"/>
                                </tree>
                            </field>
                        </group>
                        <group string="Automated Transfer">
                            <field name="line_ids" nolabel="1" colspan="4"
                                   context="{'default_transfer_model_id': active_id}">
                                <tree editable="bottom">
                                    <field name="percent_is_readonly" invisible="1"/>
                                    <field name="sequence" widget="handle"/>
                                    <field name="analytic_account_ids"
                                           widget="many2many_tags"/>
                                    <field name="partner_ids" widget="many2many_tags" domain="[('parent_id', '=', False)]"/>
                                    <field name="percent" string="Percent (%)" attrs="{'readonly': [('percent_is_readonly', '=', True)]}"/>
                                    <field name="account_id"/>
                                </tree>
                            </field>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
