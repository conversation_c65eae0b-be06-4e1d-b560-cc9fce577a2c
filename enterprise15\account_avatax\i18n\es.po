# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_avatax
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-17 20:02+0000\n"
"PO-Revision-Date: 2022-04-07 10:01+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "%(response)s"
msgstr "%(response)s"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "- %s (ID: %s) on %s"
msgstr "- %s (ID: %s) de %s"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-fw fa-arrow-right\"/>\n"
"                                    How to Get Credentials"
msgstr ""
"<i class=\"fa fa-fw fa-arrow-right\"/>\n"
"                                    Cómo obtener credenciales"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                    Avatax portal"
msgstr ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                    Portal Avatax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                    Show logs"
msgstr ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                    Mostrar registros"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                    Start logging for 30 minutes"
msgstr ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                     Iniciar inicio de sesión por 30 minutos"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                    Sync Parameters"
msgstr ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                    Sync Parameters"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                    Test connection"
msgstr ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                    Conexión de prueba"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "<span class=\"o_form_label\">AvaTax</span>"
msgstr "<span class=\"o_form_label\">AvaTax</span>"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API ID"
msgstr "Identificación de la API"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API KEY"
msgstr "Clave API"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Account that will be used by Avatax taxes for invoices."
msgstr "Cuenta que se usará para los impuestos en Avatax para las facturas."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Account that will be used by Avatax taxes for refunds."
msgstr "Cuenta que se usará para los impuestos en Avatax para las reembolsos."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Address Validation"
msgstr "Validación de direcciones"

#. module: account_avatax
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#, python-format
msgid ""
"Address validation is currently disabled, please enable it in the Accounting"
" settings."
msgstr ""
"La validación de la dirección está desactivada, actívela en los ajustes de "
"Contabilidad."

#. module: account_avatax
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#, python-format
msgid "Address validation is only supported for North American addresses."
msgstr ""
"La validación de direcciones solo funciona con direcciones de Norteamérica."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Automatically compute tax rates"
msgstr "Calcular tasas de impuestos automáticamente."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_id
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_id
msgid "Avalara API ID"
msgstr "ID de Avalara API"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_key
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_key
msgid "Avalara API KEY"
msgstr "Clave del ID de Avalara API"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_address_validation
msgid "Avalara Address Validation"
msgstr "Validación de la dirección Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_payment__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avatax_unique_code
msgid "Avalara Code"
msgstr "Código Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_partner_code
msgid "Avalara Company Code"
msgstr "Código Avalara de la empresa"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_environment
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_environment
msgid "Avalara Environment"
msgstr "Entorno Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_exemption_id
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_exemption_id
msgid "Avalara Exemption"
msgstr "Excepción Avalara"

#. module: account_avatax
#: model:ir.actions.act_window,name:account_avatax.ir_logging_avalara_action
msgid "Avalara Logging"
msgstr "Inicio de sesión Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_partner_code
msgid "Avalara Partner Code"
msgstr "Código de partner Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_show_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_show_address_validation
msgid "Avalara Show Address Validation"
msgstr "Mostrar la validación de la dirección Avalara"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.account_fiscal_position_form_inherit
msgid "Avatax"
msgstr "Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_template__avatax_category_id
msgid "Avatax Category"
msgstr "Categoría de Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_tax_date
#: model:ir.model.fields,field_description:account_avatax.field_account_payment__avatax_tax_date
msgid "Avatax Date"
msgstr "Fecha de Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Avatax Invoice Account"
msgstr "Cuenta de factura de Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_exemption
msgid "Avatax Partner Exemption Codes"
msgstr "Códigos de exención para socios de Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_avatax_category
msgid "Avatax Product Category"
msgstr "Categoría de producto de Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Avatax Refund Account"
msgstr "Cuenta de reembolso de Avatax"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_tax_date
#: model:ir.model.fields,help:account_avatax.field_account_payment__avatax_tax_date
msgid ""
"Avatax will use this date to calculate the tax on this invoice. If not "
"specified it will use the Invoice Date."
msgstr ""
"Avatax utilizará esta fecha para calcular el impuesto de esta factura. Si no"
" se especifica, se utilizará la fecha de la factura."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__city
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "City"
msgstr "Ciudad"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__code
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__code
msgid "Code"
msgstr "Código"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Commit Transactions"
msgstr "Comprometer transacciones"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_commit
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_commit
msgid "Commit in Avatax"
msgstr "Comprometerse en Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__company_id
msgid "Company"
msgstr "Compañía"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Company Code"
msgstr "Código de empresa"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.move_form_inherit
msgid "Compute Taxes using Avatax"
msgstr "Calcular los impuestos con Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__country_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Country"
msgstr "País"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avalara_partner_code
msgid "Customer Code set in Avalara for this partner."
msgstr "El código de cliente establecido en Avalara para este socio."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__description
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__description
msgid "Description"
msgstr "Descripción"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__display_name
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__display_name
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:account_avatax.field_account_move__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__display_name
#: model:ir.model.fields,field_description:account_avatax.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_category__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_product__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_template__display_name
#: model:ir.model.fields,field_description:account_avatax.field_res_company__display_name
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Environment"
msgstr "Entorno"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Generar enlace de pago de ventas"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Ir al panel de configuración"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__id
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__id
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:account_avatax.field_account_move__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__id
#: model:ir.model.fields,field_description:account_avatax.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__id
#: model:ir.model.fields,field_description:account_avatax.field_product_category__id
#: model:ir.model.fields,field_description:account_avatax.field_product_product__id
#: model:ir.model.fields,field_description:account_avatax.field_product_template__id
#: model:ir.model.fields,field_description:account_avatax.field_res_company__id
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__id
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__is_already_valid
msgid "Is Already Valid"
msgstr "Ya es válido"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_move__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_payment__is_avatax
msgid "Is Avatax"
msgstr "Es Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax____last_update
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code____last_update
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:account_avatax.field_account_move____last_update
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption____last_update
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address____last_update
#: model:ir.model.fields,field_description:account_avatax.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_category____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_product____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_template____last_update
#: model:ir.model.fields,field_description:account_avatax.field_res_company____last_update
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:account_avatax.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_avatax_unique_code
msgid "Mixin to generate unique ids for Avatax"
msgstr "Mixin para generar identificadores únicos para Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_avatax
msgid "Mixin to manage taxes with Avatax on various business documents"
msgstr "Mixin para manejar facturas con Avax en varios documentos comerciales"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__name
msgid "Name"
msgstr "Nombre"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Odoo could not change the state of the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo no pudo cambiar el estado de la transacción relacionada con %(document)s en AvaTax\n"
"Por favor comprovar el estado de `%(technical)s` en el portal de AvaTax"

#. module: account_avatax
#: code:addons/account_avatax/models/res_company.py:0
#: code:addons/account_avatax/models/res_company.py:0
#, python-format
msgid "Odoo could not fetch the exemption codes of %(company)s"
msgstr "Odoo no pudo obtener los códigos de exención de %(company)s"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Odoo could not fetch the taxes related to %(document)s.\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo no pudo obtener los impuestos relacionados con %(document)s.\n"
"Por favor, compruebe el estado de `%(technical)s` en el portal de AvaTax"

#. module: account_avatax
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#, python-format
msgid "Odoo could not validate the address of %(partner)s with Avalara."
msgstr "Odoo no pudo validar la dirección de %(partner)s con Avalara."

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Odoo could not void the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo no pudo anular la transacción relacionada con %(document)s en AvaTax\n"
"Por favor, compruebe el estado de `%(technical)s` en el portal de AvaTax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Original Address"
msgstr "Dirección original"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__partner_id
msgid "Partner"
msgstr "Partner"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "Please add your AvaTax credentials"
msgstr "Por favor, añade sus credenciales en AvaTax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_product
msgid "Product"
msgstr "Producto"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_category
msgid "Product Category"
msgstr "Categoría de producto"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__production
msgid "Production"
msgstr "Etapa de producción"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__sandbox
msgid "Sandbox"
msgstr "Modo aislado (sandbox)"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Save Validated"
msgstr "Guardar validado"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax_unique_code.py:0
#: code:addons/account_avatax/models/account_avatax_unique_code.py:0
#, python-format
msgid "Search operation not supported"
msgstr "No se admite la operación de búsqueda"

#. module: account_avatax
#: code:addons/account_avatax/models/res_company.py:0
#: code:addons/account_avatax/models/res_company.py:0
#, python-format
msgid ""
"Server Response:\n"
"%s"
msgstr ""
"Respuestas del servidor:\n"
"%s"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__state_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "State"
msgstr "Estado"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Street"
msgstr "Calle"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street2
msgid "Street2"
msgstr "Calle2"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_validate_address
msgid "Suggests validated addresses from Avatax"
msgstr "Sugiere direcciones validadas de Avatax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Synchronize the exemption codes from Avatax"
msgstr "Sincronice los códigos de exención de Avatax"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_avatax_validate_address__is_already_valid
msgid ""
"Technical field to determine whether to allow updating the address or not."
msgstr ""
"El campo técnico determina si se permite la actualización de la dirección o "
"no."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_avatax__is_avatax
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__is_avatax
#: model:ir.model.fields,help:account_avatax.field_account_move__is_avatax
#: model:ir.model.fields,help:account_avatax.field_account_payment__is_avatax
msgid "Technical field used for the visibility of fields and buttons."
msgstr "Campo técnico utilizado para la visibilidad de los campos y botones."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_partner__avalara_show_address_validation
#: model:ir.model.fields,help:account_avatax.field_res_users__avalara_show_address_validation
msgid ""
"Technical field used to hide the address validation button when the partner "
"is not in the US or Canada."
msgstr ""
"El campo técnico utilizado para ocultar el botón del interlocutor de "
"validación de la dirección, no se encuentra en Estados Unidos o en Canadá."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_partner_code
msgid ""
"The Avalara Company Code for this company. Avalara will interpret as DEFAULT"
" if it is not set."
msgstr ""
"La sociedad de Avalara para esta empresa. Avalara lo interpretará como "
"DEFAULT si no está configurado."

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"The Avalara Tax Code is required for %(name)s (#%(id)s)\n"
"See https://taxcode.avatax.avalara.com/"
msgstr ""
"Se necesita el código de impuesto de Avalara para %(name)s (#%(id)s)\n"
"Vaya a https://taxcode.avatax.avalara.com/"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"The following customer(s) need to have a zip, state and country when using "
"Avatax:"
msgstr ""
"Los siguientes clientes deben tener un código postal, una provincia y un "
"país cuando utilicen AvaTax:"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_commit
msgid "The transactions will be committed for reporting in Avatax."
msgstr "Las transacciones se reportarán en Avatax."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "This is already a valid address."
msgstr "Esta ya es una dirección válida."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__is_avatax
msgid "Use AvaTax API"
msgstr "Use el API de AvaTax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_use_upc
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_use_upc
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Use UPC"
msgstr "Use UPC"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_use_upc
msgid "Use Universal Product Code instead of custom defined codes in Avalara."
msgstr ""
"Use el código universal de producto en lugar de los códigos definidos en "
"Avalara."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_avatax__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_payment__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avatax_unique_code
msgid "Use this code to cross-reference in the Avalara portal."
msgstr ""
"Use este código para hacer una referencia cruzada en el portal Avalara."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__valid_country_ids
msgid "Valid Country"
msgstr "País válido"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_partner_form_inherit
msgid "Validate address"
msgstr "Validar dirección"

#. module: account_avatax
#: code:addons/account_avatax/models/res_partner.py:0
#: code:addons/account_avatax/models/res_partner.py:0
#, python-format
msgid "Validate address of %s"
msgstr "Validar dirección de %s"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_address_validation
msgid ""
"Validate and correct the addresses of partners in North America with "
"Avalara."
msgstr ""
"Validar y corregir las direcciones de partners en Norteamérica con Avalara."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Validated Address"
msgstr "Validar dirección"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_city
msgid "Validated City"
msgstr "Validar ciudad"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_country_id
msgid "Validated Country"
msgstr "Validar país"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_state_id
msgid "Validated State"
msgstr "Validar estado"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street
msgid "Validated Street"
msgstr "Validar calle"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street2
msgid "Validated Street2"
msgstr "Validar calle2"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_zip
msgid "Validated Zip Code"
msgstr "Validar código postal"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__zip
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Zip Code"
msgstr "Código postal"

#. module: account_avatax
#: code:addons/account_avatax/models/product.py:0
#: code:addons/account_avatax/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s] %s"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_template__avatax_category_id
msgid "https://taxcode.avatax.avalara.com/"
msgstr "https://taxcode.avatax.avalara.com/"
