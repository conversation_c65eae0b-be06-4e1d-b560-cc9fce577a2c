# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_avatax
# 
# Translators:
# hish, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-17 20:02+0000\n"
"PO-Revision-Date: 2022-04-07 10:01+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "%(response)s"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-fw fa-arrow-right\"/>\n"
"                                    How to Get Credentials"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                    Avatax portal"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                    Show logs"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                    Start logging for 30 minutes"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                    Sync Parameters"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                    Test connection"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "<span class=\"o_form_label\">AvaTax</span>"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API ID"
msgstr "API ID"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API KEY"
msgstr "API KEY"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Account that will be used by Avatax taxes for invoices."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Account that will be used by Avatax taxes for refunds."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Address Validation"
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#, python-format
msgid ""
"Address validation is currently disabled, please enable it in the Accounting"
" settings."
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#, python-format
msgid "Address validation is only supported for North American addresses."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Automatically compute tax rates"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_id
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_id
msgid "Avalara API ID"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_key
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_key
msgid "Avalara API KEY"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_address_validation
msgid "Avalara Address Validation"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_payment__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avatax_unique_code
msgid "Avalara Code"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_partner_code
msgid "Avalara Company Code"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_environment
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_environment
msgid "Avalara Environment"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_exemption_id
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_exemption_id
msgid "Avalara Exemption"
msgstr ""

#. module: account_avatax
#: model:ir.actions.act_window,name:account_avatax.ir_logging_avalara_action
msgid "Avalara Logging"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_partner_code
msgid "Avalara Partner Code"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_show_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_show_address_validation
msgid "Avalara Show Address Validation"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.account_fiscal_position_form_inherit
msgid "Avatax"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_template__avatax_category_id
msgid "Avatax Category"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_tax_date
#: model:ir.model.fields,field_description:account_avatax.field_account_payment__avatax_tax_date
msgid "Avatax Date"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Avatax Invoice Account"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_exemption
msgid "Avatax Partner Exemption Codes"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_avatax_category
msgid "Avatax Product Category"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Avatax Refund Account"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_tax_date
#: model:ir.model.fields,help:account_avatax.field_account_payment__avatax_tax_date
msgid ""
"Avatax will use this date to calculate the tax on this invoice. If not "
"specified it will use the Invoice Date."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Cancel"
msgstr "Цуцлах"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__city
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "City"
msgstr "Хот"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__code
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__code
msgid "Code"
msgstr "Дансны код"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Commit Transactions"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_commit
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_commit
msgid "Commit in Avatax"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__company_id
msgid "Company"
msgstr "Компани"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Company Code"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.move_form_inherit
msgid "Compute Taxes using Avatax"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_partner
msgid "Contact"
msgstr "Харилцах хаяг"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__country_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Country"
msgstr "Улс"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avalara_partner_code
msgid "Customer Code set in Avalara for this partner."
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Customers are required to have a zip, state and country when using Avatax."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__description
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__description
msgid "Description"
msgstr "Тайлбар"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__display_name
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__display_name
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__display_name
#: model:ir.model.fields,field_description:account_avatax.field_account_move__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__display_name
#: model:ir.model.fields,field_description:account_avatax.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_category__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_product__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_template__display_name
#: model:ir.model.fields,field_description:account_avatax.field_res_company__display_name
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Environment"
msgstr "Орчин"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Татварын тайлангийн тохируулга"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Борлуулалтын төлбөр төлөх холбоос үүсгэх"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Тохиргооны самбар руу очно уу"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__id
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__id
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__id
#: model:ir.model.fields,field_description:account_avatax.field_account_move__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__id
#: model:ir.model.fields,field_description:account_avatax.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__id
#: model:ir.model.fields,field_description:account_avatax.field_product_category__id
#: model:ir.model.fields,field_description:account_avatax.field_product_product__id
#: model:ir.model.fields,field_description:account_avatax.field_product_template__id
#: model:ir.model.fields,field_description:account_avatax.field_res_company__id
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__id
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__id
msgid "ID"
msgstr "ID"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__is_already_valid
msgid "Is Already Valid"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_move__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_payment__is_avatax
msgid "Is Avatax"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_move
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax____last_update
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code____last_update
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position____last_update
#: model:ir.model.fields,field_description:account_avatax.field_account_move____last_update
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption____last_update
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address____last_update
#: model:ir.model.fields,field_description:account_avatax.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_category____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_product____last_update
#: model:ir.model.fields,field_description:account_avatax.field_product_template____last_update
#: model:ir.model.fields,field_description:account_avatax.field_res_company____last_update
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:account_avatax.field_res_partner____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_avatax_unique_code
msgid "Mixin to generate unique ids for Avatax"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_avatax
msgid "Mixin to manage taxes with Avatax on various business documents"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__name
msgid "Name"
msgstr "Нэр"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Odoo could not change the state of the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/res_company.py:0
#: code:addons/account_avatax/models/res_company.py:0
#, python-format
msgid "Odoo could not fetch the exemption codes of %(company)s"
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Odoo could not fetch the taxes related to %(document)s.\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
#, python-format
msgid "Odoo could not validate the address of %(partner)s with Avalara."
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"Odoo could not void the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Original Address"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__partner_id
msgid "Partner"
msgstr "Харилцагч"

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid "Please add your AvaTax credentials"
msgstr ""

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_product
msgid "Product"
msgstr "Бараа"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_category
msgid "Product Category"
msgstr "Барааны ангилал"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_template
msgid "Product Template"
msgstr "Барааны загвар"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__production
msgid "Production"
msgstr "Үйлдвэрлэл"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__sandbox
msgid "Sandbox"
msgstr "Sandbox"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Save Validated"
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax_unique_code.py:0
#: code:addons/account_avatax/models/account_avatax_unique_code.py:0
#, python-format
msgid "Search operation not supported"
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/res_company.py:0
#: code:addons/account_avatax/models/res_company.py:0
#, python-format
msgid ""
"Server Response:\n"
"%s"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__state_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "State"
msgstr "Төлөв"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Street"
msgstr "Гудамж"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street2
msgid "Street2"
msgstr "Гудамж2"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_validate_address
msgid "Suggests validated addresses from Avatax"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Synchronize the exemption codes from Avatax"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_avatax_validate_address__is_already_valid
msgid ""
"Technical field to determine whether to allow updating the address or not."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_avatax__is_avatax
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__is_avatax
#: model:ir.model.fields,help:account_avatax.field_account_move__is_avatax
#: model:ir.model.fields,help:account_avatax.field_account_payment__is_avatax
msgid "Technical field used for the visibility of fields and buttons."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_partner__avalara_show_address_validation
#: model:ir.model.fields,help:account_avatax.field_res_users__avalara_show_address_validation
msgid ""
"Technical field used to hide the address validation button when the partner "
"is not in the US or Canada."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_partner_code
msgid ""
"The Avalara Company Code for this company. Avalara will interpret as DEFAULT"
" if it is not set."
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/account_avatax.py:0
#: code:addons/account_avatax/models/account_avatax.py:0
#, python-format
msgid ""
"The Avalara Tax Code is required for %(name)s (#%(id)s)\n"
"See https://taxcode.avatax.avalara.com/"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_commit
msgid "The transactions will be committed for reporting in Avatax."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "This is already a valid address."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__is_avatax
msgid "Use AvaTax API"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_use_upc
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_use_upc
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Use UPC"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_use_upc
msgid "Use Universal Product Code instead of custom defined codes in Avalara."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_avatax__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_payment__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avatax_unique_code
msgid "Use this code to cross-reference in the Avalara portal."
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__valid_country_ids
msgid "Valid Country"
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_partner_form_inherit
msgid "Validate address"
msgstr ""

#. module: account_avatax
#: code:addons/account_avatax/models/res_partner.py:0
#: code:addons/account_avatax/models/res_partner.py:0
#, python-format
msgid "Validate address of %s"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_address_validation
msgid ""
"Validate and correct the addresses of partners in North America with "
"Avalara."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Validated Address"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_city
msgid "Validated City"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_country_id
msgid "Validated Country"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_state_id
msgid "Validated State"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street
msgid "Validated Street"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street2
msgid "Validated Street2"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_zip
msgid "Validated Zip Code"
msgstr ""

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__zip
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Zip Code"
msgstr "Бүсийн Код"

#. module: account_avatax
#: code:addons/account_avatax/models/product.py:0
#: code:addons/account_avatax/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s] %s"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_template__avatax_category_id
msgid "https://taxcode.avatax.avalara.com/"
msgstr ""
