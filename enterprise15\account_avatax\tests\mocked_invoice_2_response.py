def generate_response(invoice_line_ids):
    assert len(invoice_line_ids) == 4, "the mocked response is for 4 lines"
    for i, line in enumerate(response['lines']):
        line['lineNumber'] = 'account.move.line,%s' % invoice_line_ids[i].id
    return response


response = {'addresses': [{'boundaryLevel': 'Address',
                'city': 'San Francisco',
                'country': 'US',
                'id': ***********,
                'latitude': '37.764754',
                'line1': '2280 Market St',
                'line2': '',
                'line3': '',
                'longitude': '-122.432634',
                'postalCode': '94114-1506',
                'region': 'CA',
                'taxRegionId': 4016940,
                'transactionId': **********},
               {'boundaryLevel': 'Address',
                'city': 'San Francisco',
                'country': 'US',
                'id': *************,
                'latitude': '37.71116',
                'line1': '250 Executive Park Blvd',
                'line2': '',
                'line3': '',
                'longitude': '-122.391717',
                'postalCode': '94134-3394',
                'region': 'CA',
                'taxRegionId': 4016940,
                'transactionId': **********}],
 'adjustmentDescription': '',
 'adjustmentReason': 'NotAdjusted',
 'batchCode': '',
 'businessIdentificationNo': '',
 'code': 'Journal Entry 164',
 'companyId': 281741,
 'country': 'US',
 'currencyCode': 'USD',
 'customerCode': 'CUST123456',
 'customerUsageType': '',
 'customerVendorCode': 'CUST123456',
 'date': '2021-01-01',
 'description': '',
 'destinationAddressId': ***********,
 'email': '',
 'entityUseCode': '',
 'exchangeRate': 1.0,
 'exchangeRateCurrencyCode': 'USD',
 'exchangeRateEffectiveDate': '2021-01-01',
 'exemptNo': '',
 'id': **********,
 'lines': [{'boundaryOverrideId': 0,
            'businessIdentificationNo': '',
            'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Odoo User',
            'destinationAddressId': ***********,
            'details': [{'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 11000404539513,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionId': 5000531,
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.06,
                         'rateRuleId': 1525706,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 1.8,
                         'reportingTaxCalculated': 1.8,
                         'reportingTaxableUnits': 30.0,
                         'serCode': '',
                         'signatureCode': 'AGAM',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 1.8,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 1.8,
                         'taxName': 'CA STATE TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 30.0,
                         'taxableUnits': 30.0,
                         'transactionId': **********,
                         'transactionLineId': ***********,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 12000404539516,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionId': 275,
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.0025,
                         'rateRuleId': 1525710,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.08,
                         'reportingTaxCalculated': 0.08,
                         'reportingTaxableUnits': 30.0,
                         'serCode': '',
                         'signatureCode': 'AIUQ',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 0.08,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.08,
                         'taxName': 'CA COUNTY TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 30.0,
                         'taxableUnits': 30.0,
                         'transactionId': **********,
                         'transactionLineId': ***********,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 1000517141040,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionId': 2001061792,
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.01,
                         'rateRuleId': 1525730,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.3,
                         'reportingTaxCalculated': 0.3,
                         'reportingTaxableUnits': 30.0,
                         'serCode': '',
                         'signatureCode': 'EMTV',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '38',
                         'stateFIPS': '',
                         'tax': 0.3,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.3,
                         'taxName': 'CA SPECIAL TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 30.0,
                         'taxableUnits': 30.0,
                         'transactionId': **********,
                         'transactionLineId': ***********,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'discountTypeId': 0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': ***********,
            'isItemTaxable': True,
            'isSSTP': False,
            'itemCode': 'false',
            'lineAmount': 30.0,
            'lineLocationTypes': [{'documentAddressId': *************,
                                   'documentLineId': ***********,
                                   'documentLineLocationTypeId': *************,
                                   'locationTypeCode': 'ShipFrom'},
                                  {'documentAddressId': ***********,
                                   'documentLineId': ***********,
                                   'documentLineLocationTypeId': **************,
                                   'locationTypeCode': 'ShipTo'}],
            'lineNumber': 'account.move.line,440',
            'nonPassthroughDetails': [],
            'originAddressId': *************,
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'revAccount': '',
            'sourcing': 'Origin',
            'tax': 2.18,
            'taxCalculated': 2.18,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxEngine': '',
            'taxIncluded': False,
            'taxOverrideAmount': 0.0,
            'taxOverrideReason': '',
            'taxOverrideType': 'None',
            'taxableAmount': 30.0,
            'transactionId': **********,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'boundaryOverrideId': 0,
            'businessIdentificationNo': '',
            'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Accounting',
            'destinationAddressId': ***********,
            'details': [{'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': *************,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionId': 5000531,
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.06,
                         'rateRuleId': 1525706,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 1.8,
                         'reportingTaxCalculated': 1.8,
                         'reportingTaxableUnits': 30.0,
                         'serCode': '',
                         'signatureCode': 'AGAM',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 1.8,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 1.8,
                         'taxName': 'CA STATE TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 30.0,
                         'taxableUnits': 30.0,
                         'transactionId': **********,
                         'transactionLineId': *************,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 3000517141037,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionId': 275,
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.0025,
                         'rateRuleId': 1525710,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.08,
                         'reportingTaxCalculated': 0.08,
                         'reportingTaxableUnits': 30.0,
                         'serCode': '',
                         'signatureCode': 'AIUQ',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 0.08,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.08,
                         'taxName': 'CA COUNTY TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 30.0,
                         'taxableUnits': 30.0,
                         'transactionId': **********,
                         'transactionLineId': *************,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 4000517141035,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionId': 2001061792,
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.01,
                         'rateRuleId': 1525730,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.3,
                         'reportingTaxCalculated': 0.3,
                         'reportingTaxableUnits': 30.0,
                         'serCode': '',
                         'signatureCode': 'EMTV',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '38',
                         'stateFIPS': '',
                         'tax': 0.3,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.3,
                         'taxName': 'CA SPECIAL TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 30.0,
                         'taxableUnits': 30.0,
                         'transactionId': **********,
                         'transactionLineId': *************,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'discountTypeId': 0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': *************,
            'isItemTaxable': True,
            'isSSTP': False,
            'itemCode': 'false',
            'lineAmount': 30.0,
            'lineLocationTypes': [{'documentAddressId': *************,
                                   'documentLineId': *************,
                                   'documentLineLocationTypeId': **************,
                                   'locationTypeCode': 'ShipFrom'},
                                  {'documentAddressId': ***********,
                                   'documentLineId': *************,
                                   'documentLineLocationTypeId': **************,
                                   'locationTypeCode': 'ShipTo'}],
            'lineNumber': 'account.move.line,441',
            'nonPassthroughDetails': [],
            'originAddressId': *************,
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'revAccount': '',
            'sourcing': 'Origin',
            'tax': 2.18,
            'taxCalculated': 2.18,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxEngine': '',
            'taxIncluded': False,
            'taxOverrideAmount': 0.0,
            'taxOverrideReason': '',
            'taxOverrideType': 'None',
            'taxableAmount': 30.0,
            'transactionId': **********,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'boundaryOverrideId': 0,
            'businessIdentificationNo': '',
            'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Expenses',
            'destinationAddressId': ***********,
            'details': [{'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 5000517141033,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionId': 5000531,
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.06,
                         'rateRuleId': 1525706,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.9,
                         'reportingTaxCalculated': 0.9,
                         'reportingTaxableUnits': 15.0,
                         'serCode': '',
                         'signatureCode': 'AGAM',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 0.9,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.9,
                         'taxName': 'CA STATE TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 15.0,
                         'taxableUnits': 15.0,
                         'transactionId': **********,
                         'transactionLineId': *************,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 6000517141032,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionId': 275,
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.0025,
                         'rateRuleId': 1525710,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.04,
                         'reportingTaxCalculated': 0.04,
                         'reportingTaxableUnits': 15.0,
                         'serCode': '',
                         'signatureCode': 'AIUQ',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 0.04,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.04,
                         'taxName': 'CA COUNTY TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 15.0,
                         'taxableUnits': 15.0,
                         'transactionId': **********,
                         'transactionLineId': *************,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 7000404539513,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionId': 2001061792,
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.01,
                         'rateRuleId': 1525730,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.15,
                         'reportingTaxCalculated': 0.15,
                         'reportingTaxableUnits': 15.0,
                         'serCode': '',
                         'signatureCode': 'EMTV',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '38',
                         'stateFIPS': '',
                         'tax': 0.15,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.15,
                         'taxName': 'CA SPECIAL TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 15.0,
                         'taxableUnits': 15.0,
                         'transactionId': **********,
                         'transactionLineId': *************,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'discountTypeId': 0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': *************,
            'isItemTaxable': True,
            'isSSTP': False,
            'itemCode': 'false',
            'lineAmount': 15.0,
            'lineLocationTypes': [{'documentAddressId': *************,
                                   'documentLineId': *************,
                                   'documentLineLocationTypeId': **********,
                                   'locationTypeCode': 'ShipFrom'},
                                  {'documentAddressId': ***********,
                                   'documentLineId': *************,
                                   'documentLineLocationTypeId': **********,
                                   'locationTypeCode': 'ShipTo'}],
            'lineNumber': 'account.move.line,442',
            'nonPassthroughDetails': [],
            'originAddressId': *************,
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'revAccount': '',
            'sourcing': 'Origin',
            'tax': 1.09,
            'taxCalculated': 1.09,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxEngine': '',
            'taxIncluded': False,
            'taxOverrideAmount': 0.0,
            'taxOverrideReason': '',
            'taxOverrideType': 'None',
            'taxableAmount': 15.0,
            'transactionId': **********,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'boundaryOverrideId': 0,
            'businessIdentificationNo': '',
            'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Invoicing',
            'destinationAddressId': ***********,
            'details': [{'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 8000404539512,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionId': 5000531,
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.06,
                         'rateRuleId': 1525706,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.9,
                         'reportingTaxCalculated': 0.9,
                         'reportingTaxableUnits': 15.0,
                         'serCode': '',
                         'signatureCode': 'AGAM',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 0.9,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.9,
                         'taxName': 'CA STATE TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 15.0,
                         'taxableUnits': 15.0,
                         'transactionId': **********,
                         'transactionLineId': **********,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 9000404539514,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionId': 275,
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.0025,
                         'rateRuleId': 1525710,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.04,
                         'reportingTaxCalculated': 0.04,
                         'reportingTaxableUnits': 15.0,
                         'serCode': '',
                         'signatureCode': 'AIUQ',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '',
                         'stateFIPS': '',
                         'tax': 0.04,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.04,
                         'taxName': 'CA COUNTY TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 15.0,
                         'taxableUnits': 15.0,
                         'transactionId': **********,
                         'transactionLineId': **********,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'addressId': *************,
                         'country': 'US',
                         'countyFIPS': '',
                         'exemptAmount': 0.0,
                         'exemptReasonId': 4,
                         'exemptUnits': 0.0,
                         'id': 10000404539516,
                         'inState': True,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionId': 2001061792,
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'nonTaxableRuleId': 0,
                         'nonTaxableType': 'RateRule',
                         'nonTaxableUnits': 0.0,
                         'rate': 0.01,
                         'rateRuleId': 1525730,
                         'rateSourceId': 3,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.15,
                         'reportingTaxCalculated': 0.15,
                         'reportingTaxableUnits': 15.0,
                         'serCode': '',
                         'signatureCode': 'EMTV',
                         'sourcing': 'Origin',
                         'stateAssignedNo': '38',
                         'stateFIPS': '',
                         'tax': 0.15,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.15,
                         'taxName': 'CA SPECIAL TAX',
                         'taxOverride': 0.0,
                         'taxRegionId': 4016940,
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxTypeGroupId': 'SalesAndUse',
                         'taxableAmount': 15.0,
                         'taxableUnits': 15.0,
                         'transactionId': **********,
                         'transactionLineId': **********,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'discountTypeId': 0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': **********,
            'isItemTaxable': True,
            'isSSTP': False,
            'itemCode': 'false',
            'lineAmount': 15.0,
            'lineLocationTypes': [{'documentAddressId': *************,
                                   'documentLineId': **********,
                                   'documentLineLocationTypeId': ***********,
                                   'locationTypeCode': 'ShipFrom'},
                                  {'documentAddressId': ***********,
                                   'documentLineId': **********,
                                   'documentLineLocationTypeId': ***********,
                                   'locationTypeCode': 'ShipTo'}],
            'lineNumber': 'account.move.line,443',
            'nonPassthroughDetails': [],
            'originAddressId': *************,
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'revAccount': '',
            'sourcing': 'Origin',
            'tax': 1.09,
            'taxCalculated': 1.09,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxEngine': '',
            'taxIncluded': False,
            'taxOverrideAmount': 0.0,
            'taxOverrideReason': '',
            'taxOverrideType': 'None',
            'taxableAmount': 15.0,
            'transactionId': **********,
            'vatCode': '',
            'vatNumberTypeId': 0}],
 'locationCode': '',
 'locationTypes': [{'documentAddressId': *************,
                    'documentId': **********,
                    'documentLocationTypeId': **********,
                    'locationTypeCode': 'ShipFrom'},
                   {'documentAddressId': ***********,
                    'documentId': **********,
                    'documentLocationTypeId': **********,
                    'locationTypeCode': 'ShipTo'}],
 'locked': False,
 'modifiedDate': '2021-09-27T16:19:38.6053604Z',
 'modifiedUserId': 212768,
 'originAddressId': *************,
 'purchaseOrderNo': '',
 'reconciled': False,
 'referenceCode': 'INV/2021/01/0001',
 'region': 'CA',
 'reportingLocationCode': '',
 'salespersonCode': '',
 'softwareVersion': '********',
 'status': 'Saved',
 'summary': [{'country': 'US',
              'exemption': 0.0,
              'jurisCode': '06',
              'jurisName': 'CALIFORNIA',
              'jurisType': 'State',
              'nonTaxable': 0.0,
              'rate': 0.06,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '',
              'tax': 5.4,
              'taxAuthorityType': 45,
              'taxCalculated': 5.4,
              'taxName': 'CA STATE TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': '075',
              'jurisName': 'SAN FRANCISCO',
              'jurisType': 'County',
              'nonTaxable': 0.0,
              'rate': 0.0025,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '',
              'tax': 0.24,
              'taxAuthorityType': 45,
              'taxCalculated': 0.24,
              'taxName': 'CA COUNTY TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': 'EMTV0',
              'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
              'jurisType': 'Special',
              'nonTaxable': 0.0,
              'rate': 0.01,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '38',
              'tax': 0.9,
              'taxAuthorityType': 45,
              'taxCalculated': 0.9,
              'taxName': 'CA SPECIAL TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0}],
 'taxDate': '2021-01-01',
 'taxOverrideAmount': 0.0,
 'taxOverrideReason': '',
 'taxOverrideType': 'None',
 'totalAmount': 90.0,
 'totalDiscount': 0.0,
 'totalExempt': 0.0,
 'totalTax': 6.54,
 'totalTaxCalculated': 6.54,
 'totalTaxable': 90.0,
 'type': 'SalesInvoice',
 'version': 1}
