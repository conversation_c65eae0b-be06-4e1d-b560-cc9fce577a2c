<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_partner_form_inherit" model="ir.ui.view">
        <field name="name">res.partner.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <group name="sale" position="inside">
                <field name="avatax_unique_code"/>
                <field name="avalara_partner_code" attrs="{'invisible': ['|', ('parent_id', '!=', False), ('is_company', '=', False)]}"/>
                <field name="avalara_exemption_id" attrs="{'invisible': ['|', ('parent_id', '!=', False), ('is_company', '=', False)]}"/>
            </group>
            <xpath expr="//div[hasclass('o_address_format')]" position="after">
                <field name="avalara_show_address_validation" invisible="1"/>
                <span class="o_form_label o_td_label"/>
                <button class="btn-link"
                        type="object"
                        name="action_open_validation_wizard"
                        string="Validate address"
                        attrs="{'invisible': [('avalara_show_address_validation', '=', False)]}"
                />
            </xpath>
        </field>
    </record>
</odoo>
