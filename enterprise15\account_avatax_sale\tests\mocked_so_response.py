def generate_response(sale_order_line_ids):
    assert len(sale_order_line_ids) == 5, "the mocked response is for 5 lines"
    for i, line in enumerate(response['lines']):
        line['lineNumber'] = 'sale.order.line,%s' % sale_order_line_ids[i].id
    return response


response = {'addresses': [{'boundaryLevel': 'Address',
                'city': 'San Francisco',
                'country': 'US',
                'id': 0,
                'latitude': '37.764754',
                'line1': '2280 Market St',
                'line2': '',
                'line3': '',
                'longitude': '-122.432634',
                'postalCode': '94114',
                'region': 'CA',
                'taxRegionId': 4016940,
                'transactionId': 0},
               {'boundaryLevel': 'Address',
                'city': 'San Francisco',
                'country': 'US',
                'id': 0,
                'latitude': '37.71116',
                'line1': '250 Executive Park Blvd',
                'line2': '',
                'line3': '',
                'longitude': '-122.391717',
                'postalCode': '94134',
                'region': 'CA',
                'taxRegionId': 4016940,
                'transactionId': 0}],
 'adjustmentReason': 'NotAdjusted',
 'batchCode': '',
 'code': 'Sales Order 85',
 'companyId': 2765828,
 'currencyCode': 'USD',
 'customerCode': 'CUST123456',
 'customerUsageType': '',
 'customerVendorCode': 'CUST123456',
 'date': '2021-01-01',
 'entityUseCode': '',
 'exchangeRate': 1.0,
 'exchangeRateCurrencyCode': 'USD',
 'exchangeRateEffectiveDate': '2021-01-01',
 'exemptNo': '',
 'id': 0,
 'lines': [{'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Odoo User',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 2.1,
                         'reportingTaxCalculated': 2.1,
                         'reportingTaxableUnits': 35.0,
                         'stateAssignedNo': '',
                         'tax': 2.1,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 2.1,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 35.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.09,
                         'reportingTaxCalculated': 0.09,
                         'reportingTaxableUnits': 35.0,
                         'stateAssignedNo': '',
                         'tax': 0.09,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.09,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 35.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0125,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.44,
                         'reportingTaxCalculated': 0.44,
                         'reportingTaxableUnits': 35.0,
                         'stateAssignedNo': '052',
                         'tax': 0.44,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.44,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 35.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.35,
                         'reportingTaxCalculated': 0.35,
                         'reportingTaxableUnits': 35.0,
                         'stateAssignedNo': '38',
                         'tax': 0.35,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.35,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 35.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': '',
            'lineAmount': 35.0,
            'lineNumber': 'sale.order.line,156',
            'nonPassthroughDetails': [],
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'tax': 2.98,
            'taxCalculated': 2.98,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxIncluded': False,
            'taxableAmount': 35.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Odoo User Initial Discound',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': -0.3,
                         'reportingTaxCalculated': -0.3,
                         'reportingTaxableUnits': -5.0,
                         'stateAssignedNo': '',
                         'tax': -0.3,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': -0.3,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': -5.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': -0.01,
                         'reportingTaxCalculated': -0.01,
                         'reportingTaxableUnits': -5.0,
                         'stateAssignedNo': '',
                         'tax': -0.01,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': -0.01,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': -5.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0125,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': -0.06,
                         'reportingTaxCalculated': -0.06,
                         'reportingTaxableUnits': -5.0,
                         'stateAssignedNo': '052',
                         'tax': -0.06,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': -0.06,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': -5.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': -0.05,
                         'reportingTaxCalculated': -0.05,
                         'reportingTaxableUnits': -5.0,
                         'stateAssignedNo': '38',
                         'tax': -0.05,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': -0.05,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': -5.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': '',
            'lineAmount': -5.0,
            'lineNumber': 'sale.order.line,157',
            'nonPassthroughDetails': [],
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'tax': -0.42,
            'taxCalculated': -0.42,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxIncluded': False,
            'taxableAmount': -5.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Accounting',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 1.8,
                         'reportingTaxCalculated': 1.8,
                         'reportingTaxableUnits': 30.0,
                         'stateAssignedNo': '',
                         'tax': 1.8,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 1.8,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 30.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.08,
                         'reportingTaxCalculated': 0.08,
                         'reportingTaxableUnits': 30.0,
                         'stateAssignedNo': '',
                         'tax': 0.08,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.08,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 30.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0125,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.38,
                         'reportingTaxCalculated': 0.38,
                         'reportingTaxableUnits': 30.0,
                         'stateAssignedNo': '052',
                         'tax': 0.38,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.38,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 30.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.3,
                         'reportingTaxCalculated': 0.3,
                         'reportingTaxableUnits': 30.0,
                         'stateAssignedNo': '38',
                         'tax': 0.3,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.3,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 30.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': '',
            'lineAmount': 30.0,
            'lineNumber': 'sale.order.line,158',
            'nonPassthroughDetails': [],
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'tax': 2.56,
            'taxCalculated': 2.56,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxIncluded': False,
            'taxableAmount': 30.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Expenses',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.9,
                         'reportingTaxCalculated': 0.9,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '',
                         'tax': 0.9,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.9,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.04,
                         'reportingTaxCalculated': 0.04,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '',
                         'tax': 0.04,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.04,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0125,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.19,
                         'reportingTaxCalculated': 0.19,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '052',
                         'tax': 0.19,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.19,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.15,
                         'reportingTaxCalculated': 0.15,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '38',
                         'tax': 0.15,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.15,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': '',
            'lineAmount': 15.0,
            'lineNumber': 'sale.order.line,159',
            'nonPassthroughDetails': [],
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'tax': 1.28,
            'taxCalculated': 1.28,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxIncluded': False,
            'taxableAmount': 15.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'Invoicing',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.9,
                         'reportingTaxCalculated': 0.9,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '',
                         'tax': 0.9,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.9,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.04,
                         'reportingTaxCalculated': 0.04,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '',
                         'tax': 0.04,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.04,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0125,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.19,
                         'reportingTaxCalculated': 0.19,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '052',
                         'tax': 0.19,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.19,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.15,
                         'reportingTaxCalculated': 0.15,
                         'reportingTaxableUnits': 15.0,
                         'stateAssignedNo': '38',
                         'tax': 0.15,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.15,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 15.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': '',
            'lineAmount': 15.0,
            'lineNumber': 'sale.order.line,160',
            'nonPassthroughDetails': [],
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2021-01-01',
            'tax': 1.28,
            'taxCalculated': 1.28,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2021-01-01',
            'taxIncluded': False,
            'taxableAmount': 15.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0}],
 'locationCode': '',
 'locked': False,
 'modifiedDate': '2021-12-13T18:38:19.2067334Z',
 'modifiedUserId': 1452151,
 'paymentDate': '2021-01-01',
 'purchaseOrderNo': '',
 'reconciled': False,
 'referenceCode': 'S00084',
 'reportingLocationCode': '',
 'salespersonCode': '',
 'status': 'Temporary',
 'summary': [{'country': 'US',
              'exemption': 0.0,
              'jurisCode': '06',
              'jurisName': 'CALIFORNIA',
              'jurisType': 'State',
              'nonTaxable': 0.0,
              'rate': 0.06,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '',
              'tax': 5.4,
              'taxAuthorityType': 45,
              'taxCalculated': 5.4,
              'taxName': 'CA STATE TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': '075',
              'jurisName': 'SAN FRANCISCO',
              'jurisType': 'County',
              'nonTaxable': 0.0,
              'rate': 0.0025,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '',
              'tax': 0.24,
              'taxAuthorityType': 45,
              'taxCalculated': 0.24,
              'taxName': 'CA COUNTY TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': 'EMTV0',
              'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
              'jurisType': 'Special',
              'nonTaxable': 0.0,
              'rate': 0.01,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '38',
              'tax': 0.9,
              'taxAuthorityType': 45,
              'taxCalculated': 0.9,
              'taxName': 'CA SPECIAL TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': 'EMBE0',
              'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
              'jurisType': 'Special',
              'nonTaxable': 0.0,
              'rate': 0.0125,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '052',
              'tax': 1.14,
              'taxAuthorityType': 45,
              'taxCalculated': 1.14,
              'taxName': 'CA SPECIAL TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 90.0}],
 'taxDate': '2021-01-01',
 'taxOverrideAmount': 0.0,
 'taxOverrideReason': 'Manually changed the tax calculation date',
 'taxOverrideType': 'TaxDate',
 'totalAmount': 90.0,
 'totalDiscount': 0.0,
 'totalExempt': 0.0,
 'totalTax': 7.68,
 'totalTaxCalculated': 7.68,
 'totalTaxable': 90.0,
 'type': 'SalesOrder',
 'version': 1}
