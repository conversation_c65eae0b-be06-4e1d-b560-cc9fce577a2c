def generate_response(subscription_line_ids):
    assert len(subscription_line_ids) == 2, "the mocked response is for 2 lines"
    for i, line in enumerate(response['lines']):
        line['lineNumber'] = 'sale.subscription.line,%s' % subscription_line_ids[i].id
    return response


response = {'addresses': [{'boundaryLevel': 'Zip5',
                'city': 'false',
                'country': 'US',
                'id': 0,
                'latitude': '37.718846',
                'line1': 'false',
                'line2': '',
                'line3': '',
                'longitude': '-122.409042',
                'postalCode': '94134',
                'region': 'CA',
                'taxRegionId': 4016940,
                'transactionId': 0},
               {'boundaryLevel': 'Address',
                'city': 'San Francisco',
                'country': 'US',
                'id': 0,
                'latitude': '37.71116',
                'line1': '250 Executive Park Blvd',
                'line2': '',
                'line3': '',
                'longitude': '-122.391717',
                'postalCode': '94134',
                'region': 'CA',
                'taxRegionId': 4016940,
                'transactionId': 0}],
 'adjustmentReason': 'NotAdjusted',
 'batchCode': '',
 'code': 'Subscription 54',
 'companyId': 2765828,
 'currencyCode': 'USD',
 'customerCode': 'Contact 4578',
 'customerUsageType': '',
 'customerVendorCode': 'Contact 4578',
 'date': '2022-05-09',
 'entityUseCode': '',
 'exchangeRate': 1.0,
 'exchangeRateCurrencyCode': 'USD',
 'exchangeRateEffectiveDate': '2022-05-09',
 'exemptNo': '',
 'id': 0,
 'lines': [{'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': '[PROD1] Product',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 7.2,
                         'reportingTaxCalculated': 7.2,
                         'reportingTaxableUnits': 120.0,
                         'stateAssignedNo': '',
                         'tax': 7.2,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 7.2,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 120.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.3,
                         'reportingTaxCalculated': 0.3,
                         'reportingTaxableUnits': 120.0,
                         'stateAssignedNo': '',
                         'tax': 0.3,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.3,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 120.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01375,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 1.65,
                         'reportingTaxCalculated': 1.65,
                         'reportingTaxableUnits': 120.0,
                         'stateAssignedNo': '940',
                         'tax': 1.65,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 1.65,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 120.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 1.2,
                         'reportingTaxCalculated': 1.2,
                         'reportingTaxableUnits': 120.0,
                         'stateAssignedNo': '38',
                         'tax': 1.2,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 1.2,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 120.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': 'UPC:*********',
            'lineAmount': 120.0,
            'lineNumber': 'sale.subscription.line,31',
            'nonPassthroughDetails': [],
            'quantity': 1.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2022-05-09',
            'tax': 10.35,
            'taxCalculated': 10.35,
            'taxCode': 'DC010000',
            'taxCodeId': 8575,
            'taxDate': '2022-05-09',
            'taxIncluded': False,
            'taxableAmount': 120.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0},
           {'costInsuranceFreight': 0.0,
            'customerUsageType': '',
            'description': 'TestProduct2',
            'details': [{'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '06',
                         'jurisName': 'CALIFORNIA',
                         'jurisType': 'STA',
                         'jurisdictionType': 'State',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.06,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 3.6,
                         'reportingTaxCalculated': 3.6,
                         'reportingTaxableUnits': 60.0,
                         'stateAssignedNo': '',
                         'tax': 3.6,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 3.6,
                         'taxName': 'CA STATE TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 60.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': '075',
                         'jurisName': 'SAN FRANCISCO',
                         'jurisType': 'CTY',
                         'jurisdictionType': 'County',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.0025,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.15,
                         'reportingTaxCalculated': 0.15,
                         'reportingTaxableUnits': 60.0,
                         'stateAssignedNo': '',
                         'tax': 0.15,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.15,
                         'taxName': 'CA COUNTY TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 60.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMBE0',
                         'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01375,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.83,
                         'reportingTaxCalculated': 0.83,
                         'reportingTaxableUnits': 60.0,
                         'stateAssignedNo': '940',
                         'tax': 0.83,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.83,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 60.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'},
                        {'country': 'US',
                         'exemptAmount': 0.0,
                         'id': 0,
                         'isFee': False,
                         'isNonPassThru': False,
                         'jurisCode': 'EMTV0',
                         'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
                         'jurisType': 'STJ',
                         'jurisdictionType': 'Special',
                         'liabilityType': 'Seller',
                         'nonTaxableAmount': 0.0,
                         'rate': 0.01,
                         'rateType': 'General',
                         'rateTypeCode': 'G',
                         'region': 'CA',
                         'reportingExemptUnits': 0.0,
                         'reportingNonTaxableUnits': 0.0,
                         'reportingTax': 0.6,
                         'reportingTaxCalculated': 0.6,
                         'reportingTaxableUnits': 60.0,
                         'stateAssignedNo': '38',
                         'tax': 0.6,
                         'taxAuthorityTypeId': 45,
                         'taxCalculated': 0.6,
                         'taxName': 'CA SPECIAL TAX',
                         'taxSubTypeId': 'S',
                         'taxType': 'Sales',
                         'taxableAmount': 60.0,
                         'transactionId': 0,
                         'transactionLineId': 0,
                         'unitOfBasis': 'PerCurrencyUnit'}],
            'discountAmount': 0.0,
            'entityUseCode': '',
            'exemptAmount': 0.0,
            'exemptCertId': 0,
            'exemptNo': '',
            'hsCode': '',
            'id': 0,
            'isItemTaxable': True,
            'itemCode': '',
            'lineAmount': 60.0,
            'lineNumber': 'sale.subscription.line,32',
            'nonPassthroughDetails': [],
            'quantity': 2.0,
            'ref1': '',
            'ref2': '',
            'reportingDate': '2022-05-09',
            'tax': 5.18,
            'taxCalculated': 5.18,
            'taxCode': 'PA3000100',
            'taxCodeId': 91027,
            'taxDate': '2022-05-09',
            'taxIncluded': False,
            'taxableAmount': 60.0,
            'transactionId': 0,
            'vatCode': '',
            'vatNumberTypeId': 0}],
 'locationCode': '',
 'locked': False,
 'modifiedDate': '2022-05-09T18:06:07.2761118Z',
 'modifiedUserId': 1452151,
 'paymentDate': '2022-05-09',
 'purchaseOrderNo': '',
 'reconciled': False,
 'referenceCode': 'TestSubscription',
 'reportingLocationCode': '',
 'salespersonCode': '',
 'status': 'Temporary',
 'summary': [{'country': 'US',
              'exemption': 0.0,
              'jurisCode': '06',
              'jurisName': 'CALIFORNIA',
              'jurisType': 'State',
              'nonTaxable': 0.0,
              'rate': 0.06,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '',
              'tax': 10.8,
              'taxAuthorityType': 45,
              'taxCalculated': 10.8,
              'taxName': 'CA STATE TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 180.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': '075',
              'jurisName': 'SAN FRANCISCO',
              'jurisType': 'County',
              'nonTaxable': 0.0,
              'rate': 0.0025,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '',
              'tax': 0.45,
              'taxAuthorityType': 45,
              'taxCalculated': 0.45,
              'taxName': 'CA COUNTY TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 180.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': 'EMTV0',
              'jurisName': 'SAN FRANCISCO CO LOCAL TAX SL',
              'jurisType': 'Special',
              'nonTaxable': 0.0,
              'rate': 0.01,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '38',
              'tax': 1.8,
              'taxAuthorityType': 45,
              'taxCalculated': 1.8,
              'taxName': 'CA SPECIAL TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 180.0},
             {'country': 'US',
              'exemption': 0.0,
              'jurisCode': 'EMBE0',
              'jurisName': 'SAN FRANCISCO COUNTY DISTRICT TAX SP',
              'jurisType': 'Special',
              'nonTaxable': 0.0,
              'rate': 0.01375,
              'rateType': 'General',
              'region': 'CA',
              'stateAssignedNo': '940',
              'tax': 2.48,
              'taxAuthorityType': 45,
              'taxCalculated': 2.48,
              'taxName': 'CA SPECIAL TAX',
              'taxSubType': 'S',
              'taxType': 'Sales',
              'taxable': 180.0}],
 'taxDate': '2022-05-09',
 'taxOverrideAmount': 0.0,
 'taxOverrideReason': 'Manually changed the tax calculation date',
 'taxOverrideType': 'TaxDate',
 'totalAmount': 180.0,
 'totalDiscount': 0.0,
 'totalExempt': 0.0,
 'totalTax': 15.53,
 'totalTaxCalculated': 15.53,
 'totalTaxable': 180.0,
 'type': 'SalesOrder',
 'version': 1}
