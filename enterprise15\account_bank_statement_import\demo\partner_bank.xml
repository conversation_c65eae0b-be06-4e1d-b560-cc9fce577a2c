<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="ofx_partner_bank_1" model="res.partner.bank">
            <field name="acc_number">BE68539007547034</field>
            <field name="partner_id" ref="base.res_partner_2"></field>
            <field name="bank_id" ref="base.res_bank_1"/>
        </record>

        <record id="ofx_partner_bank_2" model="res.partner.bank">
            <field name="acc_number">***********</field>
            <field name="partner_id" ref="base.res_partner_3"></field>
            <field name="bank_id" ref="base.res_bank_1"/>
        </record>

        <record id="qif_partner_bank_1" model="res.partner.bank">
            <field name="acc_number">***********</field>
            <field name="partner_id" ref="base.res_partner_4"></field>
            <field name="bank_id" ref="base.res_bank_1"/>
        </record>

        <record id="qif_partner_bank_2" model="res.partner.bank">
            <field name="acc_number">***********</field>
            <field name="partner_id" ref="base.res_partner_3"></field>
            <field name="bank_id" ref="base.res_bank_1"/>
        </record>

    </data>
</odoo>
