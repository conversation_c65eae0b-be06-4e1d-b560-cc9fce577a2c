# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# Wil Odoo, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_bank_statement_import
#. openerp-web
#: code:addons/account_bank_statement_import/static/src/js/account_bank_statement_import.js:0
#, python-format
msgid " Import Template for Bank Statements"
msgstr "استيراد قالب لكشوفات الحسابات البنكية "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "تم استيراد %d معاملات بالفعل وتجاهلها. "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "تم تجاهل 1 معاملة سبق استيرادها بالفعل."

#. module: account_bank_statement_import
#: model:ir.model.constraint,message:account_bank_statement_import.constraint_account_bank_statement_line_unique_import_id
msgid "A bank account transactions can be imported only once !"
msgstr "يمكن استيراد معاملات الحساب البنكي مرة واحدة فقط!"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_partner_id
msgid "Account Holder"
msgstr "صاحب الحساب "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_acc_number
msgid "Account Number"
msgstr "رقم الحساب"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__active
msgid "Active"
msgstr "نشط"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_note
msgid "Activity Summary"
msgstr "مخلص النشاطات"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_user_id
msgid "Activity User"
msgstr "مستخدِم النشاط "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_type_id
msgid ""
"Activity will be automatically scheduled on payment due date, improving "
"collection process."
msgstr ""
"سوف يتم جدولة النشاط تلقائياً حسب تاريخ استحقاق الدفعة، لتحسين عملية "
"التحصيل. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_name
msgid "Alias Name"
msgstr "اسم اللقب"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_domain
msgid "Alias domain"
msgstr "نطاق اللقب"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type_control_ids
msgid "Allowed account types"
msgstr "أنواع الحسابات المسموحة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__account_control_ids
msgid "Allowed accounts"
msgstr "الحسابات المسموحة"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "Already imported items"
msgstr "العناصر التي تم استيرادها بالفعل "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__available_payment_method_ids
msgid "Available Payment Method"
msgstr "طريقة السداد المتاحة"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_id
#, python-format
msgid "Bank"
msgstr "البنك"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_account_id
msgid "Bank Account"
msgstr "حساب بنكي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_statements_source
msgid "Bank Feeds"
msgstr "موجزات البنك "

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "اسم دفتر اليومية البنكي "

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "بند كشف الحساب البنكي"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "الضبط اليدوي لإعدادات البنك"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__suspense_account_id
msgid ""
"Bank statements transactions will be posted on the suspense account until "
"the final reconciliation allowing finding the right account."
msgstr ""
"سوف يتم ترحيل معاملات كشف البنك إلى الحساب المعلق إلى أن تسمح التسوية "
"النهائية بإيجاد الحساب الصحيح. "

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "إلغاء "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"لقد تعذر إيجاد دفتر اليومية الذي يتم توريد كشف الحساب المحدد إليه. الرجاء "
"اختيار دفتر اليومية يدوياً. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_sequence_id
msgid "Check Sequence"
msgstr "تسلسل الشيك "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"حدد هذا المربع إذا كنت لا تريد أن تتشارك الفواتير والإشعارات الدائنة "
"المستخرجة من دفتر اليومية هذا نفس التسلسل "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr "حدد هذا الاختيار إذا كانت شيكاتك المطبوعة مسبقًا غير مُرقمة."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_sequence_id
msgid "Checks numbering sequence."
msgstr "تسلسل أرقام الشيكات."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__color
msgid "Color Index"
msgstr "معرف اللون"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_model
msgid "Communication Standard"
msgstr "معايير الإبلاغ "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_type
msgid "Communication Type"
msgstr "نوع التواصل"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_id
msgid "Company"
msgstr "الشركة "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_id
msgid "Company related to this journal"
msgstr "الشركة المتعلقة بهذه اليومية "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__compatible_edi_ids
msgid "Compatible Edi"
msgstr "نظام تبادل البيانات إلكترونياً (EDI) متوافق "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
" تعذر فهم طبيعة الملف المحدد.\n"
"هل قمت بتثبيت التطبيق المناسب لدعم هذا النوع من الملفات؟ "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__country_code
msgid "Country Code"
msgstr "رمز الدولة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__currency_id
msgid "Currency"
msgstr "العملة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "تسلسل الإشعارات الدائنة المخصص "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_account_id
msgid "Default Account"
msgstr "الحساب الافتراضي "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_account_type
msgid "Default Account Type"
msgstr "نوع الحساب الافتراضي "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr "يحدد كيفية تسجيل كشوفات الحساب البنكية"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "صيغة نظام تبادل البيانات إلكترونياً (EDI) تدعم حركات هذه اليومية "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__edi_format_ids
msgid "Electronic invoicing"
msgstr "الفوترة الإلكترونية "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_id
msgid "Email Alias"
msgstr "لقب البريد الإلكتروني"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__entries_count
msgid "Entries Count"
msgstr "عدد القيود "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__attachment_ids
msgid "Files"
msgstr "الملفات"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome، مثال: fa-tasks "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import__attachment_ids
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr "احصل على كشوفات حسابك البنكية بصيغة إلكترونية وقم بتحديدها من هنا. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__id
msgid "ID"
msgstr "المُعرف"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "أيقونة للإشارة إلى نشاط مستثنى. "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"If it contains transactions for more than one account, it must be imported "
"on each of them."
msgstr ""
"إذا كان يتضمن معاملات لأكثر من حساب واحد، يجب أن يتم استيراده في كل منها. "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__restrict_mode_hash_table
msgid ""
"If ticked, the accounting entry or invoice receives a hash as soon as it is "
"posted and cannot be modified anymore."
msgstr ""
"إذا كان محدداً، يتلقى القيد المحاسبي أو فاتورة العميل تشفيراً فور ترحيله ولن"
" يكون بالإمكان تعديله بعد الآن. "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
#, python-format
msgid "Import"
msgstr "استيراد"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "استيراد كشف حساب بنكي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line__unique_import_id
msgid "Import ID"
msgstr "معرف الاستيراد"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "استيراد كشف حساب"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__inbound_payment_method_line_ids
msgid "Inbound Payment Methods"
msgstr "طرق الدفع الواردة "

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.install_more_import_formats_action
msgid "Install Import Format"
msgstr "تثبيت صيغة الاستيراد"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_name
msgid "It creates draft invoices and bills by sending an email."
msgstr "ينشئ مسودات فواتير عن طريق إرسال بريد إلكتروني. "

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__journal_id
msgid "Journal"
msgstr "دفتر اليومية"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "إنشاء دفتر يومية"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Journal Creation on Bank Statement Import"
msgstr "إنشاء دفتر يومية عند استيراد كشف الحساب البنكي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__journal_group_ids
msgid "Journal Groups"
msgstr "مجموعات اليومية "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__name
msgid "Journal Name"
msgstr "اسم دفتر اليومية"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__json_activity_data
msgid "Json Activity Data"
msgstr "بيانات أنشطة Json "

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the upload. If this "
"was a mistake, hit cancel to abort the upload."
msgstr ""
"فقط اضغط على موافق لإنشاء الحساب/اليومية وإنهاء الرفع. إذا قمت بذلك عن طريق "
"الخطأ، فاضغط على إلغاء لإلغاء عملية الرفع. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__kanban_dashboard
msgid "Kanban Dashboard"
msgstr "لوحة بيانات كانبان "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "الرسم البياني للوحة بيانات كانبان "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import____last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_user_id
msgid "Leave empty to assign the Salesperson of the invoice."
msgstr "اتركه فارغاً حتى يتم تعيين مندوب المبيعات المذكور في الفاتورة. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__restrict_mode_hash_table
msgid "Lock Posted Entries with Hash"
msgstr "قم بقفل القيود المرحّلة باستخدام التشفير "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__loss_account_id
msgid "Loss Account"
msgstr "حساب الخسارة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_manual_sequencing
msgid "Manual Numbering"
msgstr "ترقيم يدوي"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__inbound_payment_method_line_ids
msgid ""
"Manual: Get paid by any method outside of Odoo.\n"
"Payment Acquirers: Each payment acquirer has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"يدوي: تقاضى المال بأي وسيلة دفع خارج أودو.\n"
"بوابات الدفع: لكل بوابة دفع طريقتها الخاصة. تمكّن من طلب معاملة على/إلى بطاقة، بفضل رمز الدفع الذي يتم حفظه من قِبَل الشريك عند الشراء أو الاشتراك عبر الإنترنت. \n"
"إيداع مجمّع: قم بتحصيل شيكات متعددة للعملاء دفعة واحدة وقم بتسليم إيداع مجمّع إلى مصرفك. يُعد تطبيق  account_batch_payment ضرورياً.\n"
"الدفع المباشر في SEPA: تقاضى المال في منطقة SEPA بفضل التوكيل الذي سوف يمنحك إياه شريكك. يُعد تطبيق  account_sepa ضرورياً.\n"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__outbound_payment_method_line_ids
msgid ""
"Manual: Pay by any method outside of Odoo.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
msgstr ""
"يدوي: تقاضى المال بأي وسيلة دفع خارج أودو.\n"
"شيك: تمكن من دفع الفواتير عن طريق الشيكات وقم بطباعتها من أودو.\n"
"تحويل الدفع في SEPA: تمكن من الدفع في منطقة SEPA عن طريق تسليم ملف تحويل الدفع في SEPA لمصرفك. يُعد تطبيق account_sepa ضرورياً.\n"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_next_number
msgid "Next Check Number"
msgstr "رقم الشيك التالي"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "No currency found matching '%s'."
msgstr "لم يمكن العثور على أي عملة تطابق '%s'."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الاخطاء"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "موافق"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__outbound_payment_method_line_ids
msgid "Outbound Payment Methods"
msgstr "طرق الدفع الصادرة "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__profit_account_id
msgid "Profit Account"
msgstr "حساب الربح"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_type_id
msgid "Schedule Activity"
msgstr "جدولة نشاط "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__secure_sequence_id
msgid "Secure Sequence"
msgstr "تسلسل محمي "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"اختر 'بيع' لعرض دفاتر اليومية الخاصة بفواتير العملاء. \n"
"اختر 'شراء' لعرض دفاتر اليومية الخاصة بفواتير الموردين. \n"
"اختر 'نقدي' أو 'بنك' لعرض دفاتر اليومية الخاصة بمدفوعات العميل أو المورد.\n"
" اختر 'عام' لعرض دفاتر اليومية الخاصة بالعمليات المتنوعة."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select Files"
msgstr "قم بتحديد الملفات "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__selected_payment_method_codes
msgid "Selected Payment Method Codes"
msgstr "أكواد طريقة الدفع المختارة "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "إرسال فواتير XML/EDI "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_id
msgid ""
"Send one separate email for each invoice.\n"
"\n"
"Any file extension will be accepted.\n"
"\n"
"Only PDF and XML files will be interpreted by Odoo"
msgstr ""
"أرسل بريداً إلكترونياً منفصلاً لكل فاتورة.\n"
"\n"
"سوف يتم قبول أي ملحق ملف.\n"
"\n"
"وحدها الملفات بصيغة PDF وXML التي يتم تفسيرها من قِبَل أودو "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_override_regex
msgid "Sequence Override Regex"
msgstr "يتجاوز التسلسل التعبير النمطي "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_next_number
msgid "Sequence number of the next printed check."
msgstr "رقم التسلسل للشيك المطبوع التالي. "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__secure_sequence_id
msgid "Sequence to use to ensure the securisation of data"
msgstr "التسلسل لاستخدامه لضمان أمن البيانات "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__active
msgid "Set active to false to hide the Journal without removing it."
msgstr "قم بإزالة العلامة من الحقل نشط لإخفاء دفتر اليومية دون إزالته."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__code
msgid "Short Code"
msgstr "الكود المختصر"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__code
msgid ""
"Shorter name used for display. The journal entries of this journal will also"
" be named using this prefix by default."
msgstr ""
"اسم أقصر يُستخدم لأغراض العرض. سوف تتم تسمية قيود هذه اليومية باستخدام هذه "
"البادئة أيضاً بشكل افتراضي. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__show_on_dashboard
msgid "Show journal on dashboard"
msgstr "إظهار دفتر اليومية في لوحة البيانات "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة بناء على الأنشطة\n"
"المتأخرة: لقد مر تاريخ الاستحقاق بالفعل\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__suspense_account_id
msgid "Suspense Account"
msgstr "حساب معلق "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_override_regex
msgid ""
"Technical field used to enforce complex sequence composition that the system would normally misunderstand.\n"
"This is a regex that can include all the following capture groups: prefix1, year, prefix2, month, prefix3, seq, suffix.\n"
"The prefix* groups are the separators between the year, month and the actual increasing sequence number (seq).\n"
"e.g: ^(?P<prefix1>.*?)(?P<year>\\d{4})(?P<prefix2>\\D*?)(?P<month>\\d{2})(?P<prefix3>\\D+?)(?P<seq>\\d+)(?P<suffix>\\D*?)$"
msgstr ""
"حقل تقني يُستخدَم لدعم صياغات التسلسلات المعقدة التي قد يسيء النظام فهمها عادةً.\n"
"بإمكان هذا التعبير النمطي أن يشمل كافة المجموعات التالية: prefix1، السنة، prefix2، الشهر، prefix3، التسلسل، ملحق.\n"
"مجموعات prefix* هي التي تفصل بين السنة والشهر ورقم التسلسل المتزايد الفعلي، (seq).\n"
"مثال: ^(?P<prefix1>.*?)(?P<year>\\d{4})(?P<prefix2>\\D*?)(?P<month>\\d{2})(?P<prefix3>\\D+?)(?P<seq>\\d+)(?P<suffix>\\D*?)$"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__selected_payment_method_codes
msgid "Technical field used to hide or show payment method options if needed."
msgstr "حقل تقني يُستخدم لإخفاء أو إظهار خيارات طرق الدفع، إذا لزم الأمر. "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"كود الدولة حسب المعيار الدولي ISO المكون من حرفين.\n"
"يمكنك استخدام هذا الحقل لإجراء بحث سريع."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are uploading is not yet recorded in Odoo. "
"In order to proceed with the upload, you need to create a bank journal for "
"this account."
msgstr ""
"الحساب المرتبط بكشف الحساب الذي قمت برفعه غير مسجل في أودو بعد. حتتى تتمكن "
"من الاستمرار بالرفع، عليك إنشاء يومية بنكية لهذا الحساب. "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr "الحساب في كشف الحساب البنكي (%s) مختلف عن حساب دفتر اليومية (%s)."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s)."
msgstr ""
"العملة في كشف الحساب البنكي (%s) مختلفة عن العملة في دفتر اليومية (%s)."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__currency_id
msgid "The currency used to enter statement"
msgstr "العملة المستخدمة في إدخال كشف الحساب البنكي"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "This file doesn't contain any statement for account %s."
msgstr "لا يحتوي هذا الملف على أي كشف للحساب %s. "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "This file doesn't contain any transaction for account %s."
msgstr "لا يحتوي هذا الملف على أي معاملة للحساب %s. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type
msgid "Type"
msgstr "النوع"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى المسجل. "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل غير المقروءة "

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Upload"
msgstr "رفع"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Upload Bank Statements"
msgstr "رفع كشوفات الحساب البنكية "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence
msgid "Used to order Journals in the dashboard view"
msgstr "يُستخدم لترتيب دفاتر اليومية في عرض لوحة البيانات "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"يُستخدم لتسجيل خسارة عندما يكون الرصيد الختامي لصندوق تسجيل النقد مختلفاً "
"عما يحسبه النظام "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"يُستخدم لتسجيل ربح عندما يكون الرصيد الختامي لصندوق تسجيل النقد مختلفاً عما "
"يحسبه النظام "

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "إذا ما كان يجب عرض دفتر اليومية هذا في لوحة البيانات أم لا "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "You already have imported that file."
msgstr "لقد قمت باستيراد هذا الملف بالفعل."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
"تستطيع اختيار نماذج مختلفة لكل نوع مرجع. المرجع الافتراضي هو مرجع أودو. "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr ""
"يمكنك من هنا تعيين وسيلة التواصل الافتراضية التي ستظهر على فواتير العميل "
"بمجرد اعتمادها، لمساعدة العميل على العودة لتلك الفاتورة تحديدًا عند القيام "
"بعملية السداد."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "You can upload your bank statement using:"
msgstr "بإمكانك رفع كشف الحساب الخاص بك باستخدام: "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "You have to set a Default Account for the journal: %s"
msgstr "عليك تعيين حساب افتراضي لليومية: %s"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "The following files could not be imported:\n"
msgstr "تعذر استيراد الملفات التالية: \n"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "View successfully imported statements"
msgstr "عرض كشوفات الحساب البنكية التي تم استيرادها بنجاح "

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "or"
msgstr "أو"
