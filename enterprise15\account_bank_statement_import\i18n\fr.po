# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_bank_statement_import
#. openerp-web
#: code:addons/account_bank_statement_import/static/src/js/account_bank_statement_import.js:0
#, python-format
msgid " Import Template for Bank Statements"
msgstr "Importer un modèle pour les relevés bancaires"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "%d transactions had already been imported and were ignored."
msgstr "%d transactions ont déjà été importées et ont été ignorées."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "1 transaction had already been imported and was ignored."
msgstr "1 transaction a déjà été importée et a été ignorée."

#. module: account_bank_statement_import
#: model:ir.model.constraint,message:account_bank_statement_import.constraint_account_bank_statement_line_unique_import_id
msgid "A bank account transactions can be imported only once !"
msgstr ""
"Les transactions d'un compte bancaire ne peuvent être importées qu'une seule"
" fois !"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_partner_id
msgid "Account Holder"
msgstr "Titulaire de compte"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_acc_number
msgid "Account Number"
msgstr "Numéro de compte"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__active
msgid "Active"
msgstr "Actif"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_state
msgid "Activity State"
msgstr "État de l'Activité"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_note
msgid "Activity Summary"
msgstr "Résumé d'activité"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_user_id
msgid "Activity User"
msgstr "Utilisateur réalisant l'activité"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_type_id
msgid ""
"Activity will be automatically scheduled on payment due date, improving "
"collection process."
msgstr ""
"Une activité sera automatiquement créée à la date de paiement, afin de "
"faciliter le recouvrement."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_name
msgid "Alias Name"
msgstr "Nom de l'alias"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_domain
msgid "Alias domain"
msgstr "Domaine d'alias"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type_control_ids
msgid "Allowed account types"
msgstr "Types de compte autorisés"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__account_control_ids
msgid "Allowed accounts"
msgstr "Comptes autorisés"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "Already imported items"
msgstr "Eléments déjà importés"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Méthode de paiement disponible "

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_id
#, python-format
msgid "Bank"
msgstr "Banque"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_account_id
msgid "Bank Account"
msgstr "Compte bancaire"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_statements_source
msgid "Bank Feeds"
msgstr "Provenance des relevés bancaires"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "Bank Journal Name"
msgstr "Nom du Journal de Banque"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Ligne de relevé bancaire"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "Configuration manuelle des paramètre de la banque"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__suspense_account_id
msgid ""
"Bank statements transactions will be posted on the suspense account until "
"the final reconciliation allowing finding the right account."
msgstr ""
"Les opérations de relevés bancaires seront comptabilisées sur le compte "
"d'attente jusqu'au lettrage final permettant de retrouver le bon compte."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Cancel"
msgstr "Annuler"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"Ne peut trouver le relevé bancaire parmi les journaux. Veuillez sélectionner"
" un journal manuellement."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_sequence_id
msgid "Check Sequence"
msgstr "Séquence chèque"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" credit notes made from this journal"
msgstr ""
"Cochez cette case si vous ne souhaitez pas partager la même séquence pour "
"les factures et les avoirs créés à partir de ce journal."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_manual_sequencing
msgid "Check this option if your pre-printed checks are not numbered."
msgstr ""
"Cochez cette option si vos chèques pré-imprimés ne sont pas numérotés."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_sequence_id
msgid "Checks numbering sequence."
msgstr "Séquence de numérotation des chèques."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__color
msgid "Color Index"
msgstr "Couleur"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_model
msgid "Communication Standard"
msgstr "Standard de communication"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_type
msgid "Communication Type"
msgstr "Type de communication"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_id
msgid "Company"
msgstr "Société"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__company_id
msgid "Company related to this journal"
msgstr "Société liée à ce journal"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__compatible_edi_ids
msgid "Compatible Edi"
msgstr "Compatible Edi"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file ?"
msgstr ""
"Ce fichier est incompréhensible.\n"
"Avez-vous installé le module qui supporte ce type de fichier ?"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__country_code
msgid "Country Code"
msgstr "Code du pays"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__create_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__create_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__create_date
msgid "Created on"
msgstr "Créé le"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__currency_id
msgid "Currency"
msgstr "Devise"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__refund_sequence
msgid "Dedicated Credit Note Sequence"
msgstr "Séquence dédiée aux avoirs"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_account_id
msgid "Default Account"
msgstr "Compte par défaut"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__default_account_type
msgid "Default Account Type"
msgstr "Type de Compte par Défaut"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__bank_statements_source
msgid "Defines how the bank statements will be registered"
msgstr "Définissez comment les relevés bancaires seront enregistrés"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__display_name
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "Format EDI qui supporte les mouvements dans ce journal"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__edi_format_ids
msgid "Electronic invoicing"
msgstr "Facturation électronique"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_id
msgid "Email Alias"
msgstr "Alias de messagerie"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__entries_count
msgid "Entries Count"
msgstr "Nombre de pièces"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__attachment_ids
msgid "Files"
msgstr "Fichiers"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome e.g. fa-tasks"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import__attachment_ids
msgid ""
"Get you bank statements in electronic format from your bank and select them "
"here."
msgstr ""
"Obtenez les relevés bancaires en version électronique depuis votre banque et"
" sélectionnez-les ici."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__has_message
msgid "Has Message"
msgstr "A un message"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__id
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__id
msgid "ID"
msgstr "ID"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"If it contains transactions for more than one account, it must be imported "
"on each of them."
msgstr ""
"S'il contient des transactions pour plusieurs comptes, il doit être importé "
"sur chacun d'eux."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__restrict_mode_hash_table
msgid ""
"If ticked, the accounting entry or invoice receives a hash as soon as it is "
"posted and cannot be modified anymore."
msgstr ""
"Si elle est cochée, l'entrée comptable ou la facture reçoit un hash dès "
"qu'elle est comptabilisée et ne peut plus être modifiée."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_journal.py:0
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
#, python-format
msgid "Import"
msgstr "Importer"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Import d'un relevé bancaire"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line__unique_import_id
msgid "Import ID"
msgstr "ID d'importation"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import Statement"
msgstr "Importation d'un relevé"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__inbound_payment_method_line_ids
msgid "Inbound Payment Methods"
msgstr "Méthodes de paiement entrant"

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.install_more_import_formats_action
msgid "Install Import Format"
msgstr "Installer le Format d'Importation"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_name
msgid "It creates draft invoices and bills by sending an email."
msgstr "Il crée des brouillons de facture et de notes en envoyant un email."

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__journal_id
msgid "Journal"
msgstr "Journal"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
#, python-format
msgid "Journal Creation"
msgstr "Création d'un journal"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_import_journal_creation
msgid "Journal Creation on Bank Statement Import"
msgstr "Création d'un Journal sur l'Importation de Relevés Bancaires"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__journal_group_ids
msgid "Journal Groups"
msgstr "Groupes de journal"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__name
msgid "Journal Name"
msgstr "Nom du journal"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__json_activity_data
msgid "Json Activity Data"
msgstr "Données d'activité Json"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"Just click OK to create the account/journal and finish the upload. If this "
"was a mistake, hit cancel to abort the upload."
msgstr ""
"Cliquez sur OK pour créer le compte/journal et terminer le téléchargement. "
"Si ceci est une erreur, cliquez sur annuler pour arrêter le téléchargement."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__kanban_dashboard
msgid "Kanban Dashboard"
msgstr "Tableau de bord Kanban"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "Tableau de bord graphique Kanban"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import____last_update
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__write_uid
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import__write_date
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_user_id
msgid "Leave empty to assign the Salesperson of the invoice."
msgstr "Laisser vide pour assigner le Vendeur à la Facture."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__restrict_mode_hash_table
msgid "Lock Posted Entries with Hash"
msgstr "Verrouiller les pièces comptabilisées avec hachage"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__loss_account_id
msgid "Loss Account"
msgstr "Compte des pertes"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_manual_sequencing
msgid "Manual Numbering"
msgstr "Numérotation manuelle"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__inbound_payment_method_line_ids
msgid ""
"Manual: Get paid by any method outside of Odoo.\n"
"Payment Acquirers: Each payment acquirer has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"Manuel : Soyez payé par n'importe quelle méthode en dehors d'Odoo.\n"
"Acquéreurs de paiement : Chaque acquéreur de paiement a sa propre méthode de paiement. Demandez une transaction sur/vers une carte grâce à un jeton de paiement enregistré par le partenaire lors d'un achat ou d'un abonnement en ligne.\n"
"Dépôt par lot : Collectez plusieurs chèques de clients en une seule fois en générant et en soumettant un dépôt par lot à votre banque. Le module account_batch_payment est nécessaire.\n"
"Prélèvement SEPA : Soyez payé dans la zone SEPA grâce à un mandat que votre partenaire vous aura accordé. Le module account_sepa est nécessaire.\n"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__outbound_payment_method_line_ids
msgid ""
"Manual: Pay by any method outside of Odoo.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
msgstr ""
"Paiement manuel : payez par tout autre moyen externe à Odoo.\n"
"Chèque : payez par chèque en l'imprimant depuis Odoo.\n"
"Virement SEPA : payez en envoyant un fichier de virement SEPA à votre banque. Activez cette option dans les paramètres.\n"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_ids
msgid "Messages"
msgstr "Messages"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'Activité à Venir"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé d'activité suivante"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_next_number
msgid "Next Check Number"
msgstr "Numéro de chèque suivant"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "No currency found matching '%s'."
msgstr "Pas de devise trouvée correspondant à '%s'."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid "OK"
msgstr "OK"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__outbound_payment_method_line_ids
msgid "Outbound Payment Methods"
msgstr "Méthodes de paiement sortant"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__profit_account_id
msgid "Profit Account"
msgstr "Compte de profit"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur Responsable"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sale_activity_type_id
msgid "Schedule Activity"
msgstr "Planifier une activité"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__secure_sequence_id
msgid "Secure Sequence"
msgstr "Séquence Sécurisée"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type
msgid ""
"Select 'Sale' for customer invoices journals.\n"
"Select 'Purchase' for vendor bills journals.\n"
"Select 'Cash' or 'Bank' for journals that are used in customer or vendor payments.\n"
"Select 'General' for miscellaneous operations journals."
msgstr ""
"Sélectionnez 'Vente' pour les journaux des factures des clients.\n"
"Sélectionnez 'Achat' pour les journaux des factures des fournisseurs.\n"
"Sélectionnez 'Espèces' ou 'Banque' pour les journaux utilisés pour les paiements des clients ou des fournisseurs.\n"
"Sélectionnez 'Général' pour les journaux des opérations diverses."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Select Files"
msgstr "Sélectionner des fichiers"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__selected_payment_method_codes
msgid "Selected Payment Method Codes"
msgstr "Codes des modes de paiement sélectionnés"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "Envoyer des factures XML/EDI"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__alias_id
msgid ""
"Send one separate email for each invoice.\n"
"\n"
"Any file extension will be accepted.\n"
"\n"
"Only PDF and XML files will be interpreted by Odoo"
msgstr ""
"Envoyer un email distinct pour chaque facture.\n"
"\n"
"Toute extension de fichier sera acceptée.\n"
"\n"
"Seuls les fichiers PDF et XML seront interprétés par Odoo."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_override_regex
msgid "Sequence Override Regex"
msgstr "Regex d'override de la séquence "

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__check_next_number
msgid "Sequence number of the next printed check."
msgstr "Numéro de séquence du prochain chèque imprimé."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__secure_sequence_id
msgid "Sequence to use to ensure the securisation of data"
msgstr "Séquence à utiliser pour assurer la sécurisation des données"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__active
msgid "Set active to false to hide the Journal without removing it."
msgstr ""
"Mettre le champs actif à faux pour masquer le journal sans le supprimer."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__code
msgid "Short Code"
msgstr "Code"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__code
msgid ""
"Shorter name used for display. The journal entries of this journal will also"
" be named using this prefix by default."
msgstr ""
"Nom abrégé utilisé pour l'affichage. Les pièces comptables de ce journal "
"seront également nommées en utilisant ce préfixe par défaut."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__show_on_dashboard
msgid "Show journal on dashboard"
msgstr "Montrer le journal dans le tableau de bord"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__suspense_account_id
msgid "Suspense Account"
msgstr "Compte d'attente"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence_override_regex
msgid ""
"Technical field used to enforce complex sequence composition that the system would normally misunderstand.\n"
"This is a regex that can include all the following capture groups: prefix1, year, prefix2, month, prefix3, seq, suffix.\n"
"The prefix* groups are the separators between the year, month and the actual increasing sequence number (seq).\n"
"e.g: ^(?P<prefix1>.*?)(?P<year>\\d{4})(?P<prefix2>\\D*?)(?P<month>\\d{2})(?P<prefix3>\\D+?)(?P<seq>\\d+)(?P<suffix>\\D*?)$"
msgstr ""
"Champ technique utilisé pour appliquer une composition de séquences complexes que le système comprendrait normalement mal.\n"
"Il s'agit d'une expression régulière qui peut inclure tous les groupes de capture suivants : préfixe1, année, préfixe2, mois, préfixe3, seq, suffixe.\n"
"Les groupes de préfixes* sont les séparateurs entre l'année, le mois et le numéro de séquence croissant réel (seq).\n"
"\n"
"e.g: ^(?P<prefix1>.*?)(?P<year>\\d{4})(?P<prefix2>\\D*?)(?P<month>\\d{2})(?P<prefix3>\\D+?)(?P<seq>\\d+)(?P<suffix>\\D*?)$"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__selected_payment_method_codes
msgid "Technical field used to hide or show payment method options if needed."
msgstr ""
"Champ technique utilisé pour masquer ou afficher les options du mode de "
"paiement si nécessaire."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Code de pays ISO en deux caractères. \n"
"Vous pouvez utiliser ce champ pour une recherche rapide."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_journal_creation_view
msgid ""
"The account of the statement you are uploading is not yet recorded in Odoo. "
"In order to proceed with the upload, you need to create a bank journal for "
"this account."
msgstr ""
"Le compte du relevé que vous téléchargez n'est pas encore enregistré dans "
"Odoo. Afin de procéder au téléchargement, vous devez créer un journal de "
"banque pour ce compte."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"The account of this statement (%s) is not the same as the journal (%s)."
msgstr ""
"Le compte de ce relevé (%s) n'est pas le même que celui du journal (%s)."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid ""
"The currency of the bank statement (%s) is not the same as the currency of "
"the journal (%s)."
msgstr ""
"La devise du relevé bancaire (%s) n’est pas la même que celle du journal "
"(%s)."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__currency_id
msgid "The currency used to enter statement"
msgstr "La devise utilisée pour entrer les relevés"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "This file doesn't contain any statement for account %s."
msgstr "Ce fichier ne contient aucun relevé de le compte %s."

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "This file doesn't contain any transaction for account %s."
msgstr "Ce fichier ne contient aucune transaction pour le compte %s."

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__type
msgid "Type"
msgstr "Type"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: account_bank_statement_import
#: model:ir.actions.act_window,name:account_bank_statement_import.action_account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Upload"
msgstr "Charger"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "Upload Bank Statements"
msgstr "Importer des relevés bancaires"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__sequence
msgid "Used to order Journals in the dashboard view"
msgstr "Utilisé pour trier les journaux dans la vue du tableau de bord"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__loss_account_id
msgid ""
"Used to register a loss when the ending balance of a cash register differs "
"from what the system computes"
msgstr ""
"Utilisé pour enregistrer une perte lorsque le solde final de la caisse est "
"différent de ce qui a été calculé par le système"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__profit_account_id
msgid ""
"Used to register a profit when the ending balance of a cash register differs"
" from what the system computes"
msgstr ""
"Utilisé pour enregistrer un profit lorsque le solde final de la caisse est "
"différent de ce qui a été calculé par le système"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_import_journal_creation__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__show_on_dashboard
msgid "Whether this journal should be displayed on the dashboard or not"
msgstr "Si ce journal doit être affiché sur le tableau de bord ou non"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "You already have imported that file."
msgstr "Vous avez déjà importé ce fichier."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
"Vous pouvez choisir différents modèles par type de référence. Le modèle par "
"défaut est la référence Odoo."

#. module: account_bank_statement_import
#: model:ir.model.fields,help:account_bank_statement_import.field_account_bank_statement_import_journal_creation__invoice_reference_type
msgid ""
"You can set here the default communication that will appear on customer "
"invoices, once validated, to help the customer to refer to that particular "
"invoice when making the payment."
msgstr ""
"Vous pouvez définir la communication par défaut qui apparaitra sur les "
"factures du client, une fois celle-ci validée, pour aider le client à se "
"référer à cette facture en particulier lors du paiement."

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.account_bank_statement_import_view
msgid "You can upload your bank statement using:"
msgstr "Vous pouvez télécharger votre relevé bancaire en utilisant :"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "You have to set a Default Account for the journal: %s"
msgstr "Vous devez définir un compte par défaut pour le journal : %s"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "The following files could not be imported:\n"
msgstr "Impossible d'importer les dossiers suivants :\n"

#. module: account_bank_statement_import
#: code:addons/account_bank_statement_import/account_bank_statement_import.py:0
#, python-format
msgid "View successfully imported statements"
msgstr "Afficher les relevés importés avec succès"

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "or"
msgstr "ou"
