<?xml version="1.0" ?>
<odoo>
    <data>

        <record id="account_bank_statement_import_journal_creation_view" model="ir.ui.view">
            <field name="name">Journal Creation</field>
            <field name="model">account.bank.statement.import.journal.creation</field>
            <field name="arch" type="xml">
                <form string="Journal Creation">
                    <p>The account of the statement you are uploading is not yet recorded in Odoo. In order to proceed with the upload, you need to create a bank journal for this account.</p>
                    <p>Just click OK to create the account/journal and finish the upload. If this was a mistake, hit cancel to abort the upload.</p>
                    <group>
                        <group>
                            <field name="name" string="Bank Journal Name"/>
                            <field name="bank_acc_number" readonly="1"/>
                            <field name="bank_id"/>
                        </group>
                        <group>
                            <field name="currency_id" readonly="1" groups="base.group_multi_currency"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <footer>
                        <button name="create_journal" string="OK" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>
