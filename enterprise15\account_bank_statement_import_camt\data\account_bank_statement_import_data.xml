<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_bank_statement_import_camt" model="ir.ui.view">
        <field name="name">Import Bank Statements CAMT</field>
        <field name="model">account.bank.statement.import</field>
        <field name="inherit_id" ref="account_bank_statement_import.account_bank_statement_import_view"/>
        <field name="arch" type="xml">
            <xpath expr="//ul[@id='statement_format']" position="inside">
                <li>SEPA recommended Cash Management format (CAMT.053) <i class="fa fa-info-circle" aria-label="In case there are statements targeting multiple accounts, only those targeting the current account will be imported." title="In case there are statements targeting multiple accounts, only those targeting the current account will be imported."></i></li>
            </xpath>
        </field>
    </record>
</odoo>
