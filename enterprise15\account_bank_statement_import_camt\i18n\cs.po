# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_camt
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Concentration"
msgstr "Koncentrace ACH"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Corporate Trade"
msgstr "ACH Korporátní obchod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Credit"
msgstr "Kredit ACH"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Debit"
msgstr "Debet ACH"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Pre-Authorised"
msgstr "ACH předběžně autorizováno"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Return"
msgstr "ACH návrat"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Reversal"
msgstr "Zvrat ACH"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Settlement"
msgstr "Vypořádání ACH"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Transaction"
msgstr "Transakce ACH"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ARP Debit"
msgstr "ARP debet"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Balancing"
msgstr "Vyvažování účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Closing"
msgstr "Uzavření účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Management"
msgstr "Správa účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Opening"
msgstr "Otevření účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Transfer"
msgstr "Převod účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Info: %s"
msgstr "Doplňující informace: %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Miscellaneous Credit Operations"
msgstr "Další různé kreditní operace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Miscellaneous Debit Operations"
msgstr "Další různé debetní operace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Address:\n"
msgstr "Adresa:\n"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Adjustments"
msgstr "Úpravy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Automatic Transfer"
msgstr "Automatický přenos"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Back Value"
msgstr "Zpětná hodnota"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bank Cheque"
msgstr "Bankovní šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bank Fees"
msgstr "Bankovní poplatky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Blocked Transactions"
msgstr "Blokované transakce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bonus Issue/Capitalisation Issue"
msgstr "Bonusové vydání / kapitalizace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Borrowing fee"
msgstr "Výpůjční poplatek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Account Transfer"
msgstr "Převod pobočkového účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Deposit"
msgstr "Vklad pobočky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Withdrawal"
msgstr "Výběr pobočky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Brokerage fee"
msgstr "Makléřský poplatek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Buy Sell Back"
msgstr "Zpětný nákup"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "CSD Blocked Transactions"
msgstr "CSD Blokované transakce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Call on intermediate securities"
msgstr "Vyzvěte zprostředkující cenné papíry"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Capital Gains Distribution"
msgstr "Rozdělení kapitálových výnosů"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Deposit"
msgstr "Hotovostní vklad"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Dividend"
msgstr "Dividenda v hotovosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Letter"
msgstr "Hotovostní dopis"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Letter Adjustment"
msgstr "Úprava peněžního dopisu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Management"
msgstr "Správa hotovosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Pooling"
msgstr "Cash pooling"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Withdrawal"
msgstr "Výběr hotovosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash in lieu"
msgstr "Hotovost místo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Certified Customer Cheque"
msgstr "Certifikovaný zákaznický šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Charge/fees"
msgstr "Poplatek / poplatky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Charges"
msgstr "Poplatky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Check Number: %s"
msgstr "Zkontroluje číslo: %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque"
msgstr "Šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Deposit"
msgstr "Zkontrolujte vklad"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Reversal"
msgstr "Zkontrolujte obrácení"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Under Reserve"
msgstr "Zkontrolujte pod rezervou"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Circular Cheque"
msgstr "Kruhová kontrola"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Clean Collection"
msgstr "Čistý sběr"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Client Owned Collateral"
msgstr "Zajištění vlastněné klientem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Collateral Management"
msgstr "Vedlejší zajištění"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission"
msgstr "Komise"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission excluding taxes"
msgstr "Provize bez daní"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission including taxes"
msgstr "Provize včetně daní"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commodities"
msgstr "Komodity"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Compensation/Claims"
msgstr "Odškodnění / reklamace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Consumer Loans"
msgstr "Spotřebitelské půjčky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Controlled Disbursement"
msgstr "Kontrolované vyplácení"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Conversion"
msgstr "Konverze"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Action"
msgstr "Firemní akce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Own Account Transfer"
msgstr "Převod na vlastní účet společnosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Rebate"
msgstr "Firemní sleva"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate mark broker owned"
msgstr "Majitel korporátních značek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate mark client owned"
msgstr "Vlastník korporátní značky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Counter Party: %(partner)s"
msgstr "Protistrana: %(partner)s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Counter Transactions"
msgstr "Počítadlo transakcí"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Adjustment"
msgstr "Úprava úvěru"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Adjustments"
msgstr "Úpravy kreditu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Card Payment"
msgstr "Platba kreditní kartou"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Transfer with agreed Commercial Information"
msgstr "Převod kreditu se sjednanými obchodními informacemi"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross Trade"
msgstr "Cross trade"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border"
msgstr "Přeshraniční"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Cash Withdrawal"
msgstr "Přeshraniční výběr hotovosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Credit Card Payment"
msgstr "Přeshraniční platba kreditní kartou"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Credit Transfer"
msgstr "Přeshraniční převod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Direct Debit"
msgstr "Přeshraniční inkaso"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Intra Company Transfer"
msgstr "Přeshraniční vnitropodnikový převod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Payroll/Salary Payment"
msgstr "Přeshraniční výplaty / výplaty mezd"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Standing Order"
msgstr "Přeshraniční trvalý příkaz"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Crossed Cheque"
msgstr "Překřížený šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Custody"
msgstr "Péče"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Custody Collection"
msgstr "Opatrovnická sbírka"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Customer Card Transactions"
msgstr "Transakce se zákaznickými kartami"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Debit"
msgstr "Má dáti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Debit Adjustments"
msgstr "Úpravy inkasa"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Decrease in Value"
msgstr "Snížení hodnoty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Delivery"
msgstr "Dodání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Deposit"
msgstr "Vklad"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Deposit/Contribution"
msgstr "Vklad / příspěvek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Depositary Receipt Issue"
msgstr "Vydání depozitního dokladu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Derivatives"
msgstr "Deriváty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit"
msgstr "Inkaso"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit Payment"
msgstr "Platba inkasem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit under reserve"
msgstr "Inkaso v rezervě"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Discounted Draft"
msgstr "Zlevněný koncept"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dishonoured/Unpaid Draft"
msgstr "Znečištěný / nezaplacený koncept"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dividend Option"
msgstr "Možnost dividendy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dividend Reinvestment"
msgstr "Reinvestice dividend"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Documentary Collection"
msgstr "Dokumentární sbírka"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Documentary Credit"
msgstr "Dokumentární zápočet"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Domestic Credit Transfer"
msgstr "Tuzemský převod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Draft Maturity Change"
msgstr "Změna splatnosti konceptu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drafts/BillOfOrders"
msgstr "Koncepty / BillOfOrders"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drawdown"
msgstr "Čerpání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drawing"
msgstr "Výkres"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dutch Auction"
msgstr "Holandská aukce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "End to end ID: %s"
msgstr "End to end ID: %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Entry Info: %s"
msgstr "informace k záznamu: %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity Premium Reserve"
msgstr "Akciová prémiová rezerva"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity mark broker owned"
msgstr "Makléř značky akcií vlastní"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity mark client owned"
msgstr "Vlastník značky vlastního kapitálu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange"
msgstr "Výměna"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Rate Adjustment"
msgstr "Úprava směnného kurzu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded"
msgstr "Burza obchodována"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded CCP"
msgstr "CCP obchodovaná na burze"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded Non-CCP"
msgstr "Burza obchodovaná bez CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Extended Domain"
msgstr "Rozšířená doména"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "External Account Transfer"
msgstr "Přenos externího účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Factor Update"
msgstr "Aktualizace faktoru"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fees"
msgstr "Poplatky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fees, Commission , Taxes, Charges and Interest"
msgstr "Poplatky, provize, daně a úroky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Final Maturity"
msgstr "Konečná splatnost"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Final Payment"
msgstr "Konečná platba"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Credit Transfer"
msgstr "Převod finančních institucí"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Direct Debit Payment"
msgstr "Platba inkasem finanční instituce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Own Account Transfer"
msgstr "Převod na vlastní účet finanční instituce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Deposit Interest Amount"
msgstr "Pevná částka úroku z vkladu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Term Deposits"
msgstr "Vklady s pevným termínem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Term Loans"
msgstr "Půjčky na dobu určitou"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Float adjustment"
msgstr "Plovoucí nastavení"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Cheque"
msgstr "Zahraniční šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Cheque Under Reserve"
msgstr "Zahraniční šek v rezervě"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Currency Deposit"
msgstr "Vklad v cizí měně"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Currency Withdrawal"
msgstr "Výběr cizí měny"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Exchange"
msgstr "Směnárna"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards"
msgstr "Vpřed"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards broker owned collateral"
msgstr "Zajištění vlastněné zprostředkovateli"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards client owned collateral"
msgstr "Přeposílá klientovi kolaterál"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Freeze of funds"
msgstr "Zmrazení finančních prostředků"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Full Call / Early Redemption"
msgstr "Plné volání / předčasné uplatnění"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Future Variation Margin"
msgstr "Budoucí variační marže"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures"
msgstr "Futures"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures Commission"
msgstr "Futures provize"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures Residual Amount"
msgstr "Zbytková částka futures"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Guarantees"
msgstr "Záruky"

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Import Výpisu z bankovního účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Inspeci/Share Exchange"
msgstr "Inspeci / burza akcií"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Instruction ID: %s"
msgstr "ID instrukce (Instruction ID): %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest"
msgstr "Zájem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest Payment"
msgstr "Zajímavá platba"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest Payment with Principle"
msgstr "Platba úroků s principem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Internal Account Transfer"
msgstr "Interní převod účtu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Internal Book Transfer"
msgstr "Interní přenos knihy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Intra Company Transfer"
msgstr "Vnitropodnikový převod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Invoice Accepted with Differed Due Date"
msgstr "Faktura přijata s odlišným datem splatnosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Cash Concentration Transactions"
msgstr "Vydané transakce koncentrace hotovosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Cheques"
msgstr "Vydané šeky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Credit Transfers"
msgstr "Vydané kreditní převody"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Direct Debits"
msgstr "Vydaná inkasa"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Real Time Credit Transfer"
msgstr "Vydaný převod kreditu v reálném čase"

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_journal
msgid "Journal"
msgstr "Deník"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lack"
msgstr "Nedostatek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending Broker Owned Cash Collateral"
msgstr "Hotovostní kolaterál ve vlastnictví půjčovacího makléře"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending Client Owned Cash Collateral"
msgstr "Zapůjčení hotovostního kolaterálu ve vlastnictví klienta"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending income"
msgstr "Příjmy z půjček"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Liquidation Dividend / Liquidation Payment"
msgstr "Likvidační dividenda / likvidační platba"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Listed Derivatives – Futures"
msgstr "Uvedené deriváty - futures"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Listed Derivatives – Options"
msgstr "Uvedené deriváty - možnosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Loans, Deposits & Syndications"
msgstr "Půjčky, vklady a syndikace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lockbox Transactions"
msgstr "Lockbox transakce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Management Fees"
msgstr "Poplatky za správu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mandate ID: %s"
msgstr "ID mandanta (Mandate ID): %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Margin Payments"
msgstr "Maržové platby"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Margin client owned cash collateral"
msgstr "Maržový hotovostní kolaterál ve vlastnictví klienta"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Merchant Card Transactions"
msgstr "Transakce obchodních karet"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Merger"
msgstr "Fúze"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Credit Operations"
msgstr "Různé úvěrové operace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Debit Operations"
msgstr "Různé debetní operace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Deposit"
msgstr "Různé zálohy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Securities Operations"
msgstr "Různé operace s cennými papíry"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mixed Deposit"
msgstr "Smíšený vklad"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mortgage Loans"
msgstr "Hypoteční úvěry"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Netting"
msgstr "Síť"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"No exchange rate was found to convert an amount into the currency of the "
"journal"
msgstr "Nebyl nalezen žádný směnný kurz pro přepočet částky na měnu deníku"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Deliverable"
msgstr "Nelze dodat"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Settled"
msgstr "Nevyrovnaný"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Syndicated"
msgstr "Nesyndikované"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Taxable commissions"
msgstr "Nezdanitelné provize"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non-Presented Circular Cheque"
msgstr "Nepředložená kruhová kontrola"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Not available"
msgstr "Není k dispozici"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Notice Deposits"
msgstr "Všimněte si vkladů"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Notice Loans"
msgstr "Všimněte si půjček"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC"
msgstr "OTC"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC CCP"
msgstr "OTC CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Bonds"
msgstr "OTC deriváty - Dluhopisy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Credit Derivatives"
msgstr "OTC deriváty - kreditní deriváty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Equity"
msgstr "OTC deriváty - vlastní kapitál"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Interest Rates"
msgstr "OTC deriváty - úrokové sazby"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Structured Exotic Derivatives"
msgstr "OTC deriváty - strukturované exotické deriváty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Swaps"
msgstr "OTC deriváty - swapy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Non-CCP"
msgstr "OTC Non-CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Odd Lot Sale/Purchase"
msgstr "Zvláštní prodej / nákup"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "One-Off Direct Debit"
msgstr "Jednorázové inkaso"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Open Cheque"
msgstr "Otevřete šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Opening & Closing"
msgstr "Otevírání a zavírání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Option broker owned collateral"
msgstr "Zajištění vlastněné opčními makléři"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Option client owned collateral"
msgstr "Možnost zajištění vlastněné klientem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Options"
msgstr "Možnosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Order Cheque"
msgstr "Kontrola objednávky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Other"
msgstr "jiný"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Overdraft"
msgstr "Přečerpání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Overdraft Charge"
msgstr "Poplatek za přečerpání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Pair-Off"
msgstr "Párování"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Payment"
msgstr "Částečná platba"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Redemption Without Reduction of Nominal Value"
msgstr "Částečný odkup bez snížení nominální hodnoty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Redemption with reduction of nominal value"
msgstr "Částečné vykoupení se snížením nominální hodnoty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Payments"
msgstr "Platby"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Payroll/Salary Payment"
msgstr "Výplaty / výplaty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Placement"
msgstr "Umístění"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"Please check the currency on your bank journal.\n"
"No statements in currency %s were found in this CAMT file."
msgstr ""
"Prosím zkontrolujte měnu bankovního deníku.\n"
"V tomto souboru CAMT nebyl nalezen žádný výpis v měně %s."

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"Please set the IBAN account on your bank journal.\n"
"\n"
"This CAMT file is targeting several IBAN accounts but none match the current journal."
msgstr ""
"Nastavte prosím účet IBAN ve svém bankovním deníku.\n"
"\n"
"Tento soubor CAMT cílí na několik účtů IBAN, ale žádný neodpovídá aktuálnímu deníku."

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Point-of-Sale (POS) Payment"
msgstr "Platba v místě prodeje (POS)"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Point-of-Sale (POS) Payment - Debit Card"
msgstr "Platba v místě prodeje (POS) - debetní karta"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Portfolio Move"
msgstr "Přesun portfolia"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Posting Error"
msgstr "Chyba zveřejňování"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Pre-Authorised Direct Debit"
msgstr "Předautorizovaný inkaso"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Precious Metal"
msgstr "Drahé kovy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Principal Pay-down/pay-up"
msgstr "Splátka jistiny / splátka"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Principal Payment"
msgstr "Splátka jistiny"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Priority Credit Transfer"
msgstr "Přednostní převod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Priority Issue"
msgstr "Prioritní vydání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Put Redemption"
msgstr "Dejte vykoupení"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Cash Concentration Transactions"
msgstr "Přijaté transakce koncentrace hotovosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Cheques"
msgstr "Přijaté šeky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Credit Transfers"
msgstr "Přijaté převody"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Direct Debits"
msgstr "Přijaté inkasa"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Real Time Credit Transfer"
msgstr "Přijatý převod v reálném čase"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption"
msgstr "Vyplacení"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption Asset Allocation"
msgstr "Uplatnění alokace aktiv"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption Withdrawing Plan"
msgstr "Plán stažení výplaty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reimbursements"
msgstr "Úhrady"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Renewal"
msgstr "Obnovení"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repayment"
msgstr "Splátky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repo"
msgstr "Repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repurchase offer/Issuer Bid/Reverse Rights."
msgstr "Nabídka zpětného odkupu / nabídka emitenta / práva na obrácení."

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reset Payment"
msgstr "Resetovat platbu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Cancellation Request"
msgstr "Storno kvůli žádosti o zrušení platby"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Return/reimbursement of a Credit Transfer"
msgstr "Storno z důvodu vrácení platby / vrácení kreditu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Reversal"
msgstr "Storno kvůli zrušení platby"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Return/Unpaid Direct Debit"
msgstr "Storno z důvodu vrácení / nezaplacení inkasa"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to a Payment Cancellation Request"
msgstr "Zrušení kvůli žádosti o zrušení platby"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reverse Repo"
msgstr "Reverzní repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Rights Issue/Subscription Rights/Rights Offer"
msgstr "Vydání práv / Práva na předplatné / Nabídka práv"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA B2B Direct Debit"
msgstr "SEPA B2B inkaso"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA Core Direct Debit"
msgstr "Základní inkaso SEPA"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA Credit Transfer"
msgstr "SEPA Credit Transfer"

#. module: account_bank_statement_import_camt
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_camt.account_bank_statement_import_camt
msgid ""
"SEPA recommended Cash Management format (CAMT.053) <i class=\"fa fa-info-"
"circle\" aria-label=\"In case there are statements targeting multiple "
"accounts, only those targeting the current account will be imported.\" "
"title=\"In case there are statements targeting multiple accounts, only those"
" targeting the current account will be imported.\"/>"
msgstr ""
"Doporučený formát správy hotovosti SEPA (CAMT.053) <i class=\"fa fa-info-"
"circle\" aria-label=\"In case there are statements targeting multiple "
"accounts, only those targeting the current account will be imported.\" "
"title=\"In case there are statements targeting multiple accounts, only those"
" targeting the current account will be imported.\"/>"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Same Day Value Credit Transfer"
msgstr "Převod hodnoty ve stejný den"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities"
msgstr "Cenné papíry"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities Borrowing"
msgstr "Půjčky cenných papírů"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities Lending"
msgstr "Půjčování cenných papírů"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sell Buy Back"
msgstr "Prodat zpět"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement"
msgstr "Vyrovnání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement after collection"
msgstr "Vypořádání po vyzvednutí"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement against bank guarantee"
msgstr "Vypořádání proti bankovní záruce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement at Maturity"
msgstr "Vypořádání při splatnosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement of Sight Export document"
msgstr "Zúčtování vývozního dokladu zraku"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement of Sight Import document"
msgstr "Zúčtování dovozního dokladu vyhlídky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement under reserve"
msgstr "Vypořádání v rezervě"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Smart-Card Payment"
msgstr "Platba čipovou kartou"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Spots"
msgstr "Spots"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Stamp duty"
msgstr "Kolkovné"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Stand-By Letter Of Credit"
msgstr "Stand-by akreditiv"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Standing Order"
msgstr "Trvalý příkaz"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription"
msgstr "Předplatné"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription Asset Allocation"
msgstr "Alokace aktiv předplatného"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription Savings Plan"
msgstr "Plán úspor předplatného"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swap Payment"
msgstr "Swapová platba"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swap broker owned collateral"
msgstr "Zajištění vlastněné makléřem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swaps"
msgstr "Swapy"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sweep"
msgstr "Zametat"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sweeping"
msgstr "Kompletní"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Switch"
msgstr "Přepínač"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Syndicated"
msgstr "Syndikovaný"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Syndications"
msgstr "Syndikace"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "TBA closing"
msgstr "Uzavření TBA"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Tax Reclaim"
msgstr "Navrácení daně"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Taxes"
msgstr "Daně"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Tender"
msgstr "Nabídka"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Topping"
msgstr "Výborný"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade"
msgstr "Obchod"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade Services"
msgstr "Služby obchodu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade, Clearing and Settlement"
msgstr "Obchod, zúčtování a vypořádání"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transaction Fees"
msgstr "Transakční poplatky"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transaction ID: %s"
msgstr "ID tranksace (Transaction ID): %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transfer In"
msgstr "Přenést dovnitř"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transfer Out"
msgstr "Přeneste"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Travellers Cheques Deposit"
msgstr "Cestující kontrolují vklad"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Travellers Cheques Withdrawal"
msgstr "Cestující kontrolují výběr"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Treasury Tax And Loan Service"
msgstr "Daňová služba a půjčka"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Triparty Repo"
msgstr "Triparty repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Triparty Reverse Repo"
msgstr "Triparty reverzní repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Turnaround"
msgstr "Otočit se"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Underwriting Commission"
msgstr "Upisovací provize"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Card Transaction"
msgstr "Transakce nezaplacené kartou"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Cheque"
msgstr "Nezaplacený šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Foreign Cheque"
msgstr "Nevyplacený zahraniční šek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Upfront Payment"
msgstr "Platba předem"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Value Date"
msgstr "Datum valuty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Warrant Exercise/Warrant Conversion"
msgstr "Cvičení rozkazu / převod rozkazu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Withdrawal/distribution"
msgstr "Odstoupení / distribuce"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Withholding Tax"
msgstr "Srážková daň"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "YTD Adjustment"
msgstr "Úprava YTD"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Zero Balancing"
msgstr "Nulové vyvážení"
