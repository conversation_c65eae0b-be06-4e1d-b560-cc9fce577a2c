# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_camt
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# J<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Katja <PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Concentration"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Corporate Trade"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Credit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Pre-Authorised"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Return"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Reversal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Settlement"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Transaction"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ARP Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Balancing"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Closing"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Management"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Opening"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Info: %s"
msgstr "Dodatne informacije: %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Miscellaneous Credit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Miscellaneous Debit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Address:\n"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Adjustments"
msgstr "Prilagoditve"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Automatic Transfer"
msgstr "Avtomatski prenos"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Back Value"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bank Cheque"
msgstr "Bančni ček"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bank Fees"
msgstr "Bančne preovizije"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Blocked Transactions"
msgstr "Blokirane transakcije"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bonus Issue/Capitalisation Issue"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Borrowing fee"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Brokerage fee"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Buy Sell Back"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "CSD Blocked Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Call on intermediate securities"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Capital Gains Distribution"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Deposit"
msgstr "Gotovinski polog"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Dividend"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Letter"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Letter Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Management"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Pooling"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash in lieu"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Certified Customer Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Charge/fees"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Charges"
msgstr "Stroški"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Check Number: %s"
msgstr "Številka čeka: %s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque"
msgstr "Ček"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Reversal"
msgstr "Storniranje čeka"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Under Reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Circular Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Clean Collection"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Client Owned Collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Collateral Management"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission"
msgstr "Komisija"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission excluding taxes"
msgstr "Provizija brez davkov"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission including taxes"
msgstr "Provizija z davki"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commodities"
msgstr "Blago"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Compensation/Claims"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Consumer Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Controlled Disbursement"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Conversion"
msgstr "Pretvorba"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Action"
msgstr "Ukrepi podjetja"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Own Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Rebate"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate mark broker owned"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate mark client owned"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Counter Party: %(partner)s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Counter Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Adjustments"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Card Payment"
msgstr "Plačilo s kreditnimi karticami"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Transfer with agreed Commercial Information"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross Trade"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Cash Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Credit Card Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Intra Company Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Payroll/Salary Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Standing Order"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Crossed Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Custody"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Custody Collection"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Customer Card Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Debit"
msgstr "Debet"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Debit Adjustments"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Decrease in Value"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Delivery"
msgstr "Dostava"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Deposit"
msgstr "Depozit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Deposit/Contribution"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Depositary Receipt Issue"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Derivatives"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit under reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Discounted Draft"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dishonoured/Unpaid Draft"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dividend Option"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dividend Reinvestment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Documentary Collection"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Documentary Credit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Domestic Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Draft Maturity Change"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drafts/BillOfOrders"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drawdown"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drawing"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dutch Auction"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "End to end ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Entry Info: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity Premium Reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity mark broker owned"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity mark client owned"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Rate Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded Non-CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Extended Domain"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "External Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Factor Update"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fees"
msgstr "Pristojbine"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fees, Commission , Taxes, Charges and Interest"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Final Maturity"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Final Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Direct Debit Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Own Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Deposit Interest Amount"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Term Deposits"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Term Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Float adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Cheque Under Reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Currency Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Currency Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Exchange"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards broker owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards client owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Freeze of funds"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Full Call / Early Redemption"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Future Variation Margin"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures Commission"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures Residual Amount"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Guarantees"
msgstr ""

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Uvozi bančni izpisek"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Inspeci/Share Exchange"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Instruction ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest Payment with Principle"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Internal Account Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Internal Book Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Intra Company Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Invoice Accepted with Differed Due Date"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Cash Concentration Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Cheques"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Credit Transfers"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Direct Debits"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Real Time Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_journal
msgid "Journal"
msgstr "Dnevnik"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lack"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending Broker Owned Cash Collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending Client Owned Cash Collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending income"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Liquidation Dividend / Liquidation Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Listed Derivatives – Futures"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Listed Derivatives – Options"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Loans, Deposits & Syndications"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lockbox Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Management Fees"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mandate ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Margin Payments"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Margin client owned cash collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Merchant Card Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Merger"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Credit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Debit Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Securities Operations"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mixed Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mortgage Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Netting"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"No exchange rate was found to convert an amount into the currency of the "
"journal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Deliverable"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Settled"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Syndicated"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Taxable commissions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non-Presented Circular Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Not available"
msgstr "Ni na voljo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Notice Deposits"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Notice Loans"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Bonds"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Credit Derivatives"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Equity"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Interest Rates"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Structured Exotic Derivatives"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Swaps"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Non-CCP"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Odd Lot Sale/Purchase"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "One-Off Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Open Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Opening & Closing"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Option broker owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Option client owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Options"
msgstr "Možnosti"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Order Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Other"
msgstr "Drugo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Overdraft"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Overdraft Charge"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Pair-Off"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Redemption Without Reduction of Nominal Value"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Redemption with reduction of nominal value"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Payments"
msgstr "Plačila"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Payroll/Salary Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Placement"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"Please check the currency on your bank journal.\n"
"No statements in currency %s were found in this CAMT file."
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"Please set the IBAN account on your bank journal.\n"
"\n"
"This CAMT file is targeting several IBAN accounts but none match the current journal."
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Point-of-Sale (POS) Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Point-of-Sale (POS) Payment - Debit Card"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Portfolio Move"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Posting Error"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Pre-Authorised Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Precious Metal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Principal Pay-down/pay-up"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Principal Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Priority Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Priority Issue"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Put Redemption"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Cash Concentration Transactions"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Cheques"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Credit Transfers"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Direct Debits"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Real Time Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption Asset Allocation"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption Withdrawing Plan"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reimbursements"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Renewal"
msgstr "Obnovitev"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repayment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repurchase offer/Issuer Bid/Reverse Rights."
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reset Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Cancellation Request"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Return/reimbursement of a Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Reversal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Return/Unpaid Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to a Payment Cancellation Request"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reverse Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Rights Issue/Subscription Rights/Rights Offer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA B2B Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA Core Direct Debit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA Credit Transfer"
msgstr "SEPA nakazilo"

#. module: account_bank_statement_import_camt
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_camt.account_bank_statement_import_camt
msgid ""
"SEPA recommended Cash Management format (CAMT.053) <i class=\"fa fa-info-"
"circle\" aria-label=\"In case there are statements targeting multiple "
"accounts, only those targeting the current account will be imported.\" "
"title=\"In case there are statements targeting multiple accounts, only those"
" targeting the current account will be imported.\"/>"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Same Day Value Credit Transfer"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities Borrowing"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities Lending"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sell Buy Back"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement after collection"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement against bank guarantee"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement at Maturity"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement of Sight Export document"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement of Sight Import document"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement under reserve"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Smart-Card Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Spots"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Stamp duty"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Stand-By Letter Of Credit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Standing Order"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription"
msgstr "Naročnina"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription Asset Allocation"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription Savings Plan"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swap Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swap broker owned collateral"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swaps"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sweep"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sweeping"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Switch"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Syndicated"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Syndications"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "TBA closing"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Tax Reclaim"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Taxes"
msgstr "Davki"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Tender"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Topping"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade Services"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade, Clearing and Settlement"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transaction Fees"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transaction ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transfer In"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transfer Out"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Travellers Cheques Deposit"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Travellers Cheques Withdrawal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Treasury Tax And Loan Service"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Triparty Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Triparty Reverse Repo"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Turnaround"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Underwriting Commission"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Card Transaction"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Foreign Cheque"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Upfront Payment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Value Date"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Warrant Exercise/Warrant Conversion"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Withdrawal/distribution"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Withholding Tax"
msgstr "Davčni odtegljaj"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "YTD Adjustment"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Zero Balancing"
msgstr ""
