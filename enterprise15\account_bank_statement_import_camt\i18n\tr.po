# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_camt
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Nadir <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Nadir <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Concentration"
msgstr "ACH Concentration"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Corporate Trade"
msgstr "ACH Corporate Trade"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Credit"
msgstr "ACH Credit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Debit"
msgstr "ACH Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Pre-Authorised"
msgstr "ACH Pre-Authorised"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Return"
msgstr "ACH Return"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Reversal"
msgstr "ACH Reversal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Settlement"
msgstr "ACH Settlement"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ACH Transaction"
msgstr "ACH Transaction"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "ARP Debit"
msgstr "ARP Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Balancing"
msgstr "Account Balancing"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Closing"
msgstr "Account Closing"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Management"
msgstr "Account Management"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Opening"
msgstr "Account Opening"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Account Transfer"
msgstr "Account Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Info: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Miscellaneous Credit Operations"
msgstr "Additional Miscellaneous Credit Operations"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Additional Miscellaneous Debit Operations"
msgstr "Additional Miscellaneous Debit Operations"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Address:\n"
msgstr "Address:\n"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Adjustments"
msgstr "Adjustments"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Automatic Transfer"
msgstr "Automatic Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Back Value"
msgstr "Back Value"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bank Cheque"
msgstr "Bank Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bank Fees"
msgstr "Bank Fees"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Blocked Transactions"
msgstr "Blocked Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Bonus Issue/Capitalisation Issue"
msgstr "Bonus Issue/Capitalisation Issue"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Borrowing fee"
msgstr "Borrowing fee"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Account Transfer"
msgstr "Branch Account Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Deposit"
msgstr "Branch Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Branch Withdrawal"
msgstr "Branch Withdrawal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Brokerage fee"
msgstr "Brokerage fee"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Buy Sell Back"
msgstr "Buy Sell Back"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "CSD Blocked Transactions"
msgstr "CSD Blocked Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Call on intermediate securities"
msgstr "Call on intermediate securities"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Capital Gains Distribution"
msgstr "Capital Gains Distribution"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Deposit"
msgstr "Cash Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Dividend"
msgstr "Cash Dividend"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Letter"
msgstr "Cash Letter"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Letter Adjustment"
msgstr "Cash Letter Adjustment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Management"
msgstr "Cash Management"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Pooling"
msgstr "Cash Pooling"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash Withdrawal"
msgstr "Cash Withdrawal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cash in lieu"
msgstr "Cash in lieu"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Certified Customer Cheque"
msgstr "Certified Customer Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Charge/fees"
msgstr "Charge/fees"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Charges"
msgstr "Charges"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Check Number: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque"
msgstr "Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Deposit"
msgstr "Cheque Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Reversal"
msgstr "Cheque Reversal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cheque Under Reserve"
msgstr "Cheque Under Reserve"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Circular Cheque"
msgstr "Circular Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Clean Collection"
msgstr "Clean Collection"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Client Owned Collateral"
msgstr "Client Owned Collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Collateral Management"
msgstr "Collateral Management"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission"
msgstr "Commission"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission excluding taxes"
msgstr "Commission excluding taxes"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commission including taxes"
msgstr "Commission including taxes"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Commodities"
msgstr "Commodities"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Compensation/Claims"
msgstr "Compensation/Claims"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Consumer Loans"
msgstr "Consumer Loans"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Controlled Disbursement"
msgstr "Controlled Disbursement"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Conversion"
msgstr "Çevrim"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Action"
msgstr "Corporate Action"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Own Account Transfer"
msgstr "Corporate Own Account Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate Rebate"
msgstr "Corporate Rebate"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate mark broker owned"
msgstr "Corporate mark broker owned"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Corporate mark client owned"
msgstr "Corporate mark client owned"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Counter Party: %(partner)s"
msgstr "Counter Party: %(partner)s"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Counter Transactions"
msgstr "Counter Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Adjustment"
msgstr "Credit Adjustment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Adjustments"
msgstr "Credit Adjustments"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Card Payment"
msgstr "Credit Card Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Credit Transfer with agreed Commercial Information"
msgstr "Credit Transfer with agreed Commercial Information"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross Trade"
msgstr "Cross Trade"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border"
msgstr "Cross-Border"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Cash Withdrawal"
msgstr "Cross-Border Cash Withdrawal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Credit Card Payment"
msgstr "Cross-Border Credit Card Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Credit Transfer"
msgstr "Cross-Border Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Direct Debit"
msgstr "Cross-Border Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Intra Company Transfer"
msgstr "Cross-Border Intra Company Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Payroll/Salary Payment"
msgstr "Cross-Border Payroll/Salary Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Cross-Border Standing Order"
msgstr "Cross-Border Standing Order"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Crossed Cheque"
msgstr "Crossed Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Custody"
msgstr "Custody"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Custody Collection"
msgstr "Custody Collection"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Customer Card Transactions"
msgstr "Customer Card Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Debit"
msgstr "Borç"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Debit Adjustments"
msgstr "Debit Adjustments"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Decrease in Value"
msgstr "Decrease in Value"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Delivery"
msgstr "Teslimat"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Deposit"
msgstr "Depozito"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Deposit/Contribution"
msgstr "Deposit/Contribution"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Depositary Receipt Issue"
msgstr "Depositary Receipt Issue"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Derivatives"
msgstr "Derivatives"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit"
msgstr "Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit Payment"
msgstr "Direct Debit Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Direct Debit under reserve"
msgstr "Direct Debit under reserve"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Discounted Draft"
msgstr "Discounted Draft"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dishonoured/Unpaid Draft"
msgstr "Dishonoured/Unpaid Draft"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dividend Option"
msgstr "Dividend Option"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dividend Reinvestment"
msgstr "Dividend Reinvestment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Documentary Collection"
msgstr "Documentary Collection"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Documentary Credit"
msgstr "Documentary Credit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Domestic Credit Transfer"
msgstr "Domestic Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Draft Maturity Change"
msgstr "Draft Maturity Change"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drafts/BillOfOrders"
msgstr "Drafts/BillOfOrders"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drawdown"
msgstr "Drawdown"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Drawing"
msgstr "Drawing"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Dutch Auction"
msgstr "Dutch Auction"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "End to end ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Entry Info: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity Premium Reserve"
msgstr "Equity Premium Reserve"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity mark broker owned"
msgstr "Equity mark broker owned"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Equity mark client owned"
msgstr "Equity mark client owned"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange"
msgstr "Exchange"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Rate Adjustment"
msgstr "Exchange Rate Adjustment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded"
msgstr "Exchange Traded"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded CCP"
msgstr "Exchange Traded CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Exchange Traded Non-CCP"
msgstr "Exchange Traded Non-CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Extended Domain"
msgstr "Extended Domain"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "External Account Transfer"
msgstr "External Account Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Factor Update"
msgstr "Factor Update"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fees"
msgstr "Ücret"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fees, Commission , Taxes, Charges and Interest"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Final Maturity"
msgstr "Final Maturity"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Final Payment"
msgstr "Final Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Credit Transfer"
msgstr "Financial Institution Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Direct Debit Payment"
msgstr "Financial Institution Direct Debit Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Financial Institution Own Account Transfer"
msgstr "Financial Institution Own Account Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Deposit Interest Amount"
msgstr "Fixed Deposit Interest Amount"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Term Deposits"
msgstr "Fixed Term Deposits"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Fixed Term Loans"
msgstr "Fixed Term Loans"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Float adjustment"
msgstr "Float adjustment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Cheque"
msgstr "Foreign Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Cheque Under Reserve"
msgstr "Foreign Cheque Under Reserve"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Currency Deposit"
msgstr "Foreign Currency Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Currency Withdrawal"
msgstr "Foreign Currency Withdrawal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Foreign Exchange"
msgstr "Foreign Exchange"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards"
msgstr "Forwards"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards broker owned collateral"
msgstr "Forwards broker owned collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Forwards client owned collateral"
msgstr "Forwards client owned collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Freeze of funds"
msgstr "Freeze of funds"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Full Call / Early Redemption"
msgstr "Full Call / Early Redemption"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Future Variation Margin"
msgstr "Future Variation Margin"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures"
msgstr "Futures"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures Commission"
msgstr "Futures Commission"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Futures Residual Amount"
msgstr "Futures Residual Amount"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Guarantees"
msgstr "Guarantees"

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Banka Hesap Ekstresi İçe Aktar"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Inspeci/Share Exchange"
msgstr "Inspeci/Share Exchange"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Instruction ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest"
msgstr "Interest"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest Payment"
msgstr "Interest Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Interest Payment with Principle"
msgstr "Interest Payment with Principle"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Internal Account Transfer"
msgstr "Internal Account Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Internal Book Transfer"
msgstr "Internal Book Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Intra Company Transfer"
msgstr "Intra Company Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Invoice Accepted with Differed Due Date"
msgstr "Invoice Accepted with Differed Due Date"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Cash Concentration Transactions"
msgstr "Issued Cash Concentration Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Cheques"
msgstr "Issued Cheques"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Credit Transfers"
msgstr "Issued Credit Transfers"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Direct Debits"
msgstr "Issued Direct Debits"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Issued Real Time Credit Transfer"
msgstr "Issued Real Time Credit Transfer"

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_journal
msgid "Journal"
msgstr "Yevmiye"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lack"
msgstr "Lack"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending Broker Owned Cash Collateral"
msgstr "Lending Broker Owned Cash Collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending Client Owned Cash Collateral"
msgstr "Lending Client Owned Cash Collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lending income"
msgstr "Lending income"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Liquidation Dividend / Liquidation Payment"
msgstr "Liquidation Dividend / Liquidation Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Listed Derivatives – Futures"
msgstr "Listed Derivatives – Futures"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Listed Derivatives – Options"
msgstr "Listed Derivatives – Options"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Loans, Deposits & Syndications"
msgstr "Loans, Deposits & Syndications"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Lockbox Transactions"
msgstr "Lockbox Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Management Fees"
msgstr "Management Fees"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mandate ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Margin Payments"
msgstr "Margin Payments"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Margin client owned cash collateral"
msgstr "Margin client owned cash collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Merchant Card Transactions"
msgstr "Merchant Card Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Merger"
msgstr "Merger"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Credit Operations"
msgstr "Miscellaneous Credit Operations"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Debit Operations"
msgstr "Miscellaneous Debit Operations"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Deposit"
msgstr "Miscellaneous Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Miscellaneous Securities Operations"
msgstr "Miscellaneous Securities Operations"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mixed Deposit"
msgstr "Mixed Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Mortgage Loans"
msgstr "Mortgage Loans"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Netting"
msgstr "Netting"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"No exchange rate was found to convert an amount into the currency of the "
"journal"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Deliverable"
msgstr "Non Deliverable"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Settled"
msgstr "Non Settled"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Syndicated"
msgstr "Non Syndicated"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non Taxable commissions"
msgstr "Non Taxable commissions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Non-Presented Circular Cheque"
msgstr "Non-Presented Circular Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Not available"
msgstr "Mevcut değil"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Notice Deposits"
msgstr "Notice Deposits"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Notice Loans"
msgstr "Notice Loans"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC"
msgstr "OTC"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC CCP"
msgstr "OTC CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Bonds"
msgstr "OTC Derivatives – Bonds"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Credit Derivatives"
msgstr "OTC Derivatives – Credit Derivatives"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Equity"
msgstr "OTC Derivatives – Equity"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Interest Rates"
msgstr "OTC Derivatives – Interest Rates"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Structured Exotic Derivatives"
msgstr "OTC Derivatives – Structured Exotic Derivatives"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Derivatives – Swaps"
msgstr "OTC Derivatives – Swaps"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "OTC Non-CCP"
msgstr "OTC Non-CCP"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Odd Lot Sale/Purchase"
msgstr "Odd Lot Sale/Purchase"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "One-Off Direct Debit"
msgstr "One-Off Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Open Cheque"
msgstr "Open Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Opening & Closing"
msgstr "Opening & Closing"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Option broker owned collateral"
msgstr "Option broker owned collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Option client owned collateral"
msgstr "Option client owned collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Options"
msgstr "Seçenekler"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Order Cheque"
msgstr "Order Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Other"
msgstr "Diğer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Overdraft"
msgstr "Overdraft"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Overdraft Charge"
msgstr "Overdraft Charge"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Pair-Off"
msgstr "Pair-Off"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Payment"
msgstr "Partial Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Redemption Without Reduction of Nominal Value"
msgstr "Partial Redemption Without Reduction of Nominal Value"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Partial Redemption with reduction of nominal value"
msgstr "Partial Redemption with reduction of nominal value"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Payments"
msgstr "Ödemeler"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Payroll/Salary Payment"
msgstr "Payroll/Salary Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Placement"
msgstr "Placement"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid ""
"Please set the IBAN account on your bank journal.\n"
"\n"
"This CAMT file is targeting several IBAN accounts but none match the current journal."
msgstr ""
"Please set the IBAN account on your bank journal.\n"
"\n"
"This CAMT file is targeting several IBAN accounts but none match the current journal."

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Point-of-Sale (POS) Payment"
msgstr "Point-of-Sale (POS) Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Point-of-Sale (POS) Payment - Debit Card"
msgstr "Point-of-Sale (POS) Payment - Debit Card"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Portfolio Move"
msgstr "Portfolio Move"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Posting Error"
msgstr "Posting Error"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Pre-Authorised Direct Debit"
msgstr "Pre-Authorised Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Precious Metal"
msgstr "Precious Metal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Principal Pay-down/pay-up"
msgstr "Principal Pay-down/pay-up"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Principal Payment"
msgstr "Principal Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Priority Credit Transfer"
msgstr "Priority Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Priority Issue"
msgstr "Priority Issue"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Put Redemption"
msgstr "Put Redemption"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Cash Concentration Transactions"
msgstr "Received Cash Concentration Transactions"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Cheques"
msgstr "Received Cheques"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Credit Transfers"
msgstr "Received Credit Transfers"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Direct Debits"
msgstr "Received Direct Debits"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Received Real Time Credit Transfer"
msgstr "Received Real Time Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption"
msgstr "Redemption"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption Asset Allocation"
msgstr "Redemption Asset Allocation"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Redemption Withdrawing Plan"
msgstr "Redemption Withdrawing Plan"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reimbursements"
msgstr "Reimbursements"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Renewal"
msgstr "Yenileme"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repayment"
msgstr "Repayment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repo"
msgstr "Repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Repurchase offer/Issuer Bid/Reverse Rights."
msgstr "Repurchase offer/Issuer Bid/Reverse Rights."

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reset Payment"
msgstr "Reset Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Cancellation Request"
msgstr "Reversal due to Payment Cancellation Request"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Return/reimbursement of a Credit Transfer"
msgstr "Reversal due to Payment Return/reimbursement of a Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Payment Reversal"
msgstr "Reversal due to Payment Reversal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to Return/Unpaid Direct Debit"
msgstr "Reversal due to Return/Unpaid Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reversal due to a Payment Cancellation Request"
msgstr "Reversal due to a Payment Cancellation Request"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Reverse Repo"
msgstr "Reverse Repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Rights Issue/Subscription Rights/Rights Offer"
msgstr "Rights Issue/Subscription Rights/Rights Offer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA B2B Direct Debit"
msgstr "SEPA B2B Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA Core Direct Debit"
msgstr "SEPA Core Direct Debit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "SEPA Credit Transfer"
msgstr "SEPA Alacak Transferi"

#. module: account_bank_statement_import_camt
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_camt.account_bank_statement_import_camt
msgid ""
"SEPA recommended Cash Management format (CAMT.053) <i class=\"fa fa-info-"
"circle\" aria-label=\"In case there are statements targeting multiple "
"accounts, only those targeting the current account will be imported.\" "
"title=\"In case there are statements targeting multiple accounts, only those"
" targeting the current account will be imported.\"/>"
msgstr ""
"SEPA recommended Cash Management format (CAMT.053) <i class=\"fa fa-info-"
"circle\" aria-label=\"In case there are statements targeting multiple "
"accounts, only those targeting the current account will be imported.\" "
"title=\"In case there are statements targeting multiple accounts, only those"
" targeting the current account will be imported.\"/>"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Same Day Value Credit Transfer"
msgstr "Same Day Value Credit Transfer"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities"
msgstr "Securities"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities Borrowing"
msgstr "Securities Borrowing"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Securities Lending"
msgstr "Securities Lending"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sell Buy Back"
msgstr "Sell Buy Back"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement"
msgstr "Settlement"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement after collection"
msgstr "Settlement after collection"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement against bank guarantee"
msgstr "Settlement against bank guarantee"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement at Maturity"
msgstr "Settlement at Maturity"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement of Sight Export document"
msgstr "Settlement of Sight Export document"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement of Sight Import document"
msgstr "Settlement of Sight Import document"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Settlement under reserve"
msgstr "Settlement under reserve"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Smart-Card Payment"
msgstr "Smart-Card Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Spots"
msgstr "Spots"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Stamp duty"
msgstr "Stamp duty"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Stand-By Letter Of Credit"
msgstr "Stand-By Letter Of Credit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Standing Order"
msgstr "Standing Order"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription"
msgstr "Abonelik"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription Asset Allocation"
msgstr "Subscription Asset Allocation"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Subscription Savings Plan"
msgstr "Subscription Savings Plan"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swap Payment"
msgstr "Swap Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swap broker owned collateral"
msgstr "Swap broker owned collateral"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Swaps"
msgstr "Swaps"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sweep"
msgstr "Sweep"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Sweeping"
msgstr "Sweeping"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Switch"
msgstr "Switch"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Syndicated"
msgstr "Syndicated"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Syndications"
msgstr "Syndications"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "TBA closing"
msgstr "TBA closing"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Tax Reclaim"
msgstr "Tax Reclaim"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Taxes"
msgstr "Vergiler"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Tender"
msgstr "Tender"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Topping"
msgstr "Topping"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade"
msgstr "Trade"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade Services"
msgstr "Trade Services"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Trade, Clearing and Settlement"
msgstr "Trade, Clearing and Settlement"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transaction Fees"
msgstr "Transaction Fees"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transaction ID: %s"
msgstr ""

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transfer In"
msgstr "Transfer In"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Transfer Out"
msgstr "Transfer Out"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Travellers Cheques Deposit"
msgstr "Travellers Cheques Deposit"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Travellers Cheques Withdrawal"
msgstr "Travellers Cheques Withdrawal"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Treasury Tax And Loan Service"
msgstr "Treasury Tax And Loan Service"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Triparty Repo"
msgstr "Triparty Repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Triparty Reverse Repo"
msgstr "Triparty Reverse Repo"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Turnaround"
msgstr "Turnaround"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Underwriting Commission"
msgstr "Underwriting Commission"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Card Transaction"
msgstr "Unpaid Card Transaction"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Cheque"
msgstr "Unpaid Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Unpaid Foreign Cheque"
msgstr "Unpaid Foreign Cheque"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Upfront Payment"
msgstr "Upfront Payment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Value Date"
msgstr "Value Date"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Warrant Exercise/Warrant Conversion"
msgstr "Warrant Exercise/Warrant Conversion"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Withdrawal/distribution"
msgstr "Withdrawal/distribution"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Withholding Tax"
msgstr "Withholding Tax"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "YTD Adjustment"
msgstr "YTD Adjustment"

#. module: account_bank_statement_import_camt
#: code:addons/account_bank_statement_import_camt/wizard/account_bank_statement_import_camt.py:0
#, python-format
msgid "Zero Balancing"
msgstr "Zero Balancing"
