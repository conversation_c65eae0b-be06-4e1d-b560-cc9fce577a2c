<?xml version="1.0" encoding="UTF-8"?>
<!--Sample XML file generated by FFFS (www.fkl.fi) B2C project  to demonstrate FInnish statement of account, 2009-11-02-->
<!--Attachment for Federation of Finnish Financial Services B2C Guideline for Finnish markets -description project -->
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:camt.053.001.02" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:iso:std:iso:20022:tech:xsd:camt.053.001.01
camt.053.001.02.xsd">
	<BkToCstmrStmt>
		<GrpHdr>
			<MsgId>BANKFILEID00001</MsgId>
			<CreDtTm>2009-10-30T03:30:47+02:00</CreDtTm>
			<!-- No need for message pagination (at this stage)-->
		</GrpHdr>
		<Stmt>
			<!-- This ID identifies the statement at the bank (for example MT940 :20:)-->
			<Id>BANKSTMTID0972009</Id>
			<!-- Numbering to have audit for empty statements (may differ from Legal Number). Since this element is optional statement is valid also without it.  The Legal Sequence Number identifies the official statement number-->
			<ElctrncSeqNb>120</ElctrncSeqNb>
			<!-- Account Statement number (per Booking day, Periodical statements are inserted into same message but each day as own Stmt-instance -->
			<!-- This number should be sequential and zero if this is an empty (without transactions, only the balances) statement because of audit trail -->
			<!-- Note: Electronic Sequence Number starts from 1 in the beginning of camt-service (if bank is able to deliver it) and will increase without change to 1 in the each calendar year  -->
			<LglSeqNb>97</LglSeqNb>
			<CreDtTm>2009-10-30T02:00:00+02:00</CreDtTm>
			<FrToDt>
				<!-- Recommendation: One booking date from start of day till end of day-->
				<!-- Each Balance instance has its own date to be used in the ERP database processing-->
				<!-- Because of each balance instance on the statement has their correpsonding date, this structure may be omitted -->
				<FrDtTm>2009-10-29T02:00:00+02:00</FrDtTm>
				<ToDtTm>2009-10-29T21:00:00+02:00</ToDtTm>
			</FrToDt>
			<!-- Optional to indicate possible reporduction of the statement, not used at all if a "normal" one delivered statement -->
			<!--	COPY is used for parallel statement delivered for two different customer id for same statement, DUPL is used if statement is reporduced by the request of the customer 
			<CpyDplctInd>COPY</CpyDplctInd>
			-->
			<!-- Bank Account of which statement is reported -->
			<Acct>
				<Id>
					<!-- always and only in IBAN format -->
					<IBAN>******************</IBAN>
				</Id>
				<!-- optional info from the bank to identify account type, in this case Current account -->
				<Tp>
					<Cd>CACC</Cd>
				</Tp>
				<!-- "Main" Currency of the account.  In case of multicurrency account there should be own Stmt -instances for each currency pocket statement per booking date -->
				<Ccy>EUR</Ccy>
				<!-- Account owner per bank's register -->
				<Ownr>
					<Nm>BANK ACCOUNT OWNER</Nm>
					<PstlAdr>
						<StrtNm>HELSINGINKATU</StrtNm>
						<BldgNb>31</BldgNb>
						<PstCd>00100</PstCd>
						<TwnNm>HELSINKI</TwnNm>
						<Ctry>FI</Ctry>
					</PstlAdr>
					<Id>
						<OrgId>
							<Othr>
								<!-- Finnish Organisation Id (Y-tunnus) -->
								<Id>***********</Id>
								<SchmeNm>
									<!-- Recipient's bank Id.  In case of Finnish Y-tunnus use "CUST" -->
									<Cd>BANK</Cd>
								</SchmeNm>
							</Othr>
						</OrgId>
					</Id>
				</Ownr>
				<!-- Reporting bank identified only by BIC but it is always available-->
				<Svcr>
					<FinInstnId>
						<BIC>ESSEFIHX</BIC>
					</FinInstnId>
				</Svcr>
			</Acct>
			<RltdAcct>
				<!-- Parent account of the current account and not used at all in case of single account reported within statement -->
				<!-- TITO-structure of corporate group account statements is not repeated but Related Account info is enough to build up the structure at customer applications -->
				<Id>
					<IBAN>******************</IBAN>
				</Id>
				<Ccy>EUR</Ccy>
			</RltdAcct>
			<!-- Optional Interest terms and conditions reporting as statement level information for the reported bank account.  Level of this reporting is up to reporting bank -->
			<!-- Actual Interest bookings (credits or debits) are done as normal statement bookings -->
			<Intrst>
				<Tp>
					<!-- "normal" daily interest with INDY (if OVRN is reported it will be given in separate Intrst -instance) -->
					<Cd>INDY</Cd>
				</Tp>
				<Rate>
					<Tp>
						<Othr>Business agreement</Othr>
					</Tp>
					<VldtyRg>
						<Amt>
							<FrToAmt>
								<FrAmt>
									<BdryAmt>0.00</BdryAmt>
									<Incl>true</Incl>
								</FrAmt>
								<ToAmt>
									<BdryAmt>1000000.00</BdryAmt>
									<Incl>true</Incl>
								</ToAmt>
							</FrToAmt>
						</Amt>
						<CdtDbtInd>CRDT</CdtDbtInd>
						<Ccy>EUR</Ccy>
					</VldtyRg>
				</Rate>
			</Intrst>
			<!-- Balances for the statement booking date reported -->
			<!-- Minimum is to report OPBD and CLBD and in case of reporting the possible account limit value also the CLAV value inclduing the limit value (CLBD + limit = CLAV) -->
			<!-- All other balances reported are based on bank implementation and are used for informative purposes -->
			<!-- in case of later camt.052 B2C Account report for intra-day reports the ITAV and ITBD are used (not at all on camt.053 statement) -->
			<Bal>
				<Tp>
					<CdOrPrtry>
						<Cd>OPBD</Cd>
					</CdOrPrtry>
				</Tp>
				<!-- Limit value (not included into the balance)-->
				<CdtLine>
					<Incl>false</Incl>
					<Amt Ccy="EUR">10000.00</Amt>
				</CdtLine>
				<Amt Ccy="EUR">10000.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<!-- OPBD reports the same date as the CLBD (in a daily statement) -->
				<Dt>
					<Dt>2009-10-29</Dt>
				</Dt>
			</Bal>
			<!-- PRCD balance might also be used as complement for OPBD since it is recommended by SWIFT MT940-conversion guideline -->
			<Bal>
				<Tp>
					<CdOrPrtry>
						<Cd>PRCD</Cd>
					</CdOrPrtry>
				</Tp>
				<CdtLine>
					<Incl>false</Incl>
					<Amt Ccy="EUR">10000.00</Amt>
				</CdtLine>
				<Amt Ccy="EUR">10000.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<!-- Since PRCD must equal with previous camt.053 CLBD also the dates reported should equal: PRCD reports same date as the previous statements CLBD date -->
				<Dt>
					<Dt>2009-10-28</Dt>
				</Dt>
			</Bal>
			<Bal>
				<!-- Booked closing balanceOpening balance on the next banking date -->
				<Tp>
					<CdOrPrtry>
						<Cd>CLBD</Cd>
					</CdOrPrtry>
				</Tp>
				<!-- Limit value (not included into the balance)-->
				<CdtLine>
					<Incl>false</Incl>
					<Amt Ccy="EUR">10000.00</Amt>
				</CdtLine>
				<Amt Ccy="EUR">23644.83</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<!-- equals -->
				<Dt>
					<Dt>2009-10-29</Dt>
				</Dt>
			</Bal>
			<Bal>
				<!-- CLAV in Finnish TITO-meaning is to used to report CLBD + Credit Line and in this sense CLAV may be omitted because of OPBD and CLBD embedded credit line.  -->
				<!-- When used to report meaning like MT940 64-tag info the CLAV is used to report funds available at the reported date based on the value date based bookings-->
				<Tp>
					<CdOrPrtry>
						<Cd>CLAV</Cd>
					</CdOrPrtry>
				</Tp>
				<Amt Ccy="EUR">33644.83</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Dt>
					<Dt>2009-10-29</Dt>
				</Dt>
			</Bal>
			<!-- additional optional summary information of the transactions.  To match current TITO at the minimun the statement (booking date) deposits and withdrawals should be reported-->
			<TxsSummry>
				<!-- total number of statement entries for reconciliation -->
				<TtlNtries>
					<NbOfNtries>10</NbOfNtries>
				</TtlNtries>
				<!-- Deposits)-->
				<TtlCdtNtries>
					<NbOfNtries>6</NbOfNtries>
					<Sum>23075.00</Sum>
				</TtlCdtNtries>
				<!-- withdrawls)-->
				<TtlDbtNtries>
					<NbOfNtries>4</NbOfNtries>
					<Sum>9430.17</Sum>
				</TtlDbtNtries>
				<!-- reporting transaction code based summaries if service is considered useful-->
				<!--
				<TtlNtriesPerBkTxCd>
				</TtlNtriesPerBkTxCd> 
				-->
			</TxsSummry>
			<!-- Here is the begiining of the transactions for the booking date 29.5.2009-->
			<Ntry>
				<!-- Transaction 1 as an sample of SALA batch  with elements filled both for PMJ-salaries as well as SCT SALA-->
				<!-- Here only as collection, since in salaries the payment level details are not reported -->
				<Amt Ccy="EUR">1000.12</Amt>
				<CdtDbtInd>DBIT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<!-- In case of separate Salary debit report (camt.054) is generated the banks' reference has to be in it as one matching term-->
				<AcctSvcrRef>091029ACCTSTMTARCH01</AcctSvcrRef>
				<BkTxCd>
					<!-- In case of PMJ salaries as in the sample.  In case of SCT SALA PMNT/ICDT/ESCT + PurposeCode SALA) -->
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>ICDT</Cd>
							<SubFmlyCd>SALA</SubFmlyCd>
						</Fmly>
					</Domn>
					<!-- Prtry used only in case of PMJ-salaries -->
					<Prtry>
						<Cd>NTRF+701TransactionCodeText</Cd>
					</Prtry>
				</BkTxCd>
				<NtryDtls>
					<Btch>
						<!-- customer made batch and message-references (not in old TS but yes in SALA SCT in case that pain.001 is used and direct corresponding matching can be found).  Purpose: Reconciiation-->
						<!-- Basic recommendation: as much as possible of the original payment instruction material that came from the customer into the bank-->
						<MsgId>MSGSALA0001</MsgId>
						<!-- in LM-batches this is an info given in the batch record and supported by most of the banks as the initiator batch level identification-->
						<PmtInfId>CustRefForSalaBatch</PmtInfId>
						<!-- customer made batch's transaction total by the initiated material.  Purpose: Reconciiation-->
						<NbOfTxs>4</NbOfTxs>
					</Btch>
					<TxDtls>
						<!-- used to specify what subtype (purpose code) of SCT SALA (category purpose and notice that tx code in this case is PMNT/ICDT/ESCT) debtor has used.  Not so critical on debtor stmts but on creditor it  is -->
						<Purp>
							<Cd>SALA</Cd>
						</Purp>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<Ntry>
				<!-- Transaction 2 / case of lump sum debit booking for a normal vendor payment batck (locl PMJ or SCT)-->
				<!-- SCT lump sum booking logic is up to payment processing bank-->
				<Amt Ccy="EUR">4000.00</Amt>
				<CdtDbtInd>DBIT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<!-- In case of separate payment debit report (camt.054) is generated the banks' reference has to be in it as one matching term-->
				<AcctSvcrRef>091029ACCTSTMTARCH02</AcctSvcrRef>
				<!-- In case of PMJ payments as in the sample.  In case of SCT PMNT/ICDT/ESCT with no purpose code) -->
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>ICDT</Cd>
							<SubFmlyCd>DMCT</SubFmlyCd>
						</Fmly>
					</Domn>
					<!-- Prtry used only in case of PMJ Payments-->
					<Prtry>
						<Cd>NTRF+702TransactionCodeText</Cd>
					</Prtry>
				</BkTxCd>
				<NtryDtls>
					<Btch>
						<!-- customer made batch and message-references (not in old LM but yes in SCT in case that pain.001 is used and direct corresponding matching can be found).  Purpose: Reconciiation-->
						<!-- Basic recommendation: as many original payment instruction ids that came from the customer into the bank should be reported -> and let the customer application to choose the ones for its reconciliation-->
						<MsgId>MSGSCT0099</MsgId>
						<PmtInfId>CustRefForPmtBatch</PmtInfId>
						<!-- customer made batch's transaction total.  Purpose: Reconciiation-->
						<NbOfTxs>3</NbOfTxs>
					</Btch>
				</NtryDtls>
			</Ntry>
			<Ntry>
				<!-- Transaction 3 / alternative reporting for the transaction 2 when customer wants to have all in statment and not separate camt.054 advice -->
				<Amt Ccy="EUR">4230.05</Amt>
				<CdtDbtInd>DBIT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<!--Booking date for all sub transactions in this entry -->
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<!--Value date for all sub transactions in this entry -->
					<Dt>2009-10-29</Dt>
				</ValDt>
				<AcctSvcrRef>091029ACCTSTMTARCH03</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>ICDT</Cd>
							<SubFmlyCd>DMCT</SubFmlyCd>
						</Fmly>
					</Domn>
					<!-- Prtry used only in case of PMJ Payments-->
					<Prtry>
						<Cd>NTRF+702TransactionCodeText</Cd>
					</Prtry>
				</BkTxCd>
				<NtryDtls>
					<Btch>
						<MsgId>MSGSCT0100</MsgId>
						<PmtInfId>CustRefForPmtBatch9</PmtInfId>
						<NbOfTxs>3</NbOfTxs>
					</Btch>
					<!-- Debit transaction details level depend on the source given by the originator in its payment material in different formats -->
					<!-- Sufficient amount of references should be reported for reconciliation-->
					<TxDtls>
						<!-- Sub transaction no 1 of the batch /  with reference -->
						<Refs>
							<!-- The contents of Refs used depends on clearing system and / or availablle data on the instruction material -->
							<AcctSvcrRef>091029ARCH03001</AcctSvcrRef>
							<!-- Instruction Id to Indicate TITO_T11_06 id given from the initiator in specific LM-payment file field -->
							<InstrId>TITOT1106ID01</InstrId>
						</Refs>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">2000.02</Amt>
							</TxAmt>
						</AmtDtls>
						<RltdPties>
							<Cdtr>
								<Nm>Creditor Company</Nm>
								<!-- unstructured address on LM based material -->
								<PstlAdr>
									<Ctry>FI</Ctry>
									<AdrLine>Mannerheimintie 123</AdrLine>
									<AdrLine>00100 Helsinki</AdrLine>
								</PstlAdr>
								<CtryOfRes>FI</CtryOfRes>
							</Cdtr>
							<!-- Creditor account in BBAN still in PMJ-transactions, banks don't convert internally into the reports creditor account on debtor reports -->
							<CdtrAcct>
								<Id>
									<Othr>
										<Id>**************</Id>
										<SchmeNm>
											<Cd>BBAN</Cd>
										</SchmeNm>
									</Othr>
								</Id>
							</CdtrAcct>
						</RltdPties>
						<RmtInf>
							<Strd>
								<CdtrRefInf>
									<Tp>
										<CdOrPrtry>
											<Cd>SCOR</Cd>
										</CdOrPrtry>
									</Tp>
									<Ref>3900</Ref>
								</CdtrRefInf>
							</Strd>
						</RmtInf>
					</TxDtls>
					<TxDtls>
						<!-- Sub transaction no 2 of the batch /  with free text -->
						<Refs>
							<AcctSvcrRef>091029ARCH03002</AcctSvcrRef>
							<InstrId>TITOT1106ID02</InstrId>
						</Refs>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">1000.01</Amt>
							</TxAmt>
						</AmtDtls>
						<RltdPties>
							<Cdtr>
								<Nm>Creditor Company</Nm>
								<PstlAdr>
									<Ctry>FI</Ctry>
									<AdrLine>Mannerheimintie 123</AdrLine>
									<AdrLine>00100 Helsinki</AdrLine>
								</PstlAdr>
								<CtryOfRes>FI</CtryOfRes>
							</Cdtr>
							<CdtrAcct>
								<Id>
									<Othr>
										<Id>**************</Id>
										<SchmeNm>
											<Cd>BBAN</Cd>
										</SchmeNm>
									</Othr>
								</Id>
								<!-- Creditor Account Changed at Debtor bank for better routing according to Debtor and its bank agreement-->
								<Tp>
									<Prtry>ACWC</Prtry>
								</Tp>
							</CdtrAcct>
						</RltdPties>
						<RmtInf>
							<Ustrd>Invoices 123 and 321</Ustrd>
						</RmtInf>
					</TxDtls>
					<TxDtls>
						<!-- Sub transaction no 3 of the batch /  with PMJ payment type code 2 (TITO T11-02) -->
						<!-- One simple option is to write this type of remittance type into one Unstrcured instance-->
						<Refs>
							<AcctSvcrRef>091029ARCH03003</AcctSvcrRef>
							<InstrId>TITOT1106ID03</InstrId>
						</Refs>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">1230.02</Amt>
							</TxAmt>
						</AmtDtls>
						<RltdPties>
							<Cdtr>
								<Nm>Creditor Company</Nm>
								<PstlAdr>
									<Ctry>FI</Ctry>
									<AdrLine>Mannerheimintie 123</AdrLine>
									<AdrLine>00100 Helsinki</AdrLine>
								</PstlAdr>
								<CtryOfRes>FI</CtryOfRes>
							</Cdtr>
							<CdtrAcct>
								<Id>
									<Othr>
										<Id>**************</Id>
										<SchmeNm>
											<Cd>BBAN</Cd>
										</SchmeNm>
									</Othr>
								</Id>
							</CdtrAcct>
						</RltdPties>
						<RmtInf>
							<Strd>
								<RfrdDocInf>
									<Tp>
										<CdOrPrtry>
											<Cd>CINV</Cd>
										</CdOrPrtry>
									</Tp>
									<Nb>217827182718</Nb>
								</RfrdDocInf>
								<Invcee>
									<Id>
										<OrgId>
											<Othr>
												<Id>9102910</Id>
											</Othr>
										</OrgId>
									</Id>
								</Invcee>
							</Strd>
						</RmtInf>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<!-- Some Credit entry samples -->
			<Ntry>
				<!-- Transaction 4a / refers to sample camt.054 report in FI_camt.054_sample.xml and its first Entry-->
				<Amt Ccy="EUR">2000.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<!-- Archiving code that matches account statement entry (don't care about the date)-->
				<AcctSvcrRef>091029ACCTSTMTARCH04</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RCDT</Cd>
							<SubFmlyCd>DMCT</SubFmlyCd>
						</Fmly>
					</Domn>
					<Prtry>
						<Cd>NTRF+705TransactionCodeText</Cd>
					</Prtry>
				</BkTxCd>
				<NtryDtls>
					<Btch>
						<MsgId>BANKFILEID998765</MsgId>
						<!-- customer made batch's transaction total.  Purpose: Reconciiation-->
						<NbOfTxs>2</NbOfTxs>
					</Btch>
					<TxDtls>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">2000.00</Amt>
							</TxAmt>
						</AmtDtls>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<Ntry>
				<!-- Transaction 4b / refers to sample camt.054 report in FI_camt.054_sample.xml and its second Entry-->
				<Amt Ccy="EUR">500.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<!-- Archiving code that matches account statement entry (don't care about the date)-->
				<AcctSvcrRef>091029ACCTSTMTARCH21</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RDDT</Cd>
							<SubFmlyCd>PMDD</SubFmlyCd>
						</Fmly>
					</Domn>
					<Prtry>
						<Cd>NTRF+705TransactionCodeText</Cd>
					</Prtry>
				</BkTxCd>
				<NtryDtls>
					<Btch>
						<MsgId>BANKFILEID998799</MsgId>
						<!-- customer made batch's transaction total.  Purpose: Reconciiation-->
						<NbOfTxs>2</NbOfTxs>
					</Btch>
					<TxDtls>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">500.00</Amt>
							</TxAmt>
						</AmtDtls>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<Ntry>
				<!-- Transaction 5 / refers to sample camt.054 report in FI_camt.054_sample.xml and its third Entry-->
				<Amt Ccy="EUR">3000.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<AcctSvcrRef>091029ACCTSTMTARCH05</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RCDT</Cd>
							<SubFmlyCd>ESCT</SubFmlyCd>
						</Fmly>
					</Domn>
				</BkTxCd>
				<NtryDtls>
					<Btch>
						<MsgId>BANKFILEID998765</MsgId>
						<!-- customer made batch's transaction total.  Purpose: Reconciiation-->
						<NbOfTxs>2</NbOfTxs>
					</Btch>
					<TxDtls>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">3000.00</Amt>
							</TxAmt>
						</AmtDtls>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<!-- Transaction 6 is an incoming international payment -->
			<Ntry>
				<Amt Ccy="EUR">2120.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<!-- May differ from booking date on non-PSD (or one-leg) transactions -->
				<ValDt>
					<Dt>2009-10-30</Dt>
				</ValDt>
				<AcctSvcrRef>091029ACCTSTMTARCH06</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RCDT</Cd>
							<SubFmlyCd>XBCT</SubFmlyCd>
						</Fmly>
					</Domn>
				</BkTxCd>
				<NtryDtls>
					<TxDtls>
						<Refs>
							<!-- Reference used by the Debtor banks-->
							<InstrId>ISSRBKREF12345678</InstrId>
							<TxId>ISSRBKREF12345678</TxId>
						</Refs>
						<!-- specification of counter values (TITO T11_05) -->
						<AmtDtls>
							<!-- Amount as original -->
							<InstdAmt>
								<Amt Ccy="USD">3200.00</Amt>
								<CcyXchg>
									<SrcCcy>USD</SrcCcy>
									<TrgtCcy>EUR</TrgtCcy>
									<!-- denomintor currency in the Exchange rate factor: Exchange rate = Unit Curerency / Quoted Currency -->
									<UnitCcy>EUR</UnitCcy>
									<XchgRate>0.666667</XchgRate>
									<CtrctId>FX12345</CtrctId>
									<QtnDt>2009-10-29T10:00:00+02:00</QtnDt>
								</CcyXchg>
							</InstdAmt>
							<TxAmt>
								<Amt Ccy="EUR">2120.00</Amt>
							</TxAmt>
							<!-- Amount as CounterValue, usually used only for other than EUR-accounts to support EUR-based book keeping of the end-customers-->
							<CntrValAmt>
								<Amt Ccy="EUR">2120.00</Amt>
								<CcyXchg>
									<SrcCcy>USD</SrcCcy>
									<TrgtCcy>EUR</TrgtCcy>
									<UnitCcy>EUR</UnitCcy>
									<XchgRate>0.666667</XchgRate>
									<CtrctId>FX12345</CtrctId>
									<QtnDt>2009-10-29T10:00:00+02:00</QtnDt>
								</CcyXchg>
							</CntrValAmt>
						</AmtDtls>
						<!-- in case of netted booking, debited charges are given like this.  Note!  Charges can be given without amount deduction also as an information!-->
						<Chrgs>
							<Amt Ccy="EUR">20.00</Amt>
							<Tp>
								<Cd>COMM</Cd>
							</Tp>
						</Chrgs>
						<RltdPties>
							<Dbtr>
								<Nm>DEBTOR NAME</Nm>
								<!-- Usually OrgId is not available in inernational money transfers -->
								<Id>
									<OrgId>
										<Othr>
											<Id>*********</Id>
											<SchmeNm>
												<Cd>DUNS</Cd>
											</SchmeNm>
										</Othr>
									</OrgId>
								</Id>
							</Dbtr>
						</RltdPties>
						<RltdAgts>
							<DbtrAgt>
								<FinInstnId>
									<BIC>BOFAUS6H</BIC>
								</FinInstnId>
							</DbtrAgt>
						</RltdAgts>
						<RmtInf>
							<Ustrd>INVOICE US20291092</Ustrd>
						</RmtInf>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<!-- Transaction 7 as a sample of priority payment reporting-->
			<Ntry>
				<!-- Transaction 7-->
				<Amt Ccy="EUR">5455.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<AcctSvcrRef>091029ACCTSTMTARCH07</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RCDT</Cd>
							<!-- Priority Credit Transfer-->
							<SubFmlyCd>PRCT</SubFmlyCd>
						</Fmly>
					</Domn>
				</BkTxCd>
				<NtryDtls>
					<TxDtls>
						<Refs>
							<!-- Reference used by the Debtor banks -->
							<InstrId>BKREFDBT0101010</InstrId>
							<TxId>BKREFDBT0101010</TxId>
						</Refs>
						<AmtDtls>
							<!-- Amount as original -->
							<InstdAmt>
								<Amt Ccy="EUR">5500.00</Amt>
							</InstdAmt>
							<TxAmt>
								<Amt Ccy="EUR">5455.00</Amt>
							</TxAmt>
						</AmtDtls>
						<Chrgs>
							<Amt Ccy="EUR">45.00</Amt>
							<Tp>
								<Cd>COMM</Cd>
							</Tp>
						</Chrgs>
						<RltdPties>
							<Dbtr>
								<Nm>PAYERS NAME2</Nm>
								<PstlAdr>
									<StrtNm>GOVERN STREET</StrtNm>
									<BldgNb>22</BldgNb>
									<PstCd>291092</PstCd>
									<TwnNm>LONDON</TwnNm>
									<Ctry>UK</Ctry>
								</PstlAdr>
								<Id>
									<OrgId>
										<Othr>
											<Id>*********</Id>
											<SchmeNm>
												<Cd>EANG</Cd>
											</SchmeNm>
										</Othr>
									</OrgId>
								</Id>
							</Dbtr>
						</RltdPties>
						<RltdAgts>
							<DbtrAgt>
								<FinInstnId>
									<BIC>BOFSGB22</BIC>
								</FinInstnId>
							</DbtrAgt>
						</RltdAgts>
						<RmtInf>
							<Strd>
								<RfrdDocAmt>
									<RmtdAmt Ccy="EUR">5500.00</RmtdAmt>
								</RfrdDocAmt>
								<CdtrRefInf>
									<Tp>
										<CdOrPrtry>
											<Cd>SCOR</Cd>
										</CdOrPrtry>
										<Issr>ISO</Issr>
									</Tp>
									<Ref>RF98***********2</Ref>
								</CdtrRefInf>
							</Strd>
						</RmtInf>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<!--Transaction 8/ Single incoming SCT-->
			<Ntry>
				<Amt Ccy="EUR">10000.00</Amt>
				<CdtDbtInd>CRDT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<AcctSvcrRef>091029ACCTSTMTARCH08</AcctSvcrRef>
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RCDT</Cd>
							<SubFmlyCd>ESCT</SubFmlyCd>
						</Fmly>
					</Domn>
				</BkTxCd>
				<NtryDtls>
					<TxDtls>
						<Refs>
							<EndToEndId>EndToEndIdSCT01</EndToEndId>
						</Refs>
						<!-- EPC recommendation -->
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">10000.00</Amt>
							</TxAmt>
						</AmtDtls>
						<RltdPties>
							<Dbtr>
								<Nm>DEBTOR</Nm>
							</Dbtr>
							<UltmtDbtr>
								<Nm>ULTIMATE DEBTOR</Nm>
								<Id>
									<OrgId>
										<Othr>
											<Id>987654321</Id>
											<SchmeNm>
												<Cd>TXID</Cd>
											</SchmeNm>
										</Othr>
									</OrgId>
								</Id>
							</UltmtDbtr>
						</RltdPties>
						<RltdAgts>
							<DbtrAgt>
								<FinInstnId>
									<BIC>NDEAFIHH</BIC>
								</FinInstnId>
							</DbtrAgt>
						</RltdAgts>
						<Purp>
							<Cd>TREA</Cd>
						</Purp>
						<RmtInf>
							<Strd>
								<CdtrRefInf>
									<Tp>
										<CdOrPrtry>
											<Cd>SCOR</Cd>
										</CdOrPrtry>
										<Issr>ISO</Issr>
									</Tp>
									<Ref>RF98***********2</Ref>
								</CdtrRefInf>
							</Strd>
						</RmtInf>
						<RltdDts>
							<AccptncDtTm>2009-10-28T03:00:00+02:00</AccptncDtTm>
						</RltdDts>
					</TxDtls>
				</NtryDtls>
			</Ntry>
			<Ntry>
				<!-- Transaction 9 as an sample of bank charges debit that can be applied to Interest too if bank has possibility to do such a reporting-->
				<!-- Maybe iomplemented for the interest reporting too-->
				<Amt Ccy="EUR">200.00</Amt>
				<CdtDbtInd>DBIT</CdtDbtInd>
				<Sts>BOOK</Sts>
				<BookgDt>
					<Dt>2009-10-29</Dt>
				</BookgDt>
				<ValDt>
					<Dt>2009-10-29</Dt>
				</ValDt>
				<AcctSvcrRef>091029ACCTSTMTARCH09</AcctSvcrRef>
				<!-- generic Family code to give possibility to report  received PMNT charges with sub types for DMCT and ESCT-->
				<BkTxCd>
					<Domn>
						<Cd>PMNT</Cd>
						<Fmly>
							<Cd>RCDT</Cd>
							<SubFmlyCd>CHRG</SubFmlyCd>
						</Fmly>
					</Domn>
				</BkTxCd>
				<NtryDtls>
					<!-- TX-details defining the type specific charges -->
					<!-- PMJ received Payments -->
					<TxDtls>
						<Refs>
							<AcctSvcrRef>091029ARCH09001</AcctSvcrRef>
						</Refs>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">100.00</Amt>
							</TxAmt>
						</AmtDtls>
						<BkTxCd>
							<Domn>
								<Cd>PMNT</Cd>
								<Fmly>
									<Cd>RCDT</Cd>
									<SubFmlyCd>DMCT</SubFmlyCd>
								</Fmly>
							</Domn>
						</BkTxCd>
						<RltdPties>
							<Dbtr>
								<Nm>SEB Merchant Banking Finland</Nm>
							</Dbtr>
						</RltdPties>
						<RltdQties>
							<Qty>
								<Unit>10000</Unit>
							</Qty>
						</RltdQties>
					</TxDtls>
					<!-- SEPA Received  Payments -->
					<TxDtls>
						<Refs>
							<AcctSvcrRef>091029ARCH09001</AcctSvcrRef>
						</Refs>
						<AmtDtls>
							<TxAmt>
								<Amt Ccy="EUR">100.00</Amt>
							</TxAmt>
						</AmtDtls>
						<BkTxCd>
							<Domn>
								<Cd>PMNT</Cd>
								<Fmly>
									<Cd>RCDT</Cd>
									<SubFmlyCd>ESCT</SubFmlyCd>
								</Fmly>
							</Domn>
						</BkTxCd>
						<RltdPties>
							<Dbtr>
								<Nm>SEB Merchant Banking Finland</Nm>
							</Dbtr>
						</RltdPties>
						<RltdQties>
							<Qty>
								<Unit>150000</Unit>
							</Qty>
						</RltdQties>
					</TxDtls>
				</NtryDtls>
			</Ntry>
		</Stmt>
	</BkToCstmrStmt>
</Document>
