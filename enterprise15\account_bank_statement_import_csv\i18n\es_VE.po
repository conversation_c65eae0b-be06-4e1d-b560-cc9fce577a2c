# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import_csv
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Spanish (Venezuela) (https://www.transifex.com/odoo/teams/41243/es_VE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_VE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import_csv
#: model:ir.ui.view,arch_db:account_bank_statement_import_csv.account_bank_statement_import_csv
msgid "Comma Separated values (.CSV)"
msgstr ""

#. module: account_bank_statement_import_csv
#. openerp-web
#: code:addons/account_bank_statement_import_csv/static/src/js/import_bank_stmt.js:14
#: model:ir.model,name:account_bank_statement_import_csv.model_account_bank_statement_import
#, python-format
msgid "Import Bank Statement"
msgstr ""

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_base_import_import
msgid "base_import.import"
msgstr ""
