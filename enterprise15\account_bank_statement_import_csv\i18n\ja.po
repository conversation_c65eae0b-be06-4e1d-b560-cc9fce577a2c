# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_csv
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <mura<PERSON><PERSON>@date-yakkyoku.com>, 2022
# <PERSON><PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_base_import_import
msgid "Base Import"
msgstr "基本インポート"

#. module: account_bank_statement_import_csv
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_csv.account_bank_statement_import_csv
msgid "Comma-Separated values (CSV)"
msgstr "カンマ区切りテキストファイル (CSV)"

#. module: account_bank_statement_import_csv
#. openerp-web
#: code:addons/account_bank_statement_import_csv/static/src/js/import_bank_stmt.js:0
#: model:ir.model,name:account_bank_statement_import_csv.model_account_bank_statement_import
#, python-format
msgid "Import Bank Statement"
msgstr "銀行取引明細のインポート"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_account_journal
msgid "Journal"
msgstr "仕訳帳"

#. module: account_bank_statement_import_csv
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
#, python-format
msgid "Mixing CSV files with other file types is not allowed."
msgstr "CSVファイルを他のファイルタイプと混合することは許可されていません。"

#. module: account_bank_statement_import_csv
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
#, python-format
msgid "Only one CSV file can be selected."
msgstr "選択できるCSVファイルは1つだけです。"

#. module: account_bank_statement_import_csv
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
#, python-format
msgid "Rows must be sorted by date."
msgstr "行は日付順にソートして下さい。"
