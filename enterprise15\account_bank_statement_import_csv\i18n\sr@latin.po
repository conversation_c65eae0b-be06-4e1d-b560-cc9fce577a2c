# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import_csv
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_bank_statement_import_csv
#: model:ir.ui.view,arch_db:account_bank_statement_import_csv.account_bank_statement_import_csv
msgid "Comma Separated values (.CSV)"
msgstr ""

#. module: account_bank_statement_import_csv
#. openerp-web
#: code:addons/account_bank_statement_import_csv/static/src/js/import_bank_stmt.js:14
#: model:ir.model,name:account_bank_statement_import_csv.model_account_bank_statement_import
#, python-format
msgid "Import Bank Statement"
msgstr "Učitaj izvod iz banke"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_base_import_import
msgid "base_import.import"
msgstr ""
