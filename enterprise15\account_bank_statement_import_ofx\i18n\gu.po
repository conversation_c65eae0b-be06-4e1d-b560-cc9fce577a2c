# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import_ofx
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-08-02 11:20+0000\n"
"PO-Revision-Date: 2018-08-02 11:20+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import_ofx
#: model:ir.ui.view,arch_db:account_bank_statement_import_ofx.account_bank_statement_import_ofx
msgid "<i class=\"fa fa-download mr4\"/>Import Sample Template"
msgstr ""

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr ""

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_journal
msgid "Journal"
msgstr "રોજનામું"

#. module: account_bank_statement_import_ofx
#: model:ir.ui.view,arch_db:account_bank_statement_import_ofx.account_bank_statement_import_ofx
msgid "Open Financial Exchange (.OFX)"
msgstr ""

#. module: account_bank_statement_import_ofx
#: code:addons/account_bank_statement_import_ofx/wizard/account_bank_statement_import_ofx.py:105
#, python-format
msgid "The library 'ofxparse' is missing, OFX import cannot proceed."
msgstr ""
