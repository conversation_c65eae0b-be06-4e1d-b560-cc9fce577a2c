# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_ofx
# 
# Translators:
# <PERSON><PERSON><PERSON> <sz<PERSON><PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <piotr.w.c<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Maksym <<EMAIL>>, 2021\n"
"Language-Team: Polish (https://www.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Pobierz Wyciąg Bankowy"

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_journal
msgid "Journal"
msgstr "Dziennik"

#. module: account_bank_statement_import_ofx
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_ofx.account_bank_statement_import_ofx
msgid "Open Financial Exchange (OFX)"
msgstr "Open Financial Exchange (OFX)"

#. module: account_bank_statement_import_ofx
#: code:addons/account_bank_statement_import_ofx/wizard/account_bank_statement_import_ofx.py:0
#, python-format
msgid "The library 'ofxparse' is missing, OFX import cannot proceed."
msgstr ""
"Nie znaleziono biblioteki 'ofxparse', import OFX nie może być kontynuowany."
