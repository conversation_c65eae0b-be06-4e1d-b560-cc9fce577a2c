# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_ofx
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Yedigen, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Yedigen, 2021\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Banka Hesap Ekstresi İçe Aktar"

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_journal
msgid "Journal"
msgstr "Yevmiye"

#. module: account_bank_statement_import_ofx
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_ofx.account_bank_statement_import_ofx
msgid "Open Financial Exchange (OFX)"
msgstr "Açık Mali Takas (OFX)"

#. module: account_bank_statement_import_ofx
#: code:addons/account_bank_statement_import_ofx/wizard/account_bank_statement_import_ofx.py:0
#, python-format
msgid "The library 'ofxparse' is missing, OFX import cannot proceed."
msgstr "'ofxparse' isimli kütüphane bulunamadı, OFX içe aktarımı başarılamadı"
