# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_qif
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid ""
"Accounting journal related to the bank statement you're importing. It has to"
" be manually chosen for statement formats which doesn't allow automatic "
"journal detection (QIF for example)."
msgstr ""
"Diario contable relacionado con el estado de cuenta bancario que está "
"importando. Debe elegirlo de manera manual pues el formato no permite la "
"detección automática del diario (por ejemplo, QIF)"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid ""
"Although the historic QIF date format is month-first (mm/dd/yy), many "
"financial institutions use the local format.Therefore, it is frequent "
"outside the US to have QIF date formated day-first (dd/mm/yy)."
msgstr ""
"Aunque el formato de fecha para QIF siempre ha sido con el mes primero "
"(mm/dd/aa), muchas instituciones financieras utilizan el formato local. Por "
"eso, es usual tener el formato de día primero (dd/mm/aa) fuera de EE. UU."

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "Could not decipher the QIF file."
msgstr "No se pudo leer el archivo QIF."

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid "Dates format"
msgstr "Formato de fechas"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Decimal Separator"
msgstr "Separador decimal "

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Field used to avoid conversion issues."
msgstr "Campo utilizado para evitar problemas de conversión"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__hide_journal_field
msgid "Hide the journal field in the view"
msgstr "Ocultar el campo del diario en la vista"

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Importar estado de cuenta bancario"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"In order to avoid conversion errors, please specify the decimal separator "
"you wish to use."
msgstr ""
"Para evitar problemas de conversión, especifique el separador decimal que "
"desea usar."

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid "Journal"
msgstr "Diario"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_qif
msgid "Quicken Interchange Format (QIF)"
msgstr "Quicken Interchange Format (QIF)"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid "Show Qif Date Format"
msgstr "Mostrar el formato de fecha QiF"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid ""
"Technical field used to ask the user for the date format used in the QIF "
"file, as this format is ambiguous."
msgstr ""
"Campo que se utiliza para preguntarle al usuario el formato de fecha que "
"desea usar en el archivo QIF ya que este es ambiguo."

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"The QIF format is ambiguous about dates: please check with your financial "
"institution whether they format it with month or day first.<br/>"
msgstr ""
"El formato QIF es ambiguo en cuestión de fechas: verifique con su "
"institución financiera si utilizan el formato de mes primero o el de día "
"primero.<br/>"

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "This file is either not a bank statement or is not correctly formed."
msgstr ""
"Puede que este archivo no sea un estado de cuenta bancario o no tenga el "
"formato correcto."

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid "Upload"
msgstr "Subir"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__day_first
msgid "dd/mm/yy"
msgstr "dd/mm/aa"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__month_first
msgid "mm/dd/yy"
msgstr "mm/dd/aa"
