# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_bank_statement_import_qif
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import_journal_id
msgid ""
"Accounting journal related to the bank statement you're importing. It has to"
" be manually chosen for statement formats which doesn't allow automatic "
"journal detection (QIF for example)."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import_qif_date_format
msgid ""
"Although the historic QIF date format is month-first (mm/dd/yy), many "
"financial institutions use the local format.Therefore, it is frequent "
"outside the US to have QIF date formated day-first (dd/mm/yy)."
msgstr ""

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:59
#, python-format
msgid "Could not decipher the QIF file."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import_qif_date_format
msgid "Dates format"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import_hide_journal_field
msgid "Hide the journal field in the view"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import_journal_id
msgid "Journal"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_qif
msgid "Quicken Interchange Format (.QIF)"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import_show_qif_date_format
msgid "Show Qif Date Format"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import_show_qif_date_format
msgid ""
"Technical field used to ask the user for the date format used in the QIF "
"file, as this format is ambiguous."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"The QIF format is ambiguous about dates: please check with your financial "
"institution whether they format it with month or day first.<br/>"
msgstr ""

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:105
#, python-format
msgid "This file is either not a bank statement or is not correctly formed."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid "_Import"
msgstr ""

#. module: account_bank_statement_import_qif
#: selection:account.bank.statement.import,qif_date_format:0
msgid "dd/mm/yy"
msgstr ""

#. module: account_bank_statement_import_qif
#: selection:account.bank.statement.import,qif_date_format:0
msgid "mm/dd/yy"
msgstr ""
