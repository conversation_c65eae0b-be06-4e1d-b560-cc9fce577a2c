# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_qif
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid ""
"Accounting journal related to the bank statement you're importing. It has to"
" be manually chosen for statement formats which doesn't allow automatic "
"journal detection (QIF for example)."
msgstr ""
"Jurnal akuntansi yang berhubungan dengan surat pernyataan bank yang Anda "
"impor. Itu harus dipilih secara manual untuk format pernyataan dimana "
"pendeteksian jurnal otomatis tidak diizinkan (QIF sebagai contoh)."

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid ""
"Although the historic QIF date format is month-first (mm/dd/yy), many "
"financial institutions use the local format.Therefore, it is frequent "
"outside the US to have QIF date formated day-first (dd/mm/yy)."
msgstr ""
"Walaupun dalam format tanggal historis QIF adalah dimulai dari bulan "
"(bb/hh/tt), banyak institusi keuangan menggunakan format lokal. Oleh "
"karenanya, sering institusi diluar AS menggunakan format tanggal QIF dimulai"
" dari hari (hh/bb/tt)."

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "Could not decipher the QIF file."
msgstr "Tidak dapat membaca berkas QIF."

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid "Dates format"
msgstr "Format tanggal"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Decimal Separator"
msgstr "Pemisah Desimal"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Field used to avoid conversion issues."
msgstr "Field digunakan untuk menghindari masalah conversion."

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__hide_journal_field
msgid "Hide the journal field in the view"
msgstr "Sembunyikan bidang jurnal pada tampilan"

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "Impor Pernyataan Bank"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"In order to avoid conversion errors, please specify the decimal separator "
"you wish to use."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid "Journal"
msgstr "Jurnal"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_qif
msgid "Quicken Interchange Format (QIF)"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid "Show Qif Date Format"
msgstr "Munculkan Format Tanggal QIF"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid ""
"Technical field used to ask the user for the date format used in the QIF "
"file, as this format is ambiguous."
msgstr ""
"Kolom teknis yang digunakan untuk bertanya pada pengguna tentang format "
"tanggal yang digunakan pada berkas QIF, karena format ini meragukan."

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"The QIF format is ambiguous about dates: please check with your financial "
"institution whether they format it with month or day first.<br/>"
msgstr ""
"Format QIF meragukan pada tanggal: mohon cek pada institusi  keuangan Anda "
"apakah format yang digunakan dimulai dari bulan atau hari terlebih dahulu."

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "This file is either not a bank statement or is not correctly formed."
msgstr ""
"Berkas ini tidak baik untuk surat pernyataan bank atau tidak terbentuk "
"dengan benar."

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid "Upload"
msgstr "Unggah"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__day_first
msgid "dd/mm/yy"
msgstr "tt/bb/tttt"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__month_first
msgid "mm/dd/yy"
msgstr "bb/tt/tttt"
