# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_qif
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: ສີສຸວັນ ສັງບົວບຸລົມ <<EMAIL>>, 2022\n"
"Language-Team: Lao (https://www.transifex.com/odoo/teams/41243/lo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lo\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid ""
"Accounting journal related to the bank statement you're importing. It has to"
" be manually chosen for statement formats which doesn't allow automatic "
"journal detection (QIF for example)."
msgstr ""
"ບັນຊີລາຍວັນທີ່ກ່ຽວຂ້ອງກັບບັນຊີສຳຮອງເງິນຝາກທະນາຄານທີ່ທ່ານກໍາລັງນໍາເຂົ້າ. "
"ມັນຕ້ອງໄດ້ຮັບການເລືອກເອົາດ້ວຍຕົນເອງສໍາລັບຮູບແບບບັນຊີສຳຮອງທະນາຄານ "
"ຊຶ່ງບໍ່ອະນຸຍາດໃຫ້ການຊອກຄົ້ນຫາໄດ້ຢ່າງອັດຕະໂນມັດ (ຕົວຢ່າ QIF )."

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid ""
"Although the historic QIF date format is month-first (mm/dd/yy), many "
"financial institutions use the local format.Therefore, it is frequent "
"outside the US to have QIF date formated day-first (dd/mm/yy)."
msgstr ""
"ເຖິງແມ່ນວ່າ ປະຫວັດ ຮູບແບບວັນທີ ຂອງ QIF  ເອົາເດືອນຂຶ້ນກ່ອນ  (mm/dd/yy), ແຕ່ "
"ຫຼາຍໆສະຖາບັນການເງິນ ໄດ້ໃຊ້ ຮູບແບບວັນທີຂອງພາສາຕົນ. ເພາະສະນັ້ນ, ຢູ່ນອກສະຫະລັດ "
"ຈຶ່ງມີ ຮູບແບບວັນທີ ຂອງ QIF ທີ່ເອົາ ມື້ຂຶ້ນກ່ອນ (dd / mm / yy) ຢູ່ເລື້ອຍໆ."

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "Could not decipher the QIF file."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid "Dates format"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Decimal Separator"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Field used to avoid conversion issues."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__hide_journal_field
msgid "Hide the journal field in the view"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "ນໍາເຂົ້າບັນຊີສຳຮອງເງິນຝາກທະນາຄານ"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"In order to avoid conversion errors, please specify the decimal separator "
"you wish to use."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid "Journal"
msgstr "ບັນຊີປະຈຳວັນ"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_qif
msgid "Quicken Interchange Format (QIF)"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid "Show Qif Date Format"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid ""
"Technical field used to ask the user for the date format used in the QIF "
"file, as this format is ambiguous."
msgstr ""

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"The QIF format is ambiguous about dates: please check with your financial "
"institution whether they format it with month or day first.<br/>"
msgstr ""

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "This file is either not a bank statement or is not correctly formed."
msgstr ""

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid "Upload"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__day_first
msgid "dd/mm/yy"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__month_first
msgid "mm/dd/yy"
msgstr ""
