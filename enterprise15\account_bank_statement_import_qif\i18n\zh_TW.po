# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_qif
# 
# Translators:
# se<PERSON> huang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid ""
"Accounting journal related to the bank statement you're importing. It has to"
" be manually chosen for statement formats which doesn't allow automatic "
"journal detection (QIF for example)."
msgstr "與您要匯入的銀行對帳單相關的會計日記帳。 必須手動選擇不允許自動日誌檢測的語句格式（例如QIF）。"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid ""
"Although the historic QIF date format is month-first (mm/dd/yy), many "
"financial institutions use the local format.Therefore, it is frequent "
"outside the US to have QIF date formated day-first (dd/mm/yy)."
msgstr ""
"雖然歷史QIF日期格式是以月為先（mm / dd / yy），但許多金融機構使用本地格式。因此，在美國以外的國家/地區通常會在第一天（dd / mm /"
" yy）格式化QIF日期。"

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "Could not decipher the QIF file."
msgstr "無法解碼QIF文件。"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_date_format
msgid "Dates format"
msgstr "日期格式"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Decimal Separator"
msgstr "小數分割符"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__qif_decimal_point
msgid "Field used to avoid conversion issues."
msgstr "用於避免轉換問題的欄位。"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__hide_journal_field
msgid "Hide the journal field in the view"
msgstr "隱藏視圖中的日記欄位"

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_bank_statement_import
msgid "Import Bank Statement"
msgstr "匯入銀行對帳單"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"In order to avoid conversion errors, please specify the decimal separator "
"you wish to use."
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model,name:account_bank_statement_import_qif.model_account_journal
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__journal_id
msgid "Journal"
msgstr "日記帳"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_qif
msgid "Quicken Interchange Format (QIF)"
msgstr ""

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,field_description:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid "Show Qif Date Format"
msgstr "顯示Qif日期格式"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields,help:account_bank_statement_import_qif.field_account_bank_statement_import__show_qif_date_format
msgid ""
"Technical field used to ask the user for the date format used in the QIF "
"file, as this format is ambiguous."
msgstr "用於詢問用戶QIF文件中使用的日期格式的技術欄位，因為此格式不明確。"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid ""
"The QIF format is ambiguous about dates: please check with your financial "
"institution whether they format it with month or day first.<br/>"
msgstr "QIF格式對日期不明確：請與您的金融機構核實是否將其格式化為月或日。<br/>"

#. module: account_bank_statement_import_qif
#: code:addons/account_bank_statement_import_qif/wizard/account_bank_statement_import_qif.py:0
#, python-format
msgid "This file is either not a bank statement or is not correctly formed."
msgstr "此文件不是銀行對帳單或未正確形成。"

#. module: account_bank_statement_import_qif
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import_qif.account_bank_statement_import_view_inherited
msgid "Upload"
msgstr "上傳"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__day_first
msgid "dd/mm/yy"
msgstr "日/月/年"

#. module: account_bank_statement_import_qif
#: model:ir.model.fields.selection,name:account_bank_statement_import_qif.selection__account_bank_statement_import__qif_date_format__month_first
msgid "mm/dd/yy"
msgstr "月/日/年"
