# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_batch_payment
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>eli Õigus <<EMAIL>>, 2022
# JanaAvalah, 2022
# Anna, 2023
# <PERSON><PERSON><PERSON>, 2024
# Birgit Vijar, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Birgit Vijar, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_form_inherit_account_batch_payment
msgid "<span>Batch Payment</span>"
msgstr "<span>Koondmakse</span>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid ""
"<strong attrs=\"{'invisible': [('warning_line_ids', '=', [])]}\">The "
"following warnings were also raised; they do not impeach validation</strong>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>Please first consider the following warnings</strong>"
msgstr "<strong>Palun võtke esmalt arvesse järgnevaid hoiatusi</strong>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>The following errors occurred</strong>"
msgstr "<strong>Ilmnesid järgnevad vead</strong>"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_download_wizard
msgid "Account Batch download wizard"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Additional help message about the error"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must belong to the same company."
msgstr "Kõik koondmakses olevad maksed peavad kuuluma samale ettevõttele."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must share the same payment method."
msgstr "Kõik koondmakses olevad maksed peavad olema tehtud sama makseviisiga."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Amount"
msgstr "Summa"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__amount_signed
msgid "Amount Signed"
msgstr "Allkirjastatud summa"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Saadaval maksemeetod"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "Pank"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr "Pangaandmik"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr "Koondmakse sisu"

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr "Massmaksed"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr "Massmaksete raport"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_payment.py:0
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
#, python-format
msgid "Batch Payment"
msgstr "Koondmakse"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Batch Payments"
msgstr "Koondmaksed"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr "Maksete tüüp"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
msgid "Batch payment from which the file has been generated."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""
"Koondmaksed võimaldavad teil grupeerida erinevaid makseid lihtsustamaks\n"
"                    sobitamist. Koondmaksed on kasulikud ka tšekkide\n"
"                    panka viimisel."

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard
msgid "Batch payments error reporting wizard"
msgstr "Koondmaksete vea teavitamise viisard"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard_line
msgid "Batch payments error reporting wizard line"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Cannot validate an empty batch. Please add some payments to it first."
msgstr "Tühja koondmakset ei saa kinnitada. Palun lisage maksed."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Click here to download the generated file:"
msgstr "Vajutage siia, et laadida alla genereeritud fail:"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Close"
msgstr "Sulge"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "Kood"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Create Batch Payment"
msgstr "Loo koondmakse"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr "Loo uus kliendi koondmakse"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr "Looge uus tarnija koondmakse"

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
msgid "Create batch payment"
msgstr "Loo koondmakse"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr "Seotud ekspordifaili loomise kuupäev."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "Valuuta"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "Klient"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "Date"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description"
msgstr "Kirjeldus"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description of the error"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Download export file"
msgstr "Lae ekspordifail alla"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__error_line_ids
msgid "Error Line"
msgstr "Vea rida"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__error_wizard_id
msgid "Error Wizard"
msgstr "Vea viisard"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Exclude Payments"
msgstr "Välista maksed"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr "Ekspordi fail, mis on seotud selle koondiga. "

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Exported File"
msgstr "Eksporditud fail"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_file
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "Fail"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr "Failide genereerimine on lubatud"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr "Faili nimi"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "File name"
msgstr "Faili nimi"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_file
msgid "Generated XML file"
msgstr "Loodud XML fail"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr "Makse kuupäev"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr "Grupeeri"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Help"
msgstr "Abi"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
msgid "ID"
msgstr "ID"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Id: %s"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__inbound
msgid "Inbound"
msgstr "Sissetulev"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Inbound Batch Payments Sequence"
msgstr "Laekuvate koondmaksete järjestus"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "Andmik"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "Teavitused"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__payment_method_name
msgid "Name"
msgstr "Nimi"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr "Ekspordi faili nimetus"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "Name of the generated XML file"
msgstr "Loodud XML faili nimi"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_payment__amount_signed
msgid "Negative value of amount field if payment_type is outbound"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__draft
msgid "New"
msgstr "Uus"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not reconciled"
msgstr "Sobitamata"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__outbound
msgid "Outbound"
msgstr "Väljuv"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Outbound Batch Payments Sequence"
msgstr "Väljuvate koondmaksete järjestus"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr "Maksemeetod"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment_method
msgid "Payment Methods"
msgstr "Maksmise meetodid"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payment method"
msgstr "Makseviis"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "Maksed"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
msgid "Payments causing this error"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/wizard/batch_error.py:0
#, python-format
msgid "Payments in Error"
msgstr "Maksete viga"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Payments must be posted to be added to a batch."
msgstr "Koondmakse loomiseks peavad maksed olema kinnitatud."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "Prindi"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr "Prindi koondmakse"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Proceed with validation"
msgstr "Jätka kinnitamist"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Re-generate Export File"
msgstr "Genereeri fail uuesti"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__reconciled
msgid "Reconciled"
msgstr "Sobitatud"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "Viide"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__sent
msgid "Sent"
msgstr "Saadetud"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Set payments state to \"posted\"."
msgstr "Määra maksete staatuseks \"postitatud\"."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Show"
msgstr "Näita"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid "Show Remove Button"
msgstr "Näita \"Eemalda\" nuppu"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid "Show Remove Options"
msgstr "Näita eemaldamise valikud"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have already been matched with a bank statement."
msgstr "Mõned maksed on juba sobitatud pangaväljavõttega."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have already been sent."
msgstr "Mõned maksed on juba toestatud."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "Maakond"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch cannot be validated."
msgstr "Koondmakset ei saa kinnitada."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch could not be validated"
msgstr "Koondmakset ei saa kinnitada"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr "Koondmakse peab sisaldama sama maksemeetodit nagu sisaldavad maksed. "

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "The batch must have the same type as the payments it contains."
msgstr "Koondmaksel peab olema sama maksetüüp nagu maksetel. "

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
msgid ""
"The batch payment generating the errors and warnings displayed in this "
"wizard."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr ""
"Koondmakse ja koondmakses sisaldavad maksed peavad sisaldama ühte ja sama "
"andmikku. "

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "Kokku"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid ""
"True if and only if the options to remove the payments causing the errors or"
" warnings from the batch should be shown"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr "Lahti ühendatud"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr "Kinnita"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__warning_line_ids
msgid "Warning Line"
msgstr "Hoiatuse rida"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__warning_wizard_id
msgid "Warning Wizard"
msgstr "Hoiatuse viisard"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid ""
"Whether or not this line should display a button allowing to remove its "
"related payments from the batch"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments that are not posted.\n"
"Payments:\n"
"%s"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments with zero amount in a Batch Payment.\n"
"Payments:\n"
"%s"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "auto ..."
msgstr "automaatselt ..."
