# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_batch_payment
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_download_wizard
msgid "Account Batch download wizard"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:75
#, python-format
msgid "All payments in the batch must belong to the same company."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:86
#, python-format
msgid "All payments in the batch must share the same payment method."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:81
#, python-format
msgid "All payments in the batch must share the same type."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Amount"
msgstr "કિંમત"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "બેન્ક"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr ""

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Batch Payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
msgid "Batch Payments"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
msgid "Batch payment from which the file has been generated."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Click here to download the generated file:"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Close"
msgstr "બંધ કરો"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "કોડ"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:57
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
#, python-format
msgid "Create Batch Payment"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "ચલણ"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "ભાગીદાર"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "તારીખ"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_report_account_batch_payment_print_batch_payment__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Download export file"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Exported File"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_file
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "ફાઇલ"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "File name"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_file
msgid "Generated XML file"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
#: model:ir.model.fields,field_description:account_batch_payment.field_report_account_batch_payment_print_batch_payment__id
msgid "ID"
msgstr "ઓળખ"

#. module: account_batch_payment
#: selection:account.batch.payment,batch_type:0
msgid "Inbound"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:31
#, python-format
msgid "Inbound Batch Payments Sequence"
msgstr ""

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/js/account_batch_payment_reconciliation.js:135
#, python-format
msgid "Incorrect Operation"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "રોજનામું"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_report_account_batch_payment_print_batch_payment____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "મેમો"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "Name of the generated XML file"
msgstr ""

#. module: account_batch_payment
#: selection:account.batch.payment,state:0
msgid "New"
msgstr "નવું"

#. module: account_batch_payment
#: selection:account.batch.payment,batch_type:0
msgid "Outbound"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:17
#, python-format
msgid "Outbound Batch Payments Sequence"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "ચૂકવણીઓ"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "છાપો"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_print_batch_payment
msgid "Print Batch Payments"
msgstr ""

#. module: account_batch_payment
#: selection:account.batch.payment,state:0
msgid "Reconciled"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "સંદર્ભ"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Register Payment"
msgstr ""

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/xml/account_reconciliation.xml:8
#, python-format
msgid "Select a Batch Payment"
msgstr ""

#. module: account_batch_payment
#: selection:account.batch.payment,state:0
msgid "Sent"
msgstr ""

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/js/account_batch_payment_reconciliation.js:133
#, python-format
msgid ""
"Some journal items from the selected batch payment are already selected in "
"another reconciliation : "
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "અવસ્થા"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:88
#, python-format
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:83
#, python-format
msgid "The batch must have the same type as the payments it contains."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:78
#, python-format
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "કુલ"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__warning_message
msgid "Warning"
msgstr "ચેતવણી"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__warning_message
msgid "Warning message to display about the content of the downloadable file."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "auto ..."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "re-Generate Export File"
msgstr ""
