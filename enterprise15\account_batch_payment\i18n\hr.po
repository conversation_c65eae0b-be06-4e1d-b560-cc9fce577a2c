# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_batch_payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Gordana Bilas, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_form_inherit_account_batch_payment
msgid "<span>Batch Payment</span>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid ""
"<strong attrs=\"{'invisible': [('warning_line_ids', '=', [])]}\">The "
"following warnings were also raised; they do not impeach validation</strong>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>Please first consider the following warnings</strong>"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>The following errors occurred</strong>"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_download_wizard
msgid "Account Batch download wizard"
msgstr "Čarobnjak za skupna plaćanja"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Additional help message about the error"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must belong to the same company."
msgstr "Sva plaćanja u ovoj obradi moraju pripadati istom poduzeću."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must share the same payment method."
msgstr "Sva plaćanja u ovoj obradi moraju imati isti način plaćanja."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Amount"
msgstr "Iznos"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__amount_signed
msgid "Amount Signed"
msgstr "Potpisan iznos"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Raspoložive metode plaćanja"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "Banka"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr "Bankovni dnevnik"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr "Sadržaj obrade"

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr "Skupni depozit"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_payment.py:0
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
#, python-format
msgid "Batch Payment"
msgstr "Skupno plaćanje"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Batch Payments"
msgstr "Skupna plaćanja"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
msgid "Batch payment from which the file has been generated."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""
"Skupna plaćanja omogućuju grupiranje različitih plaćanja radi lakšeg\n"
"                    usklađivanja. Također su korisni prilikom polaganja depozita\n"
"                    u banku."

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard
msgid "Batch payments error reporting wizard"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard_line
msgid "Batch payments error reporting wizard line"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Cannot validate an empty batch. Please add some payments to it first."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Click here to download the generated file:"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Close"
msgstr "Zatvori"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "Šifra"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Create Batch Payment"
msgstr "Kreiraj skupno plaćanje"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr "Stvorite novo skupno plaćanje kupaca"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr "Kreirajte novo skupno plaćanje dobavljača"

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
msgid "Create batch payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "Kupac"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "Datum"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description"
msgstr "Opis"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description of the error"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Download export file"
msgstr "Preuzimanje izvežene datoteke"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__error_line_ids
msgid "Error Line"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__error_wizard_id
msgid "Error Wizard"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Exclude Payments"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Exported File"
msgstr "Izvežena datotka"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_file
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "Datoteka"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr "Omogućeno generiranje datoteke"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr "Naziv datoteke"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "File name"
msgstr "Naziv datoteke"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_file
msgid "Generated XML file"
msgstr "Generirana XML datoteka"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr "Datum generiranja"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr "Grupiraj po"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Help"
msgstr "Pomoć"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
msgid "ID"
msgstr "ID"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Id: %s"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__inbound
msgid "Inbound"
msgstr "Ulazni"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Inbound Batch Payments Sequence"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "Dnevnik"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "Memo"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__payment_method_name
msgid "Name"
msgstr "Naziv"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "Name of the generated XML file"
msgstr "Naziv generirane XML datoteke"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_payment__amount_signed
msgid "Negative value of amount field if payment_type is outbound"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__draft
msgid "New"
msgstr "Novi"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not reconciled"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__outbound
msgid "Outbound"
msgstr "Izlazni"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Outbound Batch Payments Sequence"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr "Način plaćanja"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment_method
msgid "Payment Methods"
msgstr "Načini plaćanja"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payment method"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "Plaćanja"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
msgid "Payments causing this error"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/wizard/batch_error.py:0
#, python-format
msgid "Payments in Error"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Payments must be posted to be added to a batch."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "Ispis"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Proceed with validation"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Re-generate Export File"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__reconciled
msgid "Reconciled"
msgstr "Zatvoreno"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "Vezna oznaka"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__sent
msgid "Sent"
msgstr "Poslano"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Set payments state to \"posted\"."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Show"
msgstr "Prikaži"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid "Show Remove Button"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid "Show Remove Options"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have already been matched with a bank statement."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have already been sent."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "Stanje"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch cannot be validated."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch could not be validated"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "The batch must have the same type as the payments it contains."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
msgid ""
"The batch payment generating the errors and warnings displayed in this "
"wizard."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr "Metoda plaćanja koja se koristi u ovom skupnom plaćanju"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "Ukupno"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid ""
"True if and only if the options to remove the payments causing the errors or"
" warnings from the batch should be shown"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr "Otvoren"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr "Odobri"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__warning_line_ids
msgid "Warning Line"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__warning_wizard_id
msgid "Warning Wizard"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid ""
"Whether or not this line should display a button allowing to remove its "
"related payments from the batch"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments that are not posted.\n"
"Payments:\n"
"%s"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments with zero amount in a Batch Payment.\n"
"Payments:\n"
"%s"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "auto ..."
msgstr "automatski..."
