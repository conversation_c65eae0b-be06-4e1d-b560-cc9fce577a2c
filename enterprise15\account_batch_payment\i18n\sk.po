# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_batch_payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_form_inherit_account_batch_payment
msgid "<span>Batch Payment</span>"
msgstr "<span>Dávková platba</span>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid ""
"<strong attrs=\"{'invisible': [('warning_line_ids', '=', [])]}\">The "
"following warnings were also raised; they do not impeach validation</strong>"
msgstr ""
"<strong attrs=\"{'invisible': [('warning_line_ids', '=', [])]}\">Boli "
"vznesené aj nasledujúce varovania; nespochybňujú validáciu</strong>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>Please first consider the following warnings</strong>"
msgstr "<strong>Najskôr zvážte nasledujúce varovania</strong>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>The following errors occurred</strong>"
msgstr ""

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_download_wizard
msgid "Account Batch download wizard"
msgstr "Sprievodca sťahovaním účtov"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Additional help message about the error"
msgstr "Ďalšia správa o chybe"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must belong to the same company."
msgstr "Všetky platby v dávke musia patriť tej istej spoločnosti."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must share the same payment method."
msgstr "Všetky platby v dávke musia zdieľať rovnaký spôsob platby."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Amount"
msgstr "Suma"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__amount_signed
msgid "Amount Signed"
msgstr "Podpísaná čiastka"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Dostupné spôsoby platby"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "Bankové doklady"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr "Banková účtovná kniha"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr "Dávkový obsah"

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr "Depozit šarže"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr "Správa o vklade dávky"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_payment.py:0
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
#, python-format
msgid "Batch Payment"
msgstr "Hromadná platba"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Batch Payments"
msgstr "Hromadné platby"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr "Typ dávky"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
msgid "Batch payment from which the file has been generated."
msgstr "Dávková platba, z ktorej bol vygenerovaný súbor."

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""
"Hromadné platby vám uľahčujú zoskupovanie rôznych platieb\n"
"                    párovania. Sú užitočné aj pri vklade šekov\n"
"                  do banky."

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard
msgid "Batch payments error reporting wizard"
msgstr "Sprievodca prehľadov chýb hromadných platieb"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard_line
msgid "Batch payments error reporting wizard line"
msgstr "Riadok sprievodcu prehľadov chýb hromadných platieb"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Cannot validate an empty batch. Please add some payments to it first."
msgstr ""
"Prázdnu dávku nie je možné overiť. Najskôr do nej pridajte nejaké platby."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Click here to download the generated file:"
msgstr "Kliknutím sem stiahnete vygenerovaný súbor:"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Close"
msgstr "Zatvoriť"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "Kód"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Create Batch Payment"
msgstr "Vytvorte hromadnú platbu"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr "Vytvorte novú dávkovú platbu zákazníka"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr "Vytvorte novú dávkovú platbu dodávateľa"

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
msgid "Create batch payment"
msgstr "Vytvorte hromadnú platbu"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr "Dátum vytvorenia súvisiaceho exportného súboru."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "Mena"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "Zákazník"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "Dátum"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description"
msgstr "Popis"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description of the error"
msgstr "Popis chyby"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Download export file"
msgstr "Stiahnite si exportný súbor"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__error_line_ids
msgid "Error Line"
msgstr "Chybový riadok"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__error_wizard_id
msgid "Error Wizard"
msgstr "Sprievodca chybami"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Exclude Payments"
msgstr "Vylúčte platby"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr "Exportujte súbor súvisiaci s touto dávkou"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Exported File"
msgstr "Exportovaný súbor"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_file
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "Súbor"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr "Generovanie súborov je povolené"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr "Názov súboru"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "File name"
msgstr "Názov súboru"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_file
msgid "Generated XML file"
msgstr "Vygenerovaný súbor XML"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr "Dátum generovania"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Help"
msgstr "Pomoc"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
msgid "ID"
msgstr "ID"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Id: %s"
msgstr "Id: %s"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__inbound
msgid "Inbound"
msgstr "Prichádzajúce"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Inbound Batch Payments Sequence"
msgstr "Postupnosť dávkových dávok"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "Účtovný denník"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "Pripomienka"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__payment_method_name
msgid "Name"
msgstr "Meno"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr "Názov exportovaného súboru vygenerovaného pre túto dávku"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "Name of the generated XML file"
msgstr "Názov vygenerovaného súboru XML"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_payment__amount_signed
msgid "Negative value of amount field if payment_type is outbound"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__draft
msgid "New"
msgstr "Nové"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not reconciled"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__outbound
msgid "Outbound"
msgstr "Odchádzajúce"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Outbound Batch Payments Sequence"
msgstr "Poradie odchádzajúcich dávkových platieb"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr "Metóda platby"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment_method
msgid "Payment Methods"
msgstr "Platobné metódy"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payment method"
msgstr "Spôsob platby"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "Platby"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
msgid "Payments causing this error"
msgstr "Platby spôsobujúce túto chybu"

#. module: account_batch_payment
#: code:addons/account_batch_payment/wizard/batch_error.py:0
#, python-format
msgid "Payments in Error"
msgstr "Platby omylom"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Payments must be posted to be added to a batch."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "Tlač"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr "Tlač hromadnej platby"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Proceed with validation"
msgstr "Pokračujte s overením"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Re-generate Export File"
msgstr "Znova vygenerujte exportný súbor"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__reconciled
msgid "Reconciled"
msgstr "Zosúladené"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "Referencia"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__sent
msgid "Sent"
msgstr "Poslané"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Set payments state to \"posted\"."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Show"
msgstr "Zobraziť"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid "Show Remove Button"
msgstr "Zobraziť tlačidlo Odstrániť"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid "Show Remove Options"
msgstr "Zobraziť možnosti odstránenia"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have already been matched with a bank statement."
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have already been sent."
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "Štát"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch cannot be validated."
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch could not be validated"
msgstr "Dávku sa nepodarilo overiť"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr "Dávka musí mať rovnaký spôsob platby ako platby, ktoré obsahuje."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "The batch must have the same type as the payments it contains."
msgstr "Dávka musí mať rovnaký typ ako platby, ktoré obsahuje."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
msgid ""
"The batch payment generating the errors and warnings displayed in this "
"wizard."
msgstr ""
"Dávková platba generujúca chyby a varovania zobrazené v tomto sprievodcovi."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr "Denník dávkovej platby a platieb, ktoré obsahuje, musí byť rovnaký."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr "Spôsob platby použitý pri platbách v tejto dávke."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "Celkom"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid ""
"True if and only if the options to remove the payments causing the errors or"
" warnings from the batch should be shown"
msgstr ""
"Platí vtedy a len vtedy, ak by sa mali zobraziť možnosti odstránenia platieb"
" spôsobujúcich chyby alebo varovania z dávky"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr "Nezladené"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr "Potvrdiť"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__warning_line_ids
msgid "Warning Line"
msgstr "Varovná čiara"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__warning_wizard_id
msgid "Warning Wizard"
msgstr "Sprievodca varovaním"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""
"Či má alebo nemá táto dávková platba vo formulári zobraziť tlačidlo "
"„Vytvoriť súbor“ namiesto „Tlačiť“."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid ""
"Whether or not this line should display a button allowing to remove its "
"related payments from the batch"
msgstr ""
"To, či sa na tomto riadku má zobrazovať tlačidlo umožňujúce odstrániť z "
"dávky súvisiace platby"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments that are not posted.\n"
"Payments:\n"
"%s"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments with zero amount in a Batch Payment.\n"
"Payments:\n"
"%s"
msgstr ""
"V hromadnej platbe nemôžete pridať platby s nulovou čiastkou.\n"
"Platby:\n"
"%s"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "auto ..."
msgstr "auto ..."
