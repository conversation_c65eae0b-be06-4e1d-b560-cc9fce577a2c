<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_print_batch_payment" model="ir.actions.report">
        <field name="name">Print Batch Payment</field>
        <field name="model">account.batch.payment</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">account_batch_payment.print_batch_payment</field>
        <field name="report_file">account_batch_payment.print_batch_payment</field>
        <field name="paperformat_id" ref="base.paperformat_batch_deposit"/>
        <field name="binding_model_id" ref="model_account_batch_payment"/>
        <field name="binding_type">report</field>
    </record>
</odoo>