<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Budgets -->
    <data noupdate="1">
        <!-- Optimistic -->
        <record id="crossovered_budget_budgetoptimistic0" model="crossovered.budget">
            <field eval="'Budget '+str(datetime.now().year+1)+': Optimistic'" name="name"/>
            <field eval="str(datetime.now().year+1)+'-01-01'" name="date_from"/>
            <field eval="&quot;&quot;&quot;draft&quot;&quot;&quot;" name="state"/>
            <field eval="str(datetime.now().year+1)+'-12-31'" name="date_to"/>
            <field name="user_id" ref="base.user_admin"/>
        </record>
         <record id="budget_line_analytic_admin" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-01-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-12-31'" name="date_to"/>
            <field name="planned_amount">-35000</field>
            <field name="analytic_account_id" ref="analytic.analytic_administratif"/>
        </record>
         <record id="budget_line_analytic_agrolait1" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-01-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-01-31'" name="date_to"/>
            <field name="planned_amount">10000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait2" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-02-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-02-28'" name="date_to"/>
            <field name="planned_amount">10000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait3" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-03-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-03-31'" name="date_to"/>
            <field name="planned_amount">12000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait4" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-04-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-04-30'" name="date_to"/>
            <field name="planned_amount">15000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait5" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-05-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-05-31'" name="date_to"/>
            <field name="planned_amount">15000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait6" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-06-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-06-30'" name="date_to"/>
            <field name="planned_amount">15000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait7" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-07-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-07-31'" name="date_to"/>
            <field name="planned_amount">13000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait8" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-08-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-08-31'" name="date_to"/>
            <field name="planned_amount">9000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait9" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-09-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-09-30'" name="date_to"/>
            <field name="planned_amount">8000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait10" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-10-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-10-31'" name="date_to"/>
            <field name="planned_amount">15000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait11" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-11-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-11-30'" name="date_to"/>
            <field name="planned_amount">15000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait12" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetoptimistic0" />
            <field eval="str(datetime.now().year+1)+'-12-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-12-31'" name="date_to"/>
            <field name="planned_amount">18000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>

        <!-- pessimistic-->
        <record id="crossovered_budget_budgetpessimistic0" model="crossovered.budget">
            <field eval="'Budget '+str(datetime.now().year+1)+': Pessimistic'" name="name"/>
            <field eval="str(datetime.now().year+1)+'-01-01'" name="date_from"/>
            <field eval="&quot;&quot;&quot;draft&quot;&quot;&quot;" name="state"/>
            <field eval="str(datetime.now().year+1)+'-12-31'" name="date_to"/>
            <field name="user_id" ref="base.user_admin"/>
        </record>

         <record id="budget_line_analytic_admin_pessim" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-01-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-12-31'" name="date_to"/>
            <field name="planned_amount">-55000</field>
            <field name="analytic_account_id" ref="analytic.analytic_administratif"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim1" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-01-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-01-31'" name="date_to"/>
            <field name="planned_amount">9000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim2" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-02-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-02-28'" name="date_to"/>
            <field name="planned_amount">8000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim3" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-03-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-03-31'" name="date_to"/>
            <field name="planned_amount">10000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim4" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-04-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-04-30'" name="date_to"/>
            <field name="planned_amount">14000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim5" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-05-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-05-31'" name="date_to"/>
            <field name="planned_amount">16000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim6" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-06-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-06-30'" name="date_to"/>
            <field name="planned_amount">13000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim7" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-07-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-07-31'" name="date_to"/>
            <field name="planned_amount">10000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim8" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-08-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-08-31'" name="date_to"/>
            <field name="planned_amount">8000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim9" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-09-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-09-30'" name="date_to"/>
            <field name="planned_amount">7000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim10" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-10-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-10-31'" name="date_to"/>
            <field name="planned_amount">12000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim11" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-11-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-11-30'" name="date_to"/>
            <field name="planned_amount">17000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
         <record id="budget_line_analytic_agrolait_pessim12" model="crossovered.budget.lines">
            <field name="crossovered_budget_id" ref="crossovered_budget_budgetpessimistic0" />
            <field eval="str(datetime.now().year+1)+'-12-01'" name="date_from"/>
            <field eval="str(datetime.now().year+1)+'-12-31'" name="date_to"/>
            <field name="planned_amount">17000</field>
            <field name="analytic_account_id" ref="analytic.analytic_agrolait"/>
        </record>
    </data>
</odoo>
