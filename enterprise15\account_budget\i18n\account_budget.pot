# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_budget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 11:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr ""

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "<span class=\"o_stat_text\">Budget</span>"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_ids
msgid "Activities"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_state
msgid "Activity State"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_group_id
msgid "Analytic Group"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__name
msgid "Budget Name"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr ""

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "From"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Future Activities"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Late Activities"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__state
msgid "Status"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoretical Amount"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Theoretical amount"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "To"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Today Activities"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__total_planned_amount
msgid "Total Planned Amount"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread
msgid "Unread Messages"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "e.g. Budget 2021: Optimistic"
msgstr ""
