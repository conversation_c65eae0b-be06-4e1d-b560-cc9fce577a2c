# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_budget
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "يجب أن تتضمن فترة الميزانية \"تاريخ انتهاء\" بند الميزانية "

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "يجب أن تتضمن فترة الميزانية \"تاريخ بدء\" بند الميزانية "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"الفترة \"/>"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "<span class=\"o_stat_text\">Budget</span>"
msgstr "<span class=\"o_stat_text\">الميزانية</span> "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr "الحسابات"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr "إنجاز "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr "بحاجة إلى اتخاذ إجراء "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr "الإيرادات/المصروفات الفعلية."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr "المبلغ الذي يُفترض أن تكون ربحته/أنفقته في هذا التاريخ."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr ""
"المبلغ الذي تخطط لكسبه/إنفاقه. قم بالتسجيل كقيمة موجبة إذا كان إيراداً "
"وسالبة إذا كانت تكلفة. "

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_group_id
msgid "Analytic Group"
msgstr "مجموعة تحليلية"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr "موافقة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr "الميزانية "

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "عناصر الميزانية "

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr "بند الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr "بنود الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__name
msgid "Budget Name"
msgstr "اسم الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr "حالة الميزانية "

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "وضع الميزانية "

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "أوضاع الميزانية "

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr "الميزانيات "

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr "تحليل الميزانيات "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "إلغاء الميزانية "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr "ملغي"

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr "اضغط لإنشاء ميزانية جديدة. "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr "الشركة "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr ""
"المقارنة بين المبلغ الفعلي والنظري. تحدد إذا ما كنت قد تخطيت الميزانية أو لا"
" تزال تحتها. "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "تأكيد"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr "مؤكد"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr "العملة"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Date"
msgstr "التاريخ"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "منتهي "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr "مسودة"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr "ميزانية بحالة المسودة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr "القيود..."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "From"
msgstr "من"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr "المُعرف"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr "يتخطى الميزانية "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr "الاسم"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr "غير ملغي "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل التي بها خطأ في التسليم "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr "تاريخ السداد"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "الفترة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "المبلغ المخطط"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr "المبلغ المخطط"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "المبلغ الفعلي"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr "المبلغ الفعلي"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr "إعادة التعيين لحالة مسودة"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr "المسؤول "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Show all records which has next action date is before today"
msgstr "عرض كافة السجلات المُعين لها تاريخ إجراء تالي يسبق تاريخ اليوم الجاري"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__state
msgid "Status"
msgstr "الحالة"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "يجب أن تحتوي الميزانية على حساب واحد على الأقل. "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoretical Amount"
msgstr "المبلغ النظري "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Theoretical amount"
msgstr "المبلغ النظري "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "To"
msgstr "إلى"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr "للموافقة "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr "للموافقة على الميزانية "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Today Activities"
msgstr "نشاطات اليوم"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__total_planned_amount
msgid "Total Planned Amount"
msgstr "المبلغ الكلي المخطط "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى المسجل. "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل غير المقروءة "

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"استخدم الميزانيات لمقارنة الإيرادات الفعلية مع الإيرادات المتوقعة والتكاليف "

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr "تم التصديق "

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr ""
"يجب أن تُدرج على الأقل وضع ميزانية أو حسابًا تحليلًا واحدًا في بند "
"الميزانية. "

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "e.g. Budget 2021: Optimistic"
msgstr "مثال: ميزانية 2021: واعدة "
