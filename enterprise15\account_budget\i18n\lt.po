# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_budget
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Anatolij, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# Linas V<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>au<PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Ramunė ViaLaurea <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr ""
"Biudžeto eilutės \"pabaigos data\" turėtų būti įtraukta į biudžeto "
"laikotarpį"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr ""
"Biudžeto eilutės \"pradžios data\" turėtų būti įtraukta į biudžeto "
"laikotarpį"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "<span class=\"o_stat_text\">Budget</span>"
msgstr "<span class=\"o_stat_text\">Biudžetas</span>"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr "Sąskaitos"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr "Pasiekimas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_ids
msgid "Activities"
msgstr "Veiklos"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos Išimties Dekoravimas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo ikona"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr "Iš tikrųjų gauta/išleista suma."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr "Suma, kurią turėjote būti gavęs/išleidęs šiuo metu."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr ""
"Suma, kurią planuojate gauti/išleisti. Įrašykite teigiamą sumą, jei tai yra "
"pajamos ir neigiamą, jei tai išlaidos."

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "Analitinė sąskaita"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_group_id
msgid "Analytic Group"
msgstr "Analitinė grupė"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr "Patvirtinti"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr "Biudžetas"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "Biudžeto dedamosios"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr "Biudžeto eilutė"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr "Biudžeto eilutės"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__name
msgid "Budget Name"
msgstr "Biudžeto pavadinimas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr "Biudžeto būsena"

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "Biudžeto pozicija"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "Biudžetinės pozicijos"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr "Biudžetai"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr "Biudžeto analizė"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "Atšaukti biudžetą"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr "Atšauktas"

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr "Norėdami sukurti naują biudžetą, paspauskite."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr "Įmonė"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr ""
"Palyginimas tarp praktinės ir teorinės sumos. Šis matavimas pasako, ar esate"
" aukščiau, ar žemiau biudžeto."

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "Patvirtinti"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr "Patvirtinti"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr "Valiuta"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Date"
msgstr "Data"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "Atlikta"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr "Juodraštis"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr "Juodraštiniai biudžetai"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr "Pabaigos data"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr "Įrašai..."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome piktograma, pvz., fa-tasks"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "From"
msgstr "Nuo"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Future Activities"
msgstr "Būsimos veiklos"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr "ID"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr "Yra virš biudžeto"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Late Activities"
msgstr "Vėluojančios veiklos"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Veiklos paskutinis terminas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr "Pavadinimas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr "Neatšaukta"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr "Apmokėjimo data"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "Laikotarpis"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "Suplanuota suma"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr "Suplanuota suma"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "Praktinė suma"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr "Praktinė suma"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr "Atkurti kaip juodraštį"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr "Atsakingas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS pristatymo klaida"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Rodyti visus įrašus, kurių sekančio veiksmo data yra ankstesnė nei šiandiena"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr "Pradžios data"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__state
msgid "Status"
msgstr "Būsena"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "Biudžetas turi turėti bent vieną sąskaitą."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoretical Amount"
msgstr "Teorinė suma"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Theoretical amount"
msgstr "Teoretinė suma"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "To"
msgstr " Kam"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr "Reikia patvirtinti"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr "Patvirtinti biudžetus"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Today Activities"
msgstr "Šiandienos veiklos"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__total_planned_amount
msgid "Total Planned Amount"
msgstr "Iš viso suplanuota suma"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""
"Naudokite biudžetus, kad palygintumėte planuojamas pajamas ir išlaidas su "
"realiomis"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr "Patvirtinta"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr "Interneto svetainės žinutės"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr "Svetainės komunikacijos istorija"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr ""
"Biudžeto eilutėje turite įvesti bent vieną biudžeto poziciją ar analitinę "
"sąskaitą."

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "e.g. Budget 2021: Optimistic"
msgstr ""
