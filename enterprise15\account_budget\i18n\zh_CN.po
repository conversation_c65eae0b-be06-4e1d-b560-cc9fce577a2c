# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_budget
# 
# Translators:
# <PERSON>, 2021
# xu aaron, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"End Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "预算项目的“结束日期”应包含在预算期间"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"\"Start Date\" of the budget line should be included in the Period of the "
"budget"
msgstr "预算项目的“开始日期”应包含在预算期间"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "<span class=\"o_stat_text\">Budget</span>"
msgstr "<span class=\"o_stat_text\">预算</span>"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__account_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
msgid "Accounts"
msgstr "科目"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__percentage
msgid "Achievement"
msgstr "成就"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction
msgid "Action Needed"
msgstr "需要采取动作"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_ids
msgid "Activities"
msgstr "活动"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常勋章"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图表"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__practical_amount
msgid "Amount really earned/spent."
msgstr "实际赚到/消费的金额."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__theoritical_amount
msgid "Amount you are supposed to have earned/spent at this date."
msgstr "您应该在此日期获得/消费的金额。"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__planned_amount
msgid ""
"Amount you plan to earn/spend. Record a positive amount if it is a revenue "
"and a negative amount if it is a cost."
msgstr "您计划赚到或消费的金额。如果它是一个收入和一个负数，如果它是一个成本，记录一个正的量。"

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_account_id
msgid "Analytic Account"
msgstr "分析账户"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__analytic_group_id
msgid "Analytic Group"
msgstr "分析组"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Approve"
msgstr "批准"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Budget"
msgstr "预算"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_account_analytic_account_cb_lines
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Budget Items"
msgstr "预算项"

#. module: account_budget
#: model:ir.model,name:account_budget.model_crossovered_budget_lines
msgid "Budget Line"
msgstr "预算明细"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__crossovered_budget_line
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__crossovered_budget_line
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_tree
msgid "Budget Lines"
msgstr "预算明细"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__name
msgid "Budget Name"
msgstr "预算名称"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__crossovered_budget_state
msgid "Budget State"
msgstr "预算状态"

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_budget_post
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__general_budget_id
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_post_tree
msgid "Budgetary Position"
msgstr "预算状况"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.open_budget_post_form
#: model:ir.ui.menu,name:account_budget.menu_budget_post_form
msgid "Budgetary Positions"
msgstr "预算状况"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Budgets"
msgstr "预算"

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_crossovered_budget_lines_view
#: model:ir.ui.menu,name:account_budget.menu_act_crossovered_budget_lines_view
msgid "Budgets Analysis"
msgstr "预算辅助核算"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Cancel Budget"
msgstr "取消预算"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Click to create a new budget."
msgstr "点击以创建一个新的预算。"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__company_id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__company_id
msgid "Company"
msgstr "公司"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget_lines__percentage
msgid ""
"Comparison between practical and theoretical amount. This measure tells you "
"if you are below or over budget."
msgstr "实际量与理论量进行比较。如果您低于或超过预算，这项措施会告诉您。"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Confirm"
msgstr "确认"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__confirm
msgid "Confirmed"
msgstr "已确认"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_uid
msgid "Created by"
msgstr "创建人"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__create_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__create_date
msgid "Created on"
msgstr "创建时间"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__currency_id
msgid "Currency"
msgstr "币种"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Date"
msgstr "日期"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__display_name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Done"
msgstr "完成"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft"
msgstr "草稿"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Draft Budgets"
msgstr "预算草稿"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_to
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_to
msgid "End Date"
msgstr "终止日期"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Entries..."
msgstr "分录..."

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 图标 例如代码 fa-tasks"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "From"
msgstr "从"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Future Activities"
msgstr "未来活动"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Group By"
msgstr "分组"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__has_message
msgid "Has Message"
msgstr "有信息"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__id
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__id
msgid "ID"
msgstr "ID"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示异常活动的图标。"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__is_above_budget
msgid "Is Above Budget"
msgstr "是否超预算"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget____last_update
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_uid
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__write_date
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Late Activities"
msgstr "最近的活动"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_ids
msgid "Messages"
msgstr "消息"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_budget_post__name
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__name
msgid "Name"
msgstr "名称"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一个活动日历事件"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_search
msgid "Not Cancelled"
msgstr "未取消"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要采取动作消息数量"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__paid_date
msgid "Paid Date"
msgstr "支付日期"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Period"
msgstr "期间"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__planned_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Planned Amount"
msgstr "计划金额"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Planned amount"
msgstr "计划金额"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__practical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Practical Amount"
msgstr "实际金额"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Practical amount"
msgstr "实际金额"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "Reset to Draft"
msgstr "重置为草稿"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__user_id
msgid "Responsible"
msgstr "负责人"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__activity_user_id
msgid "Responsible User"
msgstr "负责用户"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Show all records which has next action date is before today"
msgstr "显示所有的在今天之前的下一个动作日期的记录"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__date_from
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__date_from
msgid "Start Date"
msgstr "开始日期"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__state
msgid "Status"
msgstr "状态"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 计划：未来活动。"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid "The budget must have at least one account."
msgstr "预算应该至少有一个科目"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget_lines__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
msgid "Theoretical Amount"
msgstr "理论金额"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_line_pivot
msgid "Theoretical amount"
msgstr "理论金额"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "To"
msgstr "至"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve"
msgstr "待批准"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "To Approve Budgets"
msgstr "待批准的预算"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_crossovered_budget_search
msgid "Today Activities"
msgstr "今天的活动"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__total_planned_amount
msgid "Total Planned Amount"
msgstr "总计划金额"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的异常活动类型。"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_crossovered_budget_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr "使用预算来比较实际与预期的收入和成本"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__crossovered_budget__state__validate
msgid "Validated"
msgstr "已验证"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_crossovered_budget__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_crossovered_budget__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: account_budget
#: code:addons/account_budget/models/account_budget.py:0
#, python-format
msgid ""
"You have to enter at least a budgetary position or analytic account on a "
"budget line."
msgstr "您必须在预算项目上至少输入预算状况或分析账户。"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.crossovered_budget_view_form
msgid "e.g. Budget 2021: Optimistic"
msgstr "例如2021 年预算：乐观"
