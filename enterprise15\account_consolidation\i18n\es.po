# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_consolidation
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids_count
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_account_ids_count
msgid "# Accounts"
msgstr "# Cuentas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids_count
msgid "# Groups"
msgstr "# Grupos"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids_count
msgid "# Journals"
msgstr "# Diarios"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids_count
msgid "# Periods"
msgstr "# Períodos"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Control"
msgstr "% Control"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Ownership"
msgstr "% Propiedad"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "%<br/>"
msgstr "%<br/>"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#, python-format
msgid "%s (%s Currency Conversion Method)"
msgstr "%s (%sMétodo de conversión de moneda)"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "%s Consolidated Accounting"
msgstr "%sContabilidad consolidada"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "&amp;nbsp;"
msgstr "&nbsp;  "

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "(Re)Compute"
msgstr "(Re)Calcular"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "(Re)compute"
msgstr "(Re)Calcular"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "/ End Rate: 1"
msgstr "/ Tasa de término: 1"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_period_comparisons
msgid ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparison"
msgstr ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                    Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"                    Diarios:"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "<span class=\"o_form_label oe_inline\">%</span>"
msgstr "<span class=\"o_form_label oe_inline\">%</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">Actions</span>"
msgstr "<span role=\"separator\">Acciones</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">View</span>"
msgstr "<span role=\"separator\">Vista</span>"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_account_code_uniq
msgid ""
"A consolidation account with the same code already exists in this "
"consolidation."
msgstr ""
"Ya existe una cuenta de consolidación con el mismo código en esta "
"consolidación."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"A journal entry should only be linked to a company period OR to a analysis "
"period of another consolidation !"
msgstr ""
"¡Una entrada de diario debe estar vinculada a solo un período de la empresa "
"O a un período de análisis de otra consolidación!"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_account
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__line_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Account"
msgstr "Cuenta"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__account_consolidation_currency_is_different
msgid "Account Consolidation Currency Is Different"
msgstr "La moneda de la cuenta de consolidación es diferente"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Group"
msgstr "Grupo de cuenta"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_group_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_account_sections
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Groups"
msgstr "Grupos de cuentas"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_action
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_from_period_action
msgid "Account Mapping"
msgstr "Mapeo de cuentas"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid "Account Mapping: %(chart)s"
msgstr "Mapeo de cuentas: %(chart)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Account Mapping: %(company)s"
msgstr "Mapeo de cuentas: %(company)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Account Mapping: %s (for %s)"
msgstr "Mapeo de cuenta: %s (para %s)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Account Name"
msgstr "Nombre de la cuenta"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_consolidation_trial_balance_report
msgid "Account consolidation trial balance report"
msgstr "Prueba de informe de saldos de la cuenta"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
msgid "Account with Entries"
msgstr "Cuenta con entradas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Accounts"
msgstr "Cuentas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_color
msgid "Accounts color"
msgstr "Color de cuenta"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Active"
msgstr "Activo"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Add a column"
msgstr "Agregar una columna"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Additional Information"
msgstr "Información adicional"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Advanced Consolidation"
msgstr "Consolidación avanzada"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid "All"
msgstr "Todos"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Already Mapped"
msgstr "Ya mapeado"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__amount
msgid "Amount"
msgstr "Importe"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid ""
"An account group can only have accounts or other groups children but not "
"both !"
msgstr ""
"¡Un grupo de cuentas solo puede tener cuentas u otros grupos subordinados "
"pero no ambos!"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Analysis Period"
msgstr "Período de análisis"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_period_id
msgid "Analysis Period Using This"
msgstr "Período de análisis utilizando este"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.analysis_period_config_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_tree
msgid "Analysis Periods"
msgstr "Períodos de análisis"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period"
msgstr "Periodo de análisis"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period created !"
msgstr "¡Período de análisis creado!"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Apply"
msgstr "Aplicar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_attachment_count
msgid "Attachment Count"
msgstr "Nº de archivos adjuntos"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Audit"
msgstr "Auditar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Auto-generated"
msgstr "Generado automáticamente"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__auto_generated
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__auto_generated
msgid "Automatically Generated"
msgstr "Generado automáticamente"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_avg
msgid "Average Currency Rate"
msgstr "Tasa monetaria promedio"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__avg
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Average Rate"
msgstr "Tasa promedio"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Avg Rate: 1"
msgstr "Tasa promedio: 1"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__balance
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Balance"
msgstr "Balance"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Cancel"
msgstr "Cancelar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__chart_id
msgid "Chart"
msgstr "Gráfica"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of Accounts"
msgstr "Plan contable"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of account set !"
msgstr "¡Configura el plan de cuentas!"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Chart of accounts"
msgstr "Plan contable"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_tree
msgid "Charts"
msgstr "Gráficas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__child_ids
msgid "Children"
msgstr "Subordinados"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Close"
msgstr "Cerrar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Close period"
msgstr "Período cerrado"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__closed
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__closed
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Closed"
msgstr "Cerrado"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__end
msgid "Closing Rate"
msgstr "Tasa de cierre"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__code
msgid "Code"
msgstr "Código"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_color
msgid "Color"
msgstr "Color"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__color
msgid "Color Index"
msgstr "Índice de color"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_res_company
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__company_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__company_ids
msgid "Companies"
msgstr "Compañías"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__company_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Company"
msgstr "Compañía"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_company_id
msgid "Company Currency"
msgstr "Moneda de la empresa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Company Name"
msgstr "Nombre de la empresa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__company_period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Company Period"
msgstr "Período de la empresa"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_company_period_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_period_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_company_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Company Periods"
msgstr "Períodos de la empresa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_unmapped_accounts_counts
msgid "Company Unmapped Accounts Counts"
msgstr "Número de cuentas sin mapear de la empresa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_period_id
msgid "Composed Analysis Period"
msgstr "Período de análisis compuesto"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_chart_currency_id
msgid "Composed Consolidation Currency"
msgstr "Moneda de la consolidación compuesta"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Conso Rate:"
msgstr "Tasa conso:"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__account_id
msgid "Consolidated Account"
msgstr "Cuenta de consolidación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Consolidated Accounts"
msgstr "Cuentas consolidadas"

#. module: account_consolidation
#: model:ir.actions.client,name:account_consolidation.trial_balance_report_action
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Consolidated Balance"
msgstr "Balance consolidado"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Consolidated Companies"
msgstr "Empresas consolidadas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__parents_ids
msgid "Consolidated In"
msgstr "Consolidado en"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Consolidated balance"
msgstr "Balance consolidado"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__used_in_ids
msgid "Consolidated in"
msgstr "Consolidado en"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action_onboarding
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__chart_id
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_charts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation"
msgstr "Consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Account"
msgstr "Cuenta de consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_chart_filtered_ids
msgid "Consolidation Account Chart Filtered"
msgstr "Gráfico filtrado de la cuenta de consolidación"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_account_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__using_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_accounts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Accounts"
msgstr "Cuentas de consolidación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation Chart"
msgstr "Gráfico de consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_company_period
msgid "Consolidation Company Period"
msgstr "Período de consolidación de la empresa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_chart_id
msgid "Consolidation Currency"
msgstr "Moneda de consolidación"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.Consolidation_journal_line_action
msgid "Consolidation Entries"
msgstr "Entradas de consolidación"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations_consolidation_entries
msgid "Consolidation Entry"
msgstr "Entrada de consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_group
msgid "Consolidation Group"
msgstr "Grupo de consolidación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Consolidation Items"
msgstr "Elementos de consolidación "

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal
msgid "Consolidation Journal"
msgstr "Diario de consolidación "

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_move_line__consolidation_journal_line_ids
msgid "Consolidation Journal Line"
msgstr "Línea de diario de consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__consolidation_method
msgid "Consolidation Method"
msgstr "Método de consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_name
msgid "Consolidation Name"
msgstr "Nombre de la consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__composition_id
msgid "Consolidation Period"
msgstr "Período de consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period_composition
msgid "Consolidation Period Composition"
msgstr "Estructura del período de consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_rate
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Consolidation Rate"
msgstr "Tasa de consolidación "

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Consolidation Rate (%)"
msgstr "Tasa de consolidación (%)"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_account
msgid "Consolidation account"
msgstr "Cuenta de consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_chart
msgid "Consolidation chart"
msgstr "Gráfica de consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__line_ids
msgid "Consolidation items"
msgstr "Elementos de consolidación"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal_line
msgid "Consolidation journal line"
msgstr "Línea del elemento de consolidación"

#. module: account_consolidation
#: model:res.groups,name:account_consolidation.group_consolidation_user
msgid "Consolidation user"
msgstr "Usuario de consolidación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create"
msgstr "Crear"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action_onboarding
msgid "Create First Period"
msgstr "Crear primer período"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create your first analysis period &amp; set the currency rates."
msgstr "Crea tu primer período de análisis y configura el tipo de moneda."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currencies_are_different
msgid "Currencies Are Different"
msgstr "Las monedas son diferentes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Currency"
msgstr "Moneda"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__currency_amount
msgid "Currency Amount"
msgstr "Cantidad de moneda"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__currency_mode
msgid "Currency Conversion Method"
msgstr "Método de conversión de moneda"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currency_rate
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "Currency Rate"
msgstr "Tipo de moneda"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__currency_rate
msgid "Currency rate from composed chart currency to using chart currency"
msgstr ""
"Cambio de cambio de gráfica compuesta de moneda a uso de gráfica de moneda"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_home
msgid "Dashboard"
msgstr "Tablero"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__dashboard_sections
msgid "Dashboard Sections"
msgstr "Secciones del tablero"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Date"
msgstr "Fecha"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Define"
msgstr "Definir"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid ""
"Define the companies that should be consolidated &amp; the target currency"
msgstr "Define las empresas que deben consolidarse y la moneda de destino."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__note
msgid "Description"
msgstr "Descripción"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_dates
msgid "Display Dates"
msgstr "Mostrar fechas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__done
msgid "Done"
msgstr "Hecho"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__draft
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Draft"
msgstr "Borrador"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid "End Currency Rate"
msgstr "Cierre de tipo de moneda"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_end
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "End Date"
msgstr "Fecha final"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "End Rate"
msgstr "Tasa final"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__equity
msgid "Equity"
msgstr "Capital"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__exclude_journal_ids
msgid "Exclude Journals"
msgstr "Excluir asientos"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Export (XLSX)"
msgstr "Exportar (XLSX)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__filtered_used_in_ids
msgid "Filtered Used In"
msgstr "Filtros utilizados en"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Folded"
msgstr "Fallido"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__full_name
msgid "Full Name"
msgstr "Nombre completo"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__full
msgid "Full consolidation"
msgstr "Consolidación completa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__group_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__group_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Group"
msgstr "Grupo"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Group Name"
msgstr "Nombre del grupo"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Groups"
msgstr "Grupos"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__hist
msgid "Historical Rate"
msgstr "Historial de tasa"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Historical Rates"
msgstr "Historial de tasas"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Historical Rates: %(company)s"
msgstr "Historial de tasas: %(company)s"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_avg
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid ""
"How many units of company currency is needed to get 1 unit of chart currency"
msgstr ""
"¿Cuántas unidades de la moneda de la empresa se necesitan para obtener 1 "
"unidad de moneda en el gráfico?"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__id
msgid "ID"
msgstr "Identificación"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado hay nuevos mensajes que requieren tu atención."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Future"
msgstr "En el futuro"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Past"
msgstr "En el pasado"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal"
msgstr "Diario"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal Item"
msgstr "Apunte contable"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.view_account_move_line_filter
msgid "Journal Items"
msgstr "Apuntes contables"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__line_ids
msgid "Journal lines"
msgstr "Líneas de diario"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_journal_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Journals"
msgstr "Diarios contables"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__just_done
msgid "Just done"
msgstr "Recientemente realizado"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Manually Created"
msgstr "Creado manualmente"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Map Accounts"
msgstr "Esquema de cuentas"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Mapped Accounts"
msgstr "Cuentas mapeadas "

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree_mapping
msgid "Mapped In"
msgstr "Mapeadas en"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error
msgid "Message Delivery error"
msgstr "Error en envío de mensaje"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__move_line_ids
msgid "Move Line"
msgstr "Línea de movimiento"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Name"
msgstr "Nombre"

#. module: account_consolidation
#: model_terms:ir.actions.act_window,help:account_consolidation.account_mapping_action
msgid ""
"No accounts have been found. Make sure you have installed a chart of account"
" for this company or that you have access right to see the accounts of this "
"company."
msgstr ""
"No se encontraron cuentas. Asegurate de instalar un plan de cuentas para "
"esta empresa, o de que tienes derecho de acceso para ver las cuentas de esta"
" compañía."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Not Mapped"
msgstr "Sin mapear"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__none
msgid "Not consolidated"
msgstr "Sin consolidar"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__not_done
msgid "Not done"
msgstr "Sin realizar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods containing today"
msgstr "Solo los períodos que incluyen el día de hoy"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the future"
msgstr "Sólo períodos futuros"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the past"
msgstr "Sólo períodos en el pasado"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations
msgid "Operations"
msgstr "Operaciones"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__originating_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_originating_currency_id
msgid "Originating Currency"
msgstr "Moneda procedente"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
msgid "Parent"
msgstr "Principal"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_path
msgid "Parent Path"
msgstr "Ruta principal"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__period_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Period"
msgstr "Período"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_analysis_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Periods"
msgstr "Períodos"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Print Preview"
msgstr "Vista previa de impresión"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__proportional
msgid "Proportional consolidation"
msgstr "Consolidación proporcional"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__rate
msgid "Rate"
msgstr "Tasa"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_control
msgid "Rate Control"
msgstr "Tasa de control"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_ownership
msgid "Rate Ownership"
msgstr "Tasa de propiedad "

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_rate_action
msgid "Rate Ranges"
msgstr "Rangos de tasa"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Reopen period"
msgstr "Reabrir período"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Reset To Draft"
msgstr "Restablecer como borrador"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Review Chart Of Accounts"
msgstr "Revisar plan de cuentas"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Scope of Consolidation defined !"
msgstr "¡Alcance definido de la consolidación!"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__sequence
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Settings"
msgstr "Ajustes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Setup"
msgstr "Configurar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid ""
"Setup your consolidated accounts and their currency conversion method.\n"
"                Then map them with the companies accounts."
msgstr ""
"Configura las cuentas consolidadas y sus métodos de conversión de moneda. \n"
"             Luego mapea estas con las cuentas de las empresas."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_control
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_ownership
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Should be between 0 and 100 %"
msgstr "Debe estar entre 0 y 100 %"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__show_on_dashboard
msgid "Show On Dashboard"
msgstr "Mostrar en el tablero"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_conso_extra_options
msgid "Show Zero Balance Accounts"
msgstr "Mostrar cuentas con balance de cero"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_start
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__state
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__state
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "State"
msgstr "Estado"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_dashboard_onboarding_state
msgid "State Of The Account Consolidation Dashboard Onboarding Panel"
msgstr ""
"Estado del tablero de la cuenta de consolidación en el panel de integración"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_ccoa_state
msgid "State Of The Onboarding Consolidated Chart Of Account Step"
msgstr ""
"Estado del tablero de la cuenta de consolidación de las etapas de la cuenta"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_consolidation_state
msgid "State Of The Onboarding Consolidation Step"
msgstr "Estado del tablero de la etapa de consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_create_period_state
msgid "State Of The Onboarding Create Period Step"
msgstr "Estado del tablero de la etapa del período de creación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Sub-consolidated Chart"
msgstr "Gráfica de sub-consolidación"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__linked_chart_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__children_ids
msgid "Sub-consolidations"
msgstr "Sub-consolidaciones "

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations Periods"
msgstr "Períodos de sub-consolidaciones"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations periods"
msgstr "Períodos de sub-consolidaciones"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Subgroups"
msgstr "Subgrupos"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies and the company is consolidated at %s%%."
msgstr ""
"Ten en cuenta que la consolidación (%s) y la empresa consolidada (%s) tienen"
" diferentes monedas y la empresa está consolidada en %s%%."

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies."
msgstr ""
"Ten en cuenta que la consolidación (%s) y la empresa consolidada (%s) tienen"
" diferentes monedas."

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid "Take into account that this company is consolidated at %s%%."
msgstr "Ten en cuenta que esta empresa se consolida en %s%%."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_chart_currency_id
msgid "Target Currency"
msgstr "Moneda objetivo"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid ""
"The Composed Analysis Period must be different from the Analysis Period"
msgstr ""
"Análisis compuesto del período deben ser diferente al del período de "
"análisis"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "The Scope of Consolidation"
msgstr "El alcance de la consolidación"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid ""
"The rate used for the consolidation (basically this rate will multiply the "
"sum of everything"
msgstr ""
"La tasa utilizada para la consolidación (en general, esta tasa multiplicará "
"la suma de todo"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "This journal has been automatically generated on"
msgstr "Este diario se generó automáticamente en"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#, python-format
msgid "Total"
msgstr "Total"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_graph
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_grid
#, python-format
msgid "Trial Balance"
msgstr "Balance general"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Trial Balance: %s"
msgstr "Balance general: %s"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Trial balance"
msgstr "Balance general"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_period_composition__unique_composition
msgid ""
"Two compositions of the same analysis period by the same analysis period "
"cannot be created"
msgstr ""
"No se pueden crear dos composiciones del mismo período de análisis para el "
"mismo período de análisis"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Unfolded"
msgstr "Abierto"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de mensajes sin leer"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Update"
msgstr "Actualizar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__used_in_composition_ids
msgid "Used In Composition"
msgstr "Utilizado en la composición"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__color
msgid "Used in the kanban view"
msgstr "Empleado en vista de kanban"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__using_composition_ids
msgid "Using Composition"
msgstr "Usar composición"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"When setting a period on a consolidation journal, the selected consolidation"
" chart for the journal cannot be different from the one of the chosen "
"period."
msgstr ""
"Al establecer un período en un diario de consolidación, el gráfico de "
"consolidación seleccionado para el diario no puede ser diferente al del "
"período elegido."

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid ""
"You can here define complex consolidations based on other sub-"
"consolidations, as part of a whole scheme"
msgstr ""
"Aquí puedes definir consolidaciones complejas basadas en otras "
"subconsolidaciones, como parte de un esquema completo"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't delete an auto-generated journal entry."
msgstr "No puedes borrar un asiento generado de forma autómatica"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't edit an auto-generated journal entry."
msgstr "No puedes editar una entrada de diario generada automáticamente."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You cannot add journals to a closed period !"
msgstr "¡No puedes agregar diarios a un período cerrado!"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "e.g. Profit and Loss"
msgstr "por ejemplo, ganancias y pérdidas"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "e.g. Revenue"
msgstr "por ejemplo, ingresos"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/comparison.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/xml/fields_templates.xml:0
#, python-format
msgid "unmapped accounts"
msgstr "cuentas sin mappear"
