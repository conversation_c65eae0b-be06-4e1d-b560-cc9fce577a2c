# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_consolidation
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# laje_odoo, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids_count
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_account_ids_count
msgid "# Accounts"
msgstr "# de Comptes"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids_count
msgid "# Groups"
msgstr "# Groupes"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids_count
msgid "# Journals"
msgstr "# Journaux"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids_count
msgid "# Periods"
msgstr "# Périodes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Control"
msgstr "% Contrôle"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Ownership"
msgstr "% Propriété"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "%<br/>"
msgstr "%<br/>"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#, python-format
msgid "%s (%s Currency Conversion Method)"
msgstr "%s(%s Méthode de conversion des devises)"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "%s Consolidated Accounting"
msgstr "%s Comptabilité consolidée"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "(Re)Compute"
msgstr "(Re)Calculer"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "(Re)compute"
msgstr "(Re)calculer"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "/ End Rate: 1"
msgstr "/ Taux de clôture: 1"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Sélection\" role=\"img\" "
"title=\"Sélection\"/>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_period_comparisons
msgid ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparison"
msgstr ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparaison"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                    Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"                    Journaux:"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "<span class=\"o_form_label oe_inline\">%</span>"
msgstr "<span class=\"o_form_label oe_inline\">%</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">Actions</span>"
msgstr "<span role=\"Séparateur\">Actions</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">View</span>"
msgstr "<span role=\"separator\">Voir</span>"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_account_code_uniq
msgid ""
"A consolidation account with the same code already exists in this "
"consolidation."
msgstr ""
"Un compte de consolidation avec le même code existe déjà dans cette "
"consolidation."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"A journal entry should only be linked to a company period OR to a analysis "
"period of another consolidation !"
msgstr ""
"Une pièce comptable ne doit être liée qu'à une période de la société OU à "
"une période d'analyse d'une autre consolidation !"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_account
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__line_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Account"
msgstr "Compte"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__account_consolidation_currency_is_different
msgid "Account Consolidation Currency Is Different"
msgstr "La devise de la consolidation de compte est différente"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Group"
msgstr "Groupe de comptes"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_group_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_account_sections
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Groups"
msgstr "Groupes de comptes"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_action
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_from_period_action
msgid "Account Mapping"
msgstr "Affectation des comptes"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid "Account Mapping: %(chart)s"
msgstr "Compte(s) correspondant(s): %(chart)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Account Mapping: %(company)s"
msgstr "Compte(s) correspondant(s): %(company)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Account Mapping: %s (for %s)"
msgstr "Affectation des comptes : %s (pour %s)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Account Name"
msgstr "Nom du compte"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_consolidation_trial_balance_report
msgid "Account consolidation trial balance report"
msgstr "Rapport de la balance générale de la consolidation des comptes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
msgid "Account with Entries"
msgstr "Compte avec des mouvements"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Accounts"
msgstr "Comptes"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_color
msgid "Accounts color"
msgstr "Couleur des comptes"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Active"
msgstr "Actif"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Add a column"
msgstr "Ajouter une colonne"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Additional Information"
msgstr "Informations supplémentaires"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Advanced Consolidation"
msgstr "Consolidation avancée"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid "All"
msgstr "Tous"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Already Mapped"
msgstr "Déjà affecté"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__amount
msgid "Amount"
msgstr "Montant"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid ""
"An account group can only have accounts or other groups children but not "
"both !"
msgstr ""
"Un groupe de comptes ne peut avoir que des comptes ou d'autres groupes comme"
" enfants mais pas les deux !"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Analysis Period"
msgstr "Période d'analyse"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_period_id
msgid "Analysis Period Using This"
msgstr "Période d'analyse utilisant ceci"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.analysis_period_config_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_tree
msgid "Analysis Periods"
msgstr "Périodes d'analyse"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period"
msgstr "Période d'analyse"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period created !"
msgstr "Période d'analyse créée !"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Apply"
msgstr "Appliquer"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Audit"
msgstr "Audit"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Auto-generated"
msgstr "Généré automatiquement"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__auto_generated
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__auto_generated
msgid "Automatically Generated"
msgstr "Créer automatiquement"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_avg
msgid "Average Currency Rate"
msgstr "Taux de change moyen"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__avg
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Average Rate"
msgstr "Taux moyen"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Avg Rate: 1"
msgstr "Taux Moyen: 1"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__balance
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Balance"
msgstr "Solde"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Cancel"
msgstr "Annuler"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__chart_id
msgid "Chart"
msgstr "Plan"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of Accounts"
msgstr "Plan comptable"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of account set !"
msgstr "Plan comptable défini !"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Chart of accounts"
msgstr "Plan comptable"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_tree
msgid "Charts"
msgstr "Plans"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__child_ids
msgid "Children"
msgstr "Enfants"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Close"
msgstr "Fermer"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Close period"
msgstr "Clôturer la période"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__closed
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__closed
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Closed"
msgstr "Fermé"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__end
msgid "Closing Rate"
msgstr "Taux de clôture"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__code
msgid "Code"
msgstr "Code"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_color
msgid "Color"
msgstr "Couleur"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__color
msgid "Color Index"
msgstr "Couleur"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_res_company
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__company_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__company_ids
msgid "Companies"
msgstr "Sociétés"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__company_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Company"
msgstr "Société"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_company_id
msgid "Company Currency"
msgstr "Devise de la société"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Company Name"
msgstr "Nom de la société"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__company_period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Company Period"
msgstr "Période de la société"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_company_period_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_period_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_company_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Company Periods"
msgstr "Périodes de la société"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_unmapped_accounts_counts
msgid "Company Unmapped Accounts Counts"
msgstr "Comptes des comptes non mappés de l'entreprise "

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_period_id
msgid "Composed Analysis Period"
msgstr "Période d'analyse composée"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_chart_currency_id
msgid "Composed Consolidation Currency"
msgstr "Devise de consolidation composée"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Conso Rate:"
msgstr "Taux Conso:"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__account_id
msgid "Consolidated Account"
msgstr "Compte consolidé"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Consolidated Accounts"
msgstr "Comptes consolidés"

#. module: account_consolidation
#: model:ir.actions.client,name:account_consolidation.trial_balance_report_action
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Consolidated Balance"
msgstr "Bilan consolidé"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Consolidated Companies"
msgstr "Sociétés consolidées"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__parents_ids
msgid "Consolidated In"
msgstr "Consolidé en"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Consolidated balance"
msgstr "Bilan consolidé"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__used_in_ids
msgid "Consolidated in"
msgstr "Consolidé en"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action_onboarding
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__chart_id
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_charts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation"
msgstr "Consolidation"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Account"
msgstr "Compte de consolidation"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_chart_filtered_ids
msgid "Consolidation Account Chart Filtered"
msgstr "Graphique du Compte de Consolidation Filtré"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_account_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__using_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_accounts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Accounts"
msgstr "Comptes de consolidation"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation Chart"
msgstr "Graphique de Consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_company_period
msgid "Consolidation Company Period"
msgstr "Période de consolidation"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_chart_id
msgid "Consolidation Currency"
msgstr "Devise de consolidation"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.Consolidation_journal_line_action
msgid "Consolidation Entries"
msgstr "Ecritures de consolidation"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations_consolidation_entries
msgid "Consolidation Entry"
msgstr "Ecriture de consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_group
msgid "Consolidation Group"
msgstr "Groupe de Consolidation"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Consolidation Items"
msgstr "Postes de consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal
msgid "Consolidation Journal"
msgstr "Journal de consolidation"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_move_line__consolidation_journal_line_ids
msgid "Consolidation Journal Line"
msgstr "Ligne de Journal Consolidé"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__consolidation_method
msgid "Consolidation Method"
msgstr "Méthode de consolidation"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_name
msgid "Consolidation Name"
msgstr "Nom de la consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__composition_id
msgid "Consolidation Period"
msgstr "Période de consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period_composition
msgid "Consolidation Period Composition"
msgstr "Composition de la période de consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_rate
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Consolidation Rate"
msgstr "Taux de consolidation"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Consolidation Rate (%)"
msgstr "Taux de consolidation (%)"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_account
msgid "Consolidation account"
msgstr "Compte de consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_chart
msgid "Consolidation chart"
msgstr "Graphique de consolidation"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__line_ids
msgid "Consolidation items"
msgstr "Postes de consolidation"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal_line
msgid "Consolidation journal line"
msgstr "Ligne de Journal Consolidé"

#. module: account_consolidation
#: model:res.groups,name:account_consolidation.group_consolidation_user
msgid "Consolidation user"
msgstr "Utilisateur de consolidation"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create"
msgstr "Créer"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action_onboarding
msgid "Create First Period"
msgstr "Créer la première période"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create your first analysis period &amp; set the currency rates."
msgstr ""
"Créez votre première période d'analyse &amp; déterminez les taux des "
"devises."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_date
msgid "Created on"
msgstr "Créé le"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currencies_are_different
msgid "Currencies Are Different"
msgstr "Les devises sont différentes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Currency"
msgstr "Devise"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__currency_amount
msgid "Currency Amount"
msgstr "Montant en devise"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__currency_mode
msgid "Currency Conversion Method"
msgstr "Méthode de conversion des devises"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currency_rate
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "Currency Rate"
msgstr "Taux de la devise"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__currency_rate
msgid "Currency rate from composed chart currency to using chart currency"
msgstr ""
"Taux de change de la devise du plan comptable composé vers la devise du plan"
" comptable utilisé"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_home
msgid "Dashboard"
msgstr "Tableau de bord"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__dashboard_sections
msgid "Dashboard Sections"
msgstr "Sections du tableau de bord"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Date"
msgstr "Date"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Define"
msgstr "Définir"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid ""
"Define the companies that should be consolidated &amp; the target currency"
msgstr "Définir les sociétés à consolider et la devise cible"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__note
msgid "Description"
msgstr "Description"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_dates
msgid "Display Dates"
msgstr "Afficher les dates"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__done
msgid "Done"
msgstr "Fait"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__draft
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Draft"
msgstr "Brouillon"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#, python-format
msgid "Edit"
msgstr "Modifier"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid "End Currency Rate"
msgstr "Taux de change final"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_end
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "End Date"
msgstr "Date de fin"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "End Rate"
msgstr "Taux final"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__equity
msgid "Equity"
msgstr "Capitaux propres"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__exclude_journal_ids
msgid "Exclude Journals"
msgstr "Exclure des Journaux"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Export (XLSX)"
msgstr "Exporter (XLSX)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__filtered_used_in_ids
msgid "Filtered Used In"
msgstr "Filtre utilisé dans"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Folded"
msgstr "Replié"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__full_name
msgid "Full Name"
msgstr "Nom Complet"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__full
msgid "Full consolidation"
msgstr "Consolidation complète"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__group_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__group_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Group"
msgstr "Groupe"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Group Name"
msgstr "Nom du groupe"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Groups"
msgstr "Groupes"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__has_message
msgid "Has Message"
msgstr "A un message"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__hist
msgid "Historical Rate"
msgstr "Taux historique"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Historical Rates"
msgstr "Taux historiques"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Historical Rates: %(company)s"
msgstr "Taux Historiques: %(company)s"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_avg
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid ""
"How many units of company currency is needed to get 1 unit of chart currency"
msgstr ""
"Combien d'unités de devise de l'entreprise sont nécessaires pour obtenir 1 "
"unité de devise du plan comptable"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__id
msgid "ID"
msgstr "ID"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Future"
msgstr "Dans le futur"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Past"
msgstr "Dans le passé"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal"
msgstr "Journal"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.view_account_move_line_filter
msgid "Journal Items"
msgstr "Écritures comptables"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__line_ids
msgid "Journal lines"
msgstr "Lignes de Journal"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_journal_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Journals"
msgstr "Journaux"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__just_done
msgid "Just done"
msgstr "Fait à l'instant"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Manually Created"
msgstr "Créé manuellement"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Map Accounts"
msgstr "Cartographier Comptes"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Mapped Accounts"
msgstr "Comptes Cartographiés"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree_mapping
msgid "Mapped In"
msgstr "Cartographié Dans"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_ids
msgid "Messages"
msgstr "Messages"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__move_line_ids
msgid "Move Line"
msgstr "Ligne d'écriture"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Name"
msgstr "Nom"

#. module: account_consolidation
#: model_terms:ir.actions.act_window,help:account_consolidation.account_mapping_action
msgid ""
"No accounts have been found. Make sure you have installed a chart of account"
" for this company or that you have access right to see the accounts of this "
"company."
msgstr ""
"Aucun compte n'a été trouvé. Assurez-vous qu'un plan comptable a bien été "
"installé pour cette société ou que vous diposez des droits d'accès aux "
"comptes de cette société. "

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Not Mapped"
msgstr "Pas Cartographié"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__none
msgid "Not consolidated"
msgstr "Non consolidé"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__not_done
msgid "Not done"
msgstr "Pas fait"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods containing today"
msgstr "Seules les périodes contenant aujourd'hui"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the future"
msgstr "Seulement les périodes futures"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the past"
msgstr "Seulement les périodes dans le passé."

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations
msgid "Operations"
msgstr "Opérations"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__originating_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_originating_currency_id
msgid "Originating Currency"
msgstr "Devise d'origine"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
msgid "Parent"
msgstr "Parent"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_path
msgid "Parent Path"
msgstr "Chemin parent"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__period_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Period"
msgstr "Période"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_analysis_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Periods"
msgstr "Périodes"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Print Preview"
msgstr "Imprimer"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__proportional
msgid "Proportional consolidation"
msgstr "Consolidation proportionnelle"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__rate
msgid "Rate"
msgstr "Taux"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_control
msgid "Rate Control"
msgstr "Taux de contrôle"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_ownership
msgid "Rate Ownership"
msgstr "Taux de Propriété"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_rate_action
msgid "Rate Ranges"
msgstr "Plages de Taux"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Reopen period"
msgstr "Rouvrir la période"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Reset To Draft"
msgstr "Remettre en brouillon"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Review Chart Of Accounts"
msgstr "Revoir le Plan Comptable"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Scope of Consolidation defined !"
msgstr "Périmètre de consolidation défini !"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__sequence
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Settings"
msgstr "Paramètres"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Setup"
msgstr "Configuration"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid ""
"Setup your consolidated accounts and their currency conversion method.\n"
"                Then map them with the companies accounts."
msgstr ""
"Configurez vos comptes consolidés et leur méthode de conversion des devises.\n"
"                Ensuite, cartographiez-les avec les comptes des entreprises."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_control
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_ownership
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Should be between 0 and 100 %"
msgstr "Doit être compris entre 0 et 100%"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__show_on_dashboard
msgid "Show On Dashboard"
msgstr "Afficher sur le tableau de bord"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_conso_extra_options
msgid "Show Zero Balance Accounts"
msgstr "Afficher les comptes à solde zéro"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_start
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Start Date"
msgstr "Date de début"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__state
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__state
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "State"
msgstr "État"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_dashboard_onboarding_state
msgid "State Of The Account Consolidation Dashboard Onboarding Panel"
msgstr ""
"Etat du tableau de bord de consolidation des comptes du tableau "
"d'intégration"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_ccoa_state
msgid "State Of The Onboarding Consolidated Chart Of Account Step"
msgstr "État de l'étape du plan de compte consolidé d'intégration"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_consolidation_state
msgid "State Of The Onboarding Consolidation Step"
msgstr "État de l'étape de consolidation de l'intégration"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_create_period_state
msgid "State Of The Onboarding Create Period Step"
msgstr "État de l'étape de création de période d'intégration"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Sub-consolidated Chart"
msgstr "Graphique Sous-consolidé"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__linked_chart_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__children_ids
msgid "Sub-consolidations"
msgstr "Sous-consolidations"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations Periods"
msgstr "Périodes de sous-consolidations"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations periods"
msgstr "Périodes de sous-consolidations"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Subgroups"
msgstr "Sous-groupes"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies and the company is consolidated at %s%%."
msgstr ""
"Tenir compte du fait que la consolidation (%s) et la société consolidée (%s)"
" ont des devises différentes et la société est consolidée à %s%%."

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies."
msgstr ""
"Tenez compte du fait que la consolidation (%s) et la société consolidée (%s)"
" ont des devises différentes."

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid "Take into account that this company is consolidated at %s%%."
msgstr "Tenez compte que cette société est consolidée à %s%%."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_chart_currency_id
msgid "Target Currency"
msgstr "Devise cible"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid ""
"The Composed Analysis Period must be different from the Analysis Period"
msgstr ""
"La période d'analyse composée doit être différente de la période d'analyse."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "The Scope of Consolidation"
msgstr "Le périmètre de consolidation"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid ""
"The rate used for the consolidation (basically this rate will multiply the "
"sum of everything"
msgstr ""
"Le taux utilisé pour la consolidation (fondamentalement, ce taux multipliera"
" la somme de tout)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "This journal has been automatically generated on"
msgstr "Ce journal a été généré automatiquement le"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#, python-format
msgid "Total"
msgstr "Total"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_graph
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_grid
#, python-format
msgid "Trial Balance"
msgstr "Balance générale"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Trial Balance: %s"
msgstr "Balance générale: %s"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Trial balance"
msgstr "Balance générale"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_period_composition__unique_composition
msgid ""
"Two compositions of the same analysis period by the same analysis period "
"cannot be created"
msgstr ""
"Impossible de créer deux compositions de la même période d'analyse par la "
"même période d'analyse"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Unfolded"
msgstr "Déplié"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Update"
msgstr "Mettre à jour"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__used_in_composition_ids
msgid "Used In Composition"
msgstr "Utilisé dans la Composition"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__color
msgid "Used in the kanban view"
msgstr "Utilisé dans la vue Kanban"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__using_composition_ids
msgid "Using Composition"
msgstr "Utilisation de la composition"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"When setting a period on a consolidation journal, the selected consolidation"
" chart for the journal cannot be different from the one of the chosen "
"period."
msgstr ""
"Lorsque vous définissez une période sur un journal de consolidation, le "
"graphique de consolidation sélectionné pour le journal ne peut pas être "
"différent de celui de la période choisie."

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid ""
"You can here define complex consolidations based on other sub-"
"consolidations, as part of a whole scheme"
msgstr ""
"Vous pouvez ici définir des consolidations complexes basées sur d'autres "
"sous-consolidations, dans le cadre d'un schéma complet"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't delete an auto-generated journal entry."
msgstr ""
"Vous ne pouvez pas supprimer une écriture de journal générée "
"automatiquement."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't edit an auto-generated journal entry."
msgstr ""
"Vous ne pouvez pas modifier une pièce comptable générée automatiquement."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You cannot add journals to a closed period !"
msgstr "Vous ne pouvez pas ajouter de journaux à une période clôturée !"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "e.g. Profit and Loss"
msgstr "par exemple : Profit et perte"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "e.g. Revenue"
msgstr "par exemple : Revenus"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/comparison.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/xml/fields_templates.xml:0
#, python-format
msgid "unmapped accounts"
msgstr "Compte non-cartographié"
