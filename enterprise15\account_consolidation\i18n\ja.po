# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_consolidation
# 
# Translators:
# NANI<PERSON> <naniwa.ma<PERSON><EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Jun<PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids_count
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_account_ids_count
msgid "# Accounts"
msgstr "勘定科目数"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids_count
msgid "# Groups"
msgstr "＃グループ"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids_count
msgid "# Journals"
msgstr "仕訳帳数"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids_count
msgid "# Periods"
msgstr "期間数"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Control"
msgstr "コントロール％"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Ownership"
msgstr "所有権％"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "%<br/>"
msgstr ""

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#, python-format
msgid "%s (%s Currency Conversion Method)"
msgstr "%s (%s 通貨換算方法)"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "%s Consolidated Accounting"
msgstr "%s 連結会計"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "(Re)Compute"
msgstr "(再)計算"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "(Re)compute"
msgstr "(再)計算"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "/ End Rate: 1"
msgstr "/ 決算時レート: 1"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_period_comparisons
msgid ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparison"
msgstr ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    比較"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                    Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"                    仕訳帳:"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "<span class=\"o_form_label oe_inline\">%</span>"
msgstr "<span class=\"o_form_label oe_inline\">%</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">Actions</span>"
msgstr "<span role=\"separator\">アクション</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">View</span>"
msgstr "<span role=\"separator\">照会</span>"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_account_code_uniq
msgid ""
"A consolidation account with the same code already exists in this "
"consolidation."
msgstr "この連結には、同じコードの連結勘定がすでに存在します。"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"A journal entry should only be linked to a company period OR to a analysis "
"period of another consolidation !"
msgstr ""

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_account
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__line_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Account"
msgstr "勘定科目"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__account_consolidation_currency_is_different
msgid "Account Consolidation Currency Is Different"
msgstr "勘定科目連結通貨が異なる"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Group"
msgstr "勘定グループ"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_group_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_account_sections
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Groups"
msgstr "勘定グループ"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_action
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_from_period_action
msgid "Account Mapping"
msgstr "勘定科目マッピング"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid "Account Mapping: %(chart)s"
msgstr "勘定科目対応: %(chart)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Account Mapping: %(company)s"
msgstr "勘定科目対応: %(company)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Account Mapping: %s (for %s)"
msgstr "勘定科目マッピング: %s (for %s)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Account Name"
msgstr "勘定科目名"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_consolidation_trial_balance_report
msgid "Account consolidation trial balance report"
msgstr "連結勘定試算表レポート"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
msgid "Account with Entries"
msgstr "入力のあるアカウント"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Accounts"
msgstr "アカウント"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_color
msgid "Accounts color"
msgstr "勘定科目カラー"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Active"
msgstr "有効"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Add a column"
msgstr "カラム追加"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Additional Information"
msgstr "追加情報"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Advanced Consolidation"
msgstr "高度な連結"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid "All"
msgstr "全て"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Already Mapped"
msgstr "対応付け済"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__amount
msgid "Amount"
msgstr "金額"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid ""
"An account group can only have accounts or other groups children but not "
"both !"
msgstr "勘定グループは、勘定科目または他のグループのみを子として持つことができ、両方を持つことはできません!"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Analysis Period"
msgstr "分析期間"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_period_id
msgid "Analysis Period Using This"
msgstr "以下を使用する分析期間"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.analysis_period_config_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_tree
msgid "Analysis Periods"
msgstr "分析期間"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period"
msgstr "分析期間"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period created !"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Apply"
msgstr "適用"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Audit"
msgstr "監査"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Auto-generated"
msgstr "自動生成"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__auto_generated
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__auto_generated
msgid "Automatically Generated"
msgstr "自動生成"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_avg
msgid "Average Currency Rate"
msgstr "平均為替レート"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__avg
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Average Rate"
msgstr "平均レート"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Avg Rate: 1"
msgstr "平均レート: 1"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__balance
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Balance"
msgstr "残高"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Cancel"
msgstr "取消"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__chart_id
msgid "Chart"
msgstr "チャート"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of Accounts"
msgstr "勘定科目表"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of account set !"
msgstr "勘定科目表設定済！"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Chart of accounts"
msgstr "勘定科目表"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_tree
msgid "Charts"
msgstr "一覧"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__child_ids
msgid "Children"
msgstr "子"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Close"
msgstr "閉じる"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Close period"
msgstr "期間をクローズ"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__closed
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__closed
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Closed"
msgstr "クローズ"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__end
msgid "Closing Rate"
msgstr "決算時レート"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__code
msgid "Code"
msgstr "コード"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_color
msgid "Color"
msgstr "色"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__color
msgid "Color Index"
msgstr "カラーインデクス"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_res_company
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__company_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__company_ids
msgid "Companies"
msgstr "会社"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__company_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Company"
msgstr "会社"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_company_id
msgid "Company Currency"
msgstr "会社の通貨"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Company Name"
msgstr "会社名"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__company_period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Company Period"
msgstr "会社期間"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_company_period_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_period_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_company_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Company Periods"
msgstr "会社期間"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_unmapped_accounts_counts
msgid "Company Unmapped Accounts Counts"
msgstr "会社の未マップ口座数"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_period_id
msgid "Composed Analysis Period"
msgstr "構成された分析期間"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_chart_currency_id
msgid "Composed Consolidation Currency"
msgstr "構成された連結通貨"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration
msgid "Configuration"
msgstr "設定"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Conso Rate:"
msgstr "連結割合:"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__account_id
msgid "Consolidated Account"
msgstr "連結勘定科目"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Consolidated Accounts"
msgstr "連結勘定科目"

#. module: account_consolidation
#: model:ir.actions.client,name:account_consolidation.trial_balance_report_action
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Consolidated Balance"
msgstr "連結残高"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Consolidated Companies"
msgstr "連結対象会社"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__parents_ids
msgid "Consolidated In"
msgstr "以下で連結された："

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Consolidated balance"
msgstr "連結残高"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__used_in_ids
msgid "Consolidated in"
msgstr "以下で連結された："

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action_onboarding
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__chart_id
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_charts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation"
msgstr "連結"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Account"
msgstr "連結勘定"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_chart_filtered_ids
msgid "Consolidation Account Chart Filtered"
msgstr "連結勘定科目一覧フィルタ済"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_account_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__using_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_accounts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Accounts"
msgstr "連結勘定"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation Chart"
msgstr "連結一覧"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_company_period
msgid "Consolidation Company Period"
msgstr "連結会社期間"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_chart_id
msgid "Consolidation Currency"
msgstr "連結通貨"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.Consolidation_journal_line_action
msgid "Consolidation Entries"
msgstr "連結エントリ"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations_consolidation_entries
msgid "Consolidation Entry"
msgstr "連結エントリ"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_group
msgid "Consolidation Group"
msgstr "連結グループ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Consolidation Items"
msgstr "連結項目"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal
msgid "Consolidation Journal"
msgstr "連結仕訳帳"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_move_line__consolidation_journal_line_ids
msgid "Consolidation Journal Line"
msgstr "連結仕訳帳明細"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__consolidation_method
msgid "Consolidation Method"
msgstr "連結方法"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_name
msgid "Consolidation Name"
msgstr "連結名"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__composition_id
msgid "Consolidation Period"
msgstr "連結期間"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period_composition
msgid "Consolidation Period Composition"
msgstr "連結期間比較"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_rate
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Consolidation Rate"
msgstr "連結割合"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Consolidation Rate (%)"
msgstr "連結割合(%)"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_account
msgid "Consolidation account"
msgstr "連結勘定科目"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_chart
msgid "Consolidation chart"
msgstr "連結一覧"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__line_ids
msgid "Consolidation items"
msgstr "連結項目"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal_line
msgid "Consolidation journal line"
msgstr "連結仕訳帳明細"

#. module: account_consolidation
#: model:res.groups,name:account_consolidation.group_consolidation_user
msgid "Consolidation user"
msgstr "連結ユーザ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create"
msgstr "作成"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action_onboarding
msgid "Create First Period"
msgstr "第一期間を作成する"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create your first analysis period &amp; set the currency rates."
msgstr "最初の分析期間を作成し、通貨レートを設定します。"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_uid
msgid "Created by"
msgstr "作成者"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_date
msgid "Created on"
msgstr "作成日"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currencies_are_different
msgid "Currencies Are Different"
msgstr "通貨が異なる"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Currency"
msgstr "通貨"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__currency_amount
msgid "Currency Amount"
msgstr "取引通貨高"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__currency_mode
msgid "Currency Conversion Method"
msgstr "通貨換算法"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currency_rate
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "Currency Rate"
msgstr "為替レート"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__currency_rate
msgid "Currency rate from composed chart currency to using chart currency"
msgstr "構成一覧の通貨から使用一覧の通貨への為替レート"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_home
msgid "Dashboard"
msgstr "ダッシュボード"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__dashboard_sections
msgid "Dashboard Sections"
msgstr "ダッシュボードセクション"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Date"
msgstr "日付"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Define"
msgstr "定義"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid ""
"Define the companies that should be consolidated &amp; the target currency"
msgstr "連結対象の会社とターゲット通貨を定義"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__note
msgid "Description"
msgstr "説明"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_dates
msgid "Display Dates"
msgstr "日付を表示"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__display_name
msgid "Display Name"
msgstr "表示名"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__done
msgid "Done"
msgstr "完了"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__draft
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Draft"
msgstr "ドラフト"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#, python-format
msgid "Edit"
msgstr "編集"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid "End Currency Rate"
msgstr "終了為替レート"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_end
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "End Date"
msgstr "終了日"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "End Rate"
msgstr "決算時レート"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__equity
msgid "Equity"
msgstr "資本"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__exclude_journal_ids
msgid "Exclude Journals"
msgstr "除外対象仕訳帳"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Export (XLSX)"
msgstr "エクスポート(XLSX)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__filtered_used_in_ids
msgid "Filtered Used In"
msgstr "以下でフィルタされ使用された："

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Folded"
msgstr "折りたたみ"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__full_name
msgid "Full Name"
msgstr "名称 (フル)"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__full
msgid "Full consolidation"
msgstr "全部連結"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__group_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__group_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Group"
msgstr "グループ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Group Name"
msgstr "グループ名"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Groups"
msgstr "グループ"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__hist
msgid "Historical Rate"
msgstr "取引時レート"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Historical Rates"
msgstr "過去レート"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Historical Rates: %(company)s"
msgstr "過去レート: %(company)s"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_avg
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid ""
"How many units of company currency is needed to get 1 unit of chart currency"
msgstr "チャート通貨1単位を手に入れるには、社内通貨が何単位必要ですか？"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__id
msgid "ID"
msgstr "ID"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread
msgid "If checked, new messages require your attention."
msgstr "チェックされている場合は、新しいメッセージに注意が必要です。"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合、一部のメッセージで配信エラーが発生しています。"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Future"
msgstr "将来的に"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Past"
msgstr "過去"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal"
msgstr "仕訳帳"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal Item"
msgstr "仕訳明細"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.view_account_move_line_filter
msgid "Journal Items"
msgstr "仕訳明細"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__line_ids
msgid "Journal lines"
msgstr "仕訳帳明細"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_journal_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Journals"
msgstr "仕訳帳"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__just_done
msgid "Just done"
msgstr "完了"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_main_attachment_id
msgid "Main Attachment"
msgstr "主な添付"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Manually Created"
msgstr "手動作成"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Map Accounts"
msgstr "勘定科目割当"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Mapped Accounts"
msgstr "割当済勘定科目"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree_mapping
msgid "Mapped In"
msgstr "以下にマップされた"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__move_line_ids
msgid "Move Line"
msgstr "仕訳明細"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Name"
msgstr "名称"

#. module: account_consolidation
#: model_terms:ir.actions.act_window,help:account_consolidation.account_mapping_action
msgid ""
"No accounts have been found. Make sure you have installed a chart of account"
" for this company or that you have access right to see the accounts of this "
"company."
msgstr "勘定科目が見つかりません。この会社の勘定科目一覧がインストールされているか、この会社の勘定科目を見るアクセス権があることを確認して下さい。"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Not Mapped"
msgstr "未対応付け"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__none
msgid "Not consolidated"
msgstr "非連結"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__not_done
msgid "Not done"
msgstr "未完了"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of Actions"
msgstr "アクションの数"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "アクションを必要とするメッセージの数"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーのメッセージ数"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Number of unread messages"
msgstr "未読メッセージ件数"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods containing today"
msgstr "今日を含む期間のみ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the future"
msgstr "将来の期間のみ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the past"
msgstr "過去の期間のみ"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations
msgid "Operations"
msgstr "オペレーション"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__originating_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_originating_currency_id
msgid "Originating Currency"
msgstr "発行通貨"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
msgid "Parent"
msgstr "親"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_path
msgid "Parent Path"
msgstr "親パス"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__period_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Period"
msgstr "期間"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_analysis_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Periods"
msgstr "期間"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Print Preview"
msgstr "印刷プレビュー"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__proportional
msgid "Proportional consolidation"
msgstr "比例連結"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__rate
msgid "Rate"
msgstr "レート"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_control
msgid "Rate Control"
msgstr "支配割合"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_ownership
msgid "Rate Ownership"
msgstr "所有割合"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_rate_action
msgid "Rate Ranges"
msgstr "レート範囲"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Reopen period"
msgstr "期間を再開"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Reset To Draft"
msgstr "ドラフトに再設定"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Review Chart Of Accounts"
msgstr "勘定科目表レビュー"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Scope of Consolidation defined !"
msgstr "連結スコープが定義されました！"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__sequence
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__sequence
msgid "Sequence"
msgstr "付番"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Settings"
msgstr "管理設定"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Setup"
msgstr "セットアップ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid ""
"Setup your consolidated accounts and their currency conversion method.\n"
"                Then map them with the companies accounts."
msgstr ""
"連結勘定とその通貨換算方法を設定します。\n"
"そして、それらを会社の勘定科目と対応させます。"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_control
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_ownership
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Should be between 0 and 100 %"
msgstr "0～100の間でなければいけません"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__show_on_dashboard
msgid "Show On Dashboard"
msgstr "ダッシュボードに表示"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_conso_extra_options
msgid "Show Zero Balance Accounts"
msgstr "残高０の勘定を表示"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_start
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Start Date"
msgstr "開始日"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__state
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__state
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "State"
msgstr "状況"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_dashboard_onboarding_state
msgid "State Of The Account Consolidation Dashboard Onboarding Panel"
msgstr "勘定科目統合ダッシュボード・オンボーディング・パネルのステート"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_ccoa_state
msgid "State Of The Onboarding Consolidated Chart Of Account Step"
msgstr "連結勘定科目表ステップ オンボーディングの現状 "

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_consolidation_state
msgid "State Of The Onboarding Consolidation Step"
msgstr "連結ステップ オンボーディングのステート"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_create_period_state
msgid "State Of The Onboarding Create Period Step"
msgstr "期間作成ステップ オンボーディングのステート"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Sub-consolidated Chart"
msgstr "下位連結チャート"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__linked_chart_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__children_ids
msgid "Sub-consolidations"
msgstr "サブ統合"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations Periods"
msgstr "下位連結期間"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations periods"
msgstr "下位連結期間"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Subgroups"
msgstr "サブグループ"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies and the company is consolidated at %s%%."
msgstr ""

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies."
msgstr ""

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid "Take into account that this company is consolidated at %s%%."
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_chart_currency_id
msgid "Target Currency"
msgstr "ターゲット通貨"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid ""
"The Composed Analysis Period must be different from the Analysis Period"
msgstr "下位の分析期間は、分析期間と異なる必要があります"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "The Scope of Consolidation"
msgstr "連結スコープ"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid ""
"The rate used for the consolidation (basically this rate will multiply the "
"sum of everything"
msgstr "連結に使用されるレート（基本的にこのレートはすべての合計に乗算される"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "This journal has been automatically generated on"
msgstr "この仕訳帳が自動生成された日:"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#, python-format
msgid "Total"
msgstr "合計"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_graph
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_grid
#, python-format
msgid "Trial Balance"
msgstr "試算表"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Trial Balance: %s"
msgstr "試算表: %s"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Trial balance"
msgstr "試算表"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_period_composition__unique_composition
msgid ""
"Two compositions of the same analysis period by the same analysis period "
"cannot be created"
msgstr "同じ分析期間による同じ分析期間の２つの合成を作成することはできません。"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Unfolded"
msgstr "折り畳まれていません"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未読メッセージカウンター"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Update"
msgstr "更新"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__used_in_composition_ids
msgid "Used In Composition"
msgstr "合成に使用済"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__color
msgid "Used in the kanban view"
msgstr "かんばんビューで使用済"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__using_composition_ids
msgid "Using Composition"
msgstr "合成を使用"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイトコミュニケーション履歴"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"When setting a period on a consolidation journal, the selected consolidation"
" chart for the journal cannot be different from the one of the chosen "
"period."
msgstr "連結仕訳で期間を設定する場合、その仕訳で選択された連結表は、選択された期間の連結表と異なることはできません。"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid ""
"You can here define complex consolidations based on other sub-"
"consolidations, as part of a whole scheme"
msgstr "ここでは別途定義された連結の結果を連結対象に含めることができます。"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't delete an auto-generated journal entry."
msgstr "自動生成された仕訳を削除することはできません。"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't edit an auto-generated journal entry."
msgstr "自動生成された仕訳を編集することはできません。"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You cannot add journals to a closed period !"
msgstr "クローズした期間に仕訳を追加することはできません !"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "e.g. Profit and Loss"
msgstr "例：損益勘定"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "e.g. Revenue"
msgstr "例：売上"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/comparison.py:0
#, python-format
msgid "n/a"
msgstr "該当なし"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/xml/fields_templates.xml:0
#, python-format
msgid "unmapped accounts"
msgstr "未割当勘定科目"
