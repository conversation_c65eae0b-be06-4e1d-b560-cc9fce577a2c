# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_consolidation
# 
# Translators:
# Андрей <PERSON>ев <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# G<PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Дмитрий <PERSON>ременко <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Сергей <PERSON>н <<EMAIL>>, 2022
# Wil Odoo, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Roman Kuzmenko, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids_count
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_account_ids_count
msgid "# Accounts"
msgstr "# Счета"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids_count
msgid "# Groups"
msgstr "Кол-во групп условий"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids_count
msgid "# Journals"
msgstr "# Журналы"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids_count
msgid "# Periods"
msgstr "# Периоды"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Control"
msgstr "% Контроль"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Ownership"
msgstr "% владения"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "%<br/>"
msgstr ""

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#, python-format
msgid "%s (%s Currency Conversion Method)"
msgstr "%s (%s Метод пересчета валюты)"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "%s Consolidated Accounting"
msgstr "%s Консолидированная отчетность"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "(Re)Compute"
msgstr "(Пере)вычислить"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "(Re)compute"
msgstr "(Пере)вычислить"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "/ End Rate: 1"
msgstr "/ Конечная ставка: 1"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_period_comparisons
msgid ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparison"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                    Journals:"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "<span class=\"o_form_label oe_inline\">%</span>"
msgstr "<span class=\"o_form_label oe_inline\">%</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">Actions</span>"
msgstr "<span role=\"separator\">Действия</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">View</span>"
msgstr "<span role=\"separator\">Просмотр</span>"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_account_code_uniq
msgid ""
"A consolidation account with the same code already exists in this "
"consolidation."
msgstr ""
"Счет консолидации с таким же кодом уже существует в этой консолидации."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"A journal entry should only be linked to a company period OR to a analysis "
"period of another consolidation !"
msgstr ""

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_account
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__line_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Account"
msgstr "Счёт"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__account_consolidation_currency_is_different
msgid "Account Consolidation Currency Is Different"
msgstr "Валюта консолидации счетов отличается"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Group"
msgstr "Группа счетов"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_group_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_account_sections
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Groups"
msgstr "Группы счетов"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_action
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_from_period_action
msgid "Account Mapping"
msgstr "Отображение счёта"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid "Account Mapping: %(chart)s"
msgstr "Сопоставление учетных записей: %(chart)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Account Mapping: %(company)s"
msgstr "Сопоставление учетных записей: %(company)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Account Mapping: %s (for %s)"
msgstr "Сопоставление счетов: %s (для %s)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Account Name"
msgstr "Название счёта"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_consolidation_trial_balance_report
msgid "Account consolidation trial balance report"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
msgid "Account with Entries"
msgstr "Аккаунт с записями"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Accounts"
msgstr "Cчета"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_color
msgid "Accounts color"
msgstr "Цвет счетов"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Active"
msgstr "Активно"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Add a column"
msgstr "Добавить колонку"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Additional Information"
msgstr "Дополнительная информация"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Advanced Consolidation"
msgstr "Расширенная консолидация"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid "All"
msgstr "Все"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Already Mapped"
msgstr "Уже нанесено на карту"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__amount
msgid "Amount"
msgstr "Сумма"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid ""
"An account group can only have accounts or other groups children but not "
"both !"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Analysis Period"
msgstr "Период анализа"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_period_id
msgid "Analysis Period Using This"
msgstr "Период анализа с использованием этого"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.analysis_period_config_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_tree
msgid "Analysis Periods"
msgstr "Анализ Периоды"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period"
msgstr "Период анализа"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period created !"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Apply"
msgstr "Применить"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Audit"
msgstr "аудит"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Auto-generated"
msgstr "Автоматически сгенерированный"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__auto_generated
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__auto_generated
msgid "Automatically Generated"
msgstr "Сгенерировано автоматически"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_avg
msgid "Average Currency Rate"
msgstr "Средний курс валют"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__avg
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Average Rate"
msgstr "Средняя ставка"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Avg Rate: 1"
msgstr "Средняя ставка: 1"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__balance
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Balance"
msgstr "Баланс"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Cancel"
msgstr "Отмена"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__chart_id
msgid "Chart"
msgstr "Диаграмма"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of Accounts"
msgstr "План счетов"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of account set !"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Chart of accounts"
msgstr "План счетов"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_tree
msgid "Charts"
msgstr "Диаграммы"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__child_ids
msgid "Children"
msgstr "Дочерний"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Close"
msgstr "Закрыть"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Close period"
msgstr "Период закрытия"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__closed
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__closed
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Closed"
msgstr "Закрыт"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__end
msgid "Closing Rate"
msgstr "Ставка закрытия"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__code
msgid "Code"
msgstr "Код"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_color
msgid "Color"
msgstr "Цвет"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__color
msgid "Color Index"
msgstr "Цветовая палитра"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_res_company
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__company_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__company_ids
msgid "Companies"
msgstr "Компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__company_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Company"
msgstr "Компания"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_company_id
msgid "Company Currency"
msgstr "Валюта компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Company Name"
msgstr "Название компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__company_period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Company Period"
msgstr "Период компании"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_company_period_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_period_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_company_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Company Periods"
msgstr "Периоды компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_unmapped_accounts_counts
msgid "Company Unmapped Accounts Counts"
msgstr "Подсчеты неохваченных счетов компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_period_id
msgid "Composed Analysis Period"
msgstr "Период композиционного анализа"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_chart_currency_id
msgid "Composed Consolidation Currency"
msgstr "Составленная валюта консолидации"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration
msgid "Configuration"
msgstr "Настройки"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Conso Rate:"
msgstr "Тариф \"Консо\":"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__account_id
msgid "Consolidated Account"
msgstr "Консолидированный счет"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Consolidated Accounts"
msgstr "Консолидированные счета"

#. module: account_consolidation
#: model:ir.actions.client,name:account_consolidation.trial_balance_report_action
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Consolidated Balance"
msgstr "Консолидированный баланс"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Consolidated Companies"
msgstr "Консолидированные компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__parents_ids
msgid "Consolidated In"
msgstr "Консолидированная в"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Consolidated balance"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__used_in_ids
msgid "Consolidated in"
msgstr "Консолидировано в"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action_onboarding
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__chart_id
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_charts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation"
msgstr "Консолидация"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Account"
msgstr "Консолидированный счет"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_chart_filtered_ids
msgid "Consolidation Account Chart Filtered"
msgstr "Консолидированный план счетов Отфильтрованный"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_account_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__using_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_accounts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Accounts"
msgstr "Консолидированные счета"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation Chart"
msgstr "Диаграмма консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_company_period
msgid "Consolidation Company Period"
msgstr "Период консолидации компании"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_chart_id
msgid "Consolidation Currency"
msgstr "Валюта консолидации"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.Consolidation_journal_line_action
msgid "Consolidation Entries"
msgstr "Консолидационные проводки"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations_consolidation_entries
msgid "Consolidation Entry"
msgstr "Ввод консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_group
msgid "Consolidation Group"
msgstr "Консолидационная группа"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Consolidation Items"
msgstr "Пункты консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal
msgid "Consolidation Journal"
msgstr "Журнал консолидации"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_move_line__consolidation_journal_line_ids
msgid "Consolidation Journal Line"
msgstr "Строка журнала консолидации"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__consolidation_method
msgid "Consolidation Method"
msgstr "Метод консолидации"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_name
msgid "Consolidation Name"
msgstr "Название консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__composition_id
msgid "Consolidation Period"
msgstr "Период консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period_composition
msgid "Consolidation Period Composition"
msgstr "Состав периода консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_rate
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Consolidation Rate"
msgstr "Ставка консолидации"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Consolidation Rate (%)"
msgstr "Коэффициент консолидации (%)"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_account
msgid "Consolidation account"
msgstr "Консолидированный счет"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_chart
msgid "Consolidation chart"
msgstr "Диаграмма консолидации"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__line_ids
msgid "Consolidation items"
msgstr "Статьи консолидации"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal_line
msgid "Consolidation journal line"
msgstr "Строка журнала консолидации"

#. module: account_consolidation
#: model:res.groups,name:account_consolidation.group_consolidation_user
msgid "Consolidation user"
msgstr "Пользователь консолидации"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create"
msgstr "Создать"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action_onboarding
msgid "Create First Period"
msgstr "Создать первый период"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create your first analysis period &amp; set the currency rates."
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_uid
msgid "Created by"
msgstr "Создан"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_date
msgid "Created on"
msgstr "Создан"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currencies_are_different
msgid "Currencies Are Different"
msgstr "Валюты разные"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Currency"
msgstr "Валюта"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__currency_amount
msgid "Currency Amount"
msgstr "Валюта Сумма"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__currency_mode
msgid "Currency Conversion Method"
msgstr "Метод пересчета валюты"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currency_rate
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "Currency Rate"
msgstr "Курс валюты"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__currency_rate
msgid "Currency rate from composed chart currency to using chart currency"
msgstr ""
"Курс валют от валюты составленного графика к валюте используемого графика"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_home
msgid "Dashboard"
msgstr "Обзор"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__dashboard_sections
msgid "Dashboard Sections"
msgstr "Панель управления"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Date"
msgstr "Дата"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Define"
msgstr "Определить"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid ""
"Define the companies that should be consolidated &amp; the target currency"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__note
msgid "Description"
msgstr "Описание"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_dates
msgid "Display Dates"
msgstr "Даты отображения"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__done
msgid "Done"
msgstr "Сделано"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__draft
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Draft"
msgstr "Черновик"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#, python-format
msgid "Edit"
msgstr "Редактировать"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid "End Currency Rate"
msgstr "Конечный курс валюты"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_end
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "End Date"
msgstr "Дата окончания"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "End Rate"
msgstr "Конечная ставка"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__equity
msgid "Equity"
msgstr "Собственные средства"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__exclude_journal_ids
msgid "Exclude Journals"
msgstr "Исключить журналы"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Export (XLSX)"
msgstr "Экспорт (XLSX)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__filtered_used_in_ids
msgid "Filtered Used In"
msgstr "Фильтр используется в"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Folded"
msgstr "Свернутый"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__full_name
msgid "Full Name"
msgstr "Полное Имя"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__full
msgid "Full consolidation"
msgstr "Полная консолидация"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__group_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__group_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Group"
msgstr "Группа"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Group Name"
msgstr "Наименование Группы"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Groups"
msgstr "Группы"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__hist
msgid "Historical Rate"
msgstr "Историческая ставка"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Historical Rates"
msgstr "Исторические ставки"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Historical Rates: %(company)s"
msgstr "Исторические ставки: %(company)s"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_avg
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid ""
"How many units of company currency is needed to get 1 unit of chart currency"
msgstr ""
"Сколько единиц валюты компании необходимо для получения 1 единицы валюты "
"графика"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__id
msgid "ID"
msgstr "Идентификатор"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Future"
msgstr "В будущем"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Past"
msgstr "В прошлом"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal"
msgstr "Журнал"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.view_account_move_line_filter
msgid "Journal Items"
msgstr "Элементы журнала"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__line_ids
msgid "Journal lines"
msgstr "Журнальные строки"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_journal_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Journals"
msgstr "Журналы"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__just_done
msgid "Just done"
msgstr "только завершено"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Manually Created"
msgstr "Создано вручную"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Map Accounts"
msgstr "Карточные счета"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Mapped Accounts"
msgstr "Сопоставленные счета"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree_mapping
msgid "Mapped In"
msgstr "Нанесено на карту"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__move_line_ids
msgid "Move Line"
msgstr "Переместить Строку"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Name"
msgstr "Название"

#. module: account_consolidation
#: model_terms:ir.actions.act_window,help:account_consolidation.account_mapping_action
msgid ""
"No accounts have been found. Make sure you have installed a chart of account"
" for this company or that you have access right to see the accounts of this "
"company."
msgstr ""
"Не найдено ни одного счета. Убедитесь, что у вас установлен план счетов для "
"этой компании или что у вас есть право доступа к счетам этой компании."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Not Mapped"
msgstr "Не нанесено на карту"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__none
msgid "Not consolidated"
msgstr "Не консолидировано"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__not_done
msgid "Not done"
msgstr "не завершено"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих действия"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods containing today"
msgstr "Только периоды, содержащие сегодняшний день"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the future"
msgstr "Только периоды в будущем"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the past"
msgstr "Только периоды в прошлом"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations
msgid "Operations"
msgstr "Операции"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__originating_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_originating_currency_id
msgid "Originating Currency"
msgstr "Валюта происхождения"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
msgid "Parent"
msgstr "Родитель"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__period_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Period"
msgstr "Период"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_analysis_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Periods"
msgstr "Периоды"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Print Preview"
msgstr "Предварительный просмотр"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__proportional
msgid "Proportional consolidation"
msgstr "Пропорциональная консолидация"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__rate
msgid "Rate"
msgstr "Ставка"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_control
msgid "Rate Control"
msgstr "Контроль скорости"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_ownership
msgid "Rate Ownership"
msgstr "Владение тарифами"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_rate_action
msgid "Rate Ranges"
msgstr "Диапазоны тарифов"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Reopen period"
msgstr "Период возобновления работы"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Reset To Draft"
msgstr "Переместить в черновики"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Review Chart Of Accounts"
msgstr "Обзор плана счетов"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Scope of Consolidation defined !"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__sequence
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Settings"
msgstr "Настройки"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Setup"
msgstr "Настройки"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid ""
"Setup your consolidated accounts and their currency conversion method.\n"
"                Then map them with the companies accounts."
msgstr ""
"Настройте консолидированные счета и метод их конвертации в валюту.\n"
"                Затем сопоставьте их со счетами компаний."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_control
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_ownership
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Should be between 0 and 100 %"
msgstr "Должно быть от 0 до 100 %"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__show_on_dashboard
msgid "Show On Dashboard"
msgstr "Показать на приборной панели"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_conso_extra_options
msgid "Show Zero Balance Accounts"
msgstr "Показать счета с нулевым балансом"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_start
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Start Date"
msgstr "Дата начала"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__state
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__state
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "State"
msgstr "Статус"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_dashboard_onboarding_state
msgid "State Of The Account Consolidation Dashboard Onboarding Panel"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_ccoa_state
msgid "State Of The Onboarding Consolidated Chart Of Account Step"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_consolidation_state
msgid "State Of The Onboarding Consolidation Step"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_create_period_state
msgid "State Of The Onboarding Create Period Step"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Sub-consolidated Chart"
msgstr "Субконсолидированная диаграмма"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__linked_chart_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__children_ids
msgid "Sub-consolidations"
msgstr "Субконсолидация"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations Periods"
msgstr "Субконсолидация Периоды"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations periods"
msgstr "Периоды субконсолидации"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Subgroups"
msgstr "Подгруппы"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies and the company is consolidated at %s%%."
msgstr ""

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies."
msgstr ""

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid "Take into account that this company is consolidated at %s%%."
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_chart_currency_id
msgid "Target Currency"
msgstr "Целевая валюта"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid ""
"The Composed Analysis Period must be different from the Analysis Period"
msgstr "Период составленного анализа должен отличаться от периода анализа"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "The Scope of Consolidation"
msgstr "Сфера применения консолидации"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid ""
"The rate used for the consolidation (basically this rate will multiply the "
"sum of everything"
msgstr ""
"Ставка, используемая для консолидации (в основном эта ставка будет умножать "
"сумму всех"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "This journal has been automatically generated on"
msgstr "Этот журнал был автоматически создан на"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#, python-format
msgid "Total"
msgstr "Всего"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_graph
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_grid
#, python-format
msgid "Trial Balance"
msgstr "Оборотно-сальдовая ведомость"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Trial Balance: %s"
msgstr "Пробный баланс: %s"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Trial balance"
msgstr "Судебный баланс"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_period_composition__unique_composition
msgid ""
"Two compositions of the same analysis period by the same analysis period "
"cannot be created"
msgstr ""
"Невозможно создать две композиции одного и того же периода анализа по одному"
" и тому же периоду анализа"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Unfolded"
msgstr "развернуты"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Update"
msgstr "Обновить"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__used_in_composition_ids
msgid "Used In Composition"
msgstr "Используется в составе"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__color
msgid "Used in the kanban view"
msgstr "Используется в виде канбан"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__using_composition_ids
msgid "Using Composition"
msgstr "Использование композиции"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"When setting a period on a consolidation journal, the selected consolidation"
" chart for the journal cannot be different from the one of the chosen "
"period."
msgstr ""
"При установке периода в журнале консолидации выбранный график консолидации "
"для журнала не может отличаться от выбранного периода."

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid ""
"You can here define complex consolidations based on other sub-"
"consolidations, as part of a whole scheme"
msgstr ""
"Здесь вы можете определить сложные консолидации, основанные на других "
"субконсолидациях, как часть целой схемы"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't delete an auto-generated journal entry."
msgstr "Вы не можете удалить автоматически созданную запись в журнале."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't edit an auto-generated journal entry."
msgstr "Вы не можете редактировать автоматически созданную запись в журнале."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You cannot add journals to a closed period !"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "e.g. Profit and Loss"
msgstr "например, Прибыль и убытки"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "e.g. Revenue"
msgstr "например, доходы"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/comparison.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/xml/fields_templates.xml:0
#, python-format
msgid "unmapped accounts"
msgstr "неотмеченные счета"
