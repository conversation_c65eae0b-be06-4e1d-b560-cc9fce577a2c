# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_consolidation
# 
# Translators:
# <PERSON><PERSON><PERSON>IL<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>ILMAZ <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON>ıl <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>alay <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Ozlem Cikrikci <<EMAIL>>, 2021\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids_count
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_account_ids_count
msgid "# Accounts"
msgstr "# Hesaplar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids_count
msgid "# Groups"
msgstr "# Gruplar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids_count
msgid "# Journals"
msgstr "# Yevmiyeler"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids_count
msgid "# Periods"
msgstr "# Dönemler"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Control"
msgstr "% Kontrol"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "% Ownership"
msgstr "% Sahiplik"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "%<br/>"
msgstr ""

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#, python-format
msgid "%s (%s Currency Conversion Method)"
msgstr "%s (%s Para Birimi Dönüştürme Yöntemi)"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "%s Consolidated Accounting"
msgstr "%s Konsolide Muhasebe"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "(Re)Compute"
msgstr "(Yeniden)Hesapla"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "(Re)compute"
msgstr "(Yeniden)hesapla"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "/ End Rate: 1"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" aria-label=\"Selection\" role=\"img\" "
"title=\"Selection\"/>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_period_comparisons
msgid ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Comparison"
msgstr ""
"<span class=\"fa fa-bar-chart\"/>\n"
"                    Karşılaştırma"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                    Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"                    Yevmiyeler:"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "<span class=\"o_form_label oe_inline\">%</span>"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">Actions</span>"
msgstr "<span role=\"separator\">İşlemler</span>"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "<span role=\"separator\">View</span>"
msgstr "<span role=\"separator\">Göster</span>"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_account_code_uniq
msgid ""
"A consolidation account with the same code already exists in this "
"consolidation."
msgstr ""
"Bu konsolidasyonda aynı koda sahip bir konsolidasyon hesabı zaten var."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"A journal entry should only be linked to a company period OR to a analysis "
"period of another consolidation !"
msgstr ""
"Yevmiye kaydı yalnızca şirket dönemiyle VEYA başka bir konsolidasyonun "
"analiz dönemiyle ilişkilendirilmelidir!"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_account
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__line_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Account"
msgstr "Hesap"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__account_consolidation_currency_is_different
msgid "Account Consolidation Currency Is Different"
msgstr "Hesap Konsolidasyonu Para Birimi Farklı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Group"
msgstr "Hesap Grubu"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_group_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__group_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_account_sections
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_tree
msgid "Account Groups"
msgstr "Hesap Grupları"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_action
#: model:ir.actions.act_window,name:account_consolidation.account_mapping_from_period_action
msgid "Account Mapping"
msgstr "Hesap Eşlemesi"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid "Account Mapping: %(chart)s"
msgstr "Account Mapping: %(chart)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Account Mapping: %(company)s"
msgstr "Account Mapping: %(company)s"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Account Mapping: %s (for %s)"
msgstr "Hesap Eşleme: %s (için%s)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Account Name"
msgstr "Hesap Adı"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_consolidation_trial_balance_report
msgid "Account consolidation trial balance report"
msgstr "Hesap konsolidasyonu geçici mizan raporu"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
msgid "Account with Entries"
msgstr "Kaydı bulunan hesaplar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Accounts"
msgstr "Hesaplar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_color
msgid "Accounts color"
msgstr "Hesapların rengi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction
msgid "Action Needed"
msgstr "Eylem Gerekiyor"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Active"
msgstr "Etkin"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Add a column"
msgstr "Sütun ekle"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Additional Information"
msgstr "Ek Bilgi"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Advanced Consolidation"
msgstr "Gelişmiş Konsolidasyon"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_consolidation_journals
msgid "All"
msgstr "Tümü"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Already Mapped"
msgstr "Zaten Eşlendi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__amount
msgid "Amount"
msgstr "Tutar"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_coa.py:0
#, python-format
msgid ""
"An account group can only have accounts or other groups children but not "
"both !"
msgstr ""
"Bir hesap grubunun yalnızca hesapları veya diğer alt grupları olabilir, "
"ancak her ikisinde birden olamaz!"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Analysis Period"
msgstr "Analiz Dönemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_period_id
msgid "Analysis Period Using This"
msgstr "Bunu Kullanarak Analiz Dönemi"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.analysis_period_config_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__period_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_tree
msgid "Analysis Periods"
msgstr "Analitik Dönemler"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period"
msgstr "Analiz dönemi"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Analysis period created !"
msgstr "Analiz dönemi oluşturuldu!"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Apply"
msgstr "Uygula"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Audit"
msgstr "Denetim"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Auto-generated"
msgstr "Otomatik oluşturuldu"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__auto_generated
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__auto_generated
msgid "Automatically Generated"
msgstr "Otomatik Olarak Oluşturuldu"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_avg
msgid "Average Currency Rate"
msgstr "Ortalama Döviz Kuru"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__avg
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Average Rate"
msgstr "Ortalama Oran"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Avg Rate: 1"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__balance
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Balance"
msgstr "Bakiye"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form_onboarding
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form_onboarding
msgid "Cancel"
msgstr "İptal"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__chart_id
msgid "Chart"
msgstr "Plan"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of Accounts"
msgstr "Hesap Planı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Chart of account set !"
msgstr "Hesap planı ayarla!"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Chart of accounts"
msgstr "Hesap Planı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_tree
msgid "Charts"
msgstr "Planlar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__child_ids
msgid "Children"
msgstr "Alt"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Close"
msgstr "Kapat"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Close period"
msgstr "Kapanış dönemi"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__closed
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__closed
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Closed"
msgstr "Kapanmış"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__end
msgid "Closing Rate"
msgstr "Kapanış Oranı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__code
msgid "Code"
msgstr "Kod"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_color
msgid "Color"
msgstr "Renk"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__color
msgid "Color Index"
msgstr "Renk"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_res_company
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__company_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__company_ids
msgid "Companies"
msgstr "Şirketler"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__company_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Company"
msgstr "Şirket"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_company_id
msgid "Company Currency"
msgstr "Şirket Para Birimi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__company_name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Company Name"
msgstr "Şirket Adı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__company_period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Company Period"
msgstr "Şirket Dönemi"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_company_period_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_period_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_company_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Company Periods"
msgstr "Şirket Dönemleri"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__company_unmapped_accounts_counts
msgid "Company Unmapped Accounts Counts"
msgstr "Şirket Eşlenmemiş Hesap Sayısı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_period_id
msgid "Composed Analysis Period"
msgstr "Oluşan Analiz Dönemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__composed_chart_currency_id
msgid "Composed Consolidation Currency"
msgstr "Oluşturulan Konsolidasyon Para Birimi"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration
msgid "Configuration"
msgstr "Yapılandırma"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.header_cell_template
msgid "Conso Rate:"
msgstr ""

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__account_id
msgid "Consolidated Account"
msgstr "Konsolide Hesap"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "Consolidated Accounts"
msgstr "Konsolide Hesaplar"

#. module: account_consolidation
#: model:ir.actions.client,name:account_consolidation.trial_balance_report_action
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Consolidated Balance"
msgstr "Konsolide Bilanço"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Consolidated Companies"
msgstr "Konsolide Şirketler"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__parents_ids
msgid "Consolidated In"
msgstr "Konsolide"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/trial_balance_grid/controller.js:0
#, python-format
msgid "Consolidated balance"
msgstr "Konsolide bakiye"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__used_in_ids
msgid "Consolidated in"
msgstr "Konsolide"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action
#: model:ir.actions.act_window,name:account_consolidation.consolidation_chart_action_onboarding
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__chart_id
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_charts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation"
msgstr "Konsolidasyon"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__account_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Account"
msgstr "Konsolidasyon Hesabı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_account__consolidation_account_chart_filtered_ids
msgid "Consolidation Account Chart Filtered"
msgstr "Filtrelenmiş Konsolidasyon Hesap Planı"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_account_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__using_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__account_ids
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_accounts
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Consolidation Accounts"
msgstr "Konsolidasyon Hesapları"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Consolidation Chart"
msgstr "Konsolidasyon Planı"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_company_period
msgid "Consolidation Company Period"
msgstr "Konsolidasyon Şirketi Dönemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_chart_id
msgid "Consolidation Currency"
msgstr "Konsolidasyon Para Birimi"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.Consolidation_journal_line_action
msgid "Consolidation Entries"
msgstr "Konsolidasyon Kayıtları"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations_consolidation_entries
msgid "Consolidation Entry"
msgstr "Konsolidasyon Kaydı"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_group
msgid "Consolidation Group"
msgstr "Konsolidasyon Grubu"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Consolidation Items"
msgstr "Konsolidasyon Kalemleri"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal
msgid "Consolidation Journal"
msgstr "Konsolidasyon Yevmiyesi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_account_move_line__consolidation_journal_line_ids
msgid "Consolidation Journal Line"
msgstr "Konsolidasyon Yevmiye Kalemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__consolidation_method
msgid "Consolidation Method"
msgstr "Konsolidasyon Yöntemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_name
msgid "Consolidation Name"
msgstr "Konsolidasyon Adı"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__composition_id
msgid "Consolidation Period"
msgstr "Konsolidasyon Dönemi"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_period_composition
msgid "Consolidation Period Composition"
msgstr "Konsolidasyon Dönemi Bileşimi"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_rate
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__rate_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Consolidation Rate"
msgstr "Konsolidasyon Oranı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Consolidation Rate (%)"
msgstr "Konsolidasyon Oranı (%)"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_account
msgid "Consolidation account"
msgstr "Konsolidasyon hesabı"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_chart
msgid "Consolidation chart"
msgstr "Konsolidasyon planı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__line_ids
msgid "Consolidation items"
msgstr "Konsolidasyon kalemleri"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_consolidation_journal_line
msgid "Consolidation journal line"
msgstr "Konsolidasyon yevmiye kalemi"

#. module: account_consolidation
#: model:res.groups,name:account_consolidation.group_consolidation_user
msgid "Consolidation user"
msgstr "Konsolidasyon kullanıcısı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create"
msgstr "Oluştur"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action_onboarding
msgid "Create First Period"
msgstr "İlk Periyot Oluştur"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.create_period_step
msgid "Create your first analysis period &amp; set the currency rates."
msgstr "İlk analiz döneminizi oluşturun &amp; döviz kurlarını ayarlayın."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__create_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currencies_are_different
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currencies_are_different
msgid "Currencies Are Different"
msgstr "Para Birimleri Farklı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Currency"
msgstr "Para Birimi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__currency_amount
msgid "Currency Amount"
msgstr "Döviz tutarı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__currency_mode
msgid "Currency Conversion Method"
msgstr "Para Birimi Dönüştürme Yöntemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__currency_rate
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid "Currency Rate"
msgstr "Para Birimi Çevrim Oranı"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__currency_rate
msgid "Currency rate from composed chart currency to using chart currency"
msgstr "Plan para birimini kullanarak oluşturulan plan para biriminin kuru"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_period_action
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_home
msgid "Dashboard"
msgstr "Pano"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__dashboard_sections
msgid "Dashboard Sections"
msgstr "Yönetim Paneil Bölümleri"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
msgid "Date"
msgstr "Tarih"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Define"
msgstr "Tanımlama"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid ""
"Define the companies that should be consolidated &amp; the target currency"
msgstr ""
"Konsolide edilmesi gereken şirketleri ve hedef para birimini tanımlayın"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__note
msgid "Description"
msgstr "Açıklama"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_dates
msgid "Display Dates"
msgstr "Görüntüleme Tarihleri"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__display_name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__done
msgid "Done"
msgstr "Biten"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_period__state__draft
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Draft"
msgstr "Taslak"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#, python-format
msgid "Edit"
msgstr "Düzenle"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid "End Currency Rate"
msgstr "Son Döviz Kuru"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_end
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_end
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "End Rate"
msgstr "Son Oran"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__equity
msgid "Equity"
msgstr "Özsermaye"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__exclude_journal_ids
msgid "Exclude Journals"
msgstr "Yevmiyeleri Hariç Tut"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Export (XLSX)"
msgstr "Dışa Aktar (XLSX)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__filtered_used_in_ids
msgid "Filtered Used In"
msgstr "Filtreli Kullanılan"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Folded"
msgstr "Katlanmış"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__full_name
msgid "Full Name"
msgstr "Tam Adı"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__full
msgid "Full consolidation"
msgstr "Komple konsolidasyon"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__group_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__group_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Group"
msgstr "Grup"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Group Name"
msgstr "Grup Adı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Groups"
msgstr "Gruplar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_account__currency_mode__hist
msgid "Historical Rate"
msgstr "Geçmiş Oran"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Historical Rates"
msgstr "Geçmiş Oranlar"

#. module: account_consolidation
#: code:addons/account_consolidation/models/res_company.py:0
#, python-format
msgid "Historical Rates: %(company)s"
msgstr "Historical Rates: %(company)s"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_avg
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__currency_rate_end
msgid ""
"How many units of company currency is needed to get 1 unit of chart currency"
msgstr ""
"1 birim plan para birimi elde etmek için kaç birim şirket para birimi "
"gerekir"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__id
msgid "ID"
msgstr "ID"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Future"
msgstr "Gelecekte"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "In The Past"
msgstr "Geçmişte"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal"
msgstr "Yevmiye"

#. module: account_consolidation
#: model:ir.model,name:account_consolidation.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_form
msgid "Journal Item"
msgstr "Yevmiye Kalemi"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.view_account_move_line_filter
msgid "Journal Items"
msgstr "Yevmiye Kalemleri"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__line_ids
msgid "Journal lines"
msgstr "Yevmiye kalemleri"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_journal_action
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__journal_ids
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Journals"
msgstr "Yevmiyeler"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__just_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__just_done
msgid "Just done"
msgstr "Yeni bitti"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition____last_update
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_uid
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__write_date
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_main_attachment_id
msgid "Main Attachment"
msgstr "Ana Ek"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
msgid "Manually Created"
msgstr "Manuel Oluşturuldu"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Map Accounts"
msgstr "Hesapları Eşle"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Mapped Accounts"
msgstr "Eşlenen Hesaplar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree_mapping
msgid "Mapped In"
msgstr "Eşlendi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__move_line_ids
msgid "Move Line"
msgstr "Hareket Kalemi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__name
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__name
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_tree
msgid "Name"
msgstr "Adı"

#. module: account_consolidation
#: model_terms:ir.actions.act_window,help:account_consolidation.account_mapping_action
msgid ""
"No accounts have been found. Make sure you have installed a chart of account"
" for this company or that you have access right to see the accounts of this "
"company."
msgstr ""
"Hesap bulunamadı. Bu şirket için bir hesap planı yüklediğinizden veya bu "
"şirketin hesaplarını görme hakkına sahip olduğunuzdan emin olun."

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.account_mapping_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_search_mapping
msgid "Not Mapped"
msgstr "Eşlenmedi"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__none
msgid "Not consolidated"
msgstr "Konsolide değil"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_create_period_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_dashboard_onboarding_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_ccoa_state__not_done
#: model:ir.model.fields.selection,name:account_consolidation.selection__res_company__consolidation_setup_consolidation_state__not_done
msgid "Not done"
msgstr "Yapılmadı"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of Actions"
msgstr "Eylemlerin Adedi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Eylem gerektiren mesaj adedi"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Number of unread messages"
msgstr "Okunmamış mesaj adedi"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods containing today"
msgstr "Yalnızca bugünü içeren dönemler"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the future"
msgstr "Sadece gelecek dönemler"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Only the periods in the past"
msgstr "Sadece geçmiş dönemler"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_operations
msgid "Operations"
msgstr "Operasyonlar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__originating_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__journal_originating_currency_id
msgid "Originating Currency"
msgstr "Kaynak Para Birimi"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_search
msgid "Parent"
msgstr "Üst"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__parent_path
msgid "Parent Path"
msgstr "Üst Yol"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__period_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__period_id
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Period"
msgstr "Dönem"

#. module: account_consolidation
#: model:ir.ui.menu,name:account_consolidation.menu_consolidation_configuration_analysis_periods
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Periods"
msgstr "Dönemler"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "Print Preview"
msgstr "Çıktı Önizleme"

#. module: account_consolidation
#: model:ir.model.fields.selection,name:account_consolidation.selection__consolidation_company_period__consolidation_method__proportional
msgid "Proportional consolidation"
msgstr "Oransal konsolidasyon"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__rate
msgid "Rate"
msgstr "Oran"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_control
msgid "Rate Control"
msgstr "Oran Kontrolü"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__rate_ownership
msgid "Rate Ownership"
msgstr "Sahiplik Oranı"

#. module: account_consolidation
#: model:ir.actions.act_window,name:account_consolidation.consolidation_rate_action
msgid "Rate Ranges"
msgstr "Oran Aralıkları"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_kanban
msgid "Reopen period"
msgstr "Dönemi yeniden aç"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Reset To Draft"
msgstr "Taslağa Ayarla"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Review Chart Of Accounts"
msgstr "Hesap Planını Gözden Geçirme"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS İleti hatası"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "Scope of Consolidation defined !"
msgstr "Konsolidasyonun Kapsamı Tanımlandı!"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__sequence
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__sequence
msgid "Sequence"
msgstr "Sıra"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
msgid "Settings"
msgstr "Ayarlar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid "Setup"
msgstr "Ayarlar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.chart_of_account_step
msgid ""
"Setup your consolidated accounts and their currency conversion method.\n"
"                Then map them with the companies accounts."
msgstr ""
"Konsolide hesaplarınızı ve para birimi dönüştürme yöntemini ayarlayın.\n"
"                Sonra bunları şirket hesaplarıyla eşleştirin."

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_control
#: model:ir.model.fields,help:account_consolidation.field_consolidation_company_period__rate_ownership
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period_composition__rate_consolidation
msgid "Should be between 0 and 100 %"
msgstr "0 ile %100 arasında olmalıdır"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_group__show_on_dashboard
msgid "Show On Dashboard"
msgstr "Yönetim Panelinde Göster"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.search_template_conso_extra_options
msgid "Show Zero Balance Accounts"
msgstr "Sıfır Bakiyeli Hesapları Göster"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_company_period__date_company_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__date_analysis_begin
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_rate__date_start
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__state
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__state
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_search
msgid "State"
msgstr "Durum"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_dashboard_onboarding_state
msgid "State Of The Account Consolidation Dashboard Onboarding Panel"
msgstr ""
"Hesap Konsolidasyonu Yönetim Panelinde Dinamik Yapılandırma Paneli Durumu"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_ccoa_state
msgid "State Of The Onboarding Consolidated Chart Of Account Step"
msgstr "Dinamik Yapılandırma Panelinde Hesap Planı Adımı Durumu"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_setup_consolidation_state
msgid "State Of The Onboarding Consolidation Step"
msgstr "Dinamik Yapılandırma Panelinde Konsolidasyon Adımı Durumu"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_res_company__consolidation_create_period_state
msgid "State Of The Onboarding Create Period Step"
msgstr "Dinamik Yapılandırma Panelinde Dönem Oluşturma Adımı Durumu"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid "Sub-consolidated Chart"
msgstr "Alt konsolide Plan"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_account__linked_chart_ids
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__children_ids
msgid "Sub-consolidations"
msgstr "Alt konsolidasyonlar"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations Periods"
msgstr "Alt konsolidasyon Dönemleri"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_form
msgid "Sub-consolidations periods"
msgstr "Alt konsolidasyon dönemleri"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "Subgroups"
msgstr "Altgruplar"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies and the company is consolidated at %s%%."
msgstr ""
"Konsolidasyonun  (%s) ve konsolide şirketin (%s) farklı para birimlerine "
"sahip olduğunu ve şirketin %s%% oranında konsolide olduğunu dikkate alın. "

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid ""
"Take into account that the consolidation (%s) and the consolidated company "
"(%s) have different currencies."
msgstr ""
"Konsolidasyonun (%s) ve konsolide şirketin (%s) farklı para birimlerine "
"sahip olduğunu dikkate alın. "

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/js/move_line_list/renderer.js:0
#, python-format
msgid "Take into account that this company is consolidated at %s%%."
msgstr "Bu şirketin konsolide olduğunu göz önünde bulundurarak %s%%."

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_chart__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal__currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_journal_line__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__chart_currency_id
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period_composition__using_chart_currency_id
msgid "Target Currency"
msgstr "Hedef Para Birimi"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid ""
"The Composed Analysis Period must be different from the Analysis Period"
msgstr ""
"The Composed Analysis Period must be different from the Analysis Period"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_step
msgid "The Scope of Consolidation"
msgstr "Konsolidasyonun Kapsamı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_company_period_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_period_composition_form
msgid ""
"The rate used for the consolidation (basically this rate will multiply the "
"sum of everything"
msgstr ""
"Konsolidasyon için kullanılan oran (temel olarak bu oran her şeyin toplamını"
" çoğaltacaktır)"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "This journal has been automatically generated on"
msgstr "Bu yevmiye otomatik olarak tarihinde oluşturuldu"

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/abstract.py:0
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_tree
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_tree
#, python-format
msgid "Total"
msgstr "Toplam"

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_line_search
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_graph
#: model_terms:ir.ui.view,arch_db:account_consolidation.view_trial_balance_report_grid
#, python-format
msgid "Trial Balance"
msgstr "Geçici Mizan"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_period.py:0
#, python-format
msgid "Trial Balance: %s"
msgstr "Geçici Mizan: %s"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.trial_balance_grid_search
msgid "Trial balance"
msgstr "Geçici mizan"

#. module: account_consolidation
#: model:ir.model.constraint,message:account_consolidation.constraint_consolidation_period_composition__unique_composition
msgid ""
"Two compositions of the same analysis period by the same analysis period "
"cannot be created"
msgstr ""
"Aynı analiz periyodu ile aynı analiz periyodundan iki bileşim oluşturulamaz"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.line_template
msgid "Unfolded"
msgstr "Genişlet"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread
msgid "Unread Messages"
msgstr "Okunmamış Mesajlar"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Okunmamış Mesaj Sayacı"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_journal_form
msgid "Update"
msgstr "Güncelle"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__used_in_composition_ids
msgid "Used In Composition"
msgstr "Bileşimde Kullanılan"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_chart__color
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__color
msgid "Used in the kanban view"
msgstr "Kanban görünümünde kullanılır"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__using_composition_ids
msgid "Using Composition"
msgstr "Bileşim Kullanma"

#. module: account_consolidation
#: model:ir.model.fields,field_description:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website Messages"
msgstr "Websitesi Mesajları"

#. module: account_consolidation
#: model:ir.model.fields,help:account_consolidation.field_consolidation_period__website_message_ids
msgid "Website communication history"
msgstr "Websitesi iletişim geçmişi"

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid ""
"When setting a period on a consolidation journal, the selected consolidation"
" chart for the journal cannot be different from the one of the chosen "
"period."
msgstr ""
"Bir konsolidasyon yevmiyesinde, bir dönem ayarlarken, yevmiye için seçilen "
"konsolidasyon tablosu seçilen döneminkinden farklı olamaz."

#. module: account_consolidation
#: code:addons/account_consolidation/report/trial_balance.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_chart_form
msgid ""
"You can here define complex consolidations based on other sub-"
"consolidations, as part of a whole scheme"
msgstr ""
"Burada, bütün bir şemanın parçası olarak diğer alt konsolidasyonlara dayalı "
"karmaşık konsolidasyonlar tanımlayabilirsiniz."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't delete an auto-generated journal entry."
msgstr "You can't delete an auto-generated journal entry."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You can't edit an auto-generated journal entry."
msgstr "Otomatik olarak oluşturulan bir yevmiye kaydını düzenleyemezsiniz."

#. module: account_consolidation
#: code:addons/account_consolidation/models/consolidation_journal.py:0
#, python-format
msgid "You cannot add journals to a closed period !"
msgstr "Kapalı bir döneme yevmiye ekleyemezsiniz!"

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_group_form
msgid "e.g. Profit and Loss"
msgstr ""

#. module: account_consolidation
#: model_terms:ir.ui.view,arch_db:account_consolidation.consolidation_account_form
msgid "e.g. Revenue"
msgstr ""

#. module: account_consolidation
#: code:addons/account_consolidation/report/builder/comparison.py:0
#, python-format
msgid "n/a"
msgstr "yok"

#. module: account_consolidation
#. openerp-web
#: code:addons/account_consolidation/static/src/xml/fields_templates.xml:0
#, python-format
msgid "unmapped accounts"
msgstr "eşleştirilmemiş hesaplar"
