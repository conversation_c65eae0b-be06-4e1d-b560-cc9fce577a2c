<odoo>
    <data>
        <!-- account.move.line views -->

        <record id="view_move_line_tree_grouped_general" model="ir.ui.view">
            <field name="inherit_id" ref="account.view_move_line_tree_grouped_general"/>
            <field name="name">account.consolidation.move.line.tree</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="attributes">
                    <attribute name="js_class">consolidation_move_line_list</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_account_move_line_filter_search" model="ir.ui.view">
            <field name="inherit_id" ref="account.view_account_move_line_filter"/>
            <field name="name">account.consolidation.move.line.search</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='tax_ids']" position="after">
                    <field name="consolidation_journal_line_ids"/>
                </xpath>
            </field>
        </record>

        <record id="view_account_move_line_filter" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="view_account_move_line_filter_search"/>
        </record>
    </data>
</odoo>
