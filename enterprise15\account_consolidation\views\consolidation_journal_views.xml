<odoo>
    <data>
        <!-- consolidation.journal views -->

        <record id="consolidation_journal_tree" model="ir.ui.view">
            <field name="name">consolidation.journal.tree</field>
            <field name="model">consolidation.journal</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree string="Journals" create="false">
                    <field name="name"/>
                    <field name="period_id"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="auto_generated"/>
                    <field name="balance" sum="Total"/>
                </tree>
            </field>
        </record>

        <record id="consolidation_journal_form" model="ir.ui.view">
            <field name="name">consolidation.journal.form</field>
            <field name="model">consolidation.journal</field>
            <field name="arch" type="xml">
                <form string="Journal" create="false">
                    <field name="state" invisible="1"/>
                    <field name="auto_generated" invisible="1"/>
                    <field name="chart_id" invisible="1"/>
                    <div class="alert alert-info text-center" role="status"
                         attrs="{'invisible': [('auto_generated','=', False)]}">
                        This journal has been automatically generated on
                        <field name="write_date" widget="date"/>.
                        <button name="action_generate_journal_lines" type="object"
                                string="Update" class="btn-link align-bottom m-0"
                                groups="account_consolidation.group_consolidation_user"
                                attrs="{'invisible': ['|', ('state', '=', 'closed'), ('id', '=', False)]}"/>
                    </div>
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" class="oe_inline"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="period_id"
                                       domain="[('state', '=', 'draft')]"
                                       options="{'no_create': True, 'no_open': True}"
                                       attrs="{'readonly': [('auto_generated', '=', True)]}"
                                       invisible="context.get('default_period_id', False)"/>
                                <label class="oe_read_only" for="rate_consolidation" attrs="{'invisible': [('auto_generated', '=', False)]}"/>
                                <div class="oe_read_only" attrs="{'invisible': [('auto_generated', '=', False)]}">
                                    <field name="rate_consolidation" class="oe_inline"/>
                                    <span class="o_form_label oe_inline">%</span>
                                </div>
                            </group>
                            <group>
                                <field name="chart_id"/>
                                <field name="company_period_id" readonly="1" options="{'no_open': True}"
                                       attrs="{'invisible': ['|',('id', '=', False),('company_period_id','=', False)]}"/>
                                <field name="composition_id" readonly="1" options="{'no_open': True}"
                                       attrs="{'invisible': ['|',('id', '=', False),('composition_id','=', False)]}"/>
                                <field name="currencies_are_different" invisible="1"/>
                            </group>
                        </group>
                        <field name="line_ids" nolabel="1"
                               attrs="{'invisible': [('id', '=', False)], 'readonly': [('auto_generated', '=', True)]}">
                            <tree editable="bottom">
                                <field name="journal_originating_currency_id" invisible="1"/>
                                <field name="account_id"/>
                                <field name="note"/>
                                <field name="chart_currency_id" invisible="1"/>
                                <field name="currency_amount" sum="Total" widget="monetary"
                                       attrs="{'column_invisible': [('parent.currencies_are_different', '=', False)]}"
                                       options="{'currency_field': 'journal_originating_currency_id'}"/>
                                <field name="amount" string="Balance" sum="Total"/>
                            </tree>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="consolidation_journal_search" model="ir.ui.view">
            <field name="name">consolidation.journal.search</field>
            <field name="model">consolidation.journal</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="period_id"/>
                    <filter string="Auto-generated" name="autogen" domain="[('auto_generated','=', True)]"/>
                    <filter string="Manually Created" name="created" domain="[('auto_generated','=', False)]"/>
                    <filter name="group_period_id" string="Period"
                            context="{'group_by': 'period_id'}"/>
                </search>
            </field>
        </record>

        <record id="consolidation_journal_action" model="ir.actions.act_window">
            <field name="name">Journals</field>
            <field name="res_model">consolidation.journal</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="consolidation_journal_search"/>
        </record>

        <!-- consolidation.journal.line views -->

        <record id="consolidation_journal_line_tree" model="ir.ui.view">
            <field name="name">consolidation.journal.line.tree</field>
            <field name="model">consolidation.journal.line</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <tree string="Consolidation Items" editable="top">
                    <field name="journal_originating_currency_id" invisible="1"/>
                    <field name="chart_currency_id" invisible="1"/>
                    <field name="create_date" string="Date"/>
                    <field name="account_id"/>
                    <field name="note"/>
                    <field name="currency_amount" widget="monetary" sum="Total"
                           options="{'currency_field': 'journal_originating_currency_id'}"/>
                    <field name="amount" sum="Total"/>
                </tree>
            </field>
        </record>

        <record id="consolidation_journal_line_form" model="ir.ui.view">
            <field name="name">consolidation.journal.line.form</field>
            <field name="model">consolidation.journal.line</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <form string="Journal Item">
                    <field name="auto_generated" invisible="1"/>
                    <sheet>
                        <group>
                            <group>
                                <field name="account_id" attrs="{'readonly': [('auto_generated', '=', True)]}"
                                       invisible="context.get('default_account_id', False)"/>
                                <field name="amount" attrs="{'readonly': [('auto_generated', '=', True)]}"/>
                            </group>
                            <group>
                                <field name="journal_id" invisible="context.get('default_journal_id', False)"
                                       attrs="{'readonly': [('auto_generated', '=', True)]}"
                                       string="Journal"/>
                                <field name="period_id" attrs="{'readonly': [('auto_generated', '=', True)]}"
                                       invisible="context.get('default_period_id', False)"
                                />
                            </group>
                        </group>
                        <group>
                            <field name="note"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="consolidation_journal_line_search" model="ir.ui.view">
            <field name="name">consolidation.journal.line.search</field>
            <field name="model">consolidation.journal.line</field>
            <field name="arch" type="xml">
                <search string="Trial Balance">
                    <field name="period_id"/>
                    <field name="journal_id" string="Company"/>
                    <field name="account_id"/>
                    <field name="group_id" string="Group"/>
                    <filter name="group_account_id" context="{'group_by': 'account_id'}"/>
                </search>
            </field>
        </record>

        <record id="Consolidation_journal_line_action" model="ir.actions.act_window">
            <field name="name">Consolidation Entries</field>
            <field name="res_model">consolidation.journal.line</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="consolidation_journal_line_search"/>
        </record>
    </data>
</odoo>
