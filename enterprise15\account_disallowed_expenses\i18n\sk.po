# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.main_template_de
msgid ""
"<span>There are multiple disallowed expenses rates in this period</span>"
msgstr ""
"<span>V tomto období existuje niekoľko nepovolených mier výdavkov</span>"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_account
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__account_ids
msgid "Account"
msgstr "Účet"

#. module: account_disallowed_expenses
#: code:addons/account_disallowed_expenses/models/account_disallowed_expenses.py:0
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
#, python-format
msgid "Accounts"
msgstr "Účty"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Active"
msgstr "Aktívne"

#. module: account_disallowed_expenses
#: model_terms:ir.actions.act_window,help:account_disallowed_expenses.action_account_disallowed_expenses_category_list
msgid "Add a Disallowed Expenses Category"
msgstr "Pridajte kategóriu nepovolené výdavky"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__category_id
msgid "Category"
msgstr "Kategória"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__code
msgid "Code"
msgstr "Kód"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__company_id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__company_id
msgid "Company"
msgstr "Spoločnosť"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__create_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: account_disallowed_expenses
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Disallowed Amount"
msgstr "Nepovolená suma"

#. module: account_disallowed_expenses
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_report_de
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_form
#, python-format
msgid "Disallowed Expenses"
msgstr "Nepovolené výdavky"

#. module: account_disallowed_expenses
#: model:ir.actions.act_window,name:account_disallowed_expenses.action_account_disallowed_expenses_category_list
#: model:ir.ui.menu,name:account_disallowed_expenses.menu_action_account_disallowed_expenses_category_list
msgid "Disallowed Expenses Categories"
msgstr "Kategórie nepovolených výdavkov"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_category
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_account__disallowed_expenses_category_id
msgid "Disallowed Expenses Category"
msgstr "Kategória nepovolené výdavky"

#. module: account_disallowed_expenses
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_rate
msgid "Disallowed Expenses Rate"
msgstr "Miera nepovolených výdavkov"

#. module: account_disallowed_expenses
#: model:ir.actions.client,name:account_disallowed_expenses.action_account_report_de
#: model:ir.model,name:account_disallowed_expenses.model_account_disallowed_expenses_report
msgid "Disallowed Expenses Report"
msgstr "Správa o nepovolených výdavkoch"

#. module: account_disallowed_expenses
#: model:ir.model.constraint,message:account_disallowed_expenses.constraint_account_disallowed_expenses_category_unique_code_in_country
msgid "Disallowed expenses category code should be unique in each company."
msgstr ""
"Kód kategórie nepovolených výdavkov by mal byť v každej spoločnosti "
"jedinečný."

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__display_name
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__id
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__id
msgid "ID"
msgstr "ID"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category____last_update
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_uid
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__write_date
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__name
msgid "Name"
msgstr "Meno"

#. module: account_disallowed_expenses
#: code:addons/account_disallowed_expenses/models/account_disallowed_expenses.py:0
#, python-format
msgid "No Rate"
msgstr "Bez sadzby"

#. module: account_disallowed_expenses
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_category__rate_ids
#, python-format
msgid "Rate"
msgstr "Kurz"

#. module: account_disallowed_expenses
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses.view_account_disallowed_expenses_category_form
msgid "Rates"
msgstr "Kurzy"

#. module: account_disallowed_expenses
#: model:ir.model.fields,help:account_disallowed_expenses.field_account_disallowed_expenses_category__active
msgid "Set active to false to hide the category without removing it."
msgstr ""
"Nastavením možnosti Aktívne na hodnotu False skryjete kategóriu bez jej "
"odstránenia."

#. module: account_disallowed_expenses
#: model:ir.model.fields,field_description:account_disallowed_expenses.field_account_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr "Dátum začiatku"

#. module: account_disallowed_expenses
#: code:addons/account_disallowed_expenses/report/account_disallowed_expenses_report.py:0
#, python-format
msgid "Total Amount"
msgstr "Celková suma"
