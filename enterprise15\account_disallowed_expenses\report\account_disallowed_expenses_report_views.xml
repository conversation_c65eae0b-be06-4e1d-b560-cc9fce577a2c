<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="action_account_report_de" model="ir.actions.client">
            <field name="name">Disallowed Expenses Report</field>
            <field name="tag">account_report</field>
            <field name="context" eval="{'model': 'account.disallowed.expenses.report'}" />
        </record>

        <template id="main_template_de" inherit_id="account_reports.main_template">
            <xpath expr="//div[hasclass('o_account_reports_page')]" position="before">
                <div class="alert alert-info text-center mb-0 no_print" t-if="options.get('multi_rate_in_period')">
                    <span>There are multiple disallowed expenses rates in this period</span>
                </div>
            </xpath>
        </template>

        <menuitem id="menu_action_account_report_de"
                  name="Disallowed Expenses"
                  action="action_account_report_de"
                  parent="account.account_reports_management_menu"
                  groups="account.group_account_readonly"/>
    </data>
</odoo>
