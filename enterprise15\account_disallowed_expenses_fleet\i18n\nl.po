# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses_fleet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_account_disallowed_expenses_category__car_category
msgid "Car Category"
msgstr "Autocategorie"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_disallowed_expenses_category
msgid "Disallowed Expenses Category"
msgstr "Niet-toegestane kostencategorie"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_vehicle__rate_ids
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses_fleet.fleet_vehicle_view_form
msgid "Disallowed Expenses Rate"
msgstr "Niet-toegestaan kostenratio"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_disallowed_expenses_report
msgid "Disallowed Expenses Report"
msgstr "Niet-toegestaan kostenrapportage"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__id
msgid "ID"
msgstr "ID"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr "Startdatum"

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,help:account_disallowed_expenses_fleet.field_account_disallowed_expenses_category__car_category
msgid "This checkbox makes the vehicle mandatory while booking a vendor bill."
msgstr ""
"Dit selectievakje maakt het voertuig verplicht bij het boeken van een "
"leveranciersfactuur."

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__vehicle_id
msgid "Vehicle"
msgstr "Voertuig"

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_fleet_disallowed_expenses_rate
msgid "Vehicle Disallowed Expenses Rate"
msgstr "Tarief voor niet-toegestane voertuigkosten"
