<?xml version="1.0"?>
<odoo>
    <!-- Mail template is done in a NOUPDATE block
         so users can freely customize/delete them -->
    <data noupdate="1">
        <record id="demo_followup_line1" model="account_followup.followup.line" forcecreate="False">
            <field name="name">First reminder email</field>
            <field name="delay">15</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="send_email">True</field>
            <field name="description">
Dear %(partner_name)s,

Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.

Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.

Best Regards,
            </field>
        </record>

        <record id="action_account_followup" model="ir.actions.client">
            <field name="name">Customers Statement</field>
            <field name="tag">account_report_followup</field>
            <field name="context" eval="{'model': 'account.followup.report'}" />
        </record>
    </data>
</odoo>
