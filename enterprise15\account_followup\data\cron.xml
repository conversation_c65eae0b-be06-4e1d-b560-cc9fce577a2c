<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_auto_post_draft_entry" model="ir.cron">
        <field name="name">Account Report Followup; Execute followup</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="nextcall" eval="(DateTime.now().replace(hour=2, minute=0) + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')" />
        <field name="doall" eval="False"/>
        <field name="model_id" ref="model_res_partner"/>
        <field name="code">model._cron_execute_followup()</field>
        <field name="state">code</field>
    </record>
</odoo>
