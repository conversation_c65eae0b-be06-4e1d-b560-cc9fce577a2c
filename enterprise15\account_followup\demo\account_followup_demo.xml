<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data noupdate="1">
        <record id="demo_followup_line2" model="account_followup.followup.line" forcecreate="False">
            <field name="name">Second reminder letter and email</field>
            <field name="delay">30</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="send_email">True</field>
            <field name="print_letter">True</field>
            <field name="description">
Dear %(partner_name)s,

We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.

It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).
Please, take appropriate measures in order to carry out this payment in the next 8 days.

If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.

Details of due payments is printed below.

Best Regards,
            </field>
        </record>
        <record id="demo_followup_line3" model="account_followup.followup.line" forcecreate="False">
            <field name="name">Third reminder: phone the customer</field>
            <field name="delay">40</field>
            <field name="company_id" ref="base.main_company"/>
            <field eval="False" name="send_email"/>
            <field name="manual_action">True</field>
            <field name="manual_action_type_id" ref="mail.mail_activity_data_call"/>
            <field name="manual_action_note">Call the customer on the phone! </field>
            <field name="description">
Dear %(partner_name)s,

Despite several reminders, your account is still not settled.

Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.

I trust that this action will prove unnecessary and details of due payments is printed below.

In case of any queries concerning this matter, do not hesitate to contact our accounting department.

Best Regards,
            </field>
        </record>
        <record id="demo_followup_line4" model="account_followup.followup.line">
            <field name="name">Urging reminder email</field>
            <field name="delay">50</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="send_email">True</field>
            <field name="description">
Dear %(partner_name)s,

Despite several reminders, your account is still not settled.

Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.

I trust that this action will prove unnecessary and details of due payments is printed below.

In case of any queries concerning this matter, do not hesitate to contact our accounting department.

Best Regards,
            </field>
        </record>
        <record id="demo_followup_line5" model="account_followup.followup.line">
            <field name="name">Urging reminder letter</field>
            <field name="delay">60</field>
            <field name="company_id" ref="base.main_company"/>
            <field eval="False" name="send_email"/>
            <field name="print_letter">True</field>
            <field name="description">
Dear %(partner_name)s,

Despite several reminders, your account is still not settled.

Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.

I trust that this action will prove unnecessary and details of due payments is printed below.

In case of any queries concerning this matter, do not hesitate to contact our accounting department.

Best Regards,
            </field>
        </record>
    </data>
</odoo>
