# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-20 09:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,description:account_followup.demo_followup_line5
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Despite several reminders, your account is still not settled.\n"
"\n"
"Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"عزيزي %(partner_name)s،\n"
"\n"
"مع أنك استلمت عدة تذكيرات، إلا أن حسابك غير مسوى بعد.\n"
"\n"
"سوف يتم اتخاذ إجراء قانوني لتحصيل الدين دون إشعار آخر إلا إذا قمت بدفع المبلغ كاملاً خلال الـ8 أيام القادمة.\n"
"\n"
"نحن على ثقة من أننا لن نضطر إلى اتخاذ ذلك الإجراء، وسوف تجد تفاصيل الدفع المستحق أدناه.\n"
"\n"
"إذا كانت لديك أي استفسارات حول الأمر، لا تتردد في التواصل مع قسم المحاسبة لدينا.\n"
"\n"
"مع أطيب التحيات،\n"
"            "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,description:account_followup.demo_followup_line1
#, python-format
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"عزيزي %(partner_name)s،\n"
"\n"
"يبدو أن المبلغ التالي لا يزال غير مدفوع، إلا إذا كان الخطأ من طرفنا. الرجاء اتخاذ الإجراءات المناسبة لترحيل هذا الدفع في غضون الـ8 أيام القادمة.\n"
"\n"
"إذا تم ترحيل الدفع بعد أن تم إرسال هذا البريد الإلكتروني، رجاءً تجاهَل هذه الرسالة. لا تتردد في التواصل مع قسم المحاسبة لدينا.\n"
"\n"
"مع أطيب التحيات،\n"
"            "

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line2
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"\n"
"Details of due payments is printed below.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"عزيزي %(partner_name)s،\n"
"\n"
"لقد أُحبطنا لرؤية أنه مع أننا أرسلنا لك تذكيراً، إلا أن حسابك متأخر الدفع كثيراً.\n"
"\n"
"عليك إتمام الدفع فوراً وإلا فسوف نضطر إلى اعتبار تجميد حسابك، مما يعني أننا لن نتمكن من تزويد شركتك (بالمنتجات/الخدمات) بعد الآن.\n"
"الرجاء اتخاذ الإجراءات المناسبة لترحيل هذا الدفع في غضون الـ8 أيام القادمة.\n"
"\n"
"إذا كانت لديك مشكلة لا علم لنا بها في دفع الفاتورة، لا تتردد في التواصل مع قسم المحاسبة لدينا حتى نتمكن من حلها بسرعة.\n"
"\n"
"سوف تجد تفاصيل الدفع المستحق أدناه.\n"
"\n"
"مع أطيب التحيات،\n"
"            "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line1
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line2
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line3
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line4
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line5
#, python-format
msgid "%(company_name)s Payment Reminder - %(partner_name)s"
msgstr "%(company_name)s تذكير الدفع - %(partner_name)s"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Followups'"
msgstr "'متابَعات'"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Amount Due by the partner"
msgstr ": المبلغ المستحق من قِبَل الشريك "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Current Date"
msgstr ": التاريخ الحالي"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ": اسم الشريك"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User Name"
msgstr ": اسم المستخدم"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr ": اسم شركة المستخدم "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Email Subject:</b><br/>"
msgstr "<b>موضوع البريد الإلكتروني:</b><br/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Next Reminder Date:</b>"
msgstr "<b>تاريخ التذكير القادم:</b>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: green;\"/> مدين ملتزم "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: grey;\"/> مدين عادي"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: red;\"/> مدين معسر "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"تحرير موضوع البريد "
"الإلكتروني \"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr ""
"<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" "
"title=\"الهاتف المحمول \"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"الهاتف \"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> الشركاء:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr "<span class=\"o_stat_text\">المستحق</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr "<strong>تحذير!</strong>لا حاجة لاتخاذ أي إجراء لهذا الشريك. "

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""
"يجب أن يكون اسم إجراء المتابعة فريداً. هذا الاسم مستخدَم في إجراء آخر "
"بالفعل. "

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr "قالب مخطط الحساب"

#. module: account_followup
#: model:ir.model,name:account_followup.model_report_account_followup_report_followup_print_all
msgid "Account Follow-up Report"
msgstr "تقرير متابعة الحساب "

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr "متابعة تقرير الحساب؛ لتنفيذ المتابعة "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_note
msgid "Action To Do"
msgstr "إجراء مطلوب"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "الإجراءات"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "إضافة ملاحظة"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add an email subject"
msgstr "أضف موضوعاً للبريد الإلكتروني "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "After"
msgstr "بعد"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"An error has occurred while formatting your followup letter/email. (Lang: %s, Followup Level: #%s) \n"
"\n"
"Full error description: %s"
msgstr ""
"لقد وقع خطأ أثناء تنسيق البريد الإلكتروني/رسالة المتابعة. (اللغة: %s, مستوى المتابعة: #%s) \n"
"\n"
"الوصف الكامل للخطأ: %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr "تعيين مسؤول "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
msgid "Auto Execute"
msgstr "التنفيذ الآلي "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Bad debtor"
msgstr "مدين معسر "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "Change expected payment date"
msgstr "تغيير تاريخ السداد المتوقع"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Close"
msgstr "إغلاق"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr "اتصالات"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "الشركة"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "جهة اتصال "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Could not send mail to partner %s because it does not have any email address"
" defined"
msgstr ""
"تعذر إرسال البريد الإلكتروني إلى الشريك %s لأنه لا يملك عنوان بريد إلكتروني "
"محدد "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "مرجع العميل: "

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "كشوفات حسابات العملاء "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"Date at which Odoo will remind you to take care of that follow-up if you "
"choose \"remind me later\" button."
msgstr ""
"التاريخ الذي سوف يقوم أودو بتذكيرك فيه بالاهتمام بتلك المراجعة، في حال قمت "
"باختيار زر \"ذكرني لاحقاً\". "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "التاريخ:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up levels must be different per company"
msgstr "يجب أن تكون أيام مستويات المتابعة مختلفة لدى كل شركة "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line1
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line2
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line5
#, python-format
msgid "Dear %(partner_name)s, it seems that some of your payments stay unpaid"
msgstr "عزيزي %(partner_name)s، يبدو أن بعض مدفوعاتك لا تزال غير مسددة "

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "قم بتحديد مستويات المتابعة والإجراءات المتعلقة بها "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Description"
msgstr "الوصف"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Done"
msgstr "منتهي "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr "تاريخ الاستحقاق"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "أيام الاستحقاق"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Edit Summary"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__email_subject
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__email_subject
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Email Subject"
msgstr "موضوع البريد الإلكتروني "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Excluded"
msgstr "مستثنى "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Expected Date"
msgstr "التاريخ المتوقع"

#. module: account_followup
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr "التذكير الأول "

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "First reminder email"
msgstr "أول تذكير عبر البريد الإلكتروني "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Follow-Up Action"
msgstr "إجراء المتابعة"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "معايير المتابعة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_level
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_level
msgid "Follow-up Level"
msgstr "مستوى المتابعة"

#. module: account_followup
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "مستويات المتابعة"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "تقرير المتابعة"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr "تقارير المتابعة"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "عرض تقارير المتابعة بطريقة متفرعة "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_responsible_id
msgid "Follow-up Responsible"
msgstr "مسؤول المتابعة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "حالة المتابعة "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "خطوات المتابعة"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter printed"
msgstr "تمت طباعة رسالة المتابعة "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr "تقارير المتابعة"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr "المتابعات التي تم إجراؤها / مجموع المتابعات "

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""
"لكل خطوة، حدد الإجراءات التي يجب اتخاذها ومهلة التأخير بالأيام.\n"
"                من الممكن استخدام قوالب الطباعة والبريد الإلكتروني لإرسال رسائل محددة\n"
"                للعميل."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Good debtor"
msgstr "مدين ملتزم "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
msgid "ID"
msgstr "المُعرف"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr "بحاجة إلى اتخاذ إجراء "

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr "بحاجة إلى اتخاذ إجراء "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "In order to build customized messages:"
msgstr "حتى تتمكن من إنشاء رسائل مخصصة: "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
msgid "Join open Invoices"
msgstr "دمج الفواتير المفتوحة "

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr "آخر متابعة"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "إدارة الملخص وذيول التقارير"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#, python-format
msgid "Manual Action"
msgstr "إجراء يدوي"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_type_id
msgid "Manual Action Type"
msgstr "نوع الإجراء اليدوي"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Manual action done"
msgstr "الإجراء اليدوي الذي تم تنفيذه "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Message"
msgstr "رسالة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr "تاريخ الإجراء التالي"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr "تم تعيين تاريخ التذكير التالي لـ %s"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "لا حاجة لاتخاذ إجراء "

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "لا يوجد متابعة لإرسالها!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "No followup to send!"
msgstr "لا يوجد متابعة لإرسالها! "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Normal debtor"
msgstr "مدين عادي "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid ""
"Odoo will remind you to take care of this follow-up on the next reminder "
"date."
msgstr "سوف يذكرك أودو بإتمام هذه المتابعة في تاريخ التذكير القادم. "

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr ""
"بيدك الاختيار إذا كنت ترغب في تعيين مستخدم لهذا الحقل وجعله مسؤولاً عن هذا "
"الإجراء. "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "الخيارات"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "الفواتير المتأخرة "

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr "مدفوعات متأخرة لـ %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr "قيود الشريك"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
msgid "Payment Follow-ups"
msgstr "متابعة التحصيلات"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Payment Reminder"
msgstr "تذكير الدفع "

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "طباعة رسالة المتابعة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__print_letter
msgid "Print a Letter"
msgstr "طباعة رسالة"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Print letter"
msgstr "طباعة الرسالة"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__description
msgid "Printed Message"
msgstr "الرسالة المطبوعة"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process follow-ups"
msgstr "معالجة المراجعات "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Reconcile"
msgstr "تسوية"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Remind me later"
msgstr "ذكرني لاحقًا"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_description
msgid "SMS Text Message"
msgstr "رسالة نصية قصيرة"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Save"
msgstr "حفظ"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "البحث عن المتابعات "

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "Second reminder letter and email"
msgstr "خطاب ورسالة بريد إلكتروني بالتذكير الثاني "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "إرسال رسالة نصية قصيرة "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send an Email"
msgstr "إرسال بريد إلكتروني"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send an SMS Message"
msgstr "إرسال رسالة نصية قصيرة "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Send an email"
msgstr "إرسال رسالة بريد إلكتروني"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by email"
msgstr "إرسال عبر البريد الإلكتروني "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by sms"
msgstr "الإرسال عبر الرسائل النصية القصيرة "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Source Document"
msgstr "المستند المصدر"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__payment_next_action_date
msgid "The date before which no action should be taken."
msgstr "التاريخ الذي لا تحتاج للقيام بأي إجراء قبله."

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up report was successfully emailed!"
msgstr "تم إرسال تقرير المتابعة عبر البريد الإلكتروني بنجاح! "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up was successfully sent!"
msgstr "تم إرسال المتابعة بنجاح! "

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""
"أقصى مهلة من الأيام بعد تاريخ استحقاق الفاتورة قبل إرسال رسالة التذكير. "
"يمكنك استخدام رقم سالب لإرسال تنبيه ودّي قبل ذلك. "

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "Third reminder: phone the customer"
msgstr "التذكير الثالث: الاتصال بالعميل عبر الهاتف "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "الإجمالي"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr "الإجمالي المستحق"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr "الإجمالي المتأخر"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total credit"
msgstr "إجمالي الدائن"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total debit"
msgstr "إجمالي المدين"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices
msgid "Unpaid Invoices"
msgstr "الفواتير غير المدفوعة "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "المبلغ غير المسوى "

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "Urging reminder email"
msgstr "تذكير عبر البريد الإلكتروني للحث "

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "Urging reminder letter"
msgstr "رسالة تذكير للحث "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "View Invoice"
msgstr "عرض الفاتورة"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__print_letter
msgid "When processing, it will print a PDF"
msgstr "عند إجراء المعالجة، ستقوم بطباعة ملف PDF"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_email
msgid "When processing, it will send an email"
msgstr "عند إجراء المعالجة، سيقوم بإرسال بريد إلكتروني"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_sms
msgid "When processing, it will send an sms text message"
msgstr "عند إجراء المعالجة، سوف يقوم بإرسال رسالة نصية قصيرة "

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr ""
"عند إجراء المعالجة، سيقوم بتحديد الإجراء اليدوي اللازم اتخاذه مع العميل. "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr "مع الفواتير المتأخرة"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "مع الفواتير المتأخرة"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter and mail or sms,\n"
"                                            according to the level of the follow-up. You can\n"
"                                            use the following keywords in the text. Don't\n"
"                                            forget to translate in all languages you installed\n"
"                                            using to top right icon."
msgstr ""
"قم بكتابة مقدمة الرسالة أو البريد الإلكتروني أو الرسالة النصية القصيرة هنا،\n"
"                                            بناء على مستوى المتابعة. بإمكانك استخدام\n"
"                                            الكلمات المفتاحبة التالية في النص. لا تنس\n"
"                                            ترجمتها إلى كافة اللغات التي قمت بتثبيتها\n"
"                                            باستخدام الأيقونة في الأعلى إلى اليسار. "

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/js/followup_form_controller.js:0
#, python-format
msgid "You are done with the follow-ups!<br/>You have skipped %s partner(s)."
msgstr "لقد أنهيت المتابعة! <br/>لقد قمت بتخطي %s شريك. "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a followup report to a partner for which you didn't "
"print all the invoices ({})"
msgstr ""
"أنت تحاول إرسال تقرير متابعة لشريك لم تقم بطباعة كافة الفواتير من أجله ({}) "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You need a least one follow-up level in order to process your follow-up"
msgstr ""
"أنت بحاجة إلى مستوى متابعة واحد على الأقل حتى تتمكن من معالجة المتابعة "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""
"وصفك غير صالح، استخدم الأداة المناسبة أو %% إذا كنت ترغب في استخدام رمز "
"النسبة المئوية. "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your email subject is invalid, use the right legend or %% if you want to use"
" the percent character."
msgstr ""
"موضوع بريدك الإلكتروني غير صالح، استخدم الأداة المناسبة أو %% إذا كنت ترغب "
"في استخدام رمز النسبة المئوية. "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your sms description is invalid, use the right legend or %% if you want to "
"use the percent character."
msgstr ""
"وصف الرسائل النصية القصيرة لديك غير صالح، استخدم الأداة المناسبة أو %% إذا "
"كنت ترغب في استخدام رمز النسبة المئوية. "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "أيام متأخرة، لذا عليك القيام بالإجراءات التالية:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr "مثال: الاتصال بالعملاء، ومعرفة إذا ما كان قد تم دفعها، ... "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "مثال: البريد الإلكتروني التذكيري الأول "

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr "تذكير بالدفع "
