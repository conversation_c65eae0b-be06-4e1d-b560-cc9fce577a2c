# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2021
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-20 09:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,description:account_followup.demo_followup_line5
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Despite several reminders, your account is still not settled.\n"
"\n"
"Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"Kære %(partner_name)s,\n"
"\n"
"Trods flere påmindelser, er din konto stadig ikke afviklet.\n"
"\n"
"Medminde fuld betaling finder sted indenfor de næste 8 dage, vil retslige skridt til inddrivelse af gælden blive taget, uden yderligere varsel.\n"
"\n"
"Jeg regner med at denne handling vil vise sig, at være unødvendig, og detaljer om den fulde betaling står skrevet nedenunder.\n"
"\n"
"Tøv ikke med at kontakte vores regnskabsafdeling, I tilfælde af spørgsmål vedrørende denne sag.\n"
"\n"
"Venlig hilsen,\n"
" "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,description:account_followup.demo_followup_line1
#, python-format
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"Kære %(partner_name)s,\n"
"\n"
"Undskyld hvis fejlen er vores, men det ser ud til, at følgende beløb endnu ikke er blevet betalt. Vær venlig at tage de nødvendige skridt for, at dette bliver betalt indenfor de næste 8 dage.\n"
"\n"
"Se venligst bort fra denne besked, skulle din betaling være gennemført efter afsendelsen af denne mail. Tøv ikke med at kontakte vores regnskabs afdeling.\n"
"\n"
"Venlig hilsen,"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line2
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"\n"
"Details of due payments is printed below.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"Kære %(partner_name)s,\n"
"\n"
"Vi er skuffede over at se, at trods påmindelser, er din konto nu langt over betalingsfristen.\n"
"\n"
"Det er yderst vigtigt at betaling sker øjeblikkeligt. Hvis dette ikke sker, bliver vi nødt til at overveje, at sætte din konto i bero, hvilket betyder, at vi ikke længere vil være i stand til at levere varer/tjenester til din virksomhed.\n"
"Vær venlig at tage de fornødne skridt, for at få dette betalt indenfor de næste 8 dage.\n"
"\n"
"Hvis der er et problem med at få fakturaen betalt som vi ikke er bekendte med, skal du endelig ikke holde dig tilbage fra at kontakte vores regnskabs afdeling - så vi kan få løst problemet hurtigst muligt.\n"
"\n"
"Detaljer om betalingen står skrevet nedenunder.\n"
"\n"
"Venlig Hilsen,"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line1
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line2
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line3
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line4
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line5
#, python-format
msgid "%(company_name)s Payment Reminder - %(partner_name)s"
msgstr ""

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopi)"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Followups'"
msgstr "'Opfølgninger'"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Amount Due by the partner"
msgstr ": At betale for partneren"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Current Date"
msgstr ": Nuværende dato"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ": Kundenavn"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User Name"
msgstr ": Brugernavn"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr ": Virksomhedsnavn"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Email Subject:</b><br/>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Next Reminder Date:</b>"
msgstr "<b>Næste opfølgningsdato:</b>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: green;\"/> God kunde"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal kunde"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: red;\"/> Dårlig kunde"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobil\" title=\"Mobil\"/> "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""
"<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Telefon\" "
"title=\"Telefon\"/> "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> Partnere:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr "<span class=\"o_stat_text\">Forfalden</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr ""
"<strong>Advarsel!</strong> Ingen opgaver skal udføres for denne partner."

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""
"En rykkerhandling skal være unik. Dette navn er allerede angivet på en anden"
" handling."

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontoplansskabelon"

#. module: account_followup
#: model:ir.model,name:account_followup.model_report_account_followup_report_followup_print_all
msgid "Account Follow-up Report"
msgstr "Konto Opfølgningsrapport"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr "Konto rapport opfølgning; eksekver opfølgning"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_note
msgid "Action To Do"
msgstr "Kræver handling"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "Handlinger"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "Tilføj et notat"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add an email subject"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "After"
msgstr "Efter"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"An error has occurred while formatting your followup letter/email. (Lang: %s, Followup Level: #%s) \n"
"\n"
"Full error description: %s"
msgstr ""
"Der opstod en fejl under formateringen af dit opfølgnings brev/email. (Sprog: %s, Opfølgnings niveau: #%s)\n"
"\n"
"Fuld fejl beskrivelse: %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr "Tildel en ansvarlig"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
msgid "Auto Execute"
msgstr "Auto eksekvér"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Bad debtor"
msgstr "Dårlig debitor"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "Change expected payment date"
msgstr "Ændre forventet betalingsdato"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Close"
msgstr "Luk"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr "Kommunikation"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Could not send mail to partner %s because it does not have any email address"
" defined"
msgstr ""
"Kunne ikke sende mail til partner %s, fordi den ikke har nogen email adresse"
" defineret"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "Kundens ref:"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "Kontoudtog på debitorer"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Date"
msgstr "Dato"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"Date at which Odoo will remind you to take care of that follow-up if you "
"choose \"remind me later\" button."
msgstr ""
"Dato hovrpå Odoo vil påminde dig om at tage hånd om opfølgningen, hvis du "
"klikker på \"påmind mig senere\" knappen."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "Dato:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up levels must be different per company"
msgstr "Dage til opfølgnings niveau skal være anderledes per virksomhed"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line1
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line2
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line5
#, python-format
msgid "Dear %(partner_name)s, it seems that some of your payments stay unpaid"
msgstr ""
"Kære %(partner_name)s, det ser ud til at dine faktura stadig er ubetalte"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "Definer rykkerniveauer og deres relaterede handlinger"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Description"
msgstr "Beskrivelse"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Done"
msgstr "Udført"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr "Forfaldsdato"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "Forfaldne dage"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Edit Summary"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__email_subject
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__email_subject
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Email Subject"
msgstr "Mailemne"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Excluded"
msgstr "Undtaget"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Expected Date"
msgstr "Forventet dato"

#. module: account_followup
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr "Første påmindelse"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "First reminder email"
msgstr "Første rykker e-mail"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Follow-Up Action"
msgstr "Rykkerproces"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Kriterier for opfølgning"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_level
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_level
msgid "Follow-up Level"
msgstr "Rykkerniveau"

#. module: account_followup
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "Rykkerniveauer"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Rykkerkontoudtog"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr "Rykkerkontoudtog"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "Rykkerkontoudtog Tree View"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_responsible_id
msgid "Follow-up Responsible"
msgstr "Ansvarlig for opfølgning"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "Opfølgnings status"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "Trin i rykkerproces"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter printed"
msgstr "Udskriv rykkerbrev"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr "Rykkerkontoudtog"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr "Rykkere sendt / I alt til opfølgning"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""
"For hvert trin, angiv den handling der skal udføres og forsinkelsen i dage. Det er\n"
"muligt at benytte print og e-mail skabeloner til at sende specifikke meddelser til\n"
"kunden."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Good debtor"
msgstr "God debitor"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr "Behov for handling"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr "Behov for handling"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "In order to build customized messages:"
msgstr "For at kunne lave tilpassede beskeder:"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
msgid "Join open Invoices"
msgstr "Sammenlæg åbne faktura"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr "Seneste rykker"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "Administrer opsummering og fodnotater i rapporter"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#, python-format
msgid "Manual Action"
msgstr "Manuel handling"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_type_id
msgid "Manual Action Type"
msgstr "Manuel handling"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Manual action done"
msgstr "Manuel handling udført"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Message"
msgstr "Besked"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr "Dato for næste handling"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr "Næste påmindelses dato sat til %s"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "Ingen handling påkrævet"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "Ingen rykker er sendt!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "No followup to send!"
msgstr "Ingen rykker er sendt!"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Normal debtor"
msgstr "Normal debitor"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid ""
"Odoo will remind you to take care of this follow-up on the next reminder "
"date."
msgstr ""
"Odoo minder dig om at tage dig af denne rykker på den næste påmindelsesdato."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr ""
"Eventuelt kan du tildele en bruger til dette område, som vil gøre ham "
"ansvarlig for handlingen."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "Valgmuligheder"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "Forfaldne fakturaer"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr "Forfaldne betalinger for %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__partner_id
msgid "Partner"
msgstr "Kontakt"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr "Kundeposteringer"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
msgid "Payment Follow-ups"
msgstr "Opfølgninger på betaling"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Payment Reminder"
msgstr "Betalingspåmindelse"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "Udskriv rykkerbrev"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__print_letter
msgid "Print a Letter"
msgstr "Print et brev"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Print letter"
msgstr "Udskriv brev"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__description
msgid "Printed Message"
msgstr "Udskrevet meddelelse"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process follow-ups"
msgstr "Afvikl opfølgninger"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Reconcile"
msgstr "Udlign"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr "Reference"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Remind me later"
msgstr "Påmind mig senere"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_description
msgid "SMS Text Message"
msgstr "SMS besked"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Save"
msgstr "Gem"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "Søg rykkere"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "Second reminder letter and email"
msgstr "Anden rykkerbrev og e-mail"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "Send SMS tekst besked"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send an Email"
msgstr "Send en e-mail"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send an SMS Message"
msgstr "Send en SMS besked"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Send an email"
msgstr "Send en e-mail"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by email"
msgstr "Send pr. e-mail"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by sms"
msgstr "Send via sms"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Source Document"
msgstr "Kildedokument"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__payment_next_action_date
msgid "The date before which no action should be taken."
msgstr "Datoen før der ikke skal foretages handlinger."

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up report was successfully emailed!"
msgstr "Rykkerkontoudtog er sendt via e-mail!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up was successfully sent!"
msgstr "Opfølgningen blev afsendt vellykket!"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""
"Antal dage efter forfaldsdato på en faktura, før rykker sendes. Kan være "
"negativ hvis en venlig påmindelse skal sendes før forfald. "

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "Third reminder: phone the customer"
msgstr "Tredje rykker: ring til kunden"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "I alt"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr "Tilgodehavende i alt"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr "Forfalden saldo"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total credit"
msgstr "Tilgodehavende i alt"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total debit"
msgstr "Udestående i alt"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices
msgid "Unpaid Invoices"
msgstr "Ubetalte fakturaer"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "Ikke-afstemt Aml"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "Urging reminder email"
msgstr "Opfordring til rykker e-mail"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "Urging reminder letter"
msgstr "Opfordring til rykkerbrev"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "View Invoice"
msgstr "Vis faktura"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__print_letter
msgid "When processing, it will print a PDF"
msgstr "Når den behandles, udskrives en PDF"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_email
msgid "When processing, it will send an email"
msgstr "Ved aktivering vil der sendes en e-mail"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_sms
msgid "When processing, it will send an sms text message"
msgstr "Vil sende en sms tekst besked under afvikling"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr "Ved aktivering vil kunden blive sat til manuel håndtering."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr "Med forfaldne fakturaer"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "Med forfaldne fakturaer"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter and mail or sms,\n"
"                                            according to the level of the follow-up. You can\n"
"                                            use the following keywords in the text. Don't\n"
"                                            forget to translate in all languages you installed\n"
"                                            using to top right icon."
msgstr ""

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/js/followup_form_controller.js:0
#, python-format
msgid "You are done with the follow-ups!<br/>You have skipped %s partner(s)."
msgstr ""
"Du er færdig med rykkerproceduren!<br/>Du har sprunget over %s kunde(r)."

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a followup report to a partner for which you didn't "
"print all the invoices ({})"
msgstr ""
"Du prøver at sende en opfølgnings rapport til en partner, som du ikke har "
"udprintet all faktura for ({})"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You need a least one follow-up level in order to process your follow-up"
msgstr ""
"Du skal bruge mindst én opfølgnings niveau for at kunne afvikle din "
"opfølgning"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""
"Din beskrivelse er forkert, brug den korrekte syntax eller %% hvis du ønsker"
" at bruge procent karakteren."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your email subject is invalid, use the right legend or %% if you want to use"
" the percent character."
msgstr ""

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your sms description is invalid, use the right legend or %% if you want to "
"use the percent character."
msgstr ""
"Din sms beskrivelse er ugyldig, brug tegnene til højre eller %% hvis du vil "
"bruger procent tegn."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "dage forfalden, udfør følgende handling:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr "fx: ring til kunden, check om der betalt,...."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "fx første påmindelsesmail"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr "betalings påmindelse"
