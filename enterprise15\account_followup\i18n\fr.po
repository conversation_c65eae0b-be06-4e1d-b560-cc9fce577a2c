# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-20 09:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,description:account_followup.demo_followup_line5
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Despite several reminders, your account is still not settled.\n"
"\n"
"Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"Cher %(partner_name)s,\n"
"\n"
"Malgré plusieurs rappels de notre part, vous n'avez pas régularisé vos paiements.\n"
"\n"
"Faute d'un règlement total sous 8 jours, nous engagerons les démarches juridiques nécessaires pour recouvrer les sommes dues.\n"
"\n"
"Nous espérons que ces démarches ne seront pas nécessaires. Vous trouverez ci-dessous les détails des sommes en attente de règlement.\n"
"\n"
"Notre service comptabilité demeure à votre disposition pour toute question relative à la présente relance.\n"
"\n"
"Bien cordialement,\n"
" "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,description:account_followup.demo_followup_line1
#, python-format
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"Cher(e) %(partner_name)s,\n"
"\n"
"Sauf en cas d'erreur de notre part, il semble que le montant suivant reste impayé. S'il vous plaît, prenez les mesures appropriées afin d'effectuer ce paiement dans les 8 prochains jours.\n"
"\n"
"Si votre paiement a été effectué après l'envoi de ce courrier, veuillez ignorer ce message. N'hésitez pas à contacter notre service comptabilité.\n"
"\n"
"Meilleures salutations,"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line2
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"\n"
"Details of due payments is printed below.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"Cher %(partner_name)s,\n"
"\n"
"Nous constatons avec regret que malgré notre précédent rappel, votre compte est toujours débiteur.\n"
"\n"
" Nous vous mettons donc en demeure de nous régler sous huitaine l’intégralité de la somme. Passé ce délai, nous bloquerons votre compte ce qui signifie que vous ne pourrez plus passer de commandes auprès de notre société (articles/services).\n"
"\n"
"Si pour une raison qui nous est inconnue vous ne pouvez régler ces factures, n'hésitez pas à prendre contact avec notre service comptable afin que nous trouvions une solution rapide à ce problème.\n"
"\n"
"Le détail des factures impayées est listé ci-dessous.\n"
"\n"
"Veuillez agréer nos salutations distinguées,\n"
" "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line1
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line2
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line3
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line4
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line5
#, python-format
msgid "%(company_name)s Payment Reminder - %(partner_name)s"
msgstr "%(company_name)sRappel de paiement%(partner_name)s"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Followups'"
msgstr "'Suivis'"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Amount Due by the partner"
msgstr ": Montant dû par le partenaire"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Current Date"
msgstr ": date actuelle"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ": Nom du partenaire"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User Name"
msgstr ": Nom d'utilisateur"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr ": Nom de la société de l'utilisateur"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Email Subject:</b><br/>"
msgstr "<b>Objet de l'email :</b><br/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Next Reminder Date:</b>"
msgstr "<b>Date de prochain rappel:</b>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: green;\"/> Bon débiteur"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Débiteur normal"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: red;\"/> Mauvais débiteur"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr ""
"<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Téléphone\" "
"title=\"Téléphone\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> Partenaires:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr "<span class=\"o_stat_text\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr ""
"<strong>Attention !</strong> Aucune action n'est nécessaire à prendre pour "
"ce partenaire."

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""
"Un nom d'action de suivi doit être unique. Ce nom est déjà attribué à une "
"autre action."

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de plan comptable"

#. module: account_followup
#: model:ir.model,name:account_followup.model_report_account_followup_report_followup_print_all
msgid "Account Follow-up Report"
msgstr "Rapport de suivi de compte"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr "Relance du rapport de compte ; Exécution de la relance"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_note
msgid "Action To Do"
msgstr "Action à effectuer"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "Actions"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "Ajouter une note"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add an email subject"
msgstr "Ajouter un sujet au courriel"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "After"
msgstr "Après"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"An error has occurred while formatting your followup letter/email. (Lang: %s, Followup Level: #%s) \n"
"\n"
"Full error description: %s"
msgstr ""
"Une erreur s'est produite lors du formatage de votre lettre/email de relance. (Lang:%s, Niveau de relance: #%s)\n"
"\n"
"Description complète de l'erreur: %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr "Attribuer un responsable"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
msgid "Auto Execute"
msgstr "Exécution automatique"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Bad debtor"
msgstr "Mauvais Débiteur"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "Change expected payment date"
msgstr "Changer la date de paiement prévue"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Close"
msgstr "Fermer"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr "Communication"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "Société"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Could not send mail to partner %s because it does not have any email address"
" defined"
msgstr ""
"Impossible d'envoyer le courrier au partenaire %s car aucune adresse "
"électronique n'a été définie."

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
msgid "Created on"
msgstr "Créé le"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "Réf. du client :"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "Relevé clients"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Date"
msgstr "Date"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"Date at which Odoo will remind you to take care of that follow-up if you "
"choose \"remind me later\" button."
msgstr ""
"La date à laquelle Odoo vous rappellera de vous occuper du suivi si vous "
"choisissez le bouton \"rappelez-le moi plus tard\"."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "Date :"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up levels must be different per company"
msgstr "Les jours des niveaux de relance doivent être différents par société"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line1
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line2
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line5
#, python-format
msgid "Dear %(partner_name)s, it seems that some of your payments stay unpaid"
msgstr ""
"Chèr(e) %(partner_name)s, il semblerait que certains de vos paiements "
"restent impayés"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "Définissez les niveaux de suivi et leurs actions"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Description"
msgstr "Description"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Done"
msgstr "Fait"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr "Date d'échéance"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "Retard d'échéance"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Edit Summary"
msgstr "Modifier le résumé"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__email_subject
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__email_subject
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Email Subject"
msgstr "Sujet du courriel"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Excluded"
msgstr "Exclus"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Expected Date"
msgstr "Date prévue"

#. module: account_followup
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr "Premier rappel"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "First reminder email"
msgstr "Premier email de rappel"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Follow-Up Action"
msgstr "Action de relance"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Critère de relance"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_level
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_level
msgid "Follow-up Level"
msgstr "Derniere relance"

#. module: account_followup
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "Niveaux de relance"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Rapport de suivi"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr "Rapports de suivi"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "Organigramme des Rapports de Relance"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_responsible_id
msgid "Follow-up Responsible"
msgstr "Responsable des relances"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "État de la relance"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "Étapes de relance"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter printed"
msgstr "Lettre de relance imprimée"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr "Rapports de suivi"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr "Relances faites / Total des relances"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""
"Pour chaque étape, indiquez les actions à prendre et les délais en jour. Il "
"est possible d'utiliser des modèles d'impression et d'email pour envoyer des"
" messages spécifiques au client."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Good debtor"
msgstr "Bon Débiteur"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr "Nécessite une action"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr "Nécessite une action"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "In order to build customized messages:"
msgstr "Pour créer des messages personnalisés:"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
msgid "Join open Invoices"
msgstr "Joindre les factures ouvertes"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr "Date de relance"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "Gérer le résumé et les notes de bas de page des rapports"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#, python-format
msgid "Manual Action"
msgstr "Action manuelle"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_type_id
msgid "Manual Action Type"
msgstr "Type d'action manuelle"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Manual action done"
msgstr "Action manuelle effectuée"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Message"
msgstr "Message"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr "Date de la prochaine action"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr "Date de prochain rappel fixée au %s"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "Pas d'action requise"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "Pas de suivi à envoyer !"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "No followup to send!"
msgstr "Pas de suivi à envoyer !"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Normal debtor"
msgstr "Débiteur normal"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid ""
"Odoo will remind you to take care of this follow-up on the next reminder "
"date."
msgstr ""
"Odoo vous rappellera de vous occuper de ce suivi à la date du prochain "
"rappel."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr ""
"Si besoin, vous pouvez assigner un utilisateur à ce champ, ce qui en fera le"
" responsable de cette action."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "Options"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "Factures en retard"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr "Paiements en retard pour %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__partner_id
msgid "Partner"
msgstr "Partenaire"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr "Entrées partenaires"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
msgid "Payment Follow-ups"
msgstr "Relances de paiement"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Payment Reminder"
msgstr "Rappel de paiement"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "Imprimer la Lettre de Suivi"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__print_letter
msgid "Print a Letter"
msgstr "Imprimer une Lettre"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Print letter"
msgstr "Imprimer la lettre"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__description
msgid "Printed Message"
msgstr "Imprimer le Message"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process follow-ups"
msgstr "Procéder aux relances"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Reconcile"
msgstr "Lettrer"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr "Référence"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Remind me later"
msgstr "Rappelez-le moi plus tard"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_description
msgid "SMS Text Message"
msgstr "SMS"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Save"
msgstr "Sauvegarder"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "Chercher une relance"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "Second reminder letter and email"
msgstr "Deuxième lettre de relance et email"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "Envoyer un SMS"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send an Email"
msgstr "Envoyer un email"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send an SMS Message"
msgstr "Envoyer un message SMS"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Send an email"
msgstr "Envoyer un email"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by email"
msgstr "Envoyer par email"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by sms"
msgstr "Envoyer par SMS"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Source Document"
msgstr "Document d'origine"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__payment_next_action_date
msgid "The date before which no action should be taken."
msgstr "La date avant laquelle aucune action ne devrait être lancée."

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up report was successfully emailed!"
msgstr "Le rapport de suivi a été envoyé par email avec succès !"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up was successfully sent!"
msgstr "La relance a été envoyée avec succès !"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""
"Nombre de jours à attendre après la date d'échéance de la facture avant "
"d'envoyer la relance. Il peut être négatif si vous souhaitez envoyer une "
"simple invitation avant l'échéance."

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "Third reminder: phone the customer"
msgstr "Troisième relance: appel du client"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "Total"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr "Montant dû"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr "Total en retard"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total credit"
msgstr "Total crédit"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total debit"
msgstr "Total débit"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices
msgid "Unpaid Invoices"
msgstr "Factures impayées"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "Aml non rapproché"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "Urging reminder email"
msgstr "Email de rappel"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "Urging reminder letter"
msgstr "Lettre de rappel"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "View Invoice"
msgstr "Afficher la facture"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__print_letter
msgid "When processing, it will print a PDF"
msgstr "Lors du traitement, imprimera un PDF"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_email
msgid "When processing, it will send an email"
msgstr "Lors du traitement, un email sera envoyé "

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_sms
msgid "When processing, it will send an sms text message"
msgstr "Lors du traitement, il enverra un SMS"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr ""
"Lors du traitement, Odoo indiquera l'action manuelle à faire pour ce client."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr "Avec factures en retard"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "Avec factures en retard"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter and mail or sms,\n"
"                                            according to the level of the follow-up. You can\n"
"                                            use the following keywords in the text. Don't\n"
"                                            forget to translate in all languages you installed\n"
"                                            using to top right icon."
msgstr ""
"Écrivez ici l'introduction dans la lettre et le courrier ou le sms,\n"
"selon le niveau du suivi. Vous pouvez\n"
"utiliser les mots clés suivants dans le texte. N'oubliez\n"
"pas de traduire dans toutes les langues que vous avez installées\n"
"en utilisant l'icône en haut à droite."

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/js/followup_form_controller.js:0
#, python-format
msgid "You are done with the follow-ups!<br/>You have skipped %s partner(s)."
msgstr "Vous avez terminé le suivi ! <br/>Vous avez sauté %s partenaire (s)."

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a followup report to a partner for which you didn't "
"print all the invoices ({})"
msgstr ""
"Vous essayez d'envoyer un rapport de relance à un partenaire pour lequel "
"vous n'avez pas imprimé toutes les factures ({})"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You need a least one follow-up level in order to process your follow-up"
msgstr ""
"Vous avez besoin d'au moins un niveau de relance afin de procéder à votre "
"relance"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""
"Votre description n'est pas correcte, utilisez une légende exacte  ou %% si "
"vous souhaitez utiliser le caractère pour cent."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your email subject is invalid, use the right legend or %% if you want to use"
" the percent character."
msgstr ""
"L'objet de votre email n'est pas valide, utilisez la bonne légende ou %% si "
"vous souhaitez utiliser le caractère de pourcentage."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your sms description is invalid, use the right legend or %% if you want to "
"use the percent character."
msgstr ""
"Votre description SMS n'est pas correcte, utilisez une légende exacte  ou %%"
" si vous souhaitez utiliser le caractère pour cent."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "jours de retard, faire les actions suivantes :"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr "Ex. appeler le client, vérifier si la facture est payée, ..."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "par exemple : Premier email de rappel"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr "Rappel de paiement"
