# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# da<PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# NoaFarkash, 2022
# <PERSON>tem <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-20 09:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Z<PERSON> BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,description:account_followup.demo_followup_line5
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Despite several reminders, your account is still not settled.\n"
"\n"
"Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,description:account_followup.demo_followup_line1
#, python-format
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"שלום %(partner_name)s,\n"
"\n"
"להלן רשימת החשבוניות אשר טרם הוסדר התשלום בגינן.\n"
"נודה מאוד על הסדרת התשלום לפי התנאים בחשבונית.\n"
"במידה והתשלום כבר התבצע לאחר שליחת הודעה זאת, התעלם מההודעה. אל תהססו לפנות אלינו לשאלות נוספות\n"
"\n"
"בברכה,\n"
"            "

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line2
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"\n"
"Details of due payments is printed below.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line1
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line2
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line3
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line4
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line5
#, python-format
msgid "%(company_name)s Payment Reminder - %(partner_name)s"
msgstr "%(company_name)s תזכורת לתשלום - %(partner_name)s"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (העתק)"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Followups'"
msgstr "'מעקבים'"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Amount Due by the partner"
msgstr "סכום לתשלום על-ידי השותף:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Current Date"
msgstr ": תאריך נוכחי"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ": שם לקוח/ספק"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User Name"
msgstr ": שם משתמש"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr ": שם חברת המשתמש"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Email Subject:</b><br/>"
msgstr "<b>נושא המייל:</b><br/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Next Reminder Date:</b>"
msgstr "<b>תאריך התזכורת הבאה:</b>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: green;\"/> חייב טוב"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: grey;\"/> חייב רגיל"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: red;\"/> חייב רע"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> לקוחות/ספקים:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr "<span class=\"o_stat_text\">יתרה לפרעון</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr "<strong>אזהרה!</strong> אין צורך לבצע שום פעולה עבור לקוח/ספק זה."

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr "שם לפעולת-המשך חייב להיות ייחודי. השם הזה כבר מוגדר לפעולה אחרת."

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr "תבנית לוח חשבונות"

#. module: account_followup
#: model:ir.model,name:account_followup.model_report_account_followup_report_followup_print_all
msgid "Account Follow-up Report"
msgstr "דוח מעקב חשבון"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr "מעקב אחר דוחות חשבון; בצע מעקב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_note
msgid "Action To Do"
msgstr "פעולה לביצוע"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "פעולות"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "הוסף הערה"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add an email subject"
msgstr "הוסף נושא לאימייל"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "After"
msgstr "אחרי"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"An error has occurred while formatting your followup letter/email. (Lang: %s, Followup Level: #%s) \n"
"\n"
"Full error description: %s"
msgstr ""
"אירעה שגיאה בעת עיצוב מכתב המעקב / דוא\"ל שלך. (שפה: %s, רמת מעקב: מס'%s) \n"
"\n"
"תיאור שגיאה מלא: %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr "שייך אחראי"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
msgid "Auto Execute"
msgstr "ביצוע אוטומטי"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Bad debtor"
msgstr "חייב רע"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "Change expected payment date"
msgstr "שנה את מועד התשלום הצפוי"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Close"
msgstr "סגור"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr "אסמכתה"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "חברה"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "צור קשר"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Could not send mail to partner %s because it does not have any email address"
" defined"
msgstr "לא ניתן היה לשלוח דואר ללקוח/ספק %s מכיוון שלא מוגדרת כתובת דוא\"ל"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "מזהה לקוח:"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "הצהרת לקוח"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"Date at which Odoo will remind you to take care of that follow-up if you "
"choose \"remind me later\" button."
msgstr ""
"תאריך בו Odoo יזכיר לך לדאוג למעקב זה אם תבחר בכפתור \"הזכר לי מאוחר יותר\"."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "תאריך:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up levels must be different per company"
msgstr "ימי רמות המעקב חייבים להיות שונים לכל חברה"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line1
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line2
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line5
#, python-format
msgid "Dear %(partner_name)s, it seems that some of your payments stay unpaid"
msgstr " %(partner_name)s יקר, נראה שחלק מהתשלומים שלך לא שולמו"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "הגדירו את רמות המעקב ואת פעולותיהן הקשורות"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Description"
msgstr "תיאור"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Done"
msgstr "בוצע"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr "תאריך יעד"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "ימי איחור"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Edit Summary"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__email_subject
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__email_subject
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Email Subject"
msgstr "נושא האימייל"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Excluded"
msgstr "לא נכלל"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Expected Date"
msgstr "תאריך צפוי"

#. module: account_followup
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr "תזכורת ראשונה"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "First reminder email"
msgstr "תזכורת ראשונה בדוא\"ל"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Follow-Up Action"
msgstr "פעולת מעקב"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "קריטריוני מעקב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_level
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_level
msgid "Follow-up Level"
msgstr "רמת מעקב"

#. module: account_followup
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "רמות מעקב"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "דוח מעקב"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr "דוחות מעקב"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "תצוגת ענפים לדוחות מעקב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_responsible_id
msgid "Follow-up Responsible"
msgstr "אחראי מעקב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "סטטוס מעקב"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "שלבי מעקב"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter printed"
msgstr "מכתב מעקב הודפס"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr "דוחות מעקב"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr "מעקבים שבוצעו / סה\"כ מעקבים"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""
"עבור כל שלב, ציין את הפעולות שצריך לבצע ועיכוב בימים. \n"
"                אפשר להשתמש בתבניות הדפסה ודוא\"ל כדי לשלוח הודעות מסוימות\n"
"                ללקוח."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Good debtor"
msgstr "חייב טוב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr "יש צורך בפעולה"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr "יש צורך בפעולה"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "In order to build customized messages:"
msgstr "על מנת לבנות הודעות בהתאמה אישית:"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
msgid "Join open Invoices"
msgstr "צרף חשבוניות פתוחות"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "פקודת יומן"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr "מעקב אחרון"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "נהל סיכום והערות שוליים של דוחות"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#, python-format
msgid "Manual Action"
msgstr "פעולה ידנית"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_type_id
msgid "Manual Action Type"
msgstr "סוג פעולה ידנית"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Manual action done"
msgstr "פעולה ידנית בוצעה"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Message"
msgstr "הודעה"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr "תאריך הפעולה הבא"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr "תאריך התזכורת הבא מוגדר ל %s"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "לא נדרשת פעולה"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "אין דוחות מעקב לשליחה!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "No followup to send!"
msgstr "אין מעקב לשליחה!"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Normal debtor"
msgstr "חייב רגיל"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid ""
"Odoo will remind you to take care of this follow-up on the next reminder "
"date."
msgstr "Odoo יזכיר לכם לדאוג למעקב זה בתאריך התזכורת הבא."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr ""
"באופן אופציונלי תוכלו לשייך משתמש לשדה זה, מה שיגרום לו להיות אחראי לפעולה."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "אפשרויות"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "חשבוניות שלא שולמו"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr "תשלומים באיחור עבור %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__partner_id
msgid "Partner"
msgstr "לקוח/ספק"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr "רשומות שותפים"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
msgid "Payment Follow-ups"
msgstr "מעקב תשלום"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Payment Reminder"
msgstr "תזכורת תשלום"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "הדפס מכתב מעקב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__print_letter
msgid "Print a Letter"
msgstr "הדפס מכתב"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Print letter"
msgstr "הדפס מכתב"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__description
msgid "Printed Message"
msgstr "הודעת מודפסת"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process follow-ups"
msgstr "תהליכי מעקב"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Reconcile"
msgstr "התאם"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr "מזהה"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Remind me later"
msgstr "הזכר לי מאוחר יותר"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_description
msgid "SMS Text Message"
msgstr "הודעת SMS"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Save"
msgstr "שמור"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "חפש מעקב"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "Second reminder letter and email"
msgstr "מכתב תזכורת שני ודוא\"ל"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "שלח הודעת SMS"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send an Email"
msgstr "שלח דוא\"ל"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send an SMS Message"
msgstr "שלח הודעת SMS"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Send an email"
msgstr "שלח דוא\"ל"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by email"
msgstr "שלח בדוא\"ל"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by sms"
msgstr "שלח ב sms"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Source Document"
msgstr "מסמך מקור"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__payment_next_action_date
msgid "The date before which no action should be taken."
msgstr "התאריך שעד אליו אין לבצע שום פעולה."

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up report was successfully emailed!"
msgstr "דוח המעקב נשלח בהצלחה בדוא\"ל!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up was successfully sent!"
msgstr "המעקב נשלח בהצלחה!"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""
"מספר הימים שלאחר תאריך החיוב של החשבונית להמתנה לפני שליחת התזכורת. יכול "
"להיות שלילי אם ברצונך לשלוח התראה מנומסת לפני כן."

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "Third reminder: phone the customer"
msgstr "תזכורת שלישית: התקשר אל הלקוח"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "סה\"כ"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr "יתרה לתשלום"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr "יתרה בפיגור"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total credit"
msgstr "סה\"כ זכות"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total debit"
msgstr "סה\"כ חובה"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices
msgid "Unpaid Invoices"
msgstr "חשבוניות מכירה לגבייה"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr ""

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "Urging reminder email"
msgstr "מייל תזכורת דוחק"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "Urging reminder letter"
msgstr "מכתב תזכורת דוחקת"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "View Invoice"
msgstr "הצג חשבונית"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__print_letter
msgid "When processing, it will print a PDF"
msgstr "בעת העיבוד זה ידפיס PDF"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_email
msgid "When processing, it will send an email"
msgstr "בעת העיבוד זה ישלח דוא\"ל"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_sms
msgid "When processing, it will send an sms text message"
msgstr "בעת העיבוד זה ישלח הודעת SMS"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr "בעת העיבוד, זה יגדיר את הפעולה הידנית שיש לנקוט עבור אותו לקוח."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr "עם חשבוניות באיחור"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "עם חשבוניות באיחור"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter and mail or sms,\n"
"                                            according to the level of the follow-up. You can\n"
"                                            use the following keywords in the text. Don't\n"
"                                            forget to translate in all languages you installed\n"
"                                            using to top right icon."
msgstr ""

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/js/followup_form_controller.js:0
#, python-format
msgid "You are done with the follow-ups!<br/>You have skipped %s partner(s)."
msgstr "סיימת את המעקבים!<br/>דילגת על  %s לקוחות/ספקים."

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a followup report to a partner for which you didn't "
"print all the invoices ({})"
msgstr ""
"אתה מנסה לשלוח דוח מעקב ללקוח/ספק שעבורו לא הדפסת את כל החשבוניות ({})"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You need a least one follow-up level in order to process your follow-up"
msgstr "אתה זקוק לרמת מעקב אחת לפחות כדי לעבד את המעקב שלך"

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""
"התיאור שלך אינו חוקי, השתמש במקרא הנכון או %% אם אתה רוצה להשתמש בתו האחוז."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your email subject is invalid, use the right legend or %% if you want to use"
" the percent character."
msgstr ""
"נושא האימייל שלך לא חוקי, השתמש במקרא הנכון או ב-%% אם אתה רוצה להשתמש בתו "
"האחוז."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your sms description is invalid, use the right legend or %% if you want to "
"use the percent character."
msgstr ""
"תיאור ה- sms שלך אינו חוקי, השתמש במקרא הנכון או %% אם אתה רוצה להשתמש בתו "
"האחוז."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "ימי איחור, בצע את הפעולות הבאות:"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr "למשל התקשר ללקוח, בדוק אם זה שולם, ..."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "למשל: תזכורת ראשונה בדוא\"ל"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr "תזכורת תשלום"
