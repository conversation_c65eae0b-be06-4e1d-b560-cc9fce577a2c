# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_reports_followup
# 
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-19 12:58+0000\n"
"PO-Revision-Date: 2016-09-19 12:58+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2016\n"
"Language-Team: Armenian (https://www.transifex.com/odoo/teams/41243/hy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_reports_followup
#: model:account_followup.followup.line,description:account_reports_followup.demo_followup_line3
#: model:account_followup.followup.line,description:account_reports_followup.demo_followup_line4
#: model:account_followup.followup.line,description:account_reports_followup.demo_followup_line5
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Despite several reminders, your account is still not settled.\n"
"\n"
"Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,description:account_reports_followup.demo_followup_line1
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.  \n"
"\n"
"Best Regards,\n"
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,description:account_reports_followup.demo_followup_line2
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"\n"
"Details of due payments is printed below.\n"
"\n"
"Best Regards,\n"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid ""
"<b>%(partner_name)s</b>: Partner Name\n"
"                                <b>%(date)s</b>: Current Date\n"
"                                <b>%(user_signature)s</b>: User Name\n"
"                                <b>%(company_name)s</b>: User's Company Name"
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_account_report_context_followup
msgid "A particular context for the followup report"
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_account_report_context_followup_all
msgid "A progress bar for followup reports"
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_account_followup_followup
msgid "Account Follow-up"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_manual_action_note
msgid "Action To Do"
msgstr "Անելիք"

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid "After"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_manual_action_responsible_id
msgid "Assign a Responsible"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_move_line_result
msgid "Balance"
msgstr ""

#. module: account_reports_followup
#: model:ir.actions.act_window,help:account_reports_followup.action_account_followup_definition_form
msgid "Click to define follow-up levels and their related actions."
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_res_company
msgid "Companies"
msgstr "Ընկերություններ"

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_company_id
msgid "Company"
msgstr "Ընկերությունը"

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_name
msgid "Company Name"
msgstr "Ընկերության անվանումը"

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_config_settings_inherit
msgid "Configure your follow-up levels"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_create_uid
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_create_uid
msgid "Created by"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_create_date
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_create_date
msgid "Created on"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_res_partner_payment_note
msgid "Customer Payment Promise"
msgstr ""

#. module: account_reports_followup
#: sql_constraint:account_followup.followup.line:0
msgid "Days of the follow-up levels must be different"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_display_name
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_display_name
msgid "Display Name"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.report_followup_body
msgid "Do it Later"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.report_followup_body
msgid "Done"
msgstr "Կատարված է"

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_delay
msgid "Due Days"
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,name:account_reports_followup.demo_followup_line1
msgid "First reminder email"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_followup_id
msgid "Follow Ups"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_name
msgid "Follow-Up Action"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_followup_line_ids
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_form
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_tree
msgid "Follow-up"
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_move_line_followup_line_id
msgid "Follow-up Level"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.menu,name:account_reports_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_res_partner_payment_responsible_id
msgid "Follow-up Responsible"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr ""

#. module: account_reports_followup
#: model:ir.actions.act_window,help:account_reports_followup.action_account_followup_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_account_followup_followup_line_sequence
msgid "Gives the sequence order when displaying a list of follow-up lines."
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_id
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_id
msgid "ID"
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup___last_update
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line___last_update
msgid "Last Modified on"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_write_uid
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_write_date
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_write_date
msgid "Last Updated on"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_move_line_followup_date
msgid "Latest Follow-up"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_report_context_followup_level
msgid "Level"
msgstr ""

#. module: account_reports_followup
#: code:addons/account_reports_followup/account_followup_report.py:62
#, python-format
msgid "Level: "
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.report_followup_body
msgid "Log a Note"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_manual_action
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid "Manual Action"
msgstr ""

#. module: account_reports_followup
#: code:addons/account_reports_followup/account_followup_report.py:57
#, python-format
msgid "Manual action done\n"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_res_company_min_days_between_followup
msgid "Minimum days between two follow-ups"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_config_settings_min_days_between_followup
msgid "Minimum days between two follow-ups *"
msgstr ""

#. module: account_reports_followup
#: code:addons/account_reports_followup/account_followup.py:113
#, python-format
msgid ""
"No follow-up is defined for the company \"%s\".\n"
" Please define one."
msgstr ""

#. module: account_reports_followup
#: sql_constraint:account_followup.followup:0
msgid "Only one follow-up per company is allowed"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_res_partner_payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_res_partner
msgid "Partner"
msgstr "Գործընկեր"

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr ""

#. module: account_reports_followup
#: model:ir.actions.act_window,name:account_reports_followup.action_account_followup_definition_form
msgid "Payment Follow-ups"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_res_partner_payment_note
msgid "Payment Note"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.report_followup_body
msgid "Print Letter"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_description
msgid "Printed Message"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_filter
msgid "Search Follow-up"
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,name:account_reports_followup.demo_followup_line2
msgid "Second reminder letter and email"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_send_letter
msgid "Send a Letter"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid "Send a Letter or Email"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_send_email
msgid "Send an Email"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.report_followup_body
msgid "Send by email"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,field_description:account_reports_followup.field_account_followup_followup_line_sequence
msgid "Sequence"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_account_followup_followup_line_delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,name:account_reports_followup.demo_followup_line3
msgid "Third reminder: phone the customer"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_form
msgid ""
"To remind customers of paying their invoices, you can\n"
"                        define different actions depending on how severely\n"
"                        overdue the customer is. These actions are bundled\n"
"                        into follow-up levels that are triggered when the due\n"
"                        date of an invoice has passed a certain\n"
"                        number of days. If there are other overdue invoices for the \n"
"                        same customer, the actions of the most \n"
"                        overdue invoice will be executed."
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.account_move_line_partner_tree
msgid "Total credit"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.account_move_line_partner_tree
msgid "Total debit"
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,name:account_reports_followup.demo_followup_line4
msgid "Urging reminder email"
msgstr ""

#. module: account_reports_followup
#: model:account_followup.followup.line,name:account_reports_followup.demo_followup_line5
msgid "Urging reminder letter"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_account_config_settings_min_days_between_followup
#: model:ir.model.fields,help:account_reports_followup.field_res_company_min_days_between_followup
msgid ""
"Use this if you want to be sure than a minimum number of days occurs between"
" two follow-ups."
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_account_followup_followup_line_send_letter
msgid "When processing, it will print a letter"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_account_followup_followup_line_send_email
msgid "When processing, it will send an email"
msgstr ""

#. module: account_reports_followup
#: model:ir.model.fields,help:account_reports_followup.field_account_followup_followup_line_manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter,\n"
"                            according to the level of the follow-up. You can\n"
"                            use the following keywords in the text. Don't\n"
"                            forget to translate in all languages you installed\n"
"                            using to top right icon."
msgstr ""

#. module: account_reports_followup
#: code:addons/account_reports_followup/account_followup.py:64
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr ""

#. module: account_reports_followup
#: model:ir.model,name:account_reports_followup.model_account_config_settings
msgid "account.config.settings"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr ""

#. module: account_reports_followup
#: model:ir.ui.view,arch_db:account_reports_followup.view_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr ""
