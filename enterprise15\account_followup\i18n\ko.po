# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-20 09:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,description:account_followup.demo_followup_line5
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Despite several reminders, your account is still not settled.\n"
"\n"
"Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"\n"
"I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"\n"
"In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"%(partner_name)s님께.\n"
"\n"
"여러 번 알림을 보냈음에도 불구하고 귀하의 계정이 결산되지 않았습니다.\n"
"\n"
"8일 이내에 전액을 결제하지 않을 경우, 추가 고지 없이 채권 회수를 위한 법적 조치를 취할 것입니다.\n"
"\n"
"이러한 상황이 발생하지 않기를 바라며, 지급 금액 상세내역은 아래와 같습니다\n"
"\n"
"이 문제와 관련하여 문의 사항이 있을 경우, 당사 회계부로 언제든지 연락해 주십시오.\n"
"\n"
"감사합니다.\n"
"            "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,description:account_followup.demo_followup_line1
#, python-format
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"%(partner_name)s님께.\n"
"\n"
"당사의 확인에 따르면 다음 금액이 지급되지 않았습니다. 적절한 조치를 취해 8일 이내에 대금을 지급해 주십시오.\n"
"\n"
"이 메일이 발송된 후 대금을 지급했을 경우, 이 메시지는 무시하시기 바랍니다. 문의 사항이 있으시면 당사 회계부에 언제든지 연락해 주십시오.\n"
"\n"
"감사합니다.\n"
"            "

#. module: account_followup
#: model:account_followup.followup.line,description:account_followup.demo_followup_line2
msgid ""
"\n"
"Dear %(partner_name)s,\n"
"\n"
"We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"\n"
"It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services).\n"
"Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"\n"
"Details of due payments is printed below.\n"
"\n"
"Best Regards,\n"
"            "
msgstr ""
"\n"
"%(partner_name)s님께.\n"
"\n"
"알림을 보냈음에도 불구하고 귀하의 계정에서 지급이 장기간 연체되어 매우 유감입니다.\n"
"\n"
"즉시 대금을 지급하지 않으면 귀하의 계정 정지를 고려할 수밖에 없습니다. 즉, 귀사에 (상품/서비스)를 제공할 수 없게 됩니다.\n"
"적절한 조치를 취해 8일 이내에 대금을 지급해 주십시오.\n"
"\n"
"청구 대금 지급에 당사에서 파악하지 못한 문제가 있을 경우, 신속히 문제를 해결할 수 있도록 당사 회계부에 언제든지 연락해 주십시오.\n"
"\n"
"자세한 지급금은 아래에서 확인하실 수 있습니다.\n"
"\n"
"감사합니다.\n"
"            "

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line1
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line2
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line3
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line4
#: model:account_followup.followup.line,email_subject:account_followup.demo_followup_line5
#, python-format
msgid "%(company_name)s Payment Reminder - %(partner_name)s"
msgstr ""

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Followups'"
msgstr "'후속 조치'"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Amount Due by the partner"
msgstr ": 협력사가 지불해야 할 금액"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Current Date"
msgstr ": 현재 날짜"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": Partner Name"
msgstr ": 협력사 이름"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User Name"
msgstr ": 사용자 이름"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ": User's Company Name"
msgstr ": 사용자의 회사명"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Email Subject:</b><br/>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<b>Next Reminder Date:</b>"
msgstr "<b>다음 알림 날짜 :</b>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: green;\"/> 우량 채무자"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: grey;\"/> 일반 채무자"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr "<i class=\"fa fa-circle\" style=\"color: red;\"/> 불량 채무자"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<i class=\"fa fa-fw fa-pencil o_account_reports_edit_email_subject_pencil\" "
"role=\"img\" aria-label=\"Edit Email Subject\" title=\"Edit Email "
"Subject\"/>"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<i class=\"fa fa-mobile fa-fw\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-phone fa-fw\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> 협력사 :"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr "<span class=\"o_stat_text\">지급 금액</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr "<strong>경고!</strong> 이 협력사는 조치를 취할 필요가 없습니다."

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr "후속 작업 이름은 고유하게 지정해야 합니다. 이 이름은 이미 다른 작업으로 설정되어 있습니다."

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_chart_template
msgid "Account Chart Template"
msgstr "계정과목 일람표 서식"

#. module: account_followup
#: model:ir.model,name:account_followup.model_report_account_followup_report_followup_print_all
msgid "Account Follow-up Report"
msgstr ""

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
#: model:ir.cron,cron_name:account_followup.ir_cron_auto_post_draft_entry
#: model:ir.cron,name:account_followup.ir_cron_auto_post_draft_entry
msgid "Account Report Followup; Execute followup"
msgstr "계정 보고서 후속 조치; 후속 조치 실행"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_note
msgid "Action To Do"
msgstr "수행할 작업"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "추가 작업"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "노트 추가하기"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add an email subject"
msgstr ""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "After"
msgstr "이후"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"An error has occurred while formatting your followup letter/email. (Lang: %s, Followup Level: #%s) \n"
"\n"
"Full error description: %s"
msgstr ""
"후속 조치 서신/이메일을 포맷하는 동안 오류가 발생했습니다. (언어 : %s, 후속 조치 수준 : #%s) \n"
"\n"
"전체 오류 설명 : %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_responsible_id
msgid "Assign a Responsible"
msgstr "담당자 배정"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
msgid "Auto Execute"
msgstr "자동 실행"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Bad debtor"
msgstr "악성 채무자"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "Change expected payment date"
msgstr "예상 결제 날짜 변경"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Close"
msgstr "닫기"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Communication"
msgstr "의사소통"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "회사"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"Could not send mail to partner %s because it does not have any email address"
" defined"
msgstr "지정된 이메일 주소가 없어 %s 협력사에게 메일을 발송할 수 없습니다."

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
msgid "Created by"
msgstr "작성자"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
msgid "Created on"
msgstr "작성일자"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "고객 참조 :"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "고객 명세서"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Date"
msgstr "일자"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"Date at which Odoo will remind you to take care of that follow-up if you "
"choose \"remind me later\" button."
msgstr "\"나중에 알림\" 버튼을 선택한 경우 Odoo가 후속 조치를 처리하도록 상기시켜 주는 날짜입니다."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "일자:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up levels must be different per company"
msgstr "후속 조치 수준의 날짜는 회사마다 달라야 합니다."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line1
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line2
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line3
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line4
#: model:account_followup.followup.line,sms_description:account_followup.demo_followup_line5
#, python-format
msgid "Dear %(partner_name)s, it seems that some of your payments stay unpaid"
msgstr "%(partner_name)s님. 일부 결제가 미지급 상태인 것으로 보입니다."

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "후속 조치 수준 및 관련 조치 정의"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Description"
msgstr "설명"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
msgid "Display Name"
msgstr "표시명"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Done"
msgstr "완료"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Due Date"
msgstr "만기일"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "만기일"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Edit Summary"
msgstr ""

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__email_subject
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__email_subject
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Email Subject"
msgstr "이메일 제목"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Excluded"
msgstr "제외됨"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Expected Date"
msgstr "예정일"

#. module: account_followup
#: code:addons/account_followup/models/chart_template.py:0
#, python-format
msgid "First Reminder"
msgstr "첫 번째 미리 알림"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "First reminder email"
msgstr "첫 번째 미리 알림 이메일"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Follow-Up Action"
msgstr "후속 조치"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "후속 조치 기준"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_level
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_level
msgid "Follow-up Level"
msgstr "후속 조치 수준"

#. module: account_followup
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "후속 조치 수준"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "후속 조치 보고서"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_followup.customer_statements_menu
msgid "Follow-up Reports"
msgstr "후속 조치 보고서"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr "후속 조치 보고서 트리 보기"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_responsible_id
msgid "Follow-up Responsible"
msgstr "후속 조치 담당자"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "후속 조치 상태"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "후속 조치 단계"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Follow-up letter printed"
msgstr "후속 조치 서신 인쇄됨"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_followup_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr "후속 조치 보고서"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr "후속 조치 완료 / 총 후속 조치"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"                possible to use print and e-mail templates to send specific messages to\n"
"                the customer."
msgstr ""
"각 단계에서 취할 조치와 연체일을 지정합니다.\n"
"                인쇄물과 이메일 서식을 사용하여 고객에게 특정 메시지를\n"
"                보낼 수 있습니다."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Good debtor"
msgstr "우량 채무자"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "In Need of Action"
msgstr "작업 필요"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "In need of action"
msgstr "작업 필요"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "In order to build customized messages:"
msgstr "맞춤 설정된 메시지를 작성하려면 :"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
msgid "Join open Invoices"
msgstr "미결 청구서 가입"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "분개 항목"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_date
msgid "Latest Follow-up"
msgstr "최근 후속 조치"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "보고서 요약 및 각주 관리"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#, python-format
msgid "Manual Action"
msgstr "직접 조치"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__manual_action_type_id
msgid "Manual Action Type"
msgstr "직접 조치 유형"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Manual action done"
msgstr "직접 조치 완료"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Message"
msgstr "메시지"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__payment_next_action_date
msgid "Next Action Date"
msgstr "다음 조치 날짜"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Next Reminder Date set to %s"
msgstr "다음 알림 날짜가 %s로 설정됨"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "작업 불필요함"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr "발송할 후속 조치가 없습니다!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "No followup to send!"
msgstr "발송할 후속 조치가 없습니다!"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Normal debtor"
msgstr "일반 채무자"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid ""
"Odoo will remind you to take care of this follow-up on the next reminder "
"date."
msgstr "Odoo는 다음 알림 날짜에 이 후속 조치를 처리하도록 상기시켜 줄 것입니다."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__payment_responsible_id
msgid ""
"Optionally you can assign a user to this field, which will make him "
"responsible for the action."
msgstr "또는, 이 필드에 사용자를 할당하여 후속 관리 담당자로 지정할 수 있습니다."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "옵션"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Overdue Invoices"
msgstr "연체 청구서"

#. module: account_followup
#: code:addons/account_followup/models/res_partner.py:0
#, python-format
msgid "Overdue Payments for %s"
msgstr "%s에 대한 연체 지불금"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_report_manager__partner_id
msgid "Partner"
msgstr "파트너"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Partner entries"
msgstr "협력사 항목"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
msgid "Payment Follow-ups"
msgstr "결제 후속 관리"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Payment Reminder"
msgstr "결제 알림"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "후속 조치 서신 인쇄"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__print_letter
msgid "Print a Letter"
msgstr "서신 인쇄"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Print letter"
msgstr "서신 인쇄"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__description
msgid "Printed Message"
msgstr "인쇄된 메시지"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process follow-ups"
msgstr "후속 조치 처리"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Reconcile"
msgstr "조정"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Reference"
msgstr "참조"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Remind me later"
msgstr "나중에 알림"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_description
msgid "SMS Text Message"
msgstr "SMS 문자 메시지"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Save"
msgstr "저장"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "후속 조치 검색"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "Second reminder letter and email"
msgstr "두 번째 미리 알림 서신 및 이메일"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "SMS 문자메시지 전송"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send an Email"
msgstr "이메일 발송"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send an SMS Message"
msgstr "SMS 메시지 전송"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Send an email"
msgstr "이메일 발송"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by email"
msgstr "이메일로 전송"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "Send by sms"
msgstr "SMS로 전송"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "Source Document"
msgstr "원본 문서"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__payment_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__payment_next_action_date
msgid "The date before which no action should be taken."
msgstr "조치를 취하지 않아야 하는 날짜입니다."

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up report was successfully emailed!"
msgstr "후속 조치 보고서가 이메일로 발송되었습니다!"

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/xml/account_followup_template.xml:0
#, python-format
msgid "The follow-up was successfully sent!"
msgstr "후속 조치가 성공적으로 전송되었습니다!"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder.  Could be negative if you want to send a polite alert "
"beforehand."
msgstr ""
"청구서 만기일이 지나고 알림을 보낼 때까지의 대기일입니다. 사전에 정중한 미리 알림을 보내고 싶다면 음수값을 지정할 수 있습니다."

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "Third reminder: phone the customer"
msgstr "세 번째 미리 알림 : 고객에게 전화"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "합계"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
#, python-format
msgid "Total Due"
msgstr "총 만기일"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
#, python-format
msgid "Total Overdue"
msgstr "총 기한 초과"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total credit"
msgstr "총 대변"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.account_move_line_partner_tree
msgid "Total debit"
msgstr "총 차변"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices
msgid "Unpaid Invoices"
msgstr "미지급 청구서"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "미조정된 Aml"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "Urging reminder email"
msgstr "미리 알림 메일 요청"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "Urging reminder letter"
msgstr "미리 알림 서신 요청"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template_followup_report
msgid "View Invoice"
msgstr "청구서 보기"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__print_letter
msgid "When processing, it will print a PDF"
msgstr "처리되면 PDF를 인쇄합니다."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_email
msgid "When processing, it will send an email"
msgstr "처리되면 이메일을 발송합니다."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__send_sms
msgid "When processing, it will send an sms text message"
msgstr "처리되면 SMS 문자 메시지를 발송합니다."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__manual_action
msgid ""
"When processing, it will set the manual action to be taken for that "
"customer. "
msgstr "처리되면 해당 고객에 대해 직접 조치를 취하도록 설정합니다."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_search_template
msgid "With Overdue Invoices"
msgstr "연체 청구서 사용"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "연체 청구서 사용"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid ""
"Write here the introduction in the letter and mail or sms,\n"
"                                            according to the level of the follow-up. You can\n"
"                                            use the following keywords in the text. Don't\n"
"                                            forget to translate in all languages you installed\n"
"                                            using to top right icon."
msgstr ""

#. module: account_followup
#. openerp-web
#: code:addons/account_followup/static/src/js/followup_form_controller.js:0
#, python-format
msgid "You are done with the follow-ups!<br/>You have skipped %s partner(s)."
msgstr "후속 조치를 완료했습니다! %s 협력사는 제외하였습니다."

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You are trying to send a followup report to a partner for which you didn't "
"print all the invoices ({})"
msgstr "({}) 청구서를 모두 인쇄하지 않은 상태에서 협력사에게 후속 조치 보고서를 보내려고 합니다."

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid ""
"You need a least one follow-up level in order to process your follow-up"
msgstr "후속 조치를 처리하려면 하나 이상의 후속 조치 수준이 필요합니다."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your description is invalid, use the right legend or %% if you want to use "
"the percent character."
msgstr "귀하의 설명은 유효하지 않습니다. 적절한 범례를 사용하거나 비율 문자를 사용하려면 %%를 사용하십시오."

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your email subject is invalid, use the right legend or %% if you want to use"
" the percent character."
msgstr ""

#. module: account_followup
#: code:addons/account_followup/models/account_followup.py:0
#, python-format
msgid ""
"Your sms description is invalid, use the right legend or %% if you want to "
"use the percent character."
msgstr "귀하의 SMS 설명은 유효하지 않습니다. 적절한 범례를 사용하거나 비율 문자를 사용하려면 %%를 사용하십시오."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days overdue, do the following actions:"
msgstr "일이 지날 경우, 다음과 같은 조치를 취합니다."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. Call the customer, check if it's paid, ..."
msgstr "예: 고객에게 전화, 지불되었는지 확인..."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "예: 첫번째 이메일 알림"

#. module: account_followup
#: code:addons/account_followup/models/account_followup_report.py:0
#, python-format
msgid "payment reminder"
msgstr "결제 알림"
