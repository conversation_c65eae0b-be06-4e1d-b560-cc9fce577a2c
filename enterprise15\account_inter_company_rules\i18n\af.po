# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * inter_company_rules
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Afrikaans (https://www.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:89
#, python-format
msgid " Invoice: "
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_account_invoice_auto_generated
msgid "Auto Generated Document"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_purchase_order_auto_generated
msgid "Auto Generated Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_sale_order_auto_generated
msgid "Auto Generated Sales Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings_auto_validation
#: model:ir.ui.view,arch_db:inter_company_rules.res_config_settings_view_form
msgid "Auto-validate Sales/Purchase Orders"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_res_company
msgid "Companies"
msgstr "Maatskappye"

#. module: inter_company_rules
#: model:ir.ui.view,arch_db:inter_company_rules.res_config_settings_view_form
msgid "Company"
msgstr "Maatskappy"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:82
#: code:addons/inter_company_rules/models/sale_order.py:77
#, python-format
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_auto_generate_invoices
msgid ""
"Create Invoices/Credit Notes when encoding invoices/credit notes made to "
"this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_po_from_so
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings_po_from_so
msgid "Create Purchase Orders when selling to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_so_from_po
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings_so_from_po
msgid "Create Sales Orders when buying to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings_warehouse_id
msgid ""
"Default value to set on Purchase Orders that will be created based on Sales "
"Orders made to this company."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_auto_generate_invoices
msgid ""
"Generate Customer/Vendor Bills (and credit notes) when encoding invoices (or credit notes) made to this company.\n"
" e.g: Generate a Customer Invoice when a Vendor Bill with this company as vendor is created."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings_po_from_so
msgid ""
"Generate a Purchase Order when a Sale Order with this company as customer is"
" created."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_po_from_so
msgid ""
"Generate a Purchase Order when a Sales Order with this company as customer is created.\n"
" The intercompany user must at least be Purchase User."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings_so_from_po
msgid ""
"Generate a Sale Order when a Purchase Order with this company as vendor is "
"created."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_so_from_po
msgid ""
"Generate a Sales Order when a Purchase Order with this company as vendor is created.\n"
" The intercompany user must at least be Sale User."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_intercompany_user_id
msgid "Inter Company User"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:42
#: code:addons/inter_company_rules/models/sale_order.py:46
#, python-format
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""

#. module: inter_company_rules
#: model:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Rules"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_account_invoice
msgid "Invoice"
msgstr "Faktuur"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:81
#, python-format
msgid "Please define %s journal for this company: \"%s\" (id:%d)."
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:39
#, python-format
msgid "Provide at least one user for inter company relation for % "
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:44
#: code:addons/inter_company_rules/models/sale_order.py:43
#, python-format
msgid "Provide one user for intercompany relation for % "
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_sale_order
msgid "Quotation"
msgstr "Kwotasie"

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings_rule_type
msgid "Rule Type"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_auto_validation
msgid "Sale/Purchase Orders Auto Validation"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings_rules_company_id
msgid "Select Company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings_rules_company_id
msgid "Select company to setup Inter company rules."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings_rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""

#. module: inter_company_rules
#: selection:res.config.settings,rule_type:0
msgid "Send Invoices & Credit Notes"
msgstr ""

#. module: inter_company_rules
#: selection:res.config.settings,rule_type:0
msgid "Send Sales & Purchase Orders"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_account_invoice_auto_invoice_id
msgid "Source Invoice"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_sale_order_auto_purchase_order_id
msgid "Source Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_purchase_order_auto_sale_order_id
msgid "Source Sales Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_warehouse_id
msgid "Warehouse"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_config_settings_warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_config_settings_auto_validation
msgid ""
"When a Sale Order or a Purchase Order is created by a multi\n"
"            company rule for this company, it will automatically validate it."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_auto_validation
msgid ""
"When a Sales Order or a Purchase Order is created by a multi company rule "
"for this company, it will automatically validate it"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:47
#, python-format
msgid ""
"You cannot create SO from PO because sale price list currency is different "
"than purchase price list currency."
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/res_company.py:32
#, python-format
msgid ""
"You cannot select to create invoices based on other invoices\n"
"                    simultaneously with another option ('Create Sales Orders when buying to this\n"
"                    company' or 'Create Purchase Orders when selling to this company')!"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_res_config_settings
msgid "res.config.settings"
msgstr "res.config.settings"
