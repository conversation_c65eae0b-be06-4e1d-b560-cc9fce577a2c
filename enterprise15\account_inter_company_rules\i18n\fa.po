# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_inter_company_rules
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><EMAIL>>, 2022
# <PERSON> <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_inter_company_rules
#: code:addons/account_inter_company_rules/models/account_move.py:0
#, python-format
msgid "%s Invoice: %s"
msgstr "%s فاکتور: %s"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_payment__auto_generated
msgid "Auto Generated Document"
msgstr "سند خودکار تولید شده"

#. module: account_inter_company_rules
#: code:addons/account_inter_company_rules/models/account_move.py:0
#, python-format
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr "به صورت خودکار از %(origin)s شرکت %(company)s ایجاد شده است."

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid "Create as"
msgstr "ایجاد به عنوان"

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__rule_type__not_synchronize
msgid "Do not synchronize"
msgstr "همگام‌سازی نکن"

#. module: account_inter_company_rules
#: code:addons/account_inter_company_rules/models/res_company.py:0
#, python-format
msgid ""
"Generate a bill/invoice when a company confirms an invoice/bill for %s."
msgstr ""

#. module: account_inter_company_rules
#: model_terms:ir.ui.view,arch_db:account_inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Transactions"
msgstr "تراکنشهای درون شرکتی"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_transaction_message
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_transaction_message
msgid "Intercompany Transaction Message"
msgstr "پیام تراکنش بین شرکتی"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move
msgid "Journal Entry"
msgstr "ورودی روزنامه"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move_line
msgid "Journal Item"
msgstr "آیتم سند"

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr "کاربر مسئول ایجاد اسناد طبق قوانین بین شرکتی."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__rule_type
msgid "Rule"
msgstr "قاعده"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__rules_company_id
msgid "Select Company"
msgstr "انتخاب شرکت"

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__rule_type
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""
"نوع قوانین بین شرکتی که باید در شرکت انتخاب شده رعایت شوند را مشخص کنید."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_payment__auto_invoice_id
msgid "Source Invoice"
msgstr "فاکتور منبع"

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__rule_type__invoice_and_refund
msgid "Synchronize invoices/bills"
msgstr "همگام‌سازی فاکتورها/صورت‌حساب‌ها"
