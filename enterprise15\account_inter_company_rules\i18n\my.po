# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * inter_company_rules
# 
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-07 09:57+0000\n"
"PO-Revision-Date: 2016-09-07 09:57+0000\n"
"Last-Translator: Hein Myat Phone <<EMAIL>>, 2016\n"
"Language-Team: Burmese (https://www.transifex.com/odoo/teams/41243/my/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: my\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:84
#, python-format
msgid " Invoice: "
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_account_invoice_auto_generated
msgid "Auto Generated Document"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_purchase_order_auto_generated
msgid "Auto Generated Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_sale_order_auto_generated
msgid "Auto Generated Sale Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_res_company
msgid "Companies"
msgstr "ကုမ္ပဏီများ"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:82
#: code:addons/inter_company_rules/models/sale_order.py:77
#, python-format
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""

#. module: inter_company_rules
#: selection:base.config.settings,rule_type:0
msgid "Create Invoice/Refunds when encoding invoice/refunds"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_auto_generate_invoices
msgid ""
"Create Invoices/Refunds when encoding invoices/refunds made to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_base_config_settings_po_from_so
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_po_from_so
msgid "Create Purchase Orders when selling to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_base_config_settings_so_from_po
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_so_from_po
msgid "Create Sale Orders when buying to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_base_config_settings_warehouse_id
msgid ""
"Default value to set on Purchase Orders that will be created based on Sale "
"Orders made to this company."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_auto_generate_invoices
msgid ""
"Generate Customer/Vendor Bills (and refunds) when encoding invoices (or refunds) made to this company.\n"
" e.g: Generate a Customer Invoice when a Vendor Bill with this company as vendor is created."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_base_config_settings_po_from_so
msgid ""
"Generate a Purchase Order when a Sale Order with this company as customer is"
" created."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_po_from_so
msgid ""
"Generate a Purchase Order when a Sale Order with this company as customer is created.\n"
" The intercompany user must at least be Purchase User."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_base_config_settings_so_from_po
msgid ""
"Generate a Sale Order when a Purchase Order with this company as vendor is "
"created."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_so_from_po
msgid ""
"Generate a Sale Order when a Purchase Order with this company as vendor is created.\n"
" The intercompany user must at least be Sale User."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_intercompany_user_id
msgid "Inter Company User"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:43
#: code:addons/inter_company_rules/models/sale_order.py:46
#, python-format
msgid "Inter company user of company %s doesn't have enough access rights"
msgstr ""

#. module: inter_company_rules
#: model:ir.ui.view,arch_db:inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Rules"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_account_invoice
msgid "Invoice"
msgstr "ဝယ်ကုန်စာရင်း"

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:75
#, python-format
msgid "Please define %s journal for this company: \"%s\" (id:%d)."
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:40
#, python-format
msgid "Provide at least one user for inter company relation for % "
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/account_invoice.py:40
#: code:addons/inter_company_rules/models/sale_order.py:43
#, python-format
msgid "Provide one user for intercompany relation for % "
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_base_config_settings_rule_type
msgid "Rule type"
msgstr ""

#. module: inter_company_rules
#: selection:base.config.settings,rule_type:0
msgid "SO and PO setting for inter company"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_base_config_settings_auto_validation
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_auto_validation
msgid "Sale/Purchase Orders Auto Validation"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_base_config_settings_rule_type
msgid "Select the type to setup inter company rules in selected company."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_account_invoice_auto_invoice_id
msgid "Source Invoice"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_sale_order_auto_purchase_order_id
msgid "Source Purchase Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_purchase_order_auto_sale_order_id
msgid "Source Sale Order"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_res_company_warehouse_id
msgid "Warehouse"
msgstr "ကုန်လှောင်ရုံ"

#. module: inter_company_rules
#: model:ir.model.fields,field_description:inter_company_rules.field_base_config_settings_warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_base_config_settings_auto_validation
msgid ""
"When a Sale Order or a Purchase Order is created by a multi\n"
"            company rule for this company, it will automatically validate it."
msgstr ""

#. module: inter_company_rules
#: model:ir.model.fields,help:inter_company_rules.field_res_company_auto_validation
msgid ""
"When a Sale Order or a Purchase Order is created by a multi company rule for"
" this company, it will automatically validate it"
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/purchase_order.py:47
#, python-format
msgid ""
"You cannot create SO from PO because sale price list currency is different "
"than purchase price list currency."
msgstr ""

#. module: inter_company_rules
#: code:addons/inter_company_rules/models/res_company.py:32
#, python-format
msgid ""
"You cannot select to create invoices based on other invoices\n"
"                    simultaneously with another option ('Create Sale Orders when buying to this\n"
"                    company' or 'Create Purchase Orders when selling to this company')!"
msgstr ""

#. module: inter_company_rules
#: model:ir.model,name:inter_company_rules.model_base_config_settings
msgid "base.config.settings"
msgstr ""
