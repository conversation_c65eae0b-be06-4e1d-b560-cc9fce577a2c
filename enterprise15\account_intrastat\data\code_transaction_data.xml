<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="account_intrastat_transaction_1" model="account.intrastat.code">
            <field name="code">1</field>
            <field name="type">transaction</field>
            <field name="description">Transactions involving an actual or intended transfer of ownership from residents to non-residents against financial or other compensation (except the transactions listed under 2, 7 and 8)</field>
        </record>
        <record id="account_intrastat_transaction_2" model="account.intrastat.code">
            <field name="code">2</field>
            <field name="type">transaction</field>
            <field name="description">Return and replacement of goods free of charge after registration of the original transaction</field>
        </record>
        <record id="account_intrastat_transaction_3" model="account.intrastat.code">
            <field name="code">3</field>
            <field name="type">transaction</field>
            <field name="description">Transactions involving transfer of ownership without financial or in kind compensation (e.g. aid shipments)</field>
        </record>
        <record id="account_intrastat_transaction_4" model="account.intrastat.code">
            <field name="code">4</field>
            <field name="type">transaction</field>
            <field name="description">Operations with a view to processing 1 under contract (no transfer of ownership to the processor)</field>
        </record>
        <record id="account_intrastat_transaction_5" model="account.intrastat.code">
            <field name="code">5</field>
            <field name="type">transaction</field>
            <field name="description">Operations following processing under contract (no transfer of ownership to the processor)</field>
        </record>
        <record id="account_intrastat_transaction_6" model="account.intrastat.code">
            <field name="code">6</field>
            <field name="type">transaction</field>
            <field name="description">Operations "following" repair or maintenance, other than under joint inter-governmental production programmes. Repair and maintenance can be against payment or free of charge. These operations do not involve a (future) transfer of ownership*.</field>
        </record>
        <record id="account_intrastat_transaction_7" model="account.intrastat.code">
            <field name="code">7</field>
            <field name="type">transaction</field>
            <field name="description">Operations under joint defence projects or other joint intergovernmental production programmes</field>
        </record>
        <record id="account_intrastat_transaction_8" model="account.intrastat.code">
            <field name="code">8</field>
            <field name="type">transaction</field>
            <field name="description">Transactions involving the supply of building materials and technical equipment under a general construction or civil engineering contract for which no separate invoicing of the goods is required and an invoice for the total contract is issued</field>
        </record>
        <record id="account_intrastat_transaction_9" model="account.intrastat.code">
            <field name="code">9</field>
            <field name="type">transaction</field>
            <field name="description">Other transactions which cannot be classified under other codes</field>
        </record>
        <record id="account_intrastat_transaction_11" model="account.intrastat.code">
            <field name="code">11</field>
            <field name="type">transaction</field>
            <field name="description">Outright sale/purchase except direct tade with/by private consumers. Transactions involving actual change of ownership with financial compensation</field>
        </record>
        <record id="account_intrastat_transaction_12" model="account.intrastat.code">
            <field name="code">12</field>
            <field name="type">transaction</field>
            <field name="description">Direct trade with/by private consumers (incl. distance sale). Transactions involving actual change of ownership with financial compensation</field>
        </record>
        <record id="account_intrastat_transaction_21" model="account.intrastat.code">
            <field name="code">21</field>
            <field name="type">transaction</field>
            <field name="description">Returned goods. Return and replacement of goods free of charge after registration of the original transaction</field>
        </record>
        <record id="account_intrastat_transaction_22" model="account.intrastat.code">
            <field name="code">22</field>
            <field name="type">transaction</field>
            <field name="description">Replacement of replaced goods. Return and replacement of goods free of charge after registration of the original transaction</field>
        </record>
        <record id="account_intrastat_transaction_23" model="account.intrastat.code">
            <field name="code">23</field>
            <field name="type">transaction</field>
            <field name="description">Replacement (e.g. under warranty) for goods not being returned. Return and replacement of goods free of charge after registration of the original transaction</field>
        </record>
        <record id="account_intrastat_transaction_31" model="account.intrastat.code">
            <field name="code">31</field>
            <field name="type">transaction</field>
            <field name="description">Movements to/from a warehouse (excluding call-off and consignment stock). Transactions involving intended change of ownership without financial compensation</field>
        </record>
        <record id="account_intrastat_transaction_32" model="account.intrastat.code">
            <field name="code">32</field>
            <field name="type">transaction</field>
            <field name="description">Supply for sale on approval after trial (including call-off and consignment stock). Transactions involving intended change of ownership without financial compensation</field>
        </record>
        <record id="account_intrastat_transaction_33" model="account.intrastat.code">
            <field name="code">33</field>
            <field name="type">transaction</field>
            <field name="description">Financial leasing. Transactions involving intended change of ownership without financial compensation</field>
        </record>
        <record id="account_intrastat_transaction_34" model="account.intrastat.code">
            <field name="code">34</field>
            <field name="type">transaction</field>
            <field name="description">Transactions involving transfer of ownership without financial compensation. Transactions involving intended change of ownership without financial compensation</field>
        </record>
        <record id="account_intrastat_transaction_41" model="account.intrastat.code">
            <field name="code">41</field>
            <field name="type">transaction</field>
            <field name="description">Goods expected to return to the initial Member State/country of export. Transactions with a view to processing under contract (not involving change of ownership)</field>
        </record>
        <record id="account_intrastat_transaction_42" model="account.intrastat.code">
            <field name="code">42</field>
            <field name="type">transaction</field>
            <field name="description">Goods not expected to return to the initial Member State/country of export. Transactions with a view to processing under contract (not involving change of ownership)</field>
        </record>
        <record id="account_intrastat_transaction_51" model="account.intrastat.code">
            <field name="code">51</field>
            <field name="type">transaction</field>
            <field name="description">Goods returning to the initial Member State/country of export. Transactions following processing under contract (not involving change of ownership)</field>
        </record>
        <record id="account_intrastat_transaction_52" model="account.intrastat.code">
            <field name="code">52</field>
            <field name="type">transaction</field>
            <field name="description">Goods not returning to the initial Member State/country of export. Transactions following processing under contract (not involving change of ownership)</field>
        </record>
        <record id="account_intrastat_transaction_60" model="account.intrastat.code">
            <field name="code">60</field>
            <field name="type">transaction</field>
            <field name="description">Particular transactions recorded for national purposes, such as repairs</field>
        </record>
        <record id="account_intrastat_transaction_71" model="account.intrastat.code">
            <field name="code">71</field>
            <field name="type">transaction</field>
            <field name="description">Release of goods for free circulation in a Member State with a subsquent export to another Member State. Transactions with a view to/following customs clearance (not involving change of ownership, related to goods in quasi-import or export).</field>
        </record>
        <record id="account_intrastat_transaction_72" model="account.intrastat.code">
            <field name="code">72</field>
            <field name="type">transaction</field>
            <field name="description">Transportation of goods from one Member State to another Member State to place the goods under the export procedure. Transactions with a view to/following customs clearance (not involving change of ownership, related to goods in quasi-import or export)</field>
        </record>
        <record id="account_intrastat_transaction_80" model="account.intrastat.code">
            <field name="code">80</field>
            <field name="type">transaction</field>
            <field name="description">Transactions involving the supply of building materials and technical equipment under a general construction or civil engineering contract for which no seperate invoicing of the goods is required and an invoice for the total contract is issued</field>
        </record>
        <record id="account_intrastat_transaction_91" model="account.intrastat.code">
            <field name="code">91</field>
            <field name="type">transaction</field>
            <field name="description">Hire, loan, and operational leasing longer than 24 months. Other transactions which cannot be classified under other codes</field>
        </record>
        <record id="account_intrastat_transaction_99" model="account.intrastat.code">
            <field name="code">99</field>
            <field name="type">transaction</field>
            <field name="description">Other. Other transactions which cannot be classified under other codes</field>
        </record>
    </data>
</odoo>
