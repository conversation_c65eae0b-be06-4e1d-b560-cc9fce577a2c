<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="base.de" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.be" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.at" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.cy" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.dk" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.es" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.ee" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.fi" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.fr" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.gr" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.hu" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.ie" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.it" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.lv" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.lt" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.lu" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.mt" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.nl" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.pl" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.pt" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.sk" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.cz" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.uk" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.si" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.se" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.bg" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.hr" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="base.ro" model="res.country">
            <field eval="True" name="intrastat"/>
        </record>
        <record id="account_intrastat.xi" model="res.country">
            <field name="name">Northern Ireland</field>
            <field name="code">xi</field>
            <field name="currency_id" ref="base.GBP" />
            <field eval="True" name="intrastat"/>
        </record>
    </data>
</odoo>
