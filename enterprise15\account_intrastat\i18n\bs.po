# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_intrastat
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"                Types:"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:16
#, python-format
msgid "Arrival"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:33
#: model:ir.model.fields,field_description:account_intrastat.field_product_category__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_id
#, python-format
msgid "Commodity Code"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "Država"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:30
#: code:addons/account_intrastat/models/account_sales_report.py:24
#, python-format
msgid "Country Code"
msgstr "Šifra države"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:28
#, python-format
msgid "Date"
msgstr "Datum"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default Transport Mode"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__incoterm_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__incoterm_id
msgid "Default incoterm for Intrastat"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:17
#, python-format
msgid "Dispatch"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_supplier_form_inherit_account_intrastat
msgid "Dispatch country"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_intrastat_report__display_name
#: model:ir.model.fields,field_description:account_intrastat.field_account_sales_report__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_sales_report.py:124
#: model:ir.actions.client,name:account_intrastat.action_account_report_sales
#: model:ir.model,name:account_intrastat.model_account_sales_report
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_sales
#, python-format
msgid "EC Sales List"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended Mode"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended,"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_intrastat_report__id
#: model:ir.model.fields,field_description:account_intrastat.field_account_sales_report__id
msgid "ID"
msgstr "ID"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:39
#, python-format
msgid "Incoterm Code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_company__incoterm_id
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Internacionalni komercijalni uslovi su niz predefiniranih komercijalnih "
"uslova koji se koriste za inostrane transakcije."

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_invoice_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_category_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr ""

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_invoice__intrastat_country_id
msgid "Intrastat Country"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:234
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
#, python-format
msgid "Intrastat Report"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_invoice__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_invoice__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
msgid "Intrastat region"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka fakture"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_intrastat_report____last_update
#: model:ir.model.fields,field_description:account_intrastat.field_account_sales_report____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_category
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:43
#, python-format
msgid "Quantity"
msgstr "Količina"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:32
#, python-format
msgid "Region Code"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Standard,"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:29
#, python-format
msgid "System"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:224
#: code:addons/account_intrastat/models/account_sales_report.py:114
#, python-format
msgid "Total"
msgstr "Ukupno"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "Transakcija"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:31
#, python-format
msgid "Transaction Code"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:38
#, python-format
msgid "Transport Code"
msgstr ""

#. module: account_intrastat
#: sql_constraint:account.intrastat.code:0
msgid "Triplet code/type/country_id must be unique."
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:34
#, python-format
msgid "Type"
msgstr "Tip"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_sales_report.py:23
#, python-format
msgid "VAT"
msgstr "PDV"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:44
#: code:addons/account_intrastat/models/account_sales_report.py:25
#, python-format
msgid "Value"
msgstr "Vrijednost"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:42
#, python-format
msgid "Weight"
msgstr "Težina"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
msgid "account.intrastat.code"
msgstr ""
