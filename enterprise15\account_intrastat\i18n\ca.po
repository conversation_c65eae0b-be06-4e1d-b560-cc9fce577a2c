# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON><PERSON><PERSON>, 2022
# oscaryuu, 2022
# jabe<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: jabelchi, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"                Types:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"                Tipus:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/> Opcions:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> Socis:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "All"
msgstr "Tots"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Arrival"
msgstr "Arribada"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr "País d'arribada"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr "Per país"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr "Per tipus"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__commodity
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr "Producte"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model:ir.model.fields,field_description:account_intrastat.field_product_category__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_id
#, python-format
msgid "Commodity Code"
msgstr "Codi de producte"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "País de l'empresa"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "País"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Country Code"
msgstr "Codi de país"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_origin_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_origin_country_id
msgid "Country of Origin"
msgstr "País d'origen"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default Transport Mode"
msgstr "Mode de transport predeterminat"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Dispatch"
msgstr "Despatx"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended"
msgstr "Estès"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended Mode"
msgstr "Mode estès"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Include VAT"
msgstr "IVA Inclòs"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Incoterm Code"
msgstr "Codi incoterm"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_category_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr "Intrastat"

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr "Codi Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat Country"
msgstr "País Instrastat"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
#, python-format
msgid "Intrastat Report"
msgstr "Informe Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr "Mode de transport Intrastat"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr "Codi Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr "País Intrastat, arribada a les vendes, enviament a les compres"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr "Membre Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
msgid "Intrastat region"
msgstr "Regió d'Intrastat"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move_line
msgid "Journal Item"
msgstr "Apunt comptable"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Only with VAT numbers"
msgstr "Sólament amb NIFs"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model_terms:ir.ui.view,arch_db:account_intrastat.report_invoice_document_intrastat_2019
#, python-format
msgid "Origin Country"
msgstr "País d'origen"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Partner VAT"
msgstr "NIF del partner"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_product
msgid "Product"
msgstr "Producte"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_category
msgid "Product Category"
msgstr "Categoria del producte"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_product_origin_country_id
msgid "Product Country"
msgstr "País del producte"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
msgid "Product Template"
msgstr "Plantilla de producte"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Quantity"
msgstr "Quantitat"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__region
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr "Regió"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Region Code"
msgstr "Codi de regió"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Standard"
msgstr "Estàndard "

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "System"
msgstr "Sistema"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "El país que fan servir els informes d'impostos d'aquesta empresa."

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__transaction
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "Transacció"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Transaction Code"
msgstr "Codi de transacció"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr "Transport"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Transport Code"
msgstr "Codi de transport"

#. module: account_intrastat
#: model:ir.model.constraint,message:account_intrastat.constraint_account_intrastat_code_intrastat_region_code_unique
msgid "Triplet code/type/country_id must be unique."
msgstr "El triplet codi/tipus/identificador de país ha de ser únic."

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Type"
msgstr "Tipus"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Value"
msgstr "Valor"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_variant_id
msgid "Variant commodity Code"
msgstr "Codi de variant de producte"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Weight"
msgstr "Pes"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "With VAT numbers"
msgstr "Amb NIFs"
