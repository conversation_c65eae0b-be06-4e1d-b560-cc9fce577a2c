# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# NoaFarkash, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Ha Ketem <<EMAIL>>, 2022\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"                Types:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"                סוגים:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/> אפשרויות:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> לקוחות/ספקים:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "All"
msgstr "הכל"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Arrival"
msgstr "הגעה"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr "מדינת הגעה"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr "לפי מדינה"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr "לפי סוג"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__commodity
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr "טובין"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model:ir.model.fields,field_description:account_intrastat.field_product_category__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_id
#, python-format
msgid "Commodity Code"
msgstr "קוד טובין"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "ארץ החברה"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "ארץ"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Country Code"
msgstr "קוד ארץ"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_origin_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_origin_country_id
msgid "Country of Origin"
msgstr "מדינת מקור"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default Transport Mode"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Dispatch"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended"
msgstr "מורחב"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended Mode"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Include VAT"
msgstr "הוספת מע\"מ"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Incoterm Code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_category_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr "Intrastat"

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr "קוד סטטיסטיקה פנימית"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat Country"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
#, python-format
msgid "Intrastat Report"
msgstr "דוח Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr ""

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
msgid "Intrastat region"
msgstr "אזור Intrastat"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move_line
msgid "Journal Item"
msgstr "פקודת יומן"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Only with VAT numbers"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model_terms:ir.ui.view,arch_db:account_intrastat.report_invoice_document_intrastat_2019
#, python-format
msgid "Origin Country"
msgstr "מדינת מקור"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Partner VAT"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_product
msgid "Product"
msgstr "מוצר"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_category
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_product_origin_country_id
msgid "Product Country"
msgstr ""

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Quantity"
msgstr "כמות"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__region
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr "איזור"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Region Code"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Standard"
msgstr "סטנדרט"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "System"
msgstr "מערכת"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "המדינה המוגדרת לצורך להפקת דוחות מסים עבור ארגון זה."

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Total"
msgstr "סה\"כ"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__transaction
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "עסקה"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Transaction Code"
msgstr "קוד עסקה"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr "הובלה"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Transport Code"
msgstr "קוד הובלה"

#. module: account_intrastat
#: model:ir.model.constraint,message:account_intrastat.constraint_account_intrastat_code_intrastat_region_code_unique
msgid "Triplet code/type/country_id must be unique."
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Type"
msgstr "סוג"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Value"
msgstr "ערך"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_variant_id
msgid "Variant commodity Code"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Weight"
msgstr "משקל"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "With VAT numbers"
msgstr ""
