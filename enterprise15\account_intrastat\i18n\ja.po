# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# Wil <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"                Types:"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/> オプション:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr "<span class=\"fa fa-filter\"/> 取引先:"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_type
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "All"
msgstr "全て"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Arrival"
msgstr "到着"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr "到着国"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr "国ごと"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr "タイプごと"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__commodity
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr "コモディティー"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model:ir.model.fields,field_description:account_intrastat.field_product_category__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_id
#, python-format
msgid "Commodity Code"
msgstr "コモディティーコード"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "会社"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "コモディティー国"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "国"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Country Code"
msgstr "国コード"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_origin_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_origin_country_id
msgid "Country of Origin"
msgstr "原産国"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Date"
msgstr "日付"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default Transport Mode"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Dispatch"
msgstr "発送"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended"
msgstr ""

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Extended Mode"
msgstr "拡張モード"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Include VAT"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Incoterm Code"
msgstr "インコタームコード"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_category_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr "州内"

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr "州内コード"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat Country"
msgstr "Intrastat国"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
#, python-format
msgid "Intrastat Report"
msgstr "イントラスタットレポート"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_payment__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr "Intrastatトランスポートモード"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr "州内コード"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_move__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_payment__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr "Intrastat国、販売の場合は到着、購入の場合は発送"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr "州内メンバー"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
msgid "Intrastat region"
msgstr "イントラスタットリージョン"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move
msgid "Journal Entry"
msgstr "仕訳"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move_line
msgid "Journal Item"
msgstr "仕訳明細"

#. module: account_intrastat
#: model:res.country,name:account_intrastat.xi
msgid "Northern Ireland"
msgstr "北アイルランド"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "Only with VAT numbers"
msgstr "VAT番号とのみ"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: model_terms:ir.ui.view,arch_db:account_intrastat.report_invoice_document_intrastat_2019
#, python-format
msgid "Origin"
msgstr "移動元"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Partner VAT"
msgstr "取引先VAT"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_product
msgid "Product"
msgstr "プロダクト"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_category
msgid "Product Category"
msgstr "プロダクトカテゴリ"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_product_origin_country_id
msgid "Product Country"
msgstr "プロダクト国"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
msgid "Product Template"
msgstr "プロダクトテンプレート"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Quantity"
msgstr "数量"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__region
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr "領域"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Region Code"
msgstr "地域コード"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_intrastat_extended
msgid "Standard"
msgstr "スタンダード"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "System"
msgstr "システム"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "この会社の税レポートを使用する国"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Total"
msgstr "合計"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__transaction
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "取引明細書"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Transaction Code"
msgstr "トランザクションコード"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr "輸送"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Transport Code"
msgstr "輸送コード"

#. module: account_intrastat
#: model:ir.model.constraint,message:account_intrastat.constraint_account_intrastat_code_intrastat_region_code_unique
msgid "Triplet code/type/country_id must be unique."
msgstr "Triplet code/type/country_idは単一である必要があります。"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Type"
msgstr "タイプ"

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Value"
msgstr "値"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_variant_id
msgid "Variant commodity Code"
msgstr ""

#. module: account_intrastat
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#, python-format
msgid "Weight"
msgstr "重量"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.search_template_vat
msgid "With VAT numbers"
msgstr ""
