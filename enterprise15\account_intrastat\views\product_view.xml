<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="product_template_form_view_inherit_account_intrastat" model="ir.ui.view">
            <field name="name">product.template.form.inherit.account.intrastat</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="account.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='invoicing']//group[@name='accounting']" position="inside">
                    <group name="intrastat" string="Intrastat">
                        <field name="intrastat_id"/>
                        <field name="intrastat_origin_country_id" options="{'no_open': True, 'no_create': True}"/>
                    </group>
                </xpath>
                <xpath expr="//page[@name='inventory']" position="attributes">
                    <attribute name="groups" add="account.group_account_invoice" separator=","/>
                </xpath>
            </field>
        </record>

        <record id="product_product_form_view_inherit_account_intrastat" model="ir.ui.view">
            <field name="name">product.product.form.inherit.account.intrastat</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_variant_easy_edit_view"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="inside">
                    <group>
                        <group name="intrastat" string="Intrastat">
                            <field name="intrastat_id"/>
                        </group>
                    </group>
                </xpath>
            </field>
        </record>

        <record id="product_category_form_view_inherit_account_intrastat" model="ir.ui.view">
            <field name="name">product.category.form.inherit.account.intrastat</field>
            <field name="model">product.category</field>
            <field name="inherit_id" ref="product.product_category_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='first']" position="after">
                    <group string="Intrastat">
                        <field name="intrastat_id"/>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
