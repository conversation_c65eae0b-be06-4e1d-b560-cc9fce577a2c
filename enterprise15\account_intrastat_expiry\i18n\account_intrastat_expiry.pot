# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat_expiry
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 19:53+0000\n"
"PO-Revision-Date: 2024-08-13 19:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.view_intrastat_code_expiry_search
msgid "Active"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "Check the expired"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "Check the premature"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_product_category_search_view
msgid "Intrastat"
msgstr ""

#. module: account_intrastat_expiry
#: model:ir.model,name:account_intrastat_expiry.model_account_intrastat_code
msgid "Intrastat Code"
msgstr ""

#. module: account_intrastat_expiry
#: model:ir.model,name:account_intrastat_expiry.model_account_intrastat_report
msgid "Intrastat Report"
msgstr ""

#. module: account_intrastat_expiry
#: code:addons/account_intrastat_expiry/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid commodity intrastat code product categories."
msgstr ""

#. module: account_intrastat_expiry
#: code:addons/account_intrastat_expiry/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid commodity intrastat code products."
msgstr ""

#. module: account_intrastat_expiry
#: code:addons/account_intrastat_expiry/models/account_intrastat_report.py:0
#, python-format
msgid "Invalid transaction intrastat code entries."
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.product_product_tree_view_account_intrastat_expiry
msgid "Product"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.product_category_tree_view_account_intrastat_expiry
msgid "Product Categories"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.product_category_tree_view_account_intrastat_expiry
msgid "Product Category"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "Some"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "have expired intrastat transaction codes on their lines."
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "have premature intrastat transaction codes on their lines."
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "invoices"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "product categorie's commodity codes"
msgstr ""

#. module: account_intrastat_expiry
#: model_terms:ir.ui.view,arch_db:account_intrastat_expiry.account_intrastat_expiry_main_template
msgid "product's commodity codes"
msgstr ""
