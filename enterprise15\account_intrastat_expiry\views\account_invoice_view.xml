<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="invoice_form_inherit_account_intrastat_expiry" model="ir.ui.view">
            <field name="name">account.move.form.inherit.account.intrastat</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='invoice_line_ids']/tree//field[@name='intrastat_transaction_id']" position="attributes">
                    <attribute name="domain">
                    [
                        ('type', '=', 'transaction'),
                        '|',
                        ('expiry_date', '&gt;', parent.date or context_today().strftime('%Y-%m-%d')),
                        ('expiry_date', '=', None),
                        '|',
                        ('start_date', '&lt;=', parent.date or context_today().strftime('%Y-%m-%d')),
                        ('start_date', '=', None)
                    ]
                    </attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']/form//field[@name='intrastat_transaction_id']" position="attributes">
                    <attribute name="domain">
                    [
                        ('type', '=', 'transaction'),
                        '|',
                        ('expiry_date', '&gt;', parent.date or context_today().strftime('%Y-%m-%d')),
                        ('expiry_date', '=', None),
                        '|',
                        ('start_date', '&lt;=', parent.date or context_today().strftime('%Y-%m-%d')),
                        ('start_date', '=', None)
                    ]
                    </attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
