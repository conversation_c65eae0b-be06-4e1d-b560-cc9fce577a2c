<?xml version="1.0" encoding="utf-8"?>
<odoo>

<record id="account_intrastat_expiry_product_template_search_view" model="ir.ui.view">
    <field name="name">product.template.search.account.intrastat.expiry</field>
    <field name="model">product.template</field>
    <field name="inherit_id" ref="product.product_template_search_view"/>
    <field name="arch" type="xml">
        <xpath expr="//filter[@name='categ_id']" position="after">
            <!-- Group by 'intrastat_id' not possible in 15.0 as field not stored anymore. But view can not be removed in stable.
                 TODO: Remove this record in master -->
            <!--  <filter string="Intrastat" name="group_by_intrastat_id" context="{'group_by':'intrastat_id'}"/> -->
        </xpath>
    </field>
</record>

<record id="product_product_tree_view_account_intrastat_expiry" model="ir.ui.view">
    <field name="name">product.product.tree.account.intrastat.expiry</field>
    <field name="model">product.product</field>
    <field name="mode">primary</field>
    <field eval="7" name="priority"/>
    <field name="arch" type="xml">
        <tree string="Product" multi_edit="1" duplicate="false" editable="bottom" expand="context.get('expand', False)">
            <field name="default_code" readonly="1"/>
            <field name="name" readonly="1"/>
            <field name="intrastat_id"/>
        </tree>
    </field>
</record>

<record id="account_intrastat_expiry_product_category_search_view" model="ir.ui.view">
    <field name="name">product.category.search.account.intrastat.expiry</field>
    <field name="model">product.category</field>
    <field name="inherit_id" ref="product.product_category_search_view"/>
    <field name="arch" type="xml">
        <xpath expr="//field[@name='parent_id']" position="after">
            <filter string="Intrastat" name="group_by_intrastat_id" context="{'group_by':'intrastat_id'}"/>
        </xpath>
    </field>
</record>

<record id="product_category_tree_view_account_intrastat_expiry" model="ir.ui.view">
    <field name="name">product.category.tree.account.intrastat.expiry</field>
    <field name="model">product.category</field>
    <field name="mode">primary</field>
    <field eval="7" name="priority"/>
    <field name="arch" type="xml">
        <tree string="Product Categories" multi_edit="1" duplicate="false" editable="bottom" expand="context.get('expand', False)">
            <field name="display_name" string="Product Category" readonly="1"/>
            <field name="intrastat_id"/>
        </tree>
    </field>
</record>

</odoo>
