<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="account_intrastat_expiry_main_template" inherit_id="account_reports.main_template">
    <xpath expr="//div[hasclass('o_account_reports_body')]" position="before">
        <t t-if="options.get('warnings')">
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options['warnings'].get('expired_trans')">
                <span>
                    Some <a type="action" action="action_invalid_code_moves" t-att-data-option_key="'expired_trans'">
                    invoices</a> have expired intrastat transaction codes on their lines.
                </span>
            </div>
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options['warnings'].get('premature_trans')">
                <span>
                    Some <a type="action" action="action_invalid_code_moves" t-att-data-option_key="'premature_trans'">
                    invoices</a> have premature intrastat transaction codes on their lines.
                </span>
            </div>
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options['warnings'].get('expired_comm')">
                <span>
                    Check the expired <a type="action" action="action_invalid_code_products"
                    t-att-data-option_key="'expired_comm'">product's commodity codes</a>.
                </span>
            </div>
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options['warnings'].get('premature_comm')">
                <span>
                    Check the premature <a type="action" action="action_invalid_code_products"
                    t-att-data-option_key="'premature_comm'">product's commodity codes</a>.
                </span>
            </div>
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options['warnings'].get('expired_categ_comm')">
                <span>
                    Check the expired <a type="action" action="action_invalid_code_product_categories"
                    t-att-data-option_key="'expired_categ_comm'">product categorie's commodity codes</a>.
                </span>
            </div>
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options['warnings'].get('premature_categ_comm')">
                <span>
                    Check the premature <a type="action" action="action_invalid_code_product_categories"
                    t-att-data-option_key="'premature_categ_comm'">product categorie's commodity codes</a>.
                </span>
            </div>
        </t>
    </xpath>
</template>

</odoo>
