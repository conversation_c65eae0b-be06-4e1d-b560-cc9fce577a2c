<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_move_form_inherit_ocr" model="ir.ui.view">
        <field name="name">invoice.move.form.inherit.ocr</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="retry_ocr" class="oe_highlight" string="Send For Digitalization" type="object"
                attrs="{'invisible': [('extract_can_show_send_button', '=', False)]}" data-hotkey="x" />
            </xpath>
            <xpath expr="//header" position='after'>
                <field name="extract_state" attrs="{'invisible':True}"/>
                <field name="extract_word_ids" attrs="{'invisible':True}"/>
                <field name="extract_can_show_resend_button" attrs="{'invisible':True}"/>
                <field name="extract_can_show_send_button" attrs="{'invisible':True}"/>
                <div role="status" class="o_success_ocr"
                     attrs="{'invisible':['|', '|',
                            ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['waiting_validation', 'validation_to_send'])]}" >
                    <!-- Extraction processed with success. Don't forget to validate the different fields. -->
                </div>
                <div role="status" class="alert alert-danger mb8"
                     attrs="{'invisible':['|', '|',
                            ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['not_enough_credit'])]}">
                    <button string="Buy credits" type="object" name="buy_credits" class="btn btn-sm btn-primary pull-right mb0"/>
                    <button name="retry_ocr" class="btn btn-sm btn-primary pull-right mb0 mr-1" string="Resend For Digitalization" type="object"
                    attrs="{'invisible': [('extract_can_show_resend_button', '=', False)]}" data-hotkey="v"/>
                    You don't have enough credit to extract data from your invoice.
                    <div class="clearfix"/>
                </div>
                <div role="status" class="alert alert-info mb8" name="waiting_extraction"
                     attrs="{'invisible':['|', '|',
                            ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['waiting_extraction'])]}">
                    <button string="Refresh" type="object" name="check_status" class="btn btn-sm btn-primary pull-right mb0"/>
                    All fields will be automated by Artificial Intelligence, it might take 5 seconds.
                    <div class="clearfix"/>
                </div>
                <field name="extract_status_code" invisible="1" options="{'format': false}"/>
                <div role="status" class="alert alert-info mb8" name="extraction_status"
                     attrs="{'invisible':['|', '|',
                            ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['extract_not_ready'])]}">
                    <button string="Refresh" type="object" name="check_status" class="btn btn-sm btn-primary pull-right mb0"/>
                    The data extraction is not finished yet. The extraction usually takes between 5 and 60 seconds.
                    <div class="clearfix"/>
                </div>
                <div role="status" class="alert alert-danger mb8"
                     attrs="{'invisible':['|', '|', '|',
                            ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['error_status']),
                            ('extract_status_code', '&gt;', 99)]}">
                    <button name="retry_ocr" class="btn btn-sm btn-primary pull-right mb0 mr-1" string="Resend For Digitalization" type="object" data-hotkey="v"/>
                    <field name="extract_error_message" class="oe_inline"/>
                    <div class="clearfix"/>
                </div>
                <div role="status" class="alert alert-warning mb8"
                     attrs="{'invisible':['|', '|', '|',
                            ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')),
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['error_status', 'waiting_validation']),
                            ('extract_status_code', '&lt;', 100)]}">
                    <field name="extract_error_message"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='message_ids']" position='replace'>
                <field name="message_main_attachment_id" invisible="1"/>
                <field name="message_ids" options="{'post_refresh': 'always'}"/>
            </xpath>
            <xpath expr="//form" position="attributes">
                <attribute name="js_class">account_invoice_extract_preview</attribute>
            </xpath>
            <xpath expr="//div[hasclass('o_attachment_preview')]" position="replace">
                <div class="o_attachment_preview" options="{'preview_priority_type': 'pdf'}"/>
            </xpath>
            <xpath expr="//page[@id='other_tab']//group[@name='accounting_info_group']" position="after">
                <group string="Extraction Information"
                       name="extraction_info_group"
                       attrs="{'invisible': ['|', ('move_type', 'not in', ('in_invoice', 'in_refund', 'out_invoice', 'out_refund')), ('extract_state', 'in', ('no_extract_requested', 'not_enough_credit'))]}"
                       groups="base.group_no_one">
                    <field name="extract_remote_id"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="model_account_send_for_digitalization" model="ir.actions.server">
            <field name="name">Send Bills for digitalization</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    for rec in records:
                        if rec.is_invoice():
                            action = rec.retry_ocr()
                        else:
                            raise UserError("This action is only possible for invoices")
            </field>
    </record>

</odoo>
