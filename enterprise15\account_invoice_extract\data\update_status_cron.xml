<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id='ir_cron_update_ocr_status' model='ir.cron'>
            <field name='name'>Invoice OCR: Update All Status</field>
            <field name='model_id' ref='model_account_move'/>
            <field name='state'>code</field>
            <field name='code'>model.check_all_status()</field>
            <field name='interval_number'>1</field>
            <field name='interval_type'>days</field>
            <field name='numbercall'>-1</field>
            <field name="doall" eval="False"/>
        </record>
    </data>
</odoo>
