# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_invoice_extract
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# NoaFarkash, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: hed shefer <<EMAIL>>, 2023\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_invoice_extract_words__selected_status
msgid ""
"0 for 'not selected', 1 for 'ocr selected with no user selection' and 2 for "
"'ocr selected with user selection (user may have selected the same box)"
msgstr ""
"0 עבור 'לא נבחר', 1 עבור 'זיהוי תווים אופטי OCT ללא בחירת משתמש' ו-2 עבור "
"'זיהוי תווים אופטי OCT עם בחירת משתמש (יכול להיות שהמשתמש סימן את אותה "
"בחירה)"

#. module: account_invoice_extract
#: model:mail.template,body_html:account_invoice_extract.account_invoice_extract_no_credit
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear,<br/></p>\n"
"    <p>There are no more credits on your IAP Invoice OCR account.<br/>\n"
"    You can charge your IAP Invoice OCR account in the Accounting settings page.</p>\n"
"    <p>Best regards,<br/></p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: account_invoice_extract
#: model:mail.template,name:account_invoice_extract.account_invoice_extract_no_credit
#: model:mail.template,subject:account_invoice_extract.account_invoice_extract_no_credit
msgid "Account Invoice Extract Notification"
msgstr "הודעת חילוץ פרטי חשבון וחשבונית"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid ""
"All fields will be automated by Artificial Intelligence, it might take 5 "
"seconds."
msgstr ""
"כל השדות ימולאו אוטומטית באמצעו תבינה מלאכותית, זה יכול לקחת כ-5 שניות."

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__error_status
#, python-format
msgid "An error occurred"
msgstr "התרחשה שגיאה"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_ir_attachment
msgid "Attachment"
msgstr "קובץ מצורף"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Buy credits"
msgstr "קנה אשראי"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_can_show_resend_button
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_resend_button
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_can_show_resend_button
msgid "Can show the ocr resend button"
msgstr "יכול להציג את כפתור השליחה מחדש של ה- ocr"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_can_show_send_button
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_send_button
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "יכול להציג את כפתור השליחה של ה- ocr"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__done
msgid "Completed flow"
msgstr "תהליך הושלם"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Currency"
msgstr "מטבע"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Customer"
msgstr "לקוח"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_show_ocr_option_selection__auto_send
msgid "Digitalize all bills automatically"
msgstr "תהפוך את כל חשבוניות הספק לחשבוניות דיגיטליות אוטומטית"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_show_ocr_option_selection__manual_send
msgid "Digitalize bills on demand only"
msgstr "להפוך את החשבוניות ספק לדיגיטליות לפי דרישה בלבד"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_show_ocr_option_selection__no_send
msgid "Do not digitalize bills"
msgstr "אל תהפוך את החשבוניות ספק לחשבוניות דיגיטליות"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Due Date"
msgstr "תאריך יעד"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__duplicated_vendor_ref
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__duplicated_vendor_ref
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__duplicated_vendor_ref
msgid "Duplicated vendor reference"
msgstr "הפניה כפולה לספק"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid "Enable to get only one invoice line per tax"
msgstr "אפשר לקבל רק שורת חשבונית אחת לכל מס"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_error_message
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_error_message
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_error_message
msgid "Error message"
msgstr "הודעת שגיאה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_word_ids
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_word_ids
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_word_ids
msgid "Extract Word"
msgstr "חלץ מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_state
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_state
msgid "Extract state"
msgstr "חלץ מצב"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice_extract_words
msgid "Extracted words from invoice scan"
msgstr "מילים מחולצות מסריקת חשבונית"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Extraction Information"
msgstr "מידע שחולץ"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__field
msgid "Field"
msgstr "שדה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_remote_id
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_remote_id
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_remote_id
msgid "Id of the request to IAP-OCR"
msgstr "מזהה הבקשה ל- IAP-OCR"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice"
msgstr "חשבונית מס"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_update_ocr_status_ir_actions_server
#: model:ir.cron,cron_name:account_invoice_extract.ir_cron_update_ocr_status
#: model:ir.cron,name:account_invoice_extract.ir_cron_update_ocr_status
msgid "Invoice OCR: Update All Status"
msgstr "זיהוי תווים אופטי בחשבונית: עדכון כל הסטטוס"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_bank_statement_line__extract_remote_id
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__extract_remote_id
#: model:ir.model.fields,help:account_invoice_extract.field_account_payment__extract_remote_id
msgid "Invoice extract id"
msgstr "מזהה חילוץ חשבונית"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__selected_status
msgid "Invoice extract selected status."
msgstr "סטטוס שנבחר לחילוץ חשבונית."

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice id"
msgstr "מזהה חשבונית"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "No document name provided"
msgstr "לא צוין שם מסמך"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__no_extract_requested
msgid "No extract requested"
msgstr "לא התבקש חילוץ"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__not_enough_credit
msgid "Not enough credit"
msgstr "אין מספיק אשראי"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_single_line_per_tax
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_single_line_per_tax
msgid "OCR Single Invoice Line Per Tax"
msgstr "OCR שורת חשבונית אחת לכל מס"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_show_ocr_option_selection
msgid "Processing Option"
msgstr "אפשרות לעיבוד"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Reference"
msgstr "מזהה"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Refresh"
msgstr "רענן"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Resend For Digitalization"
msgstr "שליחה חוזרת לדיגיטציה "

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.model_account_send_for_digitalization
msgid "Send Bills for digitalization"
msgstr "שלח חשבוניות ספק לדיגיטציה"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Send For Digitalization"
msgstr "שליחה לדיגיטציה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_show_ocr_option_selection
msgid "Send mode on invoices attachments"
msgstr "שלח מצב בקבצים מצורפים של חשבוניות"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "Server is currently under maintenance. Please retry later"
msgstr "עבודות תחזוקה בשרת. לא לנסות שוב מאוחר יותר"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "Server not available. Please retry later"
msgstr "שרת לא זמין. נא לנסות שוב מאוחר יותר"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_status_code
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_status_code
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_payment__extract_status_code
msgid "Status code"
msgstr "קוד סטטוס"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid ""
"The 'invoice_ocr' IAP account token is invalid. Please delete it to let Odoo"
" generate a new one or fill it with a valid token."
msgstr ""

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid ""
"The OCR module is not able to generate the invoice lines because the default"
" accounts are not correctly set on the %s journal."
msgstr ""
"מודול זיהוי התווים האופטי (OCR) לא הצליח ליצור שורת חשבונית בכלל שחשבונות "
"ברירת המחדל לא מוגדרים היטב ביומן %s."

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 60 seconds."
msgstr "חילוץ הנתונים עדיין לא הסתיים. החילוץ נמשך בדרך כלל בין 5 ל 60 שניות."

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "The document could not be found"
msgstr "המסמך לא נמצא"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "Unsupported image format"
msgstr "פורמט הדימוי לא נתמך"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__user_selected
msgid "User Selected"
msgstr "המשתמש שנבחר"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "VAT"
msgstr "מע\"מ"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Vendor"
msgstr "ספק"

#. module: account_invoice_extract
#. openerp-web
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_fields.js:0
#, python-format
msgid "Vendor Reference"
msgstr "מספר חשבונית ספק"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr "ממתין לחילוץ"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "ממתין לאישור"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid ""
"Warning: as the bill date is prior to the lock date, the accounting date was"
" set for the first following day"
msgstr ""

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "Warning: there is already a vendor bill with this reference (%s)"
msgstr "אזהרה: כבר יש חשבונית ספק עם אסמכתא זו (%s)"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_angle
msgid "Word Box Angle"
msgstr "זווית תיבת מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_height
msgid "Word Box Height"
msgstr "גובה תיבת מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midX
msgid "Word Box Midx"
msgstr "X אמצע תיבת מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midY
msgid "Word Box Midy"
msgstr "Y אמצע תיבת מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_width
msgid "Word Box Width"
msgstr "רוחב תיבת מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_page
msgid "Word Page"
msgstr "עמוד מילה"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_text
msgid "Word Text"
msgstr "טקסט מילה"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "You don't have enough credit to extract data from your invoice."
msgstr "אין לך מספיק קרדיט כדי לחלץ מידע מהחשבונית שלך."

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid "You must send the same quantity of documents and file names"
msgstr "יש לשלוח את אותה כמות של מסמכים ושל שמות קבצים"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid ""
"Your PDF file is protected by a password. The OCR can't extract data from it"
msgstr "ה-PDF שלך מוגן בסיסמא. מערכת זיהוי התווים לא יכולה לחלץ מהקובץ מידע"

#. module: account_invoice_extract
#: code:addons/account_invoice_extract/models/account_invoice.py:0
#, python-format
msgid ""
"Your invoice is too heavy to be processed by the OCR. Try to reduce the "
"number of pages and avoid pages with too many text"
msgstr ""
"החשבונית כבדה מדי לעיבוד של מזהה התווים האופטי (OCR). נסו להקטין את מספר "
"הדפים והמנעו מדפים עם יותר מדי טקסט"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__account_move__extract_state__extract_not_ready
msgid "waiting extraction, but it is not ready"
msgstr "מחכה לחילוץ, אבל זה לא מוכן"
