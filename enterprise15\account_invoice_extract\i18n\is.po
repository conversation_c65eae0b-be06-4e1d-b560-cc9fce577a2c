# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_invoice_extract
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-25 12:42+0000\n"
"PO-Revision-Date: 2018-08-24 11:36+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_invoice_extract_words__selected_status
msgid ""
"0 for 'not selected', 1 for ocr choosed and 2 for ocr selected but not "
"choosed by user"
msgstr ""

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "An error occured"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid "An error occured during the ocr process."
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid "Autocomplete Vendor Bills (OCR + AI)"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_show_ocr_option_selection
msgid "Automatic Bills Processing"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid "Buy credits"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice__extract_can_show_resend_button
msgid "Can show the ocr resend button"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_company
msgid "Companies"
msgstr "Fyrirtæki"

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "Completed flow"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: account_invoice_extract
#: selection:res.company,extract_show_ocr_option_selection:0
msgid "Do not process bills"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice__extract_word_ids
msgid "Extract Word"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice__extract_state
msgid "Extract state"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice_extract_words
msgid "Extracted words from invoice scan"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__field
msgid "Field"
msgstr "Field"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__id
msgid "ID"
msgstr "Auðkenni"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice__extract_remoteid
msgid "Id of the request to IAP-OCR"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice"
msgstr "Reikningur"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_invoice__extract_remoteid
msgid "Invoice extract id"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__selected_status
msgid "Invoice extract selected status."
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice id"
msgstr "Invoice id"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "No extract requested"
msgstr ""

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "Not enough credit"
msgstr ""

#. module: account_invoice_extract
#: selection:res.company,extract_show_ocr_option_selection:0
msgid "Process all bills automatically"
msgstr ""

#. module: account_invoice_extract
#: selection:res.company,extract_show_ocr_option_selection:0
msgid "Process bills on demand only"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid "Resend File through OCR"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid "Send File through OCR"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_show_ocr_option_selection
msgid "Send mode on invoices attachments"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 60 seconds."
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid ""
"The data extraction started. It takes usually between 5 and 60 seconds."
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid "Update status"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__user_selected
msgid "User Selected"
msgstr ""

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "Waiting extraction"
msgstr ""

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "Waiting validation"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid ""
"When receiving an email with a bill, or uploading scanned bills, Odoo will "
"parse them (OCR) and auto-complete (AI) Draft Bills to validate."
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_angle
msgid "Word Box Angle"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_height
msgid "Word Box Height"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midX
msgid "Word Box Midx"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midY
msgid "Word Box Midy"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_width
msgid "Word Box Width"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_page
msgid "Word Page"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_text
msgid "Word Text"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.invoice_supplier_form_inherit_ocr
msgid ""
"You don't have enough credit to extract datas from your invoice. You can buy"
" new credits here:"
msgstr ""

#. module: account_invoice_extract
#: selection:account.invoice,extract_state:0
msgid "waiting extraction, but it is not ready"
msgstr ""
