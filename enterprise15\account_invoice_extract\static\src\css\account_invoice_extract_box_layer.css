
  .boxLayer {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    opacity: 0.8;
  }

  .boxLayer > .o_invoice_extract_box {
    color: transparent;
    position: absolute;
    white-space: pre;
    cursor: pointer;
    border-radius: 3px;
    border: 3px solid rgba(100,100,255,0.7);
    -webkit-transform-origin: center center;
    -moz-transform-origin: center center;
    -o-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
  }

  .boxLayer > .o_invoice_extract_box.ocr_chosen:not(.selected) {
    background-color: rgba(255,100,0,0.6);
  }

  .boxLayer > .o_invoice_extract_box.selected {
    background-color: rgba(100,255,100,0.6);
  }

  .boxLayer > .o_invoice_extract_box.o_hidden {
    display: none;
  }

  .boxLayer .highlight {
    margin: -1px;
    padding: 1px;

    background-color: rgb(180, 0, 170);
    border-radius: 4px;
  }

  .boxLayer .highlight.begin {
    border-radius: 4px 0px 0px 4px;
  }

  .boxLayer .highlight.end {
    border-radius: 0px 4px 4px 0px;
  }

  .boxLayer .highlight.middle {
    border-radius: 0px;
  }

  .boxLayer .highlight.selected {
    background-color: rgb(0, 100, 0);
  }

  .boxLayer ::selection { background: rgb(0,0,255); }
  .boxLayer ::-moz-selection { background: rgb(0,0,255); }
