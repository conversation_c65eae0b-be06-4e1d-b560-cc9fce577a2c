/** @odoo-module **/

/**
 * This subview adds features related to OCR on attachments. An attachment
 * in the chatter can be sent to the OCR, which will add boxes per fields on
 * the attachment. Visually, the attachment preview displays boxes that are
 * generated by the OCR, and clicking on the boxes updates the form accordingly.
 * @see account_invoice_extract.FormRenderer
 */
import InvoiceExtractFormRenderer from '@account_invoice_extract/js/invoice_extract_form_renderer';

import FormView from 'web.FormView';
import view_registry from 'web.view_registry';

var InvoiceExtractFormView = FormView.extend({
    config: _.extend({}, FormView.prototype.config, {
        Renderer: InvoiceExtractFormRenderer,
    }),
});

view_registry.add('account_invoice_extract_preview', InvoiceExtractFormView);

export default InvoiceExtractFormView;
