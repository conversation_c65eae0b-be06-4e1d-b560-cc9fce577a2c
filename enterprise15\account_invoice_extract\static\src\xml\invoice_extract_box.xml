<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!--
        @param {account_invoice_extract.Box} widget
    -->
    <t t-name="account_invoice_extract.Box">
        <div
            t-attf-class="o_invoice_extract_box #{widget.isOcrChosen() ? 'ocr_chosen' : ''} #{widget.isSelected() ? 'selected' : ''}"
            t-att-data-id="widget.getID()"
            t-att-data-field-name="widget.getFieldName()"
            t-att-style="widget.getStyle()"
        />
    </t>

</templates>
