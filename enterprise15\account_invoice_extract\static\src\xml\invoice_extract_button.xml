<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!--
        @param {account_invoice_extract.Button} widget
    -->
    <t t-name="account_invoice_extract.Button">
        <button
            t-attf-class="o_invoice_extract_button btn btn-link ml4 #{widget.isActive() ? 'active' : ''}"
            t-att-data-field-name="widget.getFieldName()"
            type="button"
        >
            <t t-esc="widget.getText()"/>
        </button>
    </t>

</templates>
