<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="config_online_sync_proxy_mode" model="ir.config_parameter">
            <field name="key">account_online_synchronization.proxy_mode</field>
            <field name="value">production</field>
        </record>
        <record forcecreate="True" id="config_online_sync_request_timeout" model="ir.config_parameter">
            <field name="key">account_online_synchronization.request_timeout</field>
            <field name="value">60</field>
        </record>
    </data>
</odoo>
