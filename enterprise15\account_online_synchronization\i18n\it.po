# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_online_synchronization
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <g<PERSON><PERSON><PERSON>@perteghella.org>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid " If you've already opened this issue don't report it again."
msgstr "Se hai già segnalato il problema non farlo di nuovo."

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"If you've already opened a ticket for this issue, don't report it again: a "
"support agent will contact you shortly."
msgstr ""
"Se hai già aperto un ticket per il problema non farlo di nuovo: un operatore"
" ti contatterà a breve."

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Check the documentation."
msgstr "Consulta la documentazione."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "<strong>Fantastic! </strong> We've found"
msgstr "<strong>Fantastico! </strong> Trovati"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__access_token
msgid "Access Token"
msgstr "Token di accesso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_data
msgid "Account Data"
msgstr "Dati conto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__name
msgid "Account Name"
msgstr "Nome conto"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_link_journal_line__name
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__name
msgid "Account Name as provided by third party provider"
msgstr "Nome del conto come assegnato dal fornitore di terze parti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__account_number
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_number
msgid "Account Number"
msgstr "Numero conto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__account_online_account_ids
msgid "Account Online Account"
msgstr "Conto online per i conti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_online_link_id
msgid "Account Online Link"
msgstr "Collegamento online per conti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__account_online_wizard_id
msgid "Account Online Wizard"
msgstr "Procedura online per i conti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__name
msgid "Account name"
msgstr "Nome conto"

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_ir_actions_server
#: model:ir.cron,cron_name:account_online_synchronization.online_sync_cron
#: model:ir.cron,name:account_online_synchronization.online_sync_cron
msgid "Account: Journal online sync"
msgstr "Conto: sincronizzazione online dei registri"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__action
msgid "Action"
msgstr "Azione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Automated Bank Synchronization"
msgstr "Sincronizzazione automatica della banca"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__auto_sync
msgid "Automatic synchronization"
msgstr "Sincronizzazione automatica"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__balance
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__balance
msgid "Balance"
msgstr "Saldo"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_link_journal_line__balance
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__balance
msgid "Balance of the account sent by the third party provider"
msgstr "Saldo del conto inviato dal fornitore di terze parti"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement
msgid "Bank Statement"
msgstr "Estratto conto bancario"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Riga estratto conto bancario"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "Cancel"
msgstr "Annulla"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__client_id
msgid "Client"
msgstr "Cliente"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Client id"
msgstr "ID cliente"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__company_id
msgid "Company"
msgstr "Azienda"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Connect"
msgstr "Connetti"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__connected
msgid "Connected"
msgstr "Connesso"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Create a Bank Account"
msgstr "Crea un conto bancario"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create bi-monthly statements"
msgstr "Creazione estratti conto bimestrali"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create daily statements"
msgstr "Creazione estratti conto gornalieri"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create monthly statements"
msgstr "Creazione estratti conto mensili"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_link_journal_line__action__create
msgid "Create new journal"
msgstr "Crea nuovo registro"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create one statement per synchronization"
msgstr "Creazione di un estratto conto per sincronizzazione"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create weekly statements"
msgstr "Creazione estratti conto settimanali"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__bank_statement_creation_groupby
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__bank_statement_creation_groupby
msgid "Creation of Bank Statements"
msgstr "Creazione estratti conto bancari"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_bank_statement_import_journal_creation__bank_statement_creation_groupby
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__bank_statement_creation_groupby
msgid ""
"Defines when a new bank statement will be created when fetching new "
"transactions from your bank account."
msgstr ""
"Indica quando verrà creato un nuovo estratto conto durante la fase di "
"recupero di nuove operazioni dal conto bancario."

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__error
msgid "Error"
msgstr "Errore"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_data
msgid "Extra information needed by third party provider"
msgstr "Informazioni aggiuntive necessarie per il fornitore di terze parti"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Accounts"
msgstr "Recupera conti"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Transactions"
msgstr "Recupera operazioni"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__sync_date
msgid "Get transactions since"
msgstr "Acquisire operazioni dal"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Here"
msgstr "Qui"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__id
msgid "ID"
msgstr "ID"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__online_identifier
msgid "Id used to identify account by third party provider"
msgstr ""
"ID utilizzato per identificare il conto di un fornitore di terze parti"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__auto_sync
msgid ""
"If possible, we will try to automatically fetch new transactions for this "
"record"
msgstr ""
"Se possibile, verrà tentato un recupero automatico delle nuove operazioni "
"per questo record"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_data
msgid "Information needed to interract with third party provider"
msgstr ""
"Informazioni necessarie per interagire con il fornitore di terze parti"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__name
msgid "Institution Name"
msgstr "Nome istituto"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Internal Error"
msgstr "Errore interno"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Invalid value for proxy_mode config parameter."
msgstr "Valore non valido per il parametro di configurazione proxy_mode."

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_journal
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__journal_ids
msgid "Journal"
msgstr "Registro"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/wizard/account_link_journal_wizard.py:0
#, python-format
msgid "Journals linked to a bank account must be of the bank type."
msgstr "I diari collegati a un conto bancario devono essere di tipo bancario."

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal____last_update
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line____last_update
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account____last_update
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__last_refresh
msgid "Last Refresh"
msgstr "Ultimo aggiornamento"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Last refresh"
msgstr "Ultimo aggiornamento"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__last_sync
msgid "Last synchronization"
msgstr "Ultima sincronizzazione"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Link Account to Journal"
msgstr "Collega conto a registro"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "Link account to journal"
msgstr "Collega conto a registro"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_link_journal
msgid "Link list of bank accounts to journals"
msgstr "Collegamento elenchi dei conti bancari ai registri"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_link_journal_line
msgid "Link one bank account to a journal"
msgstr "Collegamento di un conto bancario a un registro"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_link_journal_line__action__link
msgid "Link to existing journal"
msgstr "Collega a registro esistente"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Message"
msgstr "Messaggio"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__name
msgid "Name"
msgstr "Nome"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Next sync:"
msgstr "Prossima sincronizzazione:"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__next_refresh
msgid "Next synchronization"
msgstr "Prossima sincronizzazione"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__disconnected
msgid "Not Connected"
msgstr "Non connesso"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__number_added
msgid "Number Added"
msgstr "Numero aggiunto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_unread_counter
msgid "Number of unread messages"
msgstr "Numero di messaggi non letti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__online_account_id
msgid "Online Account"
msgstr "Conto online"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Online Accounts"
msgstr "Conti online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__online_identifier
msgid "Online Identifier"
msgstr "Identificativo online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__next_link_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__next_link_synchronization
msgid "Online Link Next synchronization"
msgstr "Prossima sincronizzazione collegamento online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_partner__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_users__online_partner_information
msgid "Online Partner Information"
msgstr "Informazioni partner online"

#. module: account_online_synchronization
#: model:ir.actions.act_window,name:account_online_synchronization.action_account_online_link_form
#: model:ir.ui.menu,name:account_online_synchronization.menu_action_online_link_account
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Online Synchronization"
msgstr "Sincronizzazione online"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_transaction_identifier
msgid "Online Transaction Identifier"
msgstr "Identificativo di operazioni online"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
#, python-format
msgid "Opening statement: first synchronization"
msgstr "Estratto conto di apertura: prima sincronizzazione"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Please reconnect your online account."
msgstr "Connettere nuovamente il conto online."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Problem during synchronization"
msgstr "Problema durante la sincronizzazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_data
msgid "Provider Data"
msgstr "Dati fornitore"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reconnect"
msgstr "Riconnetti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__refresh_token
msgid "Refresh Token"
msgstr "Token di aggiornamento"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Report issue"
msgstr "Segnala problema"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__client_id
msgid "Represent a link for a given user towards a banking institution"
msgstr ""
"Rappresenta un collegamento, per un determinato utente, verso un istituto "
"bancario"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_link_journal_line__account_number
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_number
msgid "Set if third party provider has the full account number"
msgstr ""
"Impostato se il fornitore di terze parti possiede il numero completo del "
"conto"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__state
msgid "State"
msgstr "Stato"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_form_inherit_online_sync
msgid "Synchronization Frequency"
msgstr "Frequenza di sincronizzazione"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__journal_statements_creation
msgid "Synchronization frequency"
msgstr "Frequenza di sincronizzazione"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Synchronization link disconnected"
msgstr "Collegamento di sincronizzazione disconnesso"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "Synchronize now"
msgstr "Sincronizza ora"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__account_ids
msgid "Synchronized accounts"
msgstr "Conti sincronizzati"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"The online synchronization service is not available at the moment. Please "
"try again later."
msgstr ""
"Il servizio di sincronizzazione online non è attualmente disponibile. "
"Riprovare più tardi."

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"The reference of your error is %s. Please mention it when contacting Odoo "
"support."
msgstr ""
"Errore con riferimento %s, menzionarlo quando viene contattato il supporto "
"Odoo."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "There is no new bank account to link."
msgstr "Nessun nuovo conto bancario da collegare."

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"This version of Odoo appears to be outdated and does not support the '%s' "
"sync mode. Installing the latest update might solve this."
msgstr ""
"Questa versione di Odoo sembra essere obsoleta e non supporta la modalità di"
" sincronizzazione \"%s\". L'installazione dell'aggiornamento più recente "
"potrebbe risolvere il problema."

#. module: account_online_synchronization
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid ""
"To create a synchronization with your banking institution,<br>\n"
"                please click on <b>Add a Bank Account</b> in <b>Configuration</b> menu."
msgstr ""
"Per creare una sincronizzazione con l'istituto bancario,<br>\n"
"                fai clic su <b>Aggiungi un conto bancario</b> nel menù di <b>Configurazione</b>."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__access_token
msgid "Token used to access API."
msgstr "Token usato per accedere alle API."

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__refresh_token
msgid "Token used to sign API request, Never disclose it"
msgstr ""
"Token utilizzato per firmare la richiesta API, non deve mai essere divulgato"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__transactions
msgid "Transactions"
msgstr "Operazioni"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_unread
msgid "Unread Messages"
msgstr "Messaggi non letti"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Numero messaggi non letti"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Update Credentials"
msgstr "Aggiorna credenziali"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "View problem"
msgstr "Visualizza problema"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "You can contact Odoo support"
msgstr "Puoi contattare l'assistenza Odoo"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/wizard/account_link_journal_wizard.py:0
#, python-format
msgid "You can not link two accounts to the same journal."
msgstr "Impossibile collegare due conti allo stesso registro."

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "You cannot have two journals associated with the same Online Account."
msgstr "Impossibile avere due registri associati allo stesso account online."

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/wizard/account_link_journal_wizard.py:0
#, python-format
msgid ""
"You must select or create a journal for each account you want to "
"synchronize."
msgstr ""
"Deve essere selezionato o creato un registro per ciascun conto da "
"sincronizzare."

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid ""
"bank account(s).<br/>\n"
"                            Let's associate each one with an accounting journal."
msgstr ""
"conti bancari.<br/>\n"
"                            Associare ciascuno di essi a un registro contabile."

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_link
msgid "connection to an online banking institution"
msgstr "connessione a un istituto bancario online"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_account
msgid "representation of an online bank account"
msgstr "Rappresentazione di un conto bancario online"
