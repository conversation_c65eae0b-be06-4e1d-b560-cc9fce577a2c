# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_online_synchronization
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid " If you've already opened this issue don't report it again."
msgstr "หากคุณได้เปิดปัญหานี้แล้ว ไม่จำเป็นต้องรายงานอีกครั้ง"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"If you've already opened a ticket for this issue, don't report it again: a "
"support agent will contact you shortly."
msgstr ""
"หากคุณได้เปิดทิกเก็ตสำหรับปัญหานี้แล้ว ไม่ต้องรายงานอีก "
"ฝ่ายช่วยเหลือจะติดต่อคุณในไม่ช้า"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Check the documentation."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "<strong>Fantastic! </strong> We've found"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__access_token
msgid "Access Token"
msgstr "เข้าถึง"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_data
msgid "Account Data"
msgstr "ข้อมูลบัญชี"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__name
msgid "Account Name"
msgstr "ชื่อบัญชี"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_link_journal_line__name
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__name
msgid "Account Name as provided by third party provider"
msgstr "ชื่อบัญชีตามที่ผู้ให้บริการบุคคลที่สามให้ไว้"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__account_number
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_number
msgid "Account Number"
msgstr "หมายเลขบัญชี"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__account_online_account_ids
msgid "Account Online Account"
msgstr "บัญชี บัญชีออนไลน์"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__account_online_link_id
msgid "Account Online Link"
msgstr "ลิงค์บัญชีออนไลน์"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__account_online_wizard_id
msgid "Account Online Wizard"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__name
msgid "Account name"
msgstr ""

#. module: account_online_synchronization
#: model:ir.actions.server,name:account_online_synchronization.online_sync_cron_ir_actions_server
#: model:ir.cron,cron_name:account_online_synchronization.online_sync_cron
#: model:ir.cron,name:account_online_synchronization.online_sync_cron
msgid "Account: Journal online sync"
msgstr "บัญชี: การซิงค์สมุดรายวันออนไลน์"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__action
msgid "Action"
msgstr "การดำเนินการ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Automated Bank Synchronization"
msgstr "การซิงโครไนซ์ธนาคารอัตโนมัติ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__auto_sync
msgid "Automatic synchronization"
msgstr "การซิงโครไนซ์อัตโนมัติ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__balance
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__balance
msgid "Balance"
msgstr "ยอดเงินคงเหลือ"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_link_journal_line__balance
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__balance
msgid "Balance of the account sent by the third party provider"
msgstr "ยอดคงเหลือในบัญชีที่ส่งโดยผู้ให้บริการบุคคลที่สาม"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement
msgid "Bank Statement"
msgstr "ใบแจ้งยอดจากธนาคาร"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "รายการใบแจ้งยอดจากธนาคาร"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "Cancel"
msgstr "ยกเลิก"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__client_id
msgid "Client"
msgstr "ไคลเอนต์"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Client id"
msgstr "รหัสลูกค้า"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__company_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__company_id
msgid "Company"
msgstr "บริษัท"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Connect"
msgstr "เชื่อมต่อ"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__connected
msgid "Connected"
msgstr "เชื่อมต่อ"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Create a Bank Account"
msgstr "บัญชีธนาคาร"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create bi-monthly statements"
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create daily statements"
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create monthly statements"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_link_journal_line__action__create
msgid "Create new journal"
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create one statement per synchronization"
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_journal.py:0
#, python-format
msgid "Create weekly statements"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__create_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__bank_statement_creation_groupby
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__bank_statement_creation_groupby
msgid "Creation of Bank Statements"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_bank_statement_import_journal_creation__bank_statement_creation_groupby
#: model:ir.model.fields,help:account_online_synchronization.field_account_journal__bank_statement_creation_groupby
msgid ""
"Defines when a new bank statement will be created when fetching new "
"transactions from your bank account."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__display_name
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__error
msgid "Error"
msgstr "ผิดพลาด"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_data
msgid "Extra information needed by third party provider"
msgstr "ข้อมูลเพิ่มเติมที่จำเป็นโดยผู้ให้บริการบุคคลที่สาม"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Accounts"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Fetch Transactions"
msgstr "ดึงข้อมูลการทำธุรกรรม"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (คู่ค้า)"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__sync_date
msgid "Get transactions since"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Here"
msgstr "ที่นี่"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__id
msgid "ID"
msgstr "รหัส"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__online_identifier
msgid "Id used to identify account by third party provider"
msgstr "รหัสที่ใช้ในการระบุบัญชีโดยผู้ให้บริการบุคคลที่สาม"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__auto_sync
msgid ""
"If possible, we will try to automatically fetch new transactions for this "
"record"
msgstr ""
"หากเป็นไปได้ เราจะพยายามดึงข้อมูลธุรกรรมใหม่สำหรับบันทึกนี้โดยอัตโนมัติ"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__provider_data
msgid "Information needed to interract with third party provider"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__name
msgid "Institution Name"
msgstr "ชื่อสถาบัน"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Internal Error"
msgstr "ข้อผิดพลาดภายใน"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Invalid value for proxy_mode config parameter."
msgstr "ค่าไม่ถูกต้องสำหรับพารามิเตอร์การกำหนดค่า proxy_mode"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_journal
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__journal_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__journal_ids
msgid "Journal"
msgstr "สมุดรายวัน"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/wizard/account_link_journal_wizard.py:0
#, python-format
msgid "Journals linked to a bank account must be of the bank type."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal____last_update
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line____last_update
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account____last_update
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__last_refresh
msgid "Last Refresh"
msgstr "รีเฟรชครั้งล่าสุด"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_uid
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__write_date
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Last refresh"
msgstr "รีเฟรชครั้งล่าสุด"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__last_sync
msgid "Last synchronization"
msgstr "การซิงโครไนซ์ครั้งล่าสุด"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Link Account to Journal"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "Link account to journal"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_link_journal
msgid "Link list of bank accounts to journals"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_link_journal_line
msgid "Link one bank account to a journal"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_link_journal_line__action__link
msgid "Link to existing journal"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Message"
msgstr "ข้อความ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__name
msgid "Name"
msgstr "ชื่อ"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Next sync:"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__next_refresh
msgid "Next synchronization"
msgstr "การซิงโครไนซ์ครั้งต่อไป"

#. module: account_online_synchronization
#: model:ir.model.fields.selection,name:account_online_synchronization.selection__account_online_link__state__disconnected
msgid "Not Connected"
msgstr "ไม่ได้เชื่อมต่อ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__number_added
msgid "Number Added"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_account_id
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__online_account_id
msgid "Online Account"
msgstr "บัญชีออนไลน์"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Online Accounts"
msgstr "บัญชีออนไลน์"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_account__online_identifier
msgid "Online Identifier"
msgstr "ตัวระบุออนไลน์"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__next_link_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__next_link_synchronization
msgid "Online Link Next synchronization"
msgstr "ลิงค์ออนไลน์ การซิงโครไนซ์ถัดไป"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_partner__online_partner_information
#: model:ir.model.fields,field_description:account_online_synchronization.field_res_users__online_partner_information
msgid "Online Partner Information"
msgstr "ข้อมูลพาร์ทเนอร์ออนไลน์"

#. module: account_online_synchronization
#: model:ir.actions.act_window,name:account_online_synchronization.action_account_online_link_form
#: model:ir.ui.menu,name:account_online_synchronization.menu_action_online_link_account
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Online Synchronization"
msgstr "การซิงโครไนซ์ออนไลน์"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_line__online_transaction_identifier
msgid "Online Transaction Identifier"
msgstr "ตัวระบุธุรกรรมออนไลน์"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_bank_statement.py:0
#, python-format
msgid "Opening statement: first synchronization"
msgstr "คำสั่งเปิด: การซิงโครไนซ์ครั้งแรก"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Please reconnect your online account."
msgstr "โปรดเชื่อมต่อบัญชีออนไลน์ของคุณอีกครั้ง"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Problem during synchronization"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__provider_data
msgid "Provider Data"
msgstr "ข้อมูลผู้ให้บริการ"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Reconnect"
msgstr "เชื่อมต่อใหม่"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__refresh_token
msgid "Refresh Token"
msgstr "รีเฟรชโทเคน"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "Report issue"
msgstr "รายงานปัญหา"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__client_id
msgid "Represent a link for a given user towards a banking institution"
msgstr "แสดงลิงก์สำหรับผู้ใช้ที่กำหนดไปยังสถาบันการธนาคาร"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_link_journal_line__account_number
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_account__account_number
msgid "Set if third party provider has the full account number"
msgstr "ตั้งค่าว่าผู้ให้บริการบุคคลที่สามมีหมายเลขบัญชีเต็มหรือไม่"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_bank_statement_import_journal_creation__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_journal__account_online_link_state
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__state
msgid "State"
msgstr "สถานะ"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_form_inherit_online_sync
msgid "Synchronization Frequency"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal_line__journal_statements_creation
msgid "Synchronization frequency"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "Synchronization link disconnected"
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "Synchronize now"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__account_ids
msgid "Synchronized accounts"
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"The online synchronization service is not available at the moment. Please "
"try again later."
msgstr ""
"บริการซิงโครไนซ์ออนไลน์ไม่สามารถใช้งานได้ในขณะนี้ "
"กรุณาลองใหม่อีกครั้งในภายหลัง"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"The reference of your error is %s. Please mention it when contacting Odoo "
"support."
msgstr ""
"การอ้างอิงข้อผิดพลาดของคุณคือ %s โปรดระบุเมื่อติดต่อฝ่ายช่วยเหลือของ Odoo"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid "There is no new bank account to link."
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid ""
"This version of Odoo appears to be outdated and does not support the '%s' "
"sync mode. Installing the latest update might solve this."
msgstr ""
"ดูเหมือนว่า Odoo เวอร์ชันนี้จะล้าสมัยและไม่รองรับโหมดการซิงค์ '%s' "
"การติดตั้งการอัปเดตล่าสุดอาจแก้ปัญหานี้ได้"

#. module: account_online_synchronization
#: model_terms:ir.actions.act_window,help:account_online_synchronization.action_account_online_link_form
msgid ""
"To create a synchronization with your banking institution,<br>\n"
"                please click on <b>Add a Bank Account</b> in <b>Configuration</b> menu."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__access_token
msgid "Token used to access API."
msgstr "โทเค็นที่ใช้ในการเข้าถึง API"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__refresh_token
msgid "Token used to sign API request, Never disclose it"
msgstr "โทเค็นที่ใช้ในการลงนามคำขอ API ห้ามเปิดเผยเด็ดขาด"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_link_journal__transactions
msgid "Transactions"
msgstr "ธุรกรรม"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_online_link_view_form
msgid "Update Credentials"
msgstr "อัปเดตข้อมูลประจำตัว"

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.account_journal_dashboard_inherit_online_sync
msgid "View problem"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model.fields,field_description:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: account_online_synchronization
#: model:ir.model.fields,help:account_online_synchronization.field_account_online_link__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "You can contact Odoo support"
msgstr "คุณสามารถติดต่อฝ่ายช่วยเหลือของ Odoo"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/wizard/account_link_journal_wizard.py:0
#, python-format
msgid "You can not link two accounts to the same journal."
msgstr ""

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/models/account_online.py:0
#, python-format
msgid "You cannot have two journals associated with the same Online Account."
msgstr "คุณไม่สามารถมีสมุดรายวันสองฉบับที่เชื่อมโยงกับบัญชีออนไลน์เดียวกันได้"

#. module: account_online_synchronization
#: code:addons/account_online_synchronization/wizard/account_link_journal_wizard.py:0
#, python-format
msgid ""
"You must select or create a journal for each account you want to "
"synchronize."
msgstr ""

#. module: account_online_synchronization
#: model_terms:ir.ui.view,arch_db:account_online_synchronization.view_account_link_journal_form_wizard
msgid ""
"bank account(s).<br/>\n"
"                            Let's associate each one with an accounting journal."
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_link
msgid "connection to an online banking institution"
msgstr ""

#. module: account_online_synchronization
#: model:ir.model,name:account_online_synchronization.model_account_online_account
msgid "representation of an online bank account"
msgstr "การแสดงบัญชีธนาคารออนไลน์"
