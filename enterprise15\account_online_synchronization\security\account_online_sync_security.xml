<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record model="ir.rule" id="account_online_sync_link_rule">
        <field name="name">Account online link company rule</field>
        <field name="model_id" ref="model_account_online_link"/>
        <field name="global" eval="True"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>
    <record model="ir.rule" id="account_online_sync_account_rule">
        <field name="name">Online account company rule</field>
        <field name="model_id" ref="model_account_online_account"/>
        <field name="global" eval="True"/>
        <field name="domain_force">[('account_online_link_id.company_id','in', company_ids)]</field>
    </record>
</odoo>
