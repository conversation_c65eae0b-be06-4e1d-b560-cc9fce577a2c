# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_predictive_bills
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:29+0000\n"
"PO-Revision-Date: 2019-08-26 09:34+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_predictive_bills
#: model:ir.model,name:account_predictive_bills.model_account_move
msgid "Journal Entries"
msgstr ""

#. module: account_predictive_bills
#: model:ir.model,name:account_predictive_bills.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_predictive_bills
#: model:ir.model.fields,field_description:account_predictive_bills.field_account_move_line__predict_from_name
msgid "Predict From Name"
msgstr ""

#. module: account_predictive_bills
#: model:ir.model.fields,field_description:account_predictive_bills.field_account_move_line__predict_override_default_account
msgid "Predict Override Default Account"
msgstr ""

#. module: account_predictive_bills
#: model:ir.model.fields,help:account_predictive_bills.field_account_move_line__predict_from_name
msgid ""
"Technical field used to know on which lines the prediction must be done."
msgstr ""
