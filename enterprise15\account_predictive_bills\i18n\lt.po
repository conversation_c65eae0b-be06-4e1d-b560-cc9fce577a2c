# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_predictive_bills
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_predictive_bills
#: model:ir.model,name:account_predictive_bills.model_account_move
msgid "Journal Entry"
msgstr "Žurnalo įrašas"

#. module: account_predictive_bills
#: model:ir.model,name:account_predictive_bills.model_account_move_line
msgid "Journal Item"
msgstr "Žurnalo įrašas"

#. module: account_predictive_bills
#: model:ir.model.fields,field_description:account_predictive_bills.field_account_move_line__predict_from_name
msgid "Predict From Name"
msgstr ""

#. module: account_predictive_bills
#: model:ir.model.fields,help:account_predictive_bills.field_account_move_line__predict_from_name
msgid ""
"Technical field used to know on which lines the prediction must be done."
msgstr ""
