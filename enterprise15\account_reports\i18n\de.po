# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-18 09:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and %s others"
msgstr " und %s andere"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and one other"
msgstr " und ein anderer"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid " past period(s), previously stored on the corresponding tax line."
msgstr " past period(s), previously stored on the corresponding tax line."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "\" account balance is affected by"
msgstr "“ Kontostand wird beeinflusst durch"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(+) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""
"%s für Transaktionen(+), die aus Ihrem Online-Bankkonto importiert wurden "
"(heutiges Datum) und noch nicht in Odoo abgestimmt wurden (Warten auf die "
"endgültige Abstimmung, um das richtige Konto zu finden)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(-) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""
"%s für Transaktionen(-), die aus Ihrem Online-Bankkonto importiert wurden "
"(heutiges Datum) und noch nicht in Odoo abgestimmt wurden (Warten auf die "
"endgültige Abstimmung, um das richtige Konto zu finden)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_unexplained_difference
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(+) Outstanding Receipts"
msgstr "(+) Ausstehende Eingänge"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(-) Outstanding Payments"
msgstr "(-) Ausstehende Zahlungen"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(No Group)"
msgstr "(Keine Gruppe)"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "(copy)"
msgstr "(Kopie)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Aktualisieren"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period1
msgid "1 - 30"
msgstr "1 - 30"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period2
msgid "31 - 60"
msgstr "31 - 60"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period3
msgid "61 - 90"
msgstr "61 - 90"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period4
msgid "91 - 120"
msgstr "91 - 120"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "<br/>Companies:"
msgstr "<br/>Unternehmen:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid ""
"<i class=\"fa fa-caret-right invisible\" role=\"img\" aria-"
"label=\"Unfolded\" title=\"Unfolded\"/>"
msgstr ""
"<i class=\"fa fa-caret-right invisible\" role=\"img\" aria-"
"label=\"Unfolded\" title=\"Unfolded\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "<span class=\"fa fa-bar-chart\"/> Comparison:"
msgstr "Vergleich:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                Tax Report:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"                Steuerbericht:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Fiscal Position:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"            Steuerzuordnung:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Journals:"
msgstr "<span class=\"fa fa-book\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "<span class=\"fa fa-calendar\" title=\"Dates\" role=\"img\" aria-label=\"Dates\"/>"
msgstr "<span class=\"fa fa-calendar\" title=\"Dates\" role=\"img\" aria-label=\"Dates\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Balance is good\" title=\"Balance is good\"/>"
msgstr ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Balance is good\" title=\"Balance is good\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is good\" title=\"Partner ledger is good\"/>"
msgstr ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is good\" title=\"Partner ledger is good\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Balance is bad\" title=\"Balance is bad\"/>"
msgstr ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Balance is bad\" title=\"Balance is bad\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is bad\" title=\"Partner ledger is bad\"/>"
msgstr ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is bad\" title=\"Partner ledger is bad\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Balance is normal\" title=\"Balance is normal\"/>"
msgstr ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Balance is normal\" title=\"Balance is normal\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partner ledger is normal\" title=\"Partner ledger is normal\"/>"
msgstr ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partner ledger is normal\" title=\"Partner ledger is normal\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Codes:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Codes:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Filters:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Filter:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_groupby_fields
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Group By:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Gruppieren nach:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/>Optionen:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<span class=\"fa fa-folder-open\"/> Analytic"
msgstr "<span class=\"fa fa-folder-open\"/> Kosten"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<span class=\"fa fa-folder-open\"/> Partners"
msgstr "<span class=\"fa fa-folder-open\"/> Partner"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid ""
"<span class=\"fa fa-home\"/>\n"
"                Tax Unit:"
msgstr ""
"<span class=\"fa fa-home\"/>\n"
"                Steuereinheit:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
msgid "<span class=\"fa fa-line-chart\"/> Exchange Rates"
msgstr "<span class=\"fa fa-line-chart\"/> Wechselkurse"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid ""
"<span class=\"fa fa-user\"/>\n"
"            Account:"
msgstr "<span class=\"fa fa-user\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.footnotes_template
msgid ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"
msgstr ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Country</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Steuerland</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Tax Return Periodicity</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">regelmäßige Steuerrückzahlungen</span>\n"
"                                   <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"
msgstr ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Letzter Bankauszug\">Letzter Bankauszug</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid ""
"<span>Intrastat taxes are applied on unexpected journal entries "
"(intranational or between non intrastat countries).</span>"
msgstr ""
"<span>Intrastat-Steuern werden auf unerwartete Buchungen (intranational oder"
" zwischen Nicht-Intrastat-Ländern) erhoben.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>Please note that the report may include some rounding differences "
"towards the bookings.</span>"
msgstr ""
"<span>Bitte beachten Sie, dass es im Bericht zu Rundungsdifferenzen bei den "
"Buchungen kommen kann.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid "<span>Some partners are missing a VAT number.</span>"
msgstr "<span>Bei einigen Partnern fehlt die Mehrwertsteuernummer.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>This company is part of a tax unit. You're currently not viewing the "
"whole unit. To change that, use the Tax Unit filter.</span>"
msgstr ""
"<span>Dieses Unternehmen ist Teil einer Steuereinheit. Sie sehen derzeit "
"nicht die gesamte Einheit. Um dies zu ändern, verwenden Sie den "
"Steuereinheitsfilter."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "<span>This report only displays the data of the active company.</span>"
msgstr ""
"<span>Dieser Bericht zeigt nur die Daten des aktiven Unternehmens an.</span>"

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_financial_html_report_line_code_uniq
msgid "A report line with the same code already exists."
msgstr "Eine Berichtszeile mit dem gleichen Code existiert bereits."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""
"Eine steuerliche Einheit kann nur zwischen Unternehmen gebildet werden, die "
"dieselbe Hauptwährung haben."

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "AKTIVA"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_id
#, python-format
msgid "Account"
msgstr "Konto"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_analytic_report
msgid "Account Analytic Report"
msgstr "Kostenstellenbericht"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplanvorlage"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_code
msgid "Account Code"
msgstr "Kontocode"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "Repräsentatives Feld für die Kontoanzeige"

#. module: account_reports
#: model:ir.model,name:account_reports.model_report_account_report_journal
msgid "Account Journal Report"
msgstr "Finanz-Journal Bericht"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_name
msgid "Account Name"
msgstr "Kontoname"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr "Kontoauszug"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report (HTML Line)"
msgstr "Finanzbericht (HTML Zeilen)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
msgid "Account Report (HTML)"
msgstr "Finanzbericht (HTML)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr "Finanzbericht Fußnote"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "Konto Dargestelltes Unternehmen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "Journal zur Neubewertung der Konten"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "Steuerberater/Wirtschaftsprüfer"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_accounting_report
msgid "Accounting Report Helper"
msgstr "Assistent für Buchhaltungsberichte"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Accounts"
msgstr "Konten"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Accounts to adjust"
msgstr "Abzugleichende Konten"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Accounts without a group"
msgstr "Konten ohne eine Grupppe"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__action_id
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Aktion"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Aktionen führen gegebenenfalls zu weiteren Funktionen wie zur Öffnung der "
"Kalenderansicht oder zur automatischen „Erledigt“-Markierung, wenn ein "
"Dokument hochgeladen wurde."

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Aktivität"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Aktivitätstyp"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Add a note"
msgstr "Notiz hinzufügen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Summen unter Abschnitten einfügen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_adjustment
msgid "Adjustment"
msgstr "Berichtigung"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "Berichtigungsbuchung"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance Payments received from customers"
msgstr "Vorauszahlungen von Kunden"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance payments made to suppliers"
msgstr "Vorauszahlungen an Lieferanten"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner
msgid "Aged Partner Balances"
msgstr "Partnersalden nach Fälligkeit"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.model,name:account_reports.model_account_aged_payable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
#, python-format
msgid "Aged Payable"
msgstr "Offene Verbindlichkeit"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Offene Verbindlichkeiten"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.model,name:account_reports.model_account_aged_receivable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
#, python-format
msgid "Aged Receivable"
msgstr "Offene Forderung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Offene Forderungen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "All"
msgstr "Alle"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "All Journals"
msgstr "Alle Journale"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_allocated_earnings
msgid "Allocated Earnings"
msgstr "Zugewiesene Gewinne"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__analytic
msgid "Allow analytic filters"
msgstr "Erlaube Kosten Filter"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__comparison
msgid "Allow comparison"
msgstr "Vergleich erlauben"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__show_journal_filter
msgid "Allow filtering by journals"
msgstr "Filtern nach Journalen erlauben"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__always
msgid "Always"
msgstr "Immer"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Amount"
msgstr "Betrag"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__amount_currency
#, python-format
msgid "Amount Currency"
msgstr "Währungsbetrag"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_account_id
msgid "Analytic Account"
msgstr "Kostenstelle"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Accounts:"
msgstr "Kostenstellen:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Analytic Entries"
msgstr "Kostenstellenbuchungen"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_analytic
#: model:ir.ui.menu,name:account_reports.menu_action_report_account_analytic
#, python-format
msgid "Analytic Report"
msgstr "Kostenbericht"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Kostenstellen Tag"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Tags:"
msgstr "Kostenmarken:"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
#, python-format
msgid "Annotate"
msgstr "Kommentieren"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid "Applicable Filters"
msgstr "Anwendbare Filter"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Apply"
msgstr "Anwenden"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "As of %s"
msgstr "Ab %s"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period0
msgid "As of: "
msgstr "Ab:"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#, python-format
msgid "As of: %s"
msgstr "Ab: %s"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__to_beginning_of_period
msgid "At the beginning of the period"
msgstr "Am Anfang der Periode"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Audit"
msgstr "Prüfung"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Prüfungsberichte"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Available Filters & Options"
msgstr "Mögliche Filter & Optionen"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Durchschnittliche Kreditorenlaufzeit"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Durchschnittliche Debitorenlaufzeit"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__balance
#, python-format
msgid "Balance"
msgstr "Saldo"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_2
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_2
msgid "Balance Sheet"
msgstr "Bilanz"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency_current
msgid "Balance at current rate"
msgstr "Saldo zum aktuellen Kurs"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_balance
msgid "Balance at operation rate"
msgstr "Saldo zum Betriebskurs"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance in GL"
msgstr "Saldo im Hauptbuch"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency
msgid "Balance in foreign currency"
msgstr "Saldo in Fremdwährung"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Balance of %s"
msgstr "Saldo von %s"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax advance payment account"
msgstr "Saldo des Steuervorauszahlungskontos"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (payable)"
msgstr "Saldo des Steuer-Kontokorrentkontos (Kreditoren)"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (receivable)"
msgstr "Saldo des Steuer-Kontokorrentkontos (Debitoren)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr "Bankabstimmung"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Bericht über Bankabstimmung"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Bank Reconciliation: %s"
msgstr "Kontenabgleich: %s"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Bank- und Kassakonten"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Base Amount"
msgstr "Grundbetrag"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__date_range
msgid "Based on date ranges"
msgstr "Auf Zeiträumen basierend"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid "Both"
msgstr "Beiderseitig"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Cancel"
msgstr "Abbrechen"

#. module: account_reports
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "Carryover for period %s to %s"
msgstr "Übertrag für den Zeitraum %sbis%s "

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Bargeld"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report
msgid "Cash Flow Report"
msgstr "Geldfluss-Bericht (Cash Flow)"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
#, python-format
msgid "Cash Flow Statement"
msgstr "Cashflow-Rechnung"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, beginning of period"
msgstr "Flüssige und gleichwertige Mittel, Anfang der Periode"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, closing balance"
msgstr "Flüssige und gleichwertige Mittel, Abschlusssaldo"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from financing activities"
msgstr "Cashflows aus Finanzierungen"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from investing & extraordinary activities"
msgstr "Cashflows aus Investition & außerordentlichen Aktivitäten"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from operating activities"
msgstr "Cashflows aus Geschäftstätigkeiten"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from unclassified activities"
msgstr "Cashflows aus außerordentlichen Geschäften"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash in"
msgstr "Bareinzahlung"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash out"
msgstr "Barauszahlung"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash paid for operating activities"
msgstr "Bargeld gezahlt für Geschäftstätigkeiten"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Erhaltenes Bargeld"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash received from operating activities"
msgstr "Bargeld erhalten aus Geschäftstätigkeiten"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Ausgegebenes Bargeld"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Bargeldüberschuss"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid "Change expected payment date"
msgstr "Erwartetes Zahlungsdatum ändern"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr "Kontenplanbericht"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Chart of Accounts"
msgstr "Kontenplan"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__children_ids
msgid "Children"
msgstr "Abhängige Elemente"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr "Untergeordnete Positionen"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Closing Entry"
msgstr "Abschlussbuchung"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Abschlusssaldo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__code
msgid "Code"
msgstr "Code"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Code:"
msgstr "Code:"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Communication"
msgstr "Kommunikation"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Unternehmen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"Company %s already belongs to a tax unit in %s. A company can at most be "
"part of one tax unit per country."
msgstr ""
"Das Unternehmen %s gehört bereits zu einer Steuereinheit in %s. Ein "
"Unternehmen kann nur Teil einer Steuereinheit pro Land sein."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Company Currency:"
msgstr "Unternehmenswährung:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid "Company Only"
msgstr "Nur Unternehmen"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Company Settings"
msgstr "Unternehmenseinstellungen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr "Berechnung"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Computation: %s"
msgstr "Berechnung: %s"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen "

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Configure your TAX accounts - %s"
msgstr "Konfigurieren Sie Ihre Steuerkonten - %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Konfigurieren Ihrer Steuerkonten"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cj
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cj
#, python-format
msgid "Consolidated Journals"
msgstr "Konsolidierte Journale"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_consolidated_journal
msgid "Consolidated Journals Report"
msgstr "Bericht über konsolidierte Journale"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__control_domain
msgid "Control Domain"
msgstr "Kontroll-Domain"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Control Domain:"
msgstr "Kontroll-Domain:"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Controls failed"
msgstr "Kontrollen fehlgeschlagen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr "Verbesserungen"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Umsatzkosten"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__country_id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Country"
msgstr "Land"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Country Code"
msgstr "Ländercode"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Eintrag erstellen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__credit
#, python-format
msgid "Credit"
msgstr "Haben"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_id
#, python-format
msgid "Currency"
msgstr "Währung"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_code
msgid "Currency Code"
msgstr "Währungs-Code"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Currency Rates (%s)"
msgstr "Wechselkurse (%s)"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Umlaufvermögen"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Kurzfristige Verbindlichkeiten"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr "Zugewiesener Gewinn im aktuellen Jahr"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr "Erträge des laufenden Jahres"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Nicht zugewiesener Gewinn im aktuellen Jahr"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Umlaufvermögen zu Verbindlichkeiten"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Custom"
msgstr "Benutzerdefiniert"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#, python-format
msgid "Date"
msgstr "Datum"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date :"
msgstr "Datum:"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Date cannot be empty"
msgstr "Datum darf nicht leer sein"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""
"Datum, an dem die nächste Handlung zu Forderungsposten ausgeführt werden "
"soll. Normalerweise wird dies automatisch beim Versand von "
"Zahlungserinnerungen durch den Kundenauszug gesetzt."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date:"
msgstr "Datum:"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__debit
#, python-format
msgid "Debit"
msgstr "Soll"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Verzögerungseinheiten"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr "Abschreibung"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Details per month"
msgstr "Details pro Monat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Difference"
msgstr "Differenz"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_type
msgid "Display Type"
msgstr "Anzeigetyp"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Dokumentenname"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__domain
msgid "Domain"
msgstr "Bereich"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Domain:"
msgstr "Domäne:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "Domestic"
msgstr "Inland"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Domestic country of your accounting"
msgstr "Land Ihrer Buchhaltung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Bericht über die Unveränderlichkeitsprüfung von Daten herunterladen"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_date
#, python-format
msgid "Due Date"
msgstr "Fälligkeit"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.model,name:account_reports.model_account_sales_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
#, python-format
msgid "EC Sales List"
msgstr "EG-Verkaufsliste"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "EIGENKAPITAL"

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "End Balance"
msgstr "Endsaldo"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End Date :"
msgstr "Enddatum:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Financial Year"
msgstr "Ende des letzten Geschäftsjahres"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Month"
msgstr "Ende des letzten Monats"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Quarter"
msgstr "Ende des letzten Quartals"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Error while validating the domain of line %s:\n"
"%s"
msgstr ""
"Fehler bei der Validierung der Zeilen-Domain %s:\n"
"%s"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Excess Journal Items"
msgstr "Überschüssige Buchungszeilen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude"
msgstr "Ausschließen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude From Aged Reports"
msgstr "Aus Berichten über offene Forderungen/Verbindlichkeiten ausschließen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "Rückstellungen für Währungen ausschließen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude from adjustment/provisions entries"
msgstr "Ausschluss von Anpassungs-/Rückstellungsbuchungen"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude this account from aged reports"
msgstr ""
"Dieses Konto aus Berichten über offene Forderungen/Verbindlichkeiten "
"ausschließen"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Excluded Accounts"
msgstr "Ausgeschlossene Konten"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_3
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_3
msgid "Executive Summary"
msgstr "Kurzbericht"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__expected_pay_date
msgid "Expected Date"
msgstr "Erwartetes Datum"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr "Erwartetes Zahldatum"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s"
msgstr "Das erwartete Zahlungsdatum wurde vom %s auf den %s geändert"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s for invoice %s"
msgstr ""
"Das erwartete Zahlungsdatum wurde vom %s auf den %s geändert (Rechnung %s)"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""
"Erwartetes Zahldatum, wie es manuell durch den Kundenauszug gesetzt wurde "
"(z. B. wenn Sie einen Kunden am Telefon hatten und sein Zahlungsversprechen "
"vermerken möchten)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Aufwandsrückstellungskonto"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Expense Provision for {for_cur}"
msgstr "Aufwandsrückstellung für {for_cur}"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense account"
msgstr "Auslagenkonto"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr "Aufwände"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Export"
msgstr "Exportieren"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Exportformat für Buchhaltungsberichte"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Exportieren nach"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Export-Assistent für Buchhaltungsberichte"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Filter:"
msgstr "Filter:"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid ""
"Filters that can be used to filter and group lines in this report. This uses"
" saved filters on journal items."
msgstr ""
"Filter mit denen die Einträge in diesem Bericht gefiltert und gruppiert "
"werden können. Benutzt werden gespeicherte Filter in den Journaleinträgen."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__financial_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__financial_report_id
msgid "Financial Report"
msgstr "Finanzbericht"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:account_reports.menu_account_financial_reports_tree
msgid "Financial Reports"
msgstr "Finanzberichte"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Steuerland"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__float
msgid "Float"
msgstr "Textumlauf"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__foldable
msgid "Foldable"
msgstr "Einklappbar"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Folded"
msgstr "Zusammen gefaltet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__footnotes_ids
msgid "Footnotes"
msgstr "Fußnoten"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__strict_range
msgid "Force given dates for all accounts and account types"
msgstr "Erzwinge vorgegebene Daten bei allen Konten und Kontenarten"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Fremdwährung-Berichtigungsbuchung zum %s"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Formula:"
msgstr "Formel:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__formulas
msgid "Formulas"
msgstr "Formeln"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"From %s\n"
"to  %s"
msgstr ""
"Von %s\n"
"bis %s"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_beginning
msgid "From the beginning"
msgstr "Von Anfang an"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_fiscalyear
msgid "From the beginning of the fiscal year"
msgstr "Ab Beginn des Geschäftsjahres"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "From:"
msgstr "Von:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Aufzurufende Funktion"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "General Ledger"
msgstr "Hauptbuch"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Hauptbuchbericht"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "General Report"
msgstr "Allgemeiner Bericht"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Erzeugte Dokumente"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr "Allgemeine Umsatzsteuerauswertung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Global Summary"
msgstr "Globale Zusammenfassung"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Goods"
msgstr "Güter"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Grid"
msgstr "Gitter"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Bruttogewinn"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Bruttogewinn"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Bruttomarge (Bruttogewinn/betriebliche Erträge)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__groupby
msgid "Group by"
msgstr "Gruppieren nach"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Account &gt; Tax"
msgstr "Gruppieren nach: Konten &gt; Steuern"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Tax &gt; Account"
msgstr "Gruppieren nach: Steuern &gt; Konten"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Groupby field %s is invalid on line with name '%s'"
msgstr "Groupby-Feld %s ist ungültig in Zeile mit Namen „%s“"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Groupby:"
msgstr "Groupby:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_empty
msgid "Hide If Empty"
msgstr "Ausblenden wenn Leer"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_zero
msgid "Hide If Zero"
msgstr "Unsichtbar wenn null"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy"
msgstr "Hierarchie"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy and Subtotals"
msgstr "Hierarchie und Zwischensummen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Wie oft Steuererklärungen gemacht werden müssen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impact On Grid"
msgstr "Einfluss auf Raster"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impacted Tax Grids"
msgstr "Beeinflusste Steuerraster"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include"
msgstr "einbeziehen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include Unposted Entries"
msgstr "Schließe ungebuchte Posten ein"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include in adjustment/provisions entries"
msgstr "In Anpassungs-/Rückstellungsbuchungen einbeziehen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include unposted entries"
msgstr "Schließe ungebuchte Posten ein"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Payments"
msgstr "Einschließlich nicht abgestimmter Kontoauszugszahlungen"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Receipts"
msgstr "Einschließlich nicht abgestimmter Kontoauszugseingänge"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr "Einkommen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Ertragskonto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "Ertragsrückstellungskonto"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Income Provision for {for_cur}"
msgstr "Ertragsrückstellung für {for_cur}"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Inconsistent Statements"
msgstr "Unstimmige Kontoauszüge"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "Initial Balance"
msgstr "Anfangssaldo"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Insert foot note here"
msgstr "Fußnote hier einfügen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__internal_note
msgid "Internal Note"
msgstr "Interne Mitteilung"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__green_on_positive
msgid "Is growth good when positive"
msgstr "Ist Wachstum in Ordnung wenn positiv"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "JRNL"
msgstr "JRNL"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Journal"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Groups"
msgstr "Journalgruppen"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "Journal Items"
msgstr "Buchungszeilen"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items (%s)"
msgstr "Journaleinträge (%s)"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items for Tax Audit"
msgstr "Buchungszeilen für die Steuerprüfung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal Items on the"
msgstr "Buchungszeilen in den"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Journal Name (Code)"
msgstr "Journal-Name (Code)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal items on the"
msgstr "Buchungszeilen in den"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_print_journal_menu
#: model:ir.ui.menu,name:account_reports.menu_print_journal
msgid "Journals Audit"
msgstr "Audit der Journale"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Journals:"
msgstr "Journale:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "VERBINDLICHKEITEN"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "VERBINDLICHKEITEN + EIGENKAPITAL"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Label"
msgstr "Buchungstext"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Financial Year"
msgstr "Letztes Geschäftsjahr"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Month"
msgstr "Letzter Monat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Quarter"
msgstr "Letztes Quartal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_link_last_statement
msgid "Last Statement:"
msgstr "Letzter Bericht:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__level
msgid "Level"
msgstr "Ebene"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line
msgid "Line"
msgstr "Zeile"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__line_ids
msgid "Lines"
msgstr "Zeilen"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Load more... (%s remaining)"
msgstr "Lade weiter... (%sübrig)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Hauptunternehmen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr "Hauptinfo"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""
"Hauptgesellschaft dieser Einheit; diejenige, die die Steuern meldet und "
"zahlt."

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#, python-format
msgid "Make Adjustment Entry"
msgstr "Berichtigungsbuchung vornehmen"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "Verwalten von Zusammenfassungen und Fußnoten von Berichten"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__manager_id
msgid "Manager"
msgstr "Manager"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Matching Number"
msgstr "Abgleichsnummer"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "Mitglieder dieser Einheit"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__generated_menu_id
msgid "Menu Item"
msgstr "Menü-Punkt"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Missing Journal Items"
msgstr "Fehlende Journaleinträge"

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr "Modul"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_id
msgid "Move"
msgstr "Buchungssatz"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_name
msgid "Move Name"
msgstr "Buchungsname"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_ref
msgid "Move Ref"
msgstr "Buchungsref."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_type
msgid "Move Type"
msgstr "Umzugstyp"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation
msgid "Multicurrency Revaluation Report"
msgstr "Bericht über Neubewertung mehrerer Währungen"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "Assistent für Neubewertung mehrerer Währungen"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for fiscal position %s after %s. There should be at most one. \n"
" %s"
msgstr ""
"Es gibt mehrere Entwürfe von Steuerabschlussbuchungen für Steuerposition %s nach %s. Es sollte höchstens einen geben.\n"
"%s"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %s. There should be at most one. \n"
" %s"
msgstr ""
"Es gibt mehrere Entwürfe von Steuerabschlussbuchungen für Ihre nationale Region nach %s. Es sollte höchstens einen geben.\n"
" %s"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "NET"
msgstr "Netto"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#, python-format
msgid "Name"
msgstr "Name"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "Namen für die erzeugten Dokumente."

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Name:"
msgstr "Name:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "Net Assets"
msgstr "Netto Anlage"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Nettogewinn"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Nettovermögen"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Net increase in cash and cash equivalents"
msgstr "Nettoerhöhung bei flüssigen und gleichwertigen Mitteln"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "Nettogewinnspanne (Nettogewinn/Einnahmen)"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__never
msgid "Never"
msgstr "Niemals"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__next_action_date
msgid "Next Action Date"
msgstr "Datum nächste Aktion"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "No Comparison"
msgstr "Ohne Vergleich"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__no_unit
msgid "No Unit"
msgstr "Keine Einheit"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "No VAT number associated with your company. Please define one."
msgstr ""
"Mit Ihrem Unternehmen ist keine MwSt.-Nummer verknüpft. Bitte definieren sie"
" eine."

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No adjustment needed"
msgstr "Keine Berichtigung erforderlich"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No provision needed was found."
msgstr "Es wurde keine erforderliche Rückstellung gefunden."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid "None"
msgstr "Keine"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""
"Hinweis über einen Forderungs-Journalposten, den Sie durch den Kundenauszug "
"setzen können"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Number of periods :"
msgstr "Anzahl der Perioden:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "AUSSERBILANZIELLE KONTEN"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo Warnung"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period5
msgid "Older"
msgstr "Ältere"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "Eines der gewählten Formate kann nicht in das DMS exportiert werden"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "One or more partners has no VAT Number."
msgstr "Einer oder mehrere Partner haben keine Mehrwertsteuernummer."

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"Nur Rechnungsadministratoren ist es erlaubt, die Sperrdaten zu ändern!"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Only Show Unreconciled Entries"
msgstr "Nur nicht abgestimmte Posten anzeigen"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Eröffnungssaldo des Geschäftsjahrs"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr "Betriebliche Erträge"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr "Sonstige betriebliche Erträge"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
#, python-format
msgid "Outstanding Payments/Receipts"
msgstr "Ausstehende Zahlungen/Eingänge"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_id
msgid "Parent"
msgstr "Übergeordnet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__parent_id
msgid "Parent Menu"
msgstr "Obermenü"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_path
msgid "Parent Path"
msgstr "Übergeordneter Pfad"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "Übergeordnete Berichts-ID"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Übergeordneter Assistent"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_id
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partner Categories:"
msgstr "Partner-Kategorien:"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/res_partner.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.model,name:account_reports.model_account_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
#, python-format
msgid "Partner Ledger"
msgstr "Partnerbuch"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_name
msgid "Partner Name"
msgstr "Partner Name"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr "Partnerberichte"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_trust
msgid "Partner Trust"
msgstr "Partnervertrauen"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Partners"
msgstr "Partner"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partners:"
msgstr "Partner: "

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Payable"
msgstr "Verbindlichkeit"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Payable tax amount"
msgstr "Zahlbarer Steuerbetrag"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "Verbindlichkeiten"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__payment_id
msgid "Payment"
msgstr "Zahlung"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__percents
msgid "Percents"
msgstr "Prozent"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Leistung"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Periodicity"
msgstr "Periodizität"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Periodizität im Monat"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Please specify a Group by field when using '%s' in Formulas, on line with "
"name '%s'"
msgstr ""
"Bitte geben Sie ein Feld \"Gruppieren nach\" an, wenn Sie '%s' in Formeln "
"verwenden, in der Zeile mit dem Namen '%s'."

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Plus Sachanlagen"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Plus Anlagevermögen"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Plus langfristige Verbindlichkeiten"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Position"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Posted Entries Only"
msgstr "Nur gebuchte Einträge"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Vorauszahlungen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Vorschau der Daten"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Previous Period"
msgstr "Vorherige Periode"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Nicht zugewiesene Gewinne aus früheren Jahren"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid "Print On New Page"
msgstr "Drucke auf neuer Seite"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""
"Seien Sie vorsichtig, da es für diesen Zeitraum eine Berichtigung geben "
"könnte ("

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
msgid "Profit And Loss"
msgstr "Gewinn und Verlust"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_1
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_1
msgid "Profit and Loss"
msgstr "Gewinn und Verlust"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Rentabilität"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "Vorschlag für die Steuerabschlussbuchung"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Provision for {for_cur} (1 {comp_cur} = {rate} {for_cur})"
msgstr "Rückstellung für  {for_cur} (1 {comp_cur} = {rate} {for_cur})"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Receivable"
msgstr "Forderung"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Receivable tax amount"
msgstr "Empfangener Steuerbetrag"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Forderungen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "Reconcile"
msgstr "Ausgleich"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Abstimmungsbericht"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Ref"
msgstr "Ref."

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Reference"
msgstr "Referenz"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Related Company"
msgstr "Verbundenes Unternehmen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Erinnerung"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_currency_id
msgid "Report Currency"
msgstr "Berichtswährung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Definition"
msgstr "Berichtsdefinition"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_include
msgid "Report Include"
msgstr "Berichtseinschließung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr "Berichtszeile"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Report Line Computation"
msgstr "Berechnung der Berichtszeile"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr "Berichtspositionen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_model
msgid "Report Model"
msgstr "Report-Modell"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__report_name
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Name"
msgstr "Berichtsname"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Berichtswesen"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Gewinnrücklage"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Kapitalrendite (Nettogewinn/Anlagen)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Stornodatum"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Stornierung von: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Same Period Last Year"
msgstr "Gleiche Periode letzten Jahres"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Save"
msgstr "Speichern"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid "Search account"
msgstr "Suche Konto"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_partner
msgid "Search partner"
msgstr "Partner suchen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__name
msgid "Section Name"
msgstr "Abschnittsbezeichnung"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__sequence
msgid "Sequence"
msgstr "Reihenfolge"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Services"
msgstr "Dienstleistungen"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__tax_report
msgid ""
"Set to True to automatically filter out journal items that are not tax "
"exigible."
msgstr ""
"Setzen Sie diese Option auf True, um automatisch Journalposten "
"herauszufiltern, die nicht steuerpflichtig sind."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_coa
msgid "Setup"
msgstr "Einrichtung"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Kurzfristige Liquiditätsprognose"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__show_domain
msgid "Show Domain"
msgstr "Domain anzeigen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Buchungswarnung anzeigen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "Show unfold all filter"
msgstr "Filter zum Aufklappen aller Optionen anzeigen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Some controls failed"
msgstr "Einige Kontrollen fehlgeschlagen"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"Some of your tax groups are missing information in company %s. Please "
"complete their configuration."
msgstr ""
"Bei einigen Ihrer Steuergruppen fehlen Informationen in Firma %s. Bitte "
"vervollständigen Sie deren Konfiguration."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__special_date_changer
msgid "Special Date Changer"
msgstr "Spezieller Datumsanpasser"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__control_domain
msgid ""
"Specify a control domain that will raise a warning if the report line is not"
" computed correctly."
msgstr ""
"Geben Sie einen Kontrollbereich an, der eine Warnung ausgibt, wenn die "
"Berichtszeile nicht korrekt berechnet wird."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""
"Geben Sie einen Ihren Steuerberater/Wirtschaftsprüfer an, die beim Export "
"von Berichten als Vertreter fungieren soll."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Start Date :"
msgstr "Startdatum:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Starten ab"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__summary
msgid "Summary"
msgstr "Zusammenfassung"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "TAX"
msgstr "Steuer"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Tags"
msgstr "Stichwörter"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Amount"
msgstr "Steuerbetrag"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_end_date
msgid "Tax Closing End Date"
msgstr "Enddatum des Steuerabschlusses"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Declaration"
msgstr "Steuererklärung"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Tax Grid"
msgstr "Steuerraster"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "USt-IdNr"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Paid Adjustment"
msgstr "Berichtigung der gezahlten Steuern"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Received Adjustment"
msgstr "Berichtigung der erhaltenen Steuern"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__tax_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
#, python-format
msgid "Tax Report"
msgstr "Steuerbericht"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_report_control_error
msgid "Tax Report Control Error"
msgstr "Fehler bei der Kontrolle des Steuerberichtes"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Tax Return"
msgstr "Steuererklärung"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "Steuereinheit"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "Steuereinheiten"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Steuerbericht"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return for %s%s"
msgstr "Steuererklärung für %s%s"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return from %s to %s%s"
msgstr "Steuererklärung von %s bis %s%s"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_closing_end_date
msgid ""
"Technical field used for VAT closing, containig the end date of the period "
"this entry closes."
msgstr ""
"Technisches Feld für den MwSt-Abschluss, das das Enddatum des Zeitraums "
"enthält, den diese Buchung abschließt."

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "Technisches Modell für Buchhaltungsberichte zum Herunterladen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr "Text"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "The Book balance in Odoo dated today"
msgstr "Der Buchsaldo in Odoo von heute"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The amount will be : %s"
msgstr "Der Betrag wird sich belaufen auf: %s"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The carried over balance will be : %s"
msgstr "Der übertragene Saldo beträgt: %s "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""
"Das Land, in dem diese steuerliche Einheit verwendet wird, um die "
"Steuerberichte Ihrer Unternehmen zu gruppieren."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__country_id
msgid "The country this report is intended to."
msgstr "Das Land, für das dieser Bericht bestimmt ist."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""
"Das Land, aus dem die Steuerberichte für dieses Unternehmen zu verwenden "
"sind"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"The current balance in the General Ledger %s doesn't match the balance of "
"your last bank statement %s leading to an unexplained difference of %s."
msgstr ""
"Der aktuelle Saldo im Hauptbuch %s stimmt nicht mit dem Saldo Ihres letzten "
"Kontoauszugs %s überein. Dies führt zu einer ungeklärlichen Differenz von "
"%s."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The difference will be carried over to the next period's declaration."
msgstr ""
"Die Differenz wird auf die Erklärung für den nächsten Zeitraum übertragen."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""
"Die Kennung, die bei der Einreichung eines Berichts für diese Einheit zu "
"verwenden ist."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid "The main company of a tax unit has to be part of it."
msgstr ""
"Das Hauptunternehmen einer Steuereinheit muss Teil dieser Einheit sein."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__generated_menu_id
msgid "The menu item generated for this report, or None if there isn't any."
msgstr ""
"Der Menü-Punkt, der für diesen Bericht erstellt wurde, oder „Keiner“, wenn "
"keiner vorhanden ist."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "Die Steuereinheiten, zu denen dieses Unternehmen gehört."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "There are"
msgstr "Es gibt"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "There are some"
msgstr "Es gibt einige"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Financial Year"
msgstr "Dieses Geschäftsjahr"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Month"
msgstr "Diesen Monat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Quarter"
msgstr "Dieses Quartal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr ""
"Dies ermöglicht die Position der Summen in den Finanzberichten zu wählen."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be increased by the positive amount from"
msgstr "Dieser Betrag erhöht sich um den positiven Betrag aus"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be reduced by the negative amount from"
msgstr "Dieser Betrag wird reduziert um den negativen Betrag aus"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be set to %s."
msgstr "Dieser Betrag wird auf %s festgelegt."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This period is already closed for company %s"
msgstr "Diese Periode wurde für das Unternehmen %s schon geschlossen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Today"
msgstr "Heute"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Total"
msgstr "Gesamt"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Total %s"
msgstr "Gesamt %s"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions that were entered into Odoo, but not yet reconciled (Payments "
"triggered by invoices/bills or manually)"
msgstr ""
"Transaktionen, die in Odoo eingegeben, aber noch nicht abgestimmt wurden "
"(Zahlungen, die durch Ein-/Ausgangsrechnungen oder manuell ausgelöst wurden)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(+) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by invoices/refunds or manually)"
msgstr ""
"Transaktionen(+), die in Odoo eingegeben, aber noch nicht abgestimmt wurden "
"(Zahlungen, die durch Ausgangsrechnungen/Erstattungen oder manuell ausgelöst"
" wurden)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(-) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by bills/credit notes or manually)"
msgstr ""
"Transaktionen(-), die in Odoo eingegeben, aber noch nicht abgestimmt wurden "
"(Zahlungen ausgelöst durch Eingangsrechnungen/Gutschriften oder manuell)"

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
#, python-format
msgid "Trial Balance"
msgstr "Rohbilanz"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Triangular"
msgstr "Dreieckig"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__figure_type
msgid "Type"
msgstr "Typ"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Nicht zugewiesene Gewinne"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Undefined"
msgstr "Undefiniert"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Unexplained Difference"
msgstr "unerklärte Differenz"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold"
msgstr "Öffnen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold All"
msgstr "Alle ausklappen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Unfolded"
msgstr "ausgeklappt"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Unbekannter Partner"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
#, python-format
msgid "Unrealized Currency Gains/Losses"
msgstr "Nicht realisierte Währungsgewinne/-verluste"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unreconciled"
msgstr "Offene Posten"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__normal
msgid ""
"Use the dates that should normally be used, depending on the account types"
msgstr ""
"Verwenden Sie die Daten, die normalerweise verwendet werden sollten, je nach"
" Kontotyp"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "VAT"
msgstr "USt.-ID"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_company_form
msgid "VAT Units"
msgstr "MwSt.-Einheiten"

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Vat closing from %s to %s"
msgstr "MWSt abgeschlossen vom %s bis %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bank Statement"
msgstr "Kontoauszug ansehen"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Carryover Lines"
msgstr "Übertragszeilen ansehen"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Journal Entries"
msgstr "Journaleinträge ansehen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Journal Entry"
msgstr "Journalbuchung anzeigen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Partner"
msgstr "Partner ansehen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Payment"
msgstr "Zahlung ansehen"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid ""
"When checked this line and everything after it will be printed on a new "
"page."
msgstr ""
"Diese Zeile markieren damit alle nachfolgenden auf eine neue Seite gedruckt "
"werden."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr ""
"Wenn markiert, erscheinen Summen und Zwischensummen unter den Abschnitten "
"des Berichts"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""
"Wenn markiert, erscheinen Summen und Zwischensummen unter den Abschnitten "
"des Berichts."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr ""
"Ob wir für die ausgewählten Fremdwährungen Rückstellungen bilden müssen oder"
" nicht."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "With Draft Entries"
msgstr "Mit Buchungssätzen im Entwurf"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "You are using custom exchange rates."
msgstr "Sie verwenden benutzerdefinierte Wechselkurse."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "jährlich"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "any"
msgstr "irgendein"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "Tage nach der Periode"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__analytic
msgid "display the analytic filters"
msgstr "Zeige die Kostenfilter an"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__comparison
msgid "display the comparison filter"
msgstr "Vergleichsfilter anzeigen"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__show_journal_filter
msgid "display the journal filter in the report"
msgstr "Journalfilter im Bericht anzeigen"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "display the unfold all options in report"
msgstr "Aufklappen aller Optionen in Bericht anzeigen"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "alle zwei Monate"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "alle 4 Monate"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "following accounts"
msgstr "folgenden Konten"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "having a starting balance different than the previous ending balance"
msgstr "mit einem anderen Anfangssaldo als dem vorherigen Endsaldo"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "journal items"
msgstr "Buchungszeilen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be listed in an incorrect section of the report."
msgstr "könnten in einem falschen Abschnitt des Berichts aufgeführt sein."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be missing from the proper section of the report."
msgstr "könnten im entsprechenden Abschnitt des Berichts fehlen."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "monatlich"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "n/a"
msgstr "k. A."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_manager__report_name
msgid "name of the model of the report"
msgstr "Name des Berichts-Modells"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "prior or included in this period"
msgstr "vor oder innerhalb des Zeitraumes"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "vierteljährlich"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "halbjährlich"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__date_range
msgid "specify if the report use date_range or single date"
msgstr ""
"Geben Sie an, ob der Bericht einen Datumsbereich (date_range) oder ein "
"einziges Datum verwendet"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "statements"
msgstr "Kontoauszüge"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_report_control_error
msgid "technical field used to know if there was a failed control check"
msgstr "technisches Feld das anzeigt das eine Kontrolle fehlgeschlagen ist"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "to:"
msgstr "An:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "unposted Journal Entries"
msgstr "ungebuchte Journalbuchungen"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "which doesn't result from a bank statement nor payments."
msgstr ", die sich weder aus einem Kontoauszug noch aus Zahlungen ergeben."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ General Ledger"
msgstr "⇒ Hauptbuch"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ Rates"
msgstr "⇒ Kurse"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Zurücksetzen auf Odoos Kurs"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "⇒ journal items"
msgstr "⇒ Journal-Posten"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Some journal items appear to point to obsolete report lines."
msgstr ""
"Einige Journalbuchungen scheinen auf veraltete Berichtszeilen zu verweisen."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Check them"
msgstr "Kontrollieren"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "and correct their tax tags if necessary."
msgstr "und korregieren Sie die Steuer-Tags, wenn nötig."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Journal items with archived tax tags"
msgstr "Buchungszeilen mit archivierten Steuer-Stichwörtern"
