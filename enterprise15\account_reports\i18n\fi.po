# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <jussi.heikki<PERSON>@panimo.com>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# At<PERSON> Isopuro <<EMAIL>>, 2021
# Topi <PERSON>ra <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# V<PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Retropikzel, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Konsta Aavaranta, 2023
# Joakim Weckman, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-18 09:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and %s others"
msgstr " ja %s muut"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and one other"
msgstr " ja yksi toinen"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid " past period(s), previously stored on the corresponding tax line."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "\" account balance is affected by"
msgstr "\" tilin saldoon vaikuttaa"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(+) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""
"%s verkkopankkitililtäsi tuoduille tapahtumille(+) (päivätty tänään), joita "
"ei ole vielä täsmäytetty Odoo:ssa (Odottaa lopullista täsmäytystä, joka "
"mahdollistaa oikean tilin löytämisen)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(-) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""
"%s verkkopankkitililtäsi tuoduille tapahtumille(-) (päivätty tänään), joita "
"ei ole vielä täsmäytetty Odoo:ssa (Odottaa lopullista täsmäytystä, joka "
"mahdollistaa oikean tilin löytämisen)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_unexplained_difference
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(+) Outstanding Receipts"
msgstr "(+) Maksamattomat saamiset"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(-) Outstanding Payments"
msgstr "(-) Maksamatta olevat maksut"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(No Group)"
msgstr "(Ei ryhmää)"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "(copy)"
msgstr "(kopio)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Päivitä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period1
msgid "1 - 30"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period2
msgid "31 - 60"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period3
msgid "61 - 90"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period4
msgid "91 - 120"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "<br/>Companies:"
msgstr "<br/> Yritykset:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid ""
"<i class=\"fa fa-caret-right invisible\" role=\"img\" aria-"
"label=\"Unfolded\" title=\"Unfolded\"/>"
msgstr ""
"<i class=\"fa fa-caret-right invisible\" role=\"img\" aria-label=\"Avattu\" "
"title=\"Avattu\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "<span class=\"fa fa-bar-chart\"/> Comparison:"
msgstr "<span class=\"fa fa-bar-chart\"/> Vertailu:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                Tax Report:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Fiscal Position:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"            Verotuksellinen asema:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"            Päiväkirjat:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "<span class=\"fa fa-calendar\" title=\"Dates\" role=\"img\" aria-label=\"Dates\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Balance is good\" title=\"Balance is good\"/>"
msgstr ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Tilit ovat tasapainossa\" title=\"Tilit ovat tasapainossa\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is good\" title=\"Partner ledger is good\"/>"
msgstr ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partnerin kirjanpito on tasapainossa\" title=\"Partnerin kirjanpito "
"on tasapainossa\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Balance is bad\" title=\"Balance is bad\"/>"
msgstr ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Tili ei ole tasapainossa\" title=\"Tili ei ole tasapainossa\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is bad\" title=\"Partner ledger is bad\"/>"
msgstr ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partnerin kirjanpito ei ole tasapainossa\" title=\"Partnerin "
"kirjanpito ei ole tasapainossa\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Balance is normal\" title=\"Balance is normal\"/>"
msgstr ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Saldo on kunnossa\" title=\"Saldo on kunnossa\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partner ledger is normal\" title=\"Partner ledger is normal\"/>"
msgstr ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partnerin kirjanpito on normaali\" title=\"Partnerin kirjanpito"
" on normaali\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Codes:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Filters:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_groupby_fields
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Group By:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Ryhmän nimi:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/> Vaihtoehdot:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<span class=\"fa fa-folder-open\"/> Analytic"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<span class=\"fa fa-folder-open\"/> Partners"
msgstr "<span class=\"fa fa-folder-open\"/> Kumppanit"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid ""
"<span class=\"fa fa-home\"/>\n"
"                Tax Unit:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
msgid "<span class=\"fa fa-line-chart\"/> Exchange Rates"
msgstr "<span class=\"fa fa-line-chart\"/> Valuuttakurssit"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid ""
"<span class=\"fa fa-user\"/>\n"
"            Account:"
msgstr ""
"<span class=\"fa fa-user\"/>\n"
"            Tili:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.footnotes_template
msgid ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"
msgstr ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Poista\" title=\"Poista\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Country</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Tax Return Periodicity</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Veroilmoituksen jaksotus</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Tässä asetetut arvot ovat yrityskohtaisia.\" role=\"img\" aria-label=\"Tässä asetetut arvot ovat yrityskohtaisia.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"
msgstr ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">Edellinen tiliote</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid ""
"<span>Intrastat taxes are applied on unexpected journal entries "
"(intranational or between non intrastat countries).</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>Please note that the report may include some rounding differences "
"towards the bookings.</span>"
msgstr ""
"<span>Huomaa, että raportissa voi olla joitakin pyöristyseroja varausten "
"osalta.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid "<span>Some partners are missing a VAT number.</span>"
msgstr "<span>Joiltakin partnerilta puuttuu alv-tunniste.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>This company is part of a tax unit. You're currently not viewing the "
"whole unit. To change that, use the Tax Unit filter.</span>"
msgstr ""
"<span>Tämä yritys on osa veroyksikköä. Et näe tällä hetkellä koko yksikköä. "
"Voit muuttaa sen käyttämällä Veroyksikkö-suodatinta.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "<span>This report only displays the data of the active company.</span>"
msgstr "<span>Tämä raportti näyttää vain aktiivisen yrityksen tiedot.</span>"

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_financial_html_report_line_code_uniq
msgid "A report line with the same code already exists."
msgstr "Raporttirivi, jolla on sama koodi, on jo olemassa."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""
"Veroyksikkö voidaan luoda vain sellaisten yritysten välille, joilla on sama "
"päävaluutta."

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "VARAT"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_id
#, python-format
msgid "Account"
msgstr "Tili"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_analytic_report
msgid "Account Analytic Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Tilikarttamalli"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_code
msgid "Account Code"
msgstr "Tilikoodi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "Tilin näyttöä edustava kenttä"

#. module: account_reports
#: model:ir.model,name:account_reports.model_report_account_report_journal
msgid "Account Journal Report"
msgstr "Tilin päiväkirjan raportti"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_name
msgid "Account Name"
msgstr "Tilin nimi"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr "Tiliraportti"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report (HTML Line)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
msgid "Account Report (HTML)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr "Tiliraportin alaviite"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "Tilin edustama yritys"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "Tili arvonkorotuspäiväkirja"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "Tilitoimisto"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_accounting_report
msgid "Accounting Report Helper"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Accounts"
msgstr "Tilit"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Accounts to adjust"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Accounts without a group"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__action_id
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Toiminto"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Toiminnot voivat laukaista tietyn toiminnan, kuten kalenterinäkymän "
"avaamisen tai automaattisesti valmiiksi merkinnän, kun asiakirja on ladattu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Toimenpide"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Toimenpiteiden tyyppi"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Add a note"
msgstr "Lisää tekstirivi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Lisää kokonaissummat osioiden alapuolelle"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_adjustment
msgid "Adjustment"
msgstr "Säätö"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "Korjausvienti"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance Payments received from customers"
msgstr "Asiakkailta saadut ennakkomaksut"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance payments made to suppliers"
msgstr "Toimittajille suoritetut ennakkomaksut"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner
msgid "Aged Partner Balances"
msgstr "Erääntymisraportti"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.model,name:account_reports.model_account_aged_payable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
#, python-format
msgid "Aged Payable"
msgstr "Erääntynyt velka"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Erääntyneet velat"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.model,name:account_reports.model_account_aged_receivable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
#, python-format
msgid "Aged Receivable"
msgstr "Erääntynyt saatava"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Erääntyneet saatavat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "All"
msgstr "Kaikki"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "All Journals"
msgstr "Kaikki päiväkirjat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_allocated_earnings
msgid "Allocated Earnings"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__analytic
msgid "Allow analytic filters"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__comparison
msgid "Allow comparison"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__show_journal_filter
msgid "Allow filtering by journals"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__always
msgid "Always"
msgstr "Aina"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Amount"
msgstr "Arvo"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__amount_currency
#, python-format
msgid "Amount Currency"
msgstr "Valuuttamäärä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_account_id
msgid "Analytic Account"
msgstr "Kustannuspaikka"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Accounts:"
msgstr "Kustannuspaikat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Analytic Entries"
msgstr "Analyyttiset viennit"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_analytic
#: model:ir.ui.menu,name:account_reports.menu_action_report_account_analytic
#, python-format
msgid "Analytic Report"
msgstr "Analyyttinen raportointi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Analyyttinen tunniste"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Tags:"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
#, python-format
msgid "Annotate"
msgstr "Huomauta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid "Applicable Filters"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Apply"
msgstr "Käytä"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "As of %s"
msgstr "Päivästä %s alkaen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period0
msgid "As of: "
msgstr "Alkaen:"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#, python-format
msgid "As of: %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__to_beginning_of_period
msgid "At the beginning of the period"
msgstr "Kauden alussa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Audit"
msgstr "Tarkistus"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Tilintarkastuskertomus"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Available Filters & Options"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Toimittajien laskujen maksamisen keskimääräinen aika (päivää)"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Omien saatavien laskujen maksamisen keskimääräinen aika (päivää)"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__balance
#, python-format
msgid "Balance"
msgstr "Saldo"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_2
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_2
msgid "Balance Sheet"
msgstr "Tase"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency_current
msgid "Balance at current rate"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_balance
msgid "Balance at operation rate"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance in GL"
msgstr "Saldo pääkirjanpidossa"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency
msgid "Balance in foreign currency"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Balance of %s"
msgstr "%s saldo"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax advance payment account"
msgstr "Saldo veroennakoiden maksun tilillä"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (payable)"
msgstr "Verotulojen käyttötilin saldo (maksettava)"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (receivable)"
msgstr "Verotulojen käyttötilin saldo (saatava)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr "Pankkitapahtumien täsmäytys"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Pankkien täsmäytysten raportti"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Bank Reconciliation: %s"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Pankki- ja käteistilit"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Base Amount"
msgstr "Peruste määrä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__date_range
msgid "Based on date ranges"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid "Both"
msgstr "Molemmat"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Cancel"
msgstr "Peruuta"

#. module: account_reports
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "Carryover for period %s to %s"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Käteinen"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report
msgid "Cash Flow Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
#, python-format
msgid "Cash Flow Statement"
msgstr "Kassavirtalaskelma"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, beginning of period"
msgstr "Kassavarat ja vastaavat kauden alussa"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, closing balance"
msgstr "Kassavarat ja vastaavat kauden lopussa"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from financing activities"
msgstr "Rahoitustoiminnan kassavirrat"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from investing & extraordinary activities"
msgstr "Investointien ja satunnaisen toiminnan kassavirrat"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from operating activities"
msgstr "Liiketoiminnan kassavirrat"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from unclassified activities"
msgstr "Kassavirrat luokittelemattomasta toiminnasta"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash in"
msgstr "Käteinen sisään"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash out"
msgstr "Käteinen ulos"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash paid for operating activities"
msgstr "Liiketoiminnasta maksetut varat käteisellä"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Saadut käteisvarat"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash received from operating activities"
msgstr "Liiketoiminnasta saadut käteisvarat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Käytetyt käteisvarat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Kassan ylijäämä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid "Change expected payment date"
msgstr "Muuta odotettua maksupäivää"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Chart of Accounts"
msgstr "Tilikartta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__children_ids
msgid "Children"
msgstr "Alatunnukset"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr "Alarivit"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Closing Entry"
msgstr "Sulkeminen merkintä"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Pankin loppusaldo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__code
msgid "Code"
msgstr "Koodi"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Code:"
msgstr "Koodi:"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Communication"
msgstr "Viestintä"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Yritykset"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__company_id
msgid "Company"
msgstr "Yritys"

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"Company %s already belongs to a tax unit in %s. A company can at most be "
"part of one tax unit per country."
msgstr ""
"Yritys %s kuuluu jo veroyksikköön %s. Yritys voi kuulua enintään yhteen "
"veroyksikköön maata kohden."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Company Currency:"
msgstr "Yrityksen valuutta:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid "Company Only"
msgstr "Vain yritys"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Company Settings"
msgstr "Yrityksen asetukset"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr "Laskenta"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Computation: %s"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguraatioasetukset"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Configure your TAX accounts - %s"
msgstr "Määrätä verotilit - %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Määritä verotilit"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cj
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cj
#, python-format
msgid "Consolidated Journals"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_consolidated_journal
msgid "Consolidated Journals Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__control_domain
msgid "Control Domain"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Control Domain:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Controls failed"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Cost of Revenue"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__country_id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Country"
msgstr "Maa"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Country Code"
msgstr "Maatunnus"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Luo kirjaus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Luotu"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__credit
#, python-format
msgid "Credit"
msgstr "Kredit"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_id
#, python-format
msgid "Currency"
msgstr "Valuutta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_code
msgid "Currency Code"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Currency Rates (%s)"
msgstr "Valuuttakurssit (%s)"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Vaihtuvat vastaavat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Lyhytaikaiset velat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr "Kuluvan vuoden kohdennettu tulos"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr "kuluvan vuoden tulos"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Kuluvan vuoden kohdentamaton tulos"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Lyhytaikaiset varat ja velat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Custom"
msgstr "Mukautettu"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#, python-format
msgid "Date"
msgstr "Päivämäärä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date :"
msgstr "Päiväys"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Date cannot be empty"
msgstr "Päivämäärä ei voi olla tyhjä"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""
"Päivämäärä, jolloin saamiserän seuraava toimenpide on toteutettava. "
"Asetetaan yleensä automaattisesti, kun muistutuksia lähetetään "
"asiakastilityksen kautta."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date:"
msgstr "Päiväys:"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__debit
#, python-format
msgid "Debit"
msgstr "Debit"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Viiveen yksiköt"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr "poistot"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Details per month"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Difference"
msgstr "Ero"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_type
msgid "Display Type"
msgstr "Näyttötyyppi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Asiakirjojen nimi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__domain
msgid "Domain"
msgstr "Verkkotunnus"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Domain:"
msgstr "Toimialue:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "Domestic"
msgstr "Kotimaa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Domestic country of your accounting"
msgstr "Kirjanpitosi kotimaa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Lataa tietojen muuttumattomuuden tarkistusraportti"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_date
#, python-format
msgid "Due Date"
msgstr "Eräpäivä"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.model,name:account_reports.model_account_sales_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
#, python-format
msgid "EC Sales List"
msgstr "Myynnit yhteisöalueelle (EU)"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "OMA PÄÄOMA"

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "End Balance"
msgstr "Loppusaldo"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End Date :"
msgstr "Päättymispäivä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Financial Year"
msgstr "Edellisen tilikauden loppu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Month"
msgstr "Edellisen kuun loppu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Quarter"
msgstr "Edellisen kvartaalin loppu"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Error while validating the domain of line %s:\n"
"%s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Excess Journal Items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude"
msgstr "Älä sisällytä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude From Aged Reports"
msgstr "Älä huomioi erääntymisraportilla"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "Älä sisällytä valuuttavarauksia"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude from adjustment/provisions entries"
msgstr "Älä sisällytä oikaisu- ja korjauskirjauksia"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude this account from aged reports"
msgstr "Älä huomioi tätä tiliä erääntymisraportilla"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Excluded Accounts"
msgstr "Poissuljetut tilit"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_3
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_3
msgid "Executive Summary"
msgstr "Tiivistelmä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__expected_pay_date
msgid "Expected Date"
msgstr "Oletettu päivämäärä"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr "Odotettu maksupäivä"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s"
msgstr "Odotettu maksupäivä on muutettu %s:sta %s:ksi"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s for invoice %s"
msgstr "Laskun %s odotettu maksupäivä on muutettu %s:sta %s:ksi"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Kulujen varaustili"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Expense Provision for {for_cur}"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense account"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr "Menot"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Export"
msgstr "Vienti"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Kirjanpidon raporttien vientimuoto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Vie"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Ohjattu kirjanpidon raporttien vienti"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Filter:"
msgstr "Suodatin:"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid ""
"Filters that can be used to filter and group lines in this report. This uses"
" saved filters on journal items."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__financial_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__financial_report_id
msgid "Financial Report"
msgstr "Talousraportti"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:account_reports.menu_account_financial_reports_tree
msgid "Financial Reports"
msgstr "Talousraportit"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Verotuksellinen maa"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__float
msgid "Float"
msgstr "Liukuluku"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__foldable
msgid "Foldable"
msgstr "Taitettava"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Folded"
msgstr "Laskostettu"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__footnotes_ids
msgid "Footnotes"
msgstr "Alaviitteet"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__strict_range
msgid "Force given dates for all accounts and account types"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Ulkomaanvaluutan oikaisukirjaus alkaen %s"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Formula:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__formulas
msgid "Formulas"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"From %s\n"
"to  %s"
msgstr ""
"Alkaen %s\n"
" %s saakka"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_beginning
msgid "From the beginning"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_fiscalyear
msgid "From the beginning of the fiscal year"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "From:"
msgstr "Lähettäjä:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Kutsuttava toiminto"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "General Ledger"
msgstr "Pääkirja"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Pääkirjaraportti"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "General Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Luodut dokumentit"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Global Summary"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Goods"
msgstr "Tavarat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Grid"
msgstr "Taulukko"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Bruttovoitto"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Bruttopaino"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Bruttokateprosentti (bruttovoitto / liikevoitto)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__groupby
msgid "Group by"
msgstr "Ryhmittele"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Account &gt; Tax"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Tax &gt; Account"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Groupby field %s is invalid on line with name '%s'"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Groupby:"
msgstr "Ryhmittele:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_empty
msgid "Hide If Empty"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_zero
msgid "Hide If Zero"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy"
msgstr "Hierarkia"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy and Subtotals"
msgstr "Hierarkia ja välisummat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Kuinka usein veroilmoitukset on tehtävä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impact On Grid"
msgstr "Vaikutus verkkoon"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impacted Tax Grids"
msgstr "Vaikutuksen kohteena olevat veroverkot"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include"
msgstr "Sisällytä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include Unposted Entries"
msgstr "Sisällytä kirjaamattomat merkinnät"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include in adjustment/provisions entries"
msgstr "Sisällytä korjaukset"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include unposted entries"
msgstr "Sisällytä kirjaamattomat viennit"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Payments"
msgstr "Sisällytä täsmäyttämättömät pankkitiliotteiden maksut"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Receipts"
msgstr "Sisällytä täsmäyttämättömät pankkitiliotteiden kuitit"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr "Tulo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Tulotili"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "Tulojen varaustili"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Income Provision for {for_cur}"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Inconsistent Statements"
msgstr "Ristiriitainen tiliote"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "Initial Balance"
msgstr "Alkusaldo"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Insert foot note here"
msgstr "Lisää alaviite"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__internal_note
msgid "Internal Note"
msgstr "Sisäinen muistiinpano"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__green_on_positive
msgid "Is growth good when positive"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "JRNL"
msgstr "Päiväkirja"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Päiväkirja"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Päiväkirjan kirjaus"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Groups"
msgstr "Päiväkirjaryhmät"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Päiväkirjatapahtuma"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "Journal Items"
msgstr "Päiväkirjan tapahtumat"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items (%s)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items for Tax Audit"
msgstr "Verotarkastuksen päiväkirjat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal Items on the"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Journal Name (Code)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal items on the"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_print_journal_menu
#: model:ir.ui.menu,name:account_reports.menu_print_journal
msgid "Journals Audit"
msgstr "päiväkirjan tarkastus"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Journals:"
msgstr "Päiväkirjat:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "VELAT"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "VELAT + OMA PÄÄOMA"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Label"
msgstr "Otsikko"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Financial Year"
msgstr "Edellinen tilikausi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Month"
msgstr "Edellinen kuukausi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Quarter"
msgstr "Edellinen kvartaali"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_link_last_statement
msgid "Last Statement:"
msgstr "Viimeisin tiliote:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__level
msgid "Level"
msgstr "Taso"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line
msgid "Line"
msgstr "Rivi"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__line_ids
msgid "Lines"
msgstr "Rivit"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Load more... (%s remaining)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Pääyhtiö"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""
"Tämän yksikön pääyhtiö; se, joka viime kädessä raportoi ja maksaa verot."

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#, python-format
msgid "Make Adjustment Entry"
msgstr "Tee oikaisukirjaus"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "Hallitse raporttien yhteenvetoa ja alaviitteitä"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__manager_id
msgid "Manager"
msgstr "Päällikkö"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Matching Number"
msgstr "Vastaava numero"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "Tämän yksikön jäsenet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__generated_menu_id
msgid "Menu Item"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Missing Journal Items"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr "Moduuli"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_id
msgid "Move"
msgstr "Siirto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_name
msgid "Move Name"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_ref
msgid "Move Ref"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_type
msgid "Move Type"
msgstr "Siirron tyyppi"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation
msgid "Multicurrency Revaluation Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "Monivaluuttainen arvostusraportin ohjattu toiminto"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for fiscal position %s after %s. There should be at most one. \n"
" %s"
msgstr ""
"Verokannan %s osalta on olemassa useita luonnoksia verojen päättymiskirjauksista %s jälkeen. Niitä saa olla enintään yksi.\n"
" %s"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %s. There should be at most one. \n"
" %s"
msgstr ""
"Kotimaan alueella on useita veroluonnoksia %s jälkeen. Niitä saa olla enintään yksi.\n"
" %s"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "NET"
msgstr "NETTO"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#, python-format
msgid "Name"
msgstr "Nimi"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "Nimi, joka annetaan luoduille asiakirjoille."

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Name:"
msgstr "Nimi:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "Net Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Nettovoitto"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Nettovarallisuus"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Net increase in cash and cash equivalents"
msgstr "Rahavarojen nettokasvu"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "Nettovoittomarginaali (nettovoitto / tulot)"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__never
msgid "Never"
msgstr "Ei koskaan"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__next_action_date
msgid "Next Action Date"
msgstr "Seuraavan toimenpiteen päivä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "No Comparison"
msgstr "Ei vertailua"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__no_unit
msgid "No Unit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "No VAT number associated with your company. Please define one."
msgstr "Yritykseltä puuttuu ALV-tunniste. Määritä se."

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No adjustment needed"
msgstr "Korjauksia ei tarvita."

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No provision needed was found."
msgstr "Ei tarvittavia varauksia."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid "None"
msgstr "Ei mitään"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Number of periods :"
msgstr "Jaksojen lukumäärä :"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "TASEEN ULKOPUOLISET TILIT"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo-varoitus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period5
msgid "Older"
msgstr "Vanhempi"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "Yhtä valituista formaateista ei voi viedä DMS-järjestelmään"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "One or more partners has no VAT Number."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"Vain Billing Administrator -käyttöoikeuden omaavat käyttäjät voivat vaihtaa "
"kauden lukituspäiviä!"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Only Show Unreconciled Entries"
msgstr "Näytä vain erittelemättömät kirjaukset"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Tilikauden avaus-saldo"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr "Liiketoiminnan tuotot"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr "muut tulot"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
#, python-format
msgid "Outstanding Payments/Receipts"
msgstr "Maksamatta olevat maksut/tulot"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_id
msgid "Parent"
msgstr "Ylätaso"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__parent_id
msgid "Parent Menu"
msgstr "Ylätason valikko"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_path
msgid "Parent Path"
msgstr "Ylempi polku"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "Vanhemman raportin tunnus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Ohjattu vanhemman raportin toiminto"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_id
#, python-format
msgid "Partner"
msgstr "Kumppani"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partner Categories:"
msgstr "Partnerikategoriat"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/res_partner.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.model,name:account_reports.model_account_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
#, python-format
msgid "Partner Ledger"
msgstr "Kumppanin tilikirja"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_name
msgid "Partner Name"
msgstr "Kumppanin nimi"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr "Partnerin raportit"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_trust
msgid "Partner Trust"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Partners"
msgstr "Kumppanit"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partners:"
msgstr "Partnerit:"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Payable"
msgstr "ostovelat"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Payable tax amount"
msgstr "Maksettavan veron määrä"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "erääntyneet"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__payment_id
msgid "Payment"
msgstr "Maksu"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__percents
msgid "Percents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Suoriutuminen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Periodicity"
msgstr "Jaksotus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Jaksotus kuukaudessa"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Please specify a Group by field when using '%s' in Formulas, on line with "
"name '%s'"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "+ käyttöom."

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Plus pitkäaikaiset varat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Plus pitkäaikaiset velat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Asema"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Posted Entries Only"
msgstr "Vain kirjatut merkinnät"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "ennakot"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Esikatsele dataa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Previous Period"
msgstr "Edellinen periodi"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Edellisten vuosien kohdentamaton tulos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid "Print On New Page"
msgstr "Tulosta uudelle sivulle"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""
"Ole varovainen, sillä tällä ajanjaksolla saattaa olla voimassa oleva "
"mukautus ("

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
msgid "Profit And Loss"
msgstr "Pérdidas y Ganacias"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_1
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_1
msgid "Profit and Loss"
msgstr "Tuloslaskelma"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Kannattavuus"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "Ehdotettu verotuksen päättymispäiväkirjauksen kirjaus."

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Provision for {for_cur} (1 {comp_cur} = {rate} {for_cur})"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Receivable"
msgstr "saatavat"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Receivable tax amount"
msgstr "Saatava veron määrä"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Saatavat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "Reconcile"
msgstr "Täsmäytä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Täsmäytysraportti"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Ref"
msgstr "Viite"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Reference"
msgstr "Viite"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Related Company"
msgstr "Liittyvä yritys"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Muistutus"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_currency_id
msgid "Report Currency"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Definition"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_include
msgid "Report Include"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr "Raporttirivi"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Report Line Computation"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr "Raporttirivit"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_model
msgid "Report Model"
msgstr "Raportin ohjelmistomalli"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__report_name
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Name"
msgstr "Raportin nimi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Raportointi"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Kertyneet voittovarat"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Sijoitetun pääoman tuotto (nettovoitto / varat)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Peruutuspäivämäärä"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Vastakirjaus tositteelle %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Same Period Last Year"
msgstr "Sama ajanjakso viime vuonna"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Save"
msgstr "Tallenna"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid "Search account"
msgstr "Etsi tili"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_partner
msgid "Search partner"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__name
msgid "Section Name"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Services"
msgstr "Palvelut"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__tax_report
msgid ""
"Set to True to automatically filter out journal items that are not tax "
"exigible."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_coa
msgid "Setup"
msgstr "Asettaa"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Lyhyen aikavälin käteisvaroja koskeva ennuste"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__show_domain
msgid "Show Domain"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Näytä varoitus kirjauksesta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "Show unfold all filter"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Some controls failed"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"Some of your tax groups are missing information in company %s. Please "
"complete their configuration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__special_date_changer
msgid "Special Date Changer"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__control_domain
msgid ""
"Specify a control domain that will raise a warning if the report line is not"
" computed correctly."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr "Määritä tilitoimisto, joka toimii edustajana raportteja vietäessä."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Start Date :"
msgstr "Aloituspäivä:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Alkaen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__summary
msgid "Summary"
msgstr "Yhteenveto"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "TAX"
msgstr "VERO"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Tags"
msgstr "Tunnisteet"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Amount"
msgstr "Verotili"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_end_date
msgid "Tax Closing End Date"
msgstr "Verotuksen päättymispäivä"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Declaration"
msgstr "Veron kuvaus"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Tax Grid"
msgstr "Verotunniste"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "VAT-numero"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Paid Adjustment"
msgstr "Maksettujen verojen oikaisu"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Received Adjustment"
msgstr "Saatujen verojen oikaisu"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__tax_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
#, python-format
msgid "Tax Report"
msgstr "Veroraportti"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_report_control_error
msgid "Tax Report Control Error"
msgstr "Veroraportin valvontavirhe"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Tax Return"
msgstr "Veroilmoitus"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "Veroyksikkö"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "Veroyksiköt"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Veroraportti"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return for %s%s"
msgstr "%s%s:n veroilmoitus"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return from %s to %s%s"
msgstr "Veroilmoitus %s:sta %s%s:een"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_closing_end_date
msgid ""
"Technical field used for VAT closing, containig the end date of the period "
"this entry closes."
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "Tekninen malli kirjanpitoraportin lataamista varten"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr "Teksti"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "The Book balance in Odoo dated today"
msgstr "Kirjanpidon saldo Odoossa tälle päivälle"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The amount will be : %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The carried over balance will be : %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""
"Maa, jossa tätä veroyksikköä käytetään yritysten veroilmoitusten "
"ryhmittelyyn."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__country_id
msgid "The country this report is intended to."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr "Maa, jonka veroraportteja käytetään tälle yritykselle"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"The current balance in the General Ledger %s doesn't match the balance of "
"your last bank statement %s leading to an unexplained difference of %s."
msgstr ""
"Pääkirjan nykyinen saldo %s ei vastaa viimeisimmän tiliotteesi saldoa %s. "
"Tämä johtaa selittämättömään eroon %s."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The difference will be carried over to the next period's declaration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""
"Tunnus, jota käytetään, kun tätä yksikköä koskeva raportti lähetetään."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid "The main company of a tax unit has to be part of it."
msgstr "Veroyksikön pääyhtiön on kuuluttava siihen."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__generated_menu_id
msgid "The menu item generated for this report, or None if there isn't any."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "Veroyksiköt, joihin tämä yritys kuuluu."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "There are"
msgstr "On olemassa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "There are some"
msgstr "On joitakin"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Financial Year"
msgstr "Kuluva tilikausi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Month"
msgstr "Kuluva kuukausi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Quarter"
msgstr "Kuluva kvartaali"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr "Tämän avulla voi valita loppusummien sijainnin talousraporteissa."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be increased by the positive amount from"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be reduced by the negative amount from"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be set to %s."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This period is already closed for company %s"
msgstr "Tämä ajanjakso on jo päättynyt yrityksen %s osalta"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Today"
msgstr "Tänään"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Total"
msgstr "Yhteensä"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Total %s"
msgstr "Yhteensä %s"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions that were entered into Odoo, but not yet reconciled (Payments "
"triggered by invoices/bills or manually)"
msgstr ""
"Odoohon syötetyt tapahtumat, joita ei ole vielä täsmäytetty "
"(laskujen/laskujen käynnistämät tai manuaalisesti suoritetut maksut)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(+) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by invoices/refunds or manually)"
msgstr ""
"Tapahtumat(+), jotka on syötetty Odoohon, mutta joita ei ole vielä "
"täsmäytetty (laskut/palautukset tai manuaalisesti käynnistetyt maksut)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(-) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by bills/credit notes or manually)"
msgstr ""
"Tapahtumat(-), jotka on syötetty Odoo:hon, mutta joita ei ole vielä "
"täsmäytetty (laskujen/hyvityslaskujen tai manuaalisesti käynnistetyt maksut)"

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
#, python-format
msgid "Trial Balance"
msgstr "Koetase"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Triangular"
msgstr "Kolmion muotoinen"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__figure_type
msgid "Type"
msgstr "Tyyppi"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Kohdistamattomat tulot"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Undefined"
msgstr "Ei määritelty"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Unexplained Difference"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold"
msgstr "Avaa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold All"
msgstr "Avaa kaikki"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Unfolded"
msgstr "Avattu"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Tuntematon kumppani"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
#, python-format
msgid "Unrealized Currency Gains/Losses"
msgstr "Realisoitumattomat valuuttakurssivoitot/-tappiot"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unreconciled"
msgstr "Täsmäyttämätön"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__normal
msgid ""
"Use the dates that should normally be used, depending on the account types"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "VAT"
msgstr "ALV"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_company_form
msgid "VAT Units"
msgstr "ALV-yksiköt"

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Vat closing from %s to %s"
msgstr "ALV:n sulkeminen %ssta %s:een"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bank Statement"
msgstr "Näytä tiliote"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Carryover Lines"
msgstr "Näytä siirtorivit"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Journal Entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Journal Entry"
msgstr "Näytä päiväkirjamerkintä"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Partner"
msgstr "Näytä partneri"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Payment"
msgstr "Näytä maksu"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid ""
"When checked this line and everything after it will be printed on a new "
"page."
msgstr ""
"Kun tämä on valittuna kaikki sen jälkeinen tulostetaan uudelle sivulle."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr ""
"Kun valittu, raportin osioiden alapuolella näkyvät summat ja välisummat"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""
"Kun valittu, raportin osioiden alapuolella näkyvät summat ja välisummat."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr "Tuleeko tehdä varauksia valittuja valuuttoja varten."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "With Draft Entries"
msgstr "Luonnosmerkinnöillä"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "You are using custom exchange rates."
msgstr "Käytät mukautettuja valuuttakursseja."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "vuosittain"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "any"
msgstr "Mikä tahansa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "päivää kauden jälkeen"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__analytic
msgid "display the analytic filters"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__comparison
msgid "display the comparison filter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__show_journal_filter
msgid "display the journal filter in the report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "display the unfold all options in report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "joka toinen kuukausi"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "joka neljäs kuukausi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "following accounts"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "having a starting balance different than the previous ending balance"
msgstr "alkusaldo on erilainen kuin edellinen loppusaldo"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "journal items"
msgstr "päiväkirjan tapahtumat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be listed in an incorrect section of the report."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be missing from the proper section of the report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "kuukausittain"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_manager__report_name
msgid "name of the model of the report"
msgstr "raportin mallin nimi"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "prior or included in this period"
msgstr "ennen tätä kautta tai sisältyen tähän kauteen."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "neljännesvuosittain"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "kaksi kertaa vuodessa"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__date_range
msgid "specify if the report use date_range or single date"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "statements"
msgstr "tiliotteet"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_report_control_error
msgid "technical field used to know if there was a failed control check"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "to:"
msgstr "vastaanottaja:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "unposted Journal Entries"
msgstr "kirjaamattomat päiväkirjaviennit"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "which doesn't result from a bank statement nor payments."
msgstr "joka ei johdu tiliotteesta eikä maksuista."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ General Ledger"
msgstr "⇒ Pääkirjanpito"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ Rates"
msgstr "⇒ Kurssit"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Nollaa Odoon kurssiin"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "⇒ journal items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Some journal items appear to point to obsolete report lines."
msgstr ""
"Jotkin päiväkirjaerät näyttävät viittaavan vanhentuneisiin raporttiriveihin."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Check them"
msgstr "Tarkista ne"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "and correct their tax tags if necessary."
msgstr "ja korjaa veromerkinnät tarvittaessa."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Journal items with archived tax tags"
msgstr "Päiväkirjamerkinnät, joissa on arkistoituja verotunnisteita"
