# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_reports
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: Samkhann Seang <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:240
#, python-format
msgid "%s Payment Reminder"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "&amp;nbsp;"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:30
#, python-format
msgid "&times;"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:582
#, python-format
msgid "(No Group)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:355
#: code:addons/account_reports/models/account_financial_report.py:357
#, python-format
msgid "(copy)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:22
#, python-format
msgid "1 - 30"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:22
#, python-format
msgid "31 - 60"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:22
#, python-format
msgid "61 - 90"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:22
#, python-format
msgid "91 - 120"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<b>Accounts</b><br/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<b>Next Reminder Date:</b>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<b>Partners</b><br/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<b>Tags</b><br/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid ""
"<br/>\n"
"                <span>You can visualize their impact by activating the option to view unposted entries, or post them to remove this warning.</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid ""
"<br/>\n"
"                Analytic Accounts:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "<br/>Companies:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "<span class=\"fa fa-bar-chart\"/> Comparison:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Journals:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_multi_company
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Companies:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Filters:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<span class=\"fa fa-folder-open\"/> Analytic"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<span class=\"fa fa-folder-open\"/> Partners"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid ""
"<span class=\"fa fa-user\"/>\n"
"            Account:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Payment Follow-up</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_partner_view_form
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr ""

#. module: account_reports
#: sql_constraint:account.financial.html.report.line:0
msgid "A report line with the same code already exists."
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:21
#: code:addons/account_reports/models/account_partner_ledger.py:31
#, python-format
msgid "Account"
msgstr "គណនី"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_analytic_report
msgid "Account Analytic Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report (HTML Line)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
msgid "Account Report (HTML)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:79
#, python-format
msgid "Accounts without a group"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Accrual Basis<br/>"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__action_id
msgid "Action"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:360
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
#, python-format
msgid "Add a note"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_prepayments_received0
msgid "Advance Payments received from customers"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_paid0
msgid "Advance payments made to suppliers"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner
msgid "Aged Partner Balances"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:118
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.model,name:account_reports.model_account_aged_payable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
#, python-format
msgid "Aged Payable"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:98
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.model,name:account_reports.model_account_aged_receivable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
#, python-format
msgid "Aged Receivable"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_journals
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_multi_company
msgid "All"
msgstr "ទាំងអស់"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__analytic
msgid "Allow analytic filters"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__cash_basis
msgid "Allow cash basis mode"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__comparison
msgid "Allow comparison"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__show_journal_filter
msgid "Allow filtering by journals"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Always"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:24
#, python-format
msgid "Amount"
msgstr "ចំនួន"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:40
#, python-format
msgid "Amount Currency"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Accounts:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Analytic Entries"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:29
#: model:ir.actions.client,name:account_reports.action_account_report_analytic
#: model:ir.ui.menu,name:account_reports.menu_action_report_account_analytic
#, python-format
msgid "Analytic Report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Tags:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Annotate"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid "Applicable Filters"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Apply"
msgstr "កំណត់យក"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:770
#, python-format
msgid "As of %s"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "At the beginning of the period"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Audit"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Auto"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Available Filters & Options"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Bad debtor"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:25
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#: code:addons/account_reports/models/account_general_ledger.py:35
#: code:addons/account_reports/models/account_partner_ledger.py:42
#, python-format
msgid "Balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_2
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_2
msgid "Balance Sheet"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:211
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:212
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:432
#, python-format
msgid "Base Amount"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__date_range
msgid "Based on date ranges"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "សាច់ប្រាក់"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Cash Basis"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Cash Basis Method"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_cashsummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_3
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_3
msgid "Cash Flow Statement"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_opening_balance0
msgid "Cash and cash equivalents, beginning of period"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_closing_balance0
msgid "Cash and cash equivalents, closing balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing0
msgid "Cash flows from financing activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing0
msgid "Cash flows from investing & extraordinary activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_operating0
msgid "Cash flows from operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified0
msgid "Cash flows from unclassified activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing_in0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing_in0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified_in0
msgid "Cash in"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing_out0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing_out0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified_out0
msgid "Cash out"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_spent0
msgid "Cash paid for operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_received0
msgid "Cash received from operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_followup_report
msgid "Change expected payment date"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__children_ids
msgid "Children"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__code
msgid "Code"
msgstr "កូដ"

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:32
#: code:addons/account_reports/models/account_general_ledger.py:30
#, python-format
msgid "Communication"
msgstr "ទំនាក់ទំនង"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Companies:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__company_id
msgid "Company"
msgstr "ក្រុមហ៊ុន"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_regulator0
msgid "Computation Error: unexplained difference"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:26
#: model:ir.actions.client,name:account_reports.action_account_report_cj
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cj
#, python-format
msgid "Consolidated Journals"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_consolidated_journal
msgid "Consolidated Journals Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:248
#, python-format
msgid ""
"Could not send mail to partner because it does not have any email address "
"defined"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#: code:addons/account_reports/models/account_financial_report.py:134
#: code:addons/account_reports/models/account_general_ledger.py:34
#: code:addons/account_reports/models/account_partner_ledger.py:37
#: code:addons/account_reports/models/account_report_coa.py:32
#: code:addons/account_reports/models/account_report_coa.py:37
#: code:addons/account_reports/models/account_report_coa.py:41
#: code:addons/account_reports/models/account_report_coa.py:43
#, python-format
msgid "Credit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:32
#, python-format
msgid "Currency"
msgstr "រូបិយវត្ថុ"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:155
#, python-format
msgid "Current balance of account %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:157
#, python-format
msgid "Current balance of accounts %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Custom"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:87
#, python-format
msgid "Customer Ledger"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.followup_filter_info_template
msgid "Customer ref:"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_followup
msgid "Customers Statement"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:22
#: code:addons/account_reports/models/account_followup_report.py:29
#: code:addons/account_reports/models/account_general_ledger.py:29
#, python-format
msgid "Date"
msgstr "កាលបរិច្ឆេត"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date :"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid ""
"Date at which Odoo will remind you to take care of that follow-up if you "
"choose \"remind me later\" button."
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:225
#: code:addons/account_reports/static/src/js/account_reports.js:275
#, python-format
msgid "Date cannot be empty"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.followup_filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__days_between_two_followups
msgid "Days between two follow-ups"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#: code:addons/account_reports/models/account_financial_report.py:134
#: code:addons/account_reports/models/account_general_ledger.py:33
#: code:addons/account_reports/models/account_partner_ledger.py:36
#: code:addons/account_reports/models/account_report_coa.py:31
#: code:addons/account_reports/models/account_report_coa.py:36
#: code:addons/account_reports/models/account_report_coa.py:40
#: code:addons/account_reports/models/account_report_coa.py:42
#, python-format
msgid "Debit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_detailed_net_profit0
msgid "Detailed Net Profit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:86
#, python-format
msgid "Details per month"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Difference"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:190
#, python-format
msgid ""
"Difference between Virtual GL Balance and Last Bank Statement Ending Balance.\n"
"\n"
"If non-zero, it could be due to\n"
"  1) some bank statements being not yet encoded into Odoo\n"
"  2) payments double-encoded"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_analytic_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_consolidated_journal__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_partner_ledger__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__hierarchy_option
msgid "Display the hierarchy choice in the report options"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__domain
msgid "Domain"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:16
#, python-format
msgid "Done"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:30
#: code:addons/account_reports/models/account_partner_ledger.py:33
#, python-format
msgid "Due Date"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Duration between two reminders"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__hierarchy_option
msgid "Enable the hierarchy option"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End Date :"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Financial Year"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Month"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Quarter"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:34
#, python-format
msgid "Excluded"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_4
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_4
msgid "Executive Summary"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:33
#, python-format
msgid "Expected Date"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:55
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:147
#, python-format
msgid "Expected pay date has been changed from %s to %s for invoice %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:701
#, python-format
msgid "Export (XLSX)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Filter:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid "Filters that can be used to filter and group lines in this report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__financial_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__financial_report_id
msgid "Financial Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:account_reports.menu_account_financial_reports_tree
msgid "Financial Reports"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "Float"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Foldable"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Folded"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_followup_report
msgid "Follow-up Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_list_customer_statements
#: model:ir.ui.menu,name:account_reports.customer_statements_menu
msgid "Follow-up Reports"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.customer_statements_tree_view
msgid "Follow-up Reports Tree View"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:235
#, python-format
msgid "Follow-up email sent to %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:257
#, python-format
msgid "Follow-up letter printed"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Follow-up reports"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:35
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:180
#, python-format
msgid "Followup Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__followup_status
msgid "Followup status"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__footnotes_ids
msgid "Footnotes"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "Force given dates for all accounts and account types"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__formulas
msgid "Formulas"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:783
#, python-format
msgid ""
"From %s \n"
" to  %s"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "From the beginning"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "From the beginning of the fiscal year"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "From:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:453
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "General Ledger"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:160
#, python-format
msgid "General Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Good debtor"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__groupby
msgid "Group by"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:291
#: code:addons/account_reports/models/account_financial_report.py:867
#: code:addons/account_reports/models/account_financial_report.py:981
#, python-format
msgid "Groupby should be a field from account.move.line"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:831
#, python-format
msgid "Groupby should be a journal item field"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_zero
msgid "Hide If Zero"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy and Subtotals"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__id
#: model:ir.model.fields,field_description:account_reports.field_account_analytic_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_consolidated_journal__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__id
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger__id
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_partner_ledger__id
#: model:ir.model.fields,field_description:account_reports.field_account_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.followup_search_template
msgid "In Need of Action"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.customer_statements_search_view
#: selection:res.partner,followup_status:0
msgid "In need of action"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include Unposted Entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include unposted entries"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:308
#: code:addons/account_reports/models/account_partner_ledger.py:35
#: code:addons/account_reports/models/account_report_coa.py:50
#: model_terms:ir.ui.view,arch_db:account_reports.template_coa_table_header
#, python-format
msgid "Initial Balance"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:43
#, python-format
msgid "Insert foot note here"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__internal_note
msgid "Internal Note"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__green_on_positive
msgid "Is growth good when positive"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:21
#: code:addons/account_reports/models/account_partner_ledger.py:30
#, python-format
msgid "JRNL"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Journal Items"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:309
#, python-format
msgid "Journal Items (%s)"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_journal_items
msgid "Journal Items by tax"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#, python-format
msgid "Journal Name (Code)"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_print_journal_menu
#: model:ir.ui.menu,name:account_reports.menu_print_journal
msgid "Journals Audit"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Journals:"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:188
#, python-format
msgid "Last Bank Statement Ending Balance"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Financial Year"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_analytic_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_consolidated_journal____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_partner_ledger____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Month"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Quarter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__level
msgid "Level"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line
msgid "Line"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__line_ids
msgid "Lines"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:384
#: code:addons/account_reports/models/account_partner_ledger.py:206
#, python-format
msgid "Load more... (%s remaining)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__manager_id
msgid "Manager"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Manual"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:34
#, python-format
msgid "Matching Number"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__generated_menu_id
msgid "Menu Item"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Minimum"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:19
#: code:addons/account_reports/models/account_generic_tax_report.py:22
#, python-format
msgid "NET"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:431
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__name
#, python-format
msgid "Name"
msgstr "ឈ្មោះ"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "Net Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_net_increase0
msgid "Net increase in cash and cash equivalents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Never"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__next_action_date
#: model:ir.model.fields,field_description:account_reports.field_res_partner__payment_next_action_date
msgid "Next Action Date"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:154
#, python-format
msgid "Next action date: "
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "No Comparison"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "No Unit"
msgstr ""

#. module: account_reports
#: selection:res.partner,followup_status:0
msgid "No action needed"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:933
#, python-format
msgid "No comparison"
msgstr ""

#. module: account_reports
#: model_terms:ir.actions.act_window,help:account_reports.action_view_list_customer_statements
msgid "No follow-up to send!"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:23
#, python-format
msgid "No followup to send!"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Normal debtor"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:21
#, python-format
msgid "Not due on: %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__days_between_two_followups
msgid "Number of days between two follow-ups"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Number of periods :"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:13
#, python-format
msgid ""
"Odoo will remind you to take care of this follow-up on the next reminder "
"date."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:22
#, python-format
msgid "Older"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Only Show Unreconciled Entries"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:163
#, python-format
msgid "Operations to Process"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.customer_statements_search_view
msgid "Overdue Invoices"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:174
#, python-format
msgid "Overdue Payments for %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_id
msgid "Parent"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__parent_id
msgid "Parent Menu"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_path
msgid "Parent Path"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:24
#: code:addons/account_reports/models/account_general_ledger.py:31
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__partner_id
#, python-format
msgid "Partner"
msgstr "ដៃគូ"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partner Categories:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:228
#: code:addons/account_reports/models/res_partner.py:83
#: code:addons/account_reports/models/res_partner.py:185
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.model,name:account_reports.model_account_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#, python-format
msgid "Partner Ledger"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__partner_ledger_label
msgid "Partner Ledger Label"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partners:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:18
#, python-format
msgid "Payable"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "Percents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Posted Entries Only"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Previous Period"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr ""

#. module: account_reports
#: model:ir.actions.report,name:account_reports.action_report_followup
msgid "Print Follow-up Letter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid "Print On New Page"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:701
#, python-format
msgid "Print Preview"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:7
#, python-format
msgid "Print letter"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_account_reports_customer_statements_do_followup
msgid "Process Follow-ups"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
msgid "Profit And Loss"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_1
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_1
msgid "Profit and Loss"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:18
#, python-format
msgid "Receivable"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:32
#, python-format
msgid "Ref"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:21
#: code:addons/account_reports/models/account_analytic_report.py:23
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:23
#, python-format
msgid "Reference"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:13
#, python-format
msgid "Remind me later"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Definition"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__report_name
msgid "Report Name"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Same Period Last Year"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Save"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Search account"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__name
msgid "Section Name"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid ""
"Select this option if you want to manually define the next reminder date."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Send an email"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:10
#, python-format
msgid "Send by email"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__tax_report
msgid ""
"Set to True to automatically filter out journal items that have the boolean "
"field 'tax_exigible' set to False"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__debit_credit
msgid "Show Credit and Debit Columns"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__show_domain
msgid "Show Domain"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "Show unfold all filter"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:31
#, python-format
msgid "Source Document"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__special_date_changer
msgid "Special Date Changer"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Start Date :"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__summary
msgid "Summary"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:19
#: code:addons/account_reports/models/account_generic_tax_report.py:22
#, python-format
msgid "TAX"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:432
#, python-format
msgid "Tax Amount"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:423
#, python-format
msgid "Tax Declaration"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:152
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__tax_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#, python-format
msgid "Tax Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_partner__payment_next_action_date
msgid "The date before which no action should be taken."
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:30
#, python-format
msgid "The follow-up report was successfully emailed!"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__generated_menu_id
msgid "The menu item generated for this report, or None if there isn't any."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_followup_report
msgid ""
"The next reminder date is computed based on what you have defined in the "
"accounting settings."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "There are"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Financial Year"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Month"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Quarter"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Today"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:22
#: code:addons/account_reports/models/account_aged_partner_balance.py:78
#: code:addons/account_reports/models/account_consolidated_journals.py:41
#: code:addons/account_reports/models/account_consolidated_journals.py:73
#: code:addons/account_reports/models/account_financial_report.py:1058
#: code:addons/account_reports/models/account_financial_report.py:1141
#: code:addons/account_reports/models/account_general_ledger.py:403
#: code:addons/account_reports/models/account_general_ledger.py:415
#: code:addons/account_reports/models/account_partner_ledger.py:219
#: code:addons/account_reports/models/account_report_coa.py:52
#: code:addons/account_reports/models/account_report_coa.py:113
#: model_terms:ir.ui.view,arch_db:account_reports.customer_statements_tree_view
#: model_terms:ir.ui.view,arch_db:account_reports.template_coa_table_header
#, python-format
msgid "Total"
msgstr "សរុប​"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:394
#, python-format
msgid "Total "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:35
#: code:addons/account_reports/models/account_followup_report.py:108
#: model:ir.model.fields,field_description:account_reports.field_res_partner__total_due
#, python-format
msgid "Total Due"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:119
#: model:ir.model.fields,field_description:account_reports.field_res_partner__total_overdue
#, python-format
msgid "Total Overdue"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:42
#, python-format
msgid "Total Virtual GL Balance"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:149
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
#, python-format
msgid "Trial Balance"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__figure_type
msgid "Type"
msgstr "ប្រភេទ"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:45
#: code:addons/account_reports/models/account_financial_report.py:905
#, python-format
msgid "Undefined"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:189
#, python-format
msgid "Unexplained Difference"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold All"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Unfolded"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unreconciled"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:166
#, python-format
msgid "Unreconciled Bank Statement Lines"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "Use given dates"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:174
#, python-format
msgid "Validated Payments not Linked with a Bank Statement Line"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:85
#, python-format
msgid "Vendor Ledger"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bank Statement"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bill"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_followup_report
msgid "View Invoice"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_followup_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "View Journal Entry"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Partner"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Payment"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:151
#, python-format
msgid "Virtual GL Balance"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid ""
"When checked this line and everything after it will be printed on a new "
"page."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "With Draft Entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.followup_search_template
msgid "With Overdue Invoices"
msgstr ""

#. module: account_reports
#: selection:res.partner,followup_status:0
msgid "With overdue invoices"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/followup_form/followup_form_controller.js:69
#, python-format
msgid "You are done with the follow-ups!<br/>You have skipped %s partner(s)."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:147
#, python-format
msgid "any"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "days between each reminder"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__analytic
msgid "display the analytic filters"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__comparison
msgid "display the comparison filter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__show_journal_filter
msgid "display the journal filter in the report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__cash_basis
msgid "display the option to switch to cash basis mode"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "display the unfold all options in report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:923
#, python-format
msgid "n/a"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_manager__report_name
msgid "name of the model of the report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "prior or included in this period."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__date_range
msgid "specify if the report use date_range or single date"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "to:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "unposted Journal Entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "⇒ journal items"
msgstr ""
