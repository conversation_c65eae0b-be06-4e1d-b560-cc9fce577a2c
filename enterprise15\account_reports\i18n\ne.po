# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_reports
# 
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2018
# La<PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-21 17:04+0000\n"
"PO-Revision-Date: 2017-11-21 17:04+0000\n"
"Last-Translator: Lax<PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Nepali (https://www.transifex.com/odoo/teams/41243/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:28
#, python-format
msgid " Date "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:29
#, python-format
msgid " Due Date "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:32
#, python-format
msgid " Excluded "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:31
#, python-format
msgid " Expected Date "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:33
#, python-format
msgid " Total Due "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:203
#, python-format
msgid "%s Payment Reminder"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "&amp;nbsp;"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:19
#, python-format
msgid "&times;"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:156
#: code:addons/account_reports/models/account_financial_report.py:158
#, python-format
msgid "(copy)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#, python-format
msgid "0&nbsp;-&nbsp;30"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#, python-format
msgid "30&nbsp;-&nbsp;60"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#, python-format
msgid "60&nbsp;-&nbsp;90"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#, python-format
msgid "90&nbsp;-&nbsp;120"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<b>Accounts</b><br/>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<b>Tags</b><br/>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "<span class=\"fa fa-bar-chart\"/> Comparison: <br/>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Journals:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_multi_company
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Companies:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.followup_search_template
msgid "<span class=\"fa fa-filter\"/> Partners:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid ""
"<span class=\"fa fa-folder-open\"/> Analytic\n"
"            <span class=\"caret\"/>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid ""
"<span class=\"fa fa-user\"/>\n"
"            Account:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.partner_view_buttons
msgid "<span class=\"o_stat_text\">Due</span>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Latest Statement\">नवीनतम विवरण </span>"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid ""
"<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_followup_report_all
msgid "A progress bar for followup reports"
msgstr ""

#. module: account_reports
#: sql_constraint:account.financial.html.report.line:0
msgid "A report line with the same code already exists."
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:33
#, python-format
msgid "Account"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report Line"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Account Tags"
msgstr "खाता ट्यागहरू"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Accrual Basis,"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Accrual Basis<br/>"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_action_id
msgid "Action"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_prepayments_received0
msgid "Advance Payments received from customers"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_paid0
msgid "Advance payments made to suppliers"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner
msgid "Aged Partner Balances"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:119
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.model,name:account_reports.model_account_aged_payable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
#, python-format
msgid "Aged Payable"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:98
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.model,name:account_reports.model_account_aged_receivable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
#, python-format
msgid "Aged Receivable"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_analytic
msgid "Allow analytic filter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_comparison
msgid "Allow comparison"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_show_journal_filter
msgid "Allow filtering by journals"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Always"
msgstr "सँधै"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:23
#, python-format
msgid "Amount"
msgstr "राशि"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_template
msgid "Annotate"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Apply"
msgstr "लागू गर्नुहोस्"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:493
#, python-format
msgid "As of %s"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "At the beginning of the period"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Audit"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Auto"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
msgid "BALANCE SHEET"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#: code:addons/account_reports/models/account_general_ledger.py:35
#: code:addons/account_reports/models/account_partner_ledger.py:39
#, python-format
msgid "Balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_2
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_2
msgid "Balance Sheet"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:164
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:165
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank reconciliation report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:377
#, python-format
msgid "Base Amount"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_date_range
msgid "Based on date ranges"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "CASH"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Cash Basis"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Cash Basis Method"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Cash Basis,"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_cashsummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_3
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_3
msgid "Cash Flow Statement"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_opening_balance0
msgid "Cash and cash equivalents, beginning of period"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_closing_balance0
msgid "Cash and cash equivalents, closing balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing0
msgid "Cash flows from financing activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing0
msgid "Cash flows from investing & extraordinary activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_operating0
msgid "Cash flows from operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified0
msgid "Cash flows from unclassified activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing_in0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing_in0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified_in0
msgid "Cash in"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing_out0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing_out0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified_out0
msgid "Cash out"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_spent0
msgid "Cash paid for"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_received0
msgid "Cash received from customers"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_template_followup_report
msgid "Change expected payment date/note"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_children_ids
msgid "Children"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.main_template
msgid "Click to add an introductory explanation"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_code
msgid "Code"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:30
#: code:addons/account_reports/models/account_general_ledger.py:30
#, python-format
msgid "Communication"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
msgid "Companies"
msgstr "कम्पनीहरु"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Companies:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_company_id
msgid "Company"
msgstr "कम्पनी"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:26
#: model:ir.actions.client,name:account_reports.action_account_report_cj
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cj
#, python-format
msgid "Consolidated Journals"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_consolidated_journal
msgid "Consolidated Journals Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "सम्पर्क"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:210
#, python-format
msgid ""
"Could not send mail to partner because it does not have any email address "
"defined"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_create_uid
msgid "Created by"
msgstr "द्वारा सिर्जना गरियो"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_create_date
msgid "Created on"
msgstr "मा सिर्जना गरियो"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#: code:addons/account_reports/models/account_financial_report.py:37
#: code:addons/account_reports/models/account_general_ledger.py:34
#: code:addons/account_reports/models/account_partner_ledger.py:38
#: code:addons/account_reports/models/account_report_coa.py:31
#: code:addons/account_reports/models/account_report_coa.py:36
#: code:addons/account_reports/models/account_report_coa.py:40
#: code:addons/account_reports/models/account_report_coa.py:42
#, python-format
msgid "Credit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:32
#, python-format
msgid "Currency"
msgstr "मुद्रा"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:85
#, python-format
msgid "Current Balance in GL"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Custom"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:29
#, python-format
msgid "Customer Ledger"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Customer Statement"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.menu_action_followups
msgid "Customer Statements"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.followup_filter_info_template
msgid "Customer ref:"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_followup
#: model:ir.actions.client,name:account_reports.action_account_followup_all
msgid "Customers Statement"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_detailed_net_profit0
msgid "DETAILED NET PROFIT"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:21
#: code:addons/account_reports/models/account_general_ledger.py:29
#, python-format
msgid "Date"
msgstr "मिति"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date :"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:184
#: code:addons/account_reports/static/src/js/account_reports.js:232
#, python-format
msgid "Date cannot be empty"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line_next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#: code:addons/account_reports/models/account_financial_report.py:37
#: code:addons/account_reports/models/account_general_ledger.py:33
#: code:addons/account_reports/models/account_partner_ledger.py:37
#: code:addons/account_reports/models/account_report_coa.py:30
#: code:addons/account_reports/models/account_report_coa.py:35
#: code:addons/account_reports/models/account_report_coa.py:39
#: code:addons/account_reports/models/account_report_coa.py:41
#, python-format
msgid "Debit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:75
#, python-format
msgid "Details per month"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Difference"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_consolidated_journal_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_all_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_partner_ledger_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_display_name
msgid "Display Name"
msgstr "नाम प्रदर्शन "

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_hierarchy_option
msgid "Display the hierarchy choice in the report options"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Do it Later"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.followup_filter_info_template
msgid ""
"Document: Customer account statement<br/>\n"
"            Date:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_domain
msgid "Domain"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:42
#, python-format
msgid "Don't follow-up before :"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Done"
msgstr "भयो"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Duration between two reminders"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_hierarchy_option
msgid "Enable the hierarchy option"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End Date :"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Financial Year"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Month"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Quarter"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:156
#, python-format
msgid "Equal Last Statement Balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_4
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_4
msgid "Executive Summary"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:26
#: model:ir.model.fields,field_description:account_reports.field_account_move_line_expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line_expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:436
#, python-format
msgid "Export (XLSX)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_financial_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_financial_report_id
msgid "Financial Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
msgid "Financial Reports"
msgstr "वित्तीय रिपोर्टहरू"

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "Float"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Foldable"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:49
#, python-format
msgid "Follow-ups Done / Total Follow-ups"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:162
#: model:ir.model,name:account_reports.model_account_followup_report
#, python-format
msgid "Followup Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Footnote for reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_footnotes_ids
msgid "Footnotes"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "Force given dates for all accounts and account types"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_formulas
msgid "Formulas"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:494
#, python-format
msgid "From %s <br/> to  %s"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "From the beginning"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "From the beginning of the fiscal year"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "From:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:398
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "General Ledger"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:131
#, python-format
msgid "General Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
msgid "Generic Tax Report"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_groupby
msgid "Group by"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:521
#: code:addons/account_reports/models/account_financial_report.py:605
#, python-format
msgid "Groupby should be a field from account.move.line"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:485
#, python-format
msgid "Groupby should be a journal item field"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_hide_if_zero
msgid "Hide If Zero"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy and Subtotals"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "History <span class=\"caret\"/>"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable_id
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_consolidated_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_id
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_all_id
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger_id
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_partner_ledger_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_id
msgid "ID"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "INCOME"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.followup_search_template
msgid "In Need of Action"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include Unposted Entries"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include unposted entries"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
msgid "Income"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:286
#: code:addons/account_reports/models/account_partner_ledger.py:36
#: model:ir.ui.view,arch_db:account_reports.template_coa_report
#, python-format
msgid "Initial Balance"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:7
#, python-format
msgid "Insert foot note here"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:29
#: code:addons/account_reports/static/src/xml/account_report_template.xml:45
#, python-format
msgid "Insert note here"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line_internal_note
msgid "Internal Note"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_green_on_positive
msgid "Is growth good when positive"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:32
#, python-format
msgid "JRNL"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Journal Items"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_journal_items
msgid "Journal Items by tax"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:29
#, python-format
msgid "Journal Name (Code)"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Journals:"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Financial Year"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_consolidated_journal___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_all___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_partner_ledger___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager___last_update
msgid "Last Modified on"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Month"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Quarter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_write_date
msgid "Last Updated on"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_level
msgid "Level"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_line
msgid "Line"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_ids
msgid "Lines"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Log a Note"
msgstr ""

#. module: account_reports
#: model:mail.message.subtype,name:account_reports.followup_logged_action
msgid "Logged followup action"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_manager_id
msgid "Manager"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Manual"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:35
#, python-format
msgid "Matching Number"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_generated_menu_id
msgid "Menu Item"
msgstr ""

#. module: account_reports
#: model:mail.message.subtype,description:account_reports.followup_logged_action
msgid "Messages created after a followup action has been executed"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Minimum"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings_days_between_two_followups
msgid "Minimum days between two follow-ups"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:154
#, python-format
msgid "Minus Missing Statements"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:112
#, python-format
msgid "Minus Unreconciled Payments"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:139
#, python-format
msgid "Minus Unreconciled Statement Lines"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_regulator0
msgid ""
"Minus previously recorded advance payments (already in the starting balance)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:21
#: code:addons/account_reports/models/account_generic_tax_report.py:24
#, python-format
msgid "NET"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "NET ASSETS"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "NET PROFIT"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:376
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_name
#, python-format
msgid "Name"
msgstr "नाम"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
msgid "Net Profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_net_increase0
msgid "Net increase in cash and cash equivalents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Never"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner_payment_next_action
#: model:ir.model.fields,field_description:account_reports.field_res_users_payment_next_action
msgid "Next Action"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line_next_action_date
#: model:ir.model.fields,field_description:account_reports.field_res_partner_payment_next_action_date
#: model:ir.model.fields,field_description:account_reports.field_res_users_payment_next_action_date
msgid "Next Action Date"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Next Reminder:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:186
#, python-format
msgid "Next action date: "
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "No Comparison"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "No Unit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:557
#, python-format
msgid "No comparison"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "No followup to send !"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#, python-format
msgid "Not&nbsp;due&nbsp;on %s"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:28
#: code:addons/account_reports/static/src/xml/account_report_template.xml:44
#, python-format
msgid "Note"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_partner_payment_next_action
#: model:ir.model.fields,help:account_reports.field_res_users_payment_next_action
msgid "Note regarding the next action."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line_internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company_days_between_two_followups
msgid "Number of days between two follow-ups"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Number of periods :"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#, python-format
msgid "Older"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:38
#, python-format
msgid "One month"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:36
#, python-format
msgid "One week"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Only Show Unreconciled Entries"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:79
#, python-format
msgid "Overdue Payments for %s"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "PERFORMANCE"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "POSITION"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "PROFITABILITY"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_parent_id
msgid "Parent"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_parent_id
msgid "Parent Menu"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:31
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_partner_id
#, python-format
msgid "Partner"
msgstr "साझेदार"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:192
#: code:addons/account_reports/models/res_partner.py:25
#: code:addons/account_reports/models/res_partner.py:88
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.model,name:account_reports.model_account_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#, python-format
msgid "Partner Ledger"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner_partner_ledger_label
#: model:ir.model.fields,field_description:account_reports.field_res_users_partner_ledger_label
msgid "Partner Ledger Label"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:19
#, python-format
msgid "Payable"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Payment Follow-up"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "Percents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:152
#, python-format
msgid "Plus Missing Statements"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:110
#, python-format
msgid "Plus Unreconciled Payments"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:125
#, python-format
msgid "Plus Unreconciled Statement Lines"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Posted Entries Only"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Posted Entries Only,"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Previous Period"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Print Legal Statement"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Print Letter"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:436
#, python-format
msgid "Print Preview"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
msgid "Profit And Loss"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_1
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_1
msgid "Profit and Loss"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:161
#, python-format
msgid "Purchase"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Put back in the list"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "RETAINED EARNINGS"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:19
#, python-format
msgid "Receivable"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:34
#, python-format
msgid "Ref"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:22
#, python-format
msgid "Reference"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_report_name
msgid "Report Name"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:161
#, python-format
msgid "Sale"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Same Period Last Year"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.main_template
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Save"
msgstr "बचत गर्नुहोस्"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_name
msgid "Section Name"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.template_followup_report
msgid "Send by email"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:191
#, python-format
msgid "Sent a followup email"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_sequence
msgid "Sequence"
msgstr "अनुक्रम"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_tax_report
msgid ""
"Set to True to automatically filter out journal items that have the boolean "
"field 'tax_exigible' set to False"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_debit_credit
msgid "Show Credit and Debit Columns"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_show_domain
msgid "Show Domain"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_unfold_all_filter
msgid "Show unfold all filter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_special_date_changer
msgid "Special Date Changer"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Start Date :"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager_summary
msgid "Summary"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:27
#, python-format
msgid "Supplier Ledger"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:21
#: code:addons/account_reports/models/account_generic_tax_report.py:24
#, python-format
msgid "TAX"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:377
#, python-format
msgid "Tax Amount"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:368
#, python-format
msgid "Tax Declaration"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:196
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_tax_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#, python-format
msgid "Tax Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_text
msgid "Text"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_partner_payment_next_action_date
#: model:ir.model.fields,help:account_reports.field_res_users_payment_next_action_date
msgid "The date before which no action should be taken."
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:19
#, python-format
msgid "The followup report was successfully emailed !"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_partner_partner_ledger_label
#: model:ir.model.fields,help:account_reports.field_res_users_partner_ledger_label
msgid "The label to display on partner ledger button, in form view"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_generated_menu_id
msgid "The menu item generated for this report, or None if there isn't any."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:346
#: code:addons/account_reports/models/account_partner_ledger.py:175
#, python-format
msgid ""
"There are more than 80 items in this list, click here to see all of them"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Financial Year"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Month"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Quarter"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Today"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:18
#: code:addons/account_reports/models/account_aged_partner_balance.py:78
#: code:addons/account_reports/models/account_consolidated_journals.py:41
#: code:addons/account_reports/models/account_consolidated_journals.py:62
#: code:addons/account_reports/models/account_financial_report.py:657
#: code:addons/account_reports/models/account_financial_report.py:723
#: code:addons/account_reports/models/account_general_ledger.py:360
#: code:addons/account_reports/models/account_partner_ledger.py:183
#: code:addons/account_reports/models/account_report_coa.py:98
#: model:ir.ui.view,arch_db:account_reports.template_coa_report
#, python-format
msgid "Total"
msgstr "कुल"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:70
#: code:addons/account_reports/models/account_general_ledger.py:339
#, python-format
msgid "Total "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:97
#, python-format
msgid "Total Due"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:109
#, python-format
msgid "Total Overdue"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:134
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
#, python-format
msgid "Trial Balance"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:39
#, python-format
msgid "Two months"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:37
#, python-format
msgid "Two weeks"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_figure_type
msgid "Type"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:556
#, python-format
msgid "Undefined"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold All"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unreconciled"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner_unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users_unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_cash_basis
msgid "Use cash basis"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "Use given dates"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bill"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_caret_options
#: model:ir.ui.view,arch_db:account_reports.line_template_followup_report
msgid "View Invoice"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model:ir.ui.view,arch_db:account_reports.line_template_followup_report
#: model:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
#: model:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "View Journal Entry"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Payment"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "With Draft Entries"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "With Draft Entries,"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.followup_search_template
msgid "With Overdue Invoices"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "You have skipped"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "account.generic.tax.report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "account.report"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "days between each reminder"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_analytic
msgid "display the analytic filter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_comparison
msgid "display the comparison filter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_show_journal_filter
msgid "display the journal filter in the report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_unfold_all_filter
msgid "display the unfold all options in report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_cash_basis
msgid ""
"if true, report will always use cash basis, if false, user can choose from "
"filter inside the report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_manager
msgid "manage summary and footnotes of reports"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:567
#, python-format
msgid "n/a"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_manager_report_name
msgid "name of the model of the report"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "partners"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_date_range
msgid "specify if the report use date_range or single date"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "to:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "⇒ journal items"
msgstr ""
