# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <marcelo.per<PERSON>@arxi.pt>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# 425fe09b3064b9f906f637fff94056ae_a00ea56 <0fa3588fa89906bfcb3a354600956e0e_308047>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 00b3b9e1ff4d86aa7ff38bfe2d3464e6_0137803, 2023
# <AUTHOR> <EMAIL>, 2023
# NumerSpiral HBG, 2024
# Maitê Dietze, 2024
# Arxi, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-18 09:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Peter Lawrence Romão <<EMAIL>>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and %s others"
msgstr " e %s outros"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and one other"
msgstr "e outro"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid " past period(s), previously stored on the corresponding tax line."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "\" account balance is affected by"
msgstr "\" o saldo da conta é afetado por"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(+) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(-) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_unexplained_difference
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(+) Outstanding Receipts"
msgstr "(+) Recebimentos em Aberto"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(-) Outstanding Payments"
msgstr "(-) Pagamentos em Aberto"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(No Group)"
msgstr "(Sem Grupo)"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "(copy)"
msgstr " (cópia)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Atualizar"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period1
msgid "1 - 30"
msgstr "1-30"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period2
msgid "31 - 60"
msgstr "31 - 60"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period3
msgid "61 - 90"
msgstr "61 - 90"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period4
msgid "91 - 120"
msgstr "91 - 120"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "<br/>Companies:"
msgstr "<br/>Empresas:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid ""
"<i class=\"fa fa-caret-right invisible\" role=\"img\" aria-"
"label=\"Unfolded\" title=\"Unfolded\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "<span class=\"fa fa-bar-chart\"/> Comparison:"
msgstr "<span class=\"fa fa-bar-chart\"/> Comparação:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                Tax Report:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Fiscal Position:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"            Posição Fiscal:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"Diários:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "<span class=\"fa fa-calendar\" title=\"Dates\" role=\"img\" aria-label=\"Dates\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Balance is good\" title=\"Balance is good\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is good\" title=\"Partner ledger is good\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Balance is bad\" title=\"Balance is bad\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is bad\" title=\"Partner ledger is bad\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Balance is normal\" title=\"Balance is normal\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partner ledger is normal\" title=\"Partner ledger is normal\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Codes:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Códigos:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Filters:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"Filtros:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_groupby_fields
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Group By:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Agrupar por:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/>Opções:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<span class=\"fa fa-folder-open\"/> Analytic"
msgstr "<span class=\"fa fa-folder-open\"/> Conta Analítica"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<span class=\"fa fa-folder-open\"/> Partners"
msgstr "<span class=\"fa fa-folder-open\"/>Parceiros"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid ""
"<span class=\"fa fa-home\"/>\n"
"                Tax Unit:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Valores registados aqui são "
"distintos por empresa.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
msgid "<span class=\"fa fa-line-chart\"/> Exchange Rates"
msgstr "<span class=\"fa fa-line-chart\"/> Taxas de conversão"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid ""
"<span class=\"fa fa-user\"/>\n"
"            Account:"
msgstr ""
"<span class=\"fa fa-user\"/>\n"
"Contas:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.footnotes_template
msgid ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Country</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Tax Return Periodicity</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Último Extrato\">Último Extrato</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid ""
"<span>Intrastat taxes are applied on unexpected journal entries "
"(intranational or between non intrastat countries).</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>Please note that the report may include some rounding differences "
"towards the bookings.</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid "<span>Some partners are missing a VAT number.</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>This company is part of a tax unit. You're currently not viewing the "
"whole unit. To change that, use the Tax Unit filter.</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "<span>This report only displays the data of the active company.</span>"
msgstr ""

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_financial_html_report_line_code_uniq
msgid "A report line with the same code already exists."
msgstr "Uma linha de relatório com o mesmo código já existe."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "ATIVOS"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_id
#, python-format
msgid "Account"
msgstr "Conta"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_analytic_report
msgid "Account Analytic Report"
msgstr "Relatório de Contas Analíticas"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de Plano de Contas"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_code
msgid "Account Code"
msgstr "Código da Conta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_report_account_report_journal
msgid "Account Journal Report"
msgstr "Relatório de Diários de Conta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_name
msgid "Account Name"
msgstr "Nome da Conta"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr "Relatório Contabilístico"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report (HTML Line)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
msgid "Account Report (HTML)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_accounting_report
msgid "Accounting Report Helper"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Accounts"
msgstr "Contas"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Accounts to adjust"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Accounts without a group"
msgstr "Contas sem um grupo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__action_id
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Ação"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Uma ação pode despoletar comportamentos específicos, como abrir uma vista do"
" calendário ou marcar automaticamente um documento como concluído depois do "
"carregamento."

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Atividade"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipo de Atividade"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Add a note"
msgstr "Adicionar uma nota"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Adicionar totais depois das secções"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_adjustment
msgid "Adjustment"
msgstr "Ajustes"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "Entrada de Ajuste"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance Payments received from customers"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance payments made to suppliers"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner
msgid "Aged Partner Balances"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.model,name:account_reports.model_account_aged_payable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
#, python-format
msgid "Aged Payable"
msgstr "Pagamento Vencido"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Pagamentos Vencidos"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.model,name:account_reports.model_account_aged_receivable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
#, python-format
msgid "Aged Receivable"
msgstr "Recebimentos Vencidos"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Recebimentos Vencidos"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "All"
msgstr "Tudo"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "All Journals"
msgstr "Todos os Diários"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_allocated_earnings
msgid "Allocated Earnings"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__analytic
msgid "Allow analytic filters"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__comparison
msgid "Allow comparison"
msgstr "Permitir comparação"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__show_journal_filter
msgid "Allow filtering by journals"
msgstr "Permitir filtro por diários"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__always
msgid "Always"
msgstr "Sempre"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Amount"
msgstr "Valor"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__amount_currency
#, python-format
msgid "Amount Currency"
msgstr "Moeda do Valor"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_account_id
msgid "Analytic Account"
msgstr "Conta Analítica"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Accounts:"
msgstr "Contas Analíticas:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Analytic Entries"
msgstr "Entradas de Analítica"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_analytic
#: model:ir.ui.menu,name:account_reports.menu_action_report_account_analytic
#, python-format
msgid "Analytic Report"
msgstr "Relatório de Contas Analíticas"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Etiqueta Analítica"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Tags:"
msgstr "Marcadores Analíticos:"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
#, python-format
msgid "Annotate"
msgstr "Anotar"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid "Applicable Filters"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Apply"
msgstr "Aplicar"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "As of %s"
msgstr "Até %s"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period0
msgid "As of: "
msgstr "Até:"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#, python-format
msgid "As of: %s"
msgstr "Até: %s"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__to_beginning_of_period
msgid "At the beginning of the period"
msgstr "No início do período"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Audit"
msgstr "Auditoria"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Relatórios de Auditoria"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Available Filters & Options"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__balance
#, python-format
msgid "Balance"
msgstr "Saldo"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_2
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_2
msgid "Balance Sheet"
msgstr "Balanço"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency_current
msgid "Balance at current rate"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_balance
msgid "Balance at operation rate"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance in GL"
msgstr "Saldo na Conta"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency
msgid "Balance in foreign currency"
msgstr "Saldo em moeda estrangeira"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Balance of %s"
msgstr "Saldo de %s"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax advance payment account"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (payable)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (receivable)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr "Reconciliação Bancária"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Relatório de Reconciliação Bancária"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Bank Reconciliation: %s"
msgstr "Reconciliação Bancária: %s"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Contas Bancárias e Caixa"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Base Amount"
msgstr "Valor Base"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__date_range
msgid "Based on date ranges"
msgstr "Baseado num espaço temporal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid "Both"
msgstr "Ambos"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: account_reports
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "Carryover for period %s to %s"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Numerário"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report
msgid "Cash Flow Report"
msgstr "Relatório de Fluxo de Caixa"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
#, python-format
msgid "Cash Flow Statement"
msgstr "Extrato de Fluxos de Caixa"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, beginning of period"
msgstr "Dinheiro e equivalentes, começo do período"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, closing balance"
msgstr "Dinheiro e equivalentes, fechamento do período"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from financing activities"
msgstr "Fluxos de caixa de atividades de financiamento"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from investing & extraordinary activities"
msgstr "Fluxos de caixa de investimentos e atividades extraordinárias"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from operating activities"
msgstr "Fluxos de caixa de atividades operacionais"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from unclassified activities"
msgstr "Fluxos de caixa de atividades não classificadas"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash in"
msgstr "Entrada de Dinheiro"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash out"
msgstr "Retirada de Dinheiro"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash paid for operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Recebido em dinheiro"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash received from operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Dinheiro gasto"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid "Change expected payment date"
msgstr "Alterar data de pagamento esperada"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr "Relatório do Plano de Contas"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Chart of Accounts"
msgstr "Plano de Contas"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__children_ids
msgid "Children"
msgstr "Descendentes"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr "Linhas Filhas"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Closing Entry"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Saldo de Encerramento Bancário"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__code
msgid "Code"
msgstr "Código"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Code:"
msgstr "Código:"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Communication"
msgstr "Comunicação"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Empresas"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"Company %s already belongs to a tax unit in %s. A company can at most be "
"part of one tax unit per country."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Company Currency:"
msgstr "Moeda da Empresa:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid "Company Only"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Company Settings"
msgstr "Configurações da Empresa"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr "Cálculo"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Computation: %s"
msgstr "Cálculo: %s"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Configure your TAX accounts - %s"
msgstr "Configure as suas contas de impostos - %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cj
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cj
#, python-format
msgid "Consolidated Journals"
msgstr "Diários Consolidados"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_consolidated_journal
msgid "Consolidated Journals Report"
msgstr "Relatório de Diários Consolidados"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__control_domain
msgid "Control Domain"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Control Domain:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Controls failed"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr "Cosmética"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Custo das Vendas"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__country_id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Country"
msgstr "País"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Country Code"
msgstr "Código do País"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Criar Registro"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__credit
#, python-format
msgid "Credit"
msgstr "Crédito"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_id
#, python-format
msgid "Currency"
msgstr "Moeda"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_code
msgid "Currency Code"
msgstr "Código da Moeda"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Currency Rates (%s)"
msgstr "Taxas da Moeda (%s)"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Ativos Correntes"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Passivos Correntes"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr "Lucros Alocados do Ano Corrente"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr "Lucros do Ano em Curso"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Lucros Não Alocados do Ano Corrente"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Ativos correntes para passivos"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Custom"
msgstr "Personalizado"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#, python-format
msgid "Date"
msgstr "Data"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date :"
msgstr "Data :"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Date cannot be empty"
msgstr "Data não pode ser vazia"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""
"Data quando a próxima ação deverá ser tomada para um item a recebimento. "
"Normalmente, é definida através dos lembretes enviados no extrato de "
"cliente."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date:"
msgstr "Data:"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__debit
#, python-format
msgid "Debit"
msgstr "Débito"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr "Depreciações"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Details per month"
msgstr "Detalhes por mês"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Difference"
msgstr "Diferença"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Nome"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_type
msgid "Display Type"
msgstr "Tipo de exibição"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Nome do Documento"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__domain
msgid "Domain"
msgstr "Domínio"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Domain:"
msgstr "Domínio:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "Domestic"
msgstr "Domestic"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Domestic country of your accounting"
msgstr "País da sua contabilidade"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Transferir Relatório de Verificação de Inalterabilidade de Dados"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_date
#, python-format
msgid "Due Date"
msgstr "Data de Vencimento"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.model,name:account_reports.model_account_sales_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
#, python-format
msgid "EC Sales List"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "CAPITAL PRÓPRIO"

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "End Balance"
msgstr "Saldo Final"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End Date :"
msgstr "Data fim:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Financial Year"
msgstr "Fim do último ano fiscal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Month"
msgstr "Fim do mês passado"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Quarter"
msgstr "Fim do último Trimestre"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Error while validating the domain of line %s:\n"
"%s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Excess Journal Items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude"
msgstr "Excluir"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude From Aged Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude from adjustment/provisions entries"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude this account from aged reports"
msgstr "Excluir esta conta do relatório de dívida antiga"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Excluded Accounts"
msgstr "Contas Excluídas"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_3
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_3
msgid "Executive Summary"
msgstr "Resumo Executivo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__expected_pay_date
msgid "Expected Date"
msgstr "Data prevista"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr "Data de pagamento esperada"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s for invoice %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""
"Data de pagamento expectável, conforme gestão manual da conta corrente de "
"cliente (p.e., se falar com o cliente por telefone e pretender ser recordado"
" da data em que houve a promessa de pagamento)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Expense Provision for {for_cur}"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense account"
msgstr "Conta de despesa"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr "Despesas"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Export"
msgstr "Exportar"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Exportar para"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Filter:"
msgstr "Filtro:"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid ""
"Filters that can be used to filter and group lines in this report. This uses"
" saved filters on journal items."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__financial_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__financial_report_id
msgid "Financial Report"
msgstr "Relatório Financeiro"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:account_reports.menu_account_financial_reports_tree
msgid "Financial Reports"
msgstr "Relatórios Financeiros"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "País Fiscal"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__float
msgid "Float"
msgstr "Fracionário"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__foldable
msgid "Foldable"
msgstr "Dobrável"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Folded"
msgstr "Dobrado"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__footnotes_ids
msgid "Footnotes"
msgstr "Notas de rodapé"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__strict_range
msgid "Force given dates for all accounts and account types"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Foreign currencies adjustment entry as of %s"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Formula:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__formulas
msgid "Formulas"
msgstr "Fórmulas"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"From %s\n"
"to  %s"
msgstr ""
"De %s\n"
"para  %s"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_beginning
msgid "From the beginning"
msgstr "Desde o início"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_fiscalyear
msgid "From the beginning of the fiscal year"
msgstr "Desde o início do ano fiscal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "From:"
msgstr "De:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "General Ledger"
msgstr "Registo Geral"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Relatório de Registo Geral"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "General Report"
msgstr "Relatório Geral"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Documentos Gerados"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr "Relatório de Impostos Genérico"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Global Summary"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Goods"
msgstr "Bens"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Grid"
msgstr "Grelha"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Lucro Bruto"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Lucro bruto"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Margem de lucro bruto (lucro bruto / renda operacional)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__groupby
msgid "Group by"
msgstr "Agrupar por"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Account &gt; Tax"
msgstr "Agrupar por: Conta &gt; Imposto"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Tax &gt; Account"
msgstr "Agrupar por: Imposto &gt; Conta"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Groupby field %s is invalid on line with name '%s'"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Groupby:"
msgstr "Agrupar por:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_empty
msgid "Hide If Empty"
msgstr "Esconder se estiver vazio"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_zero
msgid "Hide If Zero"
msgstr "Esconder se estiver zerado"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy"
msgstr "Hierarquia"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy and Subtotals"
msgstr "Hierarquia e Subtotais"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Com que frequência declarações de imposto devem ser feitas"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impact On Grid"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impacted Tax Grids"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include"
msgstr "Incluir"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include Unposted Entries"
msgstr "Incluir Movimentos Não Publicados"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include in adjustment/provisions entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include unposted entries"
msgstr "Incluir movimentos não lançados"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Payments"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Receipts"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr "Rendimentos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Conta de rendimentos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Income Provision for {for_cur}"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Inconsistent Statements"
msgstr "Extratos inconsistentes"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "Initial Balance"
msgstr "Saldo de Abertura"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Insert foot note here"
msgstr "Inserir notas de rodapé aqui"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__internal_note
msgid "Internal Note"
msgstr "Nota Interna"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__green_on_positive
msgid "Is growth good when positive"
msgstr "O crescimento é bom quando positivo"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "JRNL"
msgstr "DIÁR"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Diário"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Lançamento de Diário"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Groups"
msgstr "Grupos de Diários"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Item do Diário"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "Journal Items"
msgstr "Itens do Diário"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items (%s)"
msgstr "Itens do Diário (%s)"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items for Tax Audit"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal Items on the"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Journal Name (Code)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal items on the"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_print_journal_menu
#: model:ir.ui.menu,name:account_reports.menu_print_journal
msgid "Journals Audit"
msgstr "Auditoria de Diários"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Journals:"
msgstr "Diários:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "PASSIVOS"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "PASSIVOS + CAPITAL PRÓPRIO"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Label"
msgstr "Descrição"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Financial Year"
msgstr "Último ano fiscal"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Month"
msgstr "Mês Passado"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Quarter"
msgstr "Último Trimestre"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_link_last_statement
msgid "Last Statement:"
msgstr "Último Extrato"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__level
msgid "Level"
msgstr "Nivel"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line
msgid "Line"
msgstr "Linha"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__line_ids
msgid "Lines"
msgstr "Linhas"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Load more... (%s remaining)"
msgstr "Carregar mais... (%s restantes)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Empresa Principal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr "Informação principal"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#, python-format
msgid "Make Adjustment Entry"
msgstr "Criar um ajuste"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__manager_id
msgid "Manager"
msgstr "Gestor"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Matching Number"
msgstr "Número do Matching"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "Membros desta unidade"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__generated_menu_id
msgid "Menu Item"
msgstr "Ítem de Menu"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Missing Journal Items"
msgstr "Ítens do Diário Faltantes"

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr "Módulo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_id
msgid "Move"
msgstr "Movimento"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_name
msgid "Move Name"
msgstr "Nome do Movimento"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_ref
msgid "Move Ref"
msgstr "Ref do Movimento"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_type
msgid "Move Type"
msgstr "Tipo de Movimento"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation
msgid "Multicurrency Revaluation Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for fiscal position %s after %s. There should be at most one. \n"
" %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %s. There should be at most one. \n"
" %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "NET"
msgstr "LÍQUIDO"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#, python-format
msgid "Name"
msgstr "Nome"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Name:"
msgstr "Nome:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "Net Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Ativos Líquidos"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Net increase in cash and cash equivalents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__never
msgid "Never"
msgstr "Nunca"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__next_action_date
msgid "Next Action Date"
msgstr "Data da Próxima Ação"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "No Comparison"
msgstr "Sem Comparação"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__no_unit
msgid "No Unit"
msgstr "Sem Unidade"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "No VAT number associated with your company. Please define one."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No adjustment needed"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No provision needed was found."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid "None"
msgstr "Nenhum(a)"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""
"Tome nota que poderá definir através do extrato de cliente um item do diário"
" de recebimentos"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Number of periods :"
msgstr "Número de períodos:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo - Aviso"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period5
msgid "Older"
msgstr "Mais antigo"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "One of the formats chosen can not be exported in the DMS"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "One or more partners has no VAT Number."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Only Show Unreconciled Entries"
msgstr "Mostrar Apenas Movimentos Não Reconciliados"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Saldo Inicial do Ano Fiscal"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr "Outros Rendimentos"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
#, python-format
msgid "Outstanding Payments/Receipts"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_id
msgid "Parent"
msgstr "Ascendente"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__parent_id
msgid "Parent Menu"
msgstr "Menu Ascendente"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_path
msgid "Parent Path"
msgstr "Caminho ascendente "

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_id
#, python-format
msgid "Partner"
msgstr "Parceiro"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partner Categories:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/res_partner.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.model,name:account_reports.model_account_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
#, python-format
msgid "Partner Ledger"
msgstr "Registo de Parceiro"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_name
msgid "Partner Name"
msgstr "Nome do Parceiro"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_trust
msgid "Partner Trust"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Partners"
msgstr "Parceiros"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partners:"
msgstr "Parceiros: "

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Payable"
msgstr "A Pagar"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Payable tax amount"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "A Pagar"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__payment_id
msgid "Payment"
msgstr "Pagamento"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__percents
msgid "Percents"
msgstr "Percentagens"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Desempenho"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Periodicity"
msgstr "Periodicidade"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Please specify a Group by field when using '%s' in Formulas, on line with "
"name '%s'"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Mais Ativos Tangíveis"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Mais Ativos Não Correntes"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Mais Passivos Não Correntes"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Situação"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Posted Entries Only"
msgstr "Apenas Movimentos Lançados"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Adiantamentos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Pré-visualizar dados"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Previous Period"
msgstr "Período Anterior"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Lucros Não Alocados de Anos Anteriores"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid "Print On New Page"
msgstr "Imprimir em uma página nova"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
msgid "Profit And Loss"
msgstr "Lucro e prejuízo"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_1
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_1
msgid "Profit and Loss"
msgstr "Ganhos e Perdas"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Rentabilidade"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Provision for {for_cur} (1 {comp_cur} = {rate} {for_cur})"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Receivable"
msgstr "A Receber"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Receivable tax amount"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Recebíveis"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "Reconcile"
msgstr "Reconciliar"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Relatório de Reconciliação"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Reference"
msgstr "Referência"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Related Company"
msgstr "Empresa Relacionada"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Lembrete"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_currency_id
msgid "Report Currency"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Definition"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_include
msgid "Report Include"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr "Linha do Relatório"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Report Line Computation"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr "Linhas do Relatório"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_model
msgid "Report Model"
msgstr "Modelo de Relatório"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__report_name
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Name"
msgstr "Nome do Relatório"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Relatórios"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Lucros Retidos"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Data da Reversão"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Reversão de: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Same Period Last Year"
msgstr "Mesmo período ano passado"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid "Search account"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_partner
msgid "Search partner"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__name
msgid "Section Name"
msgstr "Nome da Secção"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Services"
msgstr "Serviços"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__tax_report
msgid ""
"Set to True to automatically filter out journal items that are not tax "
"exigible."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_coa
msgid "Setup"
msgstr "Configuração"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__show_domain
msgid "Show Domain"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "Show unfold all filter"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Some controls failed"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"Some of your tax groups are missing information in company %s. Please "
"complete their configuration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__special_date_changer
msgid "Special Date Changer"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__control_domain
msgid ""
"Specify a control domain that will raise a warning if the report line is not"
" computed correctly."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Start Date :"
msgstr "Data de início:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Começar de"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__summary
msgid "Summary"
msgstr "Sumário"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "TAX"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Tags"
msgstr "Etiquetas"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Amount"
msgstr "Valor de Imposto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_end_date
msgid "Tax Closing End Date"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Declaration"
msgstr "Declaração de Impostos"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Tax Grid"
msgstr "Grade de impostos"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "NIF"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Paid Adjustment"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Received Adjustment"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__tax_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
#, python-format
msgid "Tax Report"
msgstr "Relatório de Imposto"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_report_control_error
msgid "Tax Report Control Error"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Tax Return"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return for %s%s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return from %s to %s%s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_closing_end_date
msgid ""
"Technical field used for VAT closing, containig the end date of the period "
"this entry closes."
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr "Texto"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "The Book balance in Odoo dated today"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The amount will be : %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The carried over balance will be : %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__country_id
msgid "The country this report is intended to."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr "O país de onde usar os relatórios de impostos para esta empresa"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"The current balance in the General Ledger %s doesn't match the balance of "
"your last bank statement %s leading to an unexplained difference of %s."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The difference will be carried over to the next period's declaration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid "The main company of a tax unit has to be part of it."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__generated_menu_id
msgid "The menu item generated for this report, or None if there isn't any."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "There are"
msgstr "Existem"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "There are some"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Financial Year"
msgstr "Este ano fiscal"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Month"
msgstr "Este Mês"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Quarter"
msgstr "Este Trimestre"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be increased by the positive amount from"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be reduced by the negative amount from"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be set to %s."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This period is already closed for company %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Today"
msgstr "Hoje"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Total %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions that were entered into Odoo, but not yet reconciled (Payments "
"triggered by invoices/bills or manually)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(+) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by invoices/refunds or manually)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(-) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by bills/credit notes or manually)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
#, python-format
msgid "Trial Balance"
msgstr "Balancete Geral"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Triangular"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__figure_type
msgid "Type"
msgstr "Tipo"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Lucros Não Alocados"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Undefined"
msgstr "Indefinido"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Unexplained Difference"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold All"
msgstr "Desdobrar Tudo"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Unfolded"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Parceiro desconhecido"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
#, python-format
msgid "Unrealized Currency Gains/Losses"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unreconciled"
msgstr "Não Reconciliado"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__normal
msgid ""
"Use the dates that should normally be used, depending on the account types"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "VAT"
msgstr "NIF"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_company_form
msgid "VAT Units"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Vat closing from %s to %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bank Statement"
msgstr "Ver extrato bancário"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Carryover Lines"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Journal Entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Journal Entry"
msgstr "Ver Lançamentos do Diário"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Partner"
msgstr "Ver Contacto"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Payment"
msgstr "Ver pagamento"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid ""
"When checked this line and everything after it will be printed on a new "
"page."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "With Draft Entries"
msgstr "Com Movimentos Rascunho"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "XLSX"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "You are using custom exchange rates."
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "any"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "dias após o período"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__analytic
msgid "display the analytic filters"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__comparison
msgid "display the comparison filter"
msgstr "apresentar o filtro de comparação"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__show_journal_filter
msgid "display the journal filter in the report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "display the unfold all options in report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "following accounts"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "having a starting balance different than the previous ending balance"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "journal items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be listed in an incorrect section of the report."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be missing from the proper section of the report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_manager__report_name
msgid "name of the model of the report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "prior or included in this period"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__date_range
msgid "specify if the report use date_range or single date"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "statements"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_report_control_error
msgid "technical field used to know if there was a failed control check"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "to:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "unposted Journal Entries"
msgstr "Entradas do Diário não lançadas"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "which doesn't result from a bank statement nor payments."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ General Ledger"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ Rates"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "⇒ Reset to Odoo’s Rate"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "⇒ journal items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Some journal items appear to point to obsolete report lines."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Check them"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "and correct their tax tags if necessary."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Journal items with archived tax tags"
msgstr ""
