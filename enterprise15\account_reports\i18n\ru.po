# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# Андрей Гу<PERSON>ев <<EMAIL>>, 2021
# Константи<PERSON>н <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# valmasone, 2022
# <AUTHOR> <EMAIL>, 2022
# Ye <PERSON> <<EMAIL>>, 2023
# alenafairy, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-18 09:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and %s others"
msgstr " и %s другие"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid " and one other"
msgstr " и еще один"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid " past period(s), previously stored on the corresponding tax line."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "\" account balance is affected by"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(+) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"%s for Transactions(-) imported from your online bank account (dated today) "
"that are not yet reconciled in Odoo (Waiting the final reconciliation "
"allowing finding the right account)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_unexplained_difference
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(+) Outstanding Receipts"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "(-) Outstanding Payments"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "(No Group)"
msgstr "(Нет группы)"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "(copy)"
msgstr "(копия)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Обновить"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period1
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period1
msgid "1 - 30"
msgstr "1 - 30"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period2
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period2
msgid "31 - 60"
msgstr "31 - 60"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period3
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period3
msgid "61 - 90"
msgstr "61 - 90"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period4
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period4
msgid "91 - 120"
msgstr "91 - 120"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "<br/>Companies:"
msgstr "<br/> компании:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid ""
"<i class=\"fa fa-caret-right invisible\" role=\"img\" aria-"
"label=\"Unfolded\" title=\"Unfolded\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "<span class=\"fa fa-bar-chart\"/> Comparison:"
msgstr "<span class=\"fa fa-bar-chart\"/> Сравнение:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"                Tax Report:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"                 Налоговый отчет:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Fiscal Position:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_journals
msgid ""
"<span class=\"fa fa-book\"/>\n"
"            Journals:"
msgstr ""
"<span class=\"fa fa-book\"/>\n"
"            Журналы:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "<span class=\"fa fa-calendar\" title=\"Dates\" role=\"img\" aria-label=\"Dates\"/>"
msgstr "<span class=\"fa fa-calendar\" title=\"Dates\" role=\"img\" aria-label=\"Dates\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Balance is good\" title=\"Balance is good\"/>"
msgstr ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Balance is good\" title=\"Balance is good\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is good\" title=\"Partner ledger is good\"/>"
msgstr ""
"<span class=\"fa fa-circle color-green trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is good\" title=\"Partner ledger is good\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Balance is bad\" title=\"Balance is bad\"/>"
msgstr ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Balance is bad\" title=\"Balance is bad\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is bad\" title=\"Partner ledger is bad\"/>"
msgstr ""
"<span class=\"fa fa-circle color-red trust-partner\" role=\"img\" aria-"
"label=\"Partner ledger is bad\" title=\"Partner ledger is bad\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Balance is normal\" title=\"Balance is normal\"/>"
msgstr ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Balance is normal\" title=\"Balance is normal\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partner ledger is normal\" title=\"Partner ledger is normal\"/>"
msgstr ""
"<span class=\"fa fa-circle color-transparent trust-partner\" role=\"img\" "
"aria-label=\"Partner ledger is normal\" title=\"Partner ledger is normal\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Codes:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Filters:"
msgstr ""
"<span class=\"fa fa-filter\"/>\n"
"            Фильтры:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_groupby_fields
msgid ""
"<span class=\"fa fa-filter\"/>\n"
"            Group By:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "<span class=\"fa fa-filter\"/> Options:"
msgstr "<span class=\"fa fa-filter\"/>Опции:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_analytic
msgid "<span class=\"fa fa-folder-open\"/> Analytic"
msgstr "<span class=\"fa fa-folder-open\"/> Аналитика"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_partner
msgid "<span class=\"fa fa-folder-open\"/> Partners"
msgstr "<span class=\"fa fa-folder-open\"/> Партнеры"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid ""
"<span class=\"fa fa-home\"/>\n"
"                Tax Unit:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Установленные здесь значения "
"зависят от конкретной компании.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
msgid "<span class=\"fa fa-line-chart\"/> Exchange Rates"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid ""
"<span class=\"fa fa-user\"/>\n"
"            Account:"
msgstr ""
"<span class=\"fa fa-user\"/>\n"
"            Счёт:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.footnotes_template
msgid ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"
msgstr ""
"<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-"
"trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Fiscal Country</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Tax Return Periodicity</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"o_form_label\">Периодичность налоговой декларации</span>\n"
"                        <span class=\"fa fa-lg fa-building-o\" title=\"Значения зависят от выбранной компании.\" role=\"img\" aria-label=\"Значения зависят от выбранной компании.\" groups=\"base.group_multi_company\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"
msgstr ""
"<span class=\"searchIcon\"><i class=\"fa fa-search\" role=\"img\" aria-"
"label=\"Search\" title=\"Search\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "<span title=\"Latest Statement\">Latest Statement</span>"
msgstr "<span title=\"Последняя проводка\">Последняя проводка</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid ""
"<span>Intrastat taxes are applied on unexpected journal entries "
"(intranational or between non intrastat countries).</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>Please note that the report may include some rounding differences "
"towards the bookings.</span>"
msgstr ""
"<span>Обратите внимание, что в отчет могут быть включены некоторые "
"округленные разницы в отношении бронирований.</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_sales_report_main_template
msgid "<span>Some partners are missing a VAT number.</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_tax_report
msgid ""
"<span>This company is part of a tax unit. You're currently not viewing the "
"whole unit. To change that, use the Tax Unit filter.</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "<span>This report only displays the data of the active company.</span>"
msgstr ""

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_financial_html_report_line_code_uniq
msgid "A report line with the same code already exists."
msgstr "Строка отчета с таким же кодом уже существует."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""
"Налоговая единица может быть создана только между компаниями, использующими "
"одну и ту же основную валюту."

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "АКТИВЫ"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_id
#, python-format
msgid "Account"
msgstr "Счёт"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_analytic_report
msgid "Account Analytic Report"
msgstr "Аналитический отчет"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Шаблон плана счетов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_code
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_code
msgid "Account Code"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "Представитель по отображению счетов Поле"

#. module: account_reports
#: model:ir.model,name:account_reports.model_report_account_report_journal
msgid "Account Journal Report"
msgstr "Отчет журнала счета"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__account_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__account_name
msgid "Account Name"
msgstr "Название счёта"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr "Отчёт по счёту"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report (HTML Line)"
msgstr "Отчет счета (Строка HTML)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
msgid "Account Report (HTML)"
msgstr "Отчет счета (HTML)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr "Примечание отчета счета"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "Представленная компания"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "Журнал переоценки счетов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "Бухгалтерская фирма"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_accounting_report
msgid "Accounting Report Helper"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Accounts"
msgstr "Cчета"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Accounts to adjust"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Accounts without a group"
msgstr "Счета без группы"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__action_id
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Действие"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Действия могут вызывать определенное поведение, например, открывать "
"календарь или автоматически отмечать как выполнено при загрузке документа."

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Действие"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Тип действия"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Add a note"
msgstr "Добавить заметку"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Добавить итоги ниже разделов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_adjustment
msgid "Adjustment"
msgstr "Настройка"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "Корректирующая проводка"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance Payments received from customers"
msgstr "Авансовые платежи, полученные от клиентов"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Advance payments made to suppliers"
msgstr "Авансовые платежи, сделанные поставщикам"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner
msgid "Aged Partner Balances"
msgstr "Возрастные балансы партнеров"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.model,name:account_reports.model_account_aged_payable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
#, python-format
msgid "Aged Payable"
msgstr "Сроки оплат"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Сроки оплат"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.model,name:account_reports.model_account_aged_receivable
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
#, python-format
msgid "Aged Receivable"
msgstr "Сроки платежей"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Сроки платежей"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ec_sale_code
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "All"
msgstr "Все"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "All Journals"
msgstr "Все журналы"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_allocated_earnings
msgid "Allocated Earnings"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__analytic
msgid "Allow analytic filters"
msgstr "Добавить аналитические фильтры"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__comparison
msgid "Allow comparison"
msgstr "Разрешить сравнения"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__show_journal_filter
msgid "Allow filtering by journals"
msgstr "Позволить фильтровать по журналам"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__always
msgid "Always"
msgstr "Всегда"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Amount"
msgstr "Сумма"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__amount_currency
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__amount_currency
#, python-format
msgid "Amount Currency"
msgstr "Сумма в валюте"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_account_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_account_id
msgid "Analytic Account"
msgstr "Аналитический счёт"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Accounts:"
msgstr "Аналитические счета:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Analytic Entries"
msgstr "Аналитические проводки"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_analytic
#: model:ir.ui.menu,name:account_reports.menu_action_report_account_analytic
#, python-format
msgid "Analytic Report"
msgstr "Аналитический отчет"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__analytic_tag_ids
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__analytic_tag_ids
msgid "Analytic Tag"
msgstr "Тег аналитики"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Analytic Tags:"
msgstr "Аналитические теги:"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
#, python-format
msgid "Annotate"
msgstr "Комментировать"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid "Applicable Filters"
msgstr "применены фильтры"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_currency
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Apply"
msgstr "Применить"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "As of %s"
msgstr "На %s"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period0
msgid "As of: "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#, python-format
msgid "As of: %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__to_beginning_of_period
msgid "At the beginning of the period"
msgstr "В начале периода"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "Audit"
msgstr "Аудит"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Отчеты аудита"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Available Filters & Options"
msgstr "Доступные фильтры и функции"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Средний срок погашения кредиторской задолженности"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Средний срок погашения дебиторской задолженности"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__balance
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__balance
#, python-format
msgid "Balance"
msgstr "Баланс"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_2
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_2
msgid "Balance Sheet"
msgstr "Балансовая ведомость"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency_current
msgid "Balance at current rate"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_balance
msgid "Balance at operation rate"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance in GL"
msgstr "Сальдо"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_amount_currency
msgid "Balance in foreign currency"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Balance of %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax advance payment account"
msgstr "Сальдо счета авансовых платежей по налогу"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (payable)"
msgstr "Расчетный счет (кредиторская задолженность) по налогу на прибыль"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Balance tax current account (receivable)"
msgstr "Сальдо налогового расчетного счета (дебиторская задолженность)"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr "Сверка с банком"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Отчет согласования банковской выписки"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Bank Reconciliation: %s"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Банковские и Кассовые Счета"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Base Amount"
msgstr "Базовая сумма"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__date_range
msgid "Based on date ranges"
msgstr "На основании диапазонов"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_account_type
msgid "Both"
msgstr "Оба"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Cancel"
msgstr "Отмена"

#. module: account_reports
#: code:addons/account_reports/models/account_move.py:0
#, python-format
msgid "Carryover for period %s to %s"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Наличные"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report
msgid "Cash Flow Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
#, python-format
msgid "Cash Flow Statement"
msgstr "Отчет о движении денежных средств"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, beginning of period"
msgstr "Денежные средства и их эквиваленты, начало периода"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash and cash equivalents, closing balance"
msgstr "Денежные средства и их эквиваленты, конечное сальдо"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from financing activities"
msgstr "Денежные потоки от финансовой деятельности"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from investing & extraordinary activities"
msgstr "Потоки наличности от инвестирования и экстраординарных действий"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from operating activities"
msgstr "Денежные потоки от операционной деятельности"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash flows from unclassified activities"
msgstr "Денежные потоки от неклассифицированной деятельности"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash in"
msgstr "Приход наличными"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash out"
msgstr "Расход наличными"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash paid for operating activities"
msgstr "Средства, уплаченные за операционную деятельность"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Полученные денежные средства"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Cash received from operating activities"
msgstr "Средства, полученные от операционной деятельности"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Денежные средства, направленные"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Излишек наличности в кассе"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_aged_partner_balance_line_report
msgid "Change expected payment date"
msgstr "Изменить ожидаемую дату платежа"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr "Отчет о Плане Счетов"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Chart of Accounts"
msgstr "План счетов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__children_ids
msgid "Children"
msgstr "Дочерний"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr "Дочерние Линии"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Closing Entry"
msgstr "Закрывающая запись"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Закрытие баланса банка"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__code
msgid "Code"
msgstr "Код"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Code:"
msgstr "Код:"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Communication"
msgstr "Общение"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Компании"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__company_id
msgid "Company"
msgstr "Компания"

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid ""
"Company %s already belongs to a tax unit in %s. A company can at most be "
"part of one tax unit per country."
msgstr ""
"Компания %s уже принадлежит к налоговой единице в %s. Компания может входить"
" не более чем в одну налоговую единицу в каждой стране."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Company Currency:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_unit_chooser
msgid "Company Only"
msgstr "Только компания"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Company Settings"
msgstr "Настройки компании"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr "Вычисления"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Computation: %s"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Configure your TAX accounts - %s"
msgstr "Настройка учетных записей TAX - %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Настройка налоговых счетов"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_cj
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cj
#, python-format
msgid "Consolidated Journals"
msgstr "Сводные журнал"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_consolidated_journal
msgid "Consolidated Journals Report"
msgstr "Отчет по сводным журналам"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__control_domain
msgid "Control Domain"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Control Domain:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Controls failed"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr "Косметика"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Себестоимость Дохода"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__country_id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Country"
msgstr "Страна"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Country Code"
msgstr "Код страны"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Создать запись"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Создан"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Создан"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__credit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__credit
#, python-format
msgid "Credit"
msgstr "Кредит"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_id
#, python-format
msgid "Currency"
msgstr "Валюта"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__currency_code
msgid "Currency Code"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Currency Rates (%s)"
msgstr "Курсы валют (%s)"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Текущие Активы"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Текущие Обязательства"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr "Прибутoк, использованная в отчетном пeриоди"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr "Прибыль Текущего Года"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Нераспределенная прибыль текущего периода"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Оборотные активы к обязательствам"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Custom"
msgstr "Пользовательский"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#, python-format
msgid "Date"
msgstr "Дата"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date :"
msgstr "Дата :"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Date cannot be empty"
msgstr "Дата не может быть чистой"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""
"Дата совершения следующего действия, которое должно быть предпринято для "
"дебиторской задолженности. Как правило, автоматически устанавливается после "
"отправки напоминания через ведомость покупателя."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Date:"
msgstr "Дата:"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__debit
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__debit
#, python-format
msgid "Debit"
msgstr "Дебет"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Единицы задержки"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr "Амортизация"

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Details per month"
msgstr "Детали за месяц"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Difference"
msgstr "Расхождение"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__display_type
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__display_type
msgid "Display Type"
msgstr "Способ отображения"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Название документов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__domain
msgid "Domain"
msgstr "Домен"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Domain:"
msgstr "Домен:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_fiscal_position_choser
msgid "Domestic"
msgstr "Внутренние"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Domestic country of your accounting"
msgstr "Юрисдикция для вашего финансового учета"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Скачать отчет о проверке неизменности данных"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_date
#, python-format
msgid "Due Date"
msgstr "Срок исполнения"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.model,name:account_reports.model_account_sales_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
#, python-format
msgid "EC Sales List"
msgstr "Список продаж EC"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "КАПИТАЛ"

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "End Balance"
msgstr "Конечный баланс"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End Date :"
msgstr "Дата Окончания :"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Financial Year"
msgstr "Конец Последнего Финансового Года"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Month"
msgstr "Конец Прошлого Месяца"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "End of Last Quarter"
msgstr "Конец последнего квартала"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Error while validating the domain of line %s:\n"
"%s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Excess Journal Items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude"
msgstr "Исключать"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude From Aged Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "Исключить резерв Валюта"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Exclude from adjustment/provisions entries"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_from_aged_reports
msgid "Exclude this account from aged reports"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#, python-format
msgid "Excluded Accounts"
msgstr "Исключенные счета"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_3
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_3
msgid "Executive Summary"
msgstr "Справка для Руководителя"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__expected_pay_date
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__expected_pay_date
msgid "Expected Date"
msgstr "Ожидаемая дата"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr "Ожидаемая Дата Платежа"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "Expected pay date has been changed from %s to %s for invoice %s"
msgstr "Ожидаемая дата оплаты была изменена с %s на %s для акта %s"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""
"Ожидаемая дата оплаты, как вручную установлено в ведомости клиента "
"(например, если у вас клиент на телефоне, и вы хотите запомнить дату, когда "
"он обещал заплатить)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Счет резерва на покрытие расходов"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Expense Provision for {for_cur}"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense account"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr "Расходы"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
#, python-format
msgid "Export"
msgstr "Экспорт"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Формат экспорта бухгалтерских отчетов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Экспорт в"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Программа экспорта для бухгалтерских отчетов"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Filter:"
msgstr "Фильтр:"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__applicable_filters_ids
msgid ""
"Filters that can be used to filter and group lines in this report. This uses"
" saved filters on journal items."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__financial_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__financial_report_id
msgid "Financial Report"
msgstr "Финансовый Отчет"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:account_reports.menu_account_financial_reports_tree
msgid "Financial Reports"
msgstr "Финансовые отчеты"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Страна учета"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__float
msgid "Float"
msgstr "Число с плавающей точкой"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__foldable
msgid "Foldable"
msgstr "Сворачиваемый"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Folded"
msgstr "Свернутый"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__footnotes_ids
msgid "Footnotes"
msgstr "Сноски"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__strict_range
msgid "Force given dates for all accounts and account types"
msgstr "Форсировать данные даты для всех счетов и типов счета"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Корректировочная проводка по иностранной валюте по состоянию на %s"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Formula:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__formulas
msgid "Formulas"
msgstr "Формулы"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"From %s\n"
"to  %s"
msgstr ""
"От %s\n"
"до %s"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_beginning
msgid "From the beginning"
msgstr "С начала"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__from_fiscalyear
msgid "From the beginning of the fiscal year"
msgstr "С начала отчетного периода"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "From:"
msgstr "От:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Функция для вызова"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "General Ledger"
msgstr "Главная книга"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Отчет по главной книге"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "General Report"
msgstr "Общий отчет"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Созданные документы"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr "Общий Налоговый Отчет"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Global Summary"
msgstr "Обобщение"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Goods"
msgstr "Услуги"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Grid"
msgstr "Сетка"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Валовая прибыль"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Валовая прибыль"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Валовая прибыль (валовая прибыль / операционный доход)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__groupby
msgid "Group by"
msgstr "Группировать"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Account &gt; Tax"
msgstr "Группировать по: Счет &gt; Налог"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_tax_report_choser
msgid "Group by: Tax &gt; Account"
msgstr "Группировать по: Налог &gt; Счет"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Groupby field %s is invalid on line with name '%s'"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Groupby:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_empty
msgid "Hide If Empty"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__hide_if_zero
msgid "Hide If Zero"
msgstr "Скрыть, если нулевой"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy"
msgstr "Иерархия"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Hierarchy and Subtotals"
msgstr "Иерархия и подытоги"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Как часто нужно подавать налоговые декларации"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "Идентификатор"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impact On Grid"
msgstr "Воздействие на сеть"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Impacted Tax Grids"
msgstr "Затронутые налоговые сетки"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include"
msgstr "Включить"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include Unposted Entries"
msgstr "Включить непроведенные записи"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "Include in adjustment/provisions entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Include unposted entries"
msgstr "Включить непроведенные записи"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Payments"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Including Unreconciled Bank Statement Receipts"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr "Доход"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Cчёт доходов и расходов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "Счет резерва доходов"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Income Provision for {for_cur}"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "Inconsistent Statements"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#, python-format
msgid "Initial Balance"
msgstr "Начальный баланс"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Insert foot note here"
msgstr "Вставьте сноску здесь"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__internal_note
msgid "Internal Note"
msgstr "Внутреннее примечание"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__green_on_positive
msgid "Is growth good when positive"
msgstr "Рост хороший, когда положительный"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "JRNL"
msgstr "ЖРНЛ"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Журнал"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Запись журнала"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Groups"
msgstr "Группы журналов"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Элемент журнала"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
#, python-format
msgid "Journal Items"
msgstr "Элементы журнала"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items (%s)"
msgstr "Элементы журнала (%s)"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Journal Items for Tax Audit"
msgstr "Элементы журнала для налогового аудита"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal Items on the"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#, python-format
msgid "Journal Name (Code)"
msgstr "Название журнала (Код)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "Journal items on the"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_print_journal_menu
#: model:ir.ui.menu,name:account_reports.menu_print_journal
msgid "Journals Audit"
msgstr "аудит журналов"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Journals:"
msgstr "Журналы:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "ОБЯЗАТЕЛЬСТВА"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "ОБЯЗАТЕЛЬСТВА + КАПИТАЛ"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Label"
msgstr "Метка"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Financial Year"
msgstr "Последний Финансовый Год"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format____last_update
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Month"
msgstr "Прошлый месяц"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Last Quarter"
msgstr "Последний Квартал"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_cell_template_link_last_statement
msgid "Last Statement:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__level
msgid "Level"
msgstr "Уровень"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line
msgid "Line"
msgstr "Строка"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__line_ids
msgid "Lines"
msgstr "Строк"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Load more... (%s remaining)"
msgstr "Скачать больше ... (%s напоминания)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Главная компания"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr "Основная информация"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""
"Основная компания данного подразделения; та, которая фактически отчитывается"
" и платит налоги."

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#, python-format
msgid "Make Adjustment Entry"
msgstr "Ввод корректировки"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_manager
msgid "Manage Summary and Footnotes of Reports"
msgstr "Управление итогами и примечаниями отчетов"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__manager_id
msgid "Manager"
msgstr "Менеджер"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Matching Number"
msgstr "Согласованное Число"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "Члены этого подразделения"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__generated_menu_id
msgid "Menu Item"
msgstr "Пункт меню"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Missing Journal Items"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr "Модуль"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_id
msgid "Move"
msgstr "Перемещение"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_name
msgid "Move Name"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_ref
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__move_ref
msgid "Move Ref"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__move_type
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__move_type
msgid "Move Type"
msgstr "Тип перемещения"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation
msgid "Multicurrency Revaluation Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "Мастер мультивалютной переоценки"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for fiscal position %s after %s. There should be at most one. \n"
" %s"
msgstr ""
"Для фискальной позиции %s после %s существует несколько записей закрытия проекта налога. Должна быть только одна.\n"
" %s"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %s. There should be at most one. \n"
" %s"
msgstr ""
"После %s для вашего внутреннего региона существует несколько проводок закрытия чернового налога. Должна быть не более одной.\n"
" %s"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "NET"
msgstr "NET"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_accounting_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__name
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#, python-format
msgid "Name"
msgstr "Название"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "Имя, которое следует присвоить создаваемым документам."

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Name:"
msgstr "Наименование:"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "Net Assets"
msgstr "Чистые активы"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Чистая прибыль"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Чистые активы"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Net increase in cash and cash equivalents"
msgstr "Чистое увеличение денежных средств и их эквивалентов"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "Рентабельность по чистой прибыли (чистая прибыль / доход)"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__show_domain__never
msgid "Never"
msgstr "Никогда"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__next_action_date
msgid "Next Action Date"
msgstr "Дата следующего действия"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "No Comparison"
msgstr "Нет сравнения"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__no_unit
msgid "No Unit"
msgstr "Нет Единицы"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "No VAT number associated with your company. Please define one."
msgstr ""
"Нет номера НДС, связанного с вашей компанией. Пожалуйста, укажите его."

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No adjustment needed"
msgstr "Регулировка не требуется"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "No provision needed was found."
msgstr "Необходимых положений не обнаружено."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_ir_filters
msgid "None"
msgstr "Нет"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""
"Примечание вы можете установить через отчет клиента о пункте журнала "
"дебиторской задолженности"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Number of periods :"
msgstr "Количество периодов :"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "ВНЕБАЛАНСОВЫЕ СЧЕТА"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Предупреждение Odoo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__period5
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__period5
msgid "Older"
msgstr "Более старый"

#. module: account_reports
#: code:addons/account_reports/wizard/report_export_wizard.py:0
#, python-format
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "Один из выбранных форматов не может быть экспортирован в DMS"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "One or more partners has no VAT Number."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Только администраторы биллинга могут изменять даты блокировки!"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Only Show Unreconciled Entries"
msgstr "Отображать только постоянные записи"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Начальные сальдо отчетного периода"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr "Операционная прибыль"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr "Другие доходы"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
#, python-format
msgid "Outstanding Payments/Receipts"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_id
msgid "Parent"
msgstr "Родитель"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__parent_id
msgid "Parent Menu"
msgstr "Родительское Меню"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "Идентификатор родительского отчета"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Родительский визард"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_id
#, python-format
msgid "Partner"
msgstr "Партнёр"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partner Categories:"
msgstr "Категории партнера:"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/res_partner.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.model,name:account_reports.model_account_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
#, python-format
msgid "Partner Ledger"
msgstr "Книга партнера"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_name
msgid "Partner Name"
msgstr "Имя партнера"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr "Партнерские отчеты"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__partner_trust
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__partner_trust
msgid "Partner Trust"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Partners"
msgstr "Партнёры"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.filter_info_template
msgid "Partners:"
msgstr "Партнеры:"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Payable"
msgstr "Кредиторская задолженность"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Payable tax amount"
msgstr "Сумма налога к уплате"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "Кредиторская задолженность"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__payment_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__payment_id
msgid "Payment"
msgstr "Платеж"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__figure_type__percents
msgid "Percents"
msgstr "Проценты"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Выполнение"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Periodicity"
msgstr "Периодичность"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Периодичность в месяц"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid ""
"Please specify a Group by field when using '%s' in Formulas, on line with "
"name '%s'"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Плюс Основные Активы"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Плюс Внеоборотные Активы"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Плюс долгосрочные обязательства"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Положение"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Posted Entries Only"
msgstr "Только проведенные записи"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Авансовые платежи"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Данные предварительного просмотра"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Previous Period"
msgstr "Предыдущий Период"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Нераспределенная прибыль предыдущих лет"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid "Print On New Page"
msgstr "Напечатать на новой странице"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""
"Действуйте осторожно, поскольку на этот период может существовать "
"корректировка ("

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
msgid "Profit And Loss"
msgstr "Прибыль и убыток"

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
#: model:ir.actions.client,name:account_reports.account_financial_html_report_action_1
#: model:ir.ui.menu,name:account_reports.account_financial_html_report_menu_1
msgid "Profit and Loss"
msgstr "Прибыли и убытки"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Доходность"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "Предложение проводки для закрытия налогов."

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Provision for {for_cur} (1 {comp_cur} = {rate} {for_cur})"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Receivable"
msgstr "Дебиторская задолженность"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Receivable tax amount"
msgstr "Сумма налога к получению"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Дебиторская задолженность"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_payable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_aged_receivable_report
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_partner_ledger_report
msgid "Reconcile"
msgstr "Сверить"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Отчет Сверки"

#. module: account_reports
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Ref"
msgstr "Ссылка"

#. module: account_reports
#: code:addons/account_reports/models/account_analytic_report.py:0
#, python-format
msgid "Reference"
msgstr "Ссылка"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Related Company"
msgstr "Связанная компания"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Напоминание"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_aged_partner__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_payable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_aged_receivable__report_currency_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_currency_id
msgid "Report Currency"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Definition"
msgstr "Макет отчета"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation__report_include
msgid "Report Include"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr "Строка Отчета"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "Report Line Computation"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr "Строки Отчета"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_model
msgid "Report Model"
msgstr "Модель отчета"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__report_name
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
msgid "Report Name"
msgstr "Наименование Отчета"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Отчетность"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Нераспределенная прибыть"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Доходность инвестиций (чистая прибыль / активы)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Дата сторнирования"

#. module: account_reports
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Сторнирования:%s "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
msgid "Same Period Last Year"
msgstr "За аналогичный период прошлого года"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Save"
msgstr "Сохранить"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_accounts
msgid "Search account"
msgstr "Поиск счета"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_with_filter_input_partner
msgid "Search partner"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__name
msgid "Section Name"
msgstr "Наименование Раздела"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Services"
msgstr "Услуги"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__tax_report
msgid ""
"Set to True to automatically filter out journal items that are not tax "
"exigible."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_coa
msgid "Setup"
msgstr "Настройки"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Краткосрочный прогноз движения денежных средств"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__show_domain
msgid "Show Domain"
msgstr "Показать домен"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Показать предупреждение Перемещение"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "Show unfold all filter"
msgstr "Показать развернутый весь фильтр"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Some controls failed"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid ""
"Some of your tax groups are missing information in company %s. Please "
"complete their configuration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__special_date_changer
msgid "Special Date Changer"
msgstr "Специальная изменение даты"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__control_domain
msgid ""
"Specify a control domain that will raise a warning if the report line is not"
" computed correctly."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""
"Укажите бухгалтерскую фирму, которая будет выступать в качестве "
"представителя при экспорте отчетов."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_comparison
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Start Date :"
msgstr "Дата Начала :"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Начать с"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_manager__summary
msgid "Summary"
msgstr "Сводка"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "TAX"
msgstr "НАЛОГ"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/js/account_reports.js:0
#: code:addons/account_reports/static/src/js/account_reports.js:0
#, python-format
msgid "Tags"
msgstr "Теги"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Amount"
msgstr "Сумма налога"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_end_date
msgid "Tax Closing End Date"
msgstr "Дата окончания закрытия налога"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Tax Declaration"
msgstr "Налоговая декларация"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.report_journal
msgid "Tax Grid"
msgstr "Налоговая сетка"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "ИНН"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Paid Adjustment"
msgstr "Корректировка уплаченного налога"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Tax Received Adjustment"
msgstr "Корректировка полученного налога"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report__tax_report
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
#, python-format
msgid "Tax Report"
msgstr "Налоговый Отчет"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_report_control_error
msgid "Tax Report Control Error"
msgstr "Ошибка контроля налогового отчета"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Tax Return"
msgstr "Налоговая декларация"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "Налоговое подразделение"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "Налоговые единицы"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Налоговый отчет"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return for %s%s"
msgstr "Налоговая декларация за %s%s"

#. module: account_reports
#: code:addons/account_reports/models/res_company.py:0
#, python-format
msgid "Tax return from %s to %s%s"
msgstr "Налоговая декларация c %s по %s%s"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_closing_end_date
msgid ""
"Technical field used for VAT closing, containig the end date of the period "
"this entry closes."
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "Техническая модель для бухгалтерского отчета скачать"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr "Текст"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid "The Book balance in Odoo dated today"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The amount will be : %s"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The carried over balance will be : %s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""
"Страна, в которой данная налоговая единица используется для группировки "
"деклараций налоговых отчетов ваших компаний."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__country_id
msgid "The country this report is intended to."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""
"Страна, в которой будут использоваться налоговые отчеты для этой компании."

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"The current balance in the General Ledger %s doesn't match the balance of "
"your last bank statement %s leading to an unexplained difference of %s."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "The difference will be carried over to the next period's declaration."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""
"Идентификатор, который будет использоваться при подаче отчета по данному "
"подразделению."

#. module: account_reports
#: code:addons/account_reports/models/account_tax.py:0
#, python-format
msgid "The main company of a tax unit has to be part of it."
msgstr ""
"Основная компания налогового подразделения должна входить в его состав."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__generated_menu_id
msgid "The menu item generated for this report, or None if there isn't any."
msgstr "Пункт меню, созданный для этого отчета, или нет, если этого нет."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "Налоговые подразделения, к которым относится эта компания."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "There are"
msgstr "Там есть"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "There are some"
msgstr "Есть несколько"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Financial Year"
msgstr "Этот Финансовый Год"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Month"
msgstr "Текущий месяц"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "This Quarter"
msgstr "Этот квартал"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr "Это позволяет выбрать позицию итогов в финансовых отчетах."

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be increased by the positive amount from"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be reduced by the negative amount from"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This amount will be set to %s."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "This period is already closed for company %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "Today"
msgstr "Сегодня"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#: code:addons/account_reports/models/account_consolidated_journals.py:0
#: code:addons/account_reports/models/account_financial_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_report_coa.py:0
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "Total"
msgstr "Всего"

#. module: account_reports
#: code:addons/account_reports/models/account_accounting_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#, python-format
msgid "Total %s"
msgstr "Итого %s"

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions that were entered into Odoo, but not yet reconciled (Payments "
"triggered by invoices/bills or manually)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(+) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by invoices/refunds or manually)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#, python-format
msgid ""
"Transactions(-) that were entered into Odoo, but not yet reconciled "
"(Payments triggered by bills/credit notes or manually)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
#, python-format
msgid "Trial Balance"
msgstr "Оборотно-сальдовая ведомость"

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report.py:0
#, python-format
msgid "Triangular"
msgstr "Треугольность"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line__figure_type
msgid "Type"
msgstr "Тип"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Нераспределенная прибыль"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "Undefined"
msgstr "Не определено"

#. module: account_reports
#: code:addons/account_reports/models/account_cash_flow_report.py:0
#, python-format
msgid "Unexplained Difference"
msgstr "Необъясненная разница"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold"
msgstr "Развернуть"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unfold All"
msgstr "Развернуть все"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template
msgid "Unfolded"
msgstr "Развернуты"

#. module: account_reports
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#, python-format
msgid "Unknown Partner"
msgstr "Неизвестный партнер"

#. module: account_reports
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
#, python-format
msgid "Unrealized Currency Gains/Losses"
msgstr "Нереализованные доходы/убытки от курсовой разницы"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "Unreconciled"
msgstr "Несверенные"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_financial_html_report_line__special_date_changer__normal
msgid ""
"Use the dates that should normally be used, depending on the account types"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_sales_report_generic.py:0
#, python-format
msgid "VAT"
msgstr "НДС"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_company_form
msgid "VAT Units"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_activity.py:0
#, python-format
msgid "Vat closing from %s to %s"
msgstr "Закрытие чана с %s до %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Bank Statement"
msgstr "Смотреть Банковскую Выписку"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Carryover Lines"
msgstr "Просмотреть линии переноса"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_template.xml:0
#, python-format
msgid "View Journal Entries"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Journal Entry"
msgstr "Смотреть Запись Журнала"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Partner"
msgstr "Посмотреть партнера"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_caret_options
msgid "View Payment"
msgstr "Посмотреть Платежи"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_line__print_on_new_page
msgid ""
"When checked this line and everything after it will be printed on a new "
"page."
msgstr ""
"Если обозначить эту строку и все после него будет напечатано на новой "
"странице."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr ""
"Если флажок установлен, под разделами отчета появляются итоговые и "
"промежуточные итоги"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr "Когда будет выбрано, под разделами отчета отображаются итоги."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr "Нужно ли создавать резервы по выбранным иностранным валютам."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_extra_options
msgid "With Draft Entries"
msgstr "Включая черновики"

#. module: account_reports
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "You are using custom exchange rates."
msgstr "Вы используете пользовательские курсы обмена."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "ежегодно"

#. module: account_reports
#: code:addons/account_reports/models/res_partner.py:0
#: code:addons/account_reports/models/res_partner.py:0
#, python-format
msgid "any"
msgstr "любой"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "дней после периода"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__analytic
msgid "display the analytic filters"
msgstr "отобразить аналитические фильтры"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__comparison
msgid "display the comparison filter"
msgstr "отобразить фильтр сравнения"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__show_journal_filter
msgid "display the journal filter in the report"
msgstr "отразить в отчете фильтр журнала"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__unfold_all_filter
msgid "display the unfold all options in report"
msgstr "отобразить развернутые все параметры в отчете"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "каждые 2 месяца"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "каждые 4 месяца"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "following accounts"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "having a starting balance different than the previous ending balance"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "journal items"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be listed in an incorrect section of the report."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template_control_domain
msgid "might be missing from the proper section of the report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "ежемесячно"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:0
#, python-format
msgid "n/a"
msgstr "n/a"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_manager__report_name
msgid "name of the model of the report"
msgstr "название модели отчета"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "prior or included in this period"
msgstr "в текущем периоде или ранее"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "ежеквартально"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "раз в полгода"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report__date_range
msgid "specify if the report use date_range or single date"
msgstr "укажите, используется отчет date_range или отдельная дата"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "statements"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,help:account_reports.field_account_payment__tax_report_control_error
msgid "technical field used to know if there was a failed control check"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.search_template_date_filter
msgid "to:"
msgstr "до:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "unposted Journal Entries"
msgstr "Непроведенные записи"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.bank_reconciliation_report_main_template
msgid "which doesn't result from a bank statement nor payments."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ General Ledger"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_multicurrency_report
msgid "⇒ Rates"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.template_multicurrency_report
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Сброс на тариф Odoo"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.line_template_general_ledger_report
msgid "⇒ journal items"
msgstr "⇒ записи журнала"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Some journal items appear to point to obsolete report lines."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "Check them"
msgstr "Проверьте их "

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.main_template
msgid "and correct their tax tags if necessary."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#, python-format
msgid "Journal items with archived tax tags"
msgstr "Статьи журнала с заархивированными налоговыми метками"
