# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_reports
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-29 12:08+0000\n"
"PO-Revision-Date: 2016-06-01 13:21+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:299
#: code:addons/account_reports/models/account_followup_report.py:300
#, python-format
msgid " Date "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:299
#: code:addons/account_reports/models/account_followup_report.py:300
#, python-format
msgid " Due Date "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:300
#, python-format
msgid " Excluded "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:300
#, python-format
msgid " Expected Date "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:299
#: code:addons/account_reports/models/account_followup_report.py:300
#, python-format
msgid " Total Due "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:328
#, python-format
msgid "%s - %s days ago"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:351
#, python-format
msgid "%s Payment Reminder"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
msgid "&amp;nbsp;"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:6
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:12
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:64
#, python-format
msgid "&times;"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:335
#, python-format
msgid "(as of %s)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:158
#, python-format
msgid "10% remaining!"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:356
#, python-format
msgid ": Sent a followup email"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_letter_body
msgid ""
"<br/>\n"
"            Customer ref:"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "<i class=\"fa fa-circle\" style=\"color: green;\"/> Good Debtor"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "<i class=\"fa fa-circle\" style=\"color: grey;\"/> Normal Debtor"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "<i class=\"fa fa-circle\" style=\"color: red;\"/> Bad Debtor"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "<span aria-hidden=\"true\">&amp;times;</span>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.partner_view_buttons
msgid "<span class=\"o_stat_text\">Overdue</span>"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid ""
"<strong>Good job!</strong> All followup letters and emails have been sent."
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup
msgid "<strong>Warning!</strong> No action needs to be taken for this partner."
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid ""
"<strong>Warning!</strong> The following emails have not been sent :<br/>"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_context
#: model:ir.model,name:account_reports.model_account_report_context_common
msgid "A particular context for a financial report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_context_bank_rec
msgid "A particular context for the bank reconciliation report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_context_coa
msgid "A particular context for the chart of account"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_context_followup
msgid "A particular context for the followup report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_context_general_ledger
msgid "A particular context for the general ledger"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_context_tax
msgid "A particular context for the generic tax report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_context_followup_all
msgid "A progress bar for followup reports"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_form
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_search
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_tree
msgid "Account Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_line
msgid "Account Report Line"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
msgid "Accrual Basis"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:159
#, python-format
msgid "Accrual Basis,"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_action_id
msgid "Action id"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_prepayments_received0
msgid "Advance Payments received from customers"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_paid0
msgid "Advance payments made to suppliers"
msgstr ""

#. module: account_reports
#. openerp-web
#: model:account.financial.html.report,name:account_reports.account_financial_report_agedpayable0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_agedpayable_line0
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:103
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
#, python-format
msgid "Aged Payable"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr ""

#. module: account_reports
#. openerp-web
#: model:account.financial.html.report,name:account_reports.account_financial_report_agedreceivable0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_agedreceivable_line0
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:102
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
#, python-format
msgid "Aged Receivable"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr ""

#. module: account_reports
#: selection:account.report.context.followup.all,partner_filter:0
msgid "All partners in need of action"
msgstr ""

#. module: account_reports
#: selection:account.report.context.followup.all,partner_filter:0
msgid "All partners with overdue invoices"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_html_report_xml_export
msgid "All the xml exports available for the financial reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_multi_company
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_multi_company
msgid "Allow multi-company"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Always"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:195
#, python-format
msgid "Amount"
msgstr "தொகை"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_report_type
msgid "Analysis Periods"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:106
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
#, python-format
msgid "Annotate"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:103
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:123
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:130
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:150
#, python-format
msgid "Apply"
msgstr "விண்ணப்பி"

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:205
#, python-format
msgid "April"
msgstr "ஏப்ரல்"

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:200
#, python-format
msgid "As of %s"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "At the beginning of the period"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:206
#, python-format
msgid "August"
msgstr "ஆகஸ்ட்"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Auto"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_available_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_available_company_ids
msgid "Available company ids"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_available_journal_ids
msgid "Available journal ids"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
msgid "BALANCE SHEET"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:251
#, python-format
msgid "Balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
msgid "Balance Sheet"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:189
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_journals
#, python-format
msgid "Bank Accounts"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:165
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation_with_journal
#, python-format
msgid "Bank Reconciliation"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:188
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_journal_id
#, python-format
msgid "Bank account"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report
msgid "Bank reconciliation report"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report,report_type:0
msgid "Based on a single date"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report,report_type:0
msgid "Based on date ranges"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report,report_type:0
msgid "Based on date ranges with 'older' and 'total' columns and last 3 months"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report,report_type:0
msgid "Bases on date ranges and cash basis method"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_business_statements_menu
msgid "Business Statements"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "CASH"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "CURRENT YEAR EARNINGS"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
msgid "Cash Basis"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:166
#, python-format
msgid "Cash Basis Method"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:158
#, python-format
msgid "Cash Basis,"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_cashsummary0
#: model:ir.actions.client,name:account_reports.action_account_report_cs
msgid "Cash Flow Statement"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_opening_balance0
msgid "Cash and cash equivalents, beginning of period"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_closing_balance0
msgid "Cash and cash equivalents, closing balance"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing0
msgid "Cash flows from financing activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing0
msgid "Cash flows from investing & extraordinary activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_operating0
msgid "Cash flows from operating activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified0
msgid "Cash flows from unclassified activities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing_in0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing_in0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified_in0
msgid "Cash in"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_financing_out0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_investing_out0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_unclassified_out0
msgid "Cash out"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_spent0
msgid "Cash paid for"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_cash_received0
msgid "Cash received from customers"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_line
msgid "Change expected payment date/note"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:98
#, python-format
msgid "Chart of Account"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_coa_report
msgid "Chart of Account Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_children_ids
msgid "Children"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Children Lines"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_coa.py:76
#, python-format
msgid "Class %s"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:49
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
#, python-format
msgid "Click to add an introductory explanation"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:64
#, python-format
msgid "Close"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_code
msgid "Code"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_column
msgid "Column"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:299
#: code:addons/account_reports/models/account_followup_report.py:300
#: code:addons/account_reports/models/account_general_ledger.py:251
#, python-format
msgid "Communication"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
msgid "Companies"
msgstr "நிறுவனங்கள்"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:173
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
#, python-format
msgid "Companies:"
msgstr "நிறுவனங்கள்:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_company_id
msgid "Company"
msgstr "நிறுவனம்"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_company_ids
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_company_ids
msgid "Company ids"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_date_filter_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_date_filter_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_date_filter_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_date_filter_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_date_filter_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_date_filter_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_date_filter_cmp
msgid "Comparison date filter used"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:109
#, python-format
msgid "Comparison:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:236
#, python-format
msgid "Comparison<br />"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Computation"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Cosmetics"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cost_sales0
msgid "Cost of Revenue"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Sales"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_create_date
msgid "Created on"
msgstr ""
"உருவாக்கப்பட்ட \n"
"தேதி"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:627
#: code:addons/account_reports/models/account_general_ledger.py:251
#: code:addons/account_reports/models/account_report_coa.py:128
#: code:addons/account_reports/models/account_report_coa.py:130
#: code:addons/account_reports/models/account_report_coa.py:133
#, python-format
msgid "Credit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Creditors"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:251
#, python-format
msgid "Currency"
msgstr "நாணயம்"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:89
#, python-format
msgid "Current Balance in Odoo"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:67
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:86
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:113
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:133
#, python-format
msgid "Custom"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
msgid "Customer Statement"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_followup
#: model:ir.actions.client,name:account_reports.action_account_followup_all
#: model:ir.ui.menu,name:account_reports.menu_action_followups
msgid "Customers Statement"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:195
#: code:addons/account_reports/models/account_general_ledger.py:251
#, python-format
msgid "Date"
msgstr "தேதி"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:96
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:143
#, python-format
msgid "Date :"
msgstr "தேதி:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_date_filter
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_date_filter
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_date_filter
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_date_filter
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_date_filter
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_date_filter
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_date_filter
msgid "Date filter used"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line_next_action_date
msgid ""
"Date where the next action should be taken for a receivable item. Usually, "
"automatically set when sending reminders through the customer statement."
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:59
#, python-format
msgid "Date:"
msgstr "தேதி:"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:627
#: code:addons/account_reports/models/account_general_ledger.py:251
#: code:addons/account_reports/models/account_report_coa.py:128
#: code:addons/account_reports/models/account_report_coa.py:130
#: code:addons/account_reports/models/account_report_coa.py:133
#, python-format
msgid "Debit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
msgid "Debtors"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:206
#, python-format
msgid "December"
msgstr "டிசம்பர்"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_xml_export_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Do it Later"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_letter_body
msgid ""
"Document: Customer account statement<br/>\n"
"            Date:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_domain
msgid "Domain"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:148
#, python-format
msgid "Don't follow-up before :"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Done"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:39
#, python-format
msgid "EXPORT (XLSX)"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:40
#, python-format
msgid "EXPORT (XML)"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:12
#, python-format
msgid "Email not sent because of email address of partner not filled in."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_cash_basis
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_cash_basis
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_cash_basis
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_cash_basis
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_cash_basis
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_cash_basis
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_cash_basis
msgid "Enable cash basis columns"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_comparison
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_comparison
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_comparison
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_comparison
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_comparison
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_comparison
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_comparison
msgid "Enable comparison"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:95
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:142
#, python-format
msgid "End Date :"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_date_to
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_date_to
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_date_to
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_date_to
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_date_to
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_date_to
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_date_to
msgid "End date"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_date_to_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_date_to_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_date_to_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_date_to_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_date_to_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_date_to_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_date_to_cmp
msgid "End date for comparison"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:75
#, python-format
msgid "End of Last Financial Year"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:73
#, python-format
msgid "End of Last Month"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:74
#, python-format
msgid "End of Last Quarter"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:47
#, python-format
msgid "Error"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_executivesummary0
msgid "Executive Summary"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:133
#: model:ir.model.fields,field_description:account_reports.field_account_move_line_expected_pay_date
#, python-format
msgid "Expected Payment Date"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line_expected_pay_date
msgid ""
"Expected payment date as manually set through the customer statement (e.g: "
"if you had the customer on the phone and want to remember the date he "
"promised he would pay)"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_expense0
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:205
#, python-format
msgid "February"
msgstr "பிப்ரவரி"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_financial_report_id
msgid "Financial Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_financial_report_tree
#: model:ir.ui.menu,name:account_reports.menu_account_financial_reports_tree
msgid "Financial Reports"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "Float"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Foldable"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:97
#: model:ir.model,name:account_reports.model_account_followup_report
#, python-format
msgid "Followup Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Footnote for reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_footnotes
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_footnotes
msgid "Footnotes"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_footnotes_manager_id
msgid "Footnotes Manager"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_report_type
msgid "For report like the balance sheet that do not work with date ranges"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "Force given dates for all accounts and account types"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_formulas
msgid "Formulas"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:201
#, python-format
msgid "From %s <br/> to  %s"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "From the beginning"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/models/account_general_ledger.py:215
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:101
#: model:ir.actions.client,name:account_reports.action_account_general_ledger_force_account
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
#, python-format
msgid "General Ledger"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger
msgid "General Ledger Report"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.model,name:account_reports.model_account_generic_tax_report
msgid "Generic Tax Report"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_search
msgid "Group By"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_groupby
msgid "Group by"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:153
#, python-format
msgid "Halfway through!"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:159
#, python-format
msgid "Hang in there, you're nearly done."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_hide_if_zero
msgid "Hide if zero"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:168
#, python-format
msgid "Hierarchy"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:168
#, python-format
msgid "Hierarchy and Subtotals"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "History <span class=\"caret\"/>"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_id
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_xml_export_id
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger_id
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_id
msgid "ID"
msgstr "ID"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_totalincome0
msgid "INCOME"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:8
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:17
#, python-format
msgid "In Need of Action"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:167
#, python-format
msgid "Include Unposted Entries"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:167
#, python-format
msgid "Include unposted entries"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_income0
msgid "Income"
msgstr "வருவாய்"

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:150
#, python-format
msgid "Initial Balance"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:19
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:157
#, python-format
msgid "Insert foot note here"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:136
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:151
#, python-format
msgid "Insert note here"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line_internal_note
msgid "Internal Note"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_invoice_address_id
msgid "Invoice Address"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_green_on_positive
msgid "Is growth good when positive"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:205
#, python-format
msgid "January"
msgstr "ஜனவரி"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_journal_items
msgid "Journal Items by tax"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_journal_ids
msgid "Journal ids"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:183
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
#, python-format
msgid "Journals:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:206
#, python-format
msgid "July"
msgstr "ஜூலை"

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:205
#, python-format
msgid "June"
msgstr "ஜூன்"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:83
#, python-format
msgid "Last Financial Year"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_reconciliation_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_coa_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_xml_export___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_followup_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_general_ledger___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_generic_tax_report___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager___last_update
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:62
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:81
#, python-format
msgid "Last Month"
msgstr "கடந்த மாதம்"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:63
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:82
#, python-format
msgid "Last Quarter"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:160
#, python-format
msgid "Last Statement Balance"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnotes_manager_write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_multicompany_manager_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:64
#, python-format
msgid "Last Year"
msgstr "சென்ற ஆண்டு"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Latest Statement"
msgstr "கடந்த அறிக்கை"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view_account_manager
msgid "Latest Statement:"
msgstr "கடந்த அறிக்கை:"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_legal_statements_menu
msgid "Legal Statements"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:143
#, python-format
msgid "Less Outstanding Receipt"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:116
#, python-format
msgid "Less Un-Reconciled Bank Statement Lines"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:158
#, python-format
msgid "Less Unencoded Statements"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_level
msgid "Level"
msgstr "நிலை"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_ids
msgid "Lines"
msgstr "கோடுகள்"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_report_id
msgid "Linked financial report"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Log a Note"
msgstr ""

#. module: account_reports
#: model:mail.message.subtype,name:account_reports.followup_logged_action
msgid "Logged followup action"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Main Info"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_manager_id
msgid "Manager id"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Manual"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:205
#, python-format
msgid "March"
msgstr "மார்ச்"

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:205
#, python-format
msgid "May"
msgstr "மே"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_menuitem_created
msgid "Menu Has Been Created"
msgstr ""

#. module: account_reports
#: model:mail.message.subtype,description:account_reports.followup_logged_action
msgid "Messages created after a followup action has been executed"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_regulator0
msgid ""
"Minus previously recorded advance payments (already in the starting balance)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_module_module
msgid "Module"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_multicompany_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_multicompany_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_multicompany_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_multicompany_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_multicompany_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_multicompany_manager_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_multicompany_manager_id
msgid "Multi-company Manager"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_assets0
msgid "NET ASSETS"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_net_profit0
msgid "NET PROFIT"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_name
msgid "Name"
msgstr "பெயர்"

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:159
#: code:addons/account_reports/models/account_generic_tax_report.py:161
#: code:addons/account_reports/models/account_generic_tax_report.py:164
#, python-format
msgid "Net"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
msgid "Net Audit"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profit0
msgid "Net Profit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:178
#, python-format
msgid "Net Tax Lines"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "நிகர சொத்துகள்"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_cashsummary_net_increase0
msgid "Net increase in cash and cash equivalents"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,show_domain:0
msgid "Never"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner_payment_next_action
msgid "Next Action"
msgstr "அடுத்த நடவடிக்கை"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line_next_action_date
#: model:ir.model.fields,field_description:account_reports.field_res_partner_payment_next_action_date
msgid "Next Action Date"
msgstr "அடுத்த நடவடிக்கை தேதி"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Next Reminder:"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:268
#, python-format
msgid "Next action date: "
msgstr "அடுத்த நடவடிக்கை தேதி:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_next_footnote_number
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_next_footnote_number
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_next_footnote_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_next_footnote_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_next_footnote_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_next_footnote_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_next_footnote_number
msgid "Next footnote number"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:117
#, python-format
msgid "No Comparison"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "No Unit"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:65
#, python-format
msgid "No fiscal year was found. Using the current civil year."
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "No followup to send !"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:625
#, python-format
msgid "Non-issued"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:110
#, python-format
msgid "None"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:135
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:150
#, python-format
msgid "Note"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_partner_payment_next_action
msgid "Note regarding the next action."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line_internal_note
msgid ""
"Note you can set through the customer statement about a receivable journal "
"item"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:206
#, python-format
msgid "November"
msgstr "நவம்பர்"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_number
msgid "Number"
msgstr "எண்"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company_days_between_two_followups
msgid "Number of days between two follow-ups"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_periods_number
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_periods_number
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_periods_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_periods_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_periods_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_periods_number
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_periods_number
msgid "Number of periods"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:121
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:128
#, python-format
msgid "Number of periods :"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:206
#, python-format
msgid "October"
msgstr "அக்டோபர்"

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:635
#, python-format
msgid "Older"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:144
#, python-format
msgid "One month"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:142
#, python-format
msgid "One week"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_financial_html_report_context_report_id
msgid "Only if financial report"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:156
#, python-format
msgid "Options:"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "PERFORMANCE"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "POSITION"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:38
#, python-format
msgid "PRINT PREVIEW"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "PROFITABILITY"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_parent_id
msgid "Parent"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_parent_id
msgid "Parent id"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:251
#: model:ir.model,name:account_reports.model_res_partner
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_partner_id
#, python-format
msgid "Partner"
msgstr "கூட்டாளி"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_partner_filter
msgid "Partner Filter"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
#, fuzzy
msgid "Partner Ledger"
msgstr "கூட்டாளி"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:7
#, python-format
msgid "Partners:"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_current_liabilities_payable
msgid "Payables"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_percentage
msgid "Percentage"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,figure_type:0
msgid "Percents"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:60
#, python-format
msgid "Period:"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:129
#, python-format
msgid "Plus Outstanding Payment"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:114
#, python-format
msgid "Plus Un-Reconciled Bank Statement Lines"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:156
#, python-format
msgid "Plus Unencoded Statements"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:162
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
#, python-format
msgid "Posted Entries Only"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:119
#, python-format
msgid "Previous Period"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:111
#, python-format
msgid "Previous Periods"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Print Letter"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_pnl
#: model:ir.actions.client,name:account_reports.action_account_report_pnl_fy
msgid "Profit And Loss"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report,name:account_reports.account_financial_report_profitandloss0
msgid "Profit and Loss"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:101
#, python-format
msgid "Purchase"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup
msgid "Put back in the list"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:193
#: code:addons/account_reports/models/account_report_context_common.py:281
#, python-format
msgid "Quarter #"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_retained_earnings0
msgid "RETAINED EARNINGS"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:195
#, python-format
msgid "Reference"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Reference number"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_form
msgid "Report Line"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_line_tree
msgid "Report Lines"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.view_account_financial_report_search
msgid "Report Type"
msgstr ""

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:101
#, python-format
msgid "Sale"
msgstr "விற்பனை"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:112
#, python-format
msgid "Same Last Year"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:126
#, python-format
msgid "Same Period Last Year"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:20
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:54
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:68
#, python-format
msgid "Save"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_name
msgid "Section Name"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
msgid "See bank statement"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_body
msgid "Send by email"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:341
#, python-format
msgid "Sent a followup letter"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:206
#, python-format
msgid "September"
msgstr "செப்டம்பர்"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_sequence
msgid "Sequence"
msgstr "வரிசை"

#. module: account_reports
#: model:account.financial.html.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_debit_credit
msgid "Show Credit and Debit Columns"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_show_domain
msgid "Show domain"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_hierarchy_3
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_hierarchy_3
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_hierarchy_3
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_hierarchy_3
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_hierarchy_3
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_hierarchy_3
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_hierarchy_3
msgid "Show hierarchies"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_skipped_partners_ids
msgid "Skipped partners"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_special_date_changer
msgid "Special date changer"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:88
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:135
#, python-format
msgid "Start Date :"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_date_from
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_date_from
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_date_from
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_date_from
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_date_from
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_date_from
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_date_from
msgid "Start date"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_date_from_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_date_from_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_date_from_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_date_from_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_date_from_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_date_from_cmp
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_date_from_cmp
msgid "Start date for comparison"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_started
msgid "Starting time"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_summary
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_summary
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_summary
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_summary
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_summary
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_summary
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_summary
msgid "Summary"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_target_id
msgid "Target id"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:159
#: code:addons/account_reports/models/account_generic_tax_report.py:161
#: code:addons/account_reports/models/account_generic_tax_report.py:164
#, python-format
msgid "Tax"
msgstr "வரி"

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
msgid "Tax Audit"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:178
#, python-format
msgid "Tax Lines"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_generic_tax_report.py:134
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#, python-format
msgid "Tax Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_text
msgid "Text"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_partner_payment_next_action_date
msgid "The date before which no action should be taken."
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:154
#, python-format
msgid "The first half took you %ss."
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:6
#, python-format
msgid "The followup report was successfully emailed !"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:204
#, python-format
msgid ""
"There are more than 80 items in this list, click here to see all of them"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:79
#, python-format
msgid "This Financial Year"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:66
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:78
#, python-format
msgid "This Month"
msgstr "இந்த மாதம்"

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:65
#, python-format
msgid "This Year"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:61
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:72
#, python-format
msgid "Today"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_financial_report.py:374
#: code:addons/account_reports/models/account_financial_report.py:451
#: code:addons/account_reports/models/account_financial_report.py:635
#, python-format
msgid "Total"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_general_ledger.py:195
#, python-format
msgid "Total "
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:79
#, python-format
msgid "Total Due"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_followup_report.py:91
#, python-format
msgid "Total Overdue"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:145
#, python-format
msgid "Two months"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:143
#, python-format
msgid "Two weeks"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_line_figure_type
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote_type
msgid "Type"
msgstr "வகை"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_unfolded_accounts
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_unfolded_accounts
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_unfolded_lines
msgid "Unfolded lines"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner_unreconciled_aml_ids
msgid "Unreconciled aml ids"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_context_coa_all_entries
#: model:ir.model.fields,field_description:account_reports.field_account_context_general_ledger_all_entries
#: model:ir.model.fields,field_description:account_reports.field_account_financial_html_report_context_all_entries
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_bank_rec_all_entries
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_common_all_entries
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_entries
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_tax_all_entries
msgid "Use all entries (not only posted ones)"
msgstr ""

#. module: account_reports
#: selection:account.financial.html.report.line,special_date_changer:0
msgid "Use given dates"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_move_line.py:19
#, python-format
msgid "View Bank Statement"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_move_line.py:24
#, python-format
msgid "View Invoice"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:105
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
#: model:ir.ui.view,arch_db:account_reports.report_followup_line
#, python-format
msgid "View Journal Entry"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_move_line.py:25
#, python-format
msgid "View Move"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_move_line.py:21
#, python-format
msgid "View Payment"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_financial_line.xml:65
#, python-format
msgid "Warning!"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:161
#: model:ir.ui.view,arch_db:account_reports.report_financial_body
#, python-format
msgid "With Draft Entries"
msgstr ""

#. module: account_reports
#. openerp-web
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:11
#: code:addons/account_reports/static/src/xml/account_report_backend.xml:18
#, python-format
msgid "With Overdue Invoices"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "You have sent"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_public_body
msgid "Your Statement"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_valuenow
msgid "current amount of invoices done"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:181
#, python-format
msgid "get_columns_names not implemented"
msgstr ""

#. module: account_reports
#: code:addons/account_reports/models/account_report_context_common.py:111
#, python-format
msgid "get_report_obj not implemented"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnotes_manager
msgid "manages footnotes"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_multicompany_manager
msgid "manages multicompany for reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_last_page
msgid "number of pages"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "reports in"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid "s per report."
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_followup_all
msgid ""
"s<br/>\n"
"                        That means you have spent on average"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_context_followup_all_valuemax
msgid "total amount of invoices to do"
msgstr ""

#. module: account_reports
#: model:ir.ui.view,arch_db:account_reports.report_financial_line
msgid "⇒ journal entries"
msgstr ""

#, fuzzy
#~ msgid "Account"
#~ msgstr "தொகை"

#, fuzzy
#~ msgid "Accounts"
#~ msgstr "தொகை"

#, fuzzy
#~ msgid "Base Amount"
#~ msgstr "தொகை"

#, fuzzy
#~ msgid "Equal Last Statement Balance"
#~ msgstr "கடந்த அறிக்கை"

#, fuzzy
#~ msgid "Tax Amount"
#~ msgstr "தொகை"
