<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <record id="view_account_form" model="ir.ui.view">
        <field name="name">account.account.reports.form</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <field name="currency_id" position="after">
                <field name="exclude_from_aged_reports" attrs="{'invisible': [('internal_type', 'not in', ('payable','receivable'))]}"/>
            </field>
        </field>
    </record>
</odoo>
