<odoo>
    <record model="ir.ui.view" id="account_reports_journal_dashboard_kanban_view">
        <field name="name">account.journal.dashboard.kanban.reports</field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.account_journal_dashboard_kanban_view" />
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('o_kanban_manage_reconciliation')]" position="inside">
                <div groups="account.group_account_readonly">
                    <a type="action" name="%(action_account_report_bank_reconciliation_with_journal)d">Reconciliation Report</a>
                </div>
            </xpath>

            <xpath expr="//div[hasclass('o_kanban_manage_reports')]" position="inside">
                <div t-if="journal_type == 'sale'">
                    <a type="action" name="%(action_account_report_ar)d">Aged Receivables</a>
                </div>
                <div t-if="journal_type == 'purchase'">
                    <a type="action" name="%(action_account_report_ap)d">Aged Payables</a>
                </div>
            </xpath>

            <!-- Replace the displayed amount by an action opening the General Ledger report -->
            <xpath expr="//div[@id='dashboard_bank_cash_balance']" position="replace">
                <div id="dashboard_bank_cash_balance" class="col overflow-hidden text-left">
                    <a type="object" name="action_open_bank_balance_in_gl">Balance in GL</a>
                </div>
            </xpath>

            <!-- Replace the displayed amount by an action opening the Bank Reconciliation report -->
            <xpath expr="//div[@id='dashboard_bank_cash_outstanding_balance']" position="replace">
                <div id="dashboard_bank_cash_outstanding_balance" class="col overflow-hidden text-left">
                    <a type="action" name="%(action_account_report_bank_reconciliation_with_journal)d">Outstanding Payments/Receipts</a>
                </div>
            </xpath>

        </field>
    </record>

    <!-- Only replace the label if in the group ; groups cannot be placed on a xpath tag -->
    <record model="ir.ui.view" id="account_reports_journal_dashboard_kanban_view_account_manager">
        <field name="name">account.journal.dashboard.kanban.reports</field>
        <field name="model">account.journal</field>
        <field name="groups_id" eval="[(4,ref('account.group_account_manager'))]"/>
        <field name="inherit_id" ref="account.account_journal_dashboard_kanban_view"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='latest_statement']" position="replace">
                <div name="latest_statement" class="row">
                    <div class="col overflow-hidden text-left">
                        <span title="Latest Statement">Latest Statement</span>
                    </div>
                    <div class="col-auto text-right">
                        <span><t t-esc="dashboard.last_balance"/></span>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
