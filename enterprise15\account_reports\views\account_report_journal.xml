<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_journal" inherit_id="account.report_journal">
        <!--======== override the Tax Applied section to make use of the tax report ========-->

        <!-- add a new column to the block to display the tax country if needed and adapt the header colspan-->
        <xpath expr="//table[@id='tax_table']/thead/tr[2]/th[1]" position="before">
            <th t-if="len(taxes) > 1">Country</th>
        </xpath>
        <xpath expr="//table[@id='tax_table']/thead/tr[1]/th" position="attributes">
            <t t-if="len(taxes) > 1">
                <attribute name="colspan">4</attribute>
            </t>
        </xpath>

        <!-- Replace the foreach by a new one taking into accounts the values returned by the tax report -->
        <xpath expr="//tr[@t-foreach='taxes']" position="replace">
            <t t-foreach="taxes" t-as="country_name">
                <tr t-foreach="taxes[country_name]" t-as="tax">
                    <t t-if="country_name_size > 1">
                        <t t-if="tax_index == 0">
                            <td><span t-esc="country_name"/></td>
                        </t>
                        <t t-else="">
                            <td/>
                        </t>
                    </t>
                    <td><span t-esc="tax['name']"/></td>
                    <td style="text-align: right;">
                        <span t-esc="tax['base_amount']"
                              t-options="{'widget': 'monetary', 'display_currency': (company_id or res_company).currency_id}"
                              class="text-monospace"/>
                    </td>
                    <td style="text-align: right;">
                        <span t-esc="tax['tax_amount']"
                              t-options="{'widget': 'monetary', 'display_currency': (company_id or res_company).currency_id}"
                              class="text-monospace"/>
                    </td>
                </tr>
            </t>
        </xpath>

        <!--======== Update the move table and add a new block for the tax grids ========-->

        <!-- Update the move table to add a new column for the tax grids -->

        <!-- Get the grids before the table so that we can hide the column when not needed -->
        <xpath expr="//table[@id='move_table']" position="before">
            <t t-set="grids" t-value="get_tax_grids(data, o)"/>
        </xpath>
        <xpath expr="//table[@id='move_table']/thead/tr" position="inside">
            <th style="text-align: right;" t-if="grids">Tax Grid</th>
        </xpath>

        <!-- Display all tags name applied on the line in the new column -->
        <xpath expr="//table[@id='move_table']/tbody/tr[1]" position="inside">
            <td style="text-align: right;" t-if="grids">
                <span t-foreach="aml.tax_tag_ids" t-as="tax_tag_id">
                    <span t-esc="tax_tag_id.name"/>
                </span>
            </td>
        </xpath>
        <!-- Add the empty td to the result line for display purpos -->
        <xpath expr="//table[@id='move_table']/tbody/tr[2]" position="inside">
            <td t-if="grids"/>
        </xpath>

        <!-- For compatibility purpose, replace the method called to get the taxes -->
        <xpath expr="//div[@id='tax_section']/t[1]" position="attributes">
            <attribute name="t-value">get_generic_tax_report_summary(data, o)</attribute>
        </xpath>
        <!-- Add a new block acting as a sumarry of the movements for each tax grids used in this journal-->
        <xpath expr="//div[@id='tax_section']" position="inside">
            <div class="col-5" t-if="grids">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <t t-if="len(grids) > 1">
                                <th colspan="5">Impacted Tax Grids</th>
                            </t>
                            <t t-else="">
                                <th colspan="4">Impacted Tax Grids</th>
                            </t>
                        </tr>
                        <tr>
                            <th t-if="len(grids) > 1">Country</th>
                            <th>Grid</th>
                            <th style="text-align: right;">+</th>
                            <th style="text-align: right;">-</th>
                            <th style="text-align: right;">Impact On Grid</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="grids" t-as="country_name">
                            <tr t-foreach="grids[country_name]" t-as="grid_name">
                                <t t-if="country_name_size > 1">
                                    <t t-if="grid_name_index == 0">
                                        <td><span t-esc="country_name"/></td>
                                    </t>
                                    <t t-else="">
                                        <td/>
                                    </t>
                                </t>
                                <td><span t-esc="grid_name"/></td>
                                <td style="text-align: right;">
                                    <span t-esc="grids[country_name][grid_name].get('+', 0)"
                                          t-options="{'widget': 'monetary', 'display_currency': (company_id or res_company).currency_id}"
                                          class="text-monospace"/>
                                </td>
                                <td style="text-align: right;">
                                    <span t-esc="grids[country_name][grid_name].get('-', 0)"
                                          t-options="{'widget': 'monetary', 'display_currency': (company_id or res_company).currency_id}"
                                          class="text-monospace"/>
                                </td>
                                <td style="text-align: right;">
                                    <span t-esc="grids[country_name][grid_name]['impact']"
                                          t-options="{'widget': 'monetary', 'display_currency': (company_id or res_company).currency_id}"
                                          class="text-monospace"/>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </div>
        </xpath>

    </template>
</odoo>
