<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="view_tax_unit_form" model="ir.ui.view">
        <field name="name">account.tax.unit.form</field>
        <field name="model">account.tax.unit</field>
        <field name="arch" type="xml">
            <form string="Tax Unit">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>

                    <group>
                        <group>
                            <field name="country_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="company_ids" widget="many2many_tags"/>
                            <field name="main_company_id" domain="[('id', 'in', company_ids)]" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                        <group>
                            <field name="vat"/>
                        </group>
                    </group>
                    <group>

                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_tax_unit_tree" model="ir.ui.view">
        <field name="name">account.tax.unit.tree</field>
        <field name="model">account.tax.unit</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="country_id"/>
            </tree>
        </field>
    </record>

    <record id="action_view_tax_units" model="ir.actions.act_window">
        <field name="name">Tax Units</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.tax.unit</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_tax_unit_tree"/>
    </record>

    <menuitem
        id="menu_view_tax_units"
        action="action_view_tax_units"
        parent="account.account_account_menu"
        sequence="8"
        groups="account.group_account_manager"/>

</odoo>
