<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>

<template id="footnotes_template">
    <t t-foreach="footnotes" t-as="footnote">
        <p class="footnote" t-att-id="'footnote' + str(footnote.get('number'))" t-att-data-id="footnote.get('id')">
            <span class='text'><t t-esc="str(footnote.get('number')) + '. ' + str(footnote.get('text'))"/></span>
            <span class='o_account_reports_footnote_icons'><i class="fa fa-fw fa-trash-o" role="img" aria-label="Delete" title="Delete"></i></span>
        </p>
    </t>
</template>

<template id="line_caret_options">
    <li t-if="line['caret_options'] == 'account.account'"><a role="menuitem" tabindex="-1" t-att-data-id="line.get('account_id') or line['id']" action="open_general_ledger" class="dropdown-item">General Ledger</a></li>
    <li t-if="line['caret_options'] == 'account.account'"><a role="menuitem" tabindex="-1" t-att-data-id="line.get('account_id') or line['id']" t-att-data-financial_group_line_id="line.get('model') == 'account.financial.html.report' and line.get('parent_id')" action="open_journal_items" class="dropdown-item">Journal Items</a></li>
    <li t-if="line['caret_options'] == 'account.move'">
      <a role="menuitem" tabindex="-1" t-att-data-id="line.get('line_id') or line['id']" t-att-data-model="line.get('model', 'account.move.line')" t-att-data-action-context="line.get('action_context')" data-object="account.move" action="open_document" class="dropdown-item">View Journal Entry</a>
    </li>
    <li t-if="line['caret_options'] == 'account.payment'">
      <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" t-att-data-model="line.get('model', 'account.move.line')" t-att-data-action-context="line.get('action_context')" data-object="account.payment" action="open_document" class="dropdown-item">View Payment</a>
    </li>
    <li t-if="line['caret_options'] == 'account.tax'">
      <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" action="open_tax" class="dropdown-item">Audit</a>
    </li>
    <li t-if="line['caret_options'] == 'account.tax.report.line'">
      <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" action="tax_tag_template_open_aml" class="dropdown-item">Audit</a>
    </li>
    <li t-if="line['caret_options'] == 'tax.report.line'">
      <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" action="open_tax_report_line" class="dropdown-item">Audit</a>
    </li>
    <li t-if="line['caret_options'] == 'default.tax.report'">
        <a role="menuitem"
            tabindex="-1"
            t-att-data-args="line['caret_options_args']"
            action="action_dropdown_audit_default_tax_report"
            class="dropdown-item"
        >Audit</a>
    </li>
    <li t-if="line['caret_options'] == 'account.bank.statement'"><a role="menuitem" tabindex="-1" t-att-data-id="line['id']" t-att-data-model="line.get('model', 'account.move.line')" data-object="account.bank.statement" action="open_document" class="dropdown-item">View Bank Statement</a></li>
    <li t-if="line['caret_options'] == 'account.analytic.account'">
        <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" action="open_analytic_entries" class="dropdown-item">Analytic Entries</a>
    </li>
    <li t-if="line['caret_options'] == 'res.partner'" role="presentation" id="res.partner.carret"><a role="menuitem" tabindex="-1" t-att-data-id="line['id']" t-att-data-model="line.get('model', 'res.partner')" data-object="res.partner" action="open_document" class="dropdown-item">View Partner</a></li>
</template>

<template id="filter_info_template">
    <div class="row print_only" style='margin-top: 20px; margin-bottom: 10px;'>
        <div class="col-4">
            <t t-if="options.get('date') and options['date'].get('string')"><t t-esc="options['date']['string']"/></t>
            <t t-if="not options.get('multi_company')">
                <br/><t t-esc="report.get('company_name')"/>
            </t>
            <t t-if="options.get('multi_company')">
                <br/>Companies:
                <t t-set="company_value">All</t>
                <t t-foreach="options['multi_company']" t-as="c">
                    <t t-set="company_value" t-value="''"/>
                    <t t-esc="c.get('name')"/>
                </t>
                <t t-esc="company_value"/>
            </t>
        </div>
        <div class="col-4">
            <div class="col-12">
                <t t-if="options.get('journals')">
                    Journals:
                    <t t-set="journal_value">All</t>
                    <t t-foreach="options['journals']" t-as="j">
                        <t t-if="j.get('selected') == True">
                            <t t-set="journal_value" t-value="''"/>
                            <t t-esc="j.get('code')"/>
                        </t>
                    </t>
                    <t t-esc="journal_value"/>
                </t>
            </div>
            <div class="col-12">
                <t t-if="options.get('analytic_accounts') != None">
                    Analytic Accounts:
                    <t t-set="analytic_account_value">All</t>
                    <t t-foreach="options['selected_analytic_account_names']" t-as="analytic_account_name">
                        <t t-set="analytic_account_value" t-value="''"/>
                        <t t-esc="analytic_account_name"/>
                    </t>
                    <t t-esc="analytic_account_value"/>
                </t>
            </div>
            <div class="col-12">
                <t t-if="options.get('analytic_tags') != None">
                    Analytic Tags:
                    <t t-set="analytic_tag_value">All</t>
                    <t t-foreach="options['selected_analytic_tag_names']" t-as="analytic_tag_name">
                        <t t-set="analytic_tag_value" t-value="''"/>
                        <t t-esc="analytic_tag_name"/>
                    </t>
                    <t t-esc="analytic_tag_value"/>
                </t>
            </div>
            <div class="col-12">
                <t t-if="options.get('partner_ids') != None">
                    Partners:
                    <t t-set="res_partner_value">All</t>
                    <t t-foreach="options['selected_partner_ids']" t-as="partner_name">
                        <t t-set="res_partner_value" t-value="''"/>
                        <t t-esc="partner_name"/>
                    </t>
                    <t t-esc="res_partner_value"/>
                </t>
            </div>
            <div class="col-12">
                <t t-if="options.get('partner_categories') != None">
                    Partner Categories:
                    <t t-set="res_partner_category_value">All</t>
                    <t t-foreach="options['selected_partner_categories']" t-as="partner_category_name">
                        <t t-set="res_partner_category_value" t-value="''"/>
                        <t t-esc="partner_category_name"/>
                    </t>
                    <t t-esc="res_partner_category_value"/>
                </t>
            </div>
            <div class="col-12">
                <t t-if="options.get('ir_filters') != None">
                    Filter:
                    <t t-set="selected_filter_name">None</t>
                    <t t-foreach="options['ir_filters']" t-as="current_filter">
                        <t t-if="current_filter['selected']">
                            <t t-set="selected_filter_name" t-value="current_filter['name']"/>
                        </t>
                    </t>
                    <t t-esc="selected_filter_name"/>
                </t>
            </div>
            <div class="col-12">
                <t t-if="options.get('groupby_fields') and any(opt['selected'] for opt in options['groupby_fields'])">
                    Groupby:
                    <t t-esc="' '.join(opt['name'] for opt in options['groupby_fields'] if opt['selected'])"/>
                </t>
            </div>
            <div class='col-12'>
                <t t-if="options.get('company_currency')">
                    Company Currency: <t t-esc="options['company_currency']['currency_name']"/>
                </t>
            </div>
        </div>
    </div>
</template>

<template id="cell_template">
    <t t-esc="cell.get('name')"/>
</template>

<template id="line_template">
    <t t-foreach="lines['lines']" t-as="line">
        <t t-set="trclass" t-value="'o_account_reports_default_style'"/>
        <t t-set="domainClass" t-value="'o_account_reports_domain_line_2 account_report_line_name'"/>
        <t t-if="not line.get('caret_options')">
            <t t-if="line.get('level') != None">
                <t t-set="trclass" t-value="'o_account_searchable_line o_account_reports_level'+str(line['level'])"/>
            </t>
        </t>
        <t t-if="line.get('caret_options')">
            <t t-if="line.get('level')">
                <t t-set="domainClass" t-value="'o_account_reports_domain_line_' + str(line['level']) + ' account_report_line_name'"/>
            </t>
        </t>

        <tr t-attf-class="#{trclass} #{line.get('class', '')} #{'o_js_account_report_parent_row_unfolded' if line.get('unfolded', False) else ''}"
            tabindex="0"
            t-att-data-pagebreak="'before' if (line.get('page_break') and context.get('print_mode')) else None"
            t-att-data-parent-id="line.get('parent_id', False)"
            t-att="{k: v for k, v in line.items() if k.startswith('data-')}"
            t-att-style="line.get('style', '')">
            <td t-att-data-id="line['id']" t-att-class="'o_account_report_name_ellipsis o_account_report_line o_account_report_line_indent ' + (line.get('unfoldable') and 'js_account_report_foldable o_foldable_total' or '') + ' ' + line.get('name_class', '')" t-att-data-unfolded="line.get('unfolded', False)"
            t-att-data-offset="line.get('offset', False)" t-att-data-progress="line.get('progress', False)" t-att-data-remaining="line.get('remaining', False)"  t-att-colspan="line.get('colspan', '1')"
            >
                    <t t-if="line.get('unfoldable')">
                        <span t-att-data-id="line['id']" class="o_account_reports_caret_icon">
                            <i class="fa fa-caret-down" t-if="line.get('unfolded')" role="img" aria-label="Unfolded" title="Unfolded"/>
                            <i class="fa fa-caret-right" t-if="not line.get('unfolded')" role="img" aria-label="Folded" title="Folded"/>
                        </span>
                    </t>
                    <t t-elif="not line.get('caret_options')">
                        <span t-att-data-id="line['id']" class="o_account_reports_caret_icon">
                            <i class="fa fa-caret-right invisible" role="img" aria-label="Unfolded" title="Unfolded"/>
                        </span>
                    </t>
                    <t t-if="not line.get('caret_options')">
                            <span t-if="not context.get('print_mode')" class="ellipsis_width">
                                <t t-esc="line.get('name')"/>
                            </span>
                            <span class="account_report_line_name" t-att-title="line.get('title_hover')"
                                t-att-data-account-code="line.get('code')">
                                <a t-if="line.get('action_id')" t-att-data-id="line['id']" t-att-data-action-id="line['action_id']" t-att-action="line.get('action', 'execute_action')">
                                    <t t-esc="line.get('name')"/>
                                </a>
                                <t t-else="" t-esc="line.get('name')"/>
                                <span class="js_account_report_line_footnote" t-att-data-id="line['id']">
                                    <sup t-if="line.get('footnote')"><b class="o_account_reports_footnote_sup"><a t-att-href="'#footnote'+line['footnote']"></a><t t-esc="line['footnote']"/></b></sup>
                                </span>
                            </span>
                    </t>
                    <t t-else="">
                        <t t-if="not context.get('print_mode')">
                            <div class="dropdown">
                                <a class="dropdown-toggle" data-toggle="dropdown" href='#'>
                                    <span t-att-data-id="line['id']" t-att-class="domainClass" t-att-title="line.get('title_hover')"
                                        t-att-data-account-code="line.get('code')">
                                        <t t-esc="line.get('name')"/> <i class="fa fa-caret-down no_print"/>
                                    </span>
                                </a>
                                <span class="js_account_report_line_footnote" t-att-data-id="line['id']"><sup t-if="line.get('footnote')"><b class="o_account_reports_footnote_sup"><a t-att-href="'#footnote'+line['footnote']"></a><t t-esc="line['footnote']"/></b></sup></span>
                                <div class="dropdown-menu o_account_reports_domain_dropdown" role="menu">
                                    <t t-call="{{model._get_templates()['line_caret_options']}}"/>
                                    <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" class="dropdown-item js_account_reports_add_footnote">Annotate</a>
                                </div>
                            </div>
                            <span t-att-class="domainClass + ' ellipsis_width'">
                                <t t-esc="line.get('name')"/> <i class="fa fa-caret-down no_print"/>
                            </span>
                        </t>
                        <t t-else="">
                            <span t-att-data-id="line['id']" t-att-class="domainClass" t-att-title="line.get('title_hover')"
                                t-att-data-account-code="line.get('code')">
                                <t t-esc="line.get('name')"/>
                            </span>
                            <span class="js_account_report_line_footnote" t-att-data-id="line['id']"><sup t-if="line.get('footnote')"><b class="o_account_reports_footnote_sup"><a t-att-href="'#footnote'+line['footnote']"></a><t t-esc="line['footnote']"/></b></sup></span>
                        </t>
                    </t>
            </td>
            <t t-set="column_index" t-value="0"/>
            <t t-foreach="line.get('columns')" t-as="cell">
                <t t-set="hierarchies_enabled" t-value="len(lines.get('columns_header', [])) > 1"/>
                <t t-set="cell_classes" t-value="(cell.get('class', lines.get('columns_header')[-1][column_index+line.get('colspan', 1)].get('class', '')) + (line.get('unfoldable') and ' o_foldable_total' or ''))"/>
                <td t-att-class="'o_account_report_line ' + cell_classes"
                    t-att-style="cell.get('style', lines.get('columns_header')[-1][column_index+line.get('colspan', 1)].get('style', ''))">
                    <t t-if="cell.get('carryover_popup_data')">
                        <t t-call="{{cell.get('template', 'account_reports.cell_template_popup_carryover')}}"/>
                    </t>
                    <span class="o_account_report_column_value" t-att-title="cell.get('title')">
                        <t t-call="{{cell.get('template', 'account_reports.cell_template')}}"/>
                    </span>
                    <t t-if="'o_account_report_line_ellipsis' in cell_classes">
                        <span t-if="not context.get('print_mode')" class="ellipsis_width">
                            <t t-call="{{cell.get('template', 'account_reports.cell_template')}}"/>
                        </span>
                    </t>
                </td>
                <t t-set="column_index" t-value="column_index + 1"/>
            </t>
        </tr>
    </t>
</template>

<template id="main_table_header">
    <thead class="o_account_reports_header_hierarchy">
        <t t-foreach="lines.get('columns_header')" t-as="header_line">
            <tr>
                <t t-foreach="header_line" t-as="cell">
                    <th t-att-class="'o_account_report_column_header ' + (cell.get('class', ''))"
                        t-att-colspan="cell.get('colspan')"
                        t-att-style="cell.get('style', '')"
                        t-att-title="cell.get('title')"
                        t-att-data-toggle="cell.get('data-toggle')">
                        <t t-call="{{cell.get('template', 'account_reports.cell_template')}}"/>
                    </th>
                </t>
            </tr>
        </t>
    </thead>
</template>

<template id="main_template">
    <div class='o_account_reports_body'>
        <div id="warnings_div">
            <div class="alert alert-info text-center mb-0 no_print" t-if="not options.get('all_entries') and options.get('unposted_in_period')">
                <span>There are <a action="open_unposted_moves">unposted Journal Entries</a> prior or included in this period</span>
            </div>
            <div class="alert alert-warning text-center mb-0 no_print" t-if="options.get('contains_archived_tag')">
                <span>Some journal items appear to point to obsolete report lines. <a action="action_archived_tag_move">Check them</a> and correct their tax tags if necessary.</span>
            </div>
        </div>
        <div class="o_account_reports_page o_account_reports_no_print">

            <div class="o_account_reports_header">
                <div class="print_only"><h2><t t-esc="report.get('name')"></t></h2></div>
                <t t-call="account_reports.filter_info_template"/>
            </div>
            <div class="o_account_reports_summary"/>
            <div class="table-responsive">
                <table class="o_account_reports_table table-hover">
                    <t t-call="{{model._get_templates()['main_table_header_template']}}"/>
                    <tbody>
                        <t t-if="lines.get('lines')">
                            <t t-call="{{model._get_templates()['line_template']}}"/>
                        </t>
                    </tbody>
                </table>
            </div>
            <div class="js_account_report_footnotes"/>
        </div>
    </div>
</template>

<!-- Financial reports templates -->
<template id="cell_template_show_bug_financial_reports">
    <span class="fa fa-bug"/>
</template>

<template id="cell_template_debug_popup_financial_reports">
    <span class="js_popup_formula" t-att-code="cell['line_code']" t-esc="cell['line_code']"/>
    <a
        t-att-class="'o_account_report_popup ' + cell['popup_class']"
        t-att-template="cell['popup_template']"
        t-att="cell['popup_attributes']"
        t-att-data="cell['popup_data']"
       role="button"/>
</template>

<template id="cell_template_popup_carryover">
    <a
        t-att-class="'o_account_report_popup fa fa-info-circle'"
        t-att-template="cell['popup_template']"
        t-att="{'tabindex': 1}"
        t-att-data="cell['carryover_popup_data']"
        role="button"/>
</template>

<template id="main_template_with_filter_input_accounts" inherit_id="account_reports.main_template"  primary="True">
    <xpath expr="//div[hasclass('print_only')]" position="after">
        <div class="o_reports_filter_input_wrapper no_print">
            <span class="searchIcon"><i class="fa fa-search" role="img" aria-label="Search" title="Search"></i></span>
            <input class="o_account_reports_filter_input" type="text" placeholder="Search account" />
        </div>
    </xpath>
</template>

<template id="main_template_with_filter_input_partner" inherit_id="account_reports.main_template_with_filter_input_accounts"  primary="True">
    <xpath expr="//input[hasclass('o_account_reports_filter_input')]" position="attributes">
        <attribute name="placeholder">Search partner</attribute>
    </xpath>
</template>

<!-- Control domain templates -->
<!-- Warning (banner) on top, related to control domains -->
<template id="main_template_control_domain" inherit_id="account_reports.main_template" primary="True">
    <xpath expr="//div[@id='warnings_div']" position="inside">
        <div class="alert alert-warning text-center mb-0 no_print" t-if="options.get('control_domain_missing_ids')">
            <span>
                Journal Items on the
                <a action="open_control_domain_missing" class="no_print oe_link_reports">following accounts</a>
                might be missing from the proper section of the report.
            </span>
        </div>
        <div class="alert alert-warning text-center mb-0 no_print" t-if="options.get('control_domain_excess_ids')">
            <span>
                Journal items on the
                <a action="open_control_domain_excess" class="no_print oe_link_reports">following accounts</a>
                might be listed in an incorrect section of the report.
            </span>
        </div>
    </xpath>
</template>

<!-- Bank Reconciliation Report Templates -->

<!-- Custom template to add some warnings. -->
<template id="bank_reconciliation_report_main_template" inherit_id="account_reports.main_template" primary="True">
    <xpath expr="//div[@id='warnings_div']" position="inside">

        <!-- Warning on top about unconsistent bank statements. -->
        <div class="alert alert-warning text-center mb-0 no_print" t-if="options.get('unconsistent_statement_ids')">
            <span>There are some <a action="open_unconsistent_statements">statements</a> having a starting balance different than the previous ending balance</span>
        </div>

        <!-- Warning on top about miscellaneous move lines affecting the bank accounts. -->
        <div class="alert alert-info text-center mb-0 no_print" t-if="options.get('has_bank_miscellaneous_move_lines')">
            <span>"<t t-esc="options['account_names']"/>" account balance is affected by <a action="open_bank_miscellaneous_move_lines">journal items</a> which doesn't result from a bank statement nor payments.</span>
        </div>
    </xpath>
</template>

<!-- Custom cell_template to get a clickable link to the last bank statement -->
<template id="bank_reconciliation_report_cell_template_link_last_statement">
    Last Statement: <a action="action_redirect_to_bank_statement_form" href="#" t-att-data-statement_id="cell['last_statement_id']"><t t-esc="cell['last_statement_name']"/></a>
</template>

<!-- Custom cell_template to render the unexplained difference in the balance line -->
<template id="bank_reconciliation_report_cell_template_unexplained_difference">
    <span class="fa fa-warning" t-att-title="cell['title']"/>&amp;nbsp;<t t-esc="cell['name']"/>
</template>

<!-- Aged partner balance templates -->
<template id="template_aged_partner_balance_line_report" inherit_id="account_reports.line_template" primary="True">
    <xpath expr="//span[hasclass('o_account_reports_caret_icon')]" position="before">
        <t t-if="line.get('trust') == 'good'">
            <span class="fa fa-circle color-green trust-partner" role="img" aria-label="Balance is good" title="Balance is good"></span>
        </t>
        <t t-if="line.get('trust') == 'bad'">
            <span class="fa fa-circle color-red trust-partner" role="img" aria-label="Balance is bad" title="Balance is bad"></span>
        </t>
        <t t-if="line.get('trust') == 'normal'">
            <span class="fa fa-circle color-transparent trust-partner" role="img" aria-label="Balance is normal" title="Balance is normal"></span>
        </t>
    </xpath>
    <xpath expr="//*[hasclass('dropdown-menu')]" position="inside">
        <a role="menuitem" tabindex="-1" t-att-data-id="line['id']" t-att-parent-id="line['parent_id']" class="o_change_expected_date dropdown-item" groups="account.group_account_user">Change expected payment date</a>
    </xpath>
</template>

<template id="template_aged_partner_balance_report" inherit_id="account_reports.main_template" primary="True">
    <xpath expr="//table" position="attributes">
        <attribute name="class">o_account_reports_table table-striped table-hover</attribute>
    </xpath>
</template>

<!-- Business Statements Aged receivable templates  -->
 <template id="line_template_aged_receivable_report" inherit_id="account_reports.template_aged_partner_balance_line_report" primary="True">
     <xpath expr="//span[hasclass('account_report_line_name')]" position="inside">
        &amp;nbsp;<a t-if="'partner_id' in line and line.get('unfolded')" t-att-data-partner_id="line.get('partner_id')" action="action_partner_reconcile" class="no_print oe_link_reports"  groups="account.group_account_user">Reconcile</a>
     </xpath>
 </template>

 <!-- Business Statements Aged Payable templates -->
 <template id="line_template_aged_payable_report" inherit_id="account_reports.template_aged_partner_balance_line_report" primary="True">
     <xpath expr="//span[hasclass('account_report_line_name')]" position="inside">
        &amp;nbsp;<a t-if="'partner_id' in line and line.get('unfolded')" t-att-data-partner_id="line.get('partner_id')" action="action_partner_reconcile" class="no_print oe_link_reports" groups="account.group_account_user">Reconcile</a>
     </xpath>
 </template>

<!-- partner and general ledger templates -->
<template id="line_template_partner_ledger_report" inherit_id="account_reports.line_template" primary="True">
    <xpath expr="//span[hasclass('o_account_reports_caret_icon')]" position="before">
        <t t-if="line.get('trust') == 'good'">
            <span class="fa fa-circle color-green trust-partner" role="img" aria-label="Partner ledger is good" title="Partner ledger is good"></span>
        </t>
        <t t-elif="line.get('trust') == 'bad'">
            <span class="fa fa-circle color-red trust-partner" role="img" aria-label="Partner ledger is bad" title="Partner ledger is bad"></span>
        </t>
        <t t-elif="line.get('trust') == 'normal'">
            <span class="fa fa-circle color-transparent trust-partner" role="img" aria-label="Partner ledger is normal" title="Partner ledger is normal"></span>
        </t>
        <t t-else="">
            <span class="fa fa-circle color-transparent trust-partner" role="img"/>
        </t>
    </xpath>
    <xpath expr="//span[hasclass('account_report_line_name')]" position="inside">
       &amp;nbsp;<a t-if="line.get('partner_id') and line.get('unfolded')" t-att-data-partner_id="line.get('partner_id')" action="action_partner_reconcile" class="no_print oe_link_reports">Reconcile</a>
    </xpath>
</template>
<template id="line_template_general_ledger_report" inherit_id="account_reports.line_template" primary="True">
    <xpath expr="//span[hasclass('account_report_line_name')]" position="inside">
        &amp;nbsp;<a t-if="'hierarchy' not in str(line.get('id')) and line.get('unfolded')" t-att-data-id="line.get('id')" action="view_all_journal_items" class="no_print oe_link_reports"> ⇒ journal items</a>
    </xpath>
</template>

<!-- multicurrency revaluation report templates -->
<template id="template_multicurrency_report" inherit_id="account_reports.main_template" primary="True">
    <xpath expr="//div[hasclass('o_account_reports_page')]" position="before">
        <div class="alert alert-warning text-center mb-0 no_print" t-if="options.get('custom_rate')">
            <span>You are using custom exchange rates.
            <a type="button" class="btn btn btn-link js_account_report_custom_currency" style="padding: 0; vertical-align=baseline;" data-filter='current_currency'>⇒ Reset to Odoo’s Rate</a></span>
        </div>
        <div class="alert alert-warning text-center mb-0 no_print" t-if="options.get('warning_multicompany')">
            <span>This report only displays the data of the active company.</span>
        </div>
    </xpath>
</template>
<template id="line_template_multicurrency_report" inherit_id="account_reports.line_template" primary="True">
    <xpath expr="//span[hasclass('account_report_line_name')]" position="inside">
        &amp;nbsp;<a t-if="'currency_id' in line and not 'account_id' in line" t-att-data-id="line['currency_id']" action="view_currency" class="no_print oe_link_reports">⇒ Rates</a>
        &amp;nbsp;<a t-if="'account_id' in line" t-att-data-id="line['account_id']" action="open_general_ledger" class="no_print oe_link_reports">⇒ General Ledger</a>
        &amp;nbsp;<a t-if="'currency_id' in line and 'account_id' in line and line.get('included') == 1" t-att-data-account_id="line['account_id']" t-att-data-currency_id="line['currency_id']" action="toggle_provision" class="no_print oe_link_reports" title="Exclude from adjustment/provisions entries">Exclude</a>
        <a t-if="'currency_id' in line and 'account_id' in line and line.get('included') == 0" t-att-data-account_id="line['account_id']" t-att-data-currency_id="line['currency_id']" action="toggle_provision" class="no_print oe_link_reports" title="Include in adjustment/provisions entries">Include</a>
    </xpath>
</template>

<!-- stylesheets -->
<!-- This template is only used when printing a financial report to a PDF output -->
<template id="print_template">
    <html>
        <head>
            <base t-att-href="base_url"/>
            <meta http-equiv="content-type" content="text/html; charset=utf-8" />
            <t t-call-assets="account_reports.assets_financial_report" t-js="False"/>
        </head>
        <body class="o_account_reports_body_print">
            <t t-out="body_html"/>
        </body>
    </html>
</template>
</data>
</odoo>
