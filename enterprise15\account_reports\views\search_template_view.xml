<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>

    <template id="search_template_date_filter">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-calendar" title="Dates" role="img" aria-label="Dates"/>
            <t t-if="options['date'].get('string') and options['date'].get('filter') != 'custom'"><t t-esc="options['date']['string']"/></t>
            <t t-if="options['date'].get('filter') == 'custom'">
                <t t-if="options['date']['mode'] == 'single'">Date: <span class="js_format_date"><t t-esc="options['date']['date_to']"/></span></t>
                <t t-if="options['date']['mode'] == 'range'">From: <span class="js_format_date"><t t-esc="options['date']['date_from']"/></span> <br/><span class="o_reports_date_to">to: <span class="js_format_date"><t t-esc="options['date']['date_to']"/></span></span></t>
            </t>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <t t-if="options['date']['mode'] == 'single'">
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="Today" data-filter="today">Today</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="End of Last Month" data-filter="last_month">End of Last Month</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="End of Last Quarter" data-filter="last_quarter">End of Last Quarter</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="End of Last Financial Year" data-filter="last_year">End of Last Financial Year</a>
            </t>
            <t t-if="options['date']['mode'] == 'range'">
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="This Month" data-filter="this_month">This Month</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="This Quarter" data-filter="this_quarter">This Quarter</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="This Financial Year" data-filter="this_year">This Financial Year</a>
                <div role="separator" class="dropdown-divider"></div>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="Last Month" data-filter="last_month">Last Month</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="Last Quarter" data-filter="last_quarter">Last Quarter</a>
                <a role="menuitem" class="dropdown-item js_account_report_date_filter" title="Last Financial Year" data-filter="last_year">Last Financial Year</a>
            </t>
            <div role="separator" class="dropdown-divider"></div>
            <a role="menuitem" class="dropdown-item js_foldable_trigger o_closed_menu" data-filter='custom_filter_date'>Custom</a>
            <div class="o_foldable_menu o_closed_menu o_account_report_search" data-filter='custom_filter_date'>
                <t t-if="options['date']['mode'] == 'range'">
                    <label class="custom-date" for="date_from">Start Date :</label>
                    <div class="form-group custom-date">
                        <div class='o_datepicker js_account_reports_datetimepicker' t-att-data-default-value="options['date']['date_from']">
                            <input type='text' class="o_datepicker_input" name="date_from" />
                            <span class="o_datepicker_button"/>
                        </div>
                    </div>
                </t>
                <label class="custom-date" for="date_to" t-if="options['date']['mode'] == 'single'">End Date :</label>
                <label class="custom-date" for="date_to" t-if="options['date']['mode'] == 'range'">Date :</label>
                <div class="form-group custom-date">
                    <div class='o_datepicker js_account_reports_datetimepicker' t-att-data-default-value="options['date']['date_to']">
                        <input type='text' class="o_datepicker_input" name="date_to" />
                        <span class="o_datepicker_button"/>
                    </div>
                </div>
                <button type="button" class="btn btn-primary js_account_report_date_filter" data-filter="custom">Apply</button>
            </div>
        </div>
    </template>

    <template id="search_template_comparison">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-bar-chart"/> Comparison:
            <t t-if="options['comparison'].get('string') and options['comparison'].get('filter') != 'custom'"><t t-esc="options['comparison']['string']"/></t>
            <t t-if="options['comparison'].get('filter') == 'custom'">
                <t t-if="options['date']['mode'] == 'range'"><span class="o_reports_date_to"><span class="js_format_date"><t t-esc="options['comparison']['date_from']"/></span></span> <span class="o_reports_date_to"> - <span class="js_format_date"><t t-esc="options['comparison']['date_to']"/></span></span></t>
                <t t-if="options['date']['mode'] == 'single'">Date: <span class="js_format_date"><t t-esc="options['comparison']['date_to']"/></span></t>
            </t>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <a role="menuitem" title="No Comparison" data-filter="no_comparison" class="dropdown-item js_account_report_date_cmp_filter">No Comparison</a>
            <div role="separator" class="dropdown-divider"></div>
            <a role="menuitem" class="dropdown-item js_foldable_trigger o_closed_menu" data-filter='previous_period_filter'>Previous Period</a>
            <div class="o_foldable_menu o_closed_menu o_account_report_search" data-filter='previous_period_filter'>
                <label for="periods_number">Number of periods :</label><br />
                <input type="number" name="periods_number" t-att-value="options['comparison'].get('number_period')"/><br />
                <button type="button" class="btn btn-primary js_account_report_date_cmp_filter" data-filter='previous_period'>Apply</button>
            </div>
            <div role="separator" class="dropdown-divider"></div>
            <a role="menuitem" class="dropdown-item js_foldable_trigger o_closed_menu" data-filter='same_last_year_filter'>Same Period Last Year</a>
            <li class="o_foldable_menu o_closed_menu o_account_report_search" data-filter='same_last_year_filter'>
                <label for="periods_number">Number of periods :</label><br />
                <input type="number" name="periods_number" t-att-value="options['comparison'].get('number_period')"/><br />
                <button type="button" class="btn btn-primary js_account_report_date_cmp_filter" data-filter='same_last_year'>Apply</button>
            </li>
            <div role="separator" class="dropdown-divider"></div>
            <a role="menuitem" class="dropdown-item js_foldable_trigger o_closed_menu" data-filter='custom_cmp_filter'>Custom</a>
            <div class="o_foldable_menu o_closed_menu o_account_report_search" data-filter='custom_cmp_filter'>
                <t t-if="options['date']['mode'] == 'range'">
                    <label class="custom-date" for="date_from_cmp">Start Date :</label>
                    <div class="form-group custom-date">
                        <div class='o_datepicker js_account_reports_datetimepicker' t-att-data-default-value="options['comparison']['date_from']">
                            <input type='text' class="o_datepicker_input" name="date_from_cmp" />
                            <span class="o_datepicker_button"/>
                        </div>
                    </div>
                </t>
                <label class="custom-date" for="date_to_cmp" t-if="options['date']['mode'] == 'range'">End Date :</label>
                <label class="custom-date" for="date_to_cmp" t-if="options['date']['mode'] == 'single'">Date :</label>
                <div class="form-group custom-date">
                    <div class='o_datepicker js_account_reports_datetimepicker' t-att-data-default-value="options['comparison']['date_to']">
                        <input type='text' class="o_datepicker_input" name="date_to_cmp" />
                        <span class="o_datepicker_button"/>
                    </div>
                </div>
                <button type="button" class="btn btn-primary js_account_report_date_cmp_filter" data-filter='custom'>Apply</button>
            </div>
        </div>
    </template>

    <template id="search_template_extra_options">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-filter"/> Options:
            <t t-if="options.get('all_entries') != None" groups="account.group_account_readonly">
                <t t-if="options['all_entries']">With Draft Entries</t>
                <t t-if="not options['all_entries']">Posted Entries Only</t>
            </t>
            <t t-if="options.get('unreconciled') != None">
                <t t-if="options.get('all_entries') != None">,</t>
                <t t-if="options['unreconciled']">Only Show Unreconciled Entries</t>
            </t>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <a role="menuitem" class="dropdown-item js_account_report_bool_filter" t-if="options.get('all_entries') != None" title="Include unposted entries" data-filter="all_entries" groups="account.group_account_readonly">Include Unposted Entries</a>
            <a role="menuitem" class="dropdown-item js_account_report_bool_filter" t-if="options.get('hierarchy') != None" title="Hierarchy" data-filter="hierarchy">Hierarchy and Subtotals</a>
            <a role="menuitem" class="dropdown-item js_account_report_bool_filter" t-if="options.get('unreconciled') != None" title="Unreconciled" data-filter="unreconciled">Only Show Unreconciled Entries</a>
            <a role="menuitem" class="dropdown-item js_account_report_bool_filter" t-if="options.get('unfold_all') != None" title="Unfold" data-filter="unfold_all">Unfold All</a>
        </div>
    </template>

    <template id="search_template_journals">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-book"/>
            Journals:
                <t t-esc="options.get('name_journal_group')"/>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <t t-foreach="options['journals']" t-as="j">
                <t t-if="j.get('id') == 'divider'">
                    <div role="separator" class="dropdown-divider"/>
                    <b role="menuitem" class="ml4 dropdown-item-text"><t t-esc="j.get('name')"/></b>
                    <div role="separator" class="dropdown-divider"/>
                </t>
                <t t-if="j.get('id') != 'divider'">
                    <t t-if="j.get('id') == 'group'">
                        <a role="menuitem" class="dropdown-item js_account_report_group_choice_filter" data-filter="journals" t-att-data-id="j.get('id')" t-att-data-member-ids="j.get('ids')">
                            <t t-esc="j.get('name')"/>
                        </a>
                    </t>
                    <t t-else="">
                        <a role="menuitem" class="dropdown-item js_account_report_choice_filter" t-att-title="j.get('name')+ '-' + j.get('code')" t-att-data-id="j.get('id')" data-filter="journals">
                            <t t-esc="j.get('name')"/>
                        </a>
                    </t>
                </t>
            </t>
        </div>
    </template>

    <template id="search_template_account_type">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-user"/>
            Account:
            <t t-set="account_value">Both</t>
            <t t-foreach="options['account_type']" t-as="a">
                <t t-if="a.get('selected') == True">
                    <t t-set="account_value" t-value="''"/>
                    <t t-esc="a.get('name')"/>
                </t>
            </t>
            <t t-esc="account_value"/>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <t t-foreach="options['account_type']" t-as="a">
                <a role="menuitem" class="dropdown-item js_account_report_choice_filter" t-att-title="a.get('name')" t-att-data-id="a.get('id')" data-filter="account_type"><t t-esc="a.get('name')"/></a>
            </t>
        </div>
    </template>

    <template id="search_template_analytic">
        <button t-if="options.get('analytic')" type="button" class="o-no-caret btn btn-secondary dropdown-toggle account_analytic_filter" data-toggle="dropdown">
            <span class="fa fa-folder-open"/> Analytic
        </button>
        <ul class="dropdown-menu o_filter_menu" role="menu">
            <li class="o_account_report_search js_account_analytic_m2m"/>
        </ul>
    </template>

    <template id="search_template_ir_filters">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-filter"/>
            Filters:
            <t t-set="filter_name">None</t>
            <t t-foreach="options['ir_filters']" t-as="ir_filter">
                <t t-if="ir_filter['selected']">
                    <t t-set="filter_name" t-value="ir_filter['name']"/>
                </t>
            </t>
            <t t-esc="filter_name"/>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <t t-foreach="options['ir_filters']" t-as="ir_filter">
                <a t-att-title="ir_filter['name']"
                    t-att-data-id="ir_filter['id']"
                    data-filter="ir_filters"
                    class="dropdown-item js_account_report_choice_filter"
                    role="menuitem">
                    <t t-esc="ir_filter['name']"/>
                </a>
            </t>
            <div class="dropdown-divider" role="separator"></div>
        </div>
    </template>

    <template id="search_template_groupby_fields">
        <a type="button" class="dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-filter"/>
            Group By:
            <t t-esc="','.join(opt['name'] for opt in options['groupby_fields'] if opt['selected'])"/>
        </a>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <t t-foreach="options['groupby_fields']" t-as="opt">
                <a role="menuitem" class="dropdown-item js_account_report_choice_filter"
                   t-att-title="opt['name']"
                   t-att-data-id="opt['id']"
                   data-filter="groupby_fields"><t t-esc="opt['name']"/></a>
            </t>
        </div>
    </template>

    <template id="search_template_partner">
        <button t-if="options.get('partner')" type="button" class="o-no-caret btn btn-secondary dropdown-toggle account_partner_filter" data-toggle="dropdown">
            <span class="fa fa-folder-open"/> Partners
        </button>
        <ul class="dropdown-menu o_filter_menu" role="menu">
            <li class="o_account_report_search js_account_partner_m2m"/>
        </ul>
    </template>

    <template id="search_template_currency">
        <button type="button" class="dropdown-toggle btn btn-secondary" data-toggle="dropdown">
            <span class="fa fa-line-chart"/> Exchange Rates
        </button>
        <div class="dropdown-menu o_filters_menu" role="menu">
            <div class="o_account_report_search" data-filter='custom_currency'>
                <div t-foreach="options['currency_rates'].values()" t-as="opt" class="form-group">
                    <label t-att-for="opt['currency_id']">1 <t t-esc="opt['currency_main']"/> <i class="fa fa-arrow-right"/> <t t-esc="opt['currency_name']"/></label>
                    <input t-att-value="opt['rate']" t-att-name="opt['currency_id']" type="number" min="0" step="0.000001" class="js_account_report_custom_currency_input"/>
                </div>
                <button type="button" class="btn btn-primary js_account_report_custom_currency" data-filter="custom_currency">Apply</button>
            </div>
        </div>
    </template>

    <!-- ec sales report goods, triangular & services filter -->
    <template id="search_template_ec_sale_code">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-filter"/>
            Codes:
            <t t-set="code_value">All</t>
            <t t-set="selected_codes" t-value="[c['name'] for c in options['ec_sale_code'] if c['selected']]"/>
            <t t-if="len(selected_codes) > 0">
                <t t-set="code_value" t-value="', '.join(selected_codes)"/>
            </t>
            <t t-esc="code_value"/>
            <span class="caret"/>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <t t-foreach="options['ec_sale_code']" t-as="c">
                <a t-att-title="c.get('name')"
                    data-filter="ec_sale_code"
                    t-att-data-id="c.get('id')"
                    class="dropdown-item js_account_report_choice_filter">
                    <t t-esc="c.get('name')"/>
                </a>
            </t>
        </div>
    </template>

    <template id="search_template_fiscal_position_choser">
        <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
            <span class="fa fa-book"/>
            Fiscal Position:
            <t t-if="options['fiscal_position'] == 'domestic'">
                Domestic
            </t>
            <t t-elif="options['fiscal_position'] == 'all'">
                All
            </t>
            <t t-else="">
                <t t-set="selected_fiscal_position" t-value="[opt for opt in options['available_vat_fiscal_positions'] if opt['id'] == options['fiscal_position']][0]"/>
                <t t-esc="selected_fiscal_position['name']"/>
            </t>
        </button>
        <div class="dropdown-menu o_filter_menu" role="menu">
            <a t-if="options['allow_domestic']" role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" data-filter="fiscal_position" data-id="'domestic'" groups="account.group_account_readonly">Domestic</a>
            <a role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" data-filter="fiscal_position" data-id="all" groups="account.group_account_readonly">All</a>
            <div role="separator" class="dropdown-divider"/>
            <t t-foreach="options['available_vat_fiscal_positions']" t-as="fiscal_position">
                <a role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" t-att-title="fiscal_position['name']" t-att-data-id="fiscal_position['id']" data-filter="fiscal_position">
                    <t t-esc="fiscal_position['name']"/>
                </a>
            </t>
        </div>
    </template>

    <template id="search_template">
        <div class="btn-group dropdown o_account_reports_filter_date" t-if="options.get('date') != None">
            <t t-call="account_reports.search_template_date_filter"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_date_cmp" t-if="options.get('comparison') != None">
            <t t-call="account_reports.search_template_comparison"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_journals" t-if="options.get('journals') != None">
            <t t-call="account_reports.search_template_journals"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_account_type" t-if="options.get('account_type') != None">
            <t t-call="account_reports.search_template_account_type"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_analytic" t-if="options.get('analytic') != None or options.get('analytic_tags') != None">
            <t t-call="account_reports.search_template_analytic"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_ir_filters" t-if="options.get('ir_filters') != None">
            <t t-call="account_reports.search_template_ir_filters"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_groupby_fields" t-if="options.get('groupby_fields')">
            <t t-call="account_reports.search_template_groupby_fields"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_partner" t-if="options.get('partner')">
            <t t-call="account_reports.search_template_partner"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_currency" t-if="options.get('currency_rates')">
            <t t-call="account_reports.search_template_currency"/>
        </div>
        <div id="extra_options_dropdown" class="btn-group dropdown o_account_reports_filter_bool" t-if="options.get('cash_basis') != None or options.get('all_entries') != None or options.get('unfold_all') != None">
            <t t-call="account_reports.search_template_extra_options"/>
        </div>
        <div class="btn-group dropdown o_account_reports_filter_ec_sale_code"
                t-if="options.get('ec_sale_code') != None">
            <t t-call="account_reports.search_template_ec_sale_code"/>
        </div>
        <div id="fiscal_position_choice_option" class="btn-group dropdown o_account_reports_filter_fiscal_position"
            t-if="len(options['available_vat_fiscal_positions']) > (0 if options['allow_domestic'] else 1)">
            <t t-call="account_reports.search_template_fiscal_position_choser"/>
        </div>
    </template>

    <!-- Tax report templates -->
    <template id="template_tax_report" inherit_id="account_reports.main_template" primary="True">
        <xpath expr="//div[@id='warnings_div']" position="inside">
            <div class="alert alert-light text-center mb-0 no_print" t-if="options.get('group_by')">
                <span>Please note that the report may include some rounding differences towards the bookings.</span>
            </div>

            <div class="alert alert-info text-center mb-0 no_print" t-if="not model._is_generic_report(options) and options.get('available_tax_units') and options['tax_unit'] == 'company_only'">
                <span>This company is part of a tax unit. You're currently not viewing the whole unit. To change that, use the Tax Unit filter.</span>
            </div>
        </xpath>
    </template>

    <template id="search_template_generic_tax_report" inherit_id="account_reports.search_template" primary="True">
        <xpath expr="//div[@id='extra_options_dropdown']/t" position="after">
            <t t-call="account_reports.search_template_tax_report_choser"/>
            <t t-call="account_reports.search_template_tax_unit_chooser"/>
        </xpath>

        <xpath expr="//div[@id='fiscal_position_choice_option']" position="attributes">
            <attribute name="t-if">
                len(options['available_vat_fiscal_positions']) > (0 if options['allow_domestic'] else 1) and options['tax_unit'] == 'company_only'
            </attribute>
        </xpath>
    </template>

    <template id="search_template_tax_report_choser">
        <div id="tax_report_choice_option" class="btn-group dropdown o_account_reports_filter_tax_report">
            <button type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
                <span class="fa fa-book"/>
                Tax Report:
                <t t-if="options['tax_report'] == 'generic'">
                    Global Summary
                </t>
                <t t-elif="options['tax_report'] == 'generic_grouped_account_tax'">
                    Group by: Account > Tax
                </t>
                <t t-elif="options['tax_report'] == 'generic_grouped_tax_account'">
                    Group by: Tax > Account
                </t>
                <t t-else="">
                    <t t-set="selected_report" t-value="[opt for opt in options['available_tax_reports'] if opt['id'] == options['tax_report']][0]"/>
                    <t t-esc="selected_report['name']"/>
                </t>
            </button>
            <div class="dropdown-menu o_filter_menu" role="menu">
                <a role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" title="Global Summary" data-filter="tax_report" data-id="generic" groups="account.group_account_readonly">Global Summary</a>

                <a role="menuitem"
                   class="dropdown-item js_account_reports_one_choice_filter"
                   data-filter="tax_report"
                   data-id="generic_grouped_account_tax"
                   groups="account.group_account_readonly"
                >
                    Group by: Account > Tax
                </a>

                <a role="menuitem"
                   class="dropdown-item js_account_reports_one_choice_filter"
                   data-filter="tax_report"
                   data-id="generic_grouped_tax_account"
                   groups="account.group_account_readonly"
                >
                    Group by: Tax > Account
                </a>

                <div t-if="options['available_tax_reports']" role="separator" class="dropdown-divider"/>

                <t t-foreach="options['available_tax_reports']" t-as="report">
                    <a role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" t-att-title="report['name']" t-att-data-id="report['id']" data-filter="tax_report">
                        <t t-esc="report['name']"/>
                    </a>
                </t>
            </div>
        </div>
    </template>

    <template id="search_template_tax_unit_chooser">
        <div id="tax_unit_option" class="btn-group dropdown">
            <button t-if="options['available_tax_units']" type="button" class="o-no-caret btn btn-secondary dropdown-toggle" data-toggle="dropdown">
                <span class="fa fa-home"/>
                Tax Unit:
                <t>
                    <t t-if="options['tax_unit'] == 'company_only'">
                        Company Only
                    </t>
                    <t t-else="">
                        <t t-set="selected_tax_unit" t-value="[opt for opt in options['available_tax_units'] if opt['id'] == options['tax_unit']][0]"/>
                        <t t-esc="selected_tax_unit['name']"/>
                    </t>
                </t>
            </button>

            <div class="dropdown-menu o_filter_menu" role="menu">
                <a role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" data-filter="tax_unit" data-id="company_only" groups="account.group_account_readonly">Company Only</a>

                <div role="separator" class="dropdown-divider"/>

                <t t-foreach="options['available_tax_units']" t-as="available_tax_unit">
                    <a role="menuitem" class="dropdown-item js_account_reports_one_choice_filter" t-att-title="available_tax_unit['name']" t-att-data-id="available_tax_unit['id']" data-filter="tax_unit">
                        <t t-esc="available_tax_unit['name']"/>
                    </a>
                </t>
            </div>
        </div>
    </template>

    <!-- TO BE REMOVED IN MASTER -->
    <!-- Bank reconciliation report: Custom templates to get a not-editable date filter -->
    <template id="bank_reconciliation_report_search_template_date_filter">
        <t t-esc="options['date']['string']"/>
    </template>

    <!-- TO BE REMOVED IN MASTER -->
    <template id="bank_reconciliation_report_search_template" inherit_id="account_reports.search_template" primary="True">
        <xpath expr="//div[hasclass('o_account_reports_filter_date')]" position="replace">
            <div class="btn-group o_account_reports_filter_date text-muted">
                <t t-call="account_reports.bank_reconciliation_report_search_template_date_filter"/>
            </div>
        </xpath>
    </template>

    </data>
</odoo>
