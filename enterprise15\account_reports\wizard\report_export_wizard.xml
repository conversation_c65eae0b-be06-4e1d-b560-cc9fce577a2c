<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_report_export_wizard" model="ir.ui.view">
            <field name="name">account_reports.export.wizard.form</field>
            <field name="model">account_reports.export.wizard</field>
            <field name="arch" type="xml">
                <form string="Export">
                    <group>
                        <field name="export_format_ids" widget="many2many_tags" options="{'no_create': True}" domain="[('export_wizard_id', '=', id)]" required="1"/>
                    </group>
                    <group>
                        <group>
                            <field name="doc_name" required="1"/>
                        </group>
                    </group>
                    <footer>
                        <button string="Export" class="btn-primary" type="object" name="export_report" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>