# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports_cash_basis
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> V<PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_reports_cash_basis
#: model:ir.model,name:account_reports_cash_basis.model_account_report
msgid "Account Report"
msgstr "Sąskaitos ataskaita"

#. module: account_reports_cash_basis
#: model:ir.model,name:account_reports_cash_basis.model_account_financial_html_report
msgid "Account Report (HTML)"
msgstr ""

#. module: account_reports_cash_basis
#: model_terms:ir.ui.view,arch_db:account_reports_cash_basis.search_template_extra_options
msgid "Accrual Basis"
msgstr ""

#. module: account_reports_cash_basis
#: model:ir.model.fields,field_description:account_reports_cash_basis.field_account_financial_html_report__cash_basis
msgid "Allow cash basis mode"
msgstr ""

#. module: account_reports_cash_basis
#: model_terms:ir.ui.view,arch_db:account_reports_cash_basis.search_template_extra_options
msgid "Cash Basis"
msgstr "Grynųjų pinigų principas"

#. module: account_reports_cash_basis
#: model_terms:ir.ui.view,arch_db:account_reports_cash_basis.search_template_extra_options
msgid "Cash Basis Method"
msgstr "Pinigų principo metodas"

#. module: account_reports_cash_basis
#: model:ir.model,name:account_reports_cash_basis.model_account_coa_report
msgid "Chart of Account Report"
msgstr ""

#. module: account_reports_cash_basis
#: model:ir.model,name:account_reports_cash_basis.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Didžiosios Knygos Ataskaita"

#. module: account_reports_cash_basis
#: model:ir.model,name:account_reports_cash_basis.model_account_move_line
msgid "Journal Item"
msgstr "Žurnalo įrašas"

#. module: account_reports_cash_basis
#: model:ir.model.fields,help:account_reports_cash_basis.field_account_financial_html_report__cash_basis
msgid "display the option to switch to cash basis mode"
msgstr ""
