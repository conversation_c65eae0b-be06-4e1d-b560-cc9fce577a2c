# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_saft
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 19:53+0000\n"
"PO-Revision-Date: 2024-08-13 19:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_saft
#: model:ir.model,name:account_saft.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: account_saft
#: model:ir.model,name:account_saft.model_account_general_ledger
msgid "General Ledger Report"
msgstr ""

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo"
msgstr ""

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo SA"
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define `Company Registry` for your company."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Phone` or `Mobile` for your company."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one address (Zip/City) for the following partners: "
"%s."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define one or more Contacts belonging to your company."
msgstr ""
