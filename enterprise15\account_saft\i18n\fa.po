# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_saft
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> Barmshory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_saft
#: model:ir.model,name:account_saft.model_ir_attachment
msgid "Attachment"
msgstr "پیوست"

#. module: account_saft
#: model:ir.model,name:account_saft.model_account_general_ledger
msgid "General Ledger Report"
msgstr "گزارش دفتر کل"

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo"
msgstr "اودوو"

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo SA"
msgstr "Odoo SA"

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define `Company Registry` for your company."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Phone` or `Mobile` for your company."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one address (Zip/City) for the following partners: "
"%s."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one contact (Phone or Mobile) for the following "
"partners: %s."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define one or more Contacts belonging to your company."
msgstr "یک یا چند مخاطبی که به شرکت شما تعلق دارند را مشخص کنید."
