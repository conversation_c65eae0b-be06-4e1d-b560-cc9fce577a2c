# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_saft
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>risa_nexterp, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON>risa_nexterp, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_saft
#: model:ir.model,name:account_saft.model_ir_attachment
msgid "Attachment"
msgstr "Atașament"

#. module: account_saft
#: model:ir.model,name:account_saft.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Raport Carte Mare"

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo"
msgstr "Odoo"

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo SA"
msgstr "Odoo SA"

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define `Company Registry` for your company."
msgstr "Vă rugăm să definiți `Company Registry` pentru compania dvs."

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define a `Phone` or `Mobile` for your company."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one address (Zip/City) for the following partners: "
"%s."
msgstr ""
"Vă rugăm să definiți cel puțin o adresă (Zip/City) pentru următorii "
"parteneri: %s."

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one contact (Phone or Mobile) for the following "
"partners: %s."
msgstr ""
"Vă rugăm să definiți cel puțin un contact (Phone or Mobile) pentru următorii"
" parteneri: %s."

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define one or more Contacts belonging to your company."
msgstr ""
"Vă rugăm să definiți unul sau mai multe persoane de contact care aparțin "
"companiei dvs."
