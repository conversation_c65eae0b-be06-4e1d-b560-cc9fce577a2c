# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_saft
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovenian (https://www.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: account_saft
#: model:ir.model,name:account_saft.model_ir_attachment
msgid "Attachment"
msgstr "Priponka"

#. module: account_saft
#: model:ir.model,name:account_saft.model_account_general_ledger
msgid "General Ledger Report"
msgstr "Glavna knjiga - Poročila"

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo"
msgstr "Odoo"

#. module: account_saft
#: model_terms:ir.ui.view,arch_db:account_saft.saft_template
msgid "Odoo SA"
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid "Please define `Company Registry` for your company."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one address (Zip/City) for the following partners: "
"%s."
msgstr ""

#. module: account_saft
#: code:addons/account_saft/models/account_general_ledger.py:0
#, python-format
msgid ""
"Please define at least one contact (Phone or Mobile) for the following "
"partners: %s."
msgstr ""
