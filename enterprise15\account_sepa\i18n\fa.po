# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa
# 
# Translators:
# <PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "A bank account is not defined."
msgstr "یک حساب بانکی تعریف نشده است."

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment_register.py:0
#, python-format
msgid "A bank account must be set on the following documents: "
msgstr "یک حساب بانکی باید در اسناد زیر تنظیم شود:"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr "استرالیایی"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Bank account %s 's bank does not have any BIC number associated. Please "
"define one."
msgstr ""
"بانک مربوط به حساب بانکی %s دارای یک کد شناسه‌ی بانک نیست. لطفا این شماره را"
" مشخص کنید. "

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "ثبت دسته‌ای"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_batch_payment
msgid "Batch Payment"
msgstr "پرداخت دسته ای"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr ""
"نهادی که شناسه را اختصاص می‌دهد (مانند KBE-BCO یا Finanzamt Muenchen IV)."

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "Generic"
msgstr "عمومی"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_003_03
msgid "German"
msgstr "آلمانی"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__has_sepa_ct_payment_method
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr "روش پرداخت یکپارچه‌ی یورو"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification"
msgstr "شناسه"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr ""
"شناسه‌ی اختصاص داده شده بوسیله‌ی یک موسسه (مانند کد مالیات بر ارزش افزوده)."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Issuer"
msgstr "صادرکننده"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
msgid "Journal"
msgstr "دفترروزنامه‌"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Maximum amount is %s for payments in Euros, %s for other currencies."
msgstr "حداکثر مبلغ برای پرداخت به یورو %s، و %s برای سایر ارزها است. "

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr "نام طرف ارجاع بستانکار. طول آن نباید بیشتر از 70 حرف باشد."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has no country code defined."
msgstr "شریک %s هیچ کد کشور مشخصی ندارد."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has not bank account defined."
msgstr "شریک %s دارای حساب بانکی مشخصی نیست."

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_method
msgid "Payment Methods"
msgstr "روشهای پرداخت"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr "ارسال پرداخت از طریق SEPA"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr "پرداخت‌ها"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr "ارسال پرداخت از طریق SEPA"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Please first set a SEPA identification number in the accounting settings."
msgstr "ابتدا یک شناسه‌ی SEPA را در تنظیمات حسابداری مشخص کنید. "

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_register
msgid "Register Payment"
msgstr "ثبت پرداخت"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr "درخواست ثبت دسته‌ای از بانک برای صورت‌وضعیت‌های مربوطه."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "SCT Batch Booking"
msgstr "ثبت دسته‌ای SCT"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_search
msgid "SCT Payments To Send"
msgstr ""

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr "انتقال اعتبار SEPA"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal_dashboard.py:0
#, python-format
msgid "SEPA Credit Transfers to Send"
msgstr "انتقال اعتبار SEPA"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__sepa_pain_version
#: model_terms:ir.ui.view,arch_db:account_sepa.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "نسخه‌ی SEPA Pain "

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,help:account_sepa.field_account_journal__sepa_pain_version
msgid ""
"SEPA may be a generic format, some countries differ from the SEPA "
"recommendations made by the EPC (European Payment Council) and thus the XML "
"created need some tweaking."
msgstr ""
"ممکن است SEPA یک قالب عمومی باشد، برخی کشورها با توصیه‌های EPC (شورای پرداخت"
" اروپا) در مورد SEPA (مخالف هستند)، در نتیجه XML ایجاد شده نیاز به برخی "
"تنظیمات دارد."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_generic
msgid "Sct Generic"
msgstr "Sct عمومی "

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments are above the maximum amount allowed."
msgstr "برخی پرداخت‌ها بالاتر ازحداکثر مبلغ مجاز هستند."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are linked to partner addresses with characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Some payments are missing a value for 'UETR', required for the SEPA "
"Pain.001.001.09 format."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are not made on an IBAN recipient account. This batch might "
"not be accepted by certain banks because of that."
msgstr "برخی پرداخت‌ها در حساب دریافتی بین‌المللی انجام نمی‌شوند. "

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments have a name or reference containing characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have no recipient bank account set."
msgstr "برخی پرداخت‌ها هیچ حساب بانکی مشخص ندارند."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments were instructed in another currency than Euro. This batch "
"might not be accepted by certain banks because of that."
msgstr ""
"برخی پرداخت‌ها با ارز دیگری به جز یورو انجام شدند. به این دلیل ممکن است این "
"دسته مورد پذیرش چند بانک نباشد. "

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_se
msgid "Swedish"
msgstr "سوئدی"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_ch_02
msgid "Swiss"
msgstr "سوئیس"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_generic
msgid ""
"Technical feature used during the file creation. A SEPA message is said to "
"be 'generic' if it cannot be considered as a standard european credit "
"transfer. That is if the bank journal is not in €, a transaction is not in €"
" or a payee is not identified by an IBAN account number."
msgstr ""
"ویژگی فنی طی ایجاد فایل مورد استفاده قرار گرفت. یک پیام SEPA در صورتی "
"«عمومی» است که نتوان آن را به عنوان یک انتقال اعتبار اروپایی در نظر گرفت. "
"یعنی اگر دفتر روزنامه‌ی بانک به یورو نباشد، تراکنش نیز به یورو نیست و شماره "
"حساب بین‌المللی برای دریافت‌کننده‌ی مبلغ وجود نخواهد داشت. "

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"حساب %s، مرتبط با شریک %s، یک حساب بین‌المللی نیست. یک حساب بین‌المللی معتبر"
" برای استفاده از ویژگی‌های SEPA است. "

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"حساب %s، دفتر روزنامه %s، یک حساب بین‌المللی است. یک حساب بین‌المللی معتبر "
"برای استفاده از ویژگی‌های SEPA موردنیاز است. "

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The amount of the payment '%(payment)s' is too high. The maximum permitted "
"is %(limit)s."
msgstr "مبلغ پرداخت %(payment)s بسیار زیاد است. حداکثر مجاز %(limit)s است. "

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The bank defined on account %s (from partner %s) has no BIC. Please first "
"set one."
msgstr ""
"بانک مشخص شده برای حساب %s (از طرف شریک %s) هیچ کد شناسه‌‌ای ندارد. لطفاً "
"ابتدا یک کد شناسه تعیین کنید."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The BIC code '%s' associated to the bank '%s' of bank account '%s' of partner '%s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""
"کد شناسه‌ی بانک %s مربوط به بانک%s محل حساب بانکی %s شریک%s، مطابق با قواعد لازم نیست. این شناسه بایبد شامل 8 تا 11 حرف بوده و ساختار زیر را رعایت کند:\n"
"- 4 حرف: کد موسسه یا کد بانک \n"
"- 2 حرف: کد کشور\n"
"- 2 حرف یا 2 رقم: کد محل\n"
"- 3 حرف یا 3 رقم: کد شعبه، اختیاری\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"دفتر روزنامه‌ی %s نیازمند یک حساب بین‌المللی درست برای پرداخت از طریق SEPA "
"است. لطفاً ابتدا آن را پیکربندی کنید. "

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The text used in SEPA files can only contain the following characters :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Too many transactions for a single file."
msgstr "تراکنش‌های بیش از حد برای یک فایل واحد. "

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr ""
"در پرداخت‌های SEPA به عنوان نام آغازگر پرداخت نمایان می‌شود. محدود به 70 حرف"
" است. "

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Your Company Name"
msgstr "نام شرکت شما"
