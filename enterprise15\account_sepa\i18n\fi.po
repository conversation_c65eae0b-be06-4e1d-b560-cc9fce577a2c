# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <ka<PERSON>.<PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "A bank account is not defined."
msgstr "Pankkitiliä ei ole määritelty."

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment_register.py:0
#, python-format
msgid "A bank account must be set on the following documents: "
msgstr "Pankkitili on määritettävä seuraavissa asiakirjoissa: "

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr "Itävallan"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Bank account %s 's bank does not have any BIC number associated. Please "
"define one."
msgstr "Pankkitilin %s pankkiin ei liity BIC-numeroa. Määrittele sellainen."

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Erävaraus"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_batch_payment
msgid "Batch Payment"
msgstr "Erämaksu"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguraatioasetukset"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr ""
"Yhteisö joka antaa tunnisteen (esim. KBE-BCO tai Finanzamt Muenchen IV)."

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "Generic"
msgstr "Yleiset"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_003_03
msgid "German"
msgstr "Saksa"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__has_sepa_ct_payment_method
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr "Onko Sepa Ct maksutapa"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification"
msgstr "Tunnistus"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr "Yhteisötunnus (esim. VAT-tunnus)"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Issuer"
msgstr "Liikkeeseenlaskija"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
msgid "Journal"
msgstr "Päiväkirja"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Maximum amount is %s for payments in Euros, %s for other currencies."
msgstr ""
"Enimmäismäärä on %s euromääräisille maksuille ja %s muille valuutoille."

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr ""
"Velkojan viiteosapuolen nimi. Käyttösääntö: Rajoitettu 70 merkin pituuteen."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has no country code defined."
msgstr "Kumppanille %s ei ole määritelty maakoodia."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has not bank account defined."
msgstr "Kumppanilla %s ei ole määritetty pankkitiliä."

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_method
msgid "Payment Methods"
msgstr "Maksutavat"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr "Lähetettävä SEPA maksu"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr "Maksut"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr "maksua lähettävänä SEPA-maksuna"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Please first set a SEPA identification number in the accounting settings."
msgstr "Aseta ensin SEPA-tunnistenumero kirjanpitoasetuksissa."

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_register
msgid "Register Payment"
msgstr "Kirjaa maksu"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr "Pyydä pankilta erävarausta asiaan liittyviä tiliotteita varten."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "SCT Batch Booking"
msgstr "SCT-erän varaus"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_search
msgid "SCT Payments To Send"
msgstr "SCT-maksut lähetettäväksi"

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr "SEPA credit-siirto"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal_dashboard.py:0
#, python-format
msgid "SEPA Credit Transfers to Send"
msgstr "Lähetettävät SEPA-tilisiirrot"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__sepa_pain_version
#: model_terms:ir.ui.view,arch_db:account_sepa.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "SEPA Pain -versio"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,help:account_sepa.field_account_journal__sepa_pain_version
msgid ""
"SEPA may be a generic format, some countries differ from the SEPA "
"recommendations made by the EPC (European Payment Council) and thus the XML "
"created need some tweaking."
msgstr ""
"SEPA saattaa olla yleinen muoto, mutta jotkin maat poikkeavat EPC:n "
"(European Payment Council) SEPA-suosituksista, joten luotua XML-muotoa on "
"hieman muokattava."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_generic
msgid "Sct Generic"
msgstr "Sct Yleinen"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments are above the maximum amount allowed."
msgstr "Jotkin maksut ylittävät sallitun enimmäismäärän."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are linked to partner addresses with characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""
"Jotkut maksujen kumppanien osoitteet sisältävät merkkejä, joita ei tueta "
"SEPAssa. Nämä merkit on korjattu välilyönnillä."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Some payments are missing a value for 'UETR', required for the SEPA "
"Pain.001.001.09 format."
msgstr ""
"Joistakin maksuista puuttuu UETR-arvo, jota SEPA Pain.************** "
"-muodossa tarvitaan."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are not made on an IBAN recipient account. This batch might "
"not be accepted by certain banks because of that."
msgstr ""
"Joitakin maksuja ei suoriteta IBAN-vastaanottotilille. Tämän vuoksi tietyt "
"pankit eivät välttämättä hyväksy tätä erää."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments have a name or reference containing characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""
"Jotkut maksujen nimet tai viitteet sisältävät merkkejä, joita ei tueta "
"SEPAssa. Nämä merkit on korjattu välilyönnillä."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have no recipient bank account set."
msgstr "Joillekin maksuille ei ole asetettu vastaanottajan pankkitiliä."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments were instructed in another currency than Euro. This batch "
"might not be accepted by certain banks because of that."
msgstr ""
"Jotkin maksut suoritettiin muuna valuuttana kuin euroina. Tämän vuoksi "
"tietyt pankit eivät välttämättä hyväksy tätä erää."

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_se
msgid "Swedish"
msgstr "Ruotsalainen"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_ch_02
msgid "Swiss"
msgstr "Sveitsi"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_generic
msgid ""
"Technical feature used during the file creation. A SEPA message is said to "
"be 'generic' if it cannot be considered as a standard european credit "
"transfer. That is if the bank journal is not in €, a transaction is not in €"
" or a payee is not identified by an IBAN account number."
msgstr ""
"Tekninen ominaisuus, jota käytetään tiedoston luomisessa. SEPA-sanomaa "
"sanotaan \"yleiseksi\", jos sitä ei voida pitää tavanomaisena "
"eurooppalaisena tilisiirtona. Tämä tarkoittaa, että pankkipäiväkirja ei ole "
"euromääräinen, maksutapahtuma ei ole euromääräinen tai maksunsaajaa ei ole "
"tunnistettu IBAN-tilinumerolla."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"Kumppaniin \"%s\" liittyvä tili %s ei ole IBAN-tyyppinen.\n"
"SEPA-ominaisuuksien käyttäminen edellyttää voimassa olevaa IBAN-tiliä."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"Tili %s, päiväkirjassa \"%s\", ei ole IBAN-tyyppinen.\n"
"SEPA-ominaisuuksien käyttäminen edellyttää voimassa olevaa IBAN-tiliä."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The amount of the payment '%(payment)s' is too high. The maximum permitted "
"is %(limit)s."
msgstr ""
"Maksun '%(payment)s' määrä on liian suuri. Suurin sallittu arvo on "
"%(limit)s."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The bank defined on account %s (from partner %s) has no BIC. Please first "
"set one."
msgstr ""
"Tilille %s (kumppanilta %s) määritellyllä pankilla ei ole BIC-koodia. Aseta "
"ensin sellainen."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The BIC code '%s' associated to the bank '%s' of bank account '%s' of partner '%s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""
"Kumppanin \"%s\" pankkitilin \"%s\" pankkiin \"%s\" liittyvä BIC-koodi \"%s\" ei ole vaaditun sopimuksen mukainen.\n"
"Sen on sisällettävä 8 tai 11 merkkiä ja vastattava seuraavaa rakennetta:\n"
"- 4 kirjainta: laitoksen koodi tai pankin koodi\n"
"- 2 kirjainta: maakoodi\n"
"- 2 kirjainta tai numeroa: sijaintikoodi\n"
"- 3 kirjainta tai numeroa: sivukonttorin koodi, valinnainen\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"Päiväkirja '%s' edellyttää asianmukaista IBAN-tiliä SEPA-maksua varten. "
"Määritä se ensin."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The text used in SEPA files can only contain the following characters :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"
msgstr ""
"Tekstit SEPA-tiedostoissa voi sisältää vain seuraavia merkkejä:\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Too many transactions for a single file."
msgstr "Liian monta tapahtumaa yhteen tiedostoon."

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr ""
"Ilmoitetaan SEPA-maksuissa maksun aloittavan osapuolen nimenä. Rajoitettu 70"
" merkkiin."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Your Company Name"
msgstr "Yrityksen nimi"
