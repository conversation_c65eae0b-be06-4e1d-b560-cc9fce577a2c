# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_sepa
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:23+0000\n"
"PO-Revision-Date: 2017-10-24 09:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Galician (https://www.transifex.com/odoo/teams/41243/gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:42
#, python-format
msgid "A SEPA communication cannot exceed 140 characters"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_bank_account_id
msgid "Bank Account"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr ""

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_download
msgid "Cancel"
msgstr "Cancelar"

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_download
msgid ""
"Click on the file to save it somewhere on your computer. Then upload that "
"file on your bank's homebanking website to order the credit transfer."
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_sepa_credit_transfer
msgid "Create SEPA credit transfer files"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_create_date
msgid "Created on"
msgstr "Creado o"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_display_name
msgid "Display Name"
msgstr ""

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_download
msgid "Download SEPA Credit Transfer File"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company_sepa_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_filename
msgid "Filename"
msgstr ""

#. module: account_sepa
#: model:ir.actions.server,name:account_sepa.action_account_download_sepa_sct_file
msgid "Generate SEPA Credit Transfer XML"
msgstr ""

#. module: account_sepa
#: selection:res.company,sepa_pain_version:0
msgid "Generic"
msgstr ""

#. module: account_sepa
#: selection:res.company,sepa_pain_version:0
msgid "German Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_id
msgid "ID"
msgstr "ID"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company_sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings_sepa_orgid_id
msgid "Identification"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company_sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings_sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:70
#, python-format
msgid ""
"In order to export a SEPA Credit Transfer file, please only select payments "
"belonging to the same bank journal."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_is_generic
msgid "Is Generic"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company_sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings_sepa_orgid_issr
msgid "Issuer"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_journal_id
msgid "Journal"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer___last_update
msgid "Last Modified on"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings_sepa_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr ""

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_download
msgid "Ok"
msgstr ""

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:68
#, python-format
msgid ""
"Payments to export as SEPA Credit Transfer must have 'SEPA Credit Transfer' "
"selected as payment method and be posted"
msgstr ""

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_payment_partner_bank_account_id
#: model:ir.model.fields,field_description:account_sepa.field_account_register_payments_partner_bank_account_id
msgid "Recipient Bank Account"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr ""

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr ""

#. module: account_sepa
#: model:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_search
msgid "SEPA Credit Transfer Payments To Send"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal_dashboard.py:23
#, python-format
msgid "SEPA Credit Transfers to Send"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company_sepa_pain_version
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings_sepa_pain_version
msgid "SEPA Pain Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_file
msgid "SEPA XML File"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company_sepa_pain_version
#: model:ir.model.fields,help:account_sepa.field_res_config_settings_sepa_pain_version
msgid ""
"SEPA may be a generic format, some countries differ from the SEPA "
"recommandations made by the EPC (European Payment Councile) and thus the XML"
" created need some tweakenings."
msgstr ""

#. module: account_sepa
#: selection:res.company,sepa_pain_version:0
msgid "Swiss Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_sepa_credit_transfer_is_generic
msgid ""
"Technical feature used during the file creation. A SEPA message is said to "
"be 'generic' if it cannot be considered as a standard european credit "
"transfer. That is if the bank journal is not in €, a transaction is not in €"
" or a payee is not identified by an IBAN account number and a bank BIC."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:343
#, python-format
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:75
#, python-format
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:52
#, python-format
msgid ""
"The account '%s' (journal %s) requires a Bank Identification Code (BIC) to "
"pay via SEPA. Please configure it first."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:305
#, python-format
msgid ""
"The amount of the payment '%s' is too high. The maximum permitted is %s."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:123
#, python-format
msgid ""
"The creditor bank account %s used in payment %s is not identified by a BIC"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:125
#, python-format
msgid ""
"The creditor bank account %s used in payment %s is not identified by an IBAN"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:48
#, python-format
msgid ""
"The generated payment file is not a generic SEPA credit transfer. Be aware that some banks may reject it because it is not implemented on their side.\n"
"\n"
"In particular, the reason why this payment file is not generic is the following:\n"
"   "
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:50
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:61
#, python-format
msgid ""
"The partner account '%s' requires a Bank Identification Code (BIC) to pay "
"via SEPA. Please configure it first."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:17
#, python-format
msgid ""
"The text used in SEPA files can only contain the following characters :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:121
#, python-format
msgid "The transaction %s is instructed in another currency than EUR"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:337
#, python-format
msgid "There is no Bank Identifier Code recorded for bank account '%s'"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:180
#, python-format
msgid ""
"There is no Bank Identifier Code recorded for bank account '%s' of journal "
"'%s'"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:78
#, python-format
msgid "There is no bank account selected for payment '%s'"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:178
#, python-format
msgid "Too many transactions for a single file."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_sepa_credit_transfer_warning_message
msgid "Warning"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company_sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings_sepa_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company_sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings_sepa_initiating_party_name
msgid "Your Company Name"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/sepa_credit_transfer.py:117
#, python-format
msgid "Your bank account is not labelled in EUR"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "res.config.settings"
msgstr ""
