# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_sepa
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_batch_payment
msgid "Batch Payment"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "કંપનીઓ"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_abstract_payment
msgid ""
"Contains the logic shared between models which allows to register payments"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr ""

#. module: account_sepa
#: selection:res.company,sepa_pain_version:0
msgid "Generic"
msgstr ""

#. module: account_sepa
#: selection:res.company,sepa_pain_version:0
msgid "German Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:44
#: code:addons/account_sepa/models/account_batch_payment.py:51
#, python-format
msgid ""
"In result, the file might not be accepted by all bank as a valid SEPA Credit"
" Transfer file"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Issuer"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
msgid "Journal"
msgstr "રોજનામું"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr "ચૂકવણીઓ"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:42
#, python-format
msgid "Please note that the following warning has been raised:"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:46
#, python-format
msgid "Please note that the following warnings have been raised:"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_payment__partner_bank_account_id
#: model:ir.model.fields,field_description:account_sepa.field_account_register_payments__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_register_payments
msgid "Register Payments"
msgstr ""

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_search
msgid "SEPA Credit Transfer Payments To Send"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal_dashboard.py:23
#, python-format
msgid "SEPA Credit Transfers to Send"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_pain_version
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_pain_version
msgid "SEPA Pain Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_pain_version
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_pain_version
msgid ""
"SEPA may be a generic format, some countries differ from the SEPA "
"recommandations made by the EPC (European Payment Councile) and thus the XML"
" created need some tweakenings."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_generic
msgid "Sct Generic"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_warning
msgid "Sct Warning"
msgstr ""

#. module: account_sepa
#: selection:res.company,sepa_pain_version:0
msgid "Swiss Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_generic
msgid ""
"Technical feature used during the file creation. A SEPA message is said to "
"be 'generic' if it cannot be considered as a standard european credit "
"transfer. That is if the bank journal is not in €, a transaction is not in €"
" or a payee is not identified by an IBAN account number and a bank BIC."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:353
#, python-format
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:103
#, python-format
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:47
#, python-format
msgid ""
"The account '%s' (journal %s) requires a Bank Identification Code (BIC) to "
"pay via SEPA. Please configure it first."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:317
#, python-format
msgid ""
"The amount of the payment '%s' is too high. The maximum permitted is %s."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:90
#, python-format
msgid ""
"The creditor bank account %s used in payment %s is not identified by a BIC"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:92
#, python-format
msgid ""
"The creditor bank account %s used in payment %s is not identified by an IBAN"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:111
#, python-format
msgid ""
"The following payments have no recipient bank account set: %s. \n"
"\n"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:45
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:56
#, python-format
msgid ""
"The partner account '%s' requires a Bank Identification Code (BIC) to pay "
"via SEPA. Please configure it first."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:18
#, python-format
msgid ""
"The text used in SEPA files can only contain the following characters :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:88
#, python-format
msgid "The transaction %s is instructed in another currency than EUR"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:347
#, python-format
msgid "There is no Bank Identifier Code recorded for bank account '%s'"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:191
#, python-format
msgid ""
"There is no Bank Identifier Code recorded for bank account '%s' of journal "
"'%s'"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:189
#, python-format
msgid "Too many transactions for a single file."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Your Company Name"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:84
#, python-format
msgid "Your bank account is not labelled in EUR"
msgstr ""
