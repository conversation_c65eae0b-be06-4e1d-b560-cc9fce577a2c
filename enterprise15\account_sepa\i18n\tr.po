# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: abc Def <<EMAIL>>, 2021\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "A bank account is not defined."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment_register.py:0
#, python-format
msgid "A bank account must be set on the following documents: "
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Bank account %s 's bank does not have any BIC number associated. Please "
"define one."
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Toplu Rezervasyon"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_batch_payment
msgid "Batch Payment"
msgstr "Toplu Ödeme"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr "Kimliği tayin eden kurum (örn. KBE-BCO veya Finanzamt Muenchen IV)."

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "Generic"
msgstr "Jenerik"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_003_03
msgid "German"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__has_sepa_ct_payment_method
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification"
msgstr "Tanımlama"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr "Bir kurum tarafından atanan kimliği (Örn.Vergi numarası)."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Issuer"
msgstr "Düzenleyen"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
msgid "Journal"
msgstr "Yevmiye"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Maximum amount is %s for payments in Euros, %s for other currencies."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr ""
"Borç Sahibi Referans Partisinin adı. Kullanım Kuralları: 70 karakter "
"uzunluğunda sınırlıdır."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has no country code defined."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has not bank account defined."
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_method
msgid "Payment Methods"
msgstr "Ödeme Yöntemleri"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr "SEPA yoluyla göndermek için ödeme yapılması"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr "Ödemeler"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr "SEPA yoluyla göndermek için yapılan ödemeler"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Please first set a SEPA identification number in the accounting settings."
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_register
msgid "Register Payment"
msgstr "Ödeme Kaydet"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "SCT Batch Booking"
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_search
msgid "SCT Payments To Send"
msgstr ""

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr "SEPA Alacak Transferi"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal_dashboard.py:0
#, python-format
msgid "SEPA Credit Transfers to Send"
msgstr "SEPA Gönderilecek Alacak Transferleri"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__sepa_pain_version
#: model_terms:ir.ui.view,arch_db:account_sepa.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "SEPA Pain Sürümü"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,help:account_sepa.field_account_journal__sepa_pain_version
msgid ""
"SEPA may be a generic format, some countries differ from the SEPA "
"recommendations made by the EPC (European Payment Council) and thus the XML "
"created need some tweaking."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_generic
msgid "Sct Generic"
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments are above the maximum amount allowed."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are linked to partner addresses with characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are not made on an IBAN recipient account. This batch might "
"not be accepted by certain banks because of that."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments have a name or reference containing characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have no recipient bank account set."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments were instructed in another currency than Euro. This batch "
"might not be accepted by certain banks because of that."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_se
msgid "Swedish"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_ch_02
msgid "Swiss"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_generic
msgid ""
"Technical feature used during the file creation. A SEPA message is said to "
"be 'generic' if it cannot be considered as a standard european credit "
"transfer. That is if the bank journal is not in €, a transaction is not in €"
" or a payee is not identified by an IBAN account number."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"İş ortağı '%s' ile bağlantılı hesap %s, IBAN cinsinden değil.\n"
"SEPA özelliklerini kullanmak için geçerli bir IBAN hesabı gereklidir."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"Yevmiye '%s' hesabının %s, türü IBAN değil.\n"
"SEPA özelliklerini kullanmak için geçerli bir IBAN hesabı gereklidir"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The amount of the payment '%(payment)s' is too high. The maximum permitted "
"is %(limit)s."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The bank defined on account %s (from partner %s) has no BIC. Please first "
"set one."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"'%s' yevmiyesinin SEPA yoluyla ödeme yapmak için uygun bir IBAN hesabının "
"olmasını gerektiriyor. Lütfen önce yapılandırın."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The text used in SEPA files can only contain the following characters :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"
msgstr ""
"SEPA dosyalarında kullanılan metin yalnızca aşağıdaki karakterleri içerebilir:\n"
"\n"
"A b c d e f g h i j k l m o o p q s t v v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ -? : (). , '+ (Boşluk)"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Too many transactions for a single file."
msgstr "Tek bir dosya için çok fazla işlem."

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr ""
"SEPA ödemelerinde ödemeyi başlatan tarafın adı olarak görünecektir. 70 "
"karakterle sınırlandırılmıştır."

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Your Company Name"
msgstr "Şirketinizin Adı"
