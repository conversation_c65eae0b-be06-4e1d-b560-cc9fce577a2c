# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "A bank account is not defined."
msgstr "銀行賬戶未設定。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment_register.py:0
#, python-format
msgid "A bank account must be set on the following documents: "
msgstr "下列文件必須設定銀行賬戶："

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr "奧地利"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Bank account %s 's bank does not have any BIC number associated. Please "
"define one."
msgstr "銀行賬戶 %s 的銀行未有關聯至任何 BIC 號碼。 請設定一個。"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "大批預訂"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_batch_payment
msgid "Batch Payment"
msgstr "批量付款"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "公司"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "Config Settings"
msgstr "設定"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr "識別碼的編配/簽發機構（例：KBE-BCO 或 Finanzamt Muenchen IV）。"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "Generic"
msgstr "通用的"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_003_03
msgid "German"
msgstr "德國"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__has_sepa_ct_payment_method
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr "有 SEPA 轉賬付款方法"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification"
msgstr "公司 SEPA 識別碼"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr "被一家機構分配的標識例(如.增值稅 號碼)"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Issuer"
msgstr "發行人"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
msgid "Journal"
msgstr "日記帳"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Maximum amount is %s for payments in Euros, %s for other currencies."
msgstr "歐元付款最高金額為 %s；其他貨幣則為 %s。"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr "債權人參考方姓名。使用規則：長度限制在70字以內。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has no country code defined."
msgstr "合作夥伴 %s 未設定國家/地區代碼。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Partner %s has not bank account defined."
msgstr "合作夥伴 %s 未設定銀行賬戶。"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_method
msgid "Payment Methods"
msgstr "付款方法"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr "項待傳送 SEPA 付款"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr "收付款"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr "項待傳送 SEPA 付款"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"Please first set a SEPA identification number in the accounting settings."
msgstr "請首先在會計設定中，設置一個 SEPA 識別碼。"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_register
msgid "Register Payment"
msgstr "登記付款"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr "對相關銀行結單，要求銀行作大批記賬。"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "SCT Batch Booking"
msgstr "SEPA 轉賬付款大批記賬"

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_sepa_credit_transfer_search
msgid "SCT Payments To Send"
msgstr "待傳送 SEPA 轉賬付款"

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr "SEPA 轉賬付款"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal_dashboard.py:0
#, python-format
msgid "SEPA Credit Transfers to Send"
msgstr "待傳送 SEPA 轉賬付款"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__sepa_pain_version
#: model_terms:ir.ui.view,arch_db:account_sepa.view_account_journal_form
msgid "SEPA Pain Version"
msgstr "SEPA格式版本"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_bank_statement_import_journal_creation__sepa_pain_version
#: model:ir.model.fields,help:account_sepa.field_account_journal__sepa_pain_version
msgid ""
"SEPA may be a generic format, some countries differ from the SEPA "
"recommendations made by the EPC (European Payment Council) and thus the XML "
"created need some tweaking."
msgstr ""
"SEPA 可以使用一般格式，但某些國家/地區的做法與 EPC（歐洲支付委員會）建議的 SEPA 做法不同，所以產生的 XML 需要進行一些調整。"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_generic
msgid "Sct Generic"
msgstr "一般 SEPA 轉賬付款"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments are above the maximum amount allowed."
msgstr "部份付款超出允許的最高金額。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are linked to partner addresses with characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments are not made on an IBAN recipient account. This batch might "
"not be accepted by certain banks because of that."
msgstr "部份付款並非以 IBAN 收款人賬戶進行。因此，可能會有銀行不接受這批付款。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments have a name or reference containing characters not supported "
"by SEPA. These characters have been replaced by blanks."
msgstr ""

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid "Some payments have no recipient bank account set."
msgstr "部份付款未有設定收款人銀行賬戶。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some payments were instructed in another currency than Euro. This batch "
"might not be accepted by certain banks because of that."
msgstr "部份付款指示使用歐元以外的其他貨幣。因此，可能會有銀行不接受這批付款。"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_se
msgid "Swedish"
msgstr "瑞典"

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_ch_02
msgid "Swiss"
msgstr "瑞士"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_generic
msgid ""
"Technical feature used during the file creation. A SEPA message is said to "
"be 'generic' if it cannot be considered as a standard european credit "
"transfer. That is if the bank journal is not in €, a transaction is not in €"
" or a payee is not identified by an IBAN account number."
msgstr ""
"創建文件時使用的技術功能。若 SEPA 訊息不能被視為標準歐洲轉賬付款，會被稱為 “一般” 訊息，意思是銀行日記賬或交易並非使用歐元，或收款人並非以 "
"IBAN 賬戶號碼作識別。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"帳戶 %s,連結到夥伴 '%s', 不是IBAN類型.\n"
"一個有效的IBAN帳戶需要使用SEPA功能.\n"
"."

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"日記帳的 %s, 帳戶 '%s',不是IBAN類型.\n"
"一個有效的IBAN帳戶需要使用SEPA功能."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The amount of the payment '%(payment)s' is too high. The maximum permitted "
"is %(limit)s."
msgstr "付款指示「%(payment)s」的金額太大。允許的上限為 %(limit)s。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The bank defined on account %s (from partner %s) has no BIC. Please first "
"set one."
msgstr "銀行賬戶 %s（來自合作夥伴 %s）的銀行沒有 BIC. 請先設置一個."

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid ""
"The BIC code '%s' associated to the bank '%s' of bank account '%s' of partner '%s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""
"BIC 代碼「%s」（已連結銀行及銀行賬戶：%s，%s；合作夥伴為 %s）不符合格式要求。\n"
"只可有 8 個或 11 個字符，依序符合以下結構：\n"
"- 4 個字母：機構代碼或銀行代碼\n"
"- 2 個字母：國家/地區代碼\n"
"- 2 個字母或數字：位置代碼\n"
"- 3 個字母或數字：銀行分行代碼（可選填）\n"

#. module: account_sepa
#: code:addons/account_sepa/models/account_payment.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr "日記帳 '%s' 需要一個正確的IBAN 帳號來通過SEPA付費。請先設定它。"

#. module: account_sepa
#: code:addons/account_sepa/models/account_batch_payment.py:0
#, python-format
msgid ""
"The text used in SEPA files can only contain the following characters :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"
msgstr ""
"在SEPA文件裡只能包含如下字元 :\n"
"\n"
"a b c d e f g h i j k l m n o p q r s t u v w x y z\n"
"A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\n"
"0 1 2 3 4 5 6 7 8 9\n"
"/ - ? : ( ) . , ' + (space)"

#. module: account_sepa
#: code:addons/account_sepa/models/account_journal.py:0
#, python-format
msgid "Too many transactions for a single file."
msgstr "在單個文件裡太多的交易了。"

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr "出現在SEPA支付裡作為支付發起方的名稱。限70個字元。"

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Your Company Name"
msgstr "公司名稱"
