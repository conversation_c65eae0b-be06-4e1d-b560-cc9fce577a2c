<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_sepa_credit_transfer_search" model="ir.ui.view">
            <field name="name">account.sepa.credit.transfer.search</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='company_id']" position="after">
                    <filter string="SCT Payments To Send" domain="[('payment_method_line_id.code', '=', 'sepa_ct'), ('state', '=', 'posted'), ('is_move_sent', '=', False), ('is_matched', '=', False)]" name="sepa_to_send"/>
                    <separator/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
