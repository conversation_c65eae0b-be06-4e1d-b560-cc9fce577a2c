<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data noupdate="1">

        <!--
            Garbage collector-like cron, updating active mandate states to 'closed'
            once they have reached their end date.
        -->
        <record id="sdd_mandate_state_cron" model="ir.cron">
            <field name="name">Mandate state updater</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall">0</field>
            <field name="model_id" ref="account_sepa_direct_debit.model_sdd_mandate"/>
            <field name="state">code</field>
            <field name="code">model.cron_update_mandates_states()</field>
            <field name="priority">0</field>
        </record>
    </data>
</odoo>