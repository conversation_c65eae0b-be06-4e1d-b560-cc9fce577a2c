# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_sepa_direct_debit
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON>, 2021\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(if applicable)"
msgstr "(hvis relevant)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "(optional)"
msgstr "(valgfrit)"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
". As part of your rights, you are entitled to a refund from your bank under "
"the terms and conditions of your agreement with your bank. Your rights are "
"explained in a statement that you can obtain from your bank. A refund must "
"be claimed within 8 weeks starting from the date on which your account was "
"debited."
msgstr ""
". Som del af dine rettigheder, har du ret til en refundering fra din bank "
"under de betingelser og vilkår der gør sig gældende for din aftale med din "
"bank. Dine rettigheder er forklaret i en bekendtgørelse som du modtog fra "
"din bank. Der skal kræves refusion inden for 8 uger fra den dato, hvor din "
"konto blev debiteret."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Address:</strong>"
msgstr "<strong>Adresse:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>City: </strong>"
msgstr "<strong>By: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Country: </strong>"
msgstr "<strong>Land: </strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Creditor identifier:</strong>"
msgstr "<strong>Kreditor ID:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Date and place of signature:</strong> "
"......................................"
msgstr ""
"<strong>Dato og sted for underskrift:</strong> "
"......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Email:</strong>"
msgstr "<strong>E-mail:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>IBAN:</strong>"
msgstr "<strong>IBAN:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Mandate identifier:</strong>"
msgstr "<strong>Mandat ID:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"<strong>Name of the reference party:</strong> "
"......................................"
msgstr ""
"<strong>Navn på reference:</strong> ......................................"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Phone:</strong>"
msgstr "<strong>Telefon:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Signature:</strong>"
msgstr "<strong>Underskrift:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Start date:</strong>"
msgstr "<strong>Start dato:</strong>"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Transaction type:</strong> recurrent"
msgstr "<strong>Transaktion type:</strong> gentagende"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "<strong>Zip: </strong>"
msgstr "<strong>Postnummer: </strong>"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid "A debtor account is required to validate a SEPA Direct Debit mandate."
msgstr ""
"En debitorkonto er påkrævet for at validere et SEPA Direkte Debit-mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"A mandate represents the authorization you receive from a customer\n"
"                    to automatically collect money on her account."
msgstr ""
"Et mandat repræsenterer autoriseringen du modtog fra en kunde\n"
"                      til automatisk at indsamle penge på deres konto."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "Account of the customer to collect payments from."
msgstr "Konto på kunden der skal indhentes betalinger fra."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__active
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Active"
msgstr "Aktiv"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitets Type Ikon"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#, python-format
msgid "All the payments in the batch must have the same SDD scheme."
msgstr "Alle betalinger i batchen skal have samme SDD skema."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__b2b
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__b2b
msgid "B2B"
msgstr "B2B"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankkonti"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "Parti booking"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_batch_payment
msgid "Batch Payment"
msgstr "Parti betaling"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "By signing this mandate form, you authorise (A)"
msgstr "Ved at underskrive denne mandat formular, autorisere du (A)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__account_batch_payment__sdd_scheme__core
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__sdd_scheme__core
msgid "CORE"
msgstr "KERNE"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Cancel"
msgstr "Annullér"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Close"
msgstr "Luk"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__closed
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Closed"
msgstr "Lukket"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__company_id
msgid "Company for whose invoices the mandate can be used."
msgstr "Virksomhed for hvis faktura mandatet kan bruges."

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_usable
msgid "Could a SDD mandate be used?"
msgstr "Kan et SDD mandat bruges?"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid "Create a new direct debit customer mandate"
msgstr "Opret et nyt direkte debit kunde mandat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Create it."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Creditor"
msgstr "Kreditor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor Identifier"
msgstr "Kreditor ID"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_config_settings__sdd_creditor_identifier
msgid "Creditor identifier of your company within SEPA scheme."
msgstr "Kreditor ID på din virksomhed indenfor SEPA skema."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Customer mandate"
msgstr "Kunde mandat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__partner_id
msgid "Customer whose payments are to be managed by this mandate."
msgstr "Kunde hvis betalinger skal administreres af dette mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Date from which the mandate can be used (inclusive)."
msgstr "Dato fra hvornår mandatet kan bruges (inklusiv)."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid ""
"Date until which the mandate can be used. It will automatically be closed "
"after this date."
msgstr ""
"Dato indtil hvornår mandatet kan bruges. Den vil automatisk blive lukket "
"efter denne dato."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid "Date when the company expects to receive the payments of this batch."
msgstr ""
"Dato når virksomheden forventer at modtage betalinger for dette parti."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Debtor"
msgstr "Debitor"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Debtor Identifier"
msgstr "Debitor ID"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_partner_mandates
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_customer_mandates_menu
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Direct Debit Mandates"
msgstr "Direkte debit mandater"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payment to Collect"
msgstr "Direkte debit betaling at indhente"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payments to Collect"
msgstr "Direkte debit betalinger at indhente"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "Direct debit payments to collect"
msgstr "Direkte debit betalinger at indhente"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__draft
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Draft"
msgstr "Udkast"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__end_date
msgid "End Date"
msgstr "Slut dato"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "End date"
msgstr "Slut dato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Every mandate belonging to this partner."
msgstr "Hver mandat tilhørende denne partner."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__debtor_id_code
msgid "Free reference identifying the debtor in your company."
msgstr "Gratis reference der identificerer debitoren i din virksomhed."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Good news! A valid Sepa Mandate is available."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__partner_bank_id
msgid "IBAN"
msgstr "IBAN"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__id
msgid "ID"
msgstr "ID"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Identification code"
msgstr "Identifikationskode"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__name
msgid "Identifier"
msgstr "Identifikator"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
#, python-format
msgid "Invalid creditor identifier. Make sure you made no typo."
msgstr "Ugyldig kreditor ID. Tjek du ikke lavede en slåfejl."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
#, python-format
msgid "Invalid creditor identifier. Wrong format."
msgstr "Ugyldig keditor ID. Forkert format."

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,description:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Invoice paid via direct debit."
msgstr "Faktura betalt via direkte debit."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices Paid"
msgstr "Fakturaer betalt"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
msgid "Invoices matching a valid SEPA Direct Debit Mandate"
msgstr "Faktura der stemmer overens med et gyldigt SEPA Direkte Debet Mandat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoice_ids
msgid "Invoices paid using this mandate."
msgstr "Faktura betalt via dette mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices paid with this mandate."
msgstr "Faktura betalt med dette mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_journal
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payment_journal_id
msgid "Journal"
msgstr "Journal"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_move
msgid "Journal Entry"
msgstr "Postering"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payment_journal_id
msgid ""
"Journal to use to receive SEPA Direct Debit payments from this mandate."
msgstr ""
"Journal der skal anvendes til at modtage SEPA direkte debit betalinger fra "
"dette mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Vedhæftning"

#. module: account_sepa_direct_debit
#: model:ir.actions.report,name:account_sepa_direct_debit.sdd_mandate_form_report_main
msgid "Mandate form"
msgstr "Mandat form"

#. module: account_sepa_direct_debit
#: model:ir.model.constraint,message:account_sepa_direct_debit.constraint_sdd_mandate_name_unique
msgid "Mandate identifier must be unique ! Please choose another one."
msgstr "Mandt ID skal være unik ! Vælg venligst et andet."

#. module: account_sepa_direct_debit
#: model:ir.actions.server,name:account_sepa_direct_debit.sdd_mandate_state_cron_ir_actions_server
#: model:ir.cron,cron_name:account_sepa_direct_debit.sdd_mandate_state_cron
#: model:ir.cron,name:account_sepa_direct_debit.sdd_mandate_state_cron
msgid "Mandate state updater"
msgstr "Mandat status opdaterings agent"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mine Aktiviteter Deadline"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste Aktivitet Kalender Arrangement"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "No direct debit payment to collect"
msgstr "Ingen direkte debit betaling at indhente"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payments_to_collect_nber
msgid ""
"Number of Direct Debit payments to be collected for this mandate, that is, "
"the number of payments that have been generated and posted thanks to this "
"mandate and still needs their XML file to be generated and sent to the bank "
"to debit the customer's account."
msgstr ""
"Antal direkte debit betalinger der skal indhentes for dette mandat. Det vil "
"sige, antallet af betalinger der er blevet genereret og posteret takke være "
"dette mandat, og som stadig skal have deres XML fil genereret og afsendt til"
" banken, for at debitere kundens konto."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Number of invoices paid with thid mandate."
msgstr "Antal faktura betalt med dette mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antal meddelser der kræver handling"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__message_unread_counter
msgid "Number of unread messages"
msgstr "Antal ulæste beskeder"

#. module: account_sepa_direct_debit
#: model_terms:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"Once an invoice is made\n"
"                    in Odoo for a customer having an mandate active on the invoice date,\n"
"                    its validation will trigger its automatic payment, and you will\n"
"                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation\n"
"                    and send it to your bank to effectively get paid."
msgstr ""
"Når en faktura oprettes\n"
"                     i Odoo for en kunde som har et aktivt mandat på fakturerings datoen\n"
"                      vil godkendelsen udløse dens automatiske betaling, og du vil\n"
"                      efterfølgende kun skulle generere en SEPA Direkte Debit (SDD) fil med denne afvikling\n"
"                     og sende den til din bank for blive betalt på effektiv vis."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
msgid ""
"Once this invoice has been paid with Direct Debit, contains the mandate that"
" allowed the payment."
msgstr ""
"Når denne faktura er blevet betalt med Direkte Debit, indeholder dette "
"mandatet der tillod betalingen."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid "One-off Mandate"
msgstr "Engangs mandat"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid ""
"Only IBAN account numbers can receive SEPA Direct Debit payments. Please "
"select a journal associated to one."
msgstr ""
"Kun IBAN kontonumre kan modtage SEPA Direkte Debit betalinger. Vælg venligst"
" en journal associeret med en."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid ""
"Only mandates in draft state can be deleted from database when cancelled."
msgstr ""
"Kun mandater i kladde tilstand kan slettes fra databasen ved annullering."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
msgid "Oops! No valid SEPA mandate for this customer."
msgstr ""

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Open this partner's mandates"
msgstr "Åben denne partners mandater"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_with_mandates_tree
msgid "Originating SEPA mandate"
msgstr "SEPA Mandatet hvorfor det oprindede"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid "Paid Invoices"
msgstr "Betalte fakturaer"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__paid_invoices_nber
msgid "Paid Invoices Number"
msgstr "Betalt faktura nummer"

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,name:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Paid via direct debit"
msgstr "Betalt via direkte debit"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments"
msgstr "Betalinger"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Payments generated for this mandate that have not yet been collected."
msgstr "Betalinger genereret for dette mandat er endnu ikke blevet indhentet."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__payment_ids
msgid "Payments generated thanks to this mandate."
msgstr "Betalinger genereret takket være dette mandat."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#, python-format
msgid "Payments to Collect"
msgstr "Betalinger at indhente"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"Please do not pay it manually, the payment will be asked to your bank to be processed\n"
"                        automatically."
msgstr ""
"Betal venligst ikke manuelt, din bank anmodes om automatisk behandling af "
"betalingen."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Print"
msgstr "Udskriv"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr "Anmod parti booking fra banken på det relaterede kontoudtog."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_required_collection_date
msgid "Required collection date"
msgstr "Krævet indhentnings dato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Revoke"
msgstr "Tilbagekald"

#. module: account_sepa_direct_debit
#: model:ir.model.fields.selection,name:account_sepa_direct_debit.selection__sdd_mandate__state__revoked
msgid "Revoked"
msgstr "Tilbagekaldt"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Revoked SDD Mandate"
msgstr "Tilbagekaldt SDD mandat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD B2B"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_batch_booking
msgid "SDD Batch Booking"
msgstr "SDD Parti Booking"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_payment_search
msgid "SDD CORE"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.view_account_invoice_filter
msgid "SDD Mandate"
msgstr "SDD Mandat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "SDD Payments to Collect"
msgstr "SDD betalinger at indhente"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid "SDD Scheme"
msgstr "SDD Skema"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_count
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_count
msgid "SDD count"
msgstr "SDD antal"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid "SDD creditor identifier"
msgstr "SDD kreditor ID"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#, python-format
msgid "SDD scheme is set on the customer mandate."
msgstr "SDD skema er angivet på kundemandatet."

#. module: account_sepa_direct_debit
#: model:account.payment.method,name:account_sepa_direct_debit.payment_method_sdd
msgid "SEPA Direct Debit"
msgstr "SEPA direkte debit"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "SEPA Direct Debit Mandate"
msgstr "SEPA Direkte Debit mandat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_company__sdd_creditor_identifier
msgid ""
"SEPA Direct Debit creditor identifier of the company, given by the bank."
msgstr "SEPA Direkte Debit kreditor ID for virksomheden, tildelt af banken."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid ""
"SEPA Direct Debit scheme only accepts IBAN account numbers. Please select an"
" IBAN-compliant debtor account for this mandate."
msgstr ""
"SEPA Direkte Debit skema accepterer kun IBAN kontonumre. Vælg venligt en "
"debitor konto der er i overensstemmelse med IBAN for dette mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_has_usable_mandate
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_has_usable_mandate
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_has_usable_mandate
msgid "Sdd Has Usable Mandate"
msgstr "SDD Har Brugbart Mandat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_move__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment__sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner__sdd_mandate_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users__sdd_mandate_ids
msgid "Sdd Mandate"
msgstr "Sdd mandat"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Signature"
msgstr "Signatur"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#, python-format
msgid ""
"Some draft payments could not be posted because of the lack of any active "
"mandate."
msgstr ""
"Visse kladde betalinger kunne ikke posteres grundet manglen af et aktivt "
"mandat."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__start_date
msgid "Start Date"
msgstr "Start dato"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__state
msgid "State"
msgstr "Stat"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__suitable_journal_ids
msgid "Suitable Journal"
msgstr "Passende Journal"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_usable
msgid ""
"Technical field used to inform the end user there is a SDD mandate that "
"could be used to register that payment"
msgstr ""
"Teknisk felt brugt til at informere slut-brugeren om, at der et SDD mandat "
"der kan bruges til at registrere betalingen"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_bank_statement_line__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_batch_payment__sdd_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_move__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment__sdd_mandate_scheme
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__sdd_scheme
msgid ""
"The B2B scheme is an optional scheme,\n"
"offered exclusively to business payers.\n"
"Some banks/businesses might not accept B2B SDD."
msgstr ""
"B2B skemaet er et valgfrit skema,\n"
"som eksklusivt tilbydes forretnings betalere.\n"
"Det kan være at visse banker/forretninger ikke accepterer B2B SDD."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
#, python-format
msgid ""
"The SEPA Direct Debit mandate associated to the payment has been revoked and"
" cannot be used anymore."
msgstr ""
"SEPA Direkte Debit mandatet associeret med betalingen er blevet tilbagekaldt"
" og kan ikke længere anvendes."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_company.py:0
#, python-format
msgid "The creditor identifier exceeds the maximum length of 35 characters."
msgstr "Kreditor ID'et overstiger maksimal længden på 35 anslag."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid ""
"The debtor identifier you specified exceeds the limitation of 35 characters "
"imposed by SEPA regulation"
msgstr ""
"Debitor ID'en du specificerede overstiger begrænsningen på 35 anslag fastsat"
" af SEPA reguleringen"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid ""
"The end date of the mandate must be posterior or equal to its start date."
msgstr ""
"Slut datoen for mandatet skal være efter eller lig med dens start dato."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"The mandate will only be used to pay invoices into the\n"
"                specified time range. If no end date is specified,\n"
"                you will have to contact us to stop its use."
msgstr ""
"Mandaten vil kun blive brugt til at betale faktura indenfor den\n"
"                   specificerede tidsramme. Hvis ingen slut dato er angivet,\n"
"                   bliver du nødt til at kontakte os, for at standse brugen."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
#, python-format
msgid ""
"The payment must be linked to a SEPA Direct Debit mandate in order to "
"generate a Direct Debit XML."
msgstr ""
"Betalingen skal være forbundet til et SEPA Direkte Debit mandat, for at "
"kunne generere en Direkte Debit XML fil."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__state
msgid ""
"The state this mandate is in. \n"
"- 'draft' means that this mandate still needs to be confirmed before being usable. \n"
"- 'active' means that this mandate can be used to pay invoices. \n"
"- 'closed' designates a mandate that has been marked as not to use anymore without invalidating the previous transactions done with it.- 'revoked' means the mandate has been signaled as fraudulent by the customer. It cannot be used anymore, and should not ever have been. You will probably need to refund the related invoices, if any.\n"
msgstr ""
"Tilstanden dette mandat er i.\n"
"- 'kladde' betyder, at dette mandat stadig skal bekræftes, før den kan bruges.\n"
"- 'aktiv' betyder, at dette mandat kan bruges til at betale faktura.\n"
"- 'lukket' betyder, at et mandat er blevet udpeget til ikke at blive brugt mere, uden at ugyldiggøre tidligere transaktioner foretaget med den.\n"
"- 'tilbagekaldt' betyder, at mandatet er blevet anmeldt som falsk af kunden. Det kan ikke bruges mere, og skulle aldrig have været det. Du bliver formentlig nødt til at refundere relaterede faktura, om nogen.\n"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__name
msgid "The unique identifier of this mandate."
msgstr "Den unikke identifikation for dette mandat."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"This invoice will be paid using direct debit and is only\n"
"                        sent for informative purposes."
msgstr ""
"Denne faktura vil blive betalt via direkte debit, og afsendet kun\n"
"                        i informativt øjemed."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#, python-format
msgid ""
"To solve that, you should create a mandate for each of the involved "
"customers, valid at the moment of the payment date."
msgstr ""
"For at løse det, burde du oprette et mandat for hver af de involverede "
"kunder, gyldigt fra betalingsdatoen."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__one_off
msgid ""
"True if and only if this mandate can be used for only one transaction. It "
"will automatically go from 'active' to 'closed' after its first use in "
"payment if this option is set.\n"
msgstr ""
"Sandt hvis, og kun hvis, mandatet kan bruges for kun én transaktion. Den vil"
" automatisk overgå fra 'aktiv' til 'lukket', efter den først bruges til en "
"betaling, hvis denne mulighed er valgt.\n"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
#, python-format
msgid ""
"Trying to generate a Direct Debit XML file containing payments from another "
"company than that file's creditor."
msgstr ""
"Prøver at generere en Direkte Debit XML fil, som indeholder betalinger fra "
"en anden virksomhed, end filens kreditor."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
#, python-format
msgid ""
"Trying to generate a Direct Debit XML for payments coming from another "
"payment method than SEPA Direct Debit."
msgstr ""
"Prøver at generere en Direkte Debit XML fil til betalinger der kommer fra en"
" anden betalingsmetode end SEPA Direkte Debit."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:0
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:0
#, python-format
msgid ""
"Trying to register a payment on a mandate belonging to a different partner."
msgstr ""
"Prøver at registrere en betaling, på et mandat der tilhører en anden "
"partner."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_invoice.py:0
#, python-format
msgid ""
"Unable to post payment %(payment)r due to no usable mandate being available "
"at date %(date)s for partner %(partner)r. Please create one before encoding "
"a SEPA Direct Debit payment."
msgstr ""
"Kan ikke postere betalingen %(payment)r, eftersom der ikke er noget brugbart"
" mandat tilgængelig på datoen %(date)s for partneren %(partner)r. Opret "
"venligst en, før du indkoder en SEPA Direkte Debet betaling."

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_unread
msgid "Unread Messages"
msgstr "Ulæste beskeder"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Ulæste beskedtæller"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Validate"
msgstr "Validér"

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid "Validity"
msgstr "Gyldighed"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate__website_message_ids
msgid "Website communication history"
msgstr "Website kommunikations historik"

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_partner_bank.py:0
#, python-format
msgid ""
"You cannot delete a bank account linked to an active SEPA Direct Debit "
"mandate."
msgstr ""
"Du kan ikke slette en bankkonto forbundet til et aktivt SEPA Direkte Debit "
"mandat."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot generate a SEPA Direct Debit file with a required collection date"
" in the past."
msgstr ""
"Du kan ikke generere en SEPA Direkte Debit fil med en påkrævet indhentnings "
"dato i fortiden."

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_batch_payment.py:0
#, python-format
msgid ""
"Your company must have a creditor identifier in order to issue SEPA Direct "
"Debit payments requests. It can be defined in accounting module's settings."
msgstr ""
"Din virksomhed skal have et kreditor ID, for at kunne udstede SEPA Direkte "
"Debit betalingsanmodninger. Det kan defineres i regnskabsmodulets "
"indstillinger."

#. module: account_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form
msgid ""
"to send instructions to your bank to debit your account and (B) your bank to"
" debit your account in accordance with the instructions from"
msgstr ""
"til at sende instruktioner til din bank om, at debitere din konto, og (B), "
"til din bank om at debitere din konto i overensstemmelse med instruktionerne"
