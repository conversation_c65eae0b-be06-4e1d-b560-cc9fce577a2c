# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_sepa_direct_debit
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl_BE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "(if applicable)"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "(optional)"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Address:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>City:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Country:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Creditor identifier:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid ""
"<strong>Date and place of signature:</strong> "
"......................................"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>IBAN account number:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Mail address:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Mandate identifier:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid ""
"<strong>Name of the reference party:</strong> "
"......................................"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Name:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Phone number:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Signature:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Start date:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Transaction type:</strong> recurrent"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "<strong>Zip:</strong>"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:151
#, python-format
msgid "A debtor account is required to validate a SEPA Direct Debit mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"A mandate represents the authorization you receive from a customer\n"
"                    to automatically collect money on her account."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_partner_bank_id
msgid "Account of the customer to collect payments from."
msgstr ""

#. module: account_sepa_direct_debit
#: selection:sdd.mandate,state:0
msgid "Active"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Active Mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_download_xml_wizard_archive_id
msgid "Archive data containing the generated XML."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_archive_id
msgid "Archive for this operation"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_xml_archive_xml_file
msgid "Archived XML file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_generate_xml_form
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Cancel"
msgstr "Annuleren"

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid "Click to create a direct debit customer mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Close"
msgstr ""

#. module: account_sepa_direct_debit
#: selection:sdd.mandate,state:0
msgid "Closed"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Closed Mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_company_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_company_id
msgid "Company"
msgstr "Bedrijf"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_generate_xml_wizard_company_id
msgid "Company collecting the payments."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_company_id
msgid "Company for whose invoices the mandate can be used."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment_sdd_mandate_usable
msgid "Could a SDD mandate be used?"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_create_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_create_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_config_settings_sdd_creditor_identifier
msgid "Creditor identifier"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_config_settings_sdd_creditor_identifier
msgid "Creditor identifier of your company within SEPA scheme."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "Creditor information"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Customer mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_partner_id
msgid "Customer whose payments are to be managed by this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_start_date
msgid "Date from which the mandate can be used (inclusive)."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_creation_date
msgid "Date of creation"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_xml_archive_creation_date
msgid "Date of creation of this XML file."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_end_date
msgid "Date until which the mandate can be used (exclusive)."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_generate_xml_wizard_required_collection_date
msgid "Date when the company expects to be paid."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_partner_id
msgid "Debtor"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_partner_bank_id
msgid "Debtor Account"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_debtor_id_code
msgid "Debtor Identifier"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "Debtor information"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_partner_mandates
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_customer_mandates_menu
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Direct Debit Mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payment to Collect"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Direct Debit Payments To Collect"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_payments_to_collect_nber
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_journal_dashboard_kanban_view
msgid "Direct Debit Payments to Collect"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_account_payment_form
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_invoice_form_inherit
msgid "Direct Debit collecting method available"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "Direct debit payments to collect"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_display_name
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
msgid "Download SEPA Direct Debit XML"
msgstr ""

#. module: account_sepa_direct_debit
#: selection:sdd.mandate,state:0
msgid "Draft"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_mandate_search_view
msgid "Draft Mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_end_date
msgid "End Date"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "End date"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_partner_sdd_mandate_ids
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_users_sdd_mandate_ids
msgid "Every mandate belonging to this partner."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_original_doc_filename
msgid "File name of original_doc."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_debtor_id_code
msgid "Free reference identifying the debtor in your company."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.action_sdd_account_payment_generate_xml
msgid "Generate Direct Debit XML"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_generate_xml_form
msgid "Generate XML"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_xml_archive_form
msgid "Generated SDD file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_download_xml_wizard_xml_file
msgid "Generated XML file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
msgid "Generated file (click to download):"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_xml_archive_tree
msgid "History of generated SDD files"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_id
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_id
msgid "ID"
msgstr "ID"

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "Identification code"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_name
msgid "Identifier"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_company.py:38
#, python-format
msgid "Invalid creditor identifier. Make sure you made no typo."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_company.py:40
#, python-format
msgid "Invalid creditor identifier. Wrong format."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_invoice
msgid "Invoice"
msgstr "Factuur"

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,description:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Invoice paid via direct debit."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_paid_invoice_ids
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices Paid"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_paid_invoice_ids
msgid "Invoices paid using this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Invoices paid with this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_payment_journal_id
msgid "Journal for Direct Debit Payments"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_payment_journal_id
msgid ""
"Journal to use to receive SEPA Direct Debit payments from this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard___last_update
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard___last_update
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate___last_update
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_write_uid
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_write_date
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_payment_search
msgid "Linked to a revoked direct debit mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_view_account_invoice_search
msgid "Linked to revoked mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.report,name:account_sepa_direct_debit.sdd_mandate_form_report_main
msgid "Mandate form"
msgstr ""

#. module: account_sepa_direct_debit
#: sql_constraint:sdd.mandate:0
msgid "Mandate identifier must be unique ! Please choose another one."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.server,name:account_sepa_direct_debit.sdd_mandate_state_cron_ir_actions_server
#: model:ir.cron,cron_name:account_sepa_direct_debit.sdd_mandate_state_cron
#: model:ir.cron,name:account_sepa_direct_debit.sdd_mandate_state_cron
msgid "Mandate state updater"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "Mandate validity"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_download_xml_wizard_xml_filename
msgid "Name of the generated XML file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_xml_archive_xml_filename
msgid "Name of this XML file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,help:account_sepa_direct_debit.action_sdd_payments_to_collect
msgid "No direct debit payment to collect."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_payments_to_collect_nber
msgid ""
"Number of Direct Debit payments to be collected for this mandate, that is, "
"the number of payments that have been generated and posted thanks to this "
"mandate and still needs their XML file to be generated and sent to the bank "
"to debit the customer's account."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_paid_invoices_nber
msgid "Number of invoices paid with thid mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_mandate_tree_act
msgid ""
"Once an invoice is made\n"
"                    in Odoo for a customer having an mandate active on the invoice date,\n"
"                    its validation will trigger its automatic payment, and you will\n"
"                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation\n"
"                    and send it to your bank to effectively get paid."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_invoice_sdd_paying_mandate_id
msgid ""
"Once this invoice has been paid with Direct Debit, contains the mandate that"
" allowed the payment."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_xml_archive_tree_act
msgid ""
"Once you have, you will be able to keep track here of all the files\n"
"                    you created this way, and what their precise content was."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_one_off
msgid "One-off Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:137
#, python-format
msgid ""
"Only IBAN account numbers can receive SEPA Direct Debit payments. Please "
"select a journal associated to one."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:88
#, python-format
msgid ""
"Only mandates in draft state can be deleted from database when cancelled."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sdd_res_partner_view
msgid "Open this partner's mandates"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_original_doc
msgid "Original Document"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_original_doc_filename
msgid "Original Document File Name"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_original_doc
msgid ""
"Original document into which the customer authorises the use of Direct Debit"
" for his invoices."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_payment_sdd_mandate_id
msgid "Originating SEPA mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:110
#, python-format
msgid "Paid Invoices"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_paid_invoices_nber
msgid "Paid Invoices Number"
msgstr ""

#. module: account_sepa_direct_debit
#: model:mail.message.subtype,name:account_sepa_direct_debit.sdd_mt_invoice_paid_with_mandate
msgid "Paid via direct debit"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_generate_xml_form
msgid "Pay with mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_payment
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_payment_ids
msgid "Payments"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_payments_to_send_ids
msgid "Payments To Send"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_download_xml_wizard_payment_ids
msgid "Payments contained in the generated XML."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_xml_archive_payment_ids
msgid "Payments contained in this XML file."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Payments generated for this mandate that have not yet been collected."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_payment_ids
msgid "Payments generated thanks to this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_payments_in_exception
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
msgid "Payments in exception"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_payment_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_payment_ids
msgid "Payments in file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
msgid "Payments in this file"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:120
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
#, python-format
msgid "Payments to Collect"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_download_xml_wizard_payments_in_exception
msgid ""
"Payments whose XML could not be generated because they had not been "
"generated from a SEPA Direct Debit mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "Please complete your coordinates."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"Please do not pay it manually, the payment will be asked to your bank to be processed\n"
"                        automatically."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Print"
msgstr "Afdrukken"

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_account_register_payments
msgid "Register payments on multiple invoices"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_generate_xml_wizard_required_collection_date
msgid "Required collection date"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Revoke"
msgstr ""

#. module: account_sepa_direct_debit
#: selection:sdd.mandate,state:0
msgid "Revoked"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,name:account_sepa_direct_debit.account_sepa_direct_debit_xml_archive_tree_act
#: model:ir.ui.menu,name:account_sepa_direct_debit.account_sepa_direct_debit_sdd_payment_files_menu
msgid "SDD Payment Files"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_company_sdd_creditor_identifier
msgid "SDD creditor identifier"
msgstr ""

#. module: account_sepa_direct_debit
#: model:account.payment.method,name:account_sepa_direct_debit.payment_method_sdd
msgid "SEPA Direct Debit"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "SEPA Direct Debit Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_res_company_sdd_creditor_identifier
msgid ""
"SEPA Direct Debit creditor identifier of the company, given by the bank."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:153
#, python-format
msgid ""
"SEPA Direct Debit scheme only accepts IBAN account numbers. Please select an"
" IBAN-compliant debtor account for this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_xml_file
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_xml_file
msgid "SEPA XML file"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_download_xml_wizard_xml_filename
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_xml_archive_xml_filename
msgid "SEPA XML file name"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_partner_sdd_mandate_ids
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_res_users_sdd_mandate_ids
msgid "Sdd Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_account_invoice_sdd_paying_mandate_id
msgid "Sdd Paying Mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_generate_xml_wizard_payments_to_send_ids
msgid "Set of payments to (try to) put into a SDD XML file."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid "Signature"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_start_date
msgid "Start Date"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,field_description:account_sepa_direct_debit.field_sdd_mandate_state
msgid "State"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment_sdd_mandate_usable
msgid ""
"Technical field used to inform the end user there is a SDD mandate that "
"could be used to register that payment"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:139
#, python-format
msgid ""
"The bank account you select for payment must be related to a bank. Please "
"fix it first."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:141
#, python-format
msgid ""
"The bank your payment account is related to must have a BIC number. Please "
"define one."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/res_company.py:26
#, python-format
msgid "The creditor identifier exceeds the maximum length of 35 characters."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:147
#, python-format
msgid ""
"The debtor identifier you specified exceeds the limitation of 35 characters "
"imposed by SEPA regulation"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:131
#, python-format
msgid ""
"The end date of the mandate must be posterior or equal to its start date."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
msgid ""
"The following payments could not be translated to\n"
"                            SEPA Direct Debit XML. Either they do not belong to the company you\n"
"                            are currently connected on, they are not related to\n"
"                            any mandate, they have already been collected in a\n"
"                            previously generated file or the bank encoded of their\n"
"                            customer account does not have any BIC number defined."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_account_payment_sdd_mandate_id
msgid "The mandate used to generate this payment, if any."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid ""
"The mandate will only be used to pay invoices into the\n"
"                    specified time range. If no end date is specified,\n"
"                    you will have to contact us to stop its use."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_state
msgid ""
"The state this mandate is in. \n"
"- 'draft' means that this mandate still needs to be confirmed before being usable. \n"
"- 'active' means that this mandate can be used to pay invoices. \n"
"- 'closed' designates a mandate that has been marked as not to use anymore without invalidating the previous transactions done with it.- 'revoked' means the mandate has been signaled as fraudulent by the customer. It cannot be used anymore, and should not ever have been. You will probably need to refund the related invoices, if any.\n"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_name
msgid "The unique identifier of this mandate."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_mandate_form_document
msgid ""
"This form grants our company the authorization to\n"
"                    use direct debit to pay the invoices we send to you."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:169
#: code:addons/account_sepa_direct_debit/models/account_payment.py:182
#, python-format
msgid ""
"This invoice cannot be paid via SEPA Direct Debit, as there is no valid "
"mandate available for its customer at its creation date."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.sdd_report_invoice_document
msgid ""
"This invoice will be paid using direct debit and is only\n"
"                        sent for informative purposes."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model.fields,help:account_sepa_direct_debit.field_sdd_mandate_one_off
msgid ""
"True if and only if this mandate can be used for only one transaction. It "
"will automatically go from 'active' to 'closed' after its first use in "
"payment if this option is set.\n"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:111
#, python-format
msgid ""
"Trying to generate a Direct Debit XML file containing payments from another "
"company than that file's creditor."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:114
#, python-format
msgid ""
"Trying to generate a Direct Debit XML for payments coming from another "
"payment method than SEPA Direct Debit."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_payment.py:151
#, python-format
msgid ""
"Trying to register a payment on a mandate belonging to a different partner."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_download_xml_form
msgid "Unable to generate any data for the selected payments."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.ui.view,arch_db:account_sepa_direct_debit.account_sepa_direct_debit_mandate_form
msgid "Validate"
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/account_invoice.py:32
#, python-format
msgid ""
"You cannot pay an invoice with a mandate that does not cover the moment when"
" it was issued."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.actions.act_window,help:account_sepa_direct_debit.account_sepa_direct_debit_xml_archive_tree_act
msgid "You haven't generated any SDD XML payment collection file yet."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/models/sdd_mandate.py:80
#, python-format
msgid ""
"You must register this mandate's original document before validating it."
msgstr ""

#. module: account_sepa_direct_debit
#: code:addons/account_sepa_direct_debit/wizard/sdd_xml_wizards.py:29
#, python-format
msgid ""
"Your company must have a creditor identifier in order to issue SEPA Direct "
"Debit payments requests. It can be defined in accounting module's settings."
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_download_xml_wizard
msgid "sdd.download.xml.wizard"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_generate_xml_wizard
msgid "sdd.generate.xml.wizard"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_mandate
msgid "sdd.mandate"
msgstr ""

#. module: account_sepa_direct_debit
#: model:ir.model,name:account_sepa_direct_debit.model_sdd_xml_archive
msgid "sdd.xml.archive"
msgstr ""
