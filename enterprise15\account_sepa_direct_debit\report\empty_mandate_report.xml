<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="sdd_mandate_form_report_main" model="ir.actions.report">
            <field name="name">Mandate form</field>
            <field name="model">sdd.mandate</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">account_sepa_direct_debit.sdd_mandate_form_report</field>
            <field name="report_file">account_sepa_direct_debit.sdd_mandate_form_report</field>
            <field name="attachment_use">True</field>
            <field name="binding_model_id" ref="model_sdd_mandate"/>
            <field name="binding_type">report</field>
        </record>

        <template id="account_sepa_direct_debit.sdd_mandate_form_report">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="account_sepa_direct_debit.sdd_mandate_form_document" t-lang="o.partner_id.lang"/>
                </t>
            </t>
        </template>

        <template id="account_sepa_direct_debit.sdd_mandate_form_document">
            <t t-call="web.external_layout">
                <t t-call="account_sepa_direct_debit.sdd_mandate_form"/>
            </t>
        </template>

        <template id="account_sepa_direct_debit.sdd_mandate_form">
            <t t-if="o.sdd_scheme == 'CORE'">
                <h1>SEPA Direct Debit Mandate</h1>
            </t>
            <t t-if="o.sdd_scheme == 'B2B'">
                <h1>SEPA Business-to-Business Direct Debit Mandate</h1>
            </t>

            <p>
                By signing this mandate form, you authorise (A) <t t-esc="o.company_id.name"/> to send instructions to your bank to debit your account and (B) your bank to debit your account in accordance with the instructions from <t t-esc="o.company_id.name"/>.
                <t t-if="o.sdd_scheme == 'CORE'">
                    As part of your rights, you are entitled to a refund from your bank under the terms and conditions of your agreement with your bank. Your rights are explained in a statement that you can obtain from your bank. A refund must be claimed within 8 weeks starting from the date on which your account was debited.
                </t>
                <t t-if="o.sdd_scheme == 'B2B'">
                    This mandate is only intended for business-to-business transactions. You are not entitled to a refund from your bank after your account has been debited, but you are entitled to request your bank not to debit your account up until the day on which the payment is due. 
                </t>
            </p>

            <p>
                <strong>Mandate identifier:</strong> <t t-esc="o.name"/>
            </p>
            <br/>

            <h2>Creditor</h2>
            <div class="row mb-2">
                <div class="col-6">
                    <p><t t-esc="o.company_id.name"/></p>
                    <div t-field="o.company_id.partner_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": True}'/>
                </div>
                <div class="col-6">
                    <p><strong>Creditor identifier:</strong> <t t-esc="o.company_id.sdd_creditor_identifier"/></p>
                </div>
            </div>
            <br/>

            <h2>Debtor</h2>
            <div>
                <div class="row">
                    <div class="col-6">
                        <p><t t-esc="o.partner_id.name"/></p>

                        <t t-if="not o.partner_id.street and not o.partner_id.street2 or not o.partner_id.city or not o.partner_id.zip or not o.partner_id.country_id">
                            <p><strong>Address:</strong></p>
                            <t t-if="not o.partner_id.street and not o.partner_id.street2">
                                <p>.........................................................</p>
                                <p>.........................................................</p>
                            </t>
                            <p t-if="o.partner_id.street"><t t-esc="o.partner_id.street"/></p>
                            <p t-if="o.partner_id.street2"><t t-esc="o.partner_id.street2"/></p>

                            <p>
                                <strong>City: </strong>
                                <t t-if="o.partner_id.city"><t t-esc="o.partner_id.city"/></t>
                                <t t-else="">......................................</t>
                            </p>
                            <p>
                                <strong>Zip: </strong>
                                <t t-if="o.partner_id.zip"><t t-esc="o.partner_id.zip"/></t>
                                <t t-else="">......................................</t>
                            </p>
                            <p>
                                <strong>Country: </strong>
                                <t t-if="o.partner_id.country_id.name"><t t-esc="o.partner_id.country_id.name"/></t>
                                <t t-else="">......................................</t>
                            </p>
                        </t>
                        <div t-else="" t-field="o.partner_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": True}'/>
                    </div>
                    <div class="col-6">
                        <p><strong>IBAN:</strong> <t t-if="not o.partner_bank_id.acc_number">......................................</t><t t-else="" t-esc="o.partner_bank_id.acc_number"/></p>
                        <p><strong>Phone:</strong> <t t-if="not o.partner_id.phone">......................................</t><t t-else="" t-esc="o.partner_id.phone"/></p>
                        <p><strong>Email:</strong> <t t-if="not o.partner_id.email">......................................</t><t t-else="" t-esc="o.partner_id.email"/></p>
                        <p><strong>Identification code<t t-if="not o.debtor_id_code"> (if applicable)</t>:</strong></p>
                        <p t-if="not o.debtor_id_code">.........................................................</p>
                        <p t-else=""><t t-esc="o.debtor_id_code"/></p>
                    </div>
                </div>
            </div>
            <br/>

            <h2>Validity</h2>
            <p>
                The mandate will only be used to pay invoices into the
                specified time range. If no end date is specified,
                you will have to contact us to stop its use.
            </p>
            <div class="row">
                <div class="col-6">
                <p><strong>Transaction type:</strong> recurrent</p>
                </div>
                <div class="col-6">
                    <p><strong>Start date:</strong> <t t-if="not o.start_date">......................................</t><span t-else="" t-esc="o.start_date" t-options="{'widget': 'date'}"/></p>
                    <p><strong>End date<t t-if="not o.end_date"> (optional)</t>:</strong> <t t-if="not o.end_date">......................................</t><span t-else="" t-esc="o.end_date" t-options="{'widget': 'date'}"/></p>
                </div>
            </div>
            <br/>

            <h2>Signature</h2>
            <div id="signature" class="row">
                <div class="col-6">
                <p><strong>Name of the reference party:</strong> ......................................</p>
                <p><strong>Signature:</strong></p>
                </div>
                <div class="col-6">
                <p><strong>Date and place of signature:</strong> ......................................</p>
                </div>
            </div>
        </template>
    </data>
</odoo>
