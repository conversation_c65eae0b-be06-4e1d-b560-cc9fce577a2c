<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="account_sepa_direct_debit_mandate_form">
            <field name="name">sdd.mandate.form</field>
            <field name="model">sdd.mandate</field>
            <field name="arch" type="xml">
                <form string="Customer mandate">
                    <header>
                        <field name="state" widget="statusbar" statusbar_visible="draft,active"/>
                        <button string="Print" type="action" name="%(account_sepa_direct_debit.sdd_mandate_form_report_main)d" class="oe_highlight" attrs="{'invisible':[('state','!=','draft')]}"/>
                        <button string="Validate" type="object" name="action_validate_mandate" class="oe_highlight" attrs="{'invisible':[('state','!=','draft')]}"/>
                        <button string="Revoke" type="object" name="action_revoke_mandate" class="oe_highlight" attrs="{'invisible':[('state','!=','active')]}"/>
                        <button string="Close" type="object" name="action_close_mandate" class="oe_highlight" attrs="{'invisible':[('state','!=','active')]}"/>
                        <button string="Cancel" type="object" name="action_cancel_draft_mandate" attrs="{'invisible':[('state','!=','draft')]}"/>
                    </header>

                    <sheet>
                        <div class="oe_button_box" name="sdd_mandate_form_button_box">
                            <button type="object" name="action_view_payments_to_collect" icon="fa-usd" class="oe_stat_button">
                                <span><field name="payments_to_collect_nber" widget="statinfo" string="Payments to Collect" help="Payments generated for this mandate that have not yet been collected."/></span>
                            </button>

                            <button type="object" name="action_view_paid_invoices" icon="fa-pencil-square-o" class="oe_stat_button">
                                <span><field name="paid_invoices_nber" widget="statinfo" string="Invoices Paid" help="Invoices paid with this mandate."/></span>
                            </button>
                        </div>

                        <group>
                            <group>
                                <field name="partner_id"/>
                                <field name="suitable_journal_ids" invisible="1"/>
                                <field name="partner_bank_id" domain="[('partner_id', '=', partner_id)]" context="{'default_partner_id':partner_id, 'sdd_mandate': True}"/>
                                <field name="payment_journal_id"/>
                                <field name="sdd_scheme"/>
                            </group>
                            <group>
                                <field name="name"/>
                                <field name="start_date"/>
                                <field name="end_date"/>
                                <field name="company_id" groups="base.group_multi_company" required="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="o_attachment_preview"/>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="view_partner_bank_form_inherit_account_sepa_direct_debit" model="ir.ui.view">
            <field name="name">res.partner.bank.view.form.inherit.account.sepa.direct.debit</field>
            <field name="model">res.partner.bank</field>
            <field name="inherit_id" ref="base.view_partner_bank_form"/>
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes">
                    <attribute name="invisible">context.get('sdd_mandate') == True</attribute>
                </field>
                <field name="acc_holder_name" position="attributes">
                    <attribute name="invisible">context.get('sdd_mandate') == True</attribute>
                </field>
            </field>
        </record>

        <record model="ir.ui.view" id="account_sepa_direct_debit_mandate_tree">
            <field name="name">sdd.mandate.tree</field>
            <field name="model">sdd.mandate</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-muted="state == 'closed'" decoration-danger="state == 'revoked'" default_order="start_date desc">
                    <field name="partner_id"/>
                    <field name="name"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                    <field name="state"/>
                    <field name="sdd_scheme"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <record id="account_sdd_mandate_search_view" model="ir.ui.view">
            <field name="name">sdd.mandate.search</field>
            <field name="model">sdd.mandate</field>
            <field name="arch" type="xml">
                <search>
                    <field name="partner_id"/>
                    <field name="partner_bank_id"/>
                    <field name="name"/>
                    <filter string="Draft" name="account_sdd_mandate_draft_filter" domain="[('state', '=', 'draft')]"/>
                    <filter string="Active" name="account_sdd_mandate_active_filter" domain="[('state', '=', 'active')]"/>
                    <filter string="Closed" name="account_sdd_mandate_closed_filter" domain="[('state', 'in', ('closed','revoked'))]"/>
                </search>
            </field>
        </record>

        <record id="account_sepa_direct_debit_mandate_tree_act" model="ir.actions.act_window">
            <field name="name">Direct Debit Mandates</field>
            <field name="res_model">sdd.mandate</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_account_sdd_mandate_active_filter':1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new direct debit customer mandate
                </p><p>
                    A mandate represents the authorization you receive from a customer
                    to automatically collect money on her account.
                </p><p>
                    Once an invoice is made
                    in Odoo for a customer having an mandate active on the invoice date,
                    its validation will trigger its automatic payment, and you will
                    then only have to generate a SEPA Direct Debit (SDD) XML file containing this operation
                    and send it to your bank to effectively get paid.
                </p>
            </field>
        </record>

        <menuitem id="account_sepa_direct_debit_customer_mandates_menu"
            name="Direct Debit Mandates"
            parent="account.menu_finance_receivables"
            action="account_sepa_direct_debit_mandate_tree_act"
            sequence="50"/>
    </data>
</odoo>
