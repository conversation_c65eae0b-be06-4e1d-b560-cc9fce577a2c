# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_taxcloud
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Amharic (https://www.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Create Account &amp; Get Credentials"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "ድርጅት"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_create_uid
msgid "Created by"
msgstr "ፈጣሪው"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_create_date
msgid "Created on"
msgstr "የተፈጠረበት"

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company_tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings_tic_category_id
msgid "Default TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company_tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings_tic_category_id
msgid ""
"Default TICs(Taxability information codes) code to get sales tax from "
"TaxCloud by product category."
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default TaxCloud category for new products"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_display_name
msgid "Display Name"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_state_ids
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_template_state_ids
msgid "Federal States"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_id
msgid "ID"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_invoice
msgid "Invoice"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category___last_update
msgid "Last Modified on"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_write_date
msgid "Last Updated on"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "የእቃው ማሳያ"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_template_tic_category_ids
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_tic_category_ids
#: model:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_code
msgid "TIC Category Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_description
msgid "TIC Description"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings_taxcloud_api_id
msgid "TaxCloud API ID"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings_taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr ""

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "TaxCloud Categories (TIC)"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_product_tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template_tic_category_id
#: model:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_product_tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_product_template_tic_category_id
msgid ""
"TaxCloud uses Taxability Information Codes (TIC) to make sure each item in "
"your catalog is taxed at the right rate (or, for tax-exempt items, not taxed"
" at all), so it's important to make sure that each item is assigned a TIC. "
"If you can't find the right tax category for an item in your catalog, you "
"can assign it to the 'General Goods and Services' TIC, 00000. TaxCloud "
"automatically assigns products to this TIC as a default, so unless you've "
"changed an item's TIC in the past, it should already be set to 00000."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_tax_template
msgid "Template Tax Fiscal Position"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_fiscal_position.py:99
#: code:addons/account_taxcloud/models/account_invoice.py:48
#: code:addons/account_taxcloud/models/res_config_settings.py:40
#, python-format
msgid "The configuration of TaxCloud is in the Accounting app, Settings menu."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:119
#, python-format
msgid ""
"The source document on the refund is not valid and thus the refunded cart "
"won't be logged on your taxcloud account"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:89
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_fiscal_position.py:99
#: code:addons/account_taxcloud/models/account_invoice.py:48
#: code:addons/account_taxcloud/models/res_config_settings.py:40
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr ""

#. module: account_taxcloud
#: model:ir.actions.server,name:account_taxcloud.action_account_invoice_update_taxes
msgid "Update taxes with Taxcloud"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template_is_taxcloud
msgid "Use TaxCloud API"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_template_zip_codes
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_zip_codes
msgid "Zip"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:28
#, python-format
msgid "[%s] %s"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "product.tic.category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "res.config.settings"
msgstr ""
