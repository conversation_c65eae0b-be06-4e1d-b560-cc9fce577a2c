# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_taxcloud
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to Get Credentials"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/> كيفية الحصول على وثائق الاعتماد"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"
msgstr ""
"<i title=\"استيراد/تحديث TIC من TaxCloud \" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr "معرف الواجهة البرمجية للتطبيق (API) "

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr "مفتاح الواجهة البرمجية للتطبيق (API) "

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الاعدادات"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr "الفئة الافتراضية"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__tic_category_id
msgid "Default TIC Code"
msgstr "كود TIC الافتراضي"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
msgid ""
"Enable <b>Detect Automatically</b> to automatically use TaxCloud when "
"selling to American customers."
msgstr ""
"تمكين <b>خاصية الاستشعار التلقائي</b> لاستخدام TaxCloud تلقائياً عند البيع "
"للعملاء الأمريكيين. "

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "الوضع المالي "

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid "Go to Settings."
msgstr "الذهاب للإعدادات. "

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__id
msgid "ID"
msgstr "المُعرف"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "هل تمت تهيئة Taxcloud "

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "قيد اليومية"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/taxcloud_request.py:0
#, python-format
msgid ""
"Please configure taxcloud credentials on the current company or use a "
"different fiscal position"
msgstr ""
"الرجاء تهيئة بيانات اعتماد Taxcloud في الشركة الحالية أو استخدم وضعاً مالياً"
" مختلفاً "

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"الرجاء إدخال بيانات اعتماد Taxcloud الخاصة بك لحساب معدلات الضرائب تلقائياً."
" "

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_category
msgid "Product Category"
msgstr "فئة المنتج"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr "فئات TIC للمنتج"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "Product TIC Category"
msgstr "فئة TIC للمنتج"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings__tic_category_id
msgid ""
"TIC (Taxability Information Codes) allow to get specific tax rates for each "
"product type. This default value applies if no product is used in the "
"order/invoice, or if no TIC is set on the product or its product category. "
"By default, TaxCloud relies on the TIC *[0] Uncategorized* default referring"
" to general goods and services."
msgstr ""
"تتيح TIC (أكواد معلومات الخضوع للضريبة) الحصول على معدلات ضرائب محددة لكل "
"نوع منتج. تنطبق هذه القيمة الافتراضية إذا لم يتم استخدام أي منتج ءفي "
"الطلب/الفاتورة، أو إذا تم تعيين TIC للمنتج أو فئة المنتج. يعتمد TaxCloud على"
" TIC بشكل افتراضي في  TIC *[0] غير مصنّف* الافتراضي، مشيراً إلى المنتجات "
"العامة والخدمات. "

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr "فئة TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__code
msgid "TIC Category Code"
msgstr "كود فئة TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_category__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr "كود TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__description
msgid "TIC Description"
msgstr "وصف TIC"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_id
msgid "TaxCloud API ID"
msgstr "معرف TaxCloud API"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_key
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr "مفتاح TaxCloud API"

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr "فئات TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr "فئة TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud
msgid "Technical field to determine whether to hide taxes in views or not."
msgstr "حقل تقني لتحديد إذا ما كان يجب إخفاء الضرائب في العروض أم لا. "

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "قالب للوضع المالي "

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr "تم تحديث معدلات الضرائب. قد ترغب في مراجعتها قبل التصديق "

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_product_template__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. The value set "
"here prevails over the one set on the product category."
msgstr ""
"يشير ذلك إلى TIC (أكواد معلومات الخضوع للضريبة)، والتي تُستَخدَم من قِبَل "
"TaxCloud لحساب معدلات ضريبية محددة لكل نوع منتج. تسود القيمة المعيّنة هنا "
"على القيمة المحددة في فئة المنتج. "

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_category__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. This value is "
"used when no TIC is set on the product. If no value is set here, the default"
" value set in Invoicing settings is used."
msgstr ""
"يشير ذلك إلى TIC (أكواد معلومات الخضوع للضريبة)، والتي تُستَخدَم من قِبَل "
"TaxCloud لحساب معدلات ضريبية محددة لكل نوع منتج. تُستخدَم هذه القيمة عندما "
"لا يكون هناك TIC معين لهذا المنتج. إذا لم يتم تعيين قيمة هنا، سوف تُستخدَم "
"القيمة المحددة في إعدادات الفوترة. "

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#: code:addons/account_taxcloud/models/res_config_settings.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "تعذر استرداد بيانات الضرائب من Taxcloud: "

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud
msgid "Use TaxCloud API"
msgstr "استخدام TaxCloud API"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_res_company__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr "يُستخدَم لتحديد ما إذا كان يجب تحذير المستخدم لتهيئة TaxCloud أم لا. "

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"You cannot cancel an invoice sent to TaxCloud.\n"
"You need to issue a refund (credit note) for it instead.\n"
"This way the tax entries will be cancelled in TaxCloud."
msgstr ""
"لا يمكنك إلغاء فاتورة مرسلة لـ TaxCloud.\n"
"عليك إصدار أمر لاسترداد الأموال (إشعار دائن) عوضاً عن ذلك.\n"
"بهذه الطريقة، سوف يتم إلغاء القيود الضريبية في TaxCloud. "

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s] %s"
