# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_taxcloud
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to Get Credentials"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__tic_category_id
msgid "Default TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings__tic_category_id
msgid ""
"Default TICs(Taxability information codes) code to get sales tax from "
"TaxCloud by product category."
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_template__tic_category_id
msgid ""
"Each product falls into a category which has specific taxes predermined by "
"the government.The system will use the Tax Cloud category set on the "
"internal category of the product. If thereisn't any, the one on the product "
"itself will be used. Only used in United States."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__id
msgid "ID"
msgstr "ID"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_category
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "Product TIC Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__code
msgid "TIC Category Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_category__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__description
msgid "TIC Description"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
msgid "TaxCloud"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_id
msgid "TaxCloud API ID"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr ""

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_category__tic_category_id
msgid ""
"TaxCloud uses Taxability Information Codes (TIC) to make sure each item in "
"your catalog is taxed at the right rate (or, for tax-exempt items, not taxed"
" at all), so it's important to make sure that each item is assigned a TIC. "
"If you can't find the right tax category for an item in your catalog, you "
"can assign it to the 'General Goods and Services' TIC, 00000. TaxCloud "
"automatically assigns products to this TIC as a default, so unless you've "
"changed an item's TIC in the past, it should already be set to 00000."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Prijedlog za fiskalnu poziciju"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:43
#: code:addons/account_taxcloud/models/res_config_settings.py:23
#, python-format
msgid "The configuration of TaxCloud is in the Accounting app, Settings menu."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:115
#, python-format
msgid ""
"The source document on the refund is not valid and thus the refunded cart "
"won't be logged on your taxcloud account"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:85
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:43
#: code:addons/account_taxcloud/models/res_config_settings.py:23
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr ""

#. module: account_taxcloud
#: model:ir.actions.server,name:account_taxcloud.action_account_invoice_update_taxes
msgid "Update taxes with Taxcloud"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template__is_taxcloud
msgid "Use TaxCloud API"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:28
#, python-format
msgid "[%s] %s"
msgstr ""
