# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_taxcloud
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: karol<PERSON>a schusterov<PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to Get Credentials"
msgstr "Jak získat pověření"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"
msgstr ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr "API ID"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr "API KEY"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr "Výchozí kategorie"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__tic_category_id
msgid "Default TIC Code"
msgstr "Výchozí kód TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
msgid ""
"Enable <b>Detect Automatically</b> to automatically use TaxCloud when "
"selling to American customers."
msgstr ""
"Chcete-li automaticky použít TaxCloud při prodeji americkým zákazníkům, "
"povolte <b>automatické rozpoznávání</b>."

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Daňová pozice"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid "Go to Settings."
msgstr "Jít do nastavení."

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__id
msgid "ID"
msgstr "ID"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "Je nakonfigurován taxcloud"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "Položka deníku"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "Položka deníku"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/taxcloud_request.py:0
#, python-format
msgid ""
"Please configure taxcloud credentials on the current company or use a "
"different fiscal position"
msgstr ""
"Nakonfigurujte přihlašovací údaje taxcloudu na aktuální společnost nebo "
"použijte jinou fiskální pozici"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"Chcete-li automaticky vypočítat daňové sazby, zadejte své přihlašovací údaje"
" k Taxcloudu."

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_category
msgid "Product Category"
msgstr "Kategorie výrobku"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr "Kategorie produktů TIC"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "Product TIC Category"
msgstr "Kategorie TIC produktu"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "Šablona produktu"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings__tic_category_id
msgid ""
"TIC (Taxability Information Codes) allow to get specific tax rates for each "
"product type. This default value applies if no product is used in the "
"order/invoice, or if no TIC is set on the product or its product category. "
"By default, TaxCloud relies on the TIC *[0] Uncategorized* default referring"
" to general goods and services."
msgstr ""
"Kódy TIC (Taxability Information Codes) umožňují získat konkrétní daňové "
"sazby pro každý typ produktu. Tato výchozí hodnota platí, pokud v objednávce"
" / faktuře není použit žádný produkt, nebo pokud u produktu nebo jeho "
"kategorie produktu není nastaven žádný TIC. Ve výchozím nastavení TaxCloud "
"spoléhá na TIC * [0] Uncategorized * default odkazující na obecné zboží a "
"služby."

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr "Kategorie TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__code
msgid "TIC Category Code"
msgstr "Kód kategorie TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_category__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr "TIC kód"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__description
msgid "TIC Description"
msgstr "TIC popis"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_id
msgid "TaxCloud API ID"
msgstr "TaxCloud API ID"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_key
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr "TaxCloud API KEY"

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr "TaxCloud kategorie"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr "TaxCloud kategorie"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud
msgid "Technical field to determine whether to hide taxes in views or not."
msgstr "Technické pole k určení, zda skrýt daně v zobrazeních či nikoli."

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Šablona pro finanční pozici"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr ""
"Sazby daně byly aktualizovány, možná budete chtít zkontrolovat před validací"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_product_template__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. The value set "
"here prevails over the one set on the product category."
msgstr ""
"Týká se TIC (Taxability Information Codes), které používá TaxCloud pro "
"výpočet specifických daňových sazeb pro každý typ produktu. Zde nastavená "
"hodnota převládá nad hodnotou nastavenou v kategorii produktu."

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_category__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. This value is "
"used when no TIC is set on the product. If no value is set here, the default"
" value set in Invoicing settings is used."
msgstr ""
"Jedná se o kódy TIC (Taxability Information Codes), které TaxCloud používá k"
" výpočtu konkrétních daňových sazeb pro každý typ produktu. Tato hodnota se "
"používá, když na produktu není nastaven žádný TIC. Pokud zde není nastavena "
"žádná hodnota, použije se výchozí hodnota nastavená v nastavení fakturace."

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#: code:addons/account_taxcloud/models/res_config_settings.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "Nelze získat daňovou sazbu z TaxCloud:"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud
msgid "Use TaxCloud API"
msgstr "Použít API služby TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_res_company__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
"Slouží k určení, zda uživatele upozornit, aby nakonfiguroval TaxCloud."

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"You cannot cancel an invoice sent to TaxCloud.\n"
"You need to issue a refund (credit note) for it instead.\n"
"This way the tax entries will be cancelled in TaxCloud."
msgstr ""
"Fakturu odeslanou na TaxCloud nemůžete zrušit.\n"
"Místo toho je třeba za ni vrátit peníze (dobropis).\n"
"Tímto způsobem budou daňové záznamy v TaxCloudu zrušeny."

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s] %s"
