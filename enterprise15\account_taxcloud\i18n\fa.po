# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_taxcloud
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <ifara<PERSON><EMAIL>>, 2022
# <PERSON> <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Mostafa <PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to Get Credentials"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"
msgstr ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr "API ID"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr "کلید API"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_uid
msgid "Created by"
msgstr "ایجادشده توسط"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_date
msgid "Created on"
msgstr "ایجاد شده در"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr "دسته‌بندی پیشفرض"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__tic_category_id
msgid "Default TIC Code"
msgstr "کد TIC پیشفرض"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
msgid ""
"Enable <b>Detect Automatically</b> to automatically use TaxCloud when "
"selling to American customers."
msgstr ""
"Enable <b>تشخیص خودکار</b> استفاده خودکار از TaxCloud هنگام فروش به مشتریان "
"آمریکایی."

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "موقعیت سال مالی"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid "Go to Settings."
msgstr "به تنظیمات برو"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__id
msgid "ID"
msgstr "شناسه"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr "آیا TaxCloud پیکربندی شده است"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "ورودی روزنامه"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "آیتم سند"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_uid
msgid "Last Updated by"
msgstr "آخرین به‌روز‌رسانی توسط"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_date
msgid "Last Updated on"
msgstr "آخرین به روز رسانی در"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/taxcloud_request.py:0
#, python-format
msgid ""
"Please configure taxcloud credentials on the current company or use a "
"different fiscal position"
msgstr ""
"لطفاً مشخصات کاربری TaxCloud را روی شرکت فعلی پیکربندی کرده و از یک موقعیت "
"مالی متفاوت استفاده کنید"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""
"لطفاً مشخصات کاربری TaxCloud خود را برای مقایسه‌ی خودکار نرخ مالیات وارد "
"کنید."

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_category
msgid "Product Category"
msgstr "دسته بندی محصول"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr "دسته‌بندی‌های TIC محصول"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "Product TIC Category"
msgstr "دسته‌بندی TIC محصول"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "قالب محصول"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings__tic_category_id
msgid ""
"TIC (Taxability Information Codes) allow to get specific tax rates for each "
"product type. This default value applies if no product is used in the "
"order/invoice, or if no TIC is set on the product or its product category. "
"By default, TaxCloud relies on the TIC *[0] Uncategorized* default referring"
" to general goods and services."
msgstr ""
"TIC (کدهای اطلاعات مربوط به مشمولیت مالیاتی) امکان به دست آوردن نرخ‌های مشخص"
" مالیات را برای هر نوع محصول فراهم می‌کند. این مقدار پیشفرض در صورتی اعمال "
"می‌شود که هیچ محصولی در سفارش/فاکتور مورد استفاده قرار نگرفته باشد یا اینکه "
"TIC روی محصول یا دسته‌بندی آن تنظیم نشده باشد. به طور پیشفرض، TaxCloud مبتنی"
" بر  *[0]TIC پیشفرض دسته‌بندی نشده مربوط به کالاها و خدمات عمومی است. "

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr "دسته‌بندی TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__code
msgid "TIC Category Code"
msgstr "کد دسته‌بندی TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_category__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr "کد TIC"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__description
msgid "TIC Description"
msgstr "توصیف TIC"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
msgid "TaxCloud"
msgstr "ابر مالیاتی"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_id
msgid "TaxCloud API ID"
msgstr "شناسه‌ی TaxCloud API"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_key
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr "کلید TaxCloud API"

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr "دسته‌بندی‌های TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr "دسته‌بندی TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud
msgid "Technical field to determine whether to hide taxes in views or not."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "قالب برای موقعیت مالی"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr ""
"نرخ‌های مالیات به روزرسانی شده‌اند، ممکن است بخواهید پیش از اعتبارسنجی آن را"
" بررسی کنید"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_product_template__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. The value set "
"here prevails over the one set on the product category."
msgstr ""
"این به TIC اشاره دارد (کدهای اطلاعات مربوط به مشمولیت مالیاتی)، این موارد "
"بوسیله‌ی TaxCloud برای محاسبه‌ی نرخ‌های مالیاتی مشخص برای هر نوع محصول مورد "
"استفاده قرار می‌گیرند. مقدار مشخص شده در اینجا بر مقدار تعیین شده در "
"دسته‌بندی محصول ارجحیت دارد. "

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_category__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. This value is "
"used when no TIC is set on the product. If no value is set here, the default"
" value set in Invoicing settings is used."
msgstr ""
"این مورد به TIC (کدهای اطلاعات مربوط به مشمولیت مالیاتی) اشاره دارد، این "
"موارد بوسیله‌ی TaxCloud برای محاسبه‌ی نرخ‌های مالیاتی مشخص هر نوع محصول مورد"
" استفاده قرار می‌گیرند. این مقدار زمانی استفاده می‌شود که هیچ TIC روی محصول "
"تنظیم نشده باشد. اگر هیچ مقداری در اینجا مشخص نشده باشد، مقدار پیشفرض تعیین "
"شده در تنظیمات صدور فاکتور مورد استفاده قرار می‌گیرد."

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#: code:addons/account_taxcloud/models/res_config_settings.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "قادر به بازیابی مالیات‌ها از TaxCloud نیست:"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud
msgid "Use TaxCloud API"
msgstr "از TaxCloud "

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_res_company__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""
"برای تعیین لزوم هشدار دادن یا ندادن به کاربر برای پیکربندی TaxCloud "
"مورداستفاده قرار می‌گیرد."

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"You cannot cancel an invoice sent to TaxCloud.\n"
"You need to issue a refund (credit note) for it instead.\n"
"This way the tax entries will be cancelled in TaxCloud."
msgstr ""
"شما نمی‌توانید یک فاکتور ارسال شده به TaxCloud را حذف کنید.\n"
"به جای آن باید یک دستور استرداد (سند بستانکار) صادر کنید.\n"
"اینگونه سندهای مالیاتی در TaxCloud لغو می‌شوند. "

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s]%s"
