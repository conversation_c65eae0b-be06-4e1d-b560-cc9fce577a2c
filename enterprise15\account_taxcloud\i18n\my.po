# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_taxcloud
# 
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-07 09:57+0000\n"
"PO-Revision-Date: 2016-09-07 09:57+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2016\n"
"Language-Team: Burmese (https://www.transifex.com/odoo/teams/41243/my/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: my\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "ကုမ္ပဏီများ"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_create_uid
msgid "Created by"
msgstr "တည်ဆောက်သူ"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_create_date
msgid "Created on"
msgstr "တည်ဆောက်သည့်အချိန်"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company_tic_category_id
msgid "Default TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_config_settings_tic_category_id
msgid "Default TIC Code *"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_config_settings_tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_company_tic_category_id
msgid ""
"Default TICs(Taxabilty information codes) code to get sales tax from "
"TaxCloud by product category."
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_display_name
msgid "Display Name"
msgstr "ပြချင်သော အမည်"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_state_ids
msgid "Federal States"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "ဘဏ္ဍာရေးနှစ်အနေအထား"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_id
msgid "ID"
msgstr "နံပါတ်"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category___last_update
msgid "Last Modified on"
msgstr "နောက်ဆုံးပြင်ဆင်ချိန်"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_write_uid
msgid "Last Updated by"
msgstr "နောက်ဆုံးပြင်ဆင်သူ"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_write_date
msgid "Last Updated on"
msgstr "နောက်ဆုံးပြင်ဆင်ချိန်"

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "ကုန်ပစ္စည်း ပုံစံ"

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_config_settings_taxcloud_inherit
msgid "Sync TaxCloud Category(TIC)"
msgstr ""

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TIC Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_tic_category_ids
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template_tic_category_id
#: model:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_code
msgid "TIC Category Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category_description
msgid "TIC Description"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_config_settings_taxcloud_inherit
msgid "TaxCloud"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_config_settings_taxcloud_api_id
msgid "TaxCloud API ID"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_config_settings_taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.account_config_settings_taxcloud_inherit
msgid "TaxCloud Configuration"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_template_tic_category_id
msgid ""
"TaxCloud uses Taxability Information Codes (TIC) to make sure each item in "
"your catalog is taxed at the right rate (or, for tax-exempt items, not taxed"
" at all), so it’s important to make sure that each item is assigned a TIC. "
"If you can’t find the right tax category for an item in your catalog, you "
"can assign it to the 'General Goods and Services' TIC, 00000. TaxCloud "
"automatically assigns products to this TIC as a default, so unless you've "
"changed an item's TIC in the past, it should already be set to 00000."
msgstr ""

#. module: account_taxcloud
#: model:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "Taxcloud Configuration"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_tax
msgid "Taxes Fiscal Position"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_is_taxcloud
msgid "Use TaxCloud API"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_tax_zip_codes
msgid "Zip"
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:27
#, python-format
msgid "[%s] %s"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_config_settings
msgid "account.config.settings"
msgstr "account.config.settings"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "product.tic.category"
msgstr ""
