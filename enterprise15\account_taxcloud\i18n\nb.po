# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_taxcloud
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:37+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: <PERSON>k<PERSON>l (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to Get Credentials"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid ""
"<i title=\"Import/update TICs from TaxCloud\" role=\"img\" aria-"
"label=\"Import/update TICs from TaxCloud\" class=\"fa fa-refresh fa-fw\"/>"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API ID"
msgstr "API-ID"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "API KEY"
msgstr "API-NØKKEL"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.res_config_settings_view_form
msgid "Default Category"
msgstr "Standardkategori"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__tic_category_id
msgid "Default TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
msgid ""
"Enable <b>Detect Automatically</b> to automatically use TaxCloud when "
"selling to American customers."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Avgiftsregel"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid "Go to Settings."
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__id
msgid "ID"
msgstr "ID"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__is_taxcloud_configured
msgid "Is Taxcloud Configured"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move
msgid "Journal Entry"
msgstr "Bilag"

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/taxcloud_request.py:0
#, python-format
msgid ""
"Please configure taxcloud credentials on the current company or use a "
"different fiscal position"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_position_taxcloud_inherit_from_view
#: model_terms:ir.ui.view,arch_db:account_taxcloud.invoice_form_inherit
msgid ""
"Please enter your Taxcloud credentials to compute tax rates automatically."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_category
msgid "Product Category"
msgstr "Produktkategori"

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "Product TIC Categories"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_tic_category
msgid "Product TIC Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_product_template
msgid "Product Template"
msgstr "Produktmal"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_res_company__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_res_config_settings__tic_category_id
msgid ""
"TIC (Taxability Information Codes) allow to get specific tax rates for each "
"product type. This default value applies if no product is used in the "
"order/invoice, or if no TIC is set on the product or its product category. "
"By default, TaxCloud relies on the TIC *[0] Uncategorized* default referring"
" to general goods and services."
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_tree
msgid "TIC Category"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__code
msgid "TIC Category Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_category__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.account_taxcloud_tic_category_search
msgid "TIC Code"
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_tic_category__description
msgid "TIC Description"
msgstr ""

#. module: account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
msgid "TaxCloud"
msgstr "TaxCloud"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_id
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_id
msgid "TaxCloud API ID"
msgstr "TaxCloud API-ID"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_res_company__taxcloud_api_key
#: model:ir.model.fields,field_description:account_taxcloud.field_res_config_settings__taxcloud_api_key
msgid "TaxCloud API KEY"
msgstr "TaxCloud API-nøkkel"

#. module: account_taxcloud
#: model:ir.actions.act_window,name:account_taxcloud.account_taxcloud_tic_category_action
#: model:ir.ui.menu,name:account_taxcloud.menu_taxcloud_tic_category_action
msgid "TaxCloud Categories"
msgstr "TaxCloud-kategorier"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,field_description:account_taxcloud.field_product_template__tic_category_id
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_category_view_form_inherit_account_taxcloud
#: model_terms:ir.ui.view,arch_db:account_taxcloud.product_template_taxcloud_inherit_form
msgid "TaxCloud Category"
msgstr "TaxCloud-kategori"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud
msgid "Technical field to determine whether to hide taxes in views or not."
msgstr ""

#. module: account_taxcloud
#: model:ir.model,name:account_taxcloud.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "Mal for regnskapsstatus"

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"The tax rates have been updated, you may want to check it before validation"
msgstr ""
"Avgiftssatsene har blitt oppdatert, du bør sjekke dem før du validerer"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_product__tic_category_id
#: model:ir.model.fields,help:account_taxcloud.field_product_template__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. The value set "
"here prevails over the one set on the product category."
msgstr ""

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_product_category__tic_category_id
msgid ""
"This refers to TIC (Taxability Information Codes), these are used by "
"TaxCloud to compute specific tax rates for each product type. This value is "
"used when no TIC is set on the product. If no value is set here, the default"
" value set in Invoicing settings is used."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#: code:addons/account_taxcloud/models/res_config_settings.py:0
#, python-format
msgid "Unable to retrieve taxes from TaxCloud: "
msgstr "Kunne ikke hente skatt fra TaxCloud:"

#. module: account_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_bank_statement_line__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_fiscal_position_template__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_move__is_taxcloud
#: model:ir.model.fields,field_description:account_taxcloud.field_account_payment__is_taxcloud
msgid "Use TaxCloud API"
msgstr "Bruk TaxCloud-API"

#. module: account_taxcloud
#: model:ir.model.fields,help:account_taxcloud.field_account_bank_statement_line__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_fiscal_position__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_move__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_account_payment__is_taxcloud_configured
#: model:ir.model.fields,help:account_taxcloud.field_res_company__is_taxcloud_configured
msgid ""
"Used to determine whether or not to warn the user to configure TaxCloud."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/account_invoice.py:0
#, python-format
msgid ""
"You cannot cancel an invoice sent to TaxCloud.\n"
"You need to issue a refund (credit note) for it instead.\n"
"This way the tax entries will be cancelled in TaxCloud."
msgstr ""

#. module: account_taxcloud
#: code:addons/account_taxcloud/models/product.py:0
#, python-format
msgid "[%s] %s"
msgstr "[%s] %s"
