<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_taxcloud_tic_category_form" model="ir.ui.view">
        <field name="name">product.tic.category.from</field>
        <field name="model">product.tic.category</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="code"/>
                        <field name="description"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="account_taxcloud_tic_category_tree" model="ir.ui.view">
        <field name="name">product.tic.category.tree</field>
        <field name="model">product.tic.category</field>
        <field name="arch" type="xml">
            <tree string="TIC Category" editable="bottom">
                <field name="description"/>
                <field name="code" options="{'format': false}"/>
            </tree>
        </field>
    </record>

    <record id="account_taxcloud_tic_category_search" model="ir.ui.view">
        <field name="name">product.tic.category.search</field>
        <field name="model">product.tic.category</field>
        <field name="arch" type="xml">
            <search string="Product TIC Categories">
                <field name="code" string="TIC Code" options="{'format': false}"/>
                <field name="description"/>
            </search>
        </field>
    </record>

    <record id="account_taxcloud_tic_category_action" model="ir.actions.act_window">
        <field name="name">TaxCloud Categories</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.tic.category</field>
        <field name="search_view_id" ref="account_taxcloud_tic_category_search"/>
        <field name="view_id" ref="account_taxcloud_tic_category_tree"/>
    </record>

    <menuitem action="account_taxcloud_tic_category_action" groups="base.group_no_one" id="menu_taxcloud_tic_category_action" parent="account.account_management_menu" sequence="8"/>

    <record id="product_template_taxcloud_inherit_form" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <field name="taxes_id" position="after">
                <field name="tic_category_id" string="TaxCloud Category"/>
            </field>
        </field>
    </record>

    <record id="product_category_view_form_inherit_account_taxcloud" model="ir.ui.view">
        <field name="name">product.category.view.form.inherit.account.taxcloud</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="product.product_category_form_view"/>
        <field name="arch" type="xml">
            <group name='first' position="after">
                <group>
                    <group name="sale_taxcloud" string="TaxCloud">
                        <field name="tic_category_id" string="TaxCloud Category"/>
                    </group>
                </group>
            </group>
        </field>
    </record>

</odoo>
