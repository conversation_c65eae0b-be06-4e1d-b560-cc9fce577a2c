# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_winbooks_import
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: <PERSON> (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "%s is not a valid account number for %s."
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" attrs=\"{'invisible': [('only_open', '=', True)]}\">\n"
"                        The export of data from Winbooks for closed years might contain unbalanced entries. However if you want to try to import everything, Odoo will set the difference of balance in a Suspense Account.\n"
"                    </span>"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_winbooks_import_wizard
msgid "Account Winbooks import wizard"
msgstr ""

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Accounting Settings"
msgstr "Regnskapsinnstillinger"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Cancel"
msgstr "Kanseller"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Company Settings"
msgstr "Firma-innstillinger"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Counterpart (generated at import from Winbooks)"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__done
msgid "Done"
msgstr "Fullført"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__zip_file
msgid "File"
msgstr "Fil"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import"
msgstr "Import"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import (safe-mode)"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid "Import only open years"
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Import your accounting data from Winbooks."
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_move_line
msgid "Journal Item"
msgstr "Journalpost"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__just_done
msgid "Just done"
msgstr "Nettopp ferdig"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Looks great!"
msgstr "Ser bra ut!"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_move_line__winbooks_matching_number
msgid "Matching number that was used in Winbooks"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__not_done
msgid "Not done"
msgstr "Ikke fullført"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Please define the country on your company."
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Stage Search"
msgstr "Søk i stadier"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_res_company__account_onboarding_winbooks_state
msgid "State of the onboarding winbooks step"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid "Suspense Account Code"
msgstr ""

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"The code for the Suspense Account you entered doesn't match any account"
msgstr ""

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"The following banks were used for multiple partners in Winbooks, which is not allowed in Odoo. The bank number has been only set on one of each group:\n"
"%s"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid ""
"This is the code of the account in which you want to put the counterpart of "
"unbalanced moves. This might be an account from your Winbooks data, or an "
"account that you created in Odoo before the import."
msgstr ""

#. module: account_winbooks_import
#: model:ir.actions.act_window,name:account_winbooks_import.winbooks_import_action
#: model:ir.ui.menu,name:account_winbooks_import.menu_action_import_winbooks
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Winbooks Import"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_move_line__winbooks_matching_number
msgid "Winbooks Matching Number"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid ""
"Years closed in Winbooks are likely to have incomplete data. The counter "
"part of incomplete entries will be set in a suspense account"
msgstr ""

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "You should install a Fiscal Localization first."
msgstr ""

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"dbfread library not found, Winbooks Import features disabled. If you plan to"
" use it, please install the dbfread library from "
"https://pypi.org/project/dbfread/"
msgstr ""
