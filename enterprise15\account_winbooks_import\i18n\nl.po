# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_winbooks_import
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <gclau<PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "%s is not a valid account number for %s."
msgstr "%s is geen geldig rekeningnummer voor %s."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" attrs=\"{'invisible': [('only_open', '=', True)]}\">\n"
"                        The export of data from Winbooks for closed years might contain unbalanced entries. However if you want to try to import everything, Odoo will set the difference of balance in a Suspense Account.\n"
"                    </span>"
msgstr ""
"<span/>\n"
"                   <span class=\"text-warning mb4 mt16\" attrs=\"{'invisible': [('only_open', '=', True)]}\">\n"
"                       De export van gegevens uit Winbooks voor gesloten jaren kan onevenwichtige invoer bevatten. Als je echter alles wilt importeren, zal Odoo het verschil in saldo op een tussenrekening plaatsen.\n"
"                   </span>"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_winbooks_import_wizard
msgid "Account Winbooks import wizard"
msgstr "Account Winbooks importwizard"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Accounting Settings"
msgstr "Boekhoudinstellingen"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Cancel"
msgstr "Annuleren"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Company Settings"
msgstr "Bedrijfsinstellingen"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Counterpart (generated at import from Winbooks)"
msgstr "Counterpart (gegenereerde import vanuit Winbooks)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__done
msgid "Done"
msgstr "Afgehandeld"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__zip_file
msgid "File"
msgstr "Bestand"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import"
msgstr "Importeren"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import (safe-mode)"
msgstr "Import (veilige-mode)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid "Import only open years"
msgstr "Importeer alleen open jaren"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Import your accounting data from Winbooks."
msgstr "Importeer je boekhoudgegevens uit Winbooks."

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__just_done
msgid "Just done"
msgstr "Net gedaan"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Looks great!"
msgstr "Ziet er goed uit!"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_move_line__winbooks_matching_number
msgid "Matching number that was used in Winbooks"
msgstr "Overeenkomend nummer dat werd gebruikt in Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__not_done
msgid "Not done"
msgstr "Niet gedaan"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Please define the country on your company."
msgstr "Geef het land van je bedrijf op."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Stage Search"
msgstr "Zoek fase"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_res_company__account_onboarding_winbooks_state
msgid "State of the onboarding winbooks step"
msgstr "Status van de onboarding winbooks stap"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid "Suspense Account Code"
msgstr "Tussenrekeningcode"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"The code for the Suspense Account you entered doesn't match any account"
msgstr ""
"De code voor de tussenrekening die je hebt ingevoerd, komt niet overeen met "
"een rekening"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"The following banks were used for multiple partners in Winbooks, which is not allowed in Odoo. The bank number has been only set on one of each group:\n"
"%s"
msgstr ""
"De volgende banken werden gebruikt voor meerdere relaties in Winbooks, wat niet is toegestaan in Odoo. Het banknummer is slechts op één van elke groep ingesteld:\n"
"%s"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid ""
"This is the code of the account in which you want to put the counterpart of "
"unbalanced moves. This might be an account from your Winbooks data, or an "
"account that you created in Odoo before the import."
msgstr ""
"Dit is de code van het rekening waarin je de tegenrekening van "
"ongebalanceerde boekingen wilt plaatsen. Dit kan een rekening zijn uit je "
"Winbooks-gegevens, of een rekening dat je in Odoo hebt aangemaakt vóór het "
"importeren."

#. module: account_winbooks_import
#: model:ir.actions.act_window,name:account_winbooks_import.winbooks_import_action
#: model:ir.ui.menu,name:account_winbooks_import.menu_action_import_winbooks
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Winbooks Import"
msgstr "Winbooks Import"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_move_line__winbooks_matching_number
msgid "Winbooks Matching Number"
msgstr "Winbooks overeenkomend nummer"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid ""
"Years closed in Winbooks are likely to have incomplete data. The counter "
"part of incomplete entries will be set in a suspense account"
msgstr ""
"Jaren die in Winbooks zijn gesloten, bevatten waarschijnlijk onvolledige "
"gegevens. Het tegendeel van onvolledige boekingen wordt op een "
"tussenrekening bijgeschreven."

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "You should install a Fiscal Localization first."
msgstr "Je dient eerst de fiscale posities aan te maken"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"dbfread library not found, Winbooks Import features disabled. If you plan to"
" use it, please install the dbfread library from "
"https://pypi.org/project/dbfread/"
msgstr ""
"dbfread-bibliotheek niet gevonden, Winbooks-importfuncties uitgeschakeld. "
"Als je van plan bent het te gebruiken, installeer dan de dbfread-bibliotheek"
" vanaf https://pypi.org/project/dbfread/"
