# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_winbooks_import
# 
# Translators:
# <PERSON>ot<PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <judyta.ka<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <tadeusz<PERSON><PERSON><EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "%s is not a valid account number for %s."
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" attrs=\"{'invisible': [('only_open', '=', True)]}\">\n"
"                        The export of data from Winbooks for closed years might contain unbalanced entries. However if you want to try to import everything, Odoo will set the difference of balance in a Suspense Account.\n"
"                    </span>"
msgstr ""
"<span/>\n"
"                    <span class=\"text-warning mb4 mt16\" attrs=\"{'invisible': [('only_open', '=', True)]}\">\n"
"                        Eksport danych z Winbooks dla zamkniętych lat może zawierać niezbilansowane wpisy. Jeśli jednak chcesz spróbować zaimportować wszystko, Odoo ustawi różnicę salda na koncie przejściowym\n"
"                    </span>"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_winbooks_import_wizard
msgid "Account Winbooks import wizard"
msgstr "Kreator importu kont Winbooks"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Accounting Settings"
msgstr "Ustawienia księgowości"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Cancel"
msgstr "Anuluj"

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Company Settings"
msgstr "Ustawienia firmy"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Counterpart (generated at import from Winbooks)"
msgstr "Duplikat (wygenerowany przy imporcie z Winbooks)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__done
msgid "Done"
msgstr "Wykonano"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__zip_file
msgid "File"
msgstr "Plik"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import"
msgstr "Importuj"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import (safe-mode)"
msgstr "importuj (safe-mode)"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid "Import only open years"
msgstr "Importuj tylko otwarte lata"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Import your accounting data from Winbooks."
msgstr "Zaimportuj dane księgowe z Winbooks."

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_move_line
msgid "Journal Item"
msgstr "Pozycja zapisu"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__just_done
msgid "Just done"
msgstr "Wykonano"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Looks great!"
msgstr "Wygląda świetnie!"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_move_line__winbooks_matching_number
msgid "Matching number that was used in Winbooks"
msgstr "Pasujący numer, który został użyty w Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields.selection,name:account_winbooks_import.selection__res_company__account_onboarding_winbooks_state__not_done
msgid "Not done"
msgstr "Nie wykonano"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "Please define the country on your company."
msgstr "Proszę określić kraj na swojej firmie."

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Stage Search"
msgstr "Szukaj etapu"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_res_company__account_onboarding_winbooks_state
msgid "State of the onboarding winbooks step"
msgstr "Stan etapu wprowadzenia winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid "Suspense Account Code"
msgstr "Numer konta przejściowego"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"The code for the Suspense Account you entered doesn't match any account"
msgstr "Podany kod konta przejściowego nie pasuje do żadnego konta"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"The following banks were used for multiple partners in Winbooks, which is not allowed in Odoo. The bank number has been only set on one of each group:\n"
"%s"
msgstr ""
"W Winbooks dla wielu partnerów użyto następujących banków, co jest niedopuszczalne w Odoo. Numer banku został ustawiony tylko na jednym z każdej grupy:\n"
"%s"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid ""
"This is the code of the account in which you want to put the counterpart of "
"unbalanced moves. This might be an account from your Winbooks data, or an "
"account that you created in Odoo before the import."
msgstr ""
"To jest numer konta, na którym chcesz umieścić duplikat niezbilansowanych "
"przesunięć. Może to być konto z danych Winbooks lub konto utworzone w Odoo "
"przed importem."

#. module: account_winbooks_import
#: model:ir.actions.act_window,name:account_winbooks_import.winbooks_import_action
#: model:ir.ui.menu,name:account_winbooks_import.menu_action_import_winbooks
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.onboarding_winbooks_step
msgid "Winbooks Import"
msgstr "Import Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_move_line__winbooks_matching_number
msgid "Winbooks Matching Number"
msgstr "Dopasowany numer Winbooks"

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid ""
"Years closed in Winbooks are likely to have incomplete data. The counter "
"part of incomplete entries will be set in a suspense account"
msgstr ""
"Lata zamknięte w Winbooks prawdopodobnie zawierają niepełne dane. Duplikaty "
"niekompletnych wpisów zostaną ustawione na koncie przejściowym"

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid "You should install a Fiscal Localization first."
msgstr "W pierwszej kolejności należy zainstalować lokalizację fiskalną."

#. module: account_winbooks_import
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
#, python-format
msgid ""
"dbfread library not found, Winbooks Import features disabled. If you plan to"
" use it, please install the dbfread library from "
"https://pypi.org/project/dbfread/"
msgstr ""
"Nie znaleziono biblioteki dbfread, funkcje importu Winbooks wyłączone. Jeśli"
" planujesz go użyć, zainstaluj bibliotekę dbfread z "
"https://pypi.org/project/dbfread/"
