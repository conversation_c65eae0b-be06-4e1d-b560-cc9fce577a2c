<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="appointment_booked_mail_template" model="mail.template">
            <field name="name">Appointment Booked</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="subject">Appointment Booked: {{ object.appointment_type_id.name }}</field>
            <field name="email_from">{{ (user.email_formatted or object.user_id.email_formatted) }}</field>
            <field name="body_html" type="html">
<div>
    <t t-set="colors" t-value="{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}" />
    <t t-set="recurrent" t-value="object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')" />
    <t t-set="mail_tz" t-value="object._get_mail_tz() or ctx.get('mail_tz')" />
    <p>
    Appointment booked for <t t-out="object.appointment_type_id.name or ''">Schedule a Demo</t> <t t-if="object.appointment_type_id.category != 'custom'"> with <t t-out="object.partner_id.name or ''">Brandon Freeman</t></t>.
    </p>
    <div style="text-align: center; padding: 16px 0px 16px 0px;">
        <a t-attf-href="/calendar/meeting/join?token={{ object.access_token }}"
            style="padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px">
            Join</a>
        <a t-attf-href="/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event"
            style="padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px">
            View</a>
    </div>
    <table border="0" cellpadding="0" cellspacing="0"><tr>
            <td width="130px;">
                <div style="border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;">
                    <t t-out="format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''">Wednesday</t>
                </div>
                <div style="font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;">
                    <t t-out="str(object.start.day) or ''">1</t>
                </div>
                <div style='font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;'>
                    <t t-out="format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''">January 2020</t>
                </div>
                <div style="border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;">
                    <t t-if="not object.allday">
                        <div>
                            <t t-out="format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''">8:00</t>
                        </div>
                        <t t-if="mail_tz">
                            <div style="font-size: 10px; font-weight: normal;">
                                (<t t-out="mail_tz"/>)
                            </div>
                        </t>
                    </t>
                </div>
            </td>
            <td width="20px;"/>
            <td style="padding-top: 5px;">
                <p><strong>Details of the event</strong></p>
                <ul>
                    <li t-if="object.location">Location: <t t-out="object.location or ''">Bruxelles</t>
                        (<a target="_blank" t-attf-href="http://maps.google.com/maps?oi=map&amp;q={{ object.location }}">View Map</a>)
                    </li>
                    <li t-if="recurrent">When: <t t-out="object.recurrence_id.name or ''">Every 1 Weeks, for 3 events</t></li>
                    <li t-if="not object.allday and object.duration">Duration: <t t-out="('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''">0H30</t></li>
                    <li>Attendees
                    <ul>
                        <li t-foreach="object.attendee_ids" t-as="attendee">
                            <div t-attf-style="display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};"> </div>
                            <t t-if="attendee.common_name">
                                <span style="margin-left:5px" t-out="attendee.common_name or ''">Mitchell Admin</span>
                            </t>
                            <t t-else="">
                                <span style="margin-left:5px">You</span>
                            </t>
                        </li>
                    </ul></li>
                    <li t-if="object.videocall_location">Join Video Call: <a t-attf-href="{{ object.videocall_location }}" target="_blank" t-out="object.videocall_location or ''">https://meet.jit.si/odoo-xyz</a></li>
                </ul>
                <t t-if="not is_html_empty(object.description)">
                    <li>Description of the event:
                    <t t-out="object.description"></t></li>
                </t>
            </td>
    </tr></table>
</div>
            </field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="appointment_canceled_mail_template" model="mail.template">
            <field name="name">Appointment Canceled</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="subject">Appointment Canceled: {{ object.appointment_type_id.name }}</field>
            <field name="email_from">{{ (user.email_formatted or object.user_id.email_formatted) }}</field>
            <field name="body_html" type="html">
<div>
    <t t-set="colors" t-value="{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}"/>
    <t t-set="recurrent" t-value="object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')"/>
    <t t-set="mail_tz" t-value="object._get_mail_tz() or ctx.get('mail_tz')" />
    <p>
    The appointment for <t t-out="object.appointment_type_id.name or ''">Schedule a Demo</t> <t t-if="object.appointment_type_id.category != 'custom'"> with <t t-out="object.partner_id.name or ''">Brandon Freeman</t></t> has been canceled.
    </p>
<table border="0" cellpadding="0" cellspacing="0"><tr>
            <td width="130px;">
                <div style="border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;">
                    <t t-out="format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''">Wednesday</t>
                </div>
                <div style="font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;">
                    <t t-out="str(object.start.day) or ''">1</t>
                </div>
                <div style='font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;'>
                    <t t-out="format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''">January 2020</t>
                </div>
                <div style="border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;">
                    <t t-if="not object.allday">
                        <div><t t-out="format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''">8:00</t></div>
                        <t t-if="mail_tz">
                            <div style="font-size: 10px; font-weight: normal;">
                                (<t t-out="mail_tz"/>)
                            </div>
                        </t>
                    </t>
                </div>
            </td>
            <td width="20px;"/>
            <td style="padding-top: 5px;">
                <del>
                    <p><strong>Details of the event</strong></p>
                    <ul>
                            <li t-if="object.location">Location: <t t-out="object.location or ''">Bruxelles</t>
                                (<a target="_blank" t-attf-href="http://maps.google.com/maps?oi=map&amp;q={{ object.location }}">View Map</a>)
                            </li>
                            <li t-if="recurrent">When: <t t-out="object.recurrence_id.name or ''">Every 1 Weeks, for 3 events</t></li>
                            <li t-if="not object.allday and object.duration">Duration: <t t-out="('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''">0H30</t></li>
                        <li>Attendees
                        <ul t-foreach="object.attendee_ids" t-as="attendee">
                            <li>
                                <div t-attf-style="display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};"> </div>
                                <t t-if="attendee.common_name">
                                    <span style="margin-left:5px" t-out="attendee.common_name or ''"></span>
                                </t>
                                <t t-else="">
                                    <span style="margin-left:5px">You</span>
                                </t>
                            </li>
                        </ul></li>
                        <li t-if="object.videocall_location">Join Video Call: <a t-att-href="object.videocall_location" target="_blank" t-out="object.videocall_location or ''">https://meet.jit.si/odoo-xyz</a></li>
                    </ul>
                    <t t-if="not is_html_empty(object.description)">
                        <li>Description of the event:
                        <t t-out="object.description"></t></li>
                    </t>
                </del>
            </td>
    </tr></table>
</div>
            </field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
