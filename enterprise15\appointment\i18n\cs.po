# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2021
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid " (copy)"
msgstr "(kopírovat)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_count
msgid "# Appointments"
msgstr "Počet schůzek"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "#{day['today_cls'] and 'Today' or ''}"
msgstr "#{day['today_cls'] and 'Dnes' or ''}"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "%s - Let's meet"
msgstr "%s - Pojďme se setkat"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "%s with %s"
msgstr "%s s %s"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "(timezone:"
msgstr "(časové pásmo:"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid ", All Day"
msgstr ", Celý den"

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_booked_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    Appointment booked for <t t-out=\"object.appointment_type_id.name or ''\">Schedule a Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Attendees\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">You</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.videocall_location\">Join Video Call: <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"/></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    Schůzka rezervovaná pro <t t-out=\"object.appointment_type_id.name or ''\">Plán demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> s <t t-out=\"object.partner_id.name or ''\">Brandonem Freemanem</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Připojte se</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Zobrazit</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Středa</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1.</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">ledna 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Podrobnosti o události</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Lokace: <t t-out=\"object.location or ''\">Brusel</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">Zobrazit mapu</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">Kdy: <t t-out=\"object.recurrence_id.name or ''\">Každý 1 týden na 3 akce</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Trvání: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Účastníci\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">Vy</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.videocall_location\">Připojte se k videohovoru: <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Popis události:\n"
"                    <t t-out=\"object.description\"/></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_canceled_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">Schedule a Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Attendees\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"/>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">Join Video Call: <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Description of the event:\n"
"                        <t t-out=\"object.description\"/></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    Schůzka pro <t t-out=\"object.appointment_type_id.name or ''\">Plán demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> s <t t-out=\"object.partner_id.name or ''\">Brandonem Freemanem</t></t> byla zrušena.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Středa</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1.</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">ledna 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Podrobnosti o události</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Lokace: <t t-out=\"object.location or ''\">Brusel</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">Zobrazit mapu</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">Kdy: <t t-out=\"object.recurrence_id.name or ''\">Každý 1 týden na 3 akce</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Trvání: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Účastníci\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"/>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">Vy</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">Připojte se k videohovoru: <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Popis události:\n"
"                        <t t-out=\"object.description\"/></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Přidat do Kalendáře Google"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Přidat do iCal / Outlook"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-times\"/>Cancel / Reschedule"
msgstr "<i class=\"fa fa-fw fa-times\"/>Zrušit / Přeplánovat"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"<i class=\"fa fa-pencil mr-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/><em>Add Custom Questions</em>"
msgstr ""
"<i class=\"fa fa-pencil mr-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/><em>Přidat vlastní otázky</em>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> days</span>"
msgstr "<span> dnů</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> hours before</span>"
msgstr "<span> hodin před</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> hours</span>"
msgstr "<span> hodin</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>and not after </span>"
msgstr "<span>a ne po </span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>at least </span>"
msgstr "<span>nejméně </span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>until </span>"
msgstr "<span>až do</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "<strong class=\"mr-2\">Duration:</strong>"
msgstr "<strong class=\"mr-2\">Trvání:</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "<strong class=\"mr-2\">Location:</strong>"
msgstr "<strong class=\"mr-2\">Umístění</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid ""
"<strong>Appointment canceled!</strong>\n"
"                            You can schedule another appointment from here."
msgstr ""
"<strong>Schůzka byla zrušena!</strong>\n"
"Odtud můžete naplánovat další schůzku."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                            The selected timeslot is not available anymore.\n"
"                            Someone has booked the same time slot a few\n"
"                            seconds before you."
msgstr ""
"<strong>Schůzka se nezdařila!</strong>\n"
"Vybraný časový úsek již není k dispozici. Někdo si rezervoval stejný čas pár sekund před vámi."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                            The selected timeslot is not available.\n"
"                            It appears you already have another meeting with us at that date."
msgstr ""
"<strong>Schůzka se nezdařila!</strong>\n"
"Vybraný časový úsek není k dispozici.\n"
"Zdá se, že k tomuto datu už máte další schůzku."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_select_timezone
msgid "<strong>Timezone</strong>"
msgstr "<strong>Časová zóna</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<strong>Your appointment has been successfully booked!</strong><br/>"
msgstr "<strong>Vaše schůzka byla úspěšně rezervována</strong><br/>"

#. module: appointment
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid ""
"A list of slots information is needed to create a custom appointment type"
msgstr "Seznam informací o slotech potřebuje vytvořit vlastní typ události"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "A text message reminder is sent to you before your appointment"
msgstr "Před schůzkou vám bude zaslána připomínka textovou zprávou"

#. module: appointment
#: code:addons/appointment/controllers/calendar.py:0
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid "Access Denied"
msgstr "Přístup zamítnut"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__access_token
msgid "Access Token"
msgstr "Přístupový token"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_needaction
msgid "Action Needed"
msgstr "Vyžaduje akci"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__active
msgid "Active"
msgstr "Aktivní"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Add a specific appointment."
msgstr "Přidejte konkrétní schůzku."

#. module: appointment
#: model:res.groups,name:appointment.group_calendar_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Vše"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_report
#: model:ir.ui.menu,name:appointment.menu_schedule_report_online
msgid "All Appointments"
msgstr "Všechny schůzky"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__allday
#, python-format
msgid "All day"
msgstr "Celý den"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Allow Cancelling"
msgstr "Povolit zrušení"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"An employee should be set on the actual user to create the appointment type"
msgstr ""
"Zaměstnanec by měl být nastaven na skutečného uživatele, aby vytvořil typ "
"události"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"An employee should be set on the current user to create the appointment type"
msgstr ""
"Zaměstnanec by měl být nastaven na aktuálního uživatele, aby vytvořil typ "
"události"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid "An unique type slot should have a start and end datetime"
msgstr "Slot jedinečného typu by měl mít počáteční a závěrečné datum"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__name
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_answer_view_form
msgid "Answer"
msgstr "Odpovědět"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Appointment"
msgstr "schůzka"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_booked
#: model:mail.message.subtype,name:appointment.mt_appointment_type_booked
#: model:mail.message.subtype,name:appointment.mt_calendar_event_booked
#: model:mail.template,name:appointment.appointment_booked_mail_template
msgid "Appointment Booked"
msgstr "Schůzka rezervována"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_booked_mail_template
msgid "Appointment Booked: {{ object.appointment_type_id.name }}"
msgstr "Schůzka rezervována:  {{ object.appointment_type_id.name }}"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_canceled
#: model:mail.message.subtype,name:appointment.mt_appointment_type_canceled
#: model:mail.message.subtype,name:appointment.mt_calendar_event_canceled
#: model:mail.template,name:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled"
msgstr "Schůzka zrušena"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled: {{ object.appointment_type_id.name }}"
msgstr "Schůzka zrušena:  {{ object.appointment_type_id.name }}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Appointment Confirmation"
msgstr "Potvrzení schůzky"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_duration
msgid "Appointment Duration"
msgstr "Trvání schůzky"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Appointment Introduction"
msgstr "Začátek schůzky"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_appointment_type_action_custom_and_work_hours
#: model:ir.ui.menu,name:appointment.menu_calendar_appointment_type_custom_and_work_hours
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_tree_invitation
msgid "Appointment Invitations"
msgstr "Pozvánky na schůzku"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointment Name"
msgstr "Název schůzky"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_type
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_select
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_tree
msgid "Appointment Type"
msgstr "Typ schůzky"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "Appointment Types"
msgstr "Typy schůzek"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Appointment:"
msgstr "Schůzka:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__appointment_type_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_menu_appointment
msgid "Appointments"
msgstr "Pracovní schůzky"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointments by"
msgstr "Schůzky podle"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__assign_method
msgid "Assignment Method"
msgstr "Způsob přiřazení"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid ""
"Atleast one slot duration from start to end is invalid: a slot should end "
"after start"
msgstr ""
"Alespoň jeden časový úsek od začátku do konce je neplatný: časový úsek by "
"měl skončit po začátku"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid ""
"Atleast one slot duration is not enough to create a slot with the duration "
"set in the appointment type"
msgstr ""
"Alespoň jeden časový úsek nestačí k vytvoření bloku s dobou trvání "
"nastavenou v typu události"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Attendees:"
msgstr "Účastníci:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__slot_ids
msgid "Availabilities"
msgstr "Dostupnosti"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Availability"
msgstr "Dostupnost"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__answer_ids
msgid "Available Answers"
msgstr "Dostupné odpovědi"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Available Employees"
msgstr "Dostupní zaměstnanci"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Basic blocks"
msgstr "Základní bloky"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: model:ir.ui.menu,name:appointment.calendar_appointment_type_menu_action
#, python-format
msgid "Calendar"
msgstr "Kalendář"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_share
msgid "Calendar Appointment Share Wizard"
msgstr "Průvodce sdílením kalendáře schůzek"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Informace o účastnících kalendáře"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_event
msgid "Calendar Event"
msgstr "Událost kalendáře"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__min_cancellation_hours
msgid "Cancel Before (hours)"
msgstr "Zrušit před (hodiny)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__category
msgid "Category"
msgstr "Kategorie"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__checkbox
msgid "Checkboxes (multiple answers)"
msgstr "Zaškrtávací políčka (více odpovědí)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__assign_method__chosen
msgid "Chosen by the Customer"
msgstr "Vybral zákazník"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Click in your calendar to pick meeting time proposals."
msgstr "Klikněte ve svém kalendáři a vyberte si návrhy času schůzky."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_share_view_form
msgid "Close"
msgstr "Zavřít"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action
msgid ""
"Configure your service opening hours and let attendees book time slots "
"online."
msgstr ""
"Nakonfigurujte si otevírací dobu služby a nechte účastníky rezervovat si "
"časové úseky online."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm Appointment <span class=\"fa fa-arrow-right\"/>"
msgstr "Potvrdit Schůzku <span class=\"fa fa-arrow-right\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm your details"
msgstr "Potvrdit své údaje"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_confirmation
msgid "Confirmation Message"
msgstr "Potvrzovací zpráva"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Confirmation<span class=\"chevron\"/>"
msgstr "Potvrzení <span class=\"chevron\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Confirmed"
msgstr "Potvrzeno"

#. module: appointment
#: model:ir.model,name:appointment.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Copied !"
msgstr "Zkopírováno !"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action
msgid "Create an Appointment Type"
msgstr "Vytvořit typ schůzky"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__custom
msgid "Custom"
msgstr "Vlastní"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Customer Preview"
msgstr "Náhled zákazníka"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#, python-format
msgid "Date"
msgstr "Datum"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Declined"
msgstr "Odmítnuto"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__slot_type
msgid ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."
msgstr ""
"Definuje typ slotu. Opakující se slot je výchozí typ, na který se používá\n"
"        typ schůzky, který se používá opakovaně v typu jako lékařská schůzka.\n"
"        Jednorázový typ se používá pouze tehdy, když uživatel vytvoří vlastní typ schůzky pro klienta\n"
"        definující neopakující se časový úsek (např. 10. dubna 2021 od 10 do 11 hodin) ze svého kalendáře."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Description:"
msgstr "Popis"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__allday
msgid ""
"Determine if the slot englobe the whole day, mainly used for unique slot "
"type"
msgstr ""
"Určete, zda slot zabírá celý den, používá se hlavně pro jedinečný typ slotu"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_insert_share_view_form
#, python-format
msgid "Discard"
msgstr "Zrušit"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: appointment
#: model:calendar.appointment.type,name:appointment.calendar_appointment_1
msgid "Doctor Appointment"
msgstr "Lékařská schůzka"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__select
msgid "Dropdown (one answer)"
msgstr "Rozbalovací nabídka (jedna odpověď)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__duration
msgid "Duration"
msgstr "Trvání"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Duration:"
msgstr "Doba trvání:"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "Email: %s"
msgstr "Email: %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__employee_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__employee_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Employees"
msgstr "Zaměstnanci"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__end_datetime
msgid "End datetime for unique slot type management"
msgstr "Závěrečné datum pro správu jedinečného typu slotu"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__end_hour
msgid "Ending Hour"
msgstr "Zavírací hodina"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Every"
msgstr "Každý"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_follower_ids
msgid "Followers"
msgstr "Sledující"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledující (partneři)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__5
msgid "Friday"
msgstr "Pátek"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "From"
msgstr "Od"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Get Share Link"
msgstr "Získejte odkaz ke sdílení"

#. module: appointment
#: model_terms:calendar.appointment.type,message_intro:appointment.calendar_appointment_0
msgid ""
"Get a <strong>customized demo</strong> and an <strong>analysis of your "
"needs</strong>."
msgstr ""
"Získejte <strong>vlastní demo</strong> a <strong>analýzu vašich "
"potřeb</strong>."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_share__suggested_employee_ids
msgid ""
"Get the employees link to the appointment type selected to apply a domain on"
" the employees that can be selected"
msgstr ""
"Získejte odkaz na zaměstnance na vybraný typ schůzky, abyste mohli použít "
"doménu na zaměstnance, které lze vybrat"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__assign_method
msgid ""
"How employees will be assigned to meetings customers book on your website."
msgstr ""
"Jak budou zaměstnanci přiděleni na schůzky, které si zákazníci na vašem webu"
" rezervují."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__id
msgid "ID"
msgstr "ID"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_needaction
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_unread
msgid "If checked, new messages require your attention."
msgstr "Pokud je zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_error
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud je zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Insert Appointment Link"
msgstr "Vložit odkaz na schůzku"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_insert_share_view_form
msgid "Insert link"
msgstr "Vložit odkaz"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_intro
msgid "Introduction Message"
msgstr "Úvodní zpráva"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_is_follower
msgid "Is Follower"
msgstr "Je sledující"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"It's too late to cancel online, please contact the attendees another way if "
"you really can't make it."
msgstr ""
"Je příliš pozdě na zrušení online, kontaktujte účastníky jiným způsobem, "
"pokud opravdu nemůžete schůzku uskutečnit."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__country_ids
msgid ""
"Keep empty to allow visitors from any country, otherwise you only allow "
"visitors from selected countries"
msgstr ""
"Chcete-li návštěvníkům z jakékoli země povolit vstup, ponechte pole prázdné,"
" jinak povolíte vstup pouze návštěvníkům z vybraných zemí"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__share_link
msgid "Link"
msgstr "Odkaz"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Link Copied in your clipboard !"
msgstr "Odkaz zkopírován do vaší schránky!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_share_view_form
msgid "Link Generator"
msgstr "Generátor odkazu"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__location
msgid "Location"
msgstr "Místo"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__location
msgid "Location of the appointments"
msgstr "Místo schůzek"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Location:"
msgstr "Lokace:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "Meeting with %s"
msgstr "Schůzka s %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zpráv"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Messages"
msgstr "Zprávy"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "Mobile: %s"
msgstr "Mobil: %s"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__1
msgid "Monday"
msgstr "Pondělí"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__text
msgid "Multi-line text"
msgstr "Víceřádkový text"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "My Appointments"
msgstr "Moje schůzky"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Jméno"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "No appointment found."
msgstr "Žádná schůzka nenalezena"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action_custom_and_work_hours
msgid "No custom appointment type has been created !"
msgstr "Nebyl vytvořen žádný vlastní typ schůzky!"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_reporting
msgid "No data yet!"
msgstr "Zatím žádná data!"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Nic"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Počet zpráv, které vyžadují akci"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_unread_counter
msgid "Number of unread messages"
msgstr "Počet nepřečtených zpráv"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__slot_type__unique
msgid "One Shot"
msgstr "Jednorázový"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_id
msgid "Online Appointment"
msgstr "Online schůzka"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_answer
msgid "Online Appointment : Answers"
msgstr "Online schůzka: Odpovědi"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_question
msgid "Online Appointment : Questions"
msgstr "Online schůzka: Otázky"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_slot
msgid "Online Appointment : Time Slot"
msgstr "Online schůzka: časový slot"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_appointment_type_action
#: model:ir.actions.act_window,name:appointment.calendar_event_action_reporting
#: model:ir.module.category,name:appointment.module_category_calendar
#: model:ir.ui.menu,name:appointment.appointment_type_menu
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search_inherit_appointment
msgid "Online Appointments"
msgstr "Online schůzky"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"Only one work hours appointment type is allowed for a specific employee."
msgstr ""
"Pro konkrétního zaměstnance je povolený pouze jeden typ schůzky s pracovním "
"časem."

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Past"
msgstr "Minulý"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__placeholder
msgid "Placeholder"
msgstr "Zástupce"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid "Please enter a valid hour between 0:00 and 24:00 for your slots."
msgstr "Zadejte platnou hodinu mezi 0:00 a 24:00 pro své sloty."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Please, select another date."
msgstr "Vyberte jiný den."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__suggested_employee_ids
msgid "Possible employees"
msgstr "Možní zaměstnanci"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__name
msgid "Question"
msgstr "Otázka"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__question_type
msgid "Question Type"
msgstr "Typ otázky"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__question_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__question_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Questions"
msgstr "Otázky"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__radio
msgid "Radio (one answer)"
msgstr "Výběr (jedna odpověď)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__assign_method__random
msgid "Random"
msgstr "Náhodný"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__slot_type__recurring
msgid "Recurring"
msgstr "Opakující se"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__reminder_ids
msgid "Reminders"
msgstr "Upozornění"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_schedule_report
msgid "Reporting"
msgstr "Přehledy"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__question_required
msgid "Required Answer"
msgstr "Požadovaná odpověď"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Responsible"
msgstr "Odpovědný"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__country_ids
msgid "Restrict Countries"
msgstr "Omezit země"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "SCHEDULED"
msgstr "PLÁNOVÁNO"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: appointment
#: model:calendar.alarm,name:appointment.calendar_alarm_data_1h_sms
msgid "SMS Text Message - 1 Hours"
msgstr "SMS textová zpráva - 1 hodina"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__6
msgid "Saturday"
msgstr "Sobota"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Schedule Appointment"
msgstr "Plánování schůzky"

#. module: appointment
#: model:calendar.appointment.type,name:appointment.calendar_appointment_0
msgid "Schedule a Demo"
msgstr "Naplánovat demo"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Schedule an Appointment"
msgstr "Naplánovat schůzku"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Schedule an appointment."
msgstr "Naplánovat schůzku"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_reporting
msgid "Schedule appointments to get statistics"
msgstr "Naplánujte si schůzky a získejte statistiky"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__min_schedule_hours
msgid "Schedule before (hours)"
msgstr "Plán před (hodiny)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__max_schedule_days
msgid "Schedule not after (days)"
msgstr "Plánování ne po... (dnech)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Scheduling"
msgstr "Plánování"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Hledat ve všech"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "Hledat v popisu"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Name"
msgstr "Hledat v názvu"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Responsible"
msgstr "Hledat v odpovědných"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__appointment_type_count
msgid "Selected Appointments Count"
msgstr "Počet vybraných schůzek"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__sequence
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "Share"
msgstr "Sdílet"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Share Availabilities"
msgstr "Sdílet dostupnost"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "Share Link"
msgstr "Sdílet odkaz"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__char
msgid "Single line text"
msgstr "Jednořádkový text"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__slot_type
msgid "Slot type"
msgstr "Typ slotu"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__start_datetime
msgid "Start datetime for unique slot type management"
msgstr "Počáteční čas pro správu jedinečného typu slotu"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__start_hour
msgid "Starting Hour"
msgstr "Počáteční hodina"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__7
msgid "Sunday"
msgstr "Neděle"

#. module: appointment
#: model:calendar.appointment.question,name:appointment.calendar_appointment_1_question_1
msgid "Symptoms"
msgstr "Symptomy"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_share__employee_ids
msgid ""
"The employees that will be display/filter for the user to make its "
"appointment"
msgstr ""
"Zaměstnanci, kteří budou zobrazeni/filtrováni pro uživatele, aby si sjednal "
"schůzku"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "The field '%s' does not exist in the targeted model"
msgstr "Pole '%s' v cílovém modelu neexistuje"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "There is no appointment linked to your account."
msgstr "S vaším účtem není spojena žádná schůzka."

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"This category of appointment type should only have one employee but got %s "
"employees"
msgstr ""
"Tento typ kategorie schůzky by měl mít pouze jednoho zaměstnance, ale má %s "
"zaměstnanců"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "This is a preview of the customer appointment form."
msgstr "Toto je náhled formuláře na schůzku se zákazníkem."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__4
msgid "Thursday"
msgstr "Čtvrtek"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "Time displayed in"
msgstr "Čas zobrazený v"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Time<span class=\"chevron\"/>"
msgstr "Ćas <span class=\"chevron\"/>"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_tz
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "Timezone"
msgstr "Časové pásmo"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__appointment_tz
msgid "Timezone where appointment take place"
msgstr "Časové pásmo, kde se uskuteční schůzka"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "To"
msgstr "Do"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Total:"
msgstr "Celkem:"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__2
msgid "Tuesday"
msgstr "Úterý"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_unread
msgid "Unread Messages"
msgstr "Nepřečtené zprávy"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo nepřečtených zpráv"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Until (max)"
msgstr "Do (max.)"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Upcoming"
msgstr "Nadcházející"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "Pomocí horního tlačítka '<b>+ Nový</b>' vytvořte typ schůzky."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__category
msgid ""
"Used to define this appointment type's category.\n"
"        Can be one of:\n"
"            - Website: the default category, the people can access and shedule the appointment with employees from the website\n"
"            - Custom: the employee will create and share to an user a custom appointment type with hand-picked time slots\n"
"            - Work Hours: a special type of appointment type that is used by one employee and which takes the working hours of this\n"
"                employee as availabilities. This one uses recurring slot that englobe the entire week to display all possible slots\n"
"                based on its working hours and availabilities"
msgstr ""
"Používá se k definování kategorie tohoto typu schůzky.\n"
"        Může být jedním z:\n"
"            - Webová stránka: výchozí kategorie, lidé mají přístup a naplánují si schůzku se zaměstnanci z webové stránky\n"
"            - Vlastní: zaměstnanec vytvoří a sdílí s uživatelem vlastní typ schůzky s ručně vybranými časovými úseky\n"
"            - Pracovní doba: speciální typ typu schůzky, který používá jeden zaměstnanec a který bere jako disponibilní\n"
"                pracovní dobu. Tento používá opakující se slot, který pokrývá celý týden, aby zobrazil všechny možné sloty\n"
"                na základě jeho pracovní doby a dostupnosti"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "View Availabilities <span class=\"fa fa-arrow-right\"/>"
msgstr "Zobrazit možnosti <span class=\"fa fa-arrow-right\"/>"

#. module: appointment
#: model_terms:calendar.appointment.type,message_confirmation:appointment.calendar_appointment_0
msgid ""
"We thank you for your interest in our products!<br>\n"
"               Please make sure to arrive <strong>10 minutes</strong> before your appointment."
msgstr ""
"Děkujeme za váš zájem o naše produkty!<br>\n"
"Ujistěte se, že dorazíte <strong>10 minut</strong> před vaší schůzkou."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__website
msgid "Website"
msgstr "Webstránka"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__website_message_ids
msgid "Website Messages"
msgstr "Zprávy webstránky"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__website_message_ids
msgid "Website communication history"
msgstr "Historie komunikace webstránky"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__3
msgid "Wednesday"
msgstr "Středa"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__weekday
msgid "Week Day"
msgstr "Den týdne"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "When:"
msgstr "Když:"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.employee_select
msgid "With"
msgstr "S"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__work_hours
#, python-format
msgid "Work Hours"
msgstr "Pracovní hodiny"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/calendar_renderer.js:0
#: code:addons/appointment/static/src/js/calendar_renderer.js:0
#, python-format
msgid "You can not create a slot in the past."
msgstr "Nemůžete vytvořit slot v minulosti"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Email *"
msgstr "Váš Email *"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Name *"
msgstr "Vaše jméno *"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Phone *"
msgstr "Váš telefon *"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment is in less than"
msgstr "Vaše schůzka je kratší než"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "e.g. Schedule a demo"
msgstr "např. Naplánujte si demo"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hour"
msgstr "hodina"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hours from now!"
msgstr "hodiny od teď!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "on"
msgstr "na"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "timezone"
msgstr "časové pásmo"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "with"
msgstr "s "
