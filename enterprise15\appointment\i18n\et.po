# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>ru <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <armaged<PERSON><PERSON>@hot.ee>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# R<PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Patrick-<PERSON> Kiudorv, 2022
# Anna, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Jana Jaakson, 2023\n"
"Language-Team: Estonian (https://www.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid " (copy)"
msgstr " (koopia)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_count
msgid "# Appointments"
msgstr "# Kohtumised"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "#{day['today_cls'] and 'Today' or ''}"
msgstr "#{day['today_cls'] and 'Today' or ''}"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "%s - Let's meet"
msgstr "%s - Saame kokku"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "%s with %s"
msgstr "%s koos%s"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "(timezone:"
msgstr "(ajavöönd:"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid ", All Day"
msgstr ", kogu päev"

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_booked_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    Appointment booked for <t t-out=\"object.appointment_type_id.name or ''\">Schedule a Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Attendees\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">You</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.videocall_location\">Join Video Call: <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"/></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_canceled_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">Schedule a Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Attendees\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"/>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">Join Video Call: <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Description of the event:\n"
"                        <t t-out=\"object.description\"/></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Lisage Google kalendrisse"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Lisage iCal/Outlooki"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-times\"/>Cancel / Reschedule"
msgstr "<i class=\"fa fa-fw fa-times\"/>Tühistage/planeerige ümber"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"<i class=\"fa fa-pencil mr-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/><em>Add Custom Questions</em>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> days</span>"
msgstr "<span>päeva</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> hours before</span>"
msgstr "<span>tundi enne</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> hours</span>"
msgstr "<span> tunnid</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>and not after </span>"
msgstr "<span>ja mitte peale</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>at least </span>"
msgstr "<span>vähemalt</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>until </span>"
msgstr "<span>kuni</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "<strong class=\"mr-2\">Duration:</strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "<strong class=\"mr-2\">Location:</strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid ""
"<strong>Appointment canceled!</strong>\n"
"                            You can schedule another appointment from here."
msgstr ""
"<strong>Kohtumine tühistatud!</strong>\n"
"Te saate teise kohtumise aja valida siit."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                            The selected timeslot is not available anymore.\n"
"                            Someone has booked the same time slot a few\n"
"                            seconds before you."
msgstr ""
"<strong>Kohtumise broneerimine ebaõnnestus</strong>\n"
"See ajavahemik ei ole enam saadav.\n"
"Keegi on sama saadavuse broneerinud paar\n"
"sekundit enne teid."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                            The selected timeslot is not available.\n"
"                            It appears you already have another meeting with us at that date."
msgstr ""
"<strong>Kohtumise broneerimine ebaõnnestus</strong>\n"
"See ajavahemik ei ole enam saadav.\n"
"Tundub, et teil on juba samal kuupäeval meiega teine kohtumine."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_select_timezone
msgid "<strong>Timezone</strong>"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<strong>Your appointment has been successfully booked!</strong><br/>"
msgstr ""

#. module: appointment
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid ""
"A list of slots information is needed to create a custom appointment type"
msgstr ""
"Vahemike informatsiooni list on vajalik, et luua kohandatud kohtumise tüüp"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "A text message reminder is sent to you before your appointment"
msgstr "Enne teie kohtumist saadetakse teile meeldetuletus sõnumiga"

#. module: appointment
#: code:addons/appointment/controllers/calendar.py:0
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid "Access Denied"
msgstr "Juurdepääs keelatud"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__access_token
msgid "Access Token"
msgstr "Juurdepääsu võti"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__active
msgid "Active"
msgstr "Tegev"

#. module: appointment
#: model:res.groups,name:appointment.group_calendar_manager
msgid "Administrator"
msgstr "Administraator"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Kõik"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_report
#: model:ir.ui.menu,name:appointment.menu_schedule_report_online
msgid "All Appointments"
msgstr "Kõik kohtumised"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__allday
#, python-format
msgid "All day"
msgstr "Kogu päev"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Allow Cancelling"
msgstr "Luba tühistamine"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"An employee should be set on the actual user to create the appointment type"
msgstr ""

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"An employee should be set on the current user to create the appointment type"
msgstr ""

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid "An unique type slot should have a start and end datetime"
msgstr "Unikaalsel vahemiku tüübil peab olema alguse ning lõpu kuupäev/aeg"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__name
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_answer_view_form
msgid "Answer"
msgstr "Vastus"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_booked
#: model:mail.message.subtype,name:appointment.mt_appointment_type_booked
#: model:mail.message.subtype,name:appointment.mt_calendar_event_booked
#: model:mail.template,name:appointment.appointment_booked_mail_template
msgid "Appointment Booked"
msgstr "Kohtumine broneeritud"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_booked_mail_template
msgid "Appointment Booked: {{ object.appointment_type_id.name }}"
msgstr "Kohtumine broneeritud: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_canceled
#: model:mail.message.subtype,name:appointment.mt_appointment_type_canceled
#: model:mail.message.subtype,name:appointment.mt_calendar_event_canceled
#: model:mail.template,name:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled"
msgstr "Kohtumine tühistatud"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled: {{ object.appointment_type_id.name }}"
msgstr "Kohtumine tühistatud: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Appointment Confirmation"
msgstr "Kohtumise kinnitus"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_duration
msgid "Appointment Duration"
msgstr "Kohtumise kestus"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Appointment Introduction"
msgstr "Kohtumise lühitutvustus"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_appointment_type_action_custom_and_work_hours
#: model:ir.ui.menu,name:appointment.menu_calendar_appointment_type_custom_and_work_hours
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_tree_invitation
msgid "Appointment Invitations"
msgstr "Kohtumise kutsed"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointment Name"
msgstr "Kohtumise nimi"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_type
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_select
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_tree
msgid "Appointment Type"
msgstr "Kohtumise tüüp"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "Appointment Types"
msgstr "Kohtumise tüübid"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Appointment:"
msgstr "Kohtumine:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__appointment_type_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_menu_appointment
msgid "Appointments"
msgstr "Kohtumised"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointments by"
msgstr "Kohtumised"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__assign_method
msgid "Assignment Method"
msgstr "Määramise meetod"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid ""
"Atleast one slot duration from start to end is invalid: a slot should end "
"after start"
msgstr ""

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid ""
"Atleast one slot duration is not enough to create a slot with the duration "
"set in the appointment type"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Attendees:"
msgstr "Osalejad:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__slot_ids
msgid "Availabilities"
msgstr "Saadavused"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Availability"
msgstr "Saadavus"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__answer_ids
msgid "Available Answers"
msgstr "Saadaval vastused"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Available Employees"
msgstr "Saadaval töötajad"

#. module: appointment
#: model:ir.ui.menu,name:appointment.calendar_appointment_type_menu_action
msgid "Calendar"
msgstr "Kalender"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_share
msgid "Calendar Appointment Share Wizard"
msgstr ""

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Kalendris osaleja informatsioon"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_event
msgid "Calendar Event"
msgstr "Kalendrisündmus"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__min_cancellation_hours
msgid "Cancel Before (hours)"
msgstr "Tühistage enne (tundi)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__category
msgid "Category"
msgstr "Kategooria"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__checkbox
msgid "Checkboxes (multiple answers)"
msgstr "Vastusevariandid (mitu vastust)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__assign_method__chosen
msgid "Chosen by the Customer"
msgstr "Kliendi poolt valitud"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Click in your calendar to pick meeting time proposals."
msgstr "Kliki oma kalendris, et valida kohtumise aja ettepanekuid."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_share_view_form
msgid "Close"
msgstr "Sulge"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action
msgid ""
"Configure your service opening hours and let attendees book time slots "
"online."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm Appointment <span class=\"fa fa-arrow-right\"/>"
msgstr "Kinnitage kohtumine <span class=\"fa fa-arrow-right\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm your details"
msgstr "Kinnitage oma detailid"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_confirmation
msgid "Confirmation Message"
msgstr "Kinnitamise sõnum"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Confirmation<span class=\"chevron\"/>"
msgstr "Kinnitus<span class=\"chevron\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Confirmed"
msgstr "Kinnitatud"

#. module: appointment
#: model:ir.model,name:appointment.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Copied !"
msgstr "Kopeeritud !"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action
msgid "Create an Appointment Type"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__custom
msgid "Custom"
msgstr "Kohandatud"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Customer Preview"
msgstr "Kliendi eelvaade"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#, python-format
msgid "Date"
msgstr "Date"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Declined"
msgstr "Tagasi lükatud"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__slot_type
msgid ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."
msgstr ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Description:"
msgstr "Kirjeldus:"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__allday
msgid ""
"Determine if the slot englobe the whole day, mainly used for unique slot "
"type"
msgstr ""
"Määra kindlaks, kas vahemik ümbritseb kogu päeva, peamiselt kasutatakse "
"unikaalse vahemiku tüübi korral"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_insert_share_view_form
#, python-format
msgid "Discard"
msgstr "Loobu"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: appointment
#: model:calendar.appointment.type,name:appointment.calendar_appointment_1
msgid "Doctor Appointment"
msgstr ""

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__select
msgid "Dropdown (one answer)"
msgstr "Rippmenuu (üks vastus)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__duration
msgid "Duration"
msgstr "Kestus"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Duration:"
msgstr "Kestus:"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "Email: %s"
msgstr "E-post: %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__employee_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__employee_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Employees"
msgstr "Töötajad"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__end_datetime
msgid "End datetime for unique slot type management"
msgstr "Lõpu kuupäev/aeg unikaalse vahemiku tüübi haldamiseks"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__end_hour
msgid "Ending Hour"
msgstr "Lõputund"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Every"
msgstr "Iga"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad (partnerid)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__5
msgid "Friday"
msgstr "Reede"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "From"
msgstr "Kellelt?"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Get Share Link"
msgstr "Link jagamiseks"

#. module: appointment
#: model_terms:calendar.appointment.type,message_intro:appointment.calendar_appointment_0
msgid ""
"Get a <strong>customized demo</strong> and an <strong>analysis of your "
"needs</strong>."
msgstr ""
"Saage <strong>kohandatud demo</strong> ja <strong>oma vajaduste "
"analüüs</strong>."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_share__suggested_employee_ids
msgid ""
"Get the employees link to the appointment type selected to apply a domain on"
" the employees that can be selected"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__assign_method
msgid ""
"How employees will be assigned to meetings customers book on your website."
msgstr ""
"Kuidas määratakse töötajad kohtumistele, mille kliendid on broneerinud teie "
"veebilehelt."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__id
msgid "ID"
msgstr "ID"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_needaction
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_unread
msgid "If checked, new messages require your attention."
msgstr "Kui märgitud, siis uued sõnumid nõuavad Su tähelepanu."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_error
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningatel sõnumitel saatmiserror."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_insert_share_view_form
msgid "Insert link"
msgstr "Lisa link"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_intro
msgid "Introduction Message"
msgstr "Tutvustav sõnum"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_is_follower
msgid "Is Follower"
msgstr "Jälgija"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"It's too late to cancel online, please contact the attendees another way if "
"you really can't make it."
msgstr ""
"Kahjuks on liiga hilja tühistada veebilehe vahendusel, palun võtke "
"osalejatega ühendust teisel moel kui te ei jõua õigeks ajaks."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__country_ids
msgid ""
"Keep empty to allow visitors from any country, otherwise you only allow "
"visitors from selected countries"
msgstr ""
"Jätke tühjaks, et lubada külastajaid igast riigist, muidu te lubate "
"külastajaid ainult valitud riikidest"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__share_link
msgid "Link"
msgstr "Link"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Link Copied in your clipboard !"
msgstr "Link kopeeritud lõikelauale !"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_share_view_form
msgid "Link Generator"
msgstr "Lingi generaator"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__location
msgid "Location"
msgstr "Asukoht"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__location
msgid "Location of the appointments"
msgstr "Kohtumiste asukoht"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Location:"
msgstr "Asukoht:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_main_attachment_id
msgid "Main Attachment"
msgstr "Peamine manus"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "Meeting with %s"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi edastamise veateade"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Messages"
msgstr "Sõnumid"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "Mobile: %s"
msgstr "Mobiil: %s"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__1
msgid "Monday"
msgstr "Esmaspäev"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__text
msgid "Multi-line text"
msgstr "MItmerealine tekst"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "My Appointments"
msgstr "Minu Kohtumised"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Nimi"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "No appointment found."
msgstr "Kohtumist ei leitud."

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action_custom_and_work_hours
msgid "No custom appointment type has been created !"
msgstr "Kohandatud kutsumise tüüp pole loodud !"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_reporting
msgid "No data yet!"
msgstr "Andmed puuduvad!"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "Pole"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimingute arv"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_error_counter
msgid "Number of errors"
msgstr "Veateadete arv"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Tegutsemist nõudvate sõnumite arv"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kohaletoimetamise veateatega sõnumite arv"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_unread_counter
msgid "Number of unread messages"
msgstr "Lugemata sõnumite arv"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__slot_type__unique
msgid "One Shot"
msgstr "One Shot"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_id
msgid "Online Appointment"
msgstr "veebi kohtumised"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_answer
msgid "Online Appointment : Answers"
msgstr "Online kohtumine : Vastused"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_question
msgid "Online Appointment : Questions"
msgstr "Online kohtumine : Küsimused"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_slot
msgid "Online Appointment : Time Slot"
msgstr "Online kohtumine : ajavahemik"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_appointment_type_action
#: model:ir.actions.act_window,name:appointment.calendar_event_action_reporting
#: model:ir.module.category,name:appointment.module_category_calendar
#: model:ir.ui.menu,name:appointment.appointment_type_menu
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search_inherit_appointment
msgid "Online Appointments"
msgstr "Online kohtumised"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"Only one work hours appointment type is allowed for a specific employee."
msgstr "Ainult üks tööajaga kohtumise tüüp on lubatud konkreetsele töötajale."

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Past"
msgstr "Varasem"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__placeholder
msgid "Placeholder"
msgstr "Kohatäitja"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid "Please enter a valid hour between 0:00 and 24:00 for your slots."
msgstr "Palun valige enda vahemikesse kehtiv aeg 0:00 ja 24:00 vahel."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Please, select another date."
msgstr "Palun valige teine kuupäev."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__suggested_employee_ids
msgid "Possible employees"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__name
msgid "Question"
msgstr "Küsimus"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__question_type
msgid "Question Type"
msgstr "Küsimuste tüüp"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__question_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__question_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Questions"
msgstr "Küsimused"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__radio
msgid "Radio (one answer)"
msgstr "Ringidega valikud (üks vaste)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__assign_method__random
msgid "Random"
msgstr "Juhuvalik"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__slot_type__recurring
msgid "Recurring"
msgstr "Korduv"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__reminder_ids
msgid "Reminders"
msgstr "Meeldetuletused"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_schedule_report
msgid "Reporting"
msgstr "Aruandlus"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__question_required
msgid "Required Answer"
msgstr "Nõutud vastus"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Responsible"
msgstr "Vastutaja"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__country_ids
msgid "Restrict Countries"
msgstr "Piira riike"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "SCHEDULED"
msgstr "PLANEERITUD"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise veateade"

#. module: appointment
#: model:calendar.alarm,name:appointment.calendar_alarm_data_1h_sms
msgid "SMS Text Message - 1 Hours"
msgstr "SMS tekstsõnum - 1 tund"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__6
msgid "Saturday"
msgstr "Laupäev"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Schedule Appointment"
msgstr "Planeerige kohtumine"

#. module: appointment
#: model:calendar.appointment.type,name:appointment.calendar_appointment_0
msgid "Schedule a Demo"
msgstr "Planeerige demo"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Schedule an Appointment"
msgstr "Planeerige kohtumine"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_reporting
msgid "Schedule appointments to get statistics"
msgstr "Planeerige kohtumine statistika saamiseks"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__min_schedule_hours
msgid "Schedule before (hours)"
msgstr "Planeerige enne (tundi)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__max_schedule_days
msgid "Schedule not after (days)"
msgstr "Ärge planeerige peale (päeva)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Scheduling"
msgstr "Planeerimine"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "Otsi kõige hulgast"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "Otsi kirjeldusest"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Name"
msgstr "Otsi Nimes"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Responsible"
msgstr "Otsi Vastutav"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__appointment_type_count
msgid "Selected Appointments Count"
msgstr "Valitud Kohtumiste Loendus"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__sequence
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__sequence
msgid "Sequence"
msgstr "Järjestus"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "Share"
msgstr "Jaga"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Share Availabilities"
msgstr "Jaga saadavusi"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "Share Link"
msgstr "Jaga linki"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__char
msgid "Single line text"
msgstr "Üherealine tekst"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__slot_type
msgid "Slot type"
msgstr "Pesa tüüp"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__start_datetime
msgid "Start datetime for unique slot type management"
msgstr "Algus kuupäev/aeg unikaalse vahemiku tüübi haldamiseks"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__start_hour
msgid "Starting Hour"
msgstr "Alguskellaaeg"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__7
msgid "Sunday"
msgstr "Pühapäev"

#. module: appointment
#: model:calendar.appointment.question,name:appointment.calendar_appointment_1_question_1
msgid "Symptoms"
msgstr "Sümptomid"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_share__employee_ids
msgid ""
"The employees that will be display/filter for the user to make its "
"appointment"
msgstr ""

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "The field '%s' does not exist in the targeted model"
msgstr "Mudelis ei eksisteeri '%s' välja"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "There is no appointment linked to your account."
msgstr "Sinu kontoga pole lingitud ühtegi kohtumist."

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"This category of appointment type should only have one employee but got %s "
"employees"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "This is a preview of the customer appointment form."
msgstr "See on kliendi kohtumise vormi eelvaade."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__4
msgid "Thursday"
msgstr "Neljapäev"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "Time displayed in"
msgstr "Aeg kuvatud"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Time<span class=\"chevron\"/>"
msgstr "Aeg<span class=\"chevron\"/>"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_tz
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "Timezone"
msgstr "Ajavöönd"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__appointment_tz
msgid "Timezone where appointment take place"
msgstr "Kohtumise ajavöönd"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "To"
msgstr "Kuhu"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Total:"
msgstr "Kokku:"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__2
msgid "Tuesday"
msgstr "Teisipäev"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_unread
msgid "Unread Messages"
msgstr "Lugemata sõnumid"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lugemata sõnumite loendur"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Until (max)"
msgstr "Kuni (max)"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Upcoming"
msgstr "Tulemas"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "Kasutage ülemist nuppu '<b>+ Uus</b>' kohtumise tüübi loomiseks. "

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__category
msgid ""
"Used to define this appointment type's category.\n"
"        Can be one of:\n"
"            - Website: the default category, the people can access and shedule the appointment with employees from the website\n"
"            - Custom: the employee will create and share to an user a custom appointment type with hand-picked time slots\n"
"            - Work Hours: a special type of appointment type that is used by one employee and which takes the working hours of this\n"
"                employee as availabilities. This one uses recurring slot that englobe the entire week to display all possible slots\n"
"                based on its working hours and availabilities"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "View Availabilities <span class=\"fa fa-arrow-right\"/>"
msgstr "Vaata saadavusi <span class=\"fa fa-arrow-right\"/>"

#. module: appointment
#: model_terms:calendar.appointment.type,message_confirmation:appointment.calendar_appointment_0
msgid ""
"We thank you for your interest in our products!<br>\n"
"               Please make sure to arrive <strong>10 minutes</strong> before your appointment."
msgstr ""
"Me täname teid huvi eest meie toodete osas!<br>\n"
"Palun veenduge, et te saabute <strong>10 minutit</strong> enne oma kohtumist."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__website
msgid "Website"
msgstr "Veebileht"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__3
msgid "Wednesday"
msgstr "Kolmapäev"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__weekday
msgid "Week Day"
msgstr "Nädalapäev"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "When:"
msgstr "MIllal:"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.employee_select
msgid "With"
msgstr "koos"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__work_hours
#, python-format
msgid "Work Hours"
msgstr "Tööaeg"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/calendar_renderer.js:0
#: code:addons/appointment/static/src/js/calendar_renderer.js:0
#, python-format
msgid "You can not create a slot in the past."
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Email *"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Name *"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Phone *"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment is in less than"
msgstr "Teie kohtumine on vähem kui"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "e.g. Schedule a demo"
msgstr "nt planeeri demo"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hour"
msgstr "tund"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hours from now!"
msgstr "tunni pärast!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "on"
msgstr " "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "timezone"
msgstr "ajavöönd"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "with"
msgstr "koos"
