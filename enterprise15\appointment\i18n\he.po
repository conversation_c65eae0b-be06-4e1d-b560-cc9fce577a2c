# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# דודי מלכ<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# david <PERSON>, 2021
# <PERSON><PERSON>, 2022
# NoaFarkash, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Ha <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# yael terner, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: yael terner, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid " (copy)"
msgstr " (העתק)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_count
msgid "# Appointments"
msgstr "מס' פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "#{day['today_cls'] and 'Today' or ''}"
msgstr "#{day['today_cls'] and 'היום' or ''}"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "%s - Let's meet"
msgstr "%s - בואו ניפגש"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "%s with %s"
msgstr "%s עם %s"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "(timezone:"
msgstr "(אזור זמן:"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid ", All Day"
msgstr ", כל היום"

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_booked_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    Appointment booked for <t t-out=\"object.appointment_type_id.name or ''\">Schedule a Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Join</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>Details of the event</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>Attendees\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">You</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.videocall_location\">Join Video Call: <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.description\"/></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    פגישה נקבעה ל <t t-out=\"object.appointment_type_id.name or ''\">תצוגת תכלית</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> עם <t t-out=\"object.partner_id.name or ''\">בנצי פרידמן</t></t>.\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/join?token={{ object.access_token }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            הצטרפות</a>\n"
"        <a t-attf-href=\"/web?#id={{ object.id }}&amp;view_type=form&amp;model=calendar.event\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            צפיה</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">יום רביעי</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">ינואר 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div>\n"
"                            <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t>\n"
"                        </div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <p><strong>פרטי האירוע</strong></p>\n"
"                <ul>\n"
"                    <li t-if=\"object.location\">מיקום: <t t-out=\"object.location or ''\">בריסל</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">צפיה במפה</a>)\n"
"                    </li>\n"
"                    <li t-if=\"recurrent\">מתי: <t t-out=\"object.recurrence_id.name or ''\">כל שבוע, למשך 3 אירועים</t></li>\n"
"                    <li t-if=\"not object.allday and object.duration\">משך: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                    <li>משתתפים\n"
"                    <ul>\n"
"                        <li t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                            <t t-if=\"attendee.common_name\">\n"
"                                <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">מיכל אדמין</span>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <span style=\"margin-left:5px\">את/ה</span>\n"
"                            </t>\n"
"                        </li>\n"
"                    </ul></li>\n"
"                    <li t-if=\"object.videocall_location\">הצטרפות לשיחת וידאו: <a t-attf-href=\"{{ object.videocall_location }}\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </ul>\n"
"                <t t-if=\"not is_html_empty(object.description)\">\n"
"                    <li>תיאור האירוע:\n"
"                    <t t-out=\"object.description\"/></li>\n"
"                </t>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model:mail.template,body_html:appointment.appointment_canceled_mail_template
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    The appointment for <t t-out=\"object.appointment_type_id.name or ''\">Schedule a Demo</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t></t> has been canceled.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">Wednesday</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\">January 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>Details of the event</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">Location: <t t-out=\"object.location or ''\">Bruxelles</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">View Map</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">Duration: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>Attendees\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"/>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">You</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">Join Video Call: <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>Description of the event:\n"
"                        <t t-out=\"object.description\"/></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <p>\n"
"    הפגישה עבור <t t-out=\"object.appointment_type_id.name or ''\">תצוגת תכלית</t> <t t-if=\"object.appointment_type_id.category != 'custom'\"> עם <t t-out=\"object.partner_id.name or ''\">בנצי פרידמן</t></t> בוטלה.\n"
"    </p>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"            <td width=\"130px;\">\n"
"                <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or ''\">יום רביעי</t>\n"
"                </div>\n"
"                <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                    <t t-out=\"str(object.start.day) or ''\">1</t>\n"
"                </div>\n"
"                <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                    <t t-out=\"format_datetime(dt=object.start, tz=mail_tz, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or ''\"> ינואר 2020</t>\n"
"                </div>\n"
"                <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                    <t t-if=\"not object.allday\">\n"
"                        <div><t t-out=\"format_time(time=object.start, tz=mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or ''\">8:00</t></div>\n"
"                        <t t-if=\"mail_tz\">\n"
"                            <div style=\"font-size: 10px; font-weight: normal;\">\n"
"                                (<t t-out=\"mail_tz\"/>)\n"
"                            </div>\n"
"                        </t>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td width=\"20px;\"/>\n"
"            <td style=\"padding-top: 5px;\">\n"
"                <del>\n"
"                    <p><strong>פרטים על האירוע</strong></p>\n"
"                    <ul>\n"
"                            <li t-if=\"object.location\">מיקום: <t t-out=\"object.location or ''\">בריסל</t>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.location }}\">צפיה במפה</a>)\n"
"                            </li>\n"
"                            <li t-if=\"recurrent\">מתי: <t t-out=\"object.recurrence_id.name or ''\">כל שבוע, למשך 3 מפגשים</t></li>\n"
"                            <li t-if=\"not object.allday and object.duration\">משך: <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60)) or ''\">0H30</t></li>\n"
"                        <li>משתתפים\n"
"                        <ul t-foreach=\"object.attendee_ids\" t-as=\"attendee\">\n"
"                            <li>\n"
"                                <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                                <t t-if=\"attendee.common_name\">\n"
"                                    <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\"/>\n"
"                                </t>\n"
"                                <t t-else=\"\">\n"
"                                    <span style=\"margin-left:5px\">את/ה</span>\n"
"                                </t>\n"
"                            </li>\n"
"                        </ul></li>\n"
"                        <li t-if=\"object.videocall_location\">הצטרפות לשיחת וידאו: <a t-att-href=\"object.videocall_location\" target=\"_blank\" t-out=\"object.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                    </ul>\n"
"                    <t t-if=\"not is_html_empty(object.description)\">\n"
"                        <li>תיאור האירוע:\n"
"                        <t t-out=\"object.description\"/></li>\n"
"                    </t>\n"
"                </del>\n"
"            </td>\n"
"    </tr></table>\n"
"</div>\n"
"            "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>הוסף ללוח שנה גוגל"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>הוסף ל- iCal/Outlook"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<i class=\"fa fa-fw fa-times\"/>Cancel / Reschedule"
msgstr "<i class=\"fa fa-fw fa-times\"/>בטל / תזמן מחדש"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid ""
"<i class=\"fa fa-pencil mr-2\" role=\"img\" aria-label=\"Edit\" "
"title=\"Create custom questions in backend\"/><em>Add Custom Questions</em>"
msgstr ""
"<i class=\"fa fa-pencil mr-2\" role=\"img\" aria-label=\"Create\" "
"title=\"Create\"/><em>הוסף שאלות </em>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> days</span>"
msgstr "<span> ימים</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> hours before</span>"
msgstr "<span> שעות לפני</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span> hours</span>"
msgstr "<span> שעות</span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>and not after </span>"
msgstr "<span>ולא אחרי </span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>at least </span>"
msgstr "<span>לפחות </span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "<span>until </span>"
msgstr "<span>עד </span>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "<strong class=\"mr-2\">Duration:</strong>"
msgstr "<strong class=\"mr-2\">משך זמן: </strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "<strong class=\"mr-2\">Location:</strong>"
msgstr "<strong class=\"mr-2\">מיקום</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid ""
"<strong>Appointment canceled!</strong>\n"
"                            You can schedule another appointment from here."
msgstr ""
"<strong>הפגישה בוטלה!</strong>\n"
"                            תוכל לקבוע פגישה נוספת מכאן."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                            The selected timeslot is not available anymore.\n"
"                            Someone has booked the same time slot a few\n"
"                            seconds before you."
msgstr ""
"<strong>הפגישה נכשלה!</strong>\n"
"                            חלון הזמן שנבחר אינו זמין יותר.\n"
"                            מישהו הזמין את אותו חלון זמן\n"
"                            שניות לפניך."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid ""
"<strong>Appointment failed!</strong>\n"
"                            The selected timeslot is not available.\n"
"                            It appears you already have another meeting with us at that date."
msgstr ""
"<strong>הפגישה נכשלה!</strong>\n"
"                            חלון הזמן שנבחר אינו זמין.\n"
"                            נראה שיש לך כבר פגישה נוספת איתנו באותו תאריך."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_select_timezone
msgid "<strong>Timezone</strong>"
msgstr "<strong>איזור זמן</strong>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "<strong>Your appointment has been successfully booked!</strong><br/>"
msgstr "<strong>הפגישה שלך נקבעה בהצלחה!</strong><br/>"

#. module: appointment
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid ""
"A list of slots information is needed to create a custom appointment type"
msgstr "צריך מידע על רשימת משבצות ליצירת סוג פגישה מותאם אישית."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "A text message reminder is sent to you before your appointment"
msgstr "הודעת טקסט לתזכורת נשלחת אליך לפני פגישה"

#. module: appointment
#: code:addons/appointment/controllers/calendar.py:0
#: code:addons/appointment/controllers/calendar.py:0
#, python-format
msgid "Access Denied"
msgstr "הגישה נדחתה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__access_token
msgid "Access Token"
msgstr "אסימון גישה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__active
msgid "Active"
msgstr "פעיל"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Add a specific appointment."
msgstr ""

#. module: appointment
#: model:res.groups,name:appointment.group_calendar_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "הכל"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_event_action_report
#: model:ir.ui.menu,name:appointment.menu_schedule_report_online
msgid "All Appointments"
msgstr "כל הפגישות"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__allday
#, python-format
msgid "All day"
msgstr "כל היום"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Allow Cancelling"
msgstr "אפשר ביטול"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"An employee should be set on the actual user to create the appointment type"
msgstr ""

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"An employee should be set on the current user to create the appointment type"
msgstr ""

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid "An unique type slot should have a start and end datetime"
msgstr "משבצת מסוג ייחודי צריכה מועד התחלה וסיום"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__name
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_answer_view_form
msgid "Answer"
msgstr "תשובה"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Appointment"
msgstr "פגישה"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_booked
#: model:mail.message.subtype,name:appointment.mt_appointment_type_booked
#: model:mail.message.subtype,name:appointment.mt_calendar_event_booked
#: model:mail.template,name:appointment.appointment_booked_mail_template
msgid "Appointment Booked"
msgstr "פגישות שנקבעו"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_booked_mail_template
msgid "Appointment Booked: {{ object.appointment_type_id.name }}"
msgstr "נקבעה פגישה: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model:mail.message.subtype,description:appointment.mt_calendar_event_canceled
#: model:mail.message.subtype,name:appointment.mt_appointment_type_canceled
#: model:mail.message.subtype,name:appointment.mt_calendar_event_canceled
#: model:mail.template,name:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled"
msgstr "פגישה בוטלה"

#. module: appointment
#: model:mail.template,subject:appointment.appointment_canceled_mail_template
msgid "Appointment Canceled: {{ object.appointment_type_id.name }}"
msgstr "בוטל פגישה: {{ object.appointment_type_id.name }}"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Appointment Confirmation"
msgstr "אישור פגישה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_duration
msgid "Appointment Duration"
msgstr "משך פגישה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Appointment Introduction"
msgstr "תיאור פגישה"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_appointment_type_action_custom_and_work_hours
#: model:ir.ui.menu,name:appointment.menu_calendar_appointment_type_custom_and_work_hours
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_tree_invitation
msgid "Appointment Invitations"
msgstr "הזמנות לפגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointment Name"
msgstr "שם הפגישה"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_type
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__appointment_type_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__name
#: model_terms:ir.ui.view,arch_db:appointment.appointment_type_select
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_tree
msgid "Appointment Type"
msgstr "סוג פגישה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "Appointment Types"
msgstr "סוגי פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Appointment:"
msgstr "פגישה:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__appointment_type_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_graph
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_pivot
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_home_menu_appointment
msgid "Appointments"
msgstr "פגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Appointments by"
msgstr "פגישות מאת"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "Archived"
msgstr "בארכיון"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__assign_method
msgid "Assignment Method"
msgstr "שיטת הקצאה"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid ""
"Atleast one slot duration from start to end is invalid: a slot should end "
"after start"
msgstr ""
"יש לפחות משבצת אחת עם בעיה של משך: משבצת צריכה להסתיים אחרי מועד ההתחלה"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid ""
"Atleast one slot duration is not enough to create a slot with the duration "
"set in the appointment type"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Attendees:"
msgstr "משתתפים:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__slot_ids
msgid "Availabilities"
msgstr "זמינות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Availability"
msgstr "זמינות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__answer_ids
msgid "Available Answers"
msgstr "תשובות זמינות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Available Employees"
msgstr "עובדים זמינים"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Basic blocks"
msgstr ""

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#: model:ir.ui.menu,name:appointment.calendar_appointment_type_menu_action
#, python-format
msgid "Calendar"
msgstr "יומן"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_share
msgid "Calendar Appointment Share Wizard"
msgstr "אשף שיתוף פגישות בקלנדר"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "מידע על משתתפי לוח השנה"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_event
msgid "Calendar Event"
msgstr "אירוע לוח שנה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__min_cancellation_hours
msgid "Cancel Before (hours)"
msgstr "בטל לפני (שעות)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__category
msgid "Category"
msgstr "קטגוריה"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__checkbox
msgid "Checkboxes (multiple answers)"
msgstr "תיבות סימון (תשובות מרובות)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__assign_method__chosen
msgid "Chosen by the Customer"
msgstr "נבחר על ידי הלקוח"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Click in your calendar to pick meeting time proposals."
msgstr "בחר בלוח השנה שלך כדי לשלוח הצעות לשעת פגישה."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_share_view_form
msgid "Close"
msgstr "סגור"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action
msgid ""
"Configure your service opening hours and let attendees book time slots "
"online."
msgstr ""
"הגדרת שעות פתיחה ומתן שירותים, ואפשרות לאנשים להשתבץ לפגישות באופן מקוון."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm Appointment <span class=\"fa fa-arrow-right\"/>"
msgstr "אשר את הפגישה <span class=\"fa fa-arrow-right\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Confirm your details"
msgstr "אשר את הפרטים שלך"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_confirmation
msgid "Confirmation Message"
msgstr "הודעת אישור"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Confirmation<span class=\"chevron\"/>"
msgstr "אישור<span class=\"chevron\"/>"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Confirmed"
msgstr "מאושר"

#. module: appointment
#: model:ir.model,name:appointment.model_res_partner
msgid "Contact"
msgstr "צור קשר"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Copied !"
msgstr "הועתק !"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action
msgid "Create an Appointment Type"
msgstr "יצירת סוג פגישה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__create_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__create_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__custom
msgid "Custom"
msgstr "מותאם"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Customer Preview"
msgstr "צפיה מקדימה ללקוח"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Declined"
msgstr "דחייה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__slot_type
msgid ""
"Defines the type of slot. The recurring slot is the default type which is used for\n"
"        appointment type that are used recurringly in type like medical appointment.\n"
"        The one shot type is only used when an user create a custom appointment type for a client by\n"
"        defining non-recurring time slot (e.g. 10th of April 2021 from 10 to 11 am) from its calendar."
msgstr ""
"מגדיר את סוג המשבצת, ברירת המחדל היא אירוע מחזורי, המשמש\n"
"        לסוג אירועים שחוזר על עצמו, כמו תור לרופה.\n"
"        חד-פעמי מיועד למשתמש שרוצה ליצור סוג אירוע על-ידי\n"
"        בחירת משבצת זמן במועד מסויים (למשל ה-20 באפריל 2016 מ-16:20-17:20)."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Description:"
msgstr "תיאור:"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__allday
msgid ""
"Determine if the slot englobe the whole day, mainly used for unique slot "
"type"
msgstr "קובע אם המשבצת תופסת את כל היום. בעיקר מיועד למשבצת זמן ייחודית."

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_insert_share_view_form
#, python-format
msgid "Discard"
msgstr "בטל"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__display_name
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: appointment
#: model:calendar.appointment.type,name:appointment.calendar_appointment_1
msgid "Doctor Appointment"
msgstr "תור לרופא"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__select
msgid "Dropdown (one answer)"
msgstr "נפתח (תשובה אחת)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__duration
msgid "Duration"
msgstr "משך זמן"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Duration:"
msgstr "משך:"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "Email: %s"
msgstr "מייל: %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__employee_ids
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__employee_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Employees"
msgstr "עובדים"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__end_datetime
msgid "End datetime for unique slot type management"
msgstr "זמן סיום לניהול משבצות זמן ייחודיות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__end_hour
msgid "Ending Hour"
msgstr "שעת סיום"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Every"
msgstr "כל"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__5
msgid "Friday"
msgstr "יום שישי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "From"
msgstr "מ"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Get Share Link"
msgstr "קישור לשיתוף"

#. module: appointment
#: model_terms:calendar.appointment.type,message_intro:appointment.calendar_appointment_0
msgid ""
"Get a <strong>customized demo</strong> and an <strong>analysis of your "
"needs</strong>."
msgstr ""
"קבל <strong>הדגמה מותאמת אישית</strong> ו <strong>ניתוח לצרכים שלך</strong>."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_share__suggested_employee_ids
msgid ""
"Get the employees link to the appointment type selected to apply a domain on"
" the employees that can be selected"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__assign_method
msgid ""
"How employees will be assigned to meetings customers book on your website."
msgstr "כיצד העובדים ישויכו לפגישות שמזמינים לקוחות באתר האינטרנט שלך."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_needaction
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_unread
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_error
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Insert Appointment Link"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_insert_share_view_form
msgid "Insert link"
msgstr "הוסף קישור"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_intro
msgid "Introduction Message"
msgstr "הודעת פתיחה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid ""
"It's too late to cancel online, please contact the attendees another way if "
"you really can't make it."
msgstr ""
"מאוחר מדי לביטול מקוון של הפגישה. נא להודיע למוזמנים בדרך חלופית אם באמת אין"
" באפשרותך להגיע."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__country_ids
msgid ""
"Keep empty to allow visitors from any country, otherwise you only allow "
"visitors from selected countries"
msgstr ""
"השאר ריק כדי לאפשר אורחים מכל הארצות, אחרת תאפשר למבקרים מארצות נבחרות בלבד"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot____last_update
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__write_uid
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__write_date
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__share_link
msgid "Link"
msgstr "קישור"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Link Copied in your clipboard !"
msgstr "הקישור הועתק!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_share_view_form
msgid "Link Generator"
msgstr "מחולל קישורים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__location
msgid "Location"
msgstr "מיקום"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__location
msgid "Location of the appointments"
msgstr "מיקום הפגישות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Location:"
msgstr "מיקום:"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_main_attachment_id
msgid "Main Attachment"
msgstr "קובץ ראשי מצורף "

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "Meeting with %s"
msgstr "פגישה עם %s"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Messages"
msgstr "הודעות"

#. module: appointment
#: code:addons/appointment/controllers/main.py:0
#, python-format
msgid "Mobile: %s"
msgstr "נייד: %s"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__1
msgid "Monday"
msgstr "יום שני"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__text
msgid "Multi-line text"
msgstr "טקסט מרובה שורות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_search
msgid "My Appointments"
msgstr "הפגישות שלי"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "שם"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "No appointment found."
msgstr "לא נמצאו פגישות."

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_appointment_type_action_custom_and_work_hours
msgid "No custom appointment type has been created !"
msgstr "לא נוצר סוג פגישה מותאם אישית!"

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_reporting
msgid "No data yet!"
msgstr "אין דירוג עדיין"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "אף אחד"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "מספר הודעות המחייבות פעולה"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__message_unread_counter
msgid "Number of unread messages"
msgstr "מספר ההודעות שלא נקראו"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__slot_type__unique
msgid "One Shot"
msgstr "חד-פעמי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_event__appointment_type_id
msgid "Online Appointment"
msgstr "פגישה מקוונת"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_answer
msgid "Online Appointment : Answers"
msgstr "פגישה מקוונת : תשובות"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_question
msgid "Online Appointment : Questions"
msgstr "פגישה מקוונת : שאלות"

#. module: appointment
#: model:ir.model,name:appointment.model_calendar_appointment_slot
msgid "Online Appointment : Time Slot"
msgstr "פגישה מקוונת : חלון זמן"

#. module: appointment
#: model:ir.actions.act_window,name:appointment.calendar_appointment_type_action
#: model:ir.actions.act_window,name:appointment.calendar_event_action_reporting
#: model:ir.module.category,name:appointment.module_category_calendar
#: model:ir.ui.menu,name:appointment.appointment_type_menu
#: model:ir.ui.menu,name:appointment.menu_schedule_report_all
#: model_terms:ir.ui.view,arch_db:appointment.calendar_event_view_search_inherit_appointment
msgid "Online Appointments"
msgstr "פגישות מקוונות"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"Only one work hours appointment type is allowed for a specific employee."
msgstr ""

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Past"
msgstr "עבר"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__placeholder
msgid "Placeholder"
msgstr "שומר מקום"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_slot.py:0
#, python-format
msgid "Please enter a valid hour between 0:00 and 24:00 for your slots."
msgstr "נא להזין שעה תקינה בין 0:00 לבין 24:00 עבור חלונות הזמן שלך."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Please, select another date."
msgstr "אנא בחר תאריך אחר."

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__suggested_employee_ids
msgid "Possible employees"
msgstr "עובדים אפשריים"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__name
msgid "Question"
msgstr "שאלה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__question_type
msgid "Question Type"
msgstr "סוג שאלה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_answer__question_id
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__question_ids
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Questions"
msgstr "שאלות"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__radio
msgid "Radio (one answer)"
msgstr "רדיו (תשובה אחת)"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__assign_method__random
msgid "Random"
msgstr "אקראי"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__slot_type__recurring
msgid "Recurring"
msgstr "הזמנות חוזרות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__reminder_ids
msgid "Reminders"
msgstr "תזכורות"

#. module: appointment
#: model:ir.ui.menu,name:appointment.menu_schedule_report
msgid "Reporting"
msgstr "דו\"חות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__question_required
msgid "Required Answer"
msgstr "תשובה חובה"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Responsible"
msgstr "אחראי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__country_ids
msgid "Restrict Countries"
msgstr "הגבלת ארצות"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "SCHEDULED"
msgstr "מתוזמן"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: appointment
#: model:calendar.alarm,name:appointment.calendar_alarm_data_1h_sms
msgid "SMS Text Message - 1 Hours"
msgstr "הודעת SMS - שעה אחת "

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__6
msgid "Saturday"
msgstr "יום שבת"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Schedule Appointment"
msgstr "תזמן פגישה"

#. module: appointment
#: model:calendar.appointment.type,name:appointment.calendar_appointment_0
msgid "Schedule a Demo"
msgstr "תזמן הדגמה "

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Schedule an Appointment"
msgstr "תזמן פגישה"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/wysiwyg.js:0
#, python-format
msgid "Schedule an appointment."
msgstr ""

#. module: appointment
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_report
#: model_terms:ir.actions.act_window,help:appointment.calendar_event_action_reporting
msgid "Schedule appointments to get statistics"
msgstr "קבעו פגישות כדי לקבל סטטיסטיקות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__min_schedule_hours
msgid "Schedule before (hours)"
msgstr "תזמן לפני (שעות)"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__max_schedule_days
msgid "Schedule not after (days)"
msgstr "תזמן לא אחרי (ימים)"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Scheduling"
msgstr "תזמון"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "חפש בהכל"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "חפש בתיאור"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Name"
msgstr "חיפוש בשם"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Search in Responsible"
msgstr ""

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_share__appointment_type_count
msgid "Selected Appointments Count"
msgstr "כמות הפגישות שנבחרו"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_question__sequence
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__sequence
msgid "Sequence"
msgstr "רצף"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "Share"
msgstr "שתף"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#, python-format
msgid "Share Availabilities"
msgstr "שתף זמינות"

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid "Share Link"
msgstr "שתף קישור"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_question__question_type__char
msgid "Single line text"
msgstr "טקסט שורה בודדת"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__slot_type
msgid "Slot type"
msgstr "סוג משבצת"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_slot__start_datetime
msgid "Start datetime for unique slot type management"
msgstr "זמן התחלה לניהול משבצות זמן ייחודיות"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__start_hour
msgid "Starting Hour"
msgstr "שעת התחלה"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__7
msgid "Sunday"
msgstr "יום ראשון"

#. module: appointment
#: model:calendar.appointment.question,name:appointment.calendar_appointment_1_question_1
msgid "Symptoms"
msgstr "סימפטומים"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_share__employee_ids
msgid ""
"The employees that will be display/filter for the user to make its "
"appointment"
msgstr ""

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "The field '%s' does not exist in the targeted model"
msgstr "השדה '%s' לא מוגדר במודל היעד"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "There is no appointment linked to your account."
msgstr "אין פגישות מקושרות לחשבונך."

#. module: appointment
#: code:addons/appointment/models/calendar_appointment_type.py:0
#, python-format
msgid ""
"This category of appointment type should only have one employee but got %s "
"employees"
msgstr ""
"בקטגוריה הזו של סוגי פגישות צריך להיות רק עובד אחד אבל מוגדרים %s עובדים"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "This is a preview of the customer appointment form."
msgstr "זה תצוגה מקדימה של טופס פגישה ללקוח."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__4
msgid "Thursday"
msgstr "יום חמישי"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "Time displayed in"
msgstr "זמן מוצג ב"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
msgid "Time<span class=\"chevron\"/>"
msgstr "זמן<span class=\"chevron\"/>"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__appointment_tz
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_kanban
msgid "Timezone"
msgstr "אזור זמן"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__appointment_tz
msgid "Timezone where appointment take place"
msgstr "אזור הזמן בו מתקיימת הפגישה"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "To"
msgstr "ל"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "Total:"
msgstr "סה\"כ:"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__2
msgid "Tuesday"
msgstr "יום שלישי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_unread
msgid "Unread Messages"
msgstr "הודעות שלא נקראו"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__message_unread_counter
msgid "Unread Messages Counter"
msgstr "מספר ההודעות שלא נקראו"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "Until (max)"
msgstr "עד (מקסימום)"

#. module: appointment
#: code:addons/appointment/controllers/portal.py:0
#, python-format
msgid "Upcoming"
msgstr "בקרוב"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "Use the top button '<b>+ New</b>' to create an appointment type."
msgstr "השתמשו בכפתור העליון '<b>+חדש</b>' ליצירת סוג חדש של פגישה."

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__category
msgid ""
"Used to define this appointment type's category.\n"
"        Can be one of:\n"
"            - Website: the default category, the people can access and shedule the appointment with employees from the website\n"
"            - Custom: the employee will create and share to an user a custom appointment type with hand-picked time slots\n"
"            - Work Hours: a special type of appointment type that is used by one employee and which takes the working hours of this\n"
"                employee as availabilities. This one uses recurring slot that englobe the entire week to display all possible slots\n"
"                based on its working hours and availabilities"
msgstr ""

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointments_list_layout
msgid "View Availabilities <span class=\"fa fa-arrow-right\"/>"
msgstr "הצג זמינות <span class=\"fa fa-arrow-right\"/>"

#. module: appointment
#: model_terms:calendar.appointment.type,message_confirmation:appointment.calendar_appointment_0
msgid ""
"We thank you for your interest in our products!<br>\n"
"               Please make sure to arrive <strong>10 minutes</strong> before your appointment."
msgstr ""
"אנו מודים לך על התעניינותך במוצרים שלנו!<br>\n"
"               אנא דאג להגיע <strong>10 דקות</strong> לפני הפגישה שלך."

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__website
msgid "Website"
msgstr "אתר אינטרנט"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_type__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: appointment
#: model:ir.model.fields,help:appointment.field_calendar_appointment_type__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: appointment
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_slot__weekday__3
msgid "Wednesday"
msgstr "יום רביעי"

#. module: appointment
#: model:ir.model.fields,field_description:appointment.field_calendar_appointment_slot__weekday
msgid "Week Day"
msgstr "יום"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "When:"
msgstr "שעה:"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.employee_select
msgid "With"
msgstr "עם"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/xml/calendar_appointment_scheduling.xml:0
#: model:ir.model.fields.selection,name:appointment.selection__calendar_appointment_type__category__work_hours
#, python-format
msgid "Work Hours"
msgstr "שעות עבודה"

#. module: appointment
#. openerp-web
#: code:addons/appointment/static/src/js/calendar_renderer.js:0
#: code:addons/appointment/static/src/js/calendar_renderer.js:0
#, python-format
msgid "You can not create a slot in the past."
msgstr "אי אפשר ליצור משבצת בעבר."

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Email *"
msgstr "המייל שלך *"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Name *"
msgstr "שמך *"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "Your Phone *"
msgstr "טלפון )"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "Your appointment is in less than"
msgstr "הפגישה שלך היא בפחות מ-"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.calendar_appointment_type_view_form
msgid "e.g. Schedule a demo"
msgstr "למשל: תזמן הדגמה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_info
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hour"
msgstr "שעה"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_validated
msgid "hours from now!"
msgstr "שעות מעכשיו!"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_form
msgid "on"
msgstr "ב"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.appointment_calendar
msgid "timezone"
msgstr "אזור זמן"

#. module: appointment
#: model_terms:ir.ui.view,arch_db:appointment.portal_my_appointments
msgid "with"
msgstr "עם"
