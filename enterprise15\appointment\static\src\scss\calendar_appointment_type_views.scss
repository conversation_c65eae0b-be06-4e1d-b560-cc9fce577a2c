.o_kanban_view.o_kanban_dashboard.o_appointment_kanban {
    &.o_kanban_ungrouped .o_kanban_record {
        width: 325px;
        min-height: 165px;
    }

    &.o_kanban_grouped .o_kanban_record {
        min-height: 165px;
    }

    .ribbon {
        &::before, &::after {
            display: none;
        }

        span {
            background-color: $o-brand-odoo;
            padding: 5px;
            font-size: small;
            z-index: unset;
            height: auto;
        }
    }

    .ribbon-top-right {
        margin-top: -$o-kanban-dashboard-vpadding;

        span {
            left: 0px;
            right: 30px;
        }
    }

    .o_appointment_kanban_boxes {
        flex-flow: row nowrap;

        .o_appointment_kanban_box {
            flex: 1 1 auto;
            flex-flow: row nowrap;
            &:first-child {
                justify-content: flex-start;
                padding-left: 16px;
            }
            div:last-child {
                justify-content: flex-end;
                text-align: right;
            }

        }
    }
}

.o_appointment_slots_list {
    th.o_list_button {
        width: 2.4em!important;
    }

    button.fa-long-arrow-right {
        pointer-events: none;
    }
}

.o_appointment_create_custom_appointment.disabled {
    pointer-events: none;
}
