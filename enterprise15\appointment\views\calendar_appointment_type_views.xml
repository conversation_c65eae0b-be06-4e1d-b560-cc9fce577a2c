<?xml version="1.0"?>
<odoo>

    <record id="calendar_appointment_type_view_search" model="ir.ui.view">
        <field name="name">calendar.appointment.type.search</field>
        <field name="model">calendar.appointment.type</field>
        <field name="arch" type="xml">
            <search string="Appointment Types">
                <field name="name"/>
                <field name="location"/>
                <field name="employee_ids"/>
                <field name="category"/>
                <filter string="My Appointments" name="my_appointments" domain="[('employee_ids.user_id', 'in', [uid])]"/>
                <separator/>
                <filter name="filter_active" string="Archived" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="calendar_appointment_type_view_kanban" model="ir.ui.view">
        <field name="name">calendar.appointment.type.kanban</field>
        <field name="model">calendar.appointment.type</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_dashboard o_appointment_kanban" sample="1">
                <field name="appointment_tz"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary col-12 p-0 w-75">
                                        <field name="name"/>
                                    </div>
                                </div>
                            </div>
                            <div class="container o_kanban_card_content mt-3">
                                <div class="row">
                                    <div class="col-5">
                                        <button class="btn btn-primary mr-2" name="action_calendar_meetings" type="object">
                                            <field name="appointment_count"/> SCHEDULED
                                        </button>
                                    </div>
                                    <div class="col-7">
                                        <span t-if="record.appointment_tz.value" title="Timezone">
                                            <i class="fa fa-clock-o small mr-2"/><span t-esc="record.appointment_tz.value" class="align-middle"/>
                                        </span>
                                    </div>
                                </div>
                                <div class="row d-flex o_appointment_kanban_boxes">
                                    <div class="o_appointment_kanban_box o_kanban_primary_bottom bottom_block d-flex position-relative justify-content-center px-0">
                                        <div class="col-6"/>
                                        <div class="col-6">
                                            <button class="btn btn-link btn-sm py-0 fa fa-lg fa-link" name="action_share" type="object" title="Share"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="calendar_appointment_type_view_tree" model="ir.ui.view">
        <field name="name">calendar.appointment.type.tree</field>
        <field name="model">calendar.appointment.type</field>
        <field name="arch" type="xml">
            <tree string="Appointment Type" multi_edit="1" sample="1">
                <field name="sequence" widget="handle"/>
                <field name="name" readonly="1"/>
                <field name="location"/>
                <field name="appointment_tz"/>
                <field name="employee_ids" widget="many2many_tags" optional="hide"/>
                <field name="country_ids" widget="many2many_tags" groups="base.group_no_one" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="calendar_appointment_type_view_tree_invitation" model="ir.ui.view">
        <field name="name">calendar.appointment.type.tree.invitation</field>
        <field name="model">calendar.appointment.type</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <tree string="Appointment Invitations" multi_edit="1" sample="1">
                <field name="sequence" widget="handle"/>
                <field name="name" readonly="1"/>
                <field name="appointment_tz"/>
                <field name="employee_ids" widget="many2many_tags" optional="hide"/>
                <field name="category" groups="base.group_no_one"/>
            </tree>
        </field>
    </record>

    <record id="calendar_appointment_type_view_form" model="ir.ui.view">
        <field name="name">calendar.appointment.type.form</field>
        <field name="model">calendar.appointment.type</field>
        <field name="arch" type="xml">
            <form string="Appointment Type">
                <header>
                    <button name="action_share" string="Share" type="object" class="btn btn-primary"/>
                </header>
                <sheet>
                    <field name="active" invisible="1"/>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger"
                        attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_button_box" name="button_box" groups="base.group_user">
                        <button class="oe_stat_button" type="object"
                            name="action_calendar_meetings"
                            icon="fa-calendar">
                            <field string="Appointments" name="appointment_count" widget="statinfo"/>
                        </button>
                        <button string="Customer Preview" class="oe_stat_button" type="object"
                            name="action_customer_preview"
                            icon="fa-globe"/>
                    </div>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1><field name="name" placeholder="e.g. Schedule a demo"/></h1>
                    </div>
                    <notebook>
                        <page string="Scheduling" name="schedule_availability">
                            <group>
                                <group name="left_details">
                                    <label for="appointment_duration" attrs="{'invisible': [('category', '=', 'custom')]}"/>
                                    <div attrs="{'invisible': [('category', '=', 'custom')]}">
                                          <field name="appointment_duration" class="oe_inline" widget="float_time"/><span> hours</span>
                                    </div>
                                    <label for="min_schedule_hours" string="Schedule Appointment"/>
                                    <div>
                                        <div>
                                          <span>at least </span><field name="min_schedule_hours" class="oe_inline" widget="float_time"/><span> hours before</span>
                                        </div>
                                        <div attrs="{'invisible': [('category', '=', 'custom')]}">
                                          <span>and not after </span><field name="max_schedule_days" class="oe_inline"/><span> days</span>
                                        </div>
                                    </div>
                                    <label for="min_cancellation_hours" string="Allow Cancelling"/>
                                    <div>
                                        <div>
                                          <span>until </span><field name="min_cancellation_hours" class="oe_inline" widget="float_time"/><span> hours before</span>
                                        </div>
                                    </div>
                                </group>
                                <group name="right_details">
                                    <field name="appointment_tz"/>
                                    <field name="reminder_ids" widget="many2many_tags"/>
                                    <field name="location"/>
                                    <field name="assign_method" attrs="{'invisible': [('category', '!=', 'website')]}"/>
                                    <field name="country_ids" widget="many2many_tags" groups="base.group_no_one"/>
                                </group>
                            </group>
                            <separator string="Available Employees"/>
                            <field name="employee_ids" attrs="{'readonly': [('category', '!=', 'website')]}">
                                <tree string="Employees" delete="1" create="0">
                                    <field name="name"/>
                                    <field name="resource_calendar_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Availability" name="slots">
                            <field name="category" invisible="1"/>
                            <field name="slot_ids">
                                <tree editable="bottom" class="o_appointment_slots_list">
                                    <field name="weekday" string="Every"
                                        attrs="{'column_invisible': [('parent.category', '=', 'custom')]}"/>
                                    <field name="start_hour" string="From" widget="float_time"
                                        attrs="{'column_invisible': [('parent.category', '=', 'custom')]}"/>
                                    <button name="durationArrow" class="fa fa-long-arrow-right text-center" title="Until (max)"
                                        attrs="{'column_invisible': [('parent.category', '=', 'custom')]}"/>
                                    <field name="end_hour" string="To" widget="float_time"
                                        attrs="{'column_invisible': [('parent.category', '=', 'custom')]}"/>
                                    <field name="start_datetime" attrs="{'column_invisible': [('parent.category', '!=', 'custom')]}"/>
                                    <field name="end_datetime" attrs="{'column_invisible': [('parent.category', '!=', 'custom')]}"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Questions" name="questions">
                            <field name="question_ids">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="question_type"/>
                                    <field name="question_required"/>
                                </tree>
                                <form string="Questions" name="question_form">
                                    <group>
                                        <group name="question_form_left">
                                            <field name="name"/>
                                            <field name="question_type" widget="radio"/>
                                        </group>
                                        <group name="question_form_right">
                                            <field name="answer_ids" widget="many2many_tags" options="{'no_create_edit': True}"
                                                   attrs="{'invisible': [('question_type','in',('char','text'))], 'required':[('question_type','in',('select','radio','checkbox'))]}"/>
                                            <field name="placeholder" attrs="{'invisible': [('question_type','in',('select','radio','checkbox'))]}"/>
                                            <field name="question_required"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="Messages" name="messages">
                            <separator string="Appointment Introduction"/>
                            <field name="message_intro" class="oe-bordered-editor"/>
                            <separator string="Appointment Confirmation"/>
                            <field name="message_confirmation" class="oe-bordered-editor"/>
                        </page>
                    </notebook>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" options="{'post_refresh':True}" groups="base.group_user"/>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <record id="calendar_appointment_type_action" model="ir.actions.act_window">
        <field name="name">Online Appointments</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">calendar.appointment.type</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('category', '=', 'website')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create an Appointment Type
            </p><p>
                Configure your service opening hours and let attendees book time slots online.
            </p>
        </field>
    </record>

    <record id="calendar_appointment_type_action_custom_and_work_hours" model="ir.actions.act_window">
        <field name="name">Appointment Invitations</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">calendar.appointment.type</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="calendar_appointment_type_view_tree_invitation"/>
        <field name="domain">[('category', '!=', 'website')]</field>
        <field name="context">{
            'default_category': 'custom',
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No custom appointment type has been created !
            </p>
        </field>
    </record>
</odoo>
