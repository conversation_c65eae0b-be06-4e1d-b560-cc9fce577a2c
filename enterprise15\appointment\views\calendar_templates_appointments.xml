<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Pages -->
    <template id="appointments_list_layout" name="Appointments List Layout">
        <t t-set="no_breadcrumbs" t-value="True"/>
        <t t-call="portal.portal_layout">
            <div id="wrap" class="o_appointment_index">
                <div class="oe_structure container o_appointment_choice">
                    <h1 class="o_page_header mt32">
                        Schedule an Appointment
                    </h1>
                    <div t-if="message == 'cancel'" class="alert alert-danger mt16" role="alert">
                        <p class="mb-0">
                            <strong>Appointment canceled!</strong>
                            You can schedule another appointment from here.
                        </p>
                    </div>
                    <div t-if="not appointment_types" class="col-12">
                        <div class="h2 mb-3">No appointment found.</div>
                        <div class="alert alert-info text-center" groups="appointment.group_calendar_manager">
                            <p class="m-0">Use the top button '<b>+ New</b>' to create an appointment type.</p>
                        </div>
                    </div>
                    <div t-else="" class="pr-0">
                        <form class="form o_website_appointment_form"
                            t-attf-action="/calendar/#{ slug(appointment_types[0]) }/appointment?#{keep_query('filter_appointment_type_ids')}"
                            method="POST">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <t t-call="appointment.appointment_type_select"/>
                            <div t-out="appointment_types[0].message_intro" class="o_calendar_intro my-2"/>
                            <div class="form-group row col">
                                <div class="mx-auto">
                                    <button type="submit" class="btn btn-primary">View Availabilities <span class="fa fa-arrow-right"/></button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="oe_structure"/>
                </div>
            </div>
        </t>
    </template>

    <template id="appointment_type_select" name="Website Appointment: Appointment Select Form Input">
        <div id="appointment_type_select" class="form-group row col">
            <label for="calendarType" class="col-form-label">Appointment Type</label>
            <div t-att-class="'col-lg-9 col-md-7 ml-auto pl-0 ' + (len(appointment_types) == 1 and 'o_appointment_selection' or '')">
                <select name="appointment_type_id" class="form-control" id="calendarType" t-att-disabled="len(appointment_types)==1 and '1' or None">
                    <t t-foreach="appointment_types" t-as="appointment_type">
                        <option t-att-value="appointment_type.id" t-att-selected="appointment_type.id == appointment_types[0].id and 'selected'">
                            <t t-out="appointment_type.name"/>
                        </option>
                    </t>
                </select>
            </div>
        </div>
    </template>

    <!-- Appointment Info & Availabilities-->
    <template id="appointment_info" name="Online Appointment: Appointment Info">
        <t t-set="no_breadcrumbs" t-value="True"/>
        <t t-call="portal.portal_layout">
            <div id="wrap" class="d-flex bg-o-color-4 px-3 mt-3">
                <div class="appointment_portal_alert">
                    <t t-set="o_portal_fullwidth_alert" groups="appointment.group_calendar_manager">
                        <t t-call="portal.portal_back_in_edit_mode">
                            <t t-set="backend_url" t-value="'/web#id=%s&amp;view_type=form&amp;model=%s' % (appointment_type.id, appointment_type._name)"/>
                            <t t-set="custom_html">This is a preview of the customer appointment form.</t>
                        </t>
                    </t>
                </div>
                <div class="container mb64 o_appointment">
                    <ul class="wizard mt32 float-right d-none d-md-block">
                        <li class="text-primary">Time<span class="chevron"></span></li>
                        <li class="text-muted">Confirmation<span class="chevron"></span></li>
                    </ul>
                    <h1 class="o_page_header mt32"><t t-out="appointment_type.name"/></h1>
                    <div t-if="state == 'failed-employee'" class="alert alert-danger mt16" role="alert">
                        <p>
                            <strong>Appointment failed!</strong>
                            The selected timeslot is not available anymore.
                            Someone has booked the same time slot a few
                            seconds before you.
                        </p>
                        <p class="mb-0">
                            Please, select another date.
                        </p>
                    </div>
                    <div t-if="state == 'failed-partner'" class="alert alert-danger mt16" role="alert">
                        <p>
                            <strong>Appointment failed!</strong>
                            The selected timeslot is not available.
                            It appears you already have another meeting with us at that date.
                        </p>
                        <p class="mb-0">
                            Please, select another date.
                        </p>
                    </div>
                    <div t-if="state == 'cancel'" class="alert alert-danger mt16" role="alert">
                        <p class="mb-0">
                            <strong>Appointment canceled!</strong>
                            You can schedule another appointment from here.
                        </p>
                    </div>
                    <div class="clearfix"/>
                    <div class="col pr-0">
                        <form id="slots_form" class="form o_website_appointment_form" autocomplete="off">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <input type="hidden" name="appointment_type_id" t-att-value="appointment_type.id"/>
                            <input type="hidden" name="filter_appointment_type_ids" t-att-value="filter_appointment_type_ids"/>
                            <input type="hidden" name="filter_employee_ids" t-att-value="filter_employee_ids"/>
                            <t t-if="appointment_type.assign_method == 'chosen'" t-call="appointment.employee_select"/>
                        </form>
                    </div>
                    <div t-if="appointment_type.category != 'custom'" class="d-lg-flex col pl-0 mb-2 o_not_editable">
                        <div class="col-md-6 pl-0">
                            <strong class="mr-2">Duration:</strong>
                            <span t-out="appointment_type.appointment_duration" t-options="{'widget': 'float_time'}"/>
                            hour<t t-if="appointment_type.appointment_duration>=2">s</t>
                        </div>
                        <div t-if="appointment_type.location" class="col-md-6 pl-0">
                            <strong class="mr-2">Location:</strong>
                            <span t-field="appointment_type.location"/>
                        </div>
                    </div>
                    <div t-field="appointment_type.message_intro" class="o_calendar_intro mb32"/>
                    <t t-call="appointment.appointment_calendar"/>
                    <div class="oe_structure"/>
                </div>
            </div>
        </t>
    </template>

    <template id="employee_select" name="Website Appointment: Appointment Calendars">
        <div name="employee_select" t-att-class="'form-group row ' + ('o_hidden' if not suggested_employees else '')">
            <label for="selectEmployee" class="col-form-label">With</label>
            <div t-att-class="'col-lg-9 col-md-7 ml-auto pl-0 ' + (len(suggested_employees) == 1 and 'o_appointment_selection' or '')">
                <select class="form-control" name="employee_id" id="selectEmployee" t-att-disabled="len(suggested_employees) == 1 and '1' or None">
                    <t t-foreach="suggested_employees" t-as="emp">
                        <option t-att-value="emp.id" t-att-selected="emp.id == suggested_employees[0].id and 'selected'">
                            <t t-out="emp.name"/>
                        </option>
                    </t>
                </select>
            </div>
        </div>
    </template>

    <template id="appointment_calendar" name="Online Appointment">
        <div class="o_appointment_slots_loading d-flex align-items-center pb-5 mt-n5">
            <i class="fa fa-3x fa-spin fa-spinner mx-auto text-o-color-2"/>
        </div>
        <div id="slots_availabilities" class="mt-3 d-none">
            <div id="calendar" class="col-lg-8 px-0">
                <div t-foreach="slots" t-as="month" t-attf-id="month-#{month['id']}" t-attf-class="o_appointment_month px-1 px-md-3 #{'d-none' if month['id'] > 0 else ''}">
                    <div id="calendarHeader" class="d-inline-flex align-items-baseline container-fluid">
                        <button t-if="month['id'] > 0" type="button" id="prevCal" t-attf-class="btn btn-primary o_js_calendar_navigate"><i class="fa fa-arrow-left"/></button>
                        <div t-else="" class="btn disabled fa fa-arrow-left mr-1 text-white"/>
                        <h3 t-out="month['month']" class="col text-center"/>
                        <button t-if="len(slots) > month['id'] + 1" type="button" id="nextCal" t-attf-class="btn btn-primary o_js_calendar_navigate ml-1"><i class="fa fa-arrow-right"/></button>
                        <div t-else="" class="btn disabled fa fa-arrow-right ml-1 text-white"/>
                    </div>
                    <table class="table mt-1">
                        <tr class="active">
                            <t t-foreach="formated_days" t-as="day">
                                <th class="text-center p-md-2 p-1 align-middle" t-out="day"/>
                            </t>
                        </tr>
                        <tr t-foreach="month['weeks']" t-as="weeks" class="o_appointment_days">
                            <t t-foreach="weeks" t-as="day">
                                <td t-attf-class="align-middle p-md-2 p-1 text-center#{day['slots'] and ' o_day' or ''} #{day['weekend_cls']} #{day['today_cls']}"
                                    t-attf-title="#{day['today_cls'] and 'Today' or ''}">
                                    <t t-if="day['slots']">
                                        <div t-att-id="day['day']" class="o_slot_button mx-auto w-50" t-out="day['day'].day" t-att-data-available-slots="json.dumps(day['slots'])"/>
                                    </t>
                                    <t t-if="not day['slots']">
                                        <div t-out="day['day'].day" t-attf-class="mx-auto w-50 #{day['mute_cls']}"/>
                                    </t>
                                </td>
                            </t>
                        </tr>
                    </table>
                </div>
                <div id="timezoneSelection" class="mt-lg-0 mt-3">
                    <span>Time displayed in <strong t-out="request.session['timezone']"/> timezone</span>
                </div>
            </div>
            <div id="slotsList" class="o_appointment_availabilities col-lg-4"/>
        </div>
    </template>

    <template id="appointment_select_timezone" inherit_id="appointment.appointment_calendar" active="False" customize_show="True" name="Allow Tz Choice">
        <xpath expr="//div[@id='timezoneSelection']" position="replace">
            <div id="timezoneSelection" class="mt-lg-0 mt-3">
                <div class="d-flex">
                    <label for="timezone" class="col-form-label"><strong>Timezone</strong></label>
                    <div class="mx-2">
                        <form autocomplete="off">
                            <select class="form-control" name="timezone">
                                <t t-foreach="appointment_type._fields['appointment_tz'].selection(appointment_type)" t-as="timezone">
                                    <option t-att-value="timezone[0]" t-out="timezone[1]" t-att-selected="(timezone[1] == request.session.timezone) and 1 or None"/>
                                </t>
                            </select>
                        </form>
                    </div>
                </div>
            </div>
        </xpath>
    </template>

</odoo>
