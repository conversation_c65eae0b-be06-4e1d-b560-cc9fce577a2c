<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="appointment_validated" name="Website Appointment: Appointment Confirmed">
        <t t-if="request.env.user._is_public()" t-set="no_breadcrumbs" t-value="True"/>
        <t t-call="portal.portal_layout">
            <div id="wrap" class="o_appointment d-flex bg-o-color-4 px-3 mt-3">
                <div class="oe_structure"/>
                <div class="oe_structure container mt-4">
                    <h1 class="o_page_header">Appointment: <span t-out="event.name"/></h1>
                    <div class="alert alert-info" t-if="state == 'new'" role="status">
                        <p class="mb-0">
                            <strong>Your appointment has been successfully booked!</strong><br/>
                        </p>
                    </div>
                    <div class="alert alert-danger" t-if="state == 'no-cancel'" role="alert">
                        <p class="mb-0">
                            <strong>Your appointment is in less than <t t-out="event.appointment_type_id.min_cancellation_hours"/> hours from now!</strong>
                            It's too late to cancel online, please contact the attendees another way if you really can't make it.
                        </p>
                    </div>
                    <div class="oe_structure"/>
                    <div class="row">
                        <div class="col my-3 o_not_editable">
                            <t t-if="event.appointment_type_id">
                                <div t-out="event.appointment_type_id.message_confirmation" class="mb-2"/>
                            </t>
                            <div class="col-lg-6 row">
                                <div class="col-4">
                                    <label>When:</label>
                                </div>
                                <div class="col-7 mx-md-0">
                                    <t t-out="datetime_start"/>
                                    <br/>
                                    <i class="text-muted">(timezone: <t t-out="request.session.timezone"/>)</i>
                                </div>
                            </div>
                            <div class="col-lg-6 row">
                                <div class="col-4">
                                    <label>Duration:</label>
                                </div>
                                <div class="col-7 mx-md-0">
                                    <t t-out="event.duration" t-options="{'widget': 'float_time'}"/>
                                    hour<t t-if="event.duration>=2">s</t>
                                </div>
                            </div>
                            <div t-if="event.location" class="col-lg-6 row">
                                <div class="col-4">
                                    <label>Location:</label>
                                </div>
                                <div class="col-7 mx-md-0">
                                    <t t-out="event.location"/>
                                </div>
                            </div>
                            <div class="col-lg-6 row">
                                <div class="col-4">
                                    <label>Attendees:</label>
                                </div>
                                <div class="col-7 mx-md-0">
                                    <div t-foreach="event.attendee_ids" t-as="attendee">
                                        <t t-out="attendee.common_name"/>
                                        <span t-if="attendee.state == 'accepted'" class="fa fa-check text-success" title="Confirmed" role="img" aria-label="Confirmed"/>
                                        <span t-if="attendee.state == 'declined'" class="fa fa-times text-danger" title="Declined" role="img" aria-label="Declined"/>
                                    </div>
                                </div>
                            </div>
                            <div t-if="not is_html_empty(event.description)" class="col-lg-6 row">
                                <div class="col-4">
                                    <label>Description:</label>
                                </div>
                                <div class="col-7 mx-md-0">
                                    <div t-field="event.description"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-5">
                        <div class="col-md-4 text-left">
                            <a role="button" class="btn btn-block btn-primary" t-attf-href="/calendar/ics/#{event.access_token}.ics">
                                <i class="fa fa-fw fa-arrow-right"></i>Add to iCal/Outlook
                            </a>
                        </div>
                        <div class="col-md-4 text-center my-2 my-md-0">
                            <a role="button" class="btn btn-primary btn-block" t-att-href="google_url">
                                <i class="fa fa-fw fa-arrow-right"></i>Add to Google Calendar
                            </a>
                        </div>
                        <div class="col-md-4 text-right">
                            <a t-if="event.appointment_type_id" role="button" class="btn btn-danger btn-block" t-attf-href="/calendar/#{event.access_token}/cancel?partner_id=#{partner_id}">
                                <i class="fa fa-fw fa-times"></i>Cancel / Reschedule
                            </a>
                        </div>
                    </div>
                </div>
                <div class="oe_structure"/>
            </div>
        </t>
    </template>
</odoo>
