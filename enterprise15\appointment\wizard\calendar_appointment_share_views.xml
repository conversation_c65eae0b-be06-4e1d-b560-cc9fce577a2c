<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="calendar_appointment_share_view_form" model="ir.ui.view">
        <field name="name">calendar.appointment.share.view.form</field>
        <field name="model">calendar.appointment.share</field>
        <field name="arch" type="xml">
            <form string="Link Generator" create="false">
                <sheet>
                    <group>
                        <field name="appointment_type_ids" widget="many2many_tags" options="{'no_create': True}"/>
                        <field name="suggested_employee_ids" invisible="1"/>
                        <field name="appointment_type_count" invisible="1"/>
                        <field name="employee_ids" widget="many2many_tags" domain="[('id','in',suggested_employee_ids)]" attrs="{'invisible': [('appointment_type_count', '&gt;', 1)]}" options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="share_link" widget="CopyClipboardChar" readonly="1" attrs="{'invisible': [('appointment_type_count', '&lt;', 1)]}"/>
                    </group>
                </sheet>
                <footer>
                    <button string="Close" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="calendar_appointment_insert_share_view_form" model="ir.ui.view">
        <field name="name">calendar.appointment.share.view.form</field>
        <field name="model">calendar.appointment.share</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="calendar_appointment_share_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='share_link']" position="replace"></xpath>
            <xpath expr="//sheet">
                <field name="share_link" class="d-none o_appointement_share_link" readonly="1"/>
            </xpath>
            <xpath expr="//footer" position="replace">
                <footer>
                    <button string="Insert link" class="btn btn-primary o_share_link_save"/>
                    <button string="Discard" class="btn btn-secondary o_share_link_discard"/>
                </footer>
            </xpath>
        </field>
    </record>
</odoo>
