# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON>il <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "<span class=\"fa fa-warning\" title=\"Invalid minimum approvals\"/>"
msgstr "<span class=\"fa fa-warning\" title=\"الحد الأدنى من الموافقات غير صالح \"/>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>From: </span>"
msgstr "<span>من:</span>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>to: </span>"
msgstr "<span>إلى:</span>"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__active
msgid "Active"
msgstr "نشط"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: approvals
#: model:ir.model,name:approvals.model_mail_activity
msgid "Activity"
msgstr "النشاط "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_product
#: model:ir.model.fields,help:approvals.field_approval_request__has_product
msgid "Additional products that should be specified on the request."
msgstr "المنتجات الإضافية التي يجب تحديدها في الطلب. "

#. module: approvals
#: model:res.groups,name:approvals.group_approval_manager
msgid "Administrator"
msgstr "المدير "

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_all
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_all
msgid "All Approvals"
msgstr "كافة الموافقات "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__approval_type
#: model:ir.model.fields,help:approvals.field_approval_request__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""
"يتيح لك تحديد أي المستندات سوف ترغب في إنشائها بمجرد أن تتم الموافقة على "
"الطلب "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__amount
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Amount"
msgstr "المبلغ "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_reference
#: model:ir.model.fields,help:approvals.field_approval_request__has_reference
msgid "An additional reference that should be specified on the request."
msgstr "مرجع إضافي عليك تحديده في الطلب. "

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "An user may not be in the approver list multiple times."
msgstr "لا يمكن أن يكون المستخدم في قائمة المُعتمِدين أكثر من مرة. "

#. module: approvals
#: model:mail.activity.type,name:approvals.mail_activity_data_approval
msgid "Approval"
msgstr "الموافقة "

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category
msgid "Approval Category"
msgstr "فئة الموافقة "

#. module: approvals
#: model:ir.model,name:approvals.model_approval_request
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__approval_request_id
msgid "Approval Request"
msgstr "طلب موافقة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__name
msgid "Approval Subject"
msgstr "موضوع الموافقة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_type
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_type
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approval Type"
msgstr "نوع الموافقة"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category_approver
msgid "Approval Type Approver"
msgstr "مُعتمِد نوع الموافقة "

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_root
msgid "Approvals"
msgstr "الموافقات"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action
#: model:ir.ui.menu,name:approvals.approvals_category_menu_config
msgid "Approvals Types"
msgstr "أنواع الموافقات "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Approvals Types Image"
msgstr "صورة أنواع الموافقات "

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_to_review
msgid "Approvals to Review"
msgstr "الموافقات بانتظار المراجعة "

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review_category
msgid "Approvals to review"
msgstr "الموافقات بانتظار المراجعة "

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Approve"
msgstr "موافقة"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__approved
msgid "Approved"
msgstr "معتمد"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_approver
#: model:res.groups,name:approvals.group_approval_user
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver"
msgstr "مُعتَمِد "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__user_ids
msgid "Approver Users"
msgstr "المستخدِمين المُعتَمِدين "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver(s)"
msgstr "المُعتمِد (المُعتمِدين) "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approver_ids
#: model:ir.model.fields,field_description:approvals.field_approval_request__approver_ids
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approvers"
msgstr "المُعتمِدين "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_search
msgid "Archived"
msgstr "مؤرشف"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Attach Document"
msgstr "إرفاق مستند"

#. module: approvals
#: model:ir.model,name:approvals.model_ir_attachment
msgid "Attachment"
msgstr "مرفق "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,field_description:approvals.field_approval_request__automated_sequence
msgid "Automated Sequence?"
msgstr "التسلسل المؤتمت؟ "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Back To Draft"
msgstr "العودة إلى حالة المسودة "

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_borrow_items
msgid "Borrow Items"
msgstr "استعارة العناصر "

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_business_trip
msgid "Business Trip"
msgstr "رحلة عمل "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__can_edit
msgid "Can Edit"
msgstr "بإمكانه التحرير "

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__cancel
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Cancel"
msgstr "إلغاء "

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_car_rental_application
msgid "Car Rental Application"
msgstr "تطبيق تأجير السيارات "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_id
msgid "Category"
msgstr "الفئة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_code
msgid "Code"
msgstr "الكود"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__company_id
msgid "Company"
msgstr "الشركة "

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_config
msgid "Configuration"
msgstr "التهيئة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__partner_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Contact"
msgstr "جهة الاتصال"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_contract_approval
msgid "Contract Approval"
msgstr "الموافقة على جهة الاتصال "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_product_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"لا يمكن التحويل بين وحدات القياس إلا إذا كانت تنتمي لنفس الفئة. سيتم إجراء "
"التحويل بناءً على النسب."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action_new_request
msgid "Dashboard"
msgstr "لوحة البيانات "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Date"
msgstr "التاريخ"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_confirmed
msgid "Date Confirmed"
msgstr "تم تأكيد التاريخ "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_end
msgid "Date end"
msgstr "تاريخ النهاية "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_start
msgid "Date start"
msgstr "تاريخ البداية "

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Deadline"
msgstr "الموعد النهائي"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Delete"
msgstr "حذف"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__description
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__description
#: model:ir.model.fields,field_description:approvals.field_approval_request__reason
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Description"
msgstr "الوصف"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_request__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Document"
msgstr "المستند"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__requirer_document
#: model:ir.model.fields,field_description:approvals.field_approval_request__requirer_document
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Documents"
msgstr "المستندات"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Dropdown menu"
msgstr "القائمة المنسدلة"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "E.g: Expenses Paris business trip"
msgstr "مثال: نفقات رحلة عمل إلى باريس "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Edit Request"
msgstr "تحرير الطلب "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__manager_approval
msgid "Employee's Manager"
msgstr "مدير الموظف "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__existing_request_user_ids
msgid "Existing Request User"
msgstr "مستخدِم طلب موجود بالفعل "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__existing_user_ids
msgid "Existing User"
msgstr "مستخدِم موجود بالفعل "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Fields"
msgstr "الحقول"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome، مثال: fa-tasks. "

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_general_approval
msgid "General Approval"
msgstr "الموافقة العامة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_access_to_request
msgid "Has Access To Request"
msgstr "يملك صلاحية الوصول إلى الطلب "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_amount
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_amount
msgid "Has Amount"
msgstr "يحتوي على المبلغ "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_partner
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_partner
msgid "Has Contact"
msgstr "يحتوي على جهة الاتصال "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_date
msgid "Has Date"
msgstr "يحتوي على التاريخ "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_location
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_location
msgid "Has Location"
msgstr "يحتوي على الموقع "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_message
msgid "Has Message"
msgstr "يحتوي على الرسالة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_payment_method
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_payment_method
msgid "Has Payment"
msgstr "يحتوي على الدفع "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_period
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_period
msgid "Has Period"
msgstr "يحتوي على الفترة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_product
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_product
msgid "Has Product"
msgstr "يحتوي على المنتج "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_quantity
msgid "Has Quantity"
msgstr "يحتوي على الكمية "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_reference
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_reference
msgid "Has Reference"
msgstr "يحتوي على المرجع "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__manager_approval
msgid ""
"How the employee's manager interacts with this type of approval.\n"
"\n"
"        Empty: do nothing\n"
"        Is Approver: the employee's manager will be in the approver list\n"
"        Is Required Approver: the employee's manager will be required to approve the request.\n"
"    "
msgstr ""
"كيفية تفاعل مدير الموظف مع هذا النوع من الموافقات.\n"
"\n"
"        فارغ: لا يفعل شيئاً\n"
"        مُعتمِد: سوف يكون مدير الموظف في قائمة المُعتمِدين\n"
"        مُعتمِد مُلزَم: سوف يكون مدير الموظف ملزماً بالموافقة على الطلب.\n"
"    "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_category__id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__id
#: model:ir.model.fields,field_description:approvals.field_approval_request__id
msgid "ID"
msgstr "المُعرف"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,help:approvals.field_approval_request__automated_sequence
msgid ""
"If checked, the Approval Requests will have an automated generated name "
"based on the given code."
msgstr ""
"إذا كان محدداً، سوف يكون لطلب الموافقة اسم عام تلقائي مبني على الكود المعطى."
" "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__image
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_image
msgid "Image"
msgstr "صورة"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum
msgid "Invalid Minimum"
msgstr "الحد الأدنى غير صالح "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum_warning
msgid "Invalid Minimum Warning"
msgstr "تحذير بعدم صلاحية الحد الأدنى "

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__approver
msgid "Is Approver"
msgstr "مُعتمِد "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_is_follower
msgid "Is Follower"
msgstr "متابع "

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__required
msgid "Is Required Approver"
msgstr "مُعتمِد مُلزَم "

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_job_referral_award
msgid "Job Referral Award"
msgstr "جائزة إحالة وظيفة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_product_line____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_request____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "Let's go to the"
msgstr "فلنذهب إلى "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__location
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Location"
msgstr "الموقع "

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Log"
msgstr "سجل "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_manager
msgid "Manager"
msgstr "المدير"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_minimum
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_minimum
msgid "Minimum Approval"
msgstr "الحد الأدنى للموافقة "

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid ""
"Minimum Approval must be equal or superior to the sum of required Approvers."
msgstr ""
"يجب أن يكون الحد الأدنى للموافقة مساوياً أو أعلى من مجموع الموافقات "
"المطلوبة. "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_approval_menu
msgid "My Approvals"
msgstr "موافقاتي "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Approvals to Review"
msgstr "موافقاتي بانتظار المراجعة "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Request"
msgstr "طلبي "

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action
#: model:ir.ui.menu,name:approvals.approvals_request_menu_my
msgid "My Requests"
msgstr "طلباتي"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__name
msgid "Name"
msgstr "الاسم"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__new
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__new
#, python-format
msgid "New"
msgstr "جديد"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_category_menu_new
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "New Request"
msgstr "طلب جديد"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "No Approvals"
msgstr "لا توجد موافقات "

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
msgid "No Approvals Requests"
msgstr "لا توجد طلبات موافقات "

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review_category
msgid "No new approvals to review"
msgstr "لا توجد موافقات جديدة لمراجعتها "

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__no
msgid "None"
msgstr "لا شيء"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__attachment_number
msgid "Number of Attachments"
msgstr "عدد المرفقات"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__request_to_validate_count
msgid "Number of requests to validate"
msgstr "عدد الطلبات بانتظار التصديق "

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__optional
msgid "Optional"
msgstr "اختياري "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Options"
msgstr "خيارات"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Payment"
msgstr "دفعة"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_payment_application
msgid "Payment Application"
msgstr "تطبيق الدفع "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Period"
msgstr "الفترة"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_procurement
msgid "Procurement"
msgstr "الشراء "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Product"
msgstr "المنتج"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_product_line
#: model:ir.model.fields,field_description:approvals.field_approval_request__product_line_ids
msgid "Product Line"
msgstr "سلسلة المنتج "

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_product_variant
msgid "Product Variants"
msgstr "متغيرات المنتج "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_id
#: model:ir.ui.menu,name:approvals.approvals_menu_product
#: model:ir.ui.menu,name:approvals.approvals_menu_product_template
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_kanban_mobile_view
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree_independent
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Products"
msgstr "المنتجات"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__quantity
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Quantity"
msgstr "الكمية"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__reference
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Reference"
msgstr "المرجع "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_id
msgid "Reference Sequence"
msgstr "تسلسل المرجع "

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Refuse"
msgstr "رفض"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__refused
msgid "Refused"
msgstr "مرفوض"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__request_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Request"
msgstr "طلب"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.res_users_view_form
msgid "Request Approval"
msgstr "طلب الموافقة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_owner_id
msgid "Request Owner"
msgstr "صاحب الطلب "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_status
msgid "Request Status"
msgstr "حالة الطلب "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__required
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__required
msgid "Required"
msgstr "مطلوب"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence
#, python-format
msgid "Sequence"
msgstr "التسلسل "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "Start date should precede the end date."
msgstr "يجب أن يسبق تاريخ البدء تاريخ الانتهاء. "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__status
msgid "Status"
msgstr "الحالة"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Submit"
msgstr "إرسال"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__pending
msgid "Submitted"
msgstr "تم الإرسال"

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_user
msgid "The user will be able to see approvals created by himself."
msgstr "سوف يتمكن المستخدم من رؤية الموافقات التي قام بإنشائها بنفسه. "

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_manager
msgid "The user will have access to the approvals configuration."
msgstr "سوف يكون للمستخدم صلاحية الوصول إلى ضبط تهيئة الموافقات. "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no manager "
"linked to your employee profile."
msgstr ""
"هذا الطلب بحاجة إلى الموافقة عليه من قِبَل مديرك. لا يوجد مدير مرتبط بملفك "
"التعريفي. "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no user linked "
"to your manager."
msgstr ""
"هذا الطلب بحاجة إلى الموافقة عليه من قِبَل مديرك. لا يوجد مستخدم مرتبط "
"بمديرك. "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. Your manager is not in "
"the approvers list."
msgstr ""
"هذا الطلب بحاجة إلى الموافقة عليه من قِبَل مديرك. مديرك غير موجود في قائمة "
"المُعتمِدين. "

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__pending
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__pending
#, python-format
msgid "To Approve"
msgstr "بانتظار الموافقة "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "To Review:"
msgstr "بانتظار المراجعة: "

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__new
msgid "To Submit"
msgstr "للإرسال"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى المسجل."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_id
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل غير المقروءة "

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:approvals.field_approval_approver__user_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__user_id
#, python-format
msgid "User"
msgstr "المستخدم"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__user_status
msgid "User Status"
msgstr "حالة المستخدم "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Withdraw"
msgstr "سحب "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"You cannot assign the same approver multiple times on the same request."
msgstr "لا يمكنك تعيين نفس مانح الموافقة عدة مرات في نفس الطلب. "

#. module: approvals
#: code:addons/approvals/models/ir_attachment.py:0
#, python-format
msgid ""
"You cannot unlink an attachment which is linked to a validated, refused or "
"cancelled approval request."
msgstr ""
"لا يمكنك إلغاء ربط مرفق مرتبط بطلب موافقة قد تم تصديقه أو رفضه أو إلغاؤه. "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to add at least %s approvers to confirm your request."
msgstr "عليك إضافة %s مُعتمِدين على الأقل. "

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to attach at least one document."
msgstr "عليك إرفاق مستند واحد على الأقل. "

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "Your minimum approval exceeds the total of default approvers."
msgstr "الحد الأدنى للموافقة يتخطى الإجمالي الافتراضي للمُعتمِدين. "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "e.g. Brussels"
msgstr "مثال: براسيلز "

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "e.g. Procurement"
msgstr "مثال: الشراء "

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "menu"
msgstr "القائمة "

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "new request"
msgstr "طلب جديد "
