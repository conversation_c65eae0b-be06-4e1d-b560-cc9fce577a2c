# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals
# 
# Translators:
# <PERSON><PERSON><PERSON> <i<PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON> <durdi<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# hrvo<PERSON> si<PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "<span class=\"fa fa-warning\" title=\"Invalid minimum approvals\"/>"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>From: </span>"
msgstr "<span>od: </span>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>to: </span>"
msgstr "<span>za: </span>"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction
msgid "Action Needed"
msgstr "Potrebna dodatna radnja"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__active
msgid "Active"
msgstr "Aktivan"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: approvals
#: model:ir.model,name:approvals.model_mail_activity
msgid "Activity"
msgstr "Aktivnost"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoracija iznimke aktivnosti"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona tipa aktivnosti"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_product
#: model:ir.model.fields,help:approvals.field_approval_request__has_product
msgid "Additional products that should be specified on the request."
msgstr ""

#. module: approvals
#: model:res.groups,name:approvals.group_approval_manager
msgid "Administrator"
msgstr "Administrator"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_all
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_all
msgid "All Approvals"
msgstr ""

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__approval_type
#: model:ir.model.fields,help:approvals.field_approval_request__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__amount
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Amount"
msgstr "Iznos"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_reference
#: model:ir.model.fields,help:approvals.field_approval_request__has_reference
msgid "An additional reference that should be specified on the request."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "An user may not be in the approver list multiple times."
msgstr ""

#. module: approvals
#: model:mail.activity.type,name:approvals.mail_activity_data_approval
msgid "Approval"
msgstr "Odobrenje"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category
msgid "Approval Category"
msgstr "Kategorija odobrenja"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_request
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__approval_request_id
msgid "Approval Request"
msgstr "Zahtjev za odobrenjem"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__name
msgid "Approval Subject"
msgstr "Naslov odobrenja"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_type
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_type
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approval Type"
msgstr "Tip odobrenja"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category_approver
msgid "Approval Type Approver"
msgstr ""

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_root
msgid "Approvals"
msgstr "Odobrenja"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action
#: model:ir.ui.menu,name:approvals.approvals_category_menu_config
msgid "Approvals Types"
msgstr "Tipovi odobrenja"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Approvals Types Image"
msgstr ""

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_to_review
msgid "Approvals to Review"
msgstr ""

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review_category
msgid "Approvals to review"
msgstr ""

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Approve"
msgstr "Odobri"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__approved
msgid "Approved"
msgstr "Odobreno"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_approver
#: model:res.groups,name:approvals.group_approval_user
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__user_ids
msgid "Approver Users"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver(s)"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approver_ids
#: model:ir.model.fields,field_description:approvals.field_approval_request__approver_ids
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approvers"
msgstr "Odobravatelji"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Attach Document"
msgstr "Priloži dokument"

#. module: approvals
#: model:ir.model,name:approvals.model_ir_attachment
msgid "Attachment"
msgstr "Prilog"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_attachment_count
msgid "Attachment Count"
msgstr "Broj priloga"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,field_description:approvals.field_approval_request__automated_sequence
msgid "Automated Sequence?"
msgstr "Automatska sekvenca?"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Back To Draft"
msgstr "Nazad u nacrt"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_borrow_items
msgid "Borrow Items"
msgstr "Posudba stvari"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_business_trip
msgid "Business Trip"
msgstr "Poslovni put"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__can_edit
msgid "Can Edit"
msgstr "Može uređivati"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__cancel
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Cancel"
msgstr "Odustani"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_car_rental_application
msgid "Car Rental Application"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_id
msgid "Category"
msgstr "Kategorija"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_code
msgid "Code"
msgstr "Šifra"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__company_id
msgid "Company"
msgstr "Tvrtka"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_config
msgid "Configuration"
msgstr "Postava"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__partner_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Contact"
msgstr "Kontakt"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_contract_approval
msgid "Contract Approval"
msgstr ""

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_product_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konverzija jedinica mjere može se vršiti samo za jedinice mjere koje "
"pripadaju istoj kategoriji. Konverzija će se izvršiti temeljem omjera "
"jedinica mjere."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action_new_request
msgid "Dashboard"
msgstr "Nadzorna ploča"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Date"
msgstr "Datum"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_confirmed
msgid "Date Confirmed"
msgstr "Datum potvrđivanja"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_end
msgid "Date end"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_start
msgid "Date start"
msgstr ""

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Deadline"
msgstr "Krajnji rok"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Delete"
msgstr "Obriši"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__description
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__description
#: model:ir.model.fields,field_description:approvals.field_approval_request__reason
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Description"
msgstr "Opis"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_request__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Document"
msgstr "Dokument"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__requirer_document
#: model:ir.model.fields,field_description:approvals.field_approval_request__requirer_document
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Documents"
msgstr "Dokumenti"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Dropdown menu"
msgstr "Padajući izbornik"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "E.g: Expenses Paris business trip"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Edit Request"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__manager_approval
msgid "Employee's Manager"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__existing_request_user_ids
msgid "Existing Request User"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__existing_user_ids
msgid "Existing User"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Fields"
msgstr "Polja"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_follower_ids
msgid "Followers"
msgstr "Pratitelji"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratitelji (Partneri)"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikona npr. fa-tasks"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_general_approval
msgid "General Approval"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_access_to_request
msgid "Has Access To Request"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_amount
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_amount
msgid "Has Amount"
msgstr "Ima iznos"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_partner
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_partner
msgid "Has Contact"
msgstr "Ima kontakt"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_date
msgid "Has Date"
msgstr "Ima datum"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_location
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_location
msgid "Has Location"
msgstr "Ima lokaciju"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_message
msgid "Has Message"
msgstr "Ima poruku"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_payment_method
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_payment_method
msgid "Has Payment"
msgstr "Ima uplatu"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_period
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_period
msgid "Has Period"
msgstr "Ima period"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_product
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_product
msgid "Has Product"
msgstr "Ima proizvod"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_quantity
msgid "Has Quantity"
msgstr "Ima količinu"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_reference
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_reference
msgid "Has Reference"
msgstr "Ima referencu"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__manager_approval
msgid ""
"How the employee's manager interacts with this type of approval.\n"
"\n"
"        Empty: do nothing\n"
"        Is Approver: the employee's manager will be in the approver list\n"
"        Is Required Approver: the employee's manager will be required to approve the request.\n"
"    "
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_category__id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__id
#: model:ir.model.fields,field_description:approvals.field_approval_request__id
msgid "ID"
msgstr "ID"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona za prikaz iznimki."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ako je označeno, nove poruke zahtijevaju Vašu pažnju."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Ako je označeno neke poruke mogu imati grešku u dostavi."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,help:approvals.field_approval_request__automated_sequence
msgid ""
"If checked, the Approval Requests will have an automated generated name "
"based on the given code."
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__image
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_image
msgid "Image"
msgstr "Slika"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum
msgid "Invalid Minimum"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum_warning
msgid "Invalid Minimum Warning"
msgstr ""

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__approver
msgid "Is Approver"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_is_follower
msgid "Is Follower"
msgstr "Je pratitelj"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__required
msgid "Is Required Approver"
msgstr ""

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_job_referral_award
msgid "Job Referral Award"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_product_line____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_request____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "Let's go to the"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__location
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Location"
msgstr "Lokacija"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Log"
msgstr "Zapisnik"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavni prilog"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_manager
msgid "Manager"
msgstr "Voditelj"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error
msgid "Message Delivery error"
msgstr "Greška pri isporuci poruke"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_minimum
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_minimum
msgid "Minimum Approval"
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid ""
"Minimum Approval must be equal or superior to the sum of required Approvers."
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Rok za moju aktivnost"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_approval_menu
msgid "My Approvals"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Approvals to Review"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Request"
msgstr "Moj zahtjev"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action
#: model:ir.ui.menu,name:approvals.approvals_request_menu_my
msgid "My Requests"
msgstr "Moji zahtjevi"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__name
msgid "Name"
msgstr "Naziv"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__new
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__new
#, python-format
msgid "New"
msgstr "Novi"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_category_menu_new
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "New Request"
msgstr "Novi zahtjev"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok slijedeće aktivnosti"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_summary
msgid "Next Activity Summary"
msgstr "Sažetak sljedeće aktivnosti"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "No Approvals"
msgstr ""

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
msgid "No Approvals Requests"
msgstr ""

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review_category
msgid "No new approvals to review"
msgstr ""

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__no
msgid "None"
msgstr "Ništa"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__attachment_number
msgid "Number of Attachments"
msgstr "Broj privitaka"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error_counter
msgid "Number of errors"
msgstr "Broj grešaka"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtijevaju aktivnost"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Broj poruka sa greškama pri isporuci"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__request_to_validate_count
msgid "Number of requests to validate"
msgstr ""

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__optional
msgid "Optional"
msgstr "Opcionalno"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Options"
msgstr "Opcije"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Payment"
msgstr "Plaćanje"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_payment_application
msgid "Payment Application"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Period"
msgstr "Period"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_procurement
msgid "Procurement"
msgstr "Nabava"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Product"
msgstr "Proizvod"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_product_line
#: model:ir.model.fields,field_description:approvals.field_approval_request__product_line_ids
msgid "Product Line"
msgstr ""

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_product_variant
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_id
#: model:ir.ui.menu,name:approvals.approvals_menu_product
#: model:ir.ui.menu,name:approvals.approvals_menu_product_template
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_kanban_mobile_view
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree_independent
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Products"
msgstr "Proizvodi"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__quantity
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Quantity"
msgstr "Količina"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__reference
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Reference"
msgstr "Vezna oznaka"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_id
msgid "Reference Sequence"
msgstr "Vezana sekvenca"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Refuse"
msgstr "Odbiti"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__refused
msgid "Refused"
msgstr "Odbijen"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__request_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Request"
msgstr "Zahtjev"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.res_users_view_form
msgid "Request Approval"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_owner_id
msgid "Request Owner"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_status
msgid "Request Status"
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__required
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__required
msgid "Required"
msgstr "Obavezno"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_user_id
msgid "Responsible User"
msgstr "Odgovorna osoba"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence
#, python-format
msgid "Sequence"
msgstr "Sekvenca"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "Start date should precede the end date."
msgstr ""

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__status
msgid "Status"
msgstr "Status"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status po aktivnostima\n"
"U kašnjenju: Datum aktivnosti je već prošao\n"
"Danas: Datum aktivnosti je danas\n"
"Planirano: Buduće aktivnosti."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Submit"
msgstr "Potvrdi"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__pending
msgid "Submitted"
msgstr "Poslao"

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_user
msgid "The user will be able to see approvals created by himself."
msgstr ""

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_manager
msgid "The user will have access to the approvals configuration."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no manager "
"linked to your employee profile."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no user linked "
"to your manager."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. Your manager is not in "
"the approvers list."
msgstr ""

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__pending
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__pending
#, python-format
msgid "To Approve"
msgstr "Za odobriti"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "To Review:"
msgstr ""

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__new
msgid "To Submit"
msgstr "Za slanje"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Vrsta aktivnosti iznimke na zapisu."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_id
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:approvals.field_approval_approver__user_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__user_id
#, python-format
msgid "User"
msgstr "Korisnik"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__user_status
msgid "User Status"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Withdraw"
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"You cannot assign the same approver multiple times on the same request."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/ir_attachment.py:0
#, python-format
msgid ""
"You cannot unlink an attachment which is linked to a validated, refused or "
"cancelled approval request."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to add at least %s approvers to confirm your request."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to attach at least one document."
msgstr ""

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "Your minimum approval exceeds the total of default approvers."
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "e.g. Brussels"
msgstr ""

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "e.g. Procurement"
msgstr ""

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "menu"
msgstr ""

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "new request"
msgstr ""
