# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "<span class=\"fa fa-warning\" title=\"Invalid minimum approvals\"/>"
msgstr "<span class=\"fa fa-warning\" title=\"Approvazioni minime non valide\"/>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>From: </span>"
msgstr "<span>Dal: </span>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>to: </span>"
msgstr "<span>al: </span>"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__active
msgid "Active"
msgstr "Attivo"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_ids
msgid "Activities"
msgstr "Attività"

#. module: approvals
#: model:ir.model,name:approvals.model_mail_activity
msgid "Activity"
msgstr "Attività"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorazione eccezione attività"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_state
msgid "Activity State"
msgstr "Stato attività"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona tipo di attività"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_product
#: model:ir.model.fields,help:approvals.field_approval_request__has_product
msgid "Additional products that should be specified on the request."
msgstr "Prodotti aggiuntivi che devono essere specificati nella richiesta."

#. module: approvals
#: model:res.groups,name:approvals.group_approval_manager
msgid "Administrator"
msgstr "Amministratore"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_all
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_all
msgid "All Approvals"
msgstr "Tutte le approvazioni"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__approval_type
#: model:ir.model.fields,help:approvals.field_approval_request__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""
"Consente di definire quali documenti si desidera creare dopo l'approvazione "
"della richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__amount
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Amount"
msgstr "Importo"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_reference
#: model:ir.model.fields,help:approvals.field_approval_request__has_reference
msgid "An additional reference that should be specified on the request."
msgstr "Riferimento aggiuntivo che deve essere specificato nella richiesta."

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "An user may not be in the approver list multiple times."
msgstr ""
"Un utente non può essere presente più volte nella lista degli approvatori."

#. module: approvals
#: model:mail.activity.type,name:approvals.mail_activity_data_approval
msgid "Approval"
msgstr "Approvazione"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category
msgid "Approval Category"
msgstr "Categoria approvazione"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_request
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__approval_request_id
msgid "Approval Request"
msgstr "Richiesta di approvazione"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__name
msgid "Approval Subject"
msgstr "Oggetto approvazione"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_type
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_type
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approval Type"
msgstr "Tipo di approvazione"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category_approver
msgid "Approval Type Approver"
msgstr "Approvatore tipo di approvazione"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_root
msgid "Approvals"
msgstr "Approvazioni"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action
#: model:ir.ui.menu,name:approvals.approvals_category_menu_config
msgid "Approvals Types"
msgstr "Tipi di approvazione"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Approvals Types Image"
msgstr "Immagine tipi di approvazione"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_to_review
msgid "Approvals to Review"
msgstr "Approvazioni da esaminare"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review_category
msgid "Approvals to review"
msgstr "Approvazioni da esaminare"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Approve"
msgstr "Approva"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__approved
msgid "Approved"
msgstr "Approvata"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_approver
#: model:res.groups,name:approvals.group_approval_user
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver"
msgstr "Approvatore"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__user_ids
msgid "Approver Users"
msgstr "Utenti approvatori"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver(s)"
msgstr "Approvatori"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approver_ids
#: model:ir.model.fields,field_description:approvals.field_approval_request__approver_ids
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approvers"
msgstr "Approvatori"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_search
msgid "Archived"
msgstr "In archivio"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Attach Document"
msgstr "Allega documento"

#. module: approvals
#: model:ir.model,name:approvals.model_ir_attachment
msgid "Attachment"
msgstr "Allegato"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,field_description:approvals.field_approval_request__automated_sequence
msgid "Automated Sequence?"
msgstr "Sequenza automatica?"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Back To Draft"
msgstr "Ritorna a bozza"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_borrow_items
msgid "Borrow Items"
msgstr "Prestito oggetti"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_business_trip
msgid "Business Trip"
msgstr "Viaggio di lavoro"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__can_edit
msgid "Can Edit"
msgstr "Può modificare"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__cancel
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_car_rental_application
msgid "Car Rental Application"
msgstr "Applicazione noleggio auto"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_id
msgid "Category"
msgstr "Categoria"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_code
msgid "Code"
msgstr "Codice"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__company_id
msgid "Company"
msgstr "Azienda"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_config
msgid "Configuration"
msgstr "Configurazione"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__partner_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Contact"
msgstr "Contatto"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_contract_approval
msgid "Contract Approval"
msgstr "Approvazione contratto"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_product_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Le conversioni tra unità di misura possono avvenire solo se appartengono "
"alla stessa categoria. La conversione verrà effettuata in base alle "
"proporzioni."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action_new_request
msgid "Dashboard"
msgstr "Bacheca"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Date"
msgstr "Data"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_confirmed
msgid "Date Confirmed"
msgstr "Data confermata"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_end
msgid "Date end"
msgstr "Data fine"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_start
msgid "Date start"
msgstr "Data inizio"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Deadline"
msgstr "Scadenza"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Delete"
msgstr "Elimina"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__description
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__description
#: model:ir.model.fields,field_description:approvals.field_approval_request__reason
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Description"
msgstr "Descrizione"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_request__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Document"
msgstr "Documento"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__requirer_document
#: model:ir.model.fields,field_description:approvals.field_approval_request__requirer_document
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Documents"
msgstr "Documenti"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Dropdown menu"
msgstr "Menù a discesa"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "E.g: Expenses Paris business trip"
msgstr "Es. Spese viaggio di lavoro a Parigi"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Edit Request"
msgstr "Modifica richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__manager_approval
msgid "Employee's Manager"
msgstr "Supervisore dipendente"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__existing_request_user_ids
msgid "Existing Request User"
msgstr "Utente richiesta esistente"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__existing_user_ids
msgid "Existing User"
msgstr "Utente esistente"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Fields"
msgstr "Campi"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome es. fa-tasks"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_general_approval
msgid "General Approval"
msgstr "Approvazione generale"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_access_to_request
msgid "Has Access To Request"
msgstr "Ha accesso alla richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_amount
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_amount
msgid "Has Amount"
msgstr "Possiede importo"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_partner
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_partner
msgid "Has Contact"
msgstr "Possiede contatto"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_date
msgid "Has Date"
msgstr "Possiede data"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_location
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_location
msgid "Has Location"
msgstr "Possiede luogo"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_payment_method
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_payment_method
msgid "Has Payment"
msgstr "Possiede pagamento"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_period
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_period
msgid "Has Period"
msgstr "Possiede periodo"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_product
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_product
msgid "Has Product"
msgstr "Possiede prodotto"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_quantity
msgid "Has Quantity"
msgstr "Possiede quantità"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_reference
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_reference
msgid "Has Reference"
msgstr "Possiede riferimento"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__manager_approval
msgid ""
"How the employee's manager interacts with this type of approval.\n"
"\n"
"        Empty: do nothing\n"
"        Is Approver: the employee's manager will be in the approver list\n"
"        Is Required Approver: the employee's manager will be required to approve the request.\n"
"    "
msgstr ""
"Come il manager dell'impiegato interagisce con questo tipo di approvazione.\n"
"\n"
"Vuoto: non fare nulla\n"
"È approvatore: il manager del dipendente sarà nell'elenco degli approvatori\n"
"È un approvatore richiesto: il manager del dipendente dovrà approvare la richiesta."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_category__id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__id
#: model:ir.model.fields,field_description:approvals.field_approval_request__id
msgid "ID"
msgstr "ID"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona per indicare un'attività eccezione."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,help:approvals.field_approval_request__automated_sequence
msgid ""
"If checked, the Approval Requests will have an automated generated name "
"based on the given code."
msgstr ""
"Se selezionata, le richieste di approvazione avranno un nome generato "
"automaticamente in base al codice specificato."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__image
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_image
msgid "Image"
msgstr "Immagine"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum
msgid "Invalid Minimum"
msgstr "Minimo non valido"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum_warning
msgid "Invalid Minimum Warning"
msgstr "Avviso minimo non valido"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__approver
msgid "Is Approver"
msgstr "È approvatore"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__required
msgid "Is Required Approver"
msgstr "Approvatore necessario"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_job_referral_award
msgid "Job Referral Award"
msgstr "Premio referenza lavorativa"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_product_line____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_request____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "Let's go to the"
msgstr "Andiamo a"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__location
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Location"
msgstr "Luogo"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Log"
msgstr "Registra"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_manager
msgid "Manager"
msgstr "Supervisore"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_minimum
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_minimum
msgid "Minimum Approval"
msgstr "Approvazione minima"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid ""
"Minimum Approval must be equal or superior to the sum of required Approvers."
msgstr ""
"L'approvazione minima deve essere uguale o superiore alla somma degli "
"approvatori richiesti."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Scadenza mia attività"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_approval_menu
msgid "My Approvals"
msgstr "Le mie approvazioni"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Approvals to Review"
msgstr "Le miei approvazioni da esaminare"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Request"
msgstr "La mia richiesta"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action
#: model:ir.ui.menu,name:approvals.approvals_request_menu_my
msgid "My Requests"
msgstr "Le mie richieste"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__name
msgid "Name"
msgstr "Nome"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__new
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__new
#, python-format
msgid "New"
msgstr "Nuova"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_category_menu_new
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "New Request"
msgstr "Nuova richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Scadenza prossima attività"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_summary
msgid "Next Activity Summary"
msgstr "Riepilogo prossima attività"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_id
msgid "Next Activity Type"
msgstr "Tipologia prossima attività"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "No Approvals"
msgstr "Nessuna approvazione"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
msgid "No Approvals Requests"
msgstr "Nessuna richiesta di approvazione"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review_category
msgid "No new approvals to review"
msgstr "Nessuna nuova approvazione da esaminare"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__no
msgid "None"
msgstr "Nessuno"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__attachment_number
msgid "Number of Attachments"
msgstr "Numero di allegati"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__request_to_validate_count
msgid "Number of requests to validate"
msgstr "Numero di richieste da convalidare"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread_counter
msgid "Number of unread messages"
msgstr "Numero di messaggi non letti"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__optional
msgid "Optional"
msgstr "Opzionale"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Options"
msgstr "Opzioni"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Payment"
msgstr "Pagamento"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_payment_application
msgid "Payment Application"
msgstr "Domanda di pagamento"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Period"
msgstr "Periodo"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_procurement
msgid "Procurement"
msgstr "Approvvigionamento"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Product"
msgstr "Prodotto"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_product_line
#: model:ir.model.fields,field_description:approvals.field_approval_request__product_line_ids
msgid "Product Line"
msgstr "Riga prodotto"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_product_variant
msgid "Product Variants"
msgstr "Varianti prodotto"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_id
#: model:ir.ui.menu,name:approvals.approvals_menu_product
#: model:ir.ui.menu,name:approvals.approvals_menu_product_template
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_kanban_mobile_view
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree_independent
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Products"
msgstr "Prodotti"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__quantity
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Quantity"
msgstr "Quantità"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__reference
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Reference"
msgstr "Riferimento"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_id
msgid "Reference Sequence"
msgstr "Sequenza di riferimento"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Refuse"
msgstr "Respingi"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__refused
msgid "Refused"
msgstr "Respinta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__request_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Request"
msgstr "Richiesta"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.res_users_view_form
msgid "Request Approval"
msgstr "Approvazione richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_owner_id
msgid "Request Owner"
msgstr "Proprietario richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_status
msgid "Request Status"
msgstr "Stato richiesta"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__required
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__required
msgid "Required"
msgstr "Obbligatorio"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_user_id
msgid "Responsible User"
msgstr "Utente responsabile"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence
#, python-format
msgid "Sequence"
msgstr "Sequenza"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "Start date should precede the end date."
msgstr "La data di inizio deve precedere la data di fine."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__status
msgid "Status"
msgstr "Stato"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stato basato sulle attività\n"
"In ritardo: scadenza già superata\n"
"Oggi: attività in data odierna\n"
"Pianificato: attività future."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Submit"
msgstr "Invia"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__pending
msgid "Submitted"
msgstr "Inviata"

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_user
msgid "The user will be able to see approvals created by himself."
msgstr "L'utente potrà vedere le approvazioni create da se stesso."

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_manager
msgid "The user will have access to the approvals configuration."
msgstr "L'utente avrà accesso alla configurazione delle approvazioni."

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no manager "
"linked to your employee profile."
msgstr ""
"Questa richiesta deve essere approvata dal tuo manager. Non c'è nessun "
"manager collegato al tuo profilo di dipendente."

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no user linked "
"to your manager."
msgstr ""
"La tua approvazione minima supera il totale degli approvatori predefiniti."

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. Your manager is not in "
"the approvers list."
msgstr ""
"Questa richiesta deve essere approvata dal tuo manager. Il tuo manager non è"
" nella lista degli approvatori."

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__pending
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__pending
#, python-format
msgid "To Approve"
msgstr "Da approvare"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "To Review:"
msgstr "Da esaminare:"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__new
msgid "To Submit"
msgstr "Da inviare"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo di attività eccezione sul record."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_id
msgid "Unit of Measure"
msgstr "Unità di misura"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread
msgid "Unread Messages"
msgstr "Messaggi non letti"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Numero messaggi non letti"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:approvals.field_approval_approver__user_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__user_id
#, python-format
msgid "User"
msgstr "Utente"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__user_status
msgid "User Status"
msgstr "Stato utente"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Withdraw"
msgstr "Revoca"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"You cannot assign the same approver multiple times on the same request."
msgstr ""
"Impossibile assegnare più di una volta lo stesso approvatore per la medesima"
" richiesta."

#. module: approvals
#: code:addons/approvals/models/ir_attachment.py:0
#, python-format
msgid ""
"You cannot unlink an attachment which is linked to a validated, refused or "
"cancelled approval request."
msgstr ""
"Impossibile rimuovere il collegamento a un allegato collegato a una "
"richiesta di approvazione convalidata, respinta o annullata."

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to add at least %s approvers to confirm your request."
msgstr ""
"Per confermare la richiesta devono essere aggiunti almeno %s approvatori."

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to attach at least one document."
msgstr "Deve essere allegato almeno un documento."

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "Your minimum approval exceeds the total of default approvers."
msgstr ""
"La tua approvazione minima supera il totale degli approvatori predefiniti."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "e.g. Brussels"
msgstr "es. Bruxelles"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "e.g. Procurement"
msgstr "es. Approvvigionamento"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "menu"
msgstr "menu"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "new request"
msgstr "nuova richiesta"
