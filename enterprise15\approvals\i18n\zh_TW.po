# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "<span class=\"fa fa-warning\" title=\"Invalid minimum approvals\"/>"
msgstr "<span class=\"fa fa-warning\" title=\"最低批准數目無效\"/>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>From: </span>"
msgstr "<span>由： </span>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "<span>to: </span>"
msgstr "<span>至： </span>"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__active
msgid "Active"
msgstr "啟用"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_ids
msgid "Activities"
msgstr "活動"

#. module: approvals
#: model:ir.model,name:approvals.model_mail_activity
msgid "Activity"
msgstr "活動"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_product
#: model:ir.model.fields,help:approvals.field_approval_request__has_product
msgid "Additional products that should be specified on the request."
msgstr "應在請求中指定的其他產品。"

#. module: approvals
#: model:res.groups,name:approvals.group_approval_manager
msgid "Administrator"
msgstr "管理員"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_all
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_all
msgid "All Approvals"
msgstr "所有審批"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__approval_type
#: model:ir.model.fields,help:approvals.field_approval_request__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr "允許您定義請求獲得批准後要創建的文檔"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__amount
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Amount"
msgstr "總額"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_reference
#: model:ir.model.fields,help:approvals.field_approval_request__has_reference
msgid "An additional reference that should be specified on the request."
msgstr "應在申請中指定的一個附加參考。"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "An user may not be in the approver list multiple times."
msgstr "一個用戶不得多次出現在審批人名單中。"

#. module: approvals
#: model:mail.activity.type,name:approvals.mail_activity_data_approval
msgid "Approval"
msgstr "核准方式"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category
msgid "Approval Category"
msgstr "審批類別"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_request
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__approval_request_id
msgid "Approval Request"
msgstr "申請批准"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__name
msgid "Approval Subject"
msgstr "審批主題"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_type
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_type
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approval Type"
msgstr "批核類型"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category_approver
msgid "Approval Type Approver"
msgstr "批核類型批核者"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_root
msgid "Approvals"
msgstr "審批"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action
#: model:ir.ui.menu,name:approvals.approvals_category_menu_config
msgid "Approvals Types"
msgstr "批核類型"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Approvals Types Image"
msgstr "批核類型圖片"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_to_review
msgid "Approvals to Review"
msgstr "待複核的審批"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review_category
msgid "Approvals to review"
msgstr "待複核的審批"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Approve"
msgstr "批准"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__approved
msgid "Approved"
msgstr "已批准"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_approver
#: model:res.groups,name:approvals.group_approval_user
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver"
msgstr "審批人"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__user_ids
msgid "Approver Users"
msgstr "審批人用戶"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver(s)"
msgstr "審批人"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approver_ids
#: model:ir.model.fields,field_description:approvals.field_approval_request__approver_ids
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approvers"
msgstr "流程審批管理人"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_search
msgid "Archived"
msgstr "已歸檔"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Attach Document"
msgstr "附加文件"

#. module: approvals
#: model:ir.model,name:approvals.model_ir_attachment
msgid "Attachment"
msgstr "附件"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_attachment_count
msgid "Attachment Count"
msgstr "附件數"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,field_description:approvals.field_approval_request__automated_sequence
msgid "Automated Sequence?"
msgstr "自動化序列？"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Back To Draft"
msgstr "設為草稿"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_borrow_items
msgid "Borrow Items"
msgstr "借用項目"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_business_trip
msgid "Business Trip"
msgstr "商務旅行"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__can_edit
msgid "Can Edit"
msgstr "能編輯"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__cancel
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Cancel"
msgstr "取消"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_car_rental_application
msgid "Car Rental Application"
msgstr "租車應用程式"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_id
msgid "Category"
msgstr "類別"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_code
msgid "Code"
msgstr "代號"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__company_id
msgid "Company"
msgstr "公司"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_config
msgid "Configuration"
msgstr "配置"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__partner_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Contact"
msgstr "聯絡人"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_contract_approval
msgid "Contract Approval"
msgstr "合約審批"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_product_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "計量單位轉換只能在同一個類型之間進行。轉換根據比例"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_uid
msgid "Created by"
msgstr "創立者"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_date
msgid "Created on"
msgstr "建立於"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action_new_request
msgid "Dashboard"
msgstr "儀表板"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Date"
msgstr "日期"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_confirmed
msgid "Date Confirmed"
msgstr "確認日期"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_end
msgid "Date end"
msgstr "結束日期"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_start
msgid "Date start"
msgstr "開始日期"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Deadline"
msgstr "截止日期"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Delete"
msgstr "刪除"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__description
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__description
#: model:ir.model.fields,field_description:approvals.field_approval_request__reason
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Description"
msgstr "說明"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_request__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Document"
msgstr "文件"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__requirer_document
#: model:ir.model.fields,field_description:approvals.field_approval_request__requirer_document
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Documents"
msgstr "文件"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Dropdown menu"
msgstr "下拉選項"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "E.g: Expenses Paris business trip"
msgstr "例：巴黎商務旅行費用"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Edit Request"
msgstr "編輯請求"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__manager_approval
msgid "Employee's Manager"
msgstr "員工經理"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__existing_request_user_ids
msgid "Existing Request User"
msgstr "現有請求用戶"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__existing_user_ids
msgid "Existing User"
msgstr "現有用戶"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Fields"
msgstr "欄位"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_general_approval
msgid "General Approval"
msgstr "一般審批"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_access_to_request
msgid "Has Access To Request"
msgstr "有提交請求權限"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_amount
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_amount
msgid "Has Amount"
msgstr "有金額"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_partner
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_partner
msgid "Has Contact"
msgstr "有聯絡人"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_date
msgid "Has Date"
msgstr "有日期"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_location
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_location
msgid "Has Location"
msgstr "有位置"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_payment_method
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_payment_method
msgid "Has Payment"
msgstr "有付款"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_period
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_period
msgid "Has Period"
msgstr "有期間"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_product
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_product
msgid "Has Product"
msgstr "有產品"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_quantity
msgid "Has Quantity"
msgstr "有數量"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_reference
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_reference
msgid "Has Reference"
msgstr "有參考"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__manager_approval
msgid ""
"How the employee's manager interacts with this type of approval.\n"
"\n"
"        Empty: do nothing\n"
"        Is Approver: the employee's manager will be in the approver list\n"
"        Is Required Approver: the employee's manager will be required to approve the request.\n"
"    "
msgstr ""
"員工的經理如何與這種類型的批准進行互動。\n"
"\n"
"        空：不做任何事情\n"
"        是批核者：僱員的經理將在批准者列表中。\n"
"        是必須的批核者：僱員的經理將被要求批准該請求。\n"
"    "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_category__id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__id
#: model:ir.model.fields,field_description:approvals.field_approval_request__id
msgid "ID"
msgstr "ID"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,help:approvals.field_approval_request__automated_sequence
msgid ""
"If checked, the Approval Requests will have an automated generated name "
"based on the given code."
msgstr "如果選擇，批准請求將具有基於給定代碼的自動生成的名稱。"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__image
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_image
msgid "Image"
msgstr "圖像"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum
msgid "Invalid Minimum"
msgstr "無效的最小值"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum_warning
msgid "Invalid Minimum Warning"
msgstr "無效最小值警告"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__approver
msgid "Is Approver"
msgstr "審批人"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__required
msgid "Is Required Approver"
msgstr "是必需的審批人"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_job_referral_award
msgid "Job Referral Award"
msgstr "工作推薦獎勵"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_product_line____last_update
#: model:ir.model.fields,field_description:approvals.field_approval_request____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "Let's go to the"
msgstr "讓我們前往"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__location
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Location"
msgstr "地點"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Log"
msgstr "日誌"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_manager
msgid "Manager"
msgstr "管理員"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_ids
msgid "Messages"
msgstr "訊息"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_minimum
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_minimum
msgid "Minimum Approval"
msgstr "最小審批"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid ""
"Minimum Approval must be equal or superior to the sum of required Approvers."
msgstr "最低限度的批准必須等於或高於所需批准者的總和。"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_approval_menu
msgid "My Approvals"
msgstr "我的審批"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Approvals to Review"
msgstr "我的待複核審批"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Request"
msgstr "我的申請"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action
#: model:ir.ui.menu,name:approvals.approvals_request_menu_my
msgid "My Requests"
msgstr "我的需求"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__name
msgid "Name"
msgstr "名稱"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__new
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__new
#, python-format
msgid "New"
msgstr "新增"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_category_menu_new
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "New Request"
msgstr "新增請求"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "No Approvals"
msgstr "沒有審批"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
msgid "No Approvals Requests"
msgstr "沒有批准請求"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review_category
msgid "No new approvals to review"
msgstr "沒有待複核審批"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__no
msgid "None"
msgstr "無"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__attachment_number
msgid "Number of Attachments"
msgstr "附件數量"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要處理的消息數量"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__request_to_validate_count
msgid "Number of requests to validate"
msgstr "要驗證的申請數量"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息的數量"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__optional
msgid "Optional"
msgstr "選填"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Options"
msgstr "選項"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Payment"
msgstr "付款"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_payment_application
msgid "Payment Application"
msgstr "付款申請"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Period"
msgstr "會計期間"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_procurement
msgid "Procurement"
msgstr "補貨"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Product"
msgstr "商品"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_product_line
#: model:ir.model.fields,field_description:approvals.field_approval_request__product_line_ids
msgid "Product Line"
msgstr "產品明細"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_product_variant
msgid "Product Variants"
msgstr "產品變體"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_id
#: model:ir.ui.menu,name:approvals.approvals_menu_product
#: model:ir.ui.menu,name:approvals.approvals_menu_product_template
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_kanban_mobile_view
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree_independent
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Products"
msgstr "產品"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__quantity
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Quantity"
msgstr "數量"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__reference
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Reference"
msgstr "編號"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_id
msgid "Reference Sequence"
msgstr "編碼序列"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
#, python-format
msgid "Refuse"
msgstr "退回"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__refused
msgid "Refused"
msgstr "駁回"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__request_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Request"
msgstr "請求"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.res_users_view_form
msgid "Request Approval"
msgstr "請求批准"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_owner_id
msgid "Request Owner"
msgstr "請求擁有人"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_status
msgid "Request Status"
msgstr "請求狀態"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__required
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__required
msgid "Required"
msgstr "必填項"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence
#, python-format
msgid "Sequence"
msgstr "序號"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "Start date should precede the end date."
msgstr "開始日期應在結束日期之前。"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__status
msgid "Status"
msgstr "狀態"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Submit"
msgstr "提交"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__pending
msgid "Submitted"
msgstr "發送"

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_user
msgid "The user will be able to see approvals created by himself."
msgstr "用戶將能夠看到自己創建的批准。"

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_manager
msgid "The user will have access to the approvals configuration."
msgstr "此用戶將會有權限配置審批。"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no manager "
"linked to your employee profile."
msgstr "這個請求需要得到你的經理批准。但你的員工檔案未有連結至經理。"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. There is no user linked "
"to your manager."
msgstr "這個請求需要得到你的經理批准。但你的員工檔案未有連結至經理。"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"This request needs to be approved by your manager. Your manager is not in "
"the approvers list."
msgstr "這個請求需要得到你的經理批准。但你的經理不在審批人名單上。"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/components/approval/approval.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__pending
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__pending
#, python-format
msgid "To Approve"
msgstr "待批准"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "To Review:"
msgstr "待複核："

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__new
msgid "To Submit"
msgstr "待提交"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_id
msgid "Unit of Measure"
msgstr "單位"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread
msgid "Unread Messages"
msgstr "未讀消息"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀消息計數器"

#. module: approvals
#. openerp-web
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: code:addons/approvals/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:approvals.field_approval_approver__user_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__user_id
#, python-format
msgid "User"
msgstr "使用者"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__user_status
msgid "User Status"
msgstr "用戶狀態"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Withdraw"
msgstr "撤回"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid ""
"You cannot assign the same approver multiple times on the same request."
msgstr "不可在同一請求中多次分配相同的審批者。"

#. module: approvals
#: code:addons/approvals/models/ir_attachment.py:0
#, python-format
msgid ""
"You cannot unlink an attachment which is linked to a validated, refused or "
"cancelled approval request."
msgstr "若附件是連結至已驗證、已拒絕或已取消的批核請求，便不可取消。"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to add at least %s approvers to confirm your request."
msgstr "你必須加入至少 %s 個審批人去確認你的申請。"

#. module: approvals
#: code:addons/approvals/models/approval_request.py:0
#, python-format
msgid "You have to attach at least one document."
msgstr "最少必須附上一份文件。"

#. module: approvals
#: code:addons/approvals/models/approval_category.py:0
#, python-format
msgid "Your minimum approval exceeds the total of default approvers."
msgstr "你的最低批准數超過了預設審批人的總數。"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "e.g. Brussels"
msgstr "例：布魯塞爾"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "e.g. Procurement"
msgstr "例：採購"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "menu"
msgstr "選單"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "new request"
msgstr "新請求"
