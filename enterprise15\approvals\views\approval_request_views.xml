<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!--Request-->

    <record id="approval_request_action" model="ir.actions.act_window">
        <field name="name">My Requests</field>
        <field name="res_model">approval.request</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="domain">[('request_owner_id','=',uid)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Approvals Requests
            </p>
            <p>
                Let's go to the <a type="action" class="text-primary" name="%(approvals.approval_category_action_new_request)d">new request</a> menu
            </p>
        </field>
    </record>

    <record id="approval_request_action_to_review" model="ir.actions.act_window">
        <field name="name">Approvals to Review</field>
        <field name="res_model">approval.request</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="domain">['&amp;',('approver_ids.user_id','=',uid),('request_status','=','pending')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No new approvals to review
            </p>
        </field>
    </record>

    <record id="approval_request_action_all" model="ir.actions.act_window">
        <field name="name">All Approvals</field>
        <field name="res_model">approval.request</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Approvals
            </p>
            <p>
                Let's go to the <a type="action" class="text-primary" name="%(approvals.approval_category_action_new_request)d">new request</a> menu
            </p>
        </field>
    </record>

    <record id="approval_request_view_tree" model="ir.ui.view">
        <field name="name">approval.request.view.tree</field>
        <field name="model">approval.request</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <tree create="false" sample="1" decoration-info="request_status == 'new'">
                <field name="name"/>
                <field name="request_owner_id" widget="many2one_avatar_user"/>
                <field name="category_id"/>
                <field name="activity_ids" widget="list_activity"/>
                <field name="request_status" decoration-info="request_status == 'new'" decoration-warning="request_status == 'pending'" decoration-success="request_status == 'approved'" decoration-danger="request_status == 'refused'" widget="badge"/>
            </tree>
        </field>
    </record>

    <record id="approval_search_view_search" model="ir.ui.view">
        <field name="name">approval.request.search</field>
        <field name="model">approval.request</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <filter string="My Request" name="filter_my_request" domain="[('request_owner_id', '=', uid)]"/>
                <filter string="My Approvals to Review" name="filter_approvals_to_review"
                    domain="[('approver_ids.user_id', '=', uid), ('request_status', '=', 'pending')]"/>
            </search>
        </field>
    </record>

    <record id="approval_request_view_form" model="ir.ui.view">
        <field name="name">approval.request.view.form</field>
        <field name="model">approval.request</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <form string="Request" create="false">
                <header>
                    <field name="user_status" invisible="1"/>
                    <field name="has_access_to_request" invisible="1"/>
                    <button name="action_confirm" string="Submit" type="object" attrs="{'invisible':[('request_status','!=','new')]}" class="btn-primary" data-hotkey="v"/>
                    <button name="action_approve" string="Approve" type="object" attrs="{'invisible':[('user_status','!=','pending')]}" data-hotkey="q"/>
                    <button name="action_refuse" string="Refuse" type="object" attrs="{'invisible':[('user_status','!=','pending')]}" data-hotkey="x"/>
                    <button name="action_withdraw" string="Withdraw" type="object" attrs="{'invisible':['|','|',('request_status','==','new'),('user_status','in',['pending','cancel']),('user_status','=',False)]}" data-hotkey="y"/>
                    <button name="action_draft" string="Back To Draft" type="object" attrs="{'invisible':['|',('request_status','!=','cancel'),'&amp;',('user_status','==',False),('has_access_to_request','==',False)]}" data-hotkey="w"/>
                    <button name="action_cancel" string="Cancel" type="object" attrs="{'invisible':['|',('request_status','in',['new','cancel']),'&amp;',('user_status','==',False),('has_access_to_request','==',False)]}" data-hotkey="z"/>
                    <widget name="attach_document" string="Attach Document" action="message_post" attrs="{'invisible': [('attachment_number', '&lt;', 1)]}"/>
                    <widget name="attach_document" string="Attach Document" action="message_post" highlight="1" attrs="{'invisible': [('attachment_number', '&gt;=', 1)]}"/>
                    <field name="request_status" widget="statusbar"/>
                </header>
                <sheet>
                    <field name="has_date" invisible="1"/>
                    <field name="has_period" invisible="1"/>
                    <field name="has_quantity" invisible="1"/>
                    <field name="has_amount" invisible="1"/>
                    <field name="has_reference" invisible="1"/>
                    <field name="has_partner" invisible="1"/>
                    <field name="has_payment_method" invisible="1"/>
                    <field name="has_location" invisible="1"/>
                    <field name="has_product" invisible="1"/>
                    <field name="requirer_document" invisible="1"/>
                    <field name="approval_minimum" invisible="1"/>
                    <field name="approval_type" invisible="1"/>
                    <div class="o_not_full oe_button_box">
                        <button name="action_get_attachment_view"
                            class="oe_stat_button"
                            icon="fa-book"
                            type="object"
                            attrs="{'invisible': [('attachment_number', '=', 0)]}">
                            <field name="attachment_number" widget="statinfo" string="Documents" options="{'reload_on_button': true}"/>
                        </button>
                    </div>
                    <div class="oe_avatar">
                        <field name="category_image" widget="image" options="{'preview_image': 'category_image', 'size': [80, 80]}"/>
                    </div>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="automated_sequence" invisible="1"/>
                            <field name="name" placeholder="E.g: Expenses Paris business trip"
                                attrs="{'readonly': [('automated_sequence', '=', True)]}"
                                required="1"/>
                        </h1>
                    </div>
                    <group>
                        <group name="request_main">
                            <field name="request_owner_id" groups="approvals.group_approval_user" domain="[('share', '=', False)]" attrs="{'readonly':[('request_status', '!=', 'new')]}"/>
                            <field name="category_id" groups="approvals.group_approval_user" attrs="{'readonly':[('request_status','!=','new')]}" force_save="1"/>
                            <field name="date_confirmed" groups="base.group_no_one" readonly="1"/>
                            <label for="date" string="Date" attrs="{'invisible':[('has_date','==','no')]}"/>
                            <div attrs="{'invisible':[('has_date','==','no')]}">
                                <div>
                                    <field name="date" class="oe_inline" attrs="{'required': [('has_date','==','required')], 'readonly': [('request_status', '!=', 'new')]}"/>
                                </div>
                            </div>
                            <label for="date_start" string="Period" attrs="{'invisible':[('has_period','==','no')]}"/>
                            <div attrs="{'invisible':[('has_period','==','no')]}">
                                <div>
                                  <span>From: </span><field name="date_start" class="oe_inline" attrs="{'required': [('has_period','==','required')], 'readonly': [('request_status', '!=', 'new')]}"/>
                                </div>
                                <div>
                                  <span>to: </span><field name="date_end" class="oe_inline ml-4" attrs="{'required': [('has_period','==','required')], 'readonly': [('request_status','!=','new')]}"/>
                                </div>
                            </div>
                            <field name="location" attrs="{'invisible':[('has_location','==','no')], 'required': [('has_location','==','required')], 'readonly': [('request_status','!=','new')]}" placeholder="e.g. Brussels" />
                            <field name="partner_id" attrs="{'invisible':[('has_partner','==','no')], 'required': [('has_partner','==','required')], 'readonly': [('request_status','!=','new')]}"/>
                        </group>
                        <group name="request_details">
                            <field name="quantity" attrs="{
                                    'invisible':['|', ('has_quantity', '=', 'no'), ('has_product', '!=', 'no')],
                                    'required': [('has_quantity','=','required'), ('has_product', '=', 'no')],
                                    'readonly':[('request_status', '!=', 'new')],
                                }"/>
                            <field name="amount" attrs="{'invisible':[('has_amount','==','no')], 'required': [('has_amount','==','required')], 'readonly':[('request_status', '!=', 'new')]}"/>
                            <field name="reference" attrs="{'invisible':[('has_reference','==','no')], 'required': [('has_reference','==','required')]}"/>
                            <field name="company_id" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Products" name="products"
                            attrs="{'invisible': [('has_product', '=', 'no')]}">
                            <field name="product_line_ids"
                                context="{'tree_view_ref': 'approvals.approval_product_line_view_tree', 'kanban_view_ref': 'approvals.approval_product_kanban_mobile_view'}" attrs="{'readonly':[('request_status', '!=', 'new')]}"/>
                        </page>
                        <page string="Description" name="description">
                            <field name="reason" attrs="{'readonly':[('request_status', '!=', 'new')]}"/>
                        </page>
                        <page string="Approver(s)" name="approvers">
                            <field name="approver_ids" mode="tree,kanban">
                                <tree editable="bottom" decoration-success="status=='approved'" decoration-warning="status=='pending'" decoration-danger="status=='refused'">
                                    <field name="existing_request_user_ids" invisible="1"/>
                                    <field name="can_edit" invisible="1"/>
                                    <field name="user_id" string="Approver" attrs="{'readonly':[('status','!=','new')]}"/>
                                    <field name="required" attrs="{'readonly': [('can_edit', '=', False)]}" force_save="1"/>
                                    <field name="status"/>
                                    <field name="company_id" invisible="1"/>
                                </tree>
                                <kanban class="o_kanban_mobile">
                                    <field name="company_id" invisible="1"/>
                                    <field name="user_id"/>
                                    <field name="status"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div class="oe_kanban_card oe_kanban_global_click">
                                                <div class="o_kanban_content">
                                                    <t t-esc="record.user_id.value"/>
                                                    <t t-if="record.status.raw_value">
                                                        <t t-set="classname" t-value="{'approved': 'badge-success', 'pending': 'badge-warning', 'refused': 'badge-danger'}[record.status.raw_value] || 'badge-light'"/>
                                                        <span t-esc="record.status.raw_value" t-attf-class="float-right badge-pill {{ classname }}"/>
                                                    </t>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                                <form>
                                    <group>
                                        <field name="company_id" invisible="1"/>
                                        <field name="existing_request_user_ids" invisible="1"/>
                                        <field name="user_id"/>
                                    </group>
                                </form>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="o_attachment_preview"/>
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="approval_request_view_kanban" model="ir.ui.view">
        <field name="name">approval.request.view.kanban</field>
        <field name="model">approval.request</field>
        <field name="arch" type="xml">
            <kanban create="false" class="o_modules_kanban" default_group_by="request_status" default_order="create_date desc" 
            group_create="false" group_edit="false" group_delete="false" sample="1">
                <field name="request_owner_id"/>
                <field name="request_status"/>
                <field name="user_status"/>
                <field name="name"/>
                <field name="activity_ids"/>
                <field name="category_id" readonly="1"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click container">
                            <div class="o_dropdown_kanban dropdown" t-if="!selection_mode">

                                <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <a t-if="widget.editable" role="menuitem" type="edit" class="dropdown-item">Edit Request</a>
                                    <a t-if="widget.deletable" role="menuitem" type="delete" class="dropdown-item">Delete</a>
                                    <a name="action_approve" type="object" attrs="{'invisible':[('user_status','!=','pending')]}"
                                        role="menuitem" class="dropdown-item">Approve</a>
                                    <a name="action_refuse" type="object" attrs="{'invisible':[('user_status','!=','pending')]}"
                                        role="menuitem" class="dropdown-item">Refuse</a>
                                    <a name="action_withdraw" type="object" attrs="{'invisible':['|',('request_status','==','new'),('user_status','in',['pending','cancel'])]}"
                                        role="menuitem" class="dropdown-item">Withdraw</a>
                                </div>
                            </div>
                            <div class="oe_kanban_content">
                                <t t-if="record.user_status.raw_value == 'pending'">
                                    <span class="badge badge-pill float-right badge-warning mt4 mr16"><t t-esc="record.user_status.value"/></span>
                                </t>
                                <t t-if="record.user_status.raw_value == 'approved'">
                                    <span class="badge badge-pill float-right badge-success mt4 mr16"><t t-esc="record.user_status.value"/></span>
                                </t>
                                <t t-if="record.user_status.raw_value == 'refused'">
                                    <span class="badge badge-pill float-right badge-danger mt4 mr16"><t t-esc="record.user_status.value"/></span>
                                </t>
                                <div>
                                    <strong class="o_kanban_record_title"><field name="name"/></strong>
                                </div>

                                <div class="text-muted o_kanban_record_subtitle">
                                    <field name="category_id"/>
                                </div>

                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="activity_ids" widget="kanban_activity"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <field name="request_owner_id" widget="many2one_avatar_user"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

<!--MenuItem-->

    <menuitem
        id="approvals_menu_root"
        name="Approvals"
        web_icon="approvals,static/description/icon.png"
        action="approval_category_action_new_request"
        sequence="255"/>

    <menuitem
        id="approvals_approval_menu"
        parent="approvals_menu_root"
        name="My Approvals"
        sequence="10"/>

    <menuitem
        id="approvals_category_menu_new"
        parent="approvals_approval_menu"
        name="New Request"
        action="approval_category_action_new_request"
        sequence="10"/>

    <menuitem
        id="approvals_request_menu_my"
        parent="approvals_approval_menu"
        name="My Requests"
        action="approval_request_action"
        sequence="20"/>

    <menuitem
        id="approvals_menu_manager"
        parent="approvals_menu_root"
        name="Manager"
        groups="group_approval_user"
        sequence="20"/>

    <menuitem
        id="approvals_approval_menu_to_review"
        parent="approvals_menu_manager"
        name="Approvals to Review"
        action="approval_request_action_to_review"
        sequence="10"/>

    <menuitem
        id="approvals_approval_menu_all"
        parent="approvals_menu_manager"
        name="All Approvals"
        action="approval_request_action_all"
        sequence="20"/>

    <menuitem
        id="approvals_menu_config"
        parent="approvals_menu_root"
        name="Configuration"
        sequence="40"/>

    <menuitem
        id="approvals_category_menu_config"
        parent="approvals_menu_config"
        name="Approvals Types"
        action="approval_category_action"
        groups="group_approval_manager"
        sequence="10"/>

    <menuitem
        id="approvals_menu_product"
        parent="approvals_menu_root"
        name="Products"
        sequence="30"/>

    <menuitem
        id="approvals_menu_product_template"
        parent="approvals_menu_product"
        name="Products"
        action="product.product_template_action"
        sequence="10"/>

    <menuitem
        id="approvals_menu_product_variant"
        parent="approvals_menu_product"
        name="Product Variants"
        action="product.product_normal_action"
        groups="product.group_product_variant"
        sequence="20"/>
</odoo>
