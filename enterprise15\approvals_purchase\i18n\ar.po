# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals_purchase
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_category__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""
"يتيح لك تحديد أي المستندات ترغب في إنشائها بمجرد أن تتم الموافقة على الطلب "

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_category
msgid "Approval Category"
msgstr "فئة الموافقة "

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_request
msgid "Approval Request"
msgstr "طلب موافقة "

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_category__approval_type
msgid "Approval Type"
msgstr "نوع الموافقة"

#. module: approvals_purchase
#: model:approval.category,name:approvals_purchase.approval_category_data_rfq
#: model:ir.model.fields.selection,name:approvals_purchase.selection__approval_category__approval_type__purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
msgid "Create RFQ's"
msgstr "إنشاء طلبات عروض الأسعار "

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception occurred: the Approval Request"
msgstr "يوجد استثناء: طلب الموافقة "

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception(s):"
msgstr "الاستثناء (الاستثناءات): "

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_product_line.py:0
#, python-format
msgid "Please set a vendor on product(s) %s."
msgstr "الرجاء تحديد مورّد للمنتج (المنتجات) %s. "

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_product_line
msgid "Product Line"
msgstr "سلسلة المنتج "

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__product_id
msgid "Products"
msgstr "المنتجات"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_request__purchase_order_count
msgid "Purchase Order Count"
msgstr "عدد أوامر الشراء"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__purchase_order_line_id
msgid "Purchase Order Line"
msgstr "بند أمر الشراء"

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
#, python-format
msgid "Purchase Orders"
msgstr "أوامر الشراء"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__po_uom_qty
msgid "Purchase UoM Quantity"
msgstr "كمية وحدة القياس للشراء "

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_product_line__po_uom_qty
msgid ""
"The quantity converted into the UoM used by the product in Purchase Order."
msgstr ""
"الكمية التي تم تحويلها إلى وحدة القياس المستخدمة من قِبَل المنتَج في أمر "
"الشراء. "

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_request.py:0
#, python-format
msgid "You cannot create an empty purchase request."
msgstr "لا يمكنك إنشاء طلب شراء فارغ. "

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_request.py:0
#, python-format
msgid "You must select a product for each line of requested products."
msgstr "عليك تحديد منتج لكل بند من المنتجات المطلوبة. "

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "cancelled"
msgstr "ملغي"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid ""
"has been canceled.\n"
"            Manual actions may be needed."
msgstr ""
"قد تم إلغاؤه.\n"
"            قد يتطلب الأمر اتخاذ إجراء يدوياً. "

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "of"
msgstr "من"
