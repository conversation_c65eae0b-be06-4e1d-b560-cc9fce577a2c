# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals_purchase
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <a<PERSON><PERSON>@allegro.lv>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Will Sensors, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Will Sensors, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_category__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_category
msgid "Approval Category"
msgstr ""

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_request
msgid "Approval Request"
msgstr ""

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_category__approval_type
msgid "Approval Type"
msgstr ""

#. module: approvals_purchase
#: model:approval.category,name:approvals_purchase.approval_category_data_rfq
#: model:ir.model.fields.selection,name:approvals_purchase.selection__approval_category__approval_type__purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
msgid "Create RFQ's"
msgstr ""

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception occurred: the Approval Request"
msgstr ""

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "Exception(s):"
msgstr "Izņēmums(-i):"

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_product_line.py:0
#, python-format
msgid "Please set a vendor on product(s) %s."
msgstr ""

#. module: approvals_purchase
#: model:ir.model,name:approvals_purchase.model_approval_product_line
msgid "Product Line"
msgstr ""

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__product_id
msgid "Products"
msgstr "Produkti"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_request__purchase_order_count
msgid "Purchase Order Count"
msgstr ""

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__purchase_order_line_id
msgid "Purchase Order Line"
msgstr "Pasūtījuma rinda"

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:approvals_purchase.approval_purchase_request_view_form_inherit
#, python-format
msgid "Purchase Orders"
msgstr "Pirkuma Pasūtījumi"

#. module: approvals_purchase
#: model:ir.model.fields,field_description:approvals_purchase.field_approval_product_line__po_uom_qty
msgid "Purchase UoM Quantity"
msgstr "Pirkšanas mērvienības daudzums"

#. module: approvals_purchase
#: model:ir.model.fields,help:approvals_purchase.field_approval_product_line__po_uom_qty
msgid ""
"The quantity converted into the UoM used by the product in Purchase Order."
msgstr ""

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_request.py:0
#, python-format
msgid "You cannot create an empty purchase request."
msgstr ""

#. module: approvals_purchase
#: code:addons/approvals_purchase/models/approval_request.py:0
#, python-format
msgid "You must select a product for each line of requested products."
msgstr ""

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "cancelled"
msgstr "atcelts"

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid ""
"has been canceled.\n"
"            Manual actions may be needed."
msgstr ""

#. module: approvals_purchase
#: model_terms:ir.ui.view,arch_db:approvals_purchase.exception_approval_request_canceled
msgid "of"
msgstr "no"
