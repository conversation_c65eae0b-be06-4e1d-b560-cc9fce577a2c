<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="approval_purchase_category_view_form_inherit" model="ir.ui.view">
        <field name="name">approval.purchase.category.view.form.inherit</field>
        <field name="model">approval.category</field>
        <field name="inherit_id" ref="approvals.approval_category_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='approval_type']" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
            <xpath expr="//field[@name='has_quantity']" position="attributes">
                <attribute name="attrs">
                    {'readonly': [('approval_type', '=', 'purchase')]}
                </attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
            <xpath expr="//field[@name='has_product']" position="attributes">
                <attribute name="attrs">
                    {'readonly': [('approval_type', '=', 'purchase')]}
                </attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>
