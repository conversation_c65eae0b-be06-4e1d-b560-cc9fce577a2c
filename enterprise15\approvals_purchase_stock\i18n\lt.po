# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals_purchase_stock
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: approvals_purchase_stock
#: model:ir.model,name:approvals_purchase_stock.model_approval_request
msgid "Approval Request"
msgstr ""

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_request__hide_location
msgid "Hide Location"
msgstr ""

#. module: approvals_purchase_stock
#: model:ir.model,name:approvals_purchase_stock.model_approval_product_line
msgid "Product Line"
msgstr ""

#. module: approvals_purchase_stock
#: model:ir.model.fields,field_description:approvals_purchase_stock.field_approval_product_line__warehouse_id
msgid "Warehouse"
msgstr "Sandėlis"
