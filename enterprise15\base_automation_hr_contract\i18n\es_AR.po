# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_automation_hr_contract
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Spanish (Argentina) (https://www.transifex.com/odoo/teams/41243/es_AR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_AR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation_website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model,name:base_automation_hr_contract.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation_website_published
msgid "Available on the Website"
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation_website_url
msgid "The full URL to access the server action through the website."
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation_trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation_trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation_website_path
msgid "Website Path"
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation_website_url
msgid "Website Url"
msgstr ""
