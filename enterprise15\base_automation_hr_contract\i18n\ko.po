# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_automation_hr_contract
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 10:05+0000\n"
"PO-Revision-Date: 2018-08-24 11:37+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2018\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"전용 제어기를 사용하여 코드 서버 조치를 웹 사이트에서 실행할 수 있습니다. 주소는 <base> / website / action / "
"<website_path>입니다. 이 필드를 True로 설정하면 사용자가 이 작업을 실행할 수 있습니다. False로 설정하면 웹 "
"사이트를 통해 작업을 실행할 수 없습니다."

#. module: base_automation_hr_contract
#: model:ir.model,name:base_automation_hr_contract.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__website_published
msgid "Available on the Website"
msgstr "웹 사이트에서 사용 가능"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__website_url
msgid "The full URL to access the server action through the website."
msgstr "웹사이트를 통해 서버 작업에 접근하는 전체 URL입니다."

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr "임직원 근무 일정 사용"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr "사용자의 근무 일정을 사용합니다."

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__website_path
msgid "Website Path"
msgstr "웹사이트 경로"

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__website_url
msgid "Website Url"
msgstr ""
