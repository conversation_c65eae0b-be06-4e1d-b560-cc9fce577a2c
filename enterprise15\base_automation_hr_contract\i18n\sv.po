# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_automation_hr_contract
# 
# Translators:
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.3+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-06-25 08:34+0000\n"
"PO-Revision-Date: 2018-06-25 08:34+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Swedish (https://www.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model,name:base_automation_hr_contract.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__website_published
msgid "Available on the Website"
msgstr "Tillgänglig på webbplatsen"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__website_url
msgid "The full URL to access the server action through the website."
msgstr ""

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__trg_date_resource_field_id
msgid "Use employee work schedule"
msgstr "Använd anställd arbetsschema"

#. module: base_automation_hr_contract
#: model:ir.model.fields,help:base_automation_hr_contract.field_base_automation__trg_date_resource_field_id
msgid "Use the user's working schedule."
msgstr "Använd användarens arbetsschema."

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__website_path
msgid "Website Path"
msgstr "Webbplatsens sökväg"

#. module: base_automation_hr_contract
#: model:ir.model.fields,field_description:base_automation_hr_contract.field_base_automation__website_url
msgid "Website Url"
msgstr ""
