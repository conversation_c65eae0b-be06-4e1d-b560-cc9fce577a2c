# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_enterprise
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Bengali (https://www.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "% Opportunities"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Archived"
msgstr "আর্কাইভ করা"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Average Deal Size"
msgstr ""

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Campaign"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "City"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Closed Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Company"
msgstr "কোম্পানি"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Conversion Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Country"
msgstr "দেশ"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Creation Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_map
msgid "Customer"
msgstr ""

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_lead_action_dashboard
#: model:ir.ui.menu,name:crm_enterprise.crm_enterprise_dashboard_menu
msgid "Dashboard"
msgstr "ড্যাশবোর্ড"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_to_convert
msgid "Days To Convert"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Days To Opportunity"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Assign"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Close"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_exceeding_closing
msgid "Exceeded Closing Days"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Exceeding Close Days"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Expected Closing Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Expected Revenue"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Extended Filters"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Group By"
msgstr "গ্রুপ দ্বারা"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_activity_report__won_status
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__won_status
msgid "Is Won"
msgstr ""

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_lead
msgid "Lead/Opportunity"
msgstr "লিড / সুযোগ"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Leads"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__lost
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__lost
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost Reason"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Medium"
msgstr "মধ্যম"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "My Pipeline"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_lead_action_dashboard
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_opportunity_action_dashboard
msgid "No data found!"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_cohort
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_graph
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities Analysis"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__pending
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__pending
msgid "Pending"
msgstr ""

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_opportunity_action_dashboard
msgid "Pipeline Analysis"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Prorated Revenue"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Sales Team"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Sales Teams"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Salesperson"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only lead"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only opportunity"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Source"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Stage"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_lead_action_dashboard
msgid "This Dashboard allows you to see at a glance how well you are doing."
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_opportunity_action_dashboard
msgid "Use this menu to have an overview of your Pipeline."
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Win/Loss Ratio"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__won
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__won
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Won"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "days"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_graph
msgid "leads"
msgstr ""
