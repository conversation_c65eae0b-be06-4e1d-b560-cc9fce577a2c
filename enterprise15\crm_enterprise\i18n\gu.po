# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm_enterprise
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "% Opportunities"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Average Deal Size"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Campaign"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "City"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Closed Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Company"
msgstr "કંપની"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Conversion Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Country"
msgstr "દેશ"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Creation Date"
msgstr "સર્જન તારીખ"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_to_convert
msgid "Days To Convert"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Days To Opportunity"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Assign"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Close"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_exceeding_closing
msgid "Exceeded Closing Days"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Exceeding Close Days"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Expected Closing Date"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Expected Revenue"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Extended Filters"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Group By"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__won_status
msgid "Is Won"
msgstr ""

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Leads"
msgstr "લીડ"

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_lead_action_dashboard
msgid "Leads Analysis"
msgstr ""

#. module: crm_enterprise
#: selection:crm.lead,won_status:0
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost Reason"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Medium"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "My Pipeline"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_cohort
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_graph
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities"
msgstr "તકો"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities Analysis"
msgstr ""

#. module: crm_enterprise
#: selection:crm.lead,won_status:0
msgid "Pending"
msgstr "અધુરુ"

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_opportunity_action_dashboard
msgid "Pipeline Analysis"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Prorated Revenue"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Sales Channel"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Salesperson"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only lead"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only opportunity"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Source"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Stage"
msgstr ""

#. module: crm_enterprise
#: selection:crm.lead,won_status:0
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Won"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_graph
msgid "leads"
msgstr ""
