# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_enterprise
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "% Opportunities"
msgstr "% Oportunidades"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Average Deal Size"
msgstr "Tamanho médio da negociação"

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr "Análise de Atividade CRM"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Campaign"
msgstr "Campanha"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "City"
msgstr "Cidade"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Closed Date"
msgstr "Data de Fechamento"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Company"
msgstr "Empresa"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Conversion Date"
msgstr "Data de Conversão"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Country"
msgstr "País"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Creation Date"
msgstr "Data de Criação"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_map
msgid "Customer"
msgstr "Cliente"

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_lead_action_dashboard
#: model:ir.ui.menu,name:crm_enterprise.crm_enterprise_dashboard_menu
msgid "Dashboard"
msgstr "Painel"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_to_convert
msgid "Days To Convert"
msgstr "Dias para converter"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Days To Opportunity"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Assign"
msgstr "Dias para Atribuir"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Close"
msgstr "Dias para concluir"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_exceeding_closing
msgid "Exceeded Closing Days"
msgstr "Dias excedidos para fechamento"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Exceeding Close Days"
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Expected Closing Date"
msgstr "Data de encerramento esperada"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Expected Revenue"
msgstr "Receita Esperada"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Extended Filters"
msgstr "Filtros avançados"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Group By"
msgstr "Agrupar Por"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_activity_report__won_status
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__won_status
msgid "Is Won"
msgstr "É ganho"

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Prospecção/Oportunidade"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Leads"
msgstr "Leads"

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__lost
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__lost
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost"
msgstr "Perdido"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost Reason"
msgstr "Motivo da perda"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Medium"
msgstr "Médio"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "My Pipeline"
msgstr "Meu Pipeline"

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_lead_action_dashboard
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_opportunity_action_dashboard
msgid "No data found!"
msgstr "Nenhum dado encontrado!"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_cohort
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_graph
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities"
msgstr "Oportunidades"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities Analysis"
msgstr "Análise de Oportunidades"

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__pending
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__pending
msgid "Pending"
msgstr "Pendente"

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_opportunity_action_dashboard
msgid "Pipeline Analysis"
msgstr "Análise do funil"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Prorated Revenue"
msgstr "Receita Rateada"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Sales Team"
msgstr "Equipe de Vendas"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Sales Teams"
msgstr "Equipes de vendas"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Salesperson"
msgstr "Vendedor"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only lead"
msgstr "Mostrar somente o prospecto"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only opportunity"
msgstr "Mostrar apenas oportunidades"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Source"
msgstr "Origem"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Stage"
msgstr "Estágio"

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_lead_action_dashboard
msgid "This Dashboard allows you to see at a glance how well you are doing."
msgstr ""

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_opportunity_action_dashboard
msgid "Use this menu to have an overview of your Pipeline."
msgstr "Use esse menu para ter uma visão geral do seu funil."

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Win/Loss Ratio"
msgstr ""

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__won
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__won
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Won"
msgstr "Ganhamos"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "days"
msgstr "dias"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_graph
msgid "leads"
msgstr "leads"
