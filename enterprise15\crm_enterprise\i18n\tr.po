# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_enterprise
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Yedigen, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Halil, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Halil, 2022\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "% Opportunities"
msgstr "% Fırsatlar"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Average Deal Size"
msgstr "Ortalama Anlaşma Boyutu"

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr "CRM Aktivite Analizi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Campaign"
msgstr "Kampanya"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "City"
msgstr "Semt/İlçe"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Closed Date"
msgstr "Kapatma Tarihi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Company"
msgstr "Şirket"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Conversion Date"
msgstr "Dönüştürme Tarihi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Country"
msgstr "Ülke"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Creation Date"
msgstr "Oluşturulma Tarihi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_map
msgid "Customer"
msgstr "Müşteri"

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_lead_action_dashboard
#: model:ir.ui.menu,name:crm_enterprise.crm_enterprise_dashboard_menu
msgid "Dashboard"
msgstr "Pano"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_to_convert
msgid "Days To Convert"
msgstr "Dönüştürülecek Günler"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Days To Opportunity"
msgstr "Fırsat Günleri"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Assign"
msgstr "Atanana Kadarki Gün"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Days to Close"
msgstr "Kapanmaya Kalan Gün"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__days_exceeding_closing
msgid "Exceeded Closing Days"
msgstr "Kapanış Günü Geçti"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Exceeding Close Days"
msgstr "Kapanış Günleri Geçti"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Expected Closing Date"
msgstr "Tahmini Kapatma Tarihi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Expected Revenue"
msgstr "Beklenen Gelir"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Extended Filters"
msgstr "Genişletilmiş Filtreler"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Group By"
msgstr "Grupla"

#. module: crm_enterprise
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_activity_report__won_status
#: model:ir.model.fields,field_description:crm_enterprise.field_crm_lead__won_status
msgid "Is Won"
msgstr "Kazanıldı"

#. module: crm_enterprise
#: model:ir.model,name:crm_enterprise.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Aday/Fırsat"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Leads"
msgstr "Adaylar"

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__lost
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__lost
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost"
msgstr "Kayıp"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Lost Reason"
msgstr "Kayıp Nedeni"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Medium"
msgstr "Orta"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "My Pipeline"
msgstr "Fırsatlarım"

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_lead_action_dashboard
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_opportunity_action_dashboard
msgid "No data found!"
msgstr "Veri bulunamadı!"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_cohort
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_graph
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities"
msgstr "Fırsatlar"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Opportunities Analysis"
msgstr "Fırsat Analizi"

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__pending
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__pending
msgid "Pending"
msgstr "Beklemede"

#. module: crm_enterprise
#: model:ir.actions.act_window,name:crm_enterprise.crm_opportunity_action_dashboard
msgid "Pipeline Analysis"
msgstr "Fırsat Analizi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Prorated Revenue"
msgstr "Orantılı Gelir"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Sales Team"
msgstr "Satış Ekibi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
msgid "Sales Teams"
msgstr "Satış Ekipleri"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only lead"
msgstr "Sadece adayı göster"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Show only opportunity"
msgstr "Sadece fırsatı göster"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Source"
msgstr "Kaynak"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Stage"
msgstr "Aşama"

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_lead_action_dashboard
msgid "This Dashboard allows you to see at a glance how well you are doing."
msgstr ""
"Bu Gösterge Tablosu, bir bakışta ne kadar iyi yaptığınızı görmenizi sağlar."

#. module: crm_enterprise
#: model_terms:ir.actions.act_window,help:crm_enterprise.crm_opportunity_action_dashboard
msgid "Use this menu to have an overview of your Pipeline."
msgstr "Fırsat Havuzuna genel bakış için bu menüyü kullanınız"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "Win/Loss Ratio"
msgstr "Kazanılan/Kayıp Oranı"

#. module: crm_enterprise
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_activity_report__won_status__won
#: model:ir.model.fields.selection,name:crm_enterprise.selection__crm_lead__won_status__won
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_search
msgid "Won"
msgstr "Kazanıldı"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_dashboard_view
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_opportunity_view_dashboard
msgid "days"
msgstr "gün"

#. module: crm_enterprise
#: model_terms:ir.ui.view,arch_db:crm_enterprise.crm_lead_view_graph
msgid "leads"
msgstr "adaylar"
