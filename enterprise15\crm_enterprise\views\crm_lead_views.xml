<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="crm_opportunity_view_search" model="ir.ui.view">
            <field name="name">crm.lead.search</field>
            <field name="model">crm.lead</field>
            <field name="priority">32</field>
            <field name="arch" type="xml">
                <search string="Opportunities Analysis">
                    <filter string="My Pipeline" name="my"
                            domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter name="lead" string="Leads" domain="[('type','=', 'lead')]" help="Show only lead" groups="crm.group_use_lead"/>
                    <filter name="opportunity" string="Opportunities" domain="[('type','=','opportunity')]" help="Show only opportunity" groups="crm.group_use_lead"/>
                    <separator/>
                    <filter string="Won" name="won_status"
                            domain="[('probability', '=', 100)]"/>
                    <filter string="Lost" name="lost"
                            domain="[('probability', '=', 0), ('active', '=', False)]"/>
                    <field name="user_id" string="Salesperson"/>
                    <field name="team_id" context="{'invisible_team': False}"/>
                    <separator/>
                    <group expand="0" string="Extended Filters">
                        <field name="partner_id" filter_domain="[('partner_id','child_of',self)]"/>
                        <field name="stage_id" domain="['|', ('team_id', '=', False), ('team_id', '=', 'team_id')]"/>
                        <field name="campaign_id"/>
                        <field name="medium_id"/>
                        <field name="source_id"/>
                        <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                    </group>
                    <field name="city"/>
                    <field name="lost_reason"/>
                    <separator/>
                    <filter name="filter_create_date" date="create_date" default_period="this_year"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <group expand="1" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'user_id'}"/>
                        <filter string="Sales Team" name="saleschannel" context="{'group_by':'team_id'}"/>
                        <filter name="stage" string="Stage" context="{'group_by':'stage_id'}"/>
                        <filter name="city" string="City" context="{'group_by': 'city'}"/>
                        <filter string="Country" name="country" context="{'group_by':'country_id'}" />
                        <filter string="Lost Reason" name="lostreason" context="{'group_by':'lost_reason'}"/>
                        <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Campaign" name="compaign" domain="[]" context="{'group_by':'campaign_id'}"/>
                        <filter string="Medium" name="medium" domain="[]" context="{'group_by':'medium_id'}"/>
                        <filter string="Source" name="source" domain="[]" context="{'group_by':'source_id'}"/>
                        <separator orientation="vertical" />
                        <filter string="Creation Date" context="{'group_by':'create_date:month'}" name="month"/>
                        <filter string="Conversion Date" name="date_conversion" context="{'group_by': 'date_conversion'}" groups="crm.group_use_lead"/>
                        <filter string="Expected Closing Date" name="date_deadline" context="{'group_by':'date_deadline'}"/>
                        <filter string="Closed Date" name="date_closed" context="{'group_by':'date_closed'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="crm_opportunity_view_graph" model="ir.ui.view">
            <field name="name">crm.lead.graph.view</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <graph string="Opportunities" sample="1">
                    <field name="stage_id"/>
                    <field name="date_deadline" interval="month"/>
                    <field name="color" invisible="1"/>
                </graph>
            </field>
        </record>

        <record id="crm_lead_view_pivot" model="ir.ui.view">
            <field name="name">crm.lead.pivot.view</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <pivot string="Leads" display_quantity="1" sample="1">
                    <field name="user_id" type="row"/>
                    <field name="expected_revenue" type="measure"/>
                    <field name="prorated_revenue" type="measure"/>
                    <field name="day_close" type="measure"/>
                    <field name="color" invisible="1"/>
                </pivot>
            </field>
        </record>

        <record id="crm_lead_view_pivot_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.pivot.forecast.inherit</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_lead_view_pivot_forecast"/>
            <field name="arch" type="xml">
                <xpath expr="field[@name='probability']" position="after">
                    <field name="days_to_convert" invisible="1"/>
                </xpath>
            </field>
        </record>

        <record id="crm_lead_view_graph" model="ir.ui.view">
            <field name="name">crm.lead.graph.view</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <graph string="leads" type="line" sample="1">
                    <field name="create_date" interval="week"/>
                    <field name="color" invisible="1"/>
                </graph>
            </field>
        </record>

        <record id="crm_lead_view_graph_forecast" model="ir.ui.view">
            <field name="name">crm.lead.view.graph.forecast.inherit</field>
            <field name="model">crm.lead</field>
            <field name="inherit_id" ref="crm.crm_lead_view_graph_forecast"/>
            <field name="arch" type="xml">
                <xpath expr="field[@name='probability']" position="after">
                    <field name="days_to_convert" invisible="1"/>
                </xpath>
            </field>
        </record>

        <record id="crm_lead_view_map" model="ir.ui.view">
            <field name="name">crm.lead.view.map</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <map res_partner="partner_id">
                    <field name="partner_id" string="Customer"/>
                </map>
            </field>
        </record>

        <record id="crm_opportunity_view_dashboard" model="ir.ui.view">
            <field name="name">crm.lead.view.dashboard</field>
            <field name="model">crm.lead</field>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <dashboard sample="1">
                    <view type="graph" ref="crm_enterprise.crm_opportunity_view_graph"/>
                    <group>
                        <group>
                            <aggregate name="opportunities" string="Opportunities" group_operator="count" field="id" measure="__count__"/>
                            <aggregate name="expected_revenue_aggregate" field="expected_revenue" string="Expected Revenue"/>
                            <aggregate name="prorated_revenue_aggregate" field="prorated_revenue" invisible="1"/>
                            <formula name="prorated_revenue" string="Prorated Revenue" value="record.prorated_revenue_aggregate" widget="monetary"/>
                            <formula name="deal_size" string="Average Deal Size" value="record.expected_revenue_aggregate / record.opportunities" widget="monetary"/>
                            <aggregate name="days_to_assign" string="Days to Assign" field="day_open" group_operator="avg" value_label="days"/>
                            <aggregate name="days_to_close" string="Days to Close" field="day_close" group_operator="avg" value_label="days"/>
                            <aggregate name="days_exceeding_closing" string="Exceeding Close Days" field="days_exceeding_closing" group_operator="avg"/>
                        </group>
                        <group col="1">
                            <widget name="pie_chart" title="Win/Loss Ratio" attrs="{'groupby': 'won_status', 'domain': '[\'|\', (\'active\', \'=\', False), (\'active\', \'=\', True), (\'won_status\', \'!=\', \'pending\')]'}"/>
                            <widget name="pie_chart" title="Medium" attrs="{'groupby': 'medium_id'}"/>
                        </group>
                    </group>
                    <view type="pivot" ref="crm.crm_lead_view_pivot"/>
                    <view type="cohort" ref="crm_enterprise.crm_lead_view_cohort"/>
                </dashboard>
            </field>
        </record>

        <record id="crm_lead_dashboard_view" model="ir.ui.view">
            <field name="name">crm.lead.view.dashboard.lead</field>
            <field name="model">crm.lead</field>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <dashboard sample="1">
                    <view type="graph" ref="crm_enterprise.crm_lead_view_graph"/>
                    <group>
                        <group>
                                <aggregate name="leads" string="Leads" group_operator="count_distinct" field="id" measure="__count__"/>
                                <aggregate name="days_to_convert" string="Days To Opportunity" group_operator="avg" field="days_to_convert"/>
                                <aggregate name="days_to_assign" string="Days to Assign" field="day_open" group_operator="avg" value_label="days"/>
                                <formula name="opportunity_percent" string="% Opportunities" value="record.opportunities / record.leads" widget="percentage"/>
                                <aggregate name="expected_revenue" string="Expected Revenue" field="expected_revenue"
                                group_operator="sum" widget="monetary"/>
                                <aggregate name="prorated_revenue" string="Prorated Revenue" field="prorated_revenue" group_operator="sum" widget="monetary"/>
                                <aggregate name="day_close" string="Days to Close" field="day_close" group_operator="avg" value_label="days"/>
                                <aggregate name="opportunities" string="Opportunities" field="id" domain="[('type','=','opportunity')]" group_operator="count" invisible="1"/>
                        </group>
                        <group>
                            <widget name="pie_chart" title="Sales Teams" attrs="{'groupby': 'team_id'}"/>
                            <widget name="pie_chart" title="Medium" attrs="{'groupby': 'medium_id'}"/>
                        </group>
                    </group>
                    <view type="pivot" ref="crm_enterprise.crm_lead_view_pivot"/>
                </dashboard>
            </field>
        </record>

        <record id="crm_lead_view_cohort" model="ir.ui.view">
            <field name="name">crm.lead.view.cohort</field>
            <field name="model">crm.lead</field>
            <field name="arch" type="xml">
                <cohort string="Opportunities" date_start="create_date" date_stop="date_closed" interval="week" mode="churn" sample="1">
                    <field name="color" invisible="1"/>
                </cohort>
            </field>
        </record>

        <record id="crm.action_report_crm_lead_salesteam" model="ir.actions.act_window">
            <field name="view_mode">dashboard,graph,pivot,tree,form,cohort</field>
            <field name="view_id" ref="crm_lead_dashboard_view"/>
        </record>
        <record id="action_report_crm_lead_salesteam_view_dashboard" model="ir.actions.act_window.view">
            <field name="sequence">1</field>
            <field name="view_mode">dashboard</field>
            <field name="view_id" ref="crm_lead_dashboard_view"/>
            <field name="act_window_id" ref="crm.action_report_crm_lead_salesteam"/>
        </record>

        <record id="crm.action_report_crm_opportunity_salesteam" model="ir.actions.act_window">
            <field name="view_mode">dashboard,pivot,graph,tree,form,cohort</field>
            <field name="view_id" ref="crm_opportunity_view_dashboard"/>
            <field name="search_view_id" ref="crm_enterprise.crm_opportunity_view_search"/>
        </record>

        <!-- dashboard action -->
        <record id="crm_opportunity_action_dashboard" model="ir.actions.act_window">
            <field name="name">Pipeline Analysis</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">graph,pivot,cohort,dashboard,tree,form</field>
            <field name="context" eval="{
                'tree_view_ref': 'crm.crm_case_tree_view_oppor',
                'default_type': 'opportunity',
                'search_default_opportunity': True,
                'search_default_filter_create_date': 1}"/>
            <field name="search_view_id" ref="crm_enterprise.crm_opportunity_view_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data found!
                </p><p>
                    Use this menu to have an overview of your Pipeline.
                </p>
            </field>
        </record>

        <record id="crm_opportunity_action_dashboard_dashboard" model="ir.actions.act_window.view">
            <field name="sequence" eval="4"/>
            <field name="view_mode">dashboard</field>
            <field name="view_id" ref="crm_opportunity_view_dashboard"/>
            <field name="act_window_id" ref="crm_enterprise.crm_opportunity_action_dashboard"/>
        </record>

        <record id="crm_opportunity_action_dashboard_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm.crm_lead_view_pivot"/>
            <field name="act_window_id" ref="crm_enterprise.crm_opportunity_action_dashboard"/>
        </record>

        <record id="crm_opportunity_action_dashboard_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_enterprise.crm_opportunity_view_graph"/>
            <field name="act_window_id" ref="crm_enterprise.crm_opportunity_action_dashboard"/>
        </record>

        <record id="crm_opportunity_action_dashboard_cohort" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">cohort</field>
            <field name="view_id" ref="crm_enterprise.crm_lead_view_cohort"/>
            <field name="act_window_id" ref="crm_enterprise.crm_opportunity_action_dashboard"/>
        </record>

        <record id="crm_opportunity_action_dashboard_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="5"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="crm.crm_case_tree_view_oppor"/>
            <field name="act_window_id" ref="crm_enterprise.crm_opportunity_action_dashboard"/>
        </record>

        <record id="crm_lead_action_dashboard" model="ir.actions.act_window">
            <field name="name">Dashboard</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">dashboard,pivot,graph,tree</field>
            <field name="domain">['|', ('active','=',True), ('active','=',False)]</field>
            <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
            <field name="context" eval="{'search_default_filter_creation_date': 1}"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data found!
                </p><p>
                    This Dashboard allows you to see at a glance how well you are doing.
                </p>
            </field>
        </record>

        <record id="crm_lead_action_dashboard_dashboard" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">dashboard</field>
            <field name="view_id" ref="crm_lead_dashboard_view"/>
            <field name="act_window_id" ref="crm_enterprise.crm_lead_action_dashboard"/>
        </record>

        <record id="crm_lead_action_dashboard_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="2"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_enterprise.crm_lead_view_pivot"/>
            <field name="act_window_id" ref="crm_enterprise.crm_lead_action_dashboard"/>
        </record>

        <record id="crm_lead_action_dashboard_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="3"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_enterprise.crm_lead_view_graph"/>
            <field name="act_window_id" ref="crm_enterprise.crm_lead_action_dashboard"/>
        </record>

        <!-- add dashboard to pipeline action -->
        <record id="crm_lead_action_pipeline_view_dashboard" model="ir.actions.act_window.view">
            <field name="sequence" eval="7"/>
            <field name="view_mode">dashboard</field>
            <field name="view_id" ref="crm_enterprise.crm_opportunity_view_dashboard"/>
            <field name="act_window_id" ref="crm.crm_lead_action_pipeline"/>
        </record>

        <!-- add cohort to pipeline action -->
        <record id="crm_lead_action_pipeline_view_cohort" model="ir.actions.act_window.view">
            <field name="sequence" eval="6"/>
            <field name="view_mode">cohort</field>
            <field name="view_id" ref="crm_enterprise.crm_lead_view_cohort"/>
            <field name="act_window_id" ref="crm.crm_lead_action_pipeline"/>
        </record>

        <!-- add map to pipeline action -->
        <record id="crm_lead_action_pipeline_view_map" model="ir.actions.act_window.view">
            <field name="sequence" eval="7"/>
            <field name="view_mode">map</field>
            <field name="view_id" ref="crm_enterprise.crm_lead_view_map"/>
            <field name="act_window_id" ref="crm.crm_lead_action_pipeline"/>
        </record>

        <!-- add dashboard to res partner action -->
        <record id="crm_lead_action_partner_view_dashboard" model="ir.actions.act_window.view">
            <field name="sequence" eval="6"/>
            <field name="view_mode">dashboard</field>
            <field name="act_window_id" ref="crm.crm_lead_opportunities"/>
        </record>

        <!-- add cohort to res partner action -->
        <record id="crm_opportunity_partner_add_cohort" model="ir.actions.act_window.view">
            <field name="sequence" eval="7"/>
            <field name="view_mode">cohort</field>
            <field name="act_window_id" ref="crm.crm_lead_opportunities"/>
        </record>

        <!-- add dashboard to lead report action -->
        <record id="crm.crm_opportunity_report_action_lead" model="ir.actions.act_window">
            <field name="view_mode">graph,pivot,dashboard,tree</field>
        </record>
        <record id="crm_opportunity_report_action_lead_dashbaord" model="ir.actions.act_window.view">
            <field name="view_mode">dashboard</field>
            <field name="view_id" ref="crm_enterprise.crm_opportunity_view_dashboard"/>
            <field name="act_window_id" ref="crm.crm_opportunity_report_action_lead"/>
        </record>

        <!-- menuitem -->
        <record id="crm.crm_opportunity_report_menu" model="ir.ui.menu">
            <field name="action" ref="crm_enterprise.crm_opportunity_action_dashboard"/>
        </record>

        <menuitem
            id="crm_enterprise_dashboard_menu"
            name="Dashboard"
            parent="crm.crm_menu_report"
            action="crm_enterprise.crm_lead_action_dashboard"
            sequence="0"/>

    </data>
</odoo>
