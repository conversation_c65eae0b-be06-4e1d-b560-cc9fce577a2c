<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="helpdesk_ticket_to_lead_view_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.to.lead.view.form</field>
        <field name="model">helpdesk.ticket.to.lead</field>
        <field name="arch" type="xml">
            <form string="Convert Ticket to Lead">
                <group>
                    <group name="action" string="Customer" col="1">
                        <field name="action" nolabel="1" widget="radio" required="1"/>
                        <group col="2">
                            <field name="partner_id" widget="res_partner_many2one"
                                context="{'res_partner_search_mode': 'customer', 'show_vat': True}"
                                attrs="{'required': [('action', '=', 'exist')],
                                        'invisible': [('action', '!=', 'exist')]}"/>
                        </group>
                    </group>
                    <group string="Sales Team">
                        <field name="ticket_id" invisible="1"/>
                        <field name="team_id"/>
                        <field name="user_id"/>
                    </group>
                </group>
                <footer>
                    <button name="action_convert_to_lead" string="Convert" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="helpdesk_ticket_to_lead_action" model="ir.actions.act_window">
        <field name="name">Convert Ticket to Lead</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">helpdesk.ticket.to.lead</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="helpdesk_ticket_to_lead_view_form"/>
        <field name="target">new</field>
    </record>

</odoo>
