# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * currency_rate_live
# 
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-07 09:57+0000\n"
"PO-Revision-Date: 2016-09-07 09:57+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2016\n"
"Language-Team: Armenian (https://www.transifex.com/odoo/teams/41243/hy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_company
msgid "Companies"
msgstr "Ընկերություններ"

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Daily"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_provider:0
msgid "European Central Bank"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_account_config_settings_currency_interval_unit
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_currency_interval_unit
msgid "Interval Unit"
msgstr ""

#. module: currency_rate_live
#: model:ir.ui.view,arch_db:currency_rate_live.view_account_currency_config_settings_inherit
msgid "Live Currency Rate"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Manually"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Monthly"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_account_config_settings_currency_next_execution_date
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_currency_next_execution_date
msgid "Next Execution Date"
msgstr "Հաջրդ գործողության ժամկետը"

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_account_config_settings_currency_provider
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_currency_provider
msgid "Service Provider"
msgstr ""

#. module: currency_rate_live
#: code:addons/currency_rate_live/models/account_config_setting.py:38
#, python-format
msgid ""
"Unable to connect to the online exchange rate platform. The web service may "
"be temporary down. Please try again in a moment."
msgstr ""

#. module: currency_rate_live
#: model:ir.ui.view,arch_db:currency_rate_live.view_account_currency_config_settings_inherit
msgid "Update Now"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Weekly"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_provider:0
msgid "Yahoo"
msgstr ""

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_account_config_settings
msgid "account.config.settings"
msgstr ""
