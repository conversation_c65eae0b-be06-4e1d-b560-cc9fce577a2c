# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* currency_rate_live
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: <PERSON> (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__boc
msgid "Bank Of Canada"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bcrp
msgid "Bank of Peru"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__mindicador
msgid "Chilean mindicador.cl"
msgstr ""

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: currency_rate_live
#: model:ir.actions.server,name:currency_rate_live.ir_cron_currency_update_ir_actions_server
#: model:ir.cron,cron_name:currency_rate_live.ir_cron_currency_update
#: model:ir.cron,name:currency_rate_live.ir_cron_currency_update
msgid "Currency: rate update"
msgstr "Valuta: Kursoppdatering"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__daily
msgid "Daily"
msgstr "Daglig"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__ecb
msgid "European Central Bank"
msgstr "Europeisk sentralbank"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__fta
msgid "Federal Tax Administration (Switzerland)"
msgstr "Federal Tax Administration (Sveits)"

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Interval"
msgstr "Intervall"

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company__currency_interval_unit
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings__currency_interval_unit
msgid "Interval Unit"
msgstr "Intervallenhet"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__manually
msgid "Manually"
msgstr "Manuelt"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__banxico
msgid "Mexican Bank"
msgstr "Mexicanske bank"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__monthly
msgid "Monthly"
msgstr "Månedlig"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bnr
msgid "National Bank Of Romania"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company__currency_next_execution_date
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings__currency_next_execution_date
msgid "Next Execution Date"
msgstr "Neste utførelsesdato"

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Next Run"
msgstr "Neste kjøring"

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Service"
msgstr "Tjeneste"

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company__currency_provider
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings__currency_provider
msgid "Service Provider"
msgstr "Tjenestetilbyder"

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__cbuae
msgid "UAE Central Bank"
msgstr ""

#. module: currency_rate_live
#: code:addons/currency_rate_live/models/res_config_settings.py:0
#, python-format
msgid ""
"Unable to connect to the online exchange rate platform. The web service may "
"be temporary down. Please try again in a moment."
msgstr ""
"Kunne ikke koble til valutakursplattformen. Webtjenesten kan være "
"midlertidig nede. Vennligst prøv igjen om et øyeblikk."

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__weekly
msgid "Weekly"
msgstr "Ukentlig"

#. module: currency_rate_live
#: code:addons/currency_rate_live/models/res_config_settings.py:0
#, python-format
msgid ""
"Your main currency (%s) is not supported by this exchange rate provider. "
"Please choose another one."
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__xe_com
msgid "xe.com"
msgstr ""
