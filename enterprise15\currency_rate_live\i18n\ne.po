# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * currency_rate_live
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
# Lax<PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON>x<PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Nepali (https://www.transifex.com/odoo/teams/41243/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_company
msgid "Companies"
msgstr "कम्पनीहरु"

#. module: currency_rate_live
#: model:ir.actions.server,name:currency_rate_live.ir_cron_currency_update_ir_actions_server
#: model:ir.cron,cron_name:currency_rate_live.ir_cron_currency_update
#: model:ir.cron,name:currency_rate_live.ir_cron_currency_update
msgid "Currency: rate update"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Daily"
msgstr "दैनिक"

#. module: currency_rate_live
#: selection:res.company,currency_provider:0
msgid "European Central Bank"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_provider:0
msgid "Federal Tax Administration (Switzerland)"
msgstr ""

#. module: currency_rate_live
#: model:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Interval"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_currency_interval_unit
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings_currency_interval_unit
msgid "Interval Unit"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_last_currency_sync_date
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings_last_currency_sync_date
#: model:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Last Sync Date"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Manually"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_provider:0
msgid "Mexican Bank"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Monthly"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_currency_next_execution_date
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings_currency_next_execution_date
msgid "Next Execution Date"
msgstr ""

#. module: currency_rate_live
#: model:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Next Run"
msgstr ""

#. module: currency_rate_live
#: model:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Service"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company_currency_provider
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings_currency_provider
msgid "Service Provider"
msgstr ""

#. module: currency_rate_live
#: code:addons/currency_rate_live/models/res_config_settings.py:77
#, python-format
msgid ""
"Unable to connect to the online exchange rate platform. The web service may "
"be temporary down. Please try again in a moment."
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_interval_unit:0
msgid "Weekly"
msgstr ""

#. module: currency_rate_live
#: selection:res.company,currency_provider:0
msgid "Yahoo"
msgstr ""

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_config_settings
msgid "res.config.settings"
msgstr ""
