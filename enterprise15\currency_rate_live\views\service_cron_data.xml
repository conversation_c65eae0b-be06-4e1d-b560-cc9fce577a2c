<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_currency_update" model="ir.cron">
        <field name="name">Currency: rate update</field>
        <field name="model_id" ref="base.model_res_company"/>
        <field name="state">code</field>
        <field name="code">model.run_update_currency()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
    </record>

</odoo>
