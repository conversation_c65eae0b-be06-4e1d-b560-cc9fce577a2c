<?xml version="1.0"?>
<odoo>
    <data noupdate="0">
        <!-- res.partner -->
        <record id="data_cleaning_model_res_partner" model="data_cleaning.model">
            <field name="name">Contact</field>
            <field name="res_model_id" ref="base.model_res_partner"/>
            <field name="notify_user_ids" eval="[(4, ref('base.user_admin'))]"/>
        </record>

        <record id="data_cleaning_rule_res_partner_name" model="data_cleaning.rule">
            <field name="cleaning_model_id" ref="data_cleaning_model_res_partner"/>
            <field name="field_id" ref="base.field_res_partner__name"/>
            <field name="action">trim</field>
            <field name="action_trim">superfluous</field>
        </record>
        <record id="data_cleaning_rule_res_partner_email" model="data_cleaning.rule">
            <field name="cleaning_model_id" ref="data_cleaning_model_res_partner"/>
            <field name="field_id" ref="base.field_res_partner__email"/>
            <field name="action">trim</field>
            <field name="action_trim">all</field>
        </record>
        <record id="data_cleaning_rule_res_partner_vat" model="data_cleaning.rule">
            <field name="cleaning_model_id" ref="data_cleaning_model_res_partner"/>
            <field name="field_id" ref="base.field_res_partner__vat"/>
            <field name="action">trim</field>
            <field name="action_trim">all</field>
        </record>
        <record id="data_cleaning_rule_res_partner_phone" model="data_cleaning.rule">
            <field name="cleaning_model_id" ref="data_cleaning_model_res_partner"/>
            <field name="field_id" ref="base.field_res_partner__phone"/>
            <field name="action">phone</field>
        </record>
        <record id="data_cleaning_rule_res_partner_mobile" model="data_cleaning.rule">
            <field name="cleaning_model_id" ref="data_cleaning_model_res_partner"/>
            <field name="field_id" ref="base.field_res_partner__mobile"/>
            <field name="action">phone</field>
        </record>
    </data>
</odoo>
