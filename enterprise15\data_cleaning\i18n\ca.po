# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_cleaning
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# Ó<PERSON><PERSON> Fonseca <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:38+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Óscar Fonseca <<EMAIL>>, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid ""
"' field cleaning rule.<br/>\n"
"You can validate changes"
msgstr ""
"' regla de neteja de camps.<br/>\n"
"Podeu validar els canvis"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr "<span class=\"mr-1\">Cada</span>"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Action"
msgstr "Acció"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_display
msgid "Action Display"
msgstr "Visualització d'accions"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_technical
msgid "Action Technical"
msgstr "Acció tècnica"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__action
msgid "Actions"
msgstr "Accions"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__active
msgid "Active"
msgstr "Actiu"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__lower
#, python-format
msgid "All Lowercase"
msgstr "Totes les minúscules"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__all
#, python-format
msgid "All Spaces"
msgstr "Tots els espais"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__upper
#, python-format
msgid "All Uppercase"
msgstr "Totes les majúscules"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__automatic
msgid "Automatic"
msgstr "Automàtic"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_case
msgid "Case"
msgstr "Cas"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Clean"
msgstr "Neteja"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Cleaning Actions"
msgstr "Neteja d'accions"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__cleaning_mode
msgid "Cleaning Mode"
msgstr "Mode de neteja"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__cleaning_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__cleaning_model_id
msgid "Cleaning Model"
msgstr "Model de neteja"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_record
msgid "Cleaning Record"
msgstr "S'està netejant el registre"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_rule
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Cleaning Rule"
msgstr "Regla de neteja"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Cleaning Rules"
msgstr "Regles de neteja"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__company_id
msgid "Company"
msgstr "Empresa"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config
msgid "Configuration"
msgstr "Configuració"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "Configure rules to identify records to clean"
msgstr "Configura les regles per a identificar els registres a netejar"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__country_id
msgid "Country"
msgstr "País"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_date
msgid "Created on"
msgstr "Creat el"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__current_value
msgid "Current"
msgstr "Actiu"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "Neteja de dades"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_clean_records_ir_actions_server
#: model:ir.cron,cron_name:data_cleaning.ir_cron_clean_records
#: model:ir.cron,name:data_cleaning.ir_cron_clean_records
msgid "Data Cleaning: Clean Records"
msgstr "Neteja de dades: Registres nets"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__days
msgid "Days"
msgstr "Dies"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Discard"
msgstr "Descartar"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Discarded"
msgstr "Descartat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__field_id
msgid "Field"
msgstr "Camp"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_record
msgid "Field Cleaning"
msgstr "Neteja de camps"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record_notification
msgid "Field Cleaning Records"
msgstr "Registres de neteja de camps"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_config
msgid "Field Cleaning Rules"
msgstr "Regles de neteja de camps"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__name
msgid "Field Name"
msgstr "Nom de camp"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_field_form
msgid "Field To Clean"
msgstr "Camp a netejar"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__first
#, python-format
msgid "First Letters to Uppercase"
msgstr "Primeres lletres a majúscules"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__phone
#, python-format
msgid "Format Phone"
msgstr "Format Telèfon"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_case
msgid ""
"How the type case is set by the rule. 'First Letters to Uppercase' sets "
"every letter to lowercase except the first letter of each word, which is set"
" to uppercase."
msgstr ""
"Com s'estableix el cas tipus per la regla. «Primeres lletres a majúscules» "
"estableix cada lletra a minúscules excepte la primera lletra de cada "
"paraula, que s'estableix a majúscules."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "I've identified"
msgstr "He identificat"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__id
msgid "ID"
msgstr "ID"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model____last_update
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record____last_update
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__last_notification
msgid "Last Notification"
msgstr "Última notificació"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "List of users to notify when there are new records to clean"
msgstr "Llista d'usuaris a notificar quan hi hagi registres nous per netejar"

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#, python-format
msgid "Main actions"
msgstr "Accions principals"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__manual
msgid "Manual"
msgstr "Manual"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_id
msgid "Model"
msgstr "Model"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_name
msgid "Model Name"
msgstr "Nom del model"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__months
msgid "Months"
msgstr "Mesos"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__name
msgid "Name"
msgstr "Nom"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "No cleaning suggestions"
msgstr "Sense suggeriments de neteja"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency
msgid "Notify"
msgstr "Notifica"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Notifica el període de freqüència"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "Notify Users"
msgstr "Notifica als usuaris"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_id
msgid "Record ID"
msgstr "ID Registre "

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__name
msgid "Record Name"
msgstr "Nom del registre"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Records"
msgstr "Registre"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__records_to_clean_count
msgid "Records To Clean"
msgstr "Registres a netejar"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__rule_ids
msgid "Rule"
msgstr "Regla"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__rule_ids
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "Regles"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__html
#, python-format
msgid "Scrap HTML"
msgstr "Scrap HTML"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Select a model to configure cleaning actions"
msgstr "Seleccioneu un model per configurar les accions de neteja"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__case
#, python-format
msgid "Set Type Case"
msgstr "Estableix el tipus de cas"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value_display
msgid "Suggested"
msgstr "Suggerit"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value
msgid "Suggested Value"
msgstr "Valor suggerit"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__superfluous
#, python-format
msgid "Superfluous Spaces"
msgstr "Espais superflus"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#, python-format
msgid ""
"The Python module `phonenumbers` is not installed. Format phone will not "
"work."
msgstr ""
"El mòdul Python «phonenumbers» no està instal·lat. El format del telèfon no "
"funcionarà."

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_cleaning_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "La freqüència de notificació hauria de ser superior a 0"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_trim
msgid "Trim"
msgstr "Trim"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__trim
#, python-format
msgid "Trim Spaces"
msgstr "Retalla els espais"

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#, python-format
msgid "Unselect"
msgstr "Desselecciona"

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Setmanes"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_trim
msgid ""
"Which spaces are trimmed by the rule. Leading, trailing, and successive "
"spaces are considered superfluous."
msgstr ""
"Quins espais són retallats per la regla. Els espais inicials, finals i "
"successius es consideren superflus."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "here"
msgstr "aquí"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "records to clean with the '"
msgstr "registres a netejar amb el '"
