# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_cleaning
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# g<PERSON><PERSON><PERSON> b<PERSON> <guillaume.b<PERSON>@syentys.fr>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:38+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid ""
"' field cleaning rule.<br/>\n"
"You can validate changes"
msgstr ""
"'.<br/>\n"
"Vous pouvez valider les changements"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr "<span class=\"mr-1\">Chaque</span>"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Action"
msgstr "Action"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_display
msgid "Action Display"
msgstr "Action affichée"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_technical
msgid "Action Technical"
msgstr "Action technique"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__action
msgid "Actions"
msgstr "Actions"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__active
msgid "Active"
msgstr "Actif"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__lower
#, python-format
msgid "All Lowercase"
msgstr "Tout en minuscule"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__all
#, python-format
msgid "All Spaces"
msgstr "Tous les espaces"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__upper
#, python-format
msgid "All Uppercase"
msgstr "Tout en majuscule"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__automatic
msgid "Automatic"
msgstr "Automatique"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_case
msgid "Case"
msgstr "Casse"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Clean"
msgstr "Épuré"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Cleaning Actions"
msgstr "Actions de nettoyage"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__cleaning_mode
msgid "Cleaning Mode"
msgstr "Mode de nettoyage"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__cleaning_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__cleaning_model_id
msgid "Cleaning Model"
msgstr "Modèle de nettoyage"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_record
msgid "Cleaning Record"
msgstr "Enregistrement à nettoyer"

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_rule
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Cleaning Rule"
msgstr "Règle de nettoyage"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Cleaning Rules"
msgstr "Règles de nettoyage"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__company_id
msgid "Company"
msgstr "Entreprise"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config
msgid "Configuration"
msgstr "Configuration"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "Configure rules to identify records to clean"
msgstr ""
"Configurez des règles afin d'identifier les enregistrements à nettoyer"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__country_id
msgid "Country"
msgstr "Pays"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_date
msgid "Created on"
msgstr "Créé le"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__current_value
msgid "Current"
msgstr "Actuel"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "Nettoyage de Données"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_clean_records_ir_actions_server
#: model:ir.cron,cron_name:data_cleaning.ir_cron_clean_records
#: model:ir.cron,name:data_cleaning.ir_cron_clean_records
msgid "Data Cleaning: Clean Records"
msgstr "Nettoyage de données: Nettoyer les enregistrements"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__days
msgid "Days"
msgstr "Jours"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Discard"
msgstr "Ignorer"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Discarded"
msgstr "Ignoré"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__field_id
msgid "Field"
msgstr "Champ"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_record
msgid "Field Cleaning"
msgstr "Nettoyage de champ"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record_notification
msgid "Field Cleaning Records"
msgstr "Enregistrements de nettoyage de champ"

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_config
msgid "Field Cleaning Rules"
msgstr "Règles de nettoyage de champ"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__name
msgid "Field Name"
msgstr "Nom de champ"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_field_form
msgid "Field To Clean"
msgstr "Champ à nettoyer"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__first
#, python-format
msgid "First Letters to Uppercase"
msgstr "Premières lettres en majuscule"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__phone
#, python-format
msgid "Format Phone"
msgstr "Formatter le numéro de téléphone"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_case
msgid ""
"How the type case is set by the rule. 'First Letters to Uppercase' sets "
"every letter to lowercase except the first letter of each word, which is set"
" to uppercase."
msgstr ""
"Comment la casse est définie par la règle. 'Premières lettres en majuscule' "
"met chaque lettre en minuscule à l'exception de la première lettre de chaque"
" mot, qui est mise en majuscule."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "I've identified"
msgstr "J'ai identifié"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__id
msgid "ID"
msgstr "ID"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model____last_update
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record____last_update
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__last_notification
msgid "Last Notification"
msgstr "Dernière notification"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "List of users to notify when there are new records to clean"
msgstr ""
"Liste des utilisateurs à notifier lorsqu'il y a de nouveaux enregistrements "
"à nettoyer"

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#, python-format
msgid "Main actions"
msgstr "Actions principales"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__manual
msgid "Manual"
msgstr "Manuel"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_id
msgid "Model"
msgstr "Modèle"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_name
msgid "Model Name"
msgstr "Nom de modèle"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__months
msgid "Months"
msgstr "Mois"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__name
msgid "Name"
msgstr "Nom"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "No cleaning suggestions"
msgstr "Pas de suggestions de nettoyage"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency
msgid "Notify"
msgstr "Notifier"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Période de fréquence de notification"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "Notify Users"
msgstr "Utilisateurs à notifier"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_id
msgid "Record ID"
msgstr "ID de l'enregistrement"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__name
msgid "Record Name"
msgstr "Nom de l'enregistrement"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Records"
msgstr "Enregistrements"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__records_to_clean_count
msgid "Records To Clean"
msgstr "Enregistrements à nettoyer"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__rule_ids
msgid "Rule"
msgstr "Règle"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__rule_ids
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "Règles"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__html
#, python-format
msgid "Scrap HTML"
msgstr "Retirer l'HTML"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Select a model to configure cleaning actions"
msgstr "Sélectionnez un modèle afin de configurer ses actions de nettoyage"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__case
#, python-format
msgid "Set Type Case"
msgstr "Définir la casse"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value_display
msgid "Suggested"
msgstr "Suggéré"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value
msgid "Suggested Value"
msgstr "Valeur suggérée"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__superfluous
#, python-format
msgid "Superfluous Spaces"
msgstr "Espaces superflus"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#, python-format
msgid ""
"The Python module `phonenumbers` is not installed. Format phone will not "
"work."
msgstr ""
"Le module Python 'phonenumbers' n'est pas installé. Le formatage de numéro "
"de téléphone ne fonctionnera pas."

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_cleaning_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "La fréquence de notification doit être supérieure à 0"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_trim
msgid "Trim"
msgstr "Retirer"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__trim
#, python-format
msgid "Trim Spaces"
msgstr "Retirer les espaces"

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#, python-format
msgid "Unselect"
msgstr "Désélectionner"

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#, python-format
msgid "Validate"
msgstr "Valider"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Semaines"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_trim
msgid ""
"Which spaces are trimmed by the rule. Leading, trailing, and successive "
"spaces are considered superfluous."
msgstr ""
"Les espaces retirés par la règle. Les espaces de début, de fin, et "
"successifs sont considérés comme superflus."

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "here"
msgstr "ici"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "records to clean with the '"
msgstr "enregistrements à nettoyer avec la règle de nettoyage de champ '"
