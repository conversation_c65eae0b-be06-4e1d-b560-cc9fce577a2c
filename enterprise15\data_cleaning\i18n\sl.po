# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_cleaning
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:38+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid ""
"' field cleaning rule.<br/>\n"
"You can validate changes"
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr "<span class=\"mr-1\">Vsak</span>"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Action"
msgstr "Dejanje"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_display
msgid "Action Display"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_technical
msgid "Action Technical"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__action
msgid "Actions"
msgstr "Dejanja"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__active
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__active
msgid "Active"
msgstr "Aktivno"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__lower
#, python-format
msgid "All Lowercase"
msgstr ""

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__all
#, python-format
msgid "All Spaces"
msgstr ""

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__upper
#, python-format
msgid "All Uppercase"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__automatic
msgid "Automatic"
msgstr "Samodejno"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_case
msgid "Case"
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Clean"
msgstr "Očistiti"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Cleaning Actions"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__cleaning_mode
msgid "Cleaning Mode"
msgstr ""

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_model
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__cleaning_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__cleaning_model_id
msgid "Cleaning Model"
msgstr ""

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_record
msgid "Cleaning Record"
msgstr ""

#. module: data_cleaning
#: model:ir.model,name:data_cleaning.model_data_cleaning_rule
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Cleaning Rule"
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Cleaning Rules"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__company_id
msgid "Company"
msgstr "Podjetje"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config
msgid "Configuration"
msgstr "Nastavitve"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "Configure rules to identify records to clean"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__country_id
msgid "Country"
msgstr "Država"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__create_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__current_value
msgid "Current"
msgstr "Trenutno"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_root
msgid "Data Cleaning"
msgstr "Čiščenje podatkov"

#. module: data_cleaning
#: model:ir.actions.server,name:data_cleaning.ir_cron_clean_records_ir_actions_server
#: model:ir.cron,cron_name:data_cleaning.ir_cron_clean_records
#: model:ir.cron,name:data_cleaning.ir_cron_clean_records
msgid "Data Cleaning: Clean Records"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__days
msgid "Days"
msgstr "Dni"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
msgid "Discard"
msgstr "Opusti"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
msgid "Discarded"
msgstr "Zavrženo"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__display_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__field_id
msgid "Field"
msgstr "Polje"

#. module: data_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules_cleaning
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_record
msgid "Field Cleaning"
msgstr ""

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_record_notification
msgid "Field Cleaning Records"
msgstr ""

#. module: data_cleaning
#: model:ir.actions.act_window,name:data_cleaning.action_data_cleaning_config
msgid "Field Cleaning Rules"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__field_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__name
msgid "Field Name"
msgstr "Naziv polja"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_field_form
msgid "Field To Clean"
msgstr ""

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_case__first
#, python-format
msgid "First Letters to Uppercase"
msgstr ""

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__phone
#, python-format
msgid "Format Phone"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_case
msgid ""
"How the type case is set by the rule. 'First Letters to Uppercase' sets "
"every letter to lowercase except the first letter of each word, which is set"
" to uppercase."
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "I've identified"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__id
msgid "ID"
msgstr "ID"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model____last_update
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record____last_update
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule____last_update
msgid "Last Modified on"
msgstr "Zadnjič spremenjeno"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__last_notification
msgid "Last Notification"
msgstr "Zadnje obvestilo"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_uid
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__write_date
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "List of users to notify when there are new records to clean"
msgstr ""

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#, python-format
msgid "Main actions"
msgstr "Glavne akcije"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__cleaning_mode__manual
msgid "Manual"
msgstr "Ročno"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_id
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_id
msgid "Model"
msgstr "Model"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_model_name
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__res_model_name
msgid "Model Name"
msgstr "Naziv modela"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__months
msgid "Months"
msgstr "Meseci"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__name
msgid "Name"
msgstr "Naziv"

#. module: data_cleaning
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record
#: model_terms:ir.actions.act_window,help:data_cleaning.action_data_cleaning_record_notification
msgid "No cleaning suggestions"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency
msgid "Notify"
msgstr "Obvestilo"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Obvestilo o frekvenčnem obdobju"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__notify_user_ids
msgid "Notify Users"
msgstr "Obveščanje uporabnikov"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__res_id
msgid "Record ID"
msgstr "ID zapisa"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__name
msgid "Record Name"
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_search
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Records"
msgstr "Zapisi"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__records_to_clean_count
msgid "Records To Clean"
msgstr "Zapisi za čiščenje"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__rule_ids
msgid "Rule"
msgstr "Pravilo"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_model__rule_ids
#: model:ir.ui.menu,name:data_cleaning.menu_data_cleaning_config_rules
msgid "Rules"
msgstr "Pravila"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__html
#, python-format
msgid "Scrap HTML"
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_merge_model_form
msgid "Select a model to configure cleaning actions"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__case
#, python-format
msgid "Set Type Case"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value_display
msgid "Suggested"
msgstr "Predlagani"

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_record__suggested_value
msgid "Suggested Value"
msgstr "Predlagana vrednost"

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action_trim__superfluous
#, python-format
msgid "Superfluous Spaces"
msgstr ""

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#, python-format
msgid ""
"The Python module `phonenumbers` is not installed. Format phone will not "
"work."
msgstr ""

#. module: data_cleaning
#: model:ir.model.constraint,message:data_cleaning.constraint_data_cleaning_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr ""

#. module: data_cleaning
#: model:ir.model.fields,field_description:data_cleaning.field_data_cleaning_rule__action_trim
msgid "Trim"
msgstr ""

#. module: data_cleaning
#: code:addons/data_cleaning/models/data_cleaning_rule.py:0
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_rule__action__trim
#, python-format
msgid "Trim Spaces"
msgstr ""

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#, python-format
msgid "Unselect"
msgstr ""

#. module: data_cleaning
#. openerp-web
#: code:addons/data_cleaning/static/src/xml/data_cleaning.xml:0
#: model_terms:ir.ui.view,arch_db:data_cleaning.view_data_cleaning_record_list
#, python-format
msgid "Validate"
msgstr "Potrditev"

#. module: data_cleaning
#: model:ir.model.fields.selection,name:data_cleaning.selection__data_cleaning_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Tedni"

#. module: data_cleaning
#: model:ir.model.fields,help:data_cleaning.field_data_cleaning_rule__action_trim
msgid ""
"Which spaces are trimmed by the rule. Leading, trailing, and successive "
"spaces are considered superfluous."
msgstr ""

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "here"
msgstr "tukaj"

#. module: data_cleaning
#: model_terms:ir.ui.view,arch_db:data_cleaning.notification
msgid "records to clean with the '"
msgstr ""
