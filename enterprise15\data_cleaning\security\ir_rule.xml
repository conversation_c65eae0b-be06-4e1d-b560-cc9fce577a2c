<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="data_cleaning_record_multi_company" model="ir.rule">
            <field name="name">Data Cleaning Record multi company rule</field>
            <field name="model_id" ref="model_data_cleaning_record"/>
            <field eval="True" name="global"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>
    </data>
</odoo>
