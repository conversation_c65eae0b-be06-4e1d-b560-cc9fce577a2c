<?xml version="1.0"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="view_data_cleaning_model_list">
            <field name="name">Field Cleaning Model List</field>
            <field name="model">data_cleaning.model</field>
            <field name="arch" type="xml">
                <tree decoration-muted="not active">
                    <field name="name" />
                    <field name="res_model_id" />
                    <field name="cleaning_mode" groups="base.group_no_one" />
                    <field name="active" widget="boolean_toggle" />
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="view_data_merge_model_form">
            <field name="name">Field Cleaning Model Form</field>
            <field name="model">data_cleaning.model</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_clean_records" type="object" string="Clean" class="oe_highlight" />
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="open_records"
                                    type="object" icon="fa-bars">
                                <field name="records_to_clean_count" string="Records" widget="statinfo"/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" />
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="res_model_id" options="{'no_create': True, 'no_open': True}" />
                                <field name="res_model_name" invisible="1" />
                                <field name="active" widget="boolean_toggle" />
                            </group>
                            <group>
                                <field name="cleaning_mode" widget="radio" options="{'horizontal': true}" />

                                <!-- Manual cleaning -->
                                <field name="notify_user_ids" widget="many2many_tags" attrs="{'invisible': [('cleaning_mode', '=', 'automatic')]}" options="{'no_create': True, 'no_edit': True}" domain="[('share', '=', False)]" />

                                <label for="notify_frequency" invisible="1" />
                                <div class="o_row" attrs="{'invisible': ['|', ('cleaning_mode', '=', 'automatic'), ('notify_user_ids', '=', [])]}">
                                    <span class="mr-1">Every</span>
                                    <field name="notify_frequency" attrs="{'required': [('notify_user_ids', '!=', [])]}" />
                                    <field name="notify_frequency_period" attrs="{'required': [('notify_user_ids', '!=', [])]}" />
                                </div>
                            </group>
                        </group>
                        <group attrs="{'invisible': [('res_model_id', '!=', False)]}">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    Select a model to configure cleaning actions
                                </div>
                            </group>
                        </group>
                        <group attrs="{'invisible': [('res_model_id', '=', False)]}">
                            <field name="rule_ids" context="{'default_res_model_id': res_model_id}">
                                <tree string="Cleaning Actions">
                                    <field name="res_model_id" invisible="1" />
                                    <field name="sequence" widget="handle" />
                                    <field name="field_id" options="{'no_create': True, 'no_open': True}" />
                                    <field name="action_display" string="Action" />
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_data_cleaning_config">
            <field name="name">Field Cleaning Rules</field>
            <field name="res_model">data_cleaning.model</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">['|', ('active', '=', False), ('active', '=', True)]</field>
        </record>
    </data>
</odoo>
