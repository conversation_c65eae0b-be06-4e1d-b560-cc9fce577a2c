<?xml version="1.0"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="view_data_cleaning_record_list">
            <field name="name">Field Cleaning Record List</field>
            <field name="model">data_cleaning.record</field>
            <field name="arch" type="xml">
                <tree js_class="data_cleaning_list" sample="1">
                    <field name="active" invisible="1" />
                    <field name="res_model_name" invisible="1" />
                    <field name="res_id" />
                    <field name="cleaning_model_id" string="Cleaning Rule" optional="hide" />
                    <field name="name" />
                    <field name="field_id" />
                    <field name="action" string="Action" optional="hide" />
                    <field name="current_value" />
                    <field name="suggested_value_display" />

                    <button icon="fa-check" string="Validate" type="object" name="action_validate" />
                    <button icon="fa-times" string="Discard" type="object" name="action_discard" attrs="{'invisible': [('active', '=', False)]}" />
                </tree>
            </field>
        </record>

        <record model="ir.ui.view" id="view_data_cleaning_record_search">
            <field name="name">Field Cleaning Record Search</field>
            <field name="model">data_cleaning.record</field>
            <field name="arch" type="xml">
                <search string="Records">
                    <filter name="active" string="Discarded" domain="[('active', '=', False)]" />
                    <searchpanel>
                        <field name="cleaning_model_id" icon="fa-bars" string="Cleaning Rules" />
                    </searchpanel>
                </search>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_data_cleaning_record">
            <field name="name">Field Cleaning Records</field>
            <field name="res_model">data_cleaning.record</field>
            <field name="view_mode">tree,form</field>
            <field name="domain"></field>
            <field name="search_view_id" ref="view_data_cleaning_record_search" />
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No cleaning suggestions
              </p>
              <p>
              Configure rules to identify records to clean
              </p>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_data_cleaning_record_notification">
            <field name="name">Field Cleaning Records</field>
            <field name="res_model">data_cleaning.record</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{ 'searchpanel_default_cleaning_model_id': active_id }</field>
            <field name="search_view_id" ref="view_data_cleaning_record_search" />
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No cleaning suggestions
              </p>
              <p>
              Configure rules to identify records to clean
              </p>
            </field>
        </record>
    </data>
</odoo>
