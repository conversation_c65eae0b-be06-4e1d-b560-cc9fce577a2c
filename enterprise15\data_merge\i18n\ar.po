# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_merge
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "%s records have been merged"
msgstr "تم دمج %s سجلات "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"' قاعدة إلغاء التكرار.<br/>\n"
"بإمكانك دمجهم "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">Model "
"specific</span>"
msgstr ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">خاص "
"بالنموذج</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr "<span class=\"mr-1\">كل</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "يمكن للحقل أن يظهر مرة واحدة فقط! "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__active
msgid "Active"
msgstr "نشط"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "أرشفة"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Archived"
msgstr "مؤرشف"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr ""
"هل أنت متأكد من أنك ترغب في دمج السجلات المحددة في المجموعة التابعة لها؟ "

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "Are you sure that you want to merge these records?"
msgstr "هل أنت متأكد من أنك ترغب في دمج تلك السجلات؟ "

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "تلقائي"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_search
msgid "Can Be Merged"
msgstr "يمكن دمجها "

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Case/Accent Insensitive Match"
msgstr "المطابقة غير المقيدة بنوع الأحرف/اللهجة "

#. module: data_merge
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "قم بتهيئة القواعد للتعرف على السجلات المكررة "

#. module: data_merge
#: model:ir.model,name:data_merge.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr ""
"إجراء قائمة سياقي لإعادة التوجيه لطريقة عرض إلغاء التكرار لـ data_merge. "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "أنشئ بواسطة"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "أنشئ في"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "عبر الشركة "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "طريقة الدمج المخصصة "

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_cleanup_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_cleanup
#: model:ir.cron,name:data_merge.ir_cron_cleanup
msgid "Data Merge: Cleanup Records"
msgstr "دمج البيانات: تنظيف السجلات "

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_find_duplicates_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_find_duplicates
#: model:ir.cron,name:data_merge.ir_cron_find_duplicates
msgid "Data Merge: Find Duplicate Records"
msgstr "دمج البيانات: إيجاد سجلات مكررة "

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "الأيام"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
#, python-format
msgid "Deduplicate"
msgstr "إلغاء التكرار "

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_merge.menu_data_merge_group
msgid "Deduplication"
msgstr "إلغاء التكرار "

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_group
msgid "Deduplication Group"
msgstr "مجموعة إلغاء التكرار "

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_model
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "نموذج إلغاء التكرار "

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_record
msgid "Deduplication Record"
msgstr "سجل إلغاء التكرار "

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "قاعدة إلغاء التكرار "

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_config
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "قواعد إلغاء التكرار "

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "حذف"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__differences
msgid "Differences"
msgstr "الاختلافات "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "الفروقات في السجلات الأساسية "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Disable Merge"
msgstr "تعطيل الدمج "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "Discard"
msgstr "إهمال "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Discarded"
msgstr "مهمل "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "الحقول المتباعدة "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__domain
msgid "Domain"
msgstr "النطاق"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "إزالة التكرار "

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Duplicates"
msgstr "التكرارات"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr "لن يتم اقتراح البيانات المكررة ذات نسبة تطابق أقل من هذا الحد "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Enable Merge"
msgstr "تمكين الدمج "

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Exact Match"
msgstr "تطابق تام "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__field_values
msgid "Field Values"
msgstr "قيم الحقول "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "إخفاء زر إجراء الدمج "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "I've identified"
msgstr "قمت بتعريف "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__id
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "ID"
msgstr "المُعرف"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr ""
"إذا كانت القيمة صحيحة، سوف تكون أداة دمج البيانات العامة متاحة في القائمة "
"السياقية لهذا النموذج. "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr ""
"إذا كانت القيمة صحيحة، يتم استخدام هذا السجل لإجراء القائمة السياقي \"دمج\" "
"في النموذج الهدف. "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"إذا كان للنموذج طريقة دمج مخصصة، يتم تعيين قيمة خاصية الفئة  `_merge_disabled` إلى صحيحة في ذلك \n"
"             النموذج ولا يجب أن يكون إجراء دمج البيانات العام متاحاً في ذلك النموذج. "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "محذوف "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "مهمل "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_master
msgid "Is Master"
msgstr "الرئيسي "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "آخر إشعار "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "قائمة بنماذج أخرى تشير إلى هذا السجل "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr ""
"قائمة من المستخدمين الذين يجب إخطارهم عندما يكون هناك سجلات بانتظار الدمج "

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Main actions"
msgstr "الإجراءات الرئيسية "

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "يدوي"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Manual Selection - %s"
msgstr "تحديد يدوي  - %s "

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/models/ir_model.py:0
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
#, python-format
msgid "Merge"
msgstr "دمج"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.ir_model_menu_merge_action_manager
msgid "Merge Action Manager"
msgstr "مدير إجراء الدمج "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "الدمج إذا "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "وضعية الدمج "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "إجراء خادم الدمج "

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.ir_model_action_merge
msgid "Merge action Manager"
msgstr "مدير إجراء الدمج "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "إجراء الدمج مرفق "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__res_model_id
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Model"
msgstr "النموذج "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "اسم النموذج "

#. module: data_merge
#: model:ir.model,name:data_merge.model_ir_model
msgid "Models"
msgstr "النماذج "

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "شهور"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__name
msgid "Name"
msgstr "الاسم"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
#, python-format
msgid "No duplicates found"
msgstr "لم يتم العثور على أي نسخ مكررة "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "إخطار "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "فترة تواتر الإخطار "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "إخطار المستخدمين "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__record_ids
msgid "Record"
msgstr "السجل"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__group_id
msgid "Record Group"
msgstr "مجموعة السجل "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_id
msgid "Record ID"
msgstr "معرف السجل"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Records"
msgstr "السجلات"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "عدد السجلات بانتظار الدمج "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "السجلات الملائمة لعملية إلغاء التكرار "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr "سوف يتم دمج السجلات التي لها نسبة تطابق تفوق هذا الحد تلقائياً "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Rule"
msgstr "قاعدة"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "اختر نموذجاً لتهيئة قواعد إلغاء التكرار "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "نسبة التشابه "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "حد التشابه "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr "عامل التشابه بناءً على كمية حقول النصوص المتطابقة تماماً. "

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr "قم باقتراح دمج السجلات التي تطابق إحدى تلك القوانين على الأقل "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "حد الاقتراح "

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "يجب أن يكون تواتر الإشعارات أكبر من 0 "

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "The selected"
msgstr "المحدد "

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "The target model does not exists."
msgstr "النموذج الهدف غير موجود. "

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "هذا الاسم مُستخدَم بالفعل "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "حقل معرّف فريد "

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Unselect"
msgstr "إلغاء التحديد "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "تم التحديث بواسطة "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "تم التحديث في "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__used_in
msgid "Used In"
msgstr "مستخدم في"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "أسابيع"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr "عندما يكون ممكناً، سوف يتم اقتراح نسخ مكررة من شركات أخرى "

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "You must select at least two %s in order to merge them."
msgstr "عليك تحديد %s اثنين على الأقل حتى تتمكن من دمجهما. "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "سجلات مكررة بها '"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "here"
msgstr "هنا"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_merged
msgid "merged into"
msgstr "مدمج إلى "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_main
msgid "merged into this one"
msgstr "تم الدمج إلى هذا "
