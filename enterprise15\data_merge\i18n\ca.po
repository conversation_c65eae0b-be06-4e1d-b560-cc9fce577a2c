# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_merge
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON> <<EMAIL>>, 2021
# Harcogourmet, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Óscar Fonseca <<EMAIL>>, 2022\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "%s records have been merged"
msgstr "S'han fusionat %s registres"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"' regla de desduplicació.<br/>\n"
"Podeu fusionar-los"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">Model "
"specific</span>"
msgstr ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">Model "
"específic</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr "<span class=\"mr-1\">Cada</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "Un camp només pot aparèixer una vegada!"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__active
msgid "Active"
msgstr "Actiu"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "Arxivar"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Archived"
msgstr "Arxivat"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr ""
"Esteu segur que voleu fusionar els registres seleccionats en el seu grup "
"respectiu?"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "Are you sure that you want to merge these records?"
msgstr "Segur que voleu fusionar aquests registres?"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "Automàtic"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_search
msgid "Can Be Merged"
msgstr "Es pot fusionar"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Case/Accent Insensitive Match"
msgstr "Coincidència insensible a majúscules i minúscules"

#. module: data_merge
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "Configura les regles per identificar registres duplicats"

#. module: data_merge
#: model:ir.model,name:data_merge.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr ""
"Acció contextual del menú que redirigeix a la vista desduplicada de la fusió"
" de dades."

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "Creat per"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "Creat el"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_date
msgid "Created on"
msgstr "Creat el"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "Cross-Company"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "Mètode de fusió personalitzat"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_cleanup_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_cleanup
#: model:ir.cron,name:data_merge.ir_cron_cleanup
msgid "Data Merge: Cleanup Records"
msgstr "Fusió de dades: Neteja els registres"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_find_duplicates_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_find_duplicates
#: model:ir.cron,name:data_merge.ir_cron_find_duplicates
msgid "Data Merge: Find Duplicate Records"
msgstr "Combinació de dades: cerca registres duplicats"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "Dies"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
#, python-format
msgid "Deduplicate"
msgstr "Desduplica"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_merge.menu_data_merge_group
msgid "Deduplication"
msgstr "Desduplicació"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_group
msgid "Deduplication Group"
msgstr "Grup de desduplicació"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_model
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "Model de desduplicació"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_record
msgid "Deduplication Record"
msgstr "Registre de desduplicació"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "Regla de desduplicació"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_config
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "Regles de desduplicació"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "Eliminar"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__differences
msgid "Differences"
msgstr "Diferencies"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "Diferències amb el registre mestre"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Disable Merge"
msgstr "Desactiva la fusió"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "Discard"
msgstr "Descartar"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Discarded"
msgstr "Descartat"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "Camps divergents"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__domain
msgid "Domain"
msgstr "Domini "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "Duplica l'eliminació"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Duplicates"
msgstr "Duplicats"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr ""
"No es suggeriran duplicats amb una similitud per sota d'aquest llindar"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Enable Merge"
msgstr "Activa la fusió"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Exact Match"
msgstr "Coincidència exacta"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__field_values
msgid "Field Values"
msgstr "Valors del camp"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "Amaga el botó d'acció de fusió"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "I've identified"
msgstr "He identificat"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__id
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "ID"
msgstr "ID"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr ""
"Si és cert, l'eina genèrica de fusió de dades està disponible al menú "
"contextual d'aquest model."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr ""
"Si és cert, aquest registre s'utilitza per a l'acció del menú contextual "
"«Fusiona» en el model de destinació."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"Si el model ja té un mètode de fusió personalitzat, l'atribut de classe «mergedisabled» s'estableix a «true»\n"
"             Aquest model i l'acció genèrica de fusió de dades no haurien d'estar disponibles en aquest model."

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "S'ha suprimit"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "Està descartat"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_master
msgid "Is Master"
msgstr "És mestre"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "Última notificació"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "Llista d'altres models que fan referència a aquest registre"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr "Llista d'usuaris a notificar quan hi ha registres nous per fusionar"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Main actions"
msgstr "Accions principals"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "Manual"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Manual Selection - %s"
msgstr "Selecció manual - %s"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/models/ir_model.py:0
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
#, python-format
msgid "Merge"
msgstr "Combina"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.ir_model_menu_merge_action_manager
msgid "Merge Action Manager"
msgstr "Gestor d'accions de fusió"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "Fusiona si"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "Mode de fusió"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "Acció del servidor de fusió"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.ir_model_action_merge
msgid "Merge action Manager"
msgstr "Gestor d'accions de fusió"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "Acció de fusió adjuntada"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__res_model_id
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Model"
msgstr "Model"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "Nom del model"

#. module: data_merge
#: model:ir.model,name:data_merge.model_ir_model
msgid "Models"
msgstr "Models"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "Mesos"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__name
msgid "Name"
msgstr "Nom"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
#, python-format
msgid "No duplicates found"
msgstr "No s'ha trobat cap duplicat"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "Notifica"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Notifica el període de freqüència"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "Notifica als usuaris"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__record_ids
msgid "Record"
msgstr "Registre "

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__group_id
msgid "Record Group"
msgstr "Enregistra un grup"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_id
msgid "Record ID"
msgstr "ID Registre "

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Records"
msgstr "Registre"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "Registres per fusionar el compte"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "Registres elegibles per al procés de desduplicació"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr ""
"Els registres amb un percentatge de similitud per sobre d'aquest llindar es "
"fusionaran automàticament"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Rule"
msgstr "Regla"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "Seleccioneu un model per a configurar les regles de desduplicació"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "Similaritat %"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "Llindar de similitud"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr ""
"Coeficient de similitud basat en la quantitat de camps de text exactament en"
" comú."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr ""
"Suggereix fusionar els registres que coincideixin almenys amb una d'aquestes"
" regles"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "Llindar del suggeriment"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "La freqüència de notificació hauria de ser superior a 0"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "The selected"
msgstr ""

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "The target model does not exists."
msgstr "El model objectiu no existeix."

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "Aquest nom ja està agafat"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "Camp ID únic"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Unselect"
msgstr "Desselecciona"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "Actualitzat per"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "Actualitzat el"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__used_in
msgid "Used In"
msgstr "Utilitzat a"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Setmanes"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr ""
"Quan estigui habilitat, es suggeriran duplicats entre diferents empreses"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "You must select at least two %s in order to merge them."
msgstr "Heu de seleccionar com a mínim dos %s per tal de fusionar-los."

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "duplica els registres amb l''"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "here"
msgstr "aquí"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_merged
msgid "merged into"
msgstr "fusionat a"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_main
msgid "merged into this one"
msgstr "fusionat en aquest"
