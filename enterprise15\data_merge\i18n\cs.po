# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_merge
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <jan.ho<PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <brencic<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <rast<PERSON>.<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "%s records have been merged"
msgstr "%s záznamů bylo sloučeno"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"' pravidlo deduplikace.<br/>\n"
"Můžete je sloučit"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">Model "
"specific</span>"
msgstr ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', "
"False)]}\">Specifický pro model</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr "<span class=\"mr-1\">každý</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "Pole se může zobrazit pouze jednou!"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__active
msgid "Active"
msgstr "Aktivní"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "Archivovat"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Archived"
msgstr "Archivováno"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr ""
"Jste si jisti, že chcete sloučit vybrané záznamy v jejich příslušné skupině?"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "Are you sure that you want to merge these records?"
msgstr "Opravdu chcete tyto záznamy sloučit?"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "Automaticky"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_search
msgid "Can Be Merged"
msgstr "Lze sloučit"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Case/Accent Insensitive Match"
msgstr "Shoda bez ohledu na malá a velká písmena"

#. module: data_merge
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "Nakonfigurujte pravidla pro identifikaci duplicitních záznamů"

#. module: data_merge
#: model:ir.model,name:data_merge.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr ""
"Akce kontextové nabídky, která přesměruje na deduplikované zobrazení "
"data_merge."

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "Vytvořeno uživatelem"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "Vytvořeno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "Křížová společnost"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "Metoda vlastního slučování"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_cleanup_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_cleanup
#: model:ir.cron,name:data_merge.ir_cron_cleanup
msgid "Data Merge: Cleanup Records"
msgstr "Sloučení dat: Vyčistění záznamů"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_find_duplicates_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_find_duplicates
#: model:ir.cron,name:data_merge.ir_cron_find_duplicates
msgid "Data Merge: Find Duplicate Records"
msgstr "Sloučení dat: Najít duplicitní záznamy"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "Dny"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
#, python-format
msgid "Deduplicate"
msgstr "Deduplikovat"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_merge.menu_data_merge_group
msgid "Deduplication"
msgstr "Deduplikace"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_group
msgid "Deduplication Group"
msgstr "Deduplikační skupina"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_model
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "Model deduplikace"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_record
msgid "Deduplication Record"
msgstr "Deduplikační záznam"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "Pravidlo deduplikace"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_config
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "Pravidla deduplikace"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "Smazat"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__differences
msgid "Differences"
msgstr "Rozdíly"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "Rozdíly s hlavním záznamem"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Disable Merge"
msgstr "Zakázat sloučení"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "Discard"
msgstr "Zrušit"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Discarded"
msgstr "Zahozeno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "Divergentní pole"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__domain
msgid "Domain"
msgstr "Doména"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "Odstranění duplikátů"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Duplicates"
msgstr "Duplikáty"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr "Duplikáty s podobností pod touto prahovou hodnotou nebudou navrhovány"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Enable Merge"
msgstr "Povolit sloučení"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Exact Match"
msgstr "Přesná shoda"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__field_values
msgid "Field Values"
msgstr "Hodnoty pole"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "Skrýt tlačítko slučovací akce"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "I've identified"
msgstr "Identifikoval jsem"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__id
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "ID"
msgstr "ID"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr ""
"Pokud je pravda, obecný nástroj pro sloučení dat je dostupný v kontextové "
"nabídce tohoto modelu."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr ""
"Pokud je pravda, tento záznam se použije pro akci kontextové nabídky "
"\"Sloučit\" v cílovém modelu."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"Pokud model již má vlastní metodu sloučení, atribut třídy `_merge_disabled` je nastaven na hodnotu pravda\n"
"             v tomto modelu a akce sloučení obecných dat by v tomto modelu neměly být k dispozici."

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "Je smazáno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "Je zahozeno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_master
msgid "Is Master"
msgstr "Je hlavní"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "Poslední notifikace"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "Seznam dalších modelů odkazujících na tento záznam"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr ""
"Seznam uživatelů, kteří mají být upozorněni na nové záznamy ke sloučení"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Main actions"
msgstr "Hlavní akce"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "Ruční"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Manual Selection - %s"
msgstr "Manuální výběr - %s"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/models/ir_model.py:0
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
#, python-format
msgid "Merge"
msgstr "Sloučit"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.ir_model_menu_merge_action_manager
msgid "Merge Action Manager"
msgstr "Správce slučovacích akcí"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "Sloučit pokud"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "Režim sloučení"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "Slučovací akce serveru"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.ir_model_action_merge
msgid "Merge action Manager"
msgstr "Správce slučovacích akcí"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "Slučovací akce připojena"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__res_model_id
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Model"
msgstr "Model"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "Název modelu"

#. module: data_merge
#: model:ir.model,name:data_merge.model_ir_model
msgid "Models"
msgstr "Modely"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "měsíců"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__name
msgid "Name"
msgstr "Jméno"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
#, python-format
msgid "No duplicates found"
msgstr "Nebyly nalezeny žádné duplikáty"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "Oznámit"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Období upozornění"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "Upozornit uživatele"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__record_ids
msgid "Record"
msgstr "Záznam"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__group_id
msgid "Record Group"
msgstr "Skupina záznamu"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_id
msgid "Record ID"
msgstr "ID záznamu"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Records"
msgstr "Záznamy"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "Počet záznamů ke sloučení"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "Záznamy způsobilé pro proces deduplikace"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr ""
"Záznamy s procentem podobnosti nad touto prahovou hodnotou budou automaticky"
" sloučeny"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Rule"
msgstr "Pravidlo"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "Vyberte model pro konfiguraci pravidel deduplikace"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "Podobnost v %"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "Prahová hodnota podobnosti"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr "Koeficient podobnosti založený na množství běžných textových polí."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr ""
"Navrhněte sloučit záznamy odpovídající alespoň jednomu z těchto pravidel"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "Navržená prahová hodnota"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "Frekvence notifikací by měla být větší než 0"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "The selected"
msgstr ""

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "The target model does not exists."
msgstr "Cílový model neexistuje."

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "Tento název je již použitý"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "Jedinečné ID Field"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Unselect"
msgstr "Odznačit"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "Aktualizováno od"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "Aktualizováno v"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__used_in
msgid "Used In"
msgstr "Použitý v"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Týdny"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr "Po aktivaci budou navrženy duplikáty napříč různými společnostmi"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "You must select at least two %s in order to merge them."
msgstr "Chcete-li je sloučit, musíte vybrat alespoň dva %s."

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "duplicitní záznamy s '"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "here"
msgstr "zde"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_merged
msgid "merged into"
msgstr "sloučeny do"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_main
msgid "merged into this one"
msgstr "sloučeny do tohoto"
