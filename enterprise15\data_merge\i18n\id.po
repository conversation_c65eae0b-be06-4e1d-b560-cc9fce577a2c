# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* data_merge
# 
# Translators:
# <PERSON> <amunif<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Febras<PERSON> Almania <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_model.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (salinan)"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "%s records have been merged"
msgstr "%s record telah digabung"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid ""
"' deduplication rule.<br/>\n"
"You can merge them"
msgstr ""
"' peraturan deduplikasi.<br/>\n"
"Anda dapat menggabungkan mereka"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">Model "
"specific</span>"
msgstr ""
"<span attrs=\"{'invisible': [('custom_merge_method', '=', False)]}\">Model "
"spesifik</span>"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"mr-1\">Every</span>"
msgstr ""

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "<span class=\"oe_inline\">%</span>"
msgstr "<span class=\"oe_inline\">%</span>"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_rule_uniq_model_id_field_id
msgid "A field can only appear once!"
msgstr "Field hanya dapat muncul sekali!"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__active
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__active
msgid "Active"
msgstr "Aktif"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__archive
msgid "Archive"
msgstr "Arsip"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_model_view_search
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid ""
"Are you sure that you want to merge the selected records in their respective"
" group?"
msgstr ""
"Apakah Anda yakin ingin menggabungkan record terpilih di kelompok mereka "
"masing-masing?"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "Are you sure that you want to merge these records?"
msgstr "Apakah Anda yakin ingin menggabungkan record-record ini?"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__automatic
msgid "Automatic"
msgstr "Otomatis"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__is_merge_enabled
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_search
msgid "Can Be Merged"
msgstr "Dapat Digabungkan"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Case/Accent Insensitive Match"
msgstr "Tidak Peka Huruf Kecil/Besar atau Tekanan"

#. module: data_merge
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
msgid "Configure rules to identify duplicate records"
msgstr "Konfigurasikan peraturan untuk mengidentifikasi record duplikat"

#. module: data_merge
#: model:ir.model,name:data_merge.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid ""
"Contextual menu action that redirects to the deduplicate view of data_merge."
msgstr ""
"Menu action kontekstual yang mengalihkan ulang ke tampilan deduplikasi dari"
"  data_merge."

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_uid
msgid "Created By"
msgstr "Dibuat Oleh"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_create_date
msgid "Created On"
msgstr "Dibuat Pada"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__create_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__mix_by_company
msgid "Cross-Company"
msgstr "Cross-Company"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__custom_merge_method
msgid "Custom Merge Method"
msgstr "Metode Penggabungan Kustom"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_cleanup_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_cleanup
#: model:ir.cron,name:data_merge.ir_cron_cleanup
msgid "Data Merge: Cleanup Records"
msgstr "Penggabungan Data: Cleanup Record"

#. module: data_merge
#: model:ir.actions.server,name:data_merge.ir_cron_find_duplicates_ir_actions_server
#: model:ir.cron,cron_name:data_merge.ir_cron_find_duplicates
#: model:ir.cron,name:data_merge.ir_cron_find_duplicates
msgid "Data Merge: Find Duplicate Records"
msgstr "Penggabungan Data: Cari Record Duplikat"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__days
msgid "Days"
msgstr "Hari"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
#, python-format
msgid "Deduplicate"
msgstr "Deduplikasi"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.menu_data_merge_config_rules_deduplication
#: model:ir.ui.menu,name:data_merge.menu_data_merge_group
msgid "Deduplication"
msgstr "Deduplikasi"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_group
msgid "Deduplication Group"
msgstr "Deduplikasi Kelompok"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_model
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__model_id
msgid "Deduplication Model"
msgstr "Model Deduplikasi"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_record
msgid "Deduplication Record"
msgstr "Record Deduplikasi"

#. module: data_merge
#: model:ir.model,name:data_merge.model_data_merge_rule
msgid "Deduplication Rule"
msgstr "Peraturan Deduplikasi"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_config
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__rule_ids
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Deduplication Rules"
msgstr "Peraturan-Peraturan Deduplikasi"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__removal_mode__delete
msgid "Delete"
msgstr "Hapus"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__differences
msgid "Differences"
msgstr "Perbedaan"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__differences
msgid "Differences with the master record"
msgstr "Perbedaan dengan record master"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Disable Merge"
msgstr "Nonaktifkan Penggabungan"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "Discard"
msgstr "Abaikan"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Discarded"
msgstr "Dibuang"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__display_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__divergent_fields
msgid "Divergent Fields"
msgstr "Divergent Fields"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__domain
msgid "Domain"
msgstr "Ruang Lingkup"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__removal_mode
msgid "Duplicate Removal"
msgstr "Penghapusan Duplikasi"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record
#: model:ir.actions.act_window,name:data_merge.action_data_merge_record_notification
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Duplicates"
msgstr "Duplikat"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__create_threshold
msgid ""
"Duplicates with a similarity below this threshold will not be suggested"
msgstr ""
"Duplikat-Duplikat dengan kesamaan di bawah ambang batas ini tidak akan "
"disarankan"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.ir_model_view_form
msgid "Enable Merge"
msgstr "Aktifkan Penggabungan"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_rule.py:0
#, python-format
msgid "Exact Match"
msgstr "Cocok Sempurna"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__field_values
msgid "Field Values"
msgstr "Field Values"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__hide_merge_action
msgid "Hide merge action button"
msgstr "Sembunyikkan tombol action merge"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "I've identified"
msgstr "Saya mengidentifikasi"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__id
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
msgid "ID"
msgstr "ID"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__is_merge_enabled
msgid ""
"If True, the generic data merge tool is available in the contextual menu of "
"this model."
msgstr ""
"Bila True, tool merge data generik jadi tersedia pada menu kontekstual model"
" ini."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__is_contextual_merge_action
msgid ""
"If True, this record is used for contextual menu action \"Merge\" on the "
"target model."
msgstr ""
"Bila True, record ini digunakan untuk menu kontekstual action \"Merge\" pada"
" model sasaran."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_ir_model__hide_merge_action
msgid ""
"If the model already has a custom merge method, the class attribute `_merge_disabled` is set to true on\n"
"             that model and the generic data merge action should not be available on that model."
msgstr ""
"Bila model sudah memiliki metode merge custom, atribut kelas `_merge_disabled` ditetapkan true pada\n"
"             model tersebut dan action merge data generik harusnya tidak tersedia pada model tersebut."

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_deleted
msgid "Is Deleted"
msgstr "Apakah Dihapus"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_discarded
msgid "Is Discarded"
msgstr "Apakah DIbuang"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__is_master
msgid "Is Master"
msgstr "Apakah Master"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record____last_update
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__last_notification
msgid "Last Notification"
msgstr "Notifikasi Terakhir"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_uid
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__write_date
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_record__used_in
msgid "List of other models referencing this record"
msgstr "List model-model lain yang mereferensikan record ini"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__notify_user_ids
msgid "List of users to notify when there are new records to merge"
msgstr ""
"List user-user untuk dinotifikasi saat terdapat record-record baru untuk "
"digabung"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Main actions"
msgstr "Aksi Utama"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__merge_mode__manual
msgid "Manual"
msgstr "Manual"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "Manual Selection - %s"
msgstr "Pemilihan Manual - %s"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/models/ir_model.py:0
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_list
#, python-format
msgid "Merge"
msgstr "Gabung"

#. module: data_merge
#: model:ir.ui.menu,name:data_merge.ir_model_menu_merge_action_manager
msgid "Merge Action Manager"
msgstr "Manajer Action Merge"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__match_mode
msgid "Merge If"
msgstr "Merge Bila"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_mode
msgid "Merge Mode"
msgstr "Mode Merge"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_ir_model__ref_merge_ir_act_server_id
msgid "Merge Server Action"
msgstr "Server Action Merge"

#. module: data_merge
#: model:ir.actions.act_window,name:data_merge.ir_model_action_merge
msgid "Merge action Manager"
msgstr "Manajer action Merge"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__is_contextual_merge_action
msgid "Merge action attached"
msgstr "Action merge terlampir"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_id
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__res_model_id
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Model"
msgstr "Model"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__res_model_name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_model_name
msgid "Model Name"
msgstr "Nama Model"

#. module: data_merge
#: model:ir.model,name:data_merge.model_ir_model
msgid "Models"
msgstr "Model"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__months
msgid "Months"
msgstr "Bulan"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__name
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__name
msgid "Name"
msgstr "Nama"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record
#: model_terms:ir.actions.act_window,help:data_merge.action_data_merge_record_notification
#, python-format
msgid "No duplicates found"
msgstr "Tidak ada duplikat yang ditemukan"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency
msgid "Notify"
msgstr "Notifikasi"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_frequency_period
msgid "Notify Frequency Period"
msgstr "Periode Frekuensi Notifikasi"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__notify_user_ids
msgid "Notify Users"
msgstr "Notifikasi User"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__record_ids
msgid "Record"
msgstr "Catatan"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__group_id
msgid "Record Group"
msgstr "Record Kelompok"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__res_id
msgid "Record ID"
msgstr "Record ID"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_record_view_search_merge_action
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Records"
msgstr "Record-Record"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__records_to_merge_count
msgid "Records To Merge Count"
msgstr "Jumlah Record Untuk Dimerge"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__domain
msgid "Records eligible for the deduplication process"
msgstr "Record yang memenuhi syarat untuk proses deduplikasi"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__merge_threshold
msgid ""
"Records with a similarity percentage above this threshold will be "
"automatically merged"
msgstr ""
"Record dengan persentase kemiripan di atas ambang batas ini akan secara "
"otomatis digabung"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_record_search
msgid "Rule"
msgstr "Aturan"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.view_data_merge_model_form
msgid "Select a model to configure deduplication rules"
msgstr "Pilih model untuk mengonfigurasi peraturan deduplikasi"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_group__similarity
msgid "Similarity %"
msgstr "Kemiripan %"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__merge_threshold
msgid "Similarity Threshold"
msgstr "Ambang Batas Kemiripan"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_group__similarity
msgid ""
"Similarity coefficient based on the amount of text fields exactly in common."
msgstr ""
"Koefisien kemiripan berdasarkan pada jumlah field teks yang persis sama."

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__rule_ids
msgid "Suggest to merge records matching at least one of these rules"
msgstr ""
"Disarankan untuk menggabungkan record yang cocok dengan setidaknya salah "
"satu peraturan ini"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_model__create_threshold
msgid "Suggestion Threshold"
msgstr "Ambang Batas yang Disarankan"

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_check_notif_freq
msgid "The notification frequency should be greater than 0"
msgstr "Frekuensi frekuensi harus lebih besar dari 0"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/js/data_merge_list_view.js:0
#, python-format
msgid "The selected"
msgstr ""

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "The target model does not exists."
msgstr "Model sasaran tidak tersedia."

#. module: data_merge
#: model:ir.model.constraint,message:data_merge.constraint_data_merge_model_uniq_name
msgid "This name is already taken"
msgstr "Nama ini sudah diambil"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_rule__field_id
msgid "Unique ID Field"
msgstr "Field ID Uni"

#. module: data_merge
#. openerp-web
#: code:addons/data_merge/static/src/xml/data_merge_list_views.xml:0
#, python-format
msgid "Unselect"
msgstr "Batalkan pilihan"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_uid
msgid "Updated By"
msgstr "Diupdate Oleh"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__record_write_date
msgid "Updated On"
msgstr "Diupdate Pada"

#. module: data_merge
#: model:ir.model.fields,field_description:data_merge.field_data_merge_record__used_in
msgid "Used In"
msgstr "Digunakan di"

#. module: data_merge
#: model:ir.model.fields.selection,name:data_merge.selection__data_merge_model__notify_frequency_period__weeks
msgid "Weeks"
msgstr "Minggu"

#. module: data_merge
#: model:ir.model.fields,help:data_merge.field_data_merge_model__mix_by_company
msgid "When enabled, duplicates across different companies will be suggested"
msgstr ""
"Saat diaktifkan, duplikat-duplikat di perusahaan-perusahaan yang berbeda "
"akan disarankan"

#. module: data_merge
#: code:addons/data_merge/models/data_merge_record.py:0
#, python-format
msgid "You must select at least two %s in order to merge them."
msgstr "Anda harus memilih setidaknya dua %s untuk menggabungkan mereka."

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "duplicate records with the '"
msgstr "duplikat record dengan '"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_duplicate
msgid "here"
msgstr "here"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_merged
msgid "merged into"
msgstr "gabung menjadi"

#. module: data_merge
#: model_terms:ir.ui.view,arch_db:data_merge.data_merge_main
msgid "merged into this one"
msgstr "digabung ke ini"
