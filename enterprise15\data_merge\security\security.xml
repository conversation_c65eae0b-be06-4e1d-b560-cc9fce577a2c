<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="data_merge_record_multi_company" model="ir.rule">
            <field name="name">Data Merge Record multi company rule</field>
            <field name="model_id" ref="model_data_merge_record"/>
            <field name="global" eval="True"/>
            <field name="domain_force">[('company_id', 'in', [False] + company_ids)]</field>
        </record>
    </data>
</odoo>
