# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_bpost
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Basque (https://www.transifex.com/odoo/teams/41243/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_label_stock_type:0
msgid "A4"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_label_stock_type:0
msgid "A6"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_account_number
msgid "Account Number"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,help:delivery_bpost.field_delivery_carrier_bpost_saturday
msgid "Allow deliveries on Saturday (extra charges apply)"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_delivery_nature
msgid "Bpost Delivery Nature"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_domestic_deliver_type
msgid "Bpost Domestic Deliver Type"
msgstr ""

#. module: delivery_bpost
#: model:product.product,name:delivery_bpost.product_product_delivery_bpost_domestic
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_domestic_product_template
msgid "Bpost Domestic bpack 24h Pro"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_international_deliver_type
msgid "Bpost International Deliver Type"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_label_format
msgid "Bpost Label Format"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_label_stock_type
msgid "Bpost Label Stock Type"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_parcel_return_instructions
msgid "Bpost Parcel Return Instructions"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_shipment_type
msgid "Bpost Shipment Type"
msgstr ""

#. module: delivery_bpost
#: model:product.product,name:delivery_bpost.product_product_delivery_bpost_world
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_world_product_template
msgid "Bpost World Express Pro"
msgstr ""

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_delivery_carrier
msgid "Carrier"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_shipment_type:0
msgid "DOCUMENTS"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_saturday
msgid "Delivery on Saturday"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_parcel_return_instructions:0
msgid "Destroy"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_delivery_nature:0
msgid "Domestic"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_shipment_type:0
msgid "GIFT"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_shipment_type:0
msgid "GOODS"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_delivery_nature:0
msgid "International"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_shipment_type:0
msgid "OTHER"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_label_format:0
msgid "PDF"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_label_format:0
msgid "PNG"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:122
#, python-format
msgid "Packages over 30 Kg are not accepted by bpost."
msgstr ""

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_product_packaging
msgid "Packaging"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_developer_password
msgid "Passphrase"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:56
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_parcel_return_instructions:0
msgid "Return to sender by air"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_parcel_return_instructions:0
msgid "Return to sender by road"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_shipment_type:0
msgid "SAMPLE"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:79
#, python-format
msgid "Shipment created into bpost <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:48
#, python-format
msgid ""
"The address of your company/warehouse is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:58
#, python-format
msgid ""
"The estimated shipping cannot be computed because the weight of your product"
" is missing."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:54
#, python-format
msgid ""
"The estimated shipping price cannot be computed because all your products "
"are service/digital."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:40
#, python-format
msgid ""
"The recipient address is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:90
#, python-format
msgid ""
"You can not cancel a bpost shipment when a shipping label has already been "
"generated."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:95
#, python-format
msgid "You cannot compute a passphrase for non-bpost carriers."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:50
#, python-format
msgid "Your company/warehouse address must be in Belgium to ship with bpost"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_domestic_deliver_type:0
msgid "bpack 24h Pro"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_domestic_deliver_type:0
msgid "bpack 24h business"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_domestic_deliver_type:0
msgid "bpack Bus"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_international_deliver_type:0
msgid "bpack Europe Business"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_international_deliver_type:0
msgid "bpack World Business"
msgstr ""

#. module: delivery_bpost
#: selection:delivery.carrier,bpost_international_deliver_type:0
msgid "bpack World Express Pro"
msgstr ""

#. module: delivery_bpost
#: model:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "bpost Configuration"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier_bpost_default_packaging_id
msgid "bpost Default Packaging Type"
msgstr ""

#. module: delivery_bpost
#: model:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_sale
#: model:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_stock
msgid "bpost Delivery Methods"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:44
#, python-format
msgid ""
"bpost Domestic is used only to ship inside Belgium. Please change the "
"delivery method into bpost International."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:42
#, python-format
msgid ""
"bpost International is used only to ship outside Belgium. Please change the "
"delivery method into bpost Domestic."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:100
#, python-format
msgid "bpost did not return prices for this destination country."
msgstr ""
