# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_bpost
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Батболд <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_stock_type__a4
msgid "A4"
msgstr "A4"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_stock_type__a6
msgid "A6"
msgstr "A6"

#. module: delivery_bpost
#: model:ir.model.fields,help:delivery_bpost.field_delivery_carrier__bpost_saturday
msgid "Allow deliveries on Saturday (extra charges apply)"
msgstr "Бямба гаригт хүргэлтийг зөвшөөрөх (нэмэлт төлбөр тооцож болно)"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"Authentication error -- wrong credentials\n"
"(Detailed error: %s)"
msgstr ""
"Гэрчилгээний алдаа -- буруу итгэмжлэх\n"
"(Алдааны дэлгэрэнгүй: %s)"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_account_number
msgid "Bpost Account Number"
msgstr "Bpost Дансны Дугаар"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_delivery_nature
msgid "Bpost Delivery Nature"
msgstr "Bpost Хүргэлтийн арга"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_domestic_deliver_type
msgid "Bpost Domestic Deliver Type"
msgstr "Bpost Дотоодын Хүргэлтийн төрөл"

#. module: delivery_bpost
#: model:delivery.carrier,name:delivery_bpost.delivery_carrier_bpost_domestic
#: model:product.product,name:delivery_bpost.product_product_delivery_bpost_domestic
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_domestic_product_template
msgid "Bpost Domestic bpack 24h Pro"
msgstr "Bpost Дотоод bpack 24 цаг Pro"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_international_deliver_type
msgid "Bpost International Deliver Type"
msgstr "Bpost олон улсын хүргэлтийн төрөл"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_label_format
msgid "Bpost Label Format"
msgstr "Bpost Форматын нэр"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_label_stock_type
msgid "Bpost Label Stock Type"
msgstr "Bpost нэр хувьцааны төрөл"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Bpost Package Type"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_parcel_return_instructions
msgid "Bpost Parcel Return Instructions"
msgstr "Bpost илгээмжийг буцаах заавар"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_shipment_type
msgid "Bpost Shipment Type"
msgstr "Bpost ачилтын төрөл"

#. module: delivery_bpost
#: model:delivery.carrier,name:delivery_bpost.delivery_carrier_bpost_inter
#: model:product.product,name:delivery_bpost.product_product_delivery_bpost_world
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_world_product_template
msgid "Bpost World Express Pro"
msgstr "Bpost World Express Pro"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Тээвэрлэгч"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__documents
msgid "DOCUMENTS"
msgstr "Баримт бичиг"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_saturday
msgid "Delivery on Saturday"
msgstr "Бямба гарагт хүргэх"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__abandoned
msgid "Destroy"
msgstr "Устгах"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_delivery_nature__domestic
msgid "Domestic"
msgstr "Дотоодын"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__gift
msgid "GIFT"
msgstr "БЭЛЭГ"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__goods
msgid "GOODS"
msgstr "БАРАА"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_delivery_nature__international
msgid "International"
msgstr "Олон улсын"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Label Format"
msgstr ""

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Label Type"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__other
msgid "OTHER"
msgstr "БУСАД"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Options"
msgstr "Сонголтууд"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_format__png
msgid "PNG"
msgstr "PNG"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Packages over 30 Kg are not accepted by bpost."
msgstr "30 кг-аас дээш багцыг bpost хүлээн авахгүй."

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_developer_password
msgid "Passphrase"
msgstr "Нэвтрэх үг"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Ядаж нэг тээвэрлэх зүйл оруулна уу."

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Үйлчилгээ үзүүлэгч"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid ""
"Return labels created into bpost <br/> <b>Tracking Numbers: </b><br/>%s"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Return shipment created into bpost <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__rta
msgid "Return to sender by air"
msgstr "Илгээгчрүү Агаараар буцаах"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__rts
msgid "Return to sender by road"
msgstr "Илгээгчрүү газраар буцаах"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__sample
msgid "SAMPLE"
msgstr "ЗУРАГ"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Shipment created into bpost <br/> <b>Tracking Links</b> <br/>%s"
msgstr ""

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Хүргэх аргууд"

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "The BPost shipping service is unresponsive, please retry later."
msgstr ""
"BPost хүргэлтийн үйлчилгээ хариу өгөхгүй байна, дараа дахин оролдоно уу."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The address of your company/warehouse is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr ""
"Таны компани/агуулахын хаягын мэдээлэл дутуу эсвэл буруу байна(Орхигдсон талбар(ууд):\n"
"%s)"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because all your products "
"are service."
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The recipient address is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr ""
"Хүлээн авагчийн хаяг дутуу эсвэл буруу байна (Дутуу талбар (ууд):\n"
"\n"
"%s)"

#. module: delivery_bpost
#: model:product.product,uom_name:delivery_bpost.product_product_delivery_bpost_domestic
#: model:product.product,uom_name:delivery_bpost.product_product_delivery_bpost_world
#: model:product.template,uom_name:delivery_bpost.product_product_delivery_bpost_domestic_product_template
#: model:product.template,uom_name:delivery_bpost.product_product_delivery_bpost_world_product_template
msgid "Units"
msgstr "Нэгж"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "You cannot compute a passphrase for non-bpost carriers."
msgstr "Та non-bpost дамжуулагчын нууц үгийг тооцоолох боломжгүй байна."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Your company/warehouse address must be in Belgium to ship with bpost"
msgstr "Таны компани/агуулахын хаяг bpost хүргэхийн тулд Бельгид байх ёстой"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_24h_pro
msgid "bpack 24h Pro"
msgstr "bpack 24ц Pro"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_24h_business
msgid "bpack 24h business"
msgstr "bpack 24ц бизнес"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_bus
msgid "bpack Bus"
msgstr "bpack Автобус"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_europe_business
msgid "bpack Europe Business"
msgstr "bpack Европ Бизнес"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_world_business
msgid "bpack World Business"
msgstr "bpack World Business"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_world_express_pro
msgid "bpack World Express Pro"
msgstr "bpack World Express Pro"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__delivery_type__bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__stock_package_type__package_carrier_type__bpost
msgid "bpost"
msgstr "bpost"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "bpost Configuration"
msgstr "bpost Тохиргоо"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_default_package_type_id
msgid "bpost Default Package Type"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"bpost Domestic is used only to ship inside Belgium. Please change the "
"delivery method into bpost International."
msgstr ""
"bpost Дотоодын бүтээгдэхүүнийг зөвхөн Бельгид тээвэрлэхэд ашигладаг тул "
"Хүргэлтийн аргыг bpost International болгон өөрчлөнө үү."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"bpost International is used only to ship outside Belgium. Please change the "
"delivery method into bpost Domestic."
msgstr ""
"bpost International нь зөвхөн Бельгиас гадна тээвэрлэхэд ашиглагддаг. "
"Хүргэлтийн аргыг bpost Domestic болгон солино уу."

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_stock
msgid "bpost Shipping Methods"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "bpost did not return prices for this destination country."
msgstr "bpost нь очих улсын үнийг буцаагаагүй."
