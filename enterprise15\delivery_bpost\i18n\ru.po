# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_bpost
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_stock_type__a4
msgid "A4"
msgstr "А4"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_stock_type__a6
msgid "A6"
msgstr "A6"

#. module: delivery_bpost
#: model:ir.model.fields,help:delivery_bpost.field_delivery_carrier__bpost_saturday
msgid "Allow deliveries on Saturday (extra charges apply)"
msgstr "Разрешить доставку в субботу (дополнительные сборы применяются)"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"Authentication error -- wrong credentials\n"
"(Detailed error: %s)"
msgstr "Ошибка аутентификации - ложные учетные данные (Подробная ошибка: %s)"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_account_number
msgid "Bpost Account Number"
msgstr "Номер счета Bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_delivery_nature
msgid "Bpost Delivery Nature"
msgstr "Описание доставки Bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_domestic_deliver_type
msgid "Bpost Domestic Deliver Type"
msgstr "Внутренний тип доставки Bpost"

#. module: delivery_bpost
#: model:delivery.carrier,name:delivery_bpost.delivery_carrier_bpost_domestic
#: model:product.product,name:delivery_bpost.product_product_delivery_bpost_domestic
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_domestic_product_template
msgid "Bpost Domestic bpack 24h Pro"
msgstr "Bpost Domestic bpack 24h Pro"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_international_deliver_type
msgid "Bpost International Deliver Type"
msgstr "Международный тип доставки Bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_label_format
msgid "Bpost Label Format"
msgstr "Формат логотипа Bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_label_stock_type
msgid "Bpost Label Stock Type"
msgstr "Тип логотипа состава Bpost"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Bpost Package Type"
msgstr "Тип пакета Bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_parcel_return_instructions
msgid "Bpost Parcel Return Instructions"
msgstr "Инструкция возврата посылок Bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_shipment_type
msgid "Bpost Shipment Type"
msgstr "Тип отправления Bpost"

#. module: delivery_bpost
#: model:delivery.carrier,name:delivery_bpost.delivery_carrier_bpost_inter
#: model:product.product,name:delivery_bpost.product_product_delivery_bpost_world
#: model:product.template,name:delivery_bpost.product_product_delivery_bpost_world_product_template
msgid "Bpost World Express Pro"
msgstr "Bpost World Express Pro"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Перевозчик"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__documents
msgid "DOCUMENTS"
msgstr "ДОКУМЕНТЫ"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_saturday
msgid "Delivery on Saturday"
msgstr "Доставка в субботу"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__abandoned
msgid "Destroy"
msgstr "уничтожить"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_delivery_nature__domestic
msgid "Domestic"
msgstr "Внутренние"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__gift
msgid "GIFT"
msgstr "ПОДАРОК"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__goods
msgid "GOODS"
msgstr "ТОВАРЫ"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_delivery_nature__international
msgid "International"
msgstr "Международный"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Label Format"
msgstr "Формат этикетки"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Label Type"
msgstr "Тип лейбла"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__other
msgid "OTHER"
msgstr "ДРУГОЕ"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "Options"
msgstr "Параметры"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_label_format__png
msgid "PNG"
msgstr "PNG"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Packages over 30 Kg are not accepted by bpost."
msgstr "Упаковка 30 кг bpost не принимает."

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_developer_password
msgid "Passphrase"
msgstr "парольная фраза"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Укажите, пожалуйста, хотя бы один элемент для отгрузки."

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Провайдер"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid ""
"Return labels created into bpost <br/> <b>Tracking Numbers: </b><br/>%s"
msgstr ""

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Return shipment created into bpost <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__rta
msgid "Return to sender by air"
msgstr "Вернуть отправителю самолетом"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_parcel_return_instructions__rts
msgid "Return to sender by road"
msgstr "Вернуть отправителю дорогой"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_shipment_type__sample
msgid "SAMPLE"
msgstr "Образец"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "Отправление №%s было отменено"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "Shipment created into bpost <br/> <b>Tracking Links</b> <br/>%s"
msgstr ""

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Методы доставки"

#. module: delivery_bpost
#: model:ir.model,name:delivery_bpost.model_stock_package_type
msgid "Stock package type"
msgstr "Тип складской упаковки"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "The BPost shipping service is unresponsive, please retry later."
msgstr "Услуга доставки BPost не отвечает, повторите попытку позже."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The address of your company/warehouse is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr ""
"Адрес вашей компании / склада является неполной или неправильной "
"(отсутствует поле (я): %s)"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because all your products "
"are service."
msgstr ""
"Расчетная стоимость доставки не может быть вычислена, поскольку все ваши "
"товары являются сервисными."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Расчетная цена доставки не может быть рассчитана, поскольку отсутствует вес для следующего товара (товаров):\n"
" %s"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"The recipient address is incomplete or wrong (Missing field(s):  \n"
" %s)"
msgstr "Адрес получателя неполная или неправильная (отсутствует поле (я): %s)"

#. module: delivery_bpost
#: model:product.product,uom_name:delivery_bpost.product_product_delivery_bpost_domestic
#: model:product.product,uom_name:delivery_bpost.product_product_delivery_bpost_world
#: model:product.template,uom_name:delivery_bpost.product_product_delivery_bpost_domestic_product_template
#: model:product.template,uom_name:delivery_bpost.product_product_delivery_bpost_world_product_template
msgid "Units"
msgstr "Единицы"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/delivery_bpost.py:0
#, python-format
msgid "You cannot compute a passphrase for non-bpost carriers."
msgstr "Вы не можете вычислить кодовую фразу никаких bpost перевозчиков."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "Your company/warehouse address must be in Belgium to ship with bpost"
msgstr ""
"Адрес вашей компании / склада должны быть в Бельгии, чтобы доставить их с "
"bpost"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_24h_pro
msgid "bpack 24h Pro"
msgstr "bpack 24h Pro"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_24h_business
msgid "bpack 24h business"
msgstr "bpack 24h business"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_domestic_deliver_type__bpack_bus
msgid "bpack Bus"
msgstr "автобус bpack"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_europe_business
msgid "bpack Europe Business"
msgstr "bpack Europe Business"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_world_business
msgid "bpack World Business"
msgstr "bpack World Business"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__bpost_international_deliver_type__bpack_world_express_pro
msgid "bpack World Express Pro"
msgstr "bpack World Express Pro"

#. module: delivery_bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__delivery_carrier__delivery_type__bpost
#: model:ir.model.fields.selection,name:delivery_bpost.selection__stock_package_type__package_carrier_type__bpost
msgid "bpost"
msgstr "bpost"

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.view_delivery_carrier_form_with_provider_bpost
msgid "bpost Configuration"
msgstr "Настройка bpost"

#. module: delivery_bpost
#: model:ir.model.fields,field_description:delivery_bpost.field_delivery_carrier__bpost_default_package_type_id
msgid "bpost Default Package Type"
msgstr "bpost Тип пакета по умолчанию"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"bpost Domestic is used only to ship inside Belgium. Please change the "
"delivery method into bpost International."
msgstr ""
"Отечественный bpost используется только для доставки в Бельгию. Пожалуйста, "
"измените метод доставки в Международный bpost."

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid ""
"bpost International is used only to ship outside Belgium. Please change the "
"delivery method into bpost Domestic."
msgstr ""
"Международный bpost используется только для доставки за пределами Бельгии. "
"Пожалуйста, измените метод доставки в адрес Отечественного bpost."

#. module: delivery_bpost
#: model_terms:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_bpost.res_config_settings_view_form_stock
msgid "bpost Shipping Methods"
msgstr "способы доставки bpost"

#. module: delivery_bpost
#: code:addons/delivery_bpost/models/bpost_request.py:0
#, python-format
msgid "bpost did not return prices for this destination country."
msgstr "bpost не вернул цены для этой страны назначения."
