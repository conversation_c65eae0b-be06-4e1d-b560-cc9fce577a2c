<?xml version='1.0' encoding='utf-8'?>
<odoo>
  <record id="view_delivery_carrier_form_with_provider_bpost" model="ir.ui.view">
    <field name="name">delivery.carrier.form.provider.bpost</field>
    <field name="model">delivery.carrier</field>
    <field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
    <field name="arch" type="xml">
      <xpath expr="//page[@name='destination']" position="before">
        <page string="bpost Configuration" name="bpost_configuration" attrs="{'invisible': [('delivery_type', '!=', 'bpost')]}">
          <group>
            <group>
              <field name="bpost_account_number" attrs="{'required': [('delivery_type', '=', 'bpost')]}"/>
              <field name="bpost_developer_password" attrs="{'required': [('delivery_type', '=', 'bpost')]}"/>
              <field name="bpost_delivery_nature" attrs="{'required': [('delivery_type', '=', 'bpost')]}"/>
              <field name="bpost_shipment_type" attrs="{'required': [('bpost_delivery_nature', '=', 'International')], 'invisible': [('bpost_delivery_nature', 'not in', 'International')]}"/>
              <field name="bpost_parcel_return_instructions" attrs="{'required': [('bpost_delivery_nature', '=', 'International')], 'invisible': [('bpost_delivery_nature', 'not in', 'International')]}"/>
            </group>
            <group>
              <field name="bpost_domestic_deliver_type" string="Bpost Package Type" attrs="{'required': [('bpost_delivery_nature', '=', 'Domestic')], 'invisible': [('bpost_delivery_nature', 'not in', 'Domestic')]}"/>
              <field name="bpost_international_deliver_type" string="Bpost Package Type" attrs="{'required': [('bpost_delivery_nature', '=', 'International')], 'invisible': [('bpost_delivery_nature', 'not in', 'International')]}"/>
              <field name="bpost_label_stock_type" string="Label Type" attrs="{'required': [('delivery_type', '=', 'bpost')]}"/>
              <field name="bpost_label_format" string="Label Format" attrs="{'required': [('delivery_type', '=', 'bpost')]}"/>
            </group>
            <field name="can_generate_return" invisible="1"/>
            <!-- Hide Options group if no visible fields inside it. -->
            <group string="Options" attrs="{'invisible': [('can_generate_return', '=', False), ('bpost_delivery_nature', 'not in', 'Domestic')]}">
              <field name="bpost_saturday" attrs="{'invisible': [('bpost_delivery_nature', 'not in', 'Domestic')]}"/>
              <field name="return_label_on_delivery" attrs="{'invisible': [('can_generate_return', '=', False)]}"/>
              <field name="get_return_label_from_portal" attrs="{'invisible': [('return_label_on_delivery', '=', False)]}"/>
            </group>
          </group>
        </page>
      </xpath>
    </field>
  </record>
</odoo>
