<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns="http://www.dhl.com/datatypes_global" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.dhl.com/datatypes_global" elementFormDefault="unqualified" attributeFormDefault="unqualified">
<xsd:element name="DataTypes">
	<xsd:annotation>
		<xsd:documentation>Comment describing your root element</xsd:documentation>
	</xsd:annotation>
</xsd:element>
<xsd:simpleType name="AccountNumber">
	<xsd:annotation>
		<xsd:documentation>DHL Account Number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="9" />
		<xsd:whiteSpace value="collapse"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="AccountType">
	<xsd:annotation>
		<xsd:documentation>Account Type by method of payment ( DHL account)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="D" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="AddressLine">
	<xsd:annotation>
		<xsd:documentation>Address Line</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="45"/>
			<xsd:whiteSpace value="collapse"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="AdvanceDaysNotice">
	<xsd:annotation>
		<xsd:documentation>Days of advance notice required</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:nonNegativeInteger">
		<xsd:pattern value="\d{0,3}" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="AWBNumber">
	<xsd:annotation>
		<xsd:documentation>Airway bill number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="10" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DHLRoutingCode">
	<xsd:annotation>
		<xsd:documentation>Routing Code Text</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string" />
</xsd:simpleType>
<xsd:simpleType name="InternalServiceCode">
	<xsd:annotation>
		<xsd:documentation>Handling feature code returned by GLS</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string" />
</xsd:simpleType>
<xsd:simpleType name="BillCode">
	<xsd:annotation>
		<xsd:documentation>DHL billing options</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="2" />
		<xsd:maxLength value="3" />
		<xsd:enumeration value="DSA" />
		<xsd:enumeration value="DBA" />
		<xsd:enumeration value="TCA" />
		<xsd:enumeration value="IEA" />
		<xsd:enumeration value="UAN" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Billing">
	<xsd:annotation>
		<xsd:documentation>The Billing element contains the billing information of the shipment</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ShipperAccountNumber" type="AccountNumber">
			<xsd:annotation>
				<xsd:documentation>The ShipperAccountNumber element contains the DHL account number. This element must be declared once in the Bill element</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>		
		<xsd:element name="ShippingPaymentType" type="ShipmentPaymentType">
			<xsd:annotation>
				<xsd:documentation>The ShippingPaymentType element defines the method of payment. It is a mandatory field. The valid values are S:Shipper, R:Recipient and T:Third Party</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
		<xsd:element name="BillingAccountNumber" type="AccountNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The BillingAccountNumber element contains billing account number.This element is required even if the payment method is third party or consignee</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
		<xsd:element name="DutyAccountNumber" type="AccountNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DutyAccountNumber element contains the account number which is used in duty and tax payment</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="City">
	<xsd:annotation>
		<xsd:documentation>City name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CommodityCode">
	<xsd:annotation>
		<xsd:documentation>Commodity codes for shipment type</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="2" />
		<xsd:maxLength value="18" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CommodityName">
	<xsd:annotation>
		<xsd:documentation>Commodity name for shipment content</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Commodity">
	<xsd:annotation>
		<xsd:documentation>The commodity element identifies the commodity or commodities being shipped. This element should be declared once in the shipment validation request message</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="CommodityCode" type="CommodityCode">
			<xsd:annotation>
				<xsd:documentation>The CommodityCode element contains the commodity code of the shipment contents. Its value should be between 2 and 18 characters of string. This field is mandatory</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CommodityName" type="CommodityName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CommodityName element contains the commodity name of the shipment content. Its value should be no more than 35 chars. This field is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="CommunicationAddress">
	<xsd:annotation>
		<xsd:documentation>Communications line number: phone number, fax number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="50" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CommunicationType">
	<xsd:annotation>
		<xsd:documentation>Communications line type (P: phone, F: fax)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="P" />
		<xsd:enumeration value="F" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CompanyNameValidator">
	<xsd:annotation>
		<xsd:documentation>Name of company / business</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="2" />
		<xsd:maxLength value="60" />
		<xsd:whiteSpace value="collapse"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="SuiteDepartmentName">
	<xsd:annotation>
		<xsd:documentation>SuiteDepartmentName</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Consignee">
	<xsd:annotation>
		<xsd:documentation>Consignee element contains the details of the Consignee (Receiver of the shipment). This element should be declared once in the shipment validation request message</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="CompanyName" type="CompanyNameValidator">
			<xsd:annotation>
				<xsd:documentation>The CompanyName element contains the name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SuiteDepartmentName" type="SuiteDepartmentName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SuiteDepartmentName element contains the suite or department name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 1 element contains the address line 1 to the Consignee. It is mandatory field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 2 element contains the address line 2 to the Consignee.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 3 element contains the address line 3 to the Consignee.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the consignee address. It must be declared in the Consignee element for Country/Region that used city</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division name of the consignee location, for instance State, County, Prefecture, etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The division code of the consignee location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Postal Code element contains the postal code of the consignee location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The Country Code element contains the Country/Region code of the consignee location. It must be 2 letters. Please refer to the Reference Data (DHL Country/Region) for country/region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryName" type="CountryName">
			<xsd:annotation>
				<xsd:documentation>The CountryName element contains Country/Region name of the consignee. Its value should be a valid DHL Country/Region name. This element must be declared once in the Consignee element. Please refer to the Reference Data (DHL Country/Region) for a list of valid Country/Region names</xsd:documentation>
			</xsd:annotation>
		</xsd:element>		
		<xsd:element name="Contact" type="Contact">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the receiver’s contact information. It must be declared in the consignee element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains consignee’s suburb name. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetName" type="StreetName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s street name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BuildingName" type="BuildingName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s Building Name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetNumber" type="StreetNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s It is an optional field. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s contact information.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s Business Party Type Code. </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="Contact">
	<xsd:annotation>
		<xsd:documentation>The Contact element contains the receiver’s contact information. It must be declared in the consignee element</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="PersonName" type="PersonName">
			<xsd:annotation>
				<xsd:documentation>The PersonName element contains the name of the receiver.  It is a mandatory element in the consignee’s Contact element. It is a string with a maximum length of 35</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PhoneNumber" type="PhoneNumber">
			<xsd:annotation>
				<xsd:documentation>The Phone element contains the receiver’s phone number. It is a mandatory element in the consignee’s Contact Element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PhoneExtension" type="PhoneExtension" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PhoneExtension element is the extension number of the consignee. It is an optional element in the consignee’s contact Element. The value must be maximum 5 digits long</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="FaxNumber" type="PhoneNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the fax number of the consignee. It is an optional element in the consignee contact Element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Telex" type="Telex" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains telex number and answer back code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Email" type="EmailAddress" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the email address of the consignee</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="MobilePhoneNumber" type="MobilePhoneNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The MobilePhoneNumber element contains the consignee’s mobile phone number. It is an optional element in the consignee’s contact Element. The value consist of maximum 25 numbers</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="MobilePhoneNumber">
	<xsd:annotation>
		<xsd:documentation>Mobile Phone Number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:positiveInteger">
		<xsd:maxInclusive value="9999999999999999999999999" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PhoneExtension">
	<xsd:annotation>
		<xsd:documentation>Phone Extension</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="5" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CountryCode">
	<xsd:annotation>
		<xsd:documentation>ISO country codes</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="2" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CountryName">
	<xsd:annotation>
		<xsd:documentation>ISO country name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Suburb">
	<xsd:annotation>
		<xsd:documentation>Suburb name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="StreetName">
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="35"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BuildingName">
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="35"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="StreetNumber">
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="15"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:simpleType name="CourierMsg">
	<xsd:annotation>
		<xsd:documentation>Courier message for printing on airway bill</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="90" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CurrencyCode">
	<xsd:annotation>
		<xsd:documentation>ISO currency code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:pattern value="[^ ].*[^ ]" />
		<xsd:whiteSpace value="collapse"/>
		<xsd:length value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CurrencyName">
	<xsd:annotation>
		<xsd:documentation>ISO currency name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DateTime">
	<xsd:annotation>
		<xsd:documentation>Date and time</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:dateTime" />
</xsd:simpleType>
<xsd:simpleType name="Date">
	<xsd:annotation>
		<xsd:documentation>Date only</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:date">
		<xsd:pattern value="[0-9][0-9][0-9][0-9](-)[0-9][0-9](-)[0-9][0-9]" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DayHour">
	<xsd:annotation>
		<xsd:documentation>Day and hour only</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:pattern value="(0[1-9]|1[0-9]|2[0-9]|3[0-1]) ([01-24])" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DoorTo">
	<xsd:annotation>
		<xsd:documentation>Defines the type of delivery service that applies to the shipment</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="2" />
		<xsd:enumeration value="DD" />
		<xsd:enumeration value="DA" />
		<xsd:enumeration value="AA" />
		<xsd:enumeration value="DC" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Dutiable">
	<xsd:annotation>
		<xsd:documentation>For non-domestic shipments, the Dutiable element provides information which defines the types of duties to be levied</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="DeclaredValue" type="DeclaredValue_Money" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DeclaredValue element contains the shipment’s monetary value . It is required for non-doc or dutiable products. Please refer to Reference Data (Global Product Codes)</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DeclaredCurrency" type="CurrencyCode">
			<xsd:annotation>
				<xsd:documentation>The DeclaredCurrency element contains the DHL currency code in which the declared value is specified.This field is required if a declared value is given. Please refer to the Reference Data (DHL Country/Region) for a valid currency code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ScheduleB" type="ScheduleB" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ScheduleB element contains the Schedule B info</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ExportLicense" type="ExportLicense" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ExportLicense element contains the Export License info</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ImportLicense" type="ImportLicense" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ImportLicense element contains the consignee import license number. It is an optional field in the Dutiable segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="TermsOfTrade" type="TermsOfTrade">
			<xsd:annotation>
				<xsd:documentation>The TermsOfTrade element contains the codes for terms of trade of the shipment. It is required for dutiable shipment. Please refer to the Reference Data and only use the codes defined</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CommerceLicensed" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CommerceLicensed element indicates the shipment is a Commerce-Licensed shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Filing" type="Filing" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Filing element represents the filing type for the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="ExportLicense">
	<xsd:annotation>
		<xsd:documentation>ExportLicense</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="16" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ImportLicense">
	<xsd:annotation>
		<xsd:documentation>ImportLicense</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="16" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="TermsOfTrade">
	<xsd:annotation>
		<xsd:documentation>TermsOfTrade</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="3" />
		<xsd:enumeration value="CFR" />
		<xsd:enumeration value="CIF" />
		<xsd:enumeration value="CIP" />
		<xsd:enumeration value="CPT" />
		<xsd:enumeration value="DAF" />
		<xsd:enumeration value="DAP" />
		<xsd:enumeration value="DAT" />
		<xsd:enumeration value="DDP" />
		<xsd:enumeration value="DDU" />
		<xsd:enumeration value="DEQ" />
		<xsd:enumeration value="DES" />
		<xsd:enumeration value="DVU" />
		<xsd:enumeration value="EXW" />
		<xsd:enumeration value="FAS" />
		<xsd:enumeration value="FCA" />
		<xsd:enumeration value="FOB" />
		<xsd:enumeration value="DPU"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Filing">
	<xsd:annotation>
		<xsd:documentation>The Filing element represents the filing type for the shipment</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="FilingType" type="FilingType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The FilingType element represents the type of filing  used for the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="FTSR" type="FTSR" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The FTSR element represents the FTR Exemption Code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ITN" type="ITN" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ITN element represents the Internal Transaction Number (ITN) provided by the US Customs AES system. This number should be in the format starting with an“X” followed by 14 digits</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AES4EIN" type="AES4EIN" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AES4EIN element represents Exporter Identification Number of the AESPOST approved entity. Typically this is an EIN Tax ID number or a SSN</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="FilingType">
	<xsd:annotation>
		<xsd:documentation>FilingType</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="3" />
		<xsd:maxLength value="4" />
		<xsd:enumeration value="FTR" />
		<xsd:enumeration value="ITN" />
		<xsd:enumeration value="AES4" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="FTSR">
	<xsd:annotation>
		<xsd:documentation>FTSR</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="5" />
		<xsd:maxLength value="10" />
		<xsd:enumeration value="30.2(d)(2)" />
		<xsd:enumeration value="30.36" />
		<xsd:enumeration value="30.37(a)" />
		<xsd:enumeration value="30.37(b)" />
		<xsd:enumeration value="30.37(e)" />
		<xsd:enumeration value="30.37(f)" />
		<xsd:enumeration value="30.37(g)" />
		<xsd:enumeration value="30.37(h)" />
		<xsd:enumeration value="30.37(j)" />
		<xsd:enumeration value="30.37(k)" />
		<xsd:enumeration value="30.39" />
		<xsd:enumeration value="30.40(a)" />
		<xsd:enumeration value="30.40(b)" />
		<xsd:enumeration value="30.40(c)" />
		<xsd:enumeration value="30.40(d)" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ITN">
	<xsd:annotation>
		<xsd:documentation>ITN</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="15" />
		<xsd:pattern value="X[0-9]{14}" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="AES4EIN">
	<xsd:annotation>
		<xsd:documentation>AES4</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="11" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DeclaredValue">
	<xsd:annotation>
		<xsd:documentation>DeclaredValue</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:float">
		<xsd:minInclusive value="0.00" />
		<xsd:maxInclusive value="9999999999.99" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DivisionCode">
	<xsd:annotation>
		<xsd:documentation>Division (e.g. state, prefecture, etc.) code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Division">
	<xsd:annotation>
		<xsd:documentation>Division (e.g. state, prefecture, etc.) name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="EmailAddress">
	<xsd:annotation>
		<xsd:documentation>Email address containing '@'</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="50" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="ExportDeclaration">
	<xsd:annotation>
		<xsd:documentation>For non-domestic shipments, the ExportDeclaration element provides information which is used to aid in the export of a shipment</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="InterConsignee" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The InterConsignee element contains the name and address of the intermediate consignee. It is an optional field</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:maxLength value="70" />
					<xsd:minLength value="0" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="IsPartiesRelation" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The IsPartiesRelation element indicates the relation of parties to the transaction</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
		<xsd:element name="ECCN" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains ECCN info</xsd:documentation>
			</xsd:annotation>	
			<xsd:simpleType>			
				<xsd:restriction base="xsd:string">
					<xsd:minLength value="0" />
					<xsd:maxLength value="30" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="SignatureName" type="SignatureName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SignatureName element contains the Signatory’s name. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SignatureTitle" type="SignatureTitle" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SignatureTitle element contains the Signatory’s title. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ExportReason" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the reason for exporting. It is an optional element</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:maxLength value="30" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="ExportReasonCode" type="ExportReasonCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the reason code for exporting.It is a conditional element. The valid values are P:Permanent, T:Temporary, R:Re-Export. It is required for dutiable shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SedNumber" type="SEDNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the SED number. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SedNumberType" type="SEDNumberType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the SED number type. It is an optional element. This field is required if SED number is given. The valid values are one of: F (for FTSR), X (for XTN) or S (for SAS)</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="InvoiceNumber" type="InvoiceNumber">
			<xsd:annotation>
				<xsd:documentation>This element contains the Custom Invoice Number. It is a mandatory element for custom invoice details</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="InvoiceDate" type="xsd:date">
			<xsd:annotation>
				<xsd:documentation>This element contains the Custom Invoice date. It is a mandatory element for custom invoice details</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToCompanyName" type="CompanyNameValidator" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Company Name if it is different from Receiver’s Company Name. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToContactName" type="PersonName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Contact Name if it is different from Receiver’s Contact Name. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToAddressLine1" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Contact Address Lines 1 if it is different from Receiver’s Address Line 1.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToAddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Contact Address Lines 2 if it is different from Receiver’s Address Line 2.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToAddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Contact Address Lines 3 if it is different from Receiver’s Address Line 3.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToCity" type="City" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To City if it is different from Receiver’s City. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToPostcode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Postcode if it is different from Receiver’s Postcode. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToSuburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Suburb if it is different from Receiver’s Suburb. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToState" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To State if it is different from Receiver’s State. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToStateName" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To State Name if it is different from Receiver’s State Name. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToCountryCode" type="CountryCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Country Code if it is different from Receiver’s Country Code. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToCountryName" type="CountryName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Country/Region Name if it is different from Receiver’s Country/Region Name. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToPhoneNumber" type="PhoneNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Phone Number if it is different from Receiver’s Phone Number. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToPhoneNumberExtn" type="PhoneExtension" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Phone Number Extension if it is different from Receiver’s Phone Number Extension. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToFaxNumber" type="PhoneNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Billing To Fax Number if it is different from Receiver’s Fax Number. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToStreetName" type="StreetName" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Street Name if it is different from Receiver’s Street Name.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToBuildingName" type="BuildingName" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Building Name if it is different from Receiver’s Building Name.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToStreetNumber" type="StreetNumber" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Street Number if it is different from Receiver’s Street Number.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToEmail" type="EmailAddress" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Email Address if it is different from Receiver’s Email Address.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToMobilePhoneNumber" type="MobilePhoneNumber" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Mobile Phone Number if it is different from Receiver’s Mobile Phone Number.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToRegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Registration Numbers if it is different from Receiver’s Registration Numbers.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="BillToBusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>This element contains the Billing To Business Party Type Code if it is different from Receiver’s Business Party Type Code.  It is an optional element</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
		<xsd:element name="Remarks" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Remarks . It is an optional element for custom invoice details</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OtherCharges" type="OtherCharges" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the other charges that applicable for the shipment. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="TermsOfPayment" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Terms of payment of the shipment. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SignatureImage" type="SignatureImage" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Signature image for the custom invoice document. It is required to be in base64 value with valid format of PNG, GIF, JPEG, JPG. The maximum size is 1 MB</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ReceiverReference" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Receiver’s Reference details for custom invoice. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ExporterId" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Exporter ID details for custom invoice. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ExporterCode" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Exporter Code of the shipment. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PackageMarks" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Package Marks details of the shipment. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OtherRemarks2" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Other Remarks 2 for custom invoice. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OtherRemarks3" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Other Remarks 3 for custom invoice. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankINN" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank INN details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankKPP" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank KPP details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankOKPO" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank OKPO details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankOGRN" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank OGRN details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankSettlementAcctNumUSDEUR" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank Settlement Account Number in USD/EUR currency details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankSettlementAcctNumRUR" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank Settlement Account Number in RUR currency details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RUBankName" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the Russia Bank Name details of the shipment. It is an optional element. Note: this is applicable for Russia Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddDeclText1" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the additional declaration text 1. It is an optional element</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:maxLength value="700" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="AddDeclText2" type="AddDeclText" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the additional declaration text 2. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddDeclText3" type="AddDeclText" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the additional declaration text 3. It is an optional element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ExportLineItem" type="ExportLineItem" maxOccurs="999">
			<xsd:annotation>
				<xsd:documentation>Specifics about each of the line item</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ShipmentDocument" type="ShipmentDocument" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ShipmentDocument contains the shipment’s document reference and type name details.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="InvoiceInstructions" type="InvoiceInstructions" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The InvoiceInstructions contains the invoice instructions details.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerDataTextEntries" type="CustomerDataTextEntries" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerDataTextEntries contains the Customer Data Text entries detail.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PlaceOfIncoterm" type="PlaceOfIncoterm" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Name of port of departure, shipment or destination, as required under the applicable delivery term.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ShipmentPurpose" type="ShipmentPurpose" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ShipmentPurpose indicates if the shipment was sent for Personal (Gift) or Commercial (Sale) reasons.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DocumentFunction" type="DocumentFunction" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DocumentFunction describes for what purpose was the document details captured and area planned to be used.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomsDocuments" type="CustomsDocuments" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomsDocuments contains the customs documents details.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="InvoiceTotalNetWeight" type="Invoice_Weight" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>The gross weight consists of the net weight and the weight of the packaging (tare weight)</xsd:documentation>
		</xsd:annotation>		
		</xsd:element>
		<xsd:element name="InvoiceTotalGrossWeight" type="Invoice_Weight" minOccurs="0">
		<xsd:annotation>
			<xsd:documentation>The net weight is calculated as follows: gross weight minus weight of packaging (tare weight)</xsd:documentation>
			</xsd:annotation>		
		</xsd:element>
		<xsd:element name="InvoiceReferences" type="InvoiceReferences" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The InvoiceReferences contains the invoice references details.It is a optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
	<xsd:simpleType name="Invoice_Weight">
		<xsd:annotation>
			<xsd:documentation>Weight of piece or shipment</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:fractionDigits value="3"/>
			<xsd:minInclusive value="0.000"/>
			<xsd:maxInclusive value="999999999999.999"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="OtherCharges">
	<xsd:annotation>
				<xsd:documentation>Other Charges caption and its charges value details</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence maxOccurs="5">
		<xsd:element name="OtherCharge" type="OtherCharge">
			<xsd:annotation>
				<xsd:documentation>Other Charge caption and its charge value</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="OtherCharge">
		<xsd:annotation>
			<xsd:documentation>Other Charge caption and its charge value</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="OtherChargeCaption" type="OtherChargeCaption" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Other Charges Caption</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="OtherChargeValue" type="OtherChargeValue">
			<xsd:annotation>
				<xsd:documentation>Other Charges Value</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="OtherChargeType" type="OtherChargeType">
				<xsd:annotation>
					<xsd:documentation>Charge Type: Other Charges, Freight Cost, Insurance Cost, etc Other Charges Type</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="OtherChargeCaption">
		<xsd:annotation>
			<xsd:documentation>Other Charges Caption</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
	<xsd:simpleType name="OtherChargeValue">
	<xsd:annotation>
			<xsd:documentation>Other Charges Value</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="15"/>
			<xsd:fractionDigits value="3"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="OtherChargeType">
		<xsd:annotation>
			<xsd:documentation>Other Charge type (ADMIN:Administration Charge/Fee/Cost,DELIV:Delivery Charge/Fee/Cost,
				DOCUM:Documentation Charge/Fee/Cost,EXPED:Expedite Charge/Fee/Cost,EXCHA:Export Charge/Fee/Cost,
				FRCST:Freight/Shipping Charge/Fee/Cost,SSRGE:Fuel Surcharge,LOGST:Logistic Charge/Fee/Cost,
				SOTHR:Other Charge/Fee/Cost,SPKGN:Packaging/Packing Charge/Fee/Cost,PICUP:Pickup Charge/Fee/Cost,
				HRCRG:Handling Charge/Fee/Cost,VATCR:Charge/Fee/Cost,INSCH:Insurance Cost (Fee)
				REVCH:Reverse Charge,</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="5"/>
			<xsd:maxLength value="5"/>
				<xsd:enumeration value="ADMIN"/>
				<xsd:enumeration value="DELIV"/>
				<xsd:enumeration value="DOCUM"/>
				<xsd:enumeration value="EXPED"/>
				<xsd:enumeration value="EXCHA"/>
				<xsd:enumeration value="FRCST"/>
				<xsd:enumeration value="SSRGE"/>
				<xsd:enumeration value="LOGST"/>
				<xsd:enumeration value="SOTHR"/>
				<xsd:enumeration value="SPKGN"/>
				<xsd:enumeration value="PICUP"/>
				<xsd:enumeration value="HRCRG"/>
				<xsd:enumeration value="VATCR"/>
				<xsd:enumeration value="INSCH"/>
				<xsd:enumeration value="REVCH"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="BusinessPartyTypeCode">
		<xsd:annotation>
			<xsd:documentation>Business Party Trader type</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="2"/>
			<xsd:enumeration value="BU"/>
			<xsd:enumeration value="DC"/>
			<xsd:enumeration value="GV"/>
			<xsd:enumeration value="OT"/>
			<xsd:enumeration value="PR"/>
			<xsd:enumeration value="RE"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="ExportLineItem">
	<xsd:annotation>
		<xsd:documentation>Specifics about each of the line item</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="LineNumber" type="LineNumber">
			<xsd:annotation>
				<xsd:documentation>The LineNumber element contains the serial number for particular item.It’s value should not be more than 999. It is a mandatory field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Quantity" type="Quantity">
			<xsd:annotation>
				<xsd:documentation>The Quantity element contains the number of pieces in the line item.It is a mandatory field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="QuantityUnit" type="QuantityUnit">
			<xsd:annotation>
				<xsd:documentation>This element contains the measurement unit for quantity.It is a mandatory field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Description">
			<xsd:annotation>
				<xsd:documentation>This element contains the description of the line item. It is a mandatory field. The maximum length is 75 characters</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:maxLength value="75" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="Value">
			<xsd:annotation>
				<xsd:documentation>This element identifies the value in money levied for each quantity of the particular line item. It is a mandatory field. Note: Total value of the all quantity per each line item will be calculated automatically by backend system</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
					<xsd:restriction base="xsd:decimal">
						<xsd:minInclusive value="0.000"/>
						<xsd:maxInclusive value="999999999999999.999"/>
					</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="IsDomestic" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The IsDomestic element indicates whether shipment is domestic or not. The valid vaues are Y (Yes) and N (No)</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CommodityCode" type="CommodityCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CommodityCode element contains commodity code for shipment contents. Its value should lie in between 2 to 18 characters of string. This field is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ScheduleB" type="ScheduleB" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ScheduleB element contains the schedule B number.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ECCN" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains ECCN info</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:minLength value="0" />
					<xsd:maxLength value="30" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="Weight">
			<xsd:annotation>
				<xsd:documentation>This element contains information about weight of the line item.It is an optional field</xsd:documentation>
			</xsd:annotation>
			<xsd:complexType>
				<xsd:sequence>
					<xsd:element name="Weight" type="ExportLineItem_Weight">
						<xsd:annotation>
							<xsd:documentation>The Weight Element represents the weight of the individual piece in the line item. It is a mandatory field</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="WeightUnit" type="WeightUnit">
						<xsd:annotation>
							<xsd:documentation>The Weight Unit element contains the Unit by which the shipment weight is measured. It must be declared in the Weight element. The valid value for this element is K (Kilograms). Please refer to Reference Data (DHL Country/Region) for valid weight unit</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:complexType>
		</xsd:element>
		<xsd:element name="GrossWeight">
			<xsd:annotation>
				<xsd:documentation>This element contains information about gross weight of the line item. It is an mandatory field</xsd:documentation>
			</xsd:annotation>
			<xsd:complexType>
				<xsd:sequence>
					<xsd:element name="Weight" type="ExportLineItem_Weight">
						<xsd:annotation>
							<xsd:documentation>The Weight Element represents the total weight of all the piece in the line item. It is a mandatory field</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="WeightUnit" type="WeightUnit">
						<xsd:annotation>
							<xsd:documentation>The Weight Unit element contains the Unit by which the shipment weight is measured. It must be declared in the Weight element. The valid value for this element is K (Kilograms). Please refer to Reference Data (DHL Country/Region) for valid weight unit</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:complexType>
		</xsd:element>
		<xsd:element name="License" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains the shipper export licence information of the paticular item.It is an optional field</xsd:documentation>
			</xsd:annotation>
			<xsd:complexType>
				<xsd:sequence>
					<xsd:element name="LicenseNumber" type="LicenseNumber">
						<xsd:annotation>
							<xsd:documentation>Shipper export license</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="ExpiryDate" type="xsd:date">
						<xsd:annotation>
							<xsd:documentation>Shipper export license</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:complexType>
		</xsd:element>
		<xsd:element name="LicenseSymbol" type="LicenseNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>License excption symbol</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ManufactureCountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>This element allows user to capture Country/Region of Manufacture in Invoice Line item.  It is a conditional field and required for dutiable shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ManufactureCountryName"  type="CountryName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element allows user to capture Country/Region Name of Manufacture in Invoice Line item.  It is a conditional field  and required for dutiable shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ImportTaxManagedOutsideDhlExpress" type="ImportTaxManagedOutsideDhlExpress" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element allows user to indicates if import tax managed outside or DHL Express. This is an optional field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AdditionalInformation" type="AdditionalInformationType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element allows user to indicates if import tax managed outside or DHL Express. This is an optional field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ImportCommodityCode" type="CommodityCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Inbound Commodity Classification</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ItemReferences" type="ItemReferences" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contain Item References details. This is an optional field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomsPaperworks" type="CustomsPaperworks" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contain Customs Paperworks details. This is an optional field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
	<xsd:simpleType name="ImportTaxManagedOutsideDhlExpress">
		<xsd:annotation>
			<xsd:documentation>ImportTaxManagedOutsideDhlExpress</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Y"/>
			<xsd:enumeration value="N"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="AdditionalInformationType">
	<xsd:annotation>
		<xsd:documentation>Additional Informations for export line item</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence maxOccurs="8">
		<xsd:element name="AdditionalInformationText" type="AdditionalInformationText">
		<xsd:annotation>
			<xsd:documentation>Declaration text</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="AdditionalInformationText">
		<xsd:annotation>
			<xsd:documentation>Declaration text</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
<xsd:simpleType name="SignatureName">
	<xsd:annotation>
		<xsd:documentation>Signature name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:complexType name="ItemReferences">
	<xsd:annotation>
			<xsd:documentation>Export Declaration Line Item's References</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence maxOccurs="100">
			<xsd:element name="ItemReference" type="ItemReference">
				<xsd:annotation>
					<xsd:documentation>Customs Invoice Reference entry</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
	</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ItemReference">
		<xsd:all>
			<xsd:annotation>
				<xsd:documentation>Customs Invoice Reference entry</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="ItemReferenceType" type="ItemReferenceType">
			<xsd:annotation>
			<xsd:documentation>Export Declaration line items's Reference Type</xsd:documentation>
		</xsd:annotation>
		</xsd:element>
			<xsd:element name="ItemReferenceNumber" type="ItemReferenceNumber">
				<xsd:annotation>
					<xsd:documentation>Export Declaration line items's Reference Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="ItemReferenceType">
		<xsd:annotation>
			<xsd:documentation>Item Reference Type(AFE: Export Control Classification Number (ECCN), AAJ:Delivery Order number, ABW: Stock Keeping Unit, 
			ALX: Model, BRD: Brand,DGC: Dangerous Goods Content Identifier, DTC: DDTC Eligibility indicator, DTM: DDTC UOM, DTQ: DDTC Quantity, DTR: DDTC Registraton No,
			ITR: DDTC ITAR Exemption No. MAK: Make, MID: Manufacturers IDentification code, OED: Original Export Date, OET: Original Export OB Tracking ID,
			OID: Order ID, OOR: Original Outbound Carrier, PAN: Part No, PON: Purchase Order Number, SON: Sales order No, SE:Serial number, SME: DDTC SME Indicator,
			USM: DDTC USML Category Code, AAM: AWB Ref #,
			CFR:ECCN License Exemption (CFR#), DOM:Domestic indicator CoO (US), FOR:Foreign indicator CoO (US),
			USG: Global Usage, MAT: Global Material)
			</xsd:documentation>
		</xsd:annotation>
			<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="3"/>
				<xsd:enumeration value="AAJ"/>
				<xsd:enumeration value="ABW"/>
				<xsd:enumeration value="AFE"/> 
				<xsd:enumeration value="ALX"/>
				<xsd:enumeration value="BRD"/>
				<xsd:enumeration value="DGC"/>
				<xsd:enumeration value="INB"/>
				<xsd:enumeration value="MAK"/>
				<xsd:enumeration value="PAN"/>
				<xsd:enumeration value="PON"/>
				<xsd:enumeration value="SE"/>
				<xsd:enumeration value="SON"/>
				<xsd:enumeration value="OID"/>
				<xsd:enumeration value="DTC"/>
				<xsd:enumeration value="DTM"/>
				<xsd:enumeration value="DTQ"/>
				<xsd:enumeration value="DTR"/>
				<xsd:enumeration value="ITR"/>
				<xsd:enumeration value="MID"/>
				<xsd:enumeration value="OED"/>
				<xsd:enumeration value="OET"/>
				<xsd:enumeration value="OOR"/>
				<xsd:enumeration value="SME"/>
				<xsd:enumeration value="USM"/>
				<xsd:enumeration value="AAM"/>
				<xsd:enumeration value="CFR"/>
				<xsd:enumeration value="DOM"/>
				<xsd:enumeration value="FOR"/>
				<xsd:enumeration value="USG"/>
				<xsd:enumeration value="MAT"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ItemReferenceNumber">
		<xsd:annotation>
			<xsd:documentation>Export Declaration line items's Reference Number</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="CustomsPaperworks">
		<xsd:annotation>
			<xsd:documentation>Export Declaration Line Item's Customs Paperworks</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="50">
			<xsd:element name="CustomsPaperwork" type="CustomsPaperwork">
				<xsd:annotation>
					<xsd:documentation>Customs Invoice Reference entry</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CustomsPaperwork">
		<xsd:all>
			<xsd:annotation>
				<xsd:documentation>Customs Invoice Reference entry</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="CustomsPaperworkType" type="CustomsPaperworkType"/>
			<xsd:element name="CustomsPaperworkID" type="CustomsPaperworkID"/>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="CustomsPaperworkType">
		<xsd:annotation>
			<xsd:documentation>Export Declaration line items's Customs Paperwork Type (CHA:Power of Attorney,
			POA:Power of Attorney (Customer-based),PPY:Proof Of Payment,ROD:Receipt on Delivery,
			T2M:T2M Transport Accompanying Document,TAD:TAD Transport Accompanying Document T1,
			TCS:Transportation Charges Statement,ELP:Export Licenses and Permits)</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="3"/>
			<xsd:enumeration value="972"/>
			<xsd:enumeration value="AHC"/>
			<xsd:enumeration value="ATA"/>
			<xsd:enumeration value="ATR"/>
			<xsd:enumeration value="CHD"/>
			<xsd:enumeration value="CHP"/>
			<xsd:enumeration value="CIT"/>
			<xsd:enumeration value="COO"/>
			<xsd:enumeration value="DEX"/>
			<xsd:enumeration value="EU1"/>
			<xsd:enumeration value="EU2"/>
			<xsd:enumeration value="EUS"/>
			<xsd:enumeration value="FMA"/>
			<xsd:enumeration value="PHY"/>
			<xsd:enumeration value="VET"/>
			<xsd:enumeration value="VEX"/>
			<xsd:enumeration value="CRL"/>
			<xsd:enumeration value="CSD"/>
			<xsd:enumeration value="PPY"/>
			<xsd:enumeration value="CI2"/>
			<xsd:enumeration value="CIV"/>
			<xsd:enumeration value="DOV"/>
			<xsd:enumeration value="INV"/>
			<xsd:enumeration value="PFI"/>
			<xsd:enumeration value="ALC"/>
			<xsd:enumeration value="HLC"/>
			<xsd:enumeration value="JLC"/>
			<xsd:enumeration value="LIC"/>
			<xsd:enumeration value="LNP"/>
			<xsd:enumeration value="PLI"/>
			<xsd:enumeration value="DLI"/>
			<xsd:enumeration value="NID"/>
			<xsd:enumeration value="PAS"/>
			<xsd:enumeration value="CHA"/>
			<xsd:enumeration value="CPA"/>
			<xsd:enumeration value="POA"/>
			<xsd:enumeration value="BEX"/>
			<xsd:enumeration value="DGD"/>
			<xsd:enumeration value="IPA"/>
			<xsd:enumeration value="T2M"/>
			<xsd:enumeration value="TAD"/>
			<xsd:enumeration value="TCS"/>
			<xsd:enumeration value="ROD"/>
			<xsd:enumeration value="EXL"/>
			<xsd:enumeration value="HWB"/>
			<xsd:enumeration value="ELP"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomsPaperworkID">
		<xsd:annotation>
			<xsd:documentation>Export Declaration line items's Customs Paperwork ID</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:simpleType name="SignatureTitle">
	<xsd:annotation>
		<xsd:documentation>Signature title</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="ExportReasonCode">
		<xsd:annotation>
			<xsd:documentation>Export reason code (P:Permanent,R:Return (For Repair),T:Temporary,M:Used Exhibition Goods To Origin,I:Intercompany Use,
			C:Commercial Purpose Or Sale,E:Personal Belongings or Personal Use,S:Sample,G:Gift,U:Return To Origin,W:Warranty Replacement,
			D:Diplmatic Goods,F:Defenece Material)
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:length value="1"/>
			<xsd:enumeration value="P"/>
			<xsd:enumeration value="T"/>
			<xsd:enumeration value="R"/>
			<xsd:enumeration value="M"/>
			<xsd:enumeration value="I"/>
			<xsd:enumeration value="C"/>
			<xsd:enumeration value="E"/>
			<xsd:enumeration value="S"/>
			<xsd:enumeration value="G"/>
			<xsd:enumeration value="U"/>
			<xsd:enumeration value="W"/>
			<xsd:enumeration value="D"/>
			<xsd:enumeration value="F"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:simpleType name="GlobalProductCode">
	<xsd:annotation> 
		<xsd:documentation>The GlobalProductCode Element is global product code for the shipment. It is the optional field in the Shipment Details segment. It is an optional field. Please refer to Reference Data (Global Product Codes)</xsd:documentation> 
	</xsd:annotation> 
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="4" />
		<xsd:pattern value="([A-Z0-9])*" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="IDType">
	<xsd:annotation>
		<xsd:documentation>ID Type</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="SSN" />
		<xsd:enumeration value="EIN" />
		<xsd:enumeration value="DUNS" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LanguageCode">
	<xsd:annotation>
		<xsd:documentation>ISO Language Code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="2" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="InvLanguageCode">
	<xsd:annotation>
		<xsd:documentation>
			Invoice Language Code
			bg=Bugalrian br=Portuguse Brazil bs=Bosnian
			cs=Czech da=Danish de=German el=Greek en=English ep=English with SVP
			et=Estonian fi=Finnish fr=French he=Hebrew hr=Croatian hu=Hungaria
			is=Icelandic it=Italian lt=Lithuanian lv=Latvian mk=Macedon nl=Dutch
			no=Norwegian pl=Polish pt=Portuguse ro=Romanian ru=Russian
			si=Slovenian sk=Slovak sp=Spanish Latam SVP sq=Albanian sr=Serbian
			sv=Swedish tr=Turkish uk=Ukranian
		</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="2" />
		<xsd:enumeration value="bg" />
		<xsd:enumeration value="br" />
		<xsd:enumeration value="cs" />
		<xsd:enumeration value="da" />
		<xsd:enumeration value="de" />
		<xsd:enumeration value="el" />
		<xsd:enumeration value="en" />
		<xsd:enumeration value="ep" />
		<xsd:enumeration value="et" />
		<xsd:enumeration value="fi" />
		<xsd:enumeration value="fr" />
		<xsd:enumeration value="he" />
		<xsd:enumeration value="hr" />
		<xsd:enumeration value="hu" />
		<xsd:enumeration value="is" />
		<xsd:enumeration value="it" />
		<xsd:enumeration value="lt" />
		<xsd:enumeration value="lv" />
		<xsd:enumeration value="mk" />
		<xsd:enumeration value="nl" />
		<xsd:enumeration value="no" />
		<xsd:enumeration value="pl" />
		<xsd:enumeration value="pt" />
		<xsd:enumeration value="ro" />
		<xsd:enumeration value="ru" />
		<xsd:enumeration value="si" />
		<xsd:enumeration value="sk" />
		<xsd:enumeration value="sp" />
		<xsd:enumeration value="sq" />
		<xsd:enumeration value="sr" />
		<xsd:enumeration value="sv" />
		<xsd:enumeration value="tr" />
		<xsd:enumeration value="uk" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="InvoiceType">
	<xsd:annotation>
		<xsd:documentation>DHL Invoice Types CMI(Commercail Invoice), PFI (Proforma Invoice)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="3" />
		<xsd:enumeration value="CMI" />
		<xsd:enumeration value="PFI" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LicenseNumber">
	<xsd:annotation>
		<xsd:documentation>Export license number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="16" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LineNumber">
	<xsd:annotation>
		<xsd:documentation>The LineNumber element contains the serial number for particular item.It’s value should not be more than 200. It is a mandatory field</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:positiveInteger">
		<xsd:minInclusive value="1" />
		<xsd:maxInclusive value="999" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LocalProductCode">
	<xsd:annotation> 
		<xsd:documentation>The LocalProductCode Element is local product code for the shipment. It is the optional field in the Shipment Details segment</xsd:documentation> 
	</xsd:annotation> 
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="4" />
		<xsd:pattern value="([A-Z0-9])*" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Money">
	<xsd:annotation>
		<xsd:documentation>Monetary amount (with 3 decimal precision)</xsd:documentation>
	</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="18"/>
			<xsd:fractionDigits value="3"/>
		</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="ChargeValue_Money">
		<xsd:annotation>
		<xsd:documentation>Charge Value</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:minInclusive value="0.00"/>
			<xsd:maxInclusive value="9999999999.99"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DeclaredValue_Money">
		<xsd:annotation>
			<xsd:documentation>Declared Value amount</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:minInclusive value="0.000"/>
			<xsd:maxInclusive value="999999999999999.999"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:simpleType name="PackageType">
	<xsd:annotation>
		<xsd:documentation>Package Type (EE: DHL Express Envelope, OD:Other DHL Packaging, CP:Customer-provided, JB-Jumbo box, JJ-Junior jumbo Box, DF-DHL Flyer, YP-Your packaging)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="2" />
		<xsd:enumeration value="BD" />
		<xsd:enumeration value="BP" />
		<xsd:enumeration value="CP" />
		<xsd:enumeration value="DC" />
		<xsd:enumeration value="DF" />
		<xsd:enumeration value="DM" />
		<xsd:enumeration value="ED" />
		<xsd:enumeration value="EE" />
		<xsd:enumeration value="FR" />
		<xsd:enumeration value="JB" />
		<xsd:enumeration value="JD" />
		<xsd:enumeration value="JJ" />
		<xsd:enumeration value="JP" />
		<xsd:enumeration value="OD" />
		<xsd:enumeration value="PA" />
		<xsd:enumeration value="YP" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PersonName">
	<xsd:annotation>
		<xsd:documentation>Name</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
		<xsd:minLength value="2" />
		<xsd:whiteSpace value="collapse"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PhoneNumber">
	<xsd:annotation>
		<xsd:documentation>Phone Number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="25" />
		<xsd:whiteSpace value="collapse"/>
		<xsd:pattern value=".*[^\s].*" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PieceID">
	<xsd:annotation>
		<xsd:documentation>Piece ID</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
		<xsd:whiteSpace value="collapse"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Piece">
	<xsd:annotation>
		<xsd:documentation>Element encapsulating pieces information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="PieceID" type="PieceID" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PieceID element contains the piece number of each piece for this shipment. It is an optional field in the Piece segment.Its value should not be more than 35 characters</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PackageType" type="PackageType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PackageType element contains the unique ID of the piece for this shipment. It is an optional field in the Piece segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Weight" type="Piece_Weight">
			<xsd:annotation>
				<xsd:documentation>The Weight element represents the weight of the individual piece or of the shipment. It is an mandatory field in the Piece segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Width" type="xsd:positiveInteger">
			<xsd:annotation>
				<xsd:documentation>Required if height and depth are specified</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Height" type="xsd:positiveInteger">
			<xsd:annotation>
				<xsd:documentation>Required if width and depth are specified</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Depth" type="xsd:positiveInteger">
			<xsd:annotation>
				<xsd:documentation>Required if width and height are specified</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PieceContents" type="PieceContents" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The piece contents element represents the contents or description of the piece of the shipment. It is an optional field in the Piece segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PieceReference" type="Reference" minOccurs="0" maxOccurs="99">
			<xsd:annotation>
				<xsd:documentation>This element identifies the reference information. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
			<xsd:element name="AdditionalInformation" type="AdditionalInformation" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Additional Information. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="ParentPieceIdentificationNumber" type="PieceID" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Parent Shipment Piece ID. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="PieceIdentificationNumber" type="PieceID" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Piece ID if UswOwnPieceIdentificationNumber is Y. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="UseOwnPieceIdentificationNumber" default="N" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Customer able to provide PID if set to Y. The
						default is N</xsd:documentation>
				</xsd:annotation>
				<xsd:simpleType>
					<xsd:restriction base="xsd:string">
						<xsd:maxLength value="1"/>
						<xsd:enumeration value="Y"/>
						<xsd:enumeration value="N"/>
					</xsd:restriction>
				</xsd:simpleType>
			</xsd:element>
	</xsd:sequence>
</xsd:complexType>
	<xsd:complexType name="AdditionalInformation">
		<xsd:sequence>
			<xsd:element name="CustomerBarcodes" type="CustomerBarcodes" minOccurs="0" maxOccurs="2">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer Barcodes. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="CustomerAdditionalInformation" type="CustomerAdditionalInformation" minOccurs="0" maxOccurs="6">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer Additional Information with occurences of 6. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="CustomerDescription" type="CustomerDescription" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer Description. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CustomerAdditionalInformation">
		<xsd:sequence>
			<xsd:element name="Caption" type="Caption" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer Additional Information caption. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="Description" type="Description" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer Additional Informatrion description. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CustomerBarcodes">
		<xsd:sequence>
			<xsd:element name="BarcodeType" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer barcode type. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="BarcodeContent" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer barcode content. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="TextBelowBarcode" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element identifies the Customer barcode text below barcode. It is an optional field</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="Caption">
		<xsd:annotation>
			<xsd:documentation>Customer barcode Caption</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="20"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Description">
		<xsd:annotation>
			<xsd:documentation>Customer barcode Description</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="40"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomerDescription">
		<xsd:annotation>
			<xsd:documentation>Additional Information Customer Description</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="80"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:complexType name="Pieces">
	<xsd:annotation>
		<xsd:documentation>Element encapsulating pieces information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="Piece" type="Piece" maxOccurs="999">
			<xsd:annotation>
				<xsd:documentation>Element encapsulating pieces information</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ShipValResponsePiece">
	<xsd:annotation>
		<xsd:documentation>The ShipValResponsePiece element contains the piece details along with the License Plate details</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="PieceNumber" type="PieceNumber" minOccurs="1" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Contains the Piece Number</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Depth" type="xsd:decimal" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Depth of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Width" type="xsd:decimal" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Width of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Height" type="xsd:decimal" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Height of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Weight" type="Weight" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Weight of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PackageType" type="PackageType" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Package type of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DimWeight" type="Weight" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Dimensional Weight of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PieceContents" type="PieceContents" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Contents or description of the piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PieceReference" type="Reference" minOccurs="0" maxOccurs="99">
			<xsd:annotation>
				<xsd:documentation>Piece level reference</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DataIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Contains the starting letter of the piece data Identifier</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="LicensePlate" type="PieceID" minOccurs="1" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>The String representation of the License Plate Number</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="LicensePlateBarCode" type="BarCode" minOccurs="1" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Barcode representing the License Plate information</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="QRCode" type="Response_QRCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>QR Code for labelless shipment if RequestQRCode</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ShipValResponsePieces">
	<xsd:annotation>
		<xsd:documentation>Element encapsulating pieces information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="Piece" type="ShipValResponsePiece" minOccurs="0" maxOccurs="999">
			<xsd:annotation>
				<xsd:documentation>Piece</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="PieceNumber">
	<xsd:annotation>
		<xsd:documentation>Piece Number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:positiveInteger" />
</xsd:simpleType>
<xsd:simpleType name="PieceContents">
	<xsd:annotation>
		<xsd:documentation>Piece contents description</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Response_QRCode">
		<xsd:all>
			<xsd:element name="ImageFormat" type="QRCodeImageFormat">
			<xsd:annotation>
				<xsd:documentation>QR Code Image Format</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
			<xsd:element name="Image" type="xsd:base64Binary">
			<xsd:annotation>
				<xsd:documentation>QR Code Image in base64 format</xsd:documentation>
			</xsd:annotation>
			</xsd:element>
		</xsd:all>
</xsd:complexType>
<xsd:complexType name="Place">
	<xsd:annotation>
		<xsd:documentation>The place element contains the address from the shipment has to be picked. This element should be declared once in the request message</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ResidenceOrBusiness" type="ResidenceOrBusiness" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Location Type element contains the type of location from where the shipment is to be picked. The element must be declared once in the Place element. The acceptable values are B (Business), R (Residence) C (Business/Residence)</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CompanyName" type="CompanyNameValidator" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Company Name element contains the name of the company. The element must be declared unless the value specified in the element LocationType is not “R”</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine1 element contains the address line 1 to the pickup place. The AddressLine1 is mandatory.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine2 element contains the address line 2 to the pickup place. The AddressLine2 is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine3 element contains the address line 3 to the pickup place. The AddressLine3 is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the pick address. It must be declared in the Place element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The CountryCode element contains the Country/Region code of the pickup place. It must be of 2 letters. Please refer to the Reference Data (DHL Country/Region) for Country/Region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="StateCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DivisionCode element contains the division code of the pickup place. Refer to Reference Data (DHL Country/Region) for Country/Region that uses division</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="State" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division element contains the division name of the pickup place</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PostalCode element contains the postal code of the pickup place. Refer to Reference Data (DHL Country/Region) for Country/Region that uses postal code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PackageLocation" type="PackageLocation" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PackageLocation element contains the location to pick up the package</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains the suburb name to pick up the package</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="PackageLocation">
	<xsd:annotation>
		<xsd:documentation>PackageLocation</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="40" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="StateCode">
	<xsd:annotation>
		<xsd:documentation>Division (state) code.</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PostalCode">
	<xsd:annotation>
		<xsd:documentation>Full postal/zip code for address</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="12" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ProductCode">
	<xsd:annotation>
		<xsd:documentation>
			DHL product code
			D : Worldwide Express Non-dutiable (>0.5lb)
			P : Worldwide Express-Dutiable
		</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="4" />
		<xsd:enumeration value="D" />
		<xsd:enumeration value="P" />
		<xsd:enumeration value="N" />
		<xsd:enumeration value="G" />
		<xsd:enumeration value="M" />
		<xsd:enumeration value="L" />
		<xsd:enumeration value="F" />
		<xsd:enumeration value="C" />
		<xsd:enumeration value="I" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="QuantityUnit">
	<xsd:annotation>
		<xsd:documentation>
			UOM Description
			BOX Boxes
			2GM Centigram
			2M Centimeters
			2M3 Cubic Centimeters
			3M3 Cubic Feet
			M3 Cubic Meters
			DPR Dozen Pairs
			DOZ Dozen
			2NO Each
			PCS Pieces
			GM Grams
			GRS Gross
			KG Kilograms
			L Liters
			M Meters
			3GM Milligrams
			3L Milliliters
			X No Unit Required
			NO Number
			2KG Ounces
			PRS Pairs
			2L Gallons
			3KG Pounds
			CM2 Square Centimeters
			2M2 Square Feet
			3M2 Square Inches
			M2 Square Meters
			4M2 Square Yards
			3M Yards
		</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="8" />
		<xsd:enumeration value="BOX" />
		<xsd:enumeration value="2GM" />
		<xsd:enumeration value="2M" />
		<xsd:enumeration value="2M3" />
		<xsd:enumeration value="3M3" />
		<xsd:enumeration value="M3" />
		<xsd:enumeration value="DPR" />
		<xsd:enumeration value="DOZ" />
		<xsd:enumeration value="2NO" />
		<xsd:enumeration value="PCS" />
		<xsd:enumeration value="GM" />
		<xsd:enumeration value="GRS" />
		<xsd:enumeration value="KG" />
		<xsd:enumeration value="L" />
		<xsd:enumeration value="M" />
		<xsd:enumeration value="3GM" />
		<xsd:enumeration value="3L" />
		<xsd:enumeration value="X" />
		<xsd:enumeration value="NO" />
		<xsd:enumeration value="2KG" />
		<xsd:enumeration value="PRS" />
		<xsd:enumeration value="2L" />
		<xsd:enumeration value="3KG" />
		<xsd:enumeration value="CM2" />
		<xsd:enumeration value="2M2" />
		<xsd:enumeration value="3M2" />
		<xsd:enumeration value="M2" />
		<xsd:enumeration value="4M2" />
		<xsd:enumeration value="3M" />
		<xsd:enumeration value="CM" />
		<xsd:enumeration value="CONE" />
		<xsd:enumeration value="CT" />
		<xsd:enumeration value="EA" />
		<xsd:enumeration value="LBS" />
		<xsd:enumeration value="RILL" />
		<xsd:enumeration value="ROLL" />
		<xsd:enumeration value="SET" />
		<xsd:enumeration value="TU" />
		<xsd:enumeration value="YDS" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Quantity">
	<xsd:annotation>
		<xsd:documentation>Quantity</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:positiveInteger" />
</xsd:simpleType>
<xsd:simpleType name="ReferenceID">
	<xsd:annotation>
		<xsd:documentation>Shipper reference ID</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Reference">
	<xsd:annotation>
		<xsd:documentation>This element identifies the reference information. It is an optional field in the shipment validation request. Only the first reference will be taken currently</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ReferenceID" type="ReferenceID">
			<xsd:annotation>
				<xsd:documentation>The ReferenceID element contains the shipper reference ID. It is a mandatory field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ReferenceType" type="ReferenceType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ReferenceType element contains the shipment reference type. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="ReferenceType">
	<xsd:annotation>
		<xsd:documentation>Shipment reference type</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="2" />
		<xsd:maxLength value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ResidenceOrBusiness">
	<xsd:annotation>
		<xsd:documentation>Identifies if a location is a business, residence, or both (B:Business, R:Residence, C:Business Residence)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="B" />
		<xsd:enumeration value="R" />
		<xsd:enumeration value="C" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ScheduleB">
	<xsd:annotation>
		<xsd:documentation>Schedule B number</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="15" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="SEDDescription">
	<xsd:annotation>
		<xsd:documentation>Shippers Export declaration line item description</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="75" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="SEDNumberType">
	<xsd:annotation>
		<xsd:documentation>This element contains the SED number type. It is an optional element. This field is required if SED number is given. The valid values are one of: F (for FTSR), X (for XTN) or S (for SAS)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="F" />
		<xsd:enumeration value="X" />
		<xsd:enumeration value="S" />
		<xsd:enumeration value="I" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="InvoiceNumber">
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="SEDNumber">
	<xsd:annotation>
		<xsd:documentation>
			FTSR - Foreign Trade Statistics Regulations - This is usually used as FTSR 30.55 where 30.55 is a section of the regulation that applies to the commodity, e.g., no SED required
			XTN- External Transaction Number - Comprised of the shipper’s EIN or SSN and a customer reference number
			SAS - Shipper Authorization Symbol - This is not used, but is replaced by the XTN
		</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="FTSR" />
		<xsd:enumeration value="XTN" />
		<xsd:enumeration value="SAS" />
		<xsd:enumeration value="ITN" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ShipmentContents">
	<xsd:annotation>
		<xsd:documentation>Shipment contents description</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1"/>
		<xsd:maxLength value="90" />
		<xsd:whiteSpace value="collapse"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="ShipmentDetails">
	<xsd:annotation>
		<xsd:documentation>The ShipmentDetails element contains the details of the shipment. It must be declared once in the request message</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>		
		<xsd:element name="Pieces" type="Pieces">
			<xsd:annotation>
				<xsd:documentation>The Pieces element contains the pieces information of the shipment. It must be declared in the Shipment validation element. It could store maximum 999 pieces information</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="WeightUnit" type="WeightUnit">
			<xsd:annotation>
				<xsd:documentation>The Weight Unit element contains the Unit by which the shipment weight is measured. It must be declared in the Weight element of the Pickup. The valid value for this element is K (Kilograms) and L (Pounds). Please refer to Reference Data (DHL Country/Region) for valid weight unit</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="GlobalProductCode" type="GlobalProductCode">
			<xsd:annotation>
				<xsd:documentation>The GlobalProductCode element is global product code for the shipment. It is the mandatory field in the Shipment Details segment. The valid values are A to Z and 0 to 9. Please refer to the Reference Data and use appropriate Global codes enabled for the Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="LocalProductCode" type="LocalProductCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The LocalProductCode Element is the Country/Region billing product code for the shipment. It is the mandatory field in the Shipment Details segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Date" type="Date">
			<xsd:annotation>
				<xsd:documentation>The Date element contains the shipment date. Shipment date for when package(s) will be shipped (but usually current date).Value may range from today to ten days after. The date should be in YYYY-MM-DD format</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Contents" type="ShipmentContents">
			<xsd:annotation>
				<xsd:documentation>The Contents element contains the shipment description</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DimensionUnit" type="DimensionUnit" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DimensionUnit Element is the unit in which the dimensions are measured. It is an optional field. The valid value is C (Centremetres) and I (Inches). Please refer to Reference Data (DHL Country/Region) for valid dimension unit</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PackageType" type="PackageType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PackageType element contains the unique ID of the piece for this shipment. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="IsDutiable" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The IsDutiable element indicates whether shipment is dutiable or not</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CurrencyCode" type="CurrencyCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CurrencyCode element indentifies how the shipment charge is billed</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustData" type="CustData" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustData element consists of customer data that required to be printed on shipment level in GLS transport label CI template</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ShipmentCharges" type="ShipmentCharges" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Shipmentcharges element consists of shipment charges of the shipment details. This element is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ParentShipmentIdentificationNumber" type="AWBNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ParentShipmentIdentificationNumber element consists of Parent Shipment Identification Number of the shipment details. This element is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ParentShipmentGlobalProductCode" type="GlobalProductCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ParentShipmentGlobalProductCode element consists of Parent Shipment Global Product Code of the shipment details. This element is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ParentShipmentPackagesCount" type="ParentShipmentPackagesCount" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ParentShipmentPackagesCount element consists of Parent Shipment Number of Packages or Pieces of the shipment details. This element is optional</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="ShipmentCharges">
	<xsd:annotation>
		<xsd:documentation>ShipmentCharges</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:float">
		<xsd:minInclusive value="0.01" />
		<xsd:maxInclusive value="99999999.99" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="CustData">
	<xsd:annotation>
		<xsd:documentation>CustData</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="250" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DimensionUnit">
	<xsd:annotation>
		<xsd:documentation>Dimension Unit C (centimeter)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="C" />
		<xsd:enumeration value="I" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ShipmentPaymentType">
	<xsd:annotation>
		<xsd:documentation>Shipment payment type (S:Shipper)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="S" />
		<xsd:enumeration value="R" />
		<xsd:enumeration value="T" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="ParentShipmentPackagesCount">
		<xsd:restriction base="xsd:positiveInteger">
			<xsd:minInclusive value="1"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:complexType name="Shipment">
	<xsd:sequence>
		<xsd:element name="Weight" type="Weight" />
		<xsd:element name="WeightUnit" type="WeightUnit" />
		<xsd:element name="Pieces" type="Pieces" />
		<xsd:element name="DoorTo" type="DoorTo" />
		<xsd:element name="AirwarBillNumber" type="AWBNumber" minOccurs="0" />
		<xsd:element name="AccountType" type="AccountType" minOccurs="0" />
		<xsd:element name="ProductType" type="xsd:string" minOccurs="0" />
		<xsd:element name="GlobalProductType" type="xsd:string" minOccurs="0" />
		<xsd:element name="LocalProductType" type="xsd:string" minOccurs="0" />
		<xsd:element name="Commodity" type="Commodity" minOccurs="0" />
		<xsd:element name="DeclaredValue" type="Money" minOccurs="0" />
		<xsd:element name="DeclaredCurrency" type="CurrencyCode" minOccurs="0" />
		<xsd:element name="InsuredValue" type="Money" minOccurs="0" />
		<xsd:element name="InsuredCurrency" type="CurrencyCode" minOccurs="0" />
		<xsd:element name="DimensionalUnit" type="WeightUnit" minOccurs="0" />
		<xsd:element name="DimensionalWeight" type="Weight" minOccurs="0" />
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="ShipperID">
	<xsd:annotation>
		<xsd:documentation>Shipper's ID</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="30" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Shipper">
	<xsd:annotation>
		<xsd:documentation>Shipper element contains the details of the Shipper. This element should be declared once in the Shipment validation Request message</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ShipperID" type="ShipperID">
			<xsd:annotation>
				<xsd:documentation>Shipper's Account Number</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CompanyName" type="CompanyNameValidator">
			<xsd:annotation>
				<xsd:documentation>The Company Name element contains the name of the company. The element must be declared unless the value specified in the element ResidenceOrBusiness is not “R”</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SuiteDepartmentName" type="SuiteDepartmentName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SuiteDepartmentName element contains the suite or department name of the shipper</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegisteredAccount" type="AccountNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The RegisteredAccount element contains the registered account of the shipper</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine1 element contains the address of the shipper location. There are three elements for address.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine2 element contains the address of the shipper location. There are three elements for address.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine3 element contains the address of the shipper location. There are three elements for address.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the pickup address. It must be declared in the Shipper element for Country/Region that use city, please refer to the Reference Data (DHL Country/Region) for Country/Region location type</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division element contains the state or county name of the shipper location. Please refer to the Reference Data (DHL Country/Region) for Country/Region location type</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DivisionCode element contains the state or county code of the shipper location. Please refer to the Reference Data (DHL Country/Region) for Country/Region location type</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The PostalCode element contains the postal code of the pickup place. Please refer to the Reference Data (DHL Country/Region) for Country/Region location type</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OriginServiceAreaCode" type="OriginServiceAreaCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The OriginServiceAreaCode element contains the service area code of the pickup place. It is required for re-upload option of PLT shipment supporting documents image</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OriginFacilityCode" type="OriginFacilityCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The OriginFacilityCode element contains the facility area code of the pickup place. It is required for re-upload option of PLT shipment supporting documents image</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The CountryCode element contains the Country/Region code of the pickup place. It must be of 2 letters. Please refer to the Reference Data (DHL Country/Region) for Country/Region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryName" type="CountryName">
			<xsd:annotation>
				<xsd:documentation>The CountryName element contains Country/Region name of the shipper. Its value should be valid DHL Country/Region name. This element must be declared once in the Shipper element. Please refer to the Reference Data (DHL Country/Region) for Country/Region names</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Contact" type="Contact">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the information about the contact at the shipper’s location. It must be declared in the shipper element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains suburb name of the shipper. It is optional field </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetName" type="StreetName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s street name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BuildingName" type="BuildingName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s Building Name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetNumber" type="StreetNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s It is an optional field. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s contact information.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s Business Party Type Code. </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="OriginServiceAreaCode">
	<xsd:annotation>
		<xsd:documentation>OriginServiceAreaCode</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="OriginFacilityCode">
	<xsd:annotation>
		<xsd:documentation>OriginFacilityCode</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="SpecialService">
	<xsd:annotation>
		<xsd:documentation>The SpecialService Element provides various special services for the shipment. It is an optional field. Please refer to the Reference Data and use appropriate Service codes enabled for the Country/Region</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="SpecialServiceType" type="SpecialServiceType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SpecialServiceType element contains the code for special services provided for shipment. Please refer to the Reference Data and use appropriate Service codes enabled for the Country/Region</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SpecialServiceDesc" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Special Servce Description</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CommunicationAddress" type="CommunicationAddress" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CommunicationAddress element contains the communication line number :fax number or phone number. It is an optional element in the SpecialService Element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CommunicationType" type="CommunicationType" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CommunicationType element contains the communication line type :fax or phone. It is an optional element in the SpecialService Element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ChargeValue" type="ChargeValue_Money" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ChargeValue element contains the chargeable amount for the special service. It is an optional element in the SpecialService Element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CurrencyCode" type="CurrencyCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CurrencyCode element identifies how the shipment is billed</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="IsWaived" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element tells whether special service is waived or not. It is an optional element in the special service segment. The value is either Y (Yes) or N (No)</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PaymentMethods" type="PaymentMethods" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element indicates the Payment Method if it is COD service. It is an optional element in the special service segment. The value is either Y (Yes) or N (No)</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="SpecialServiceType">
	<xsd:annotation>
		<xsd:documentation>Special Service codes</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="3" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:complexType name="PaymentMethods">
		<xsd:annotation>
			<xsd:documentation>Payment methods</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="PaymentMethod" type="PaymentMethod"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="PaymentMethod">
		<xsd:annotation>
			<xsd:documentation>Payment method code</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="3"/>
			<xsd:maxLength value="3"/>
			<xsd:enumeration value="CSH"/>
			<xsd:enumeration value="CRC"/>
			<xsd:enumeration value="CHQ"/>
			<xsd:enumeration value="PYP"/>
			<xsd:enumeration value="MOB"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:simpleType name="ChargeValue">
	<xsd:annotation>
		<xsd:documentation>ChargeValue</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:float">
		<xsd:minInclusive value="0.00" />
		<xsd:maxInclusive value="9999999999.99" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="State">
	<xsd:annotation>
		<xsd:documentation>State</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ServiceAreaCode">
	<xsd:annotation>
		<xsd:documentation>DHL service area code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="FacilityCode">
	<xsd:annotation>
		<xsd:documentation>Destination Facility Code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="InboundSortCode">
	<xsd:annotation>
		<xsd:documentation>InBound Sort Code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="4" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="OutboundSortCode">
	<xsd:annotation>
		<xsd:documentation>OutBound Sort Code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="1" />
		<xsd:maxLength value="4" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Telex">
	<xsd:annotation>
		<xsd:documentation>Telex number and answer back code</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="25" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="TimeHM">
	<xsd:annotation>
		<xsd:documentation>Time in hours and minutes (HH:MM)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:time" />
</xsd:simpleType>
<xsd:complexType name="WeightSeg">
	<xsd:annotation>
		<xsd:documentation>The weight element contains the weight information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="Weight" type="Weight">
			<xsd:annotation>
				<xsd:documentation>The Weight element contains the weight of the pickup. It must be declared in the Weight element of Pickup</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="WeightUnit" type="WeightUnit">
			<xsd:annotation>
				<xsd:documentation>The Weight Unit element contains the Unit by which the shipment weight is measured. It must be declared in the Weight element of the Pickup. The valid value for this element is K (Kilograms) and L (pounds). Please refer to Reference Data - DHL Country/Region for valid weight unit</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="WeightUnit">
	<xsd:annotation>
		<xsd:documentation>Unit of weight measurement (K:KiloGram)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="0" />
		<xsd:maxLength value="1" />
		<xsd:enumeration value="K" />
		<xsd:enumeration value="L" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Weight">
	<xsd:annotation>
		<xsd:documentation>Weight of piece or shipment</xsd:documentation>
	</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:totalDigits value="15"/>
			<xsd:fractionDigits value="3"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="ExportLineItem_Weight">
		<xsd:annotation>
			<xsd:documentation>Weight of piece or shipment</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:minInclusive value="0.000"/>
			<xsd:maxInclusive value="999999999999.999"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Piece_Weight">
		<xsd:annotation>
			<xsd:documentation>Weight of piece or shipment</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:decimal">
			<xsd:minInclusive value="0.000"/>
			<xsd:maxInclusive value="99999999999.999"/>
		</xsd:restriction>
	</xsd:simpleType>
<xsd:simpleType name="YesNo">
	<xsd:annotation>
		<xsd:documentation>Boolean flag</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="Y" />
		<xsd:enumeration value="N" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Request">
	<xsd:annotation>
		<xsd:documentation>Generic request header</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ServiceHeader" type="ServiceHeader">
			<xsd:annotation>
				<xsd:documentation>The Service Header element contains the header information about the request message. This element must be declared only once in the Request element</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
		<xsd:element name="MetaData" type="MetaData" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Metadata element is used to identify the software name and software version used by customer or third party vendor. It is an optional field</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ServiceHeader">
	<xsd:annotation>
		<xsd:documentation>Standard routing header</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="MessageTime" type="xsd:dateTime">
			<xsd:annotation>
				<xsd:documentation>Time this message is sent</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="GMTOffset" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The GMToffset element is a simple type of element which gives the time difference between the service areas time and GMT</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="MessageReference" type="MessageReference">
			<xsd:annotation>
				<xsd:documentation>A string, peferably number, to uniquely identify individual messages. Minimum length must be 28 and maximum length is 32</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SiteID" type="SiteID">
			<xsd:annotation>
				<xsd:documentation>The site id element is used to identify the requestor of the message. Each partner/customer is provided with the site id and password. Each request message received is validated with this before proceeding forward</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Password" type="Password">
			<xsd:annotation>
				<xsd:documentation>The password element is used to identify the requestor of the message. Each partner/customer is provided with the site id and password. Each request message received is validated with this before proceeding forward</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="MetaData">
	<xsd:annotation>
		<xsd:documentation>MetaData header</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="SoftwareName">
			<xsd:simpleType>
				<xsd:annotation>
					<xsd:documentation>The Software Name element is used to identify the software name used by customer or third party vendor. It is mandatory field</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:string">
					<xsd:maxLength value="30" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="SoftwareVersion">
			<xsd:simpleType>
				<xsd:annotation>
					<xsd:documentation>The Software Version element is used to identify the software version used by customer or third party vendor. It is mandatory field</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:string">
					<xsd:maxLength value="10" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>			
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="SiteID">
	<xsd:annotation>
		<xsd:documentation>Site ID used for verifying the sender</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="6" />
		<xsd:maxLength value="20" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Password">
	<xsd:annotation>
		<xsd:documentation>Password used for verifying the sender</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="8" />
		<xsd:maxLength value="20" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="MessageReference">
	<xsd:annotation>
		<xsd:documentation>Reference to the requested Message</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="28" />
		<xsd:maxLength value="32" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PaymentType">
	<xsd:annotation>
		<xsd:documentation>payment type (S:Shipper,R:Recipient,T:Third Party)</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="S" />
		<xsd:enumeration value="R" />
		<xsd:enumeration value="T" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="QRCodeTemplate">
		<xsd:annotation>
			<xsd:documentation>QR Code template</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="“QR_1_00_LL_PNG_001"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="QRCodeImageFormat">
		<xsd:annotation>
			<xsd:documentation>QRCode Image Format</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="PNG"/>
		</xsd:restriction>
	</xsd:simpleType>

<xsd:complexType name="Response">
	<xsd:annotation>
		<xsd:documentation>Generic response header</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ServiceHeader" type="ResponseServiceHeader">
			<xsd:annotation>
				<xsd:documentation>The ServiceHeader element contains the header information about the request message. This element must be declared only once in the Request element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ResponseServiceHeader">
	<xsd:annotation>
		<xsd:documentation>Standard routing header</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="MessageTime" type="xsd:dateTime">
			<xsd:annotation>
				<xsd:documentation>Time this message is sent</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="MessageReference" type="MessageReference">
			<xsd:annotation>
				<xsd:documentation>A string, peferably number, to uniquely identify individual messages. Minimum length must be 28 and maximum length is 32</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SiteID" type="SiteID">
			<xsd:annotation>
				<xsd:documentation>The site id element is used to identify the requestor of the message. Each partner/customer is provided with the site id and password. Each request message received is validated with this before proceeding forward</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="Status">
	<xsd:annotation>
		<xsd:documentation>Status/Exception signal element</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ActionStatus" type="xsd:string" />
		<xsd:element name="Condition" type="Condition" minOccurs="0" maxOccurs="unbounded" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="Note">
	<xsd:annotation>
		<xsd:documentation>Note/Warning</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ActionNote" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Action note is returned by the eCom backend in response to the Pickup Request send to it. It is a mandatory field in the Note Segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Condition" type="Condition" minOccurs="0" maxOccurs="unbounded">
			<xsd:annotation>
				<xsd:documentation>The Condition note element is an optional field. It is a complex element which consists of ConditionCode and ConditionData</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="Condition">
	<xsd:annotation>
		<xsd:documentation>The Condition note element is an optional field. It is a complex element which consists of ConditionCode and ConditionData</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ConditionCode" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation>The condition code element contains the code for the condtion. It is a mandatory field in the Condtion segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ConditionData" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Condition Data element contains the data for the condition. It is an optional field in the Condition segment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="Customer">
	<xsd:annotation>
		<xsd:documentation>Customer</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="CustomerID" type="xsd:string" />
		<xsd:element name="Name" type="xsd:string" />
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="BarCode">
	<xsd:annotation>
		<xsd:documentation>BarCode</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:base64Binary" />
</xsd:simpleType>
<xsd:complexType name="BarCodes">
	<xsd:annotation>
		<xsd:documentation>Element containing BarCode data</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="BarCode" type="BarCode" minOccurs="0" maxOccurs="unbounded">
			<xsd:annotation>
				<xsd:documentation>Contains barcodes as Base64 encoded binary data</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="DestinationServiceArea">
	<xsd:annotation>
		<xsd:documentation>The DestinationServiceArea element contains the information of the shipment’s destination along with the facility code and the inbound sort code information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ServiceAreaCode" type="ServiceAreaCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Three letter service area code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Description" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Detailed description for the Area code such as city, state,country etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="FacilityCode" type="FacilityCode" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Destination Facility Code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="InboundSortCode" type="InboundSortCode" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>InBound Sort Code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="OriginServiceArea">
	<xsd:annotation>
		<xsd:documentation>The OriginServiceArea element contains the information of the shipment’s origin along with the outbound sort code info</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ServiceAreaCode" type="ServiceAreaCode"
			minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Three letter service area code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Description" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Detailed description for the Area code such as city, state,country etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="FacilityCode" type="FacilityCode" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Origin Facility Code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OutboundSortCode" type="OutboundSortCode" minOccurs="0" maxOccurs="1">
			<xsd:annotation>
				<xsd:documentation>OutBound Sort Code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ServiceArea">
	<xsd:sequence>
		<xsd:element name="ServiceAreaCode" type="ServiceAreaCode"
			minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>three letter service area code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Description" type="xsd:string"
			minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Detailed description for the Area code such as city, state,country etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ServiceEvent">
	<xsd:annotation>
		<xsd:documentation>Complex type to describe a service event. Eg Pickup, Delivery</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="EventCode">
			<xsd:annotation>
				<xsd:documentation>Two letter Code denoting a specific service event</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:length value="2" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="Description" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Description of the service event code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="LevelOfDetails">
	<xsd:annotation>
		<xsd:documentation>Checkpoint details selection flag</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="LAST_CHECK_POINT_ONLY" />
		<xsd:enumeration value="ALL_CHECK_POINTS" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="ShipmentDate">
	<xsd:sequence>
		<xsd:element name="ShipmentDateFrom" type="Date" />
		<xsd:element name="ShipmentDateTo" type="Date" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="AWBInfo">
	<xsd:sequence>
		<xsd:element name="AWBNumber" type="AWBNumber" />
		<xsd:element name="Status" type="Status" />
		<xsd:element name="ShipmentInfo" type="ShipmentInfo" minOccurs="0" />
		<xsd:element name="PieceInfo" type="PieceInfo" minOccurs="0" maxOccurs="unbounded" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ShipmentInfo">
	<xsd:sequence>
		<xsd:element name="OriginServiceArea" type="ServiceArea" />
		<xsd:element name="DestinationServiceArea" type="ServiceArea" />
		<xsd:element name="ShipperName" type="PersonName" />
		<xsd:element name="ShipperAccountNumber" type="AccountNumber" minOccurs="0" />
		<xsd:element name="ConsigneeName" type="PersonName" />
		<xsd:element name="ShipmentDate" type="xsd:dateTime" />
		<xsd:element name="Pieces" minOccurs="0" />
		<xsd:element name="Weight" type="xsd:string" minOccurs="0" />
		<xsd:element name="WeightUnit" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Weight Unit Details</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:enumeration value="L" />
					<xsd:enumeration value="K" />
					<xsd:enumeration value="G" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="EstDlvyDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:choice>
				<xsd:element name="ShipmentEvent" type="ShipmentEvent" maxOccurs="unbounded"/>
				<xsd:element name="ShipperReference" type="Reference"/>
			</xsd:choice>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ErrorResponse">
	<xsd:annotation>
		<xsd:documentation>Generic response header</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ServiceHeader" type="ServiceHeader" />
		<xsd:element name="Status" type="Status" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="ShipmentEvent">
	<xsd:annotation>
		<xsd:documentation>Describes the checkpoint information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="Date" type="xsd:date" />
		<xsd:element name="Time" type="xsd:time" />
		<xsd:element name="ServiceEvent" type="ServiceEvent" />
		<xsd:element name="Signatory" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Signatory</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string" />
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="ServiceArea" type="ServiceArea" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="PieceInfo">
	<xsd:sequence>
		<xsd:element name="PieceDetails" type="PieceDetails" minOccurs="1" maxOccurs="1" />
		<xsd:element name="PieceEvent" type="PieceEvent" maxOccurs="unbounded" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="PieceEvent">
	<xsd:annotation>
		<xsd:documentation>Describes the checkpoint information</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="Date" type="xsd:date" />
		<xsd:element name="Time" type="xsd:time" />
		<xsd:element name="ServiceEvent" type="ServiceEvent" />
		<xsd:element name="Signatory" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>Signatory</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string" />
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="ServiceArea" type="ServiceArea" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="PieceDetails">
	<xsd:sequence>
		<xsd:element name="PieceID" type="TrackingPieceID" minOccurs="0" />
		<xsd:element name="PackageType" type="PackageType" minOccurs="0" />
		<xsd:element name="Weight" type="Piece_Weight" minOccurs="0" />
		<xsd:element name="DimWeight" type="xsd:string" minOccurs="0" />
		<xsd:element name="Width" type="xsd:positiveInteger" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>required if height and depth are specified</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Height" type="xsd:positiveInteger" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>required if width and depth are specified</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Depth" type="xsd:positiveInteger" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>required if width and height are specified</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="TrackingPieces">
	<xsd:annotation>
		<xsd:documentation>Piece Info</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="PieceInfo" type="PieceInfo" minOccurs="1" maxOccurs="unbounded" />
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="TrackingPieceID">
	<xsd:annotation>
		<xsd:documentation>Piece ID</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="20" />
		<xsd:maxLength value="35" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Fault">
	<xsd:annotation>
		<xsd:documentation>Piece Fault</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="PieceFault" type="PieceFault" minOccurs="1" maxOccurs="unbounded" />
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="PieceFault">
	<xsd:sequence>
		<xsd:element name="PieceID" type="TrackingPieceID" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>The License Plate identifier.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ConditionCode" type="xsd:string" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Condition Code</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ConditionData" type="xsd:string" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>Condition Data</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="DocImages">
	<xsd:annotation>
		<xsd:documentation>DocImages</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="DocImage" type="DocImage" minOccurs="0" maxOccurs="unbounded">
			<xsd:annotation>
				<xsd:documentation>DocImages</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="DocImage">
	<xsd:annotation>
		<xsd:documentation>DocImage</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="Type" type="Type">
			<xsd:annotation>
				<xsd:documentation>The Type element indicates supporting document image type</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
		<xsd:element name="Image" type="Image">
			<xsd:annotation>
				<xsd:documentation>The Image element contains the supporting document image in base64</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
		<xsd:element name="ImageFormat" type="ImageFormat">
			<xsd:annotation>
				<xsd:documentation>The ImageFormat element indicates the supporting document image format</xsd:documentation>
			</xsd:annotation>				
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="Type">
	<xsd:annotation>
		<xsd:documentation>Image Type</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="3" />
		<xsd:enumeration value="HWB" />
		<xsd:enumeration value="INV" />
		<xsd:enumeration value="PNV" />
		<xsd:enumeration value="COO" />
		<xsd:enumeration value="NAF" />
		<xsd:enumeration value="CIN" />
		<xsd:enumeration value="DCL" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Image">
	<xsd:annotation>
		<xsd:documentation>Image</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:base64Binary" />
</xsd:simpleType>
<xsd:simpleType name="ImageFormat">
	<xsd:annotation>
		<xsd:documentation>Image Format</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="5" />
		<xsd:enumeration value="PDF" />
		<xsd:enumeration value="PNG" />
		<xsd:enumeration value="TIFF" />
		<xsd:enumeration value="GIF" />
		<xsd:enumeration value="JPEG" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LabelImageFormat">
	<xsd:annotation>
		<xsd:documentation>LabelImageFormat</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="3" />
		<xsd:maxLength value="4" />
		<xsd:enumeration value="PDF" />
		<xsd:enumeration value="ZPL2" />
		<xsd:enumeration value="EPL2" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PLTStatus">
	<xsd:annotation>
		<xsd:documentation>PLTStatus</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="1" />
		<xsd:enumeration value="A" />
		<xsd:enumeration value="D" />
		<xsd:enumeration value="S" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="QtdSInAdCur">
	<xsd:annotation>
		<xsd:documentation>QtdSInAdCur</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="CurrencyCode" type="CurrencyCode" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>The CurrencyCode element indicates the currency code of the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CurrencyRoleTypeCode" type="CurrencyRoleTypeCode" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>The CurrencyRoleTypeCode element indicates the role of the currency in billing details</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PackageCharge" type="PackageCharge" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>The PackageCharge element contains the package charge of the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ShippingCharge" type="ShippingCharge" minOccurs="1">
			<xsd:annotation>
				<xsd:documentation>The ShippingCharge element contains the total shipping charges of the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="CurrencyRoleTypeCode">
	<xsd:annotation>
		<xsd:documentation>CurrencyRoleTypeCode</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="5" />
		<xsd:enumeration value="BILLC" />
		<xsd:enumeration value="PULCL" />
		<xsd:enumeration value="INVCU" />
		<xsd:enumeration value="BASEC" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="PackageCharge">
	<xsd:annotation>
		<xsd:documentation>PackageCharge</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:decimal">
		<xsd:fractionDigits value="3" />
		<xsd:totalDigits value="18" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="ShippingCharge">
	<xsd:annotation>
		<xsd:documentation>ShippingCharge</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:decimal">
		<xsd:fractionDigits value="3" />
		<xsd:totalDigits value="18" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="LabelImage">
	<xsd:annotation>
		<xsd:documentation>LabelImage</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="OutputFormat" type="OutputFormat" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The OutputFormat element indicates the GLS’s supported label output format</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="OutputImage" type="OutputImage" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The OutputImage element contains the GLS’s label image of Transport Label and Archive Document for the required output format</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="MultiLabels" type="MultiLabels" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The OutputImage element contains the GLS’s label image of Transport Label, Archive Document, Custom Invoice and Shipment Receipt document for the required output format if there are more than one base64 image</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="MultiLabels">
	<xsd:annotation>
		<xsd:documentation>The OutputImage element contains the GLS’s label image of Transport Label, Archive Document, Custom Invoice and Shipment Receipt document for the required output format if there are more than one base64 image</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="MultiLabel" minOccurs="1" maxOccurs="99">
			<xsd:complexType>
				<xsd:annotation>
					<xsd:documentation>MultiLabel</xsd:documentation>
				</xsd:annotation>
				<xsd:sequence>
					<xsd:element name="DocName" type="xsd:string">
						<xsd:annotation>
							<xsd:documentation>DocName</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DocFormat" type="DocFormat">
						<xsd:annotation>
							<xsd:documentation>DocFormat</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
					<xsd:element name="DocImageVal" type="DocImageVal">
						<xsd:annotation>
							<xsd:documentation>DocImageVal</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:complexType>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="DocFormat">
	<xsd:annotation>
		<xsd:documentation>DocFormat</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:length value="3" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="DocImageVal">
	<xsd:annotation>
		<xsd:documentation>DocImage</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:base64Binary" />
</xsd:simpleType>
<xsd:simpleType name="OutputFormat">
	<xsd:annotation>
		<xsd:documentation>OutputFormat</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="PDF" />
		<xsd:enumeration value="PL2" />
		<xsd:enumeration value="ZPL2" />
		<xsd:enumeration value="JPG" />
		<xsd:enumeration value="PNG" />
		<xsd:enumeration value="EPL2" />
		<xsd:enumeration value="EPLN" />
		<xsd:enumeration value="ZPLN" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="OutputImage">
	<xsd:annotation>
		<xsd:documentation>OutputImage</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:base64Binary" />
</xsd:simpleType>
<xsd:complexType name="Notification">
	<xsd:annotation>
		<xsd:documentation>The Notification element contains the notification address and customized message to the designated recipient address for the shipment</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="EmailAddress" type="Message" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The EmailAddress element indicates the multiple email address of the recipient that will receive the email notification message in Message element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Message" type="Message" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Message element indicates the notification message that will be sent to the designated email address in EmailAddress element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="Message">
	<xsd:annotation>
		<xsd:documentation>Message</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="250" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="RegionCode">
	<xsd:annotation>
		<xsd:documentation>RegionCode</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:minLength value="2" />
		<xsd:maxLength value="2" />
		<xsd:enumeration value="AP" />
		<xsd:enumeration value="EU" />
		<xsd:enumeration value="AM" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="Label">
	<xsd:annotation>
		<xsd:documentation>Label</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="HideAccount" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The HideAccount element to to enable XML Services client to display the visibility of the Account number on the label</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="LabelTemplate" type="LabelTemplate" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The LabelTemplate element indicates GLS transport label templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ReceiptTemplate" type="ReceiptTemplate" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ReceiptTemplate element indicates shipment receipt template. Note: This is only applicable for Country/Region Russia (RU) usage</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ShipmentReceiptWithLabels" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ShipmentReceiptWithLabels element indicates whether the shipment receipt should be generated in a separate document. Note: This is only applicable for Country/Region Russia (RU) usage, 8X4_RU_A4_PDF and SHIP_RECPT_A4_RU_PDF template</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomsInvoiceTemplate" type="CustomsInvoiceTemplate" minOccurs="0">
		<xsd:annotation>
				<xsd:documentation>The CustomsInvoiceTemplate element indicates the Customs Invoice templated used.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Logo" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Logo element determines DHL logo to be printed in thermal label printout</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerLogo" type="CustomerLogo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerLogo element consists of customer’s company logo image in base64 and its image size format to be printed in Transport label</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Resolution" type="Resolution" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Resolution element indicates the DPI resolution required for label output on selected printer</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerBarcodeType" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerBarcodeType element consists of customer’s barcode type to be printed in Transport label using these 8X4_CustBarCode_PDF and 8X4_CustBarCode_thermal templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerBarcodeCode" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerBarcodeCode element consists of customer’s barcode code to be printed in Transport label using these 8X4_CustBarCode_PDF and 8X4_CustBarCode_thermal templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerBarcodeText" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerBarcodeText element consists of customer’s barcode text to be printed in Transport label using these 8X4_CustBarCode_PDF and 8X4_CustBarCode_thermal templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="QRCodeTemplate" type="QRCodeTemplate" minOccurs="0">
		<xsd:annotation>
				<xsd:documentation>The QRCodeTemplate element consists of QR Code Template if RequestQRCode option is selected.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="QRCodeImageFormat" type="QRCodeImageFormat" minOccurs="0">
		<xsd:annotation>
				<xsd:documentation>The QRCodeImageFormat element consists of QR Code Image Format if RequestQRCode option is selected.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="ReceiptTemplate">
	<xsd:annotation>
		<xsd:documentation>ReceiptTemplate</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="30" />
		<xsd:enumeration value="SHIP_RECPT_A4_RU_PDF" />
	</xsd:restriction>
</xsd:simpleType>
	<xsd:simpleType name="CustomsInvoiceTemplate">
		<xsd:annotation>
			<xsd:documentation>DHL Customs Invoice Template</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="COMMERCIAL_INVOICE_04"/>
			<xsd:enumeration value="COMMERCIAL_INVOICE_P_10"/>
			<xsd:enumeration value="COMMERCIAL_INVOICE_L_10"/>
		</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="NumberOfArchiveDoc">
	<xsd:annotation>
		<xsd:documentation>NumberOfArchiveDoc Value</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:positiveInteger">
		<xsd:maxInclusive value="2" />
		<xsd:enumeration value="1" />
		<xsd:enumeration value="2" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="CustomerLogo">
	<xsd:annotation>
		<xsd:documentation>CustomerLogo</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="LogoImage" type="LogoImage">
			<xsd:annotation>
				<xsd:documentation>The LogoImage element indicates customer’s logo image in base64 format</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="LogoImageFormat" type="LogoImageFormat">
			<xsd:annotation>
				<xsd:documentation>The LogoImageFormat element indicates customer’s logo image format</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="LogoImage">
	<xsd:annotation>
		<xsd:documentation>LogoImage</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:base64Binary">
		<xsd:maxLength value="1048576" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LogoImageFormat">
	<xsd:annotation>
		<xsd:documentation>LogoImage Format</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="PNG" />
		<xsd:enumeration value="GIF" />
		<xsd:enumeration value="JPEG" />
		<xsd:enumeration value="JPG" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LabelTemplate">
	<xsd:annotation>
		<xsd:documentation>LabelTemplate</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="8X4_A4_PDF" />
		<xsd:enumeration value="8X4_thermal" />
		<xsd:enumeration value="8X4_A4_TC_PDF" />
		<xsd:enumeration value="6X4_thermal" />
		<xsd:enumeration value="6X4_A4_PDF" />
		<xsd:enumeration value="8X4_CI_PDF" />
		<xsd:enumeration value="8X4_CI_thermal" />
		<xsd:enumeration value="8X4_RU_A4_PDF" />
		<xsd:enumeration value="6X4_PDF" />
		<xsd:enumeration value="8X4_PDF" />
		<xsd:enumeration value="8X4_CustBarCode_PDF" />
		<xsd:enumeration value="8X4_CustBarCode_thermal" />
		<xsd:enumeration value="8X4_CustBarCode_AdditionalInfo_PDF"/>
		<xsd:enumeration value="8X4_CustBarCode_AdditionalInfo_thermal"/>
		<xsd:enumeration value="8X4_LBBX_PDF"/>
		<xsd:enumeration value="8X4_LBBX_thermal"/>
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="Resolution">
	<xsd:annotation>
		<xsd:documentation>Resolution</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:positiveInteger">
		<xsd:minInclusive value="200" />
		<xsd:maxInclusive value="300" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="LabelRegText">
	<xsd:annotation>
		<xsd:documentation>Label Reg Text</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="150" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="DGs">
	<xsd:annotation>
		<xsd:documentation>Multiple Dangerous Goods Item</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="DG" type="DG" minOccurs="0" maxOccurs="unbounded">
			<xsd:annotation>
				<xsd:documentation>The DG element contains the details of each Danagerous Goods item that included in the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:complexType name="DG">
	<xsd:annotation>
		<xsd:documentation>Dangerous Goods</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="DG_ContentID" type="xsd:string">
			<xsd:annotation>
				<xsd:documentation>The DG_ContentID element contains the Dangerous Goods Content ID that included in the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DG_LabelDesc" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DG_LabelDesc element contains the Dangerous Goods Label Description that included in the shipment. It is recommended to leave it empty and it will be lookup via label rendering for the updated description</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DG_NetWeight" type="Weight" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DG_NetWeight element contains the Dangerous Goods Net Weight that included in the shipment. It is required for Dry Ice with Unit of Measurement details also</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DG_UOM" type="DG_UOM" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DG_UOM element contains the Dangerous Goods Unit of Measurement that included in the shipment. It is required for Dry Ice with Net Weight details also</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DG_UNCode" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The DG_UNCode element contains the Dangerous Goods with Excepted Quantities attributes using UN code that included in the shipment</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
<xsd:simpleType name="DG_UOM">
	<xsd:annotation>
		<xsd:documentation>DG_UOM</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:enumeration value="KG" />
		<xsd:enumeration value="LB" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="AddDeclText">
	<xsd:annotation>
		<xsd:documentation>Additional Declaration Text</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:string">
		<xsd:maxLength value="140" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:simpleType name="SignatureImage">
	<xsd:annotation>
		<xsd:documentation>SignatureImage</xsd:documentation>
	</xsd:annotation>
	<xsd:restriction base="xsd:base64Binary">
		<xsd:maxLength value="1048576" />
	</xsd:restriction>
</xsd:simpleType>
<xsd:complexType name="ShipmentDocument">
	<xsd:annotation>
		<xsd:documentation>Shipment Document</xsd:documentation>
	</xsd:annotation>
	<xsd:sequence>
		<xsd:element name="ShipmentDocumentReference" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains export declaration’s shipment document reference value.It is an optional field</xsd:documentation>
			</xsd:annotation>
			<xsd:simpleType>
				<xsd:restriction base="xsd:string">
					<xsd:minLength value="0" />
					<xsd:maxLength value="35" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
		<xsd:element name="DocumentTypeName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>This element contains export declaration’s shipment document type name value.It is an optional field</xsd:documentation>
			</xsd:annotation>	
			<xsd:simpleType>				
				<xsd:restriction base="xsd:string">
					<xsd:minLength value="0" />
					<xsd:maxLength value="3" />
				</xsd:restriction>
			</xsd:simpleType>
		</xsd:element>
	</xsd:sequence>
</xsd:complexType>
	<xsd:simpleType name="InvoiceInstructions">
		<xsd:annotation>
			<xsd:documentation>Shipment Instructions for Customs Invoice printing purposes in COMMERCIAL_INVOICE_04 template</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="300"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="CustomerDataTextEntries">
		<xsd:annotation>
			<xsd:documentation>Customer Data Texts that would like to be printed in COMMERCIAL_INVOICE_04 template</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="6">
			<xsd:element name="CustomerDataTextEntry" type="CustomerDataTextEntry" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Customer Data Texts that would like to be printed in COMMERCIAL_INVOICE_04 template</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CustomerDataTextEntry">
		<xsd:annotation>
				<xsd:documentation>Customer Data Texts that would like to be printed in COMMERCIAL_INVOICE_04 template</xsd:documentation>
		</xsd:annotation>
		<xsd:all>
			<xsd:element name="CustomerDataTextNumber" type="CustomerDataTextNumber">
				<xsd:annotation>
					<xsd:documentation>Customer Data Texts Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CustomerDataText" type="CustomerDataText">
				<xsd:annotation>
					<xsd:documentation>Customs Invoice - Customer Data Text in COMMERCIAL_INVOICE_04 template</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="CustomerDataTextNumber">
		<xsd:annotation>
			<xsd:documentation>Customer Data Texts Number</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="1"/>
			<xsd:whiteSpace value="collapse"/>
			<xsd:pattern value="([1-6])*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomerDataText">
		<xsd:annotation>
			<xsd:documentation>Customs Invoice - Customer Data Text in COMMERCIAL_INVOICE_04 template</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="45"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PlaceOfIncoterm">
		<xsd:annotation>
			<xsd:documentation>Declaration text</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="256"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="ShipmentPurpose">
		<xsd:annotation>
			<xsd:documentation>Indicates if the Trading Transaction Type, shipment was sent for Personal (Gift) or Commercial (Sale) reasons
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:whiteSpace value="collapse"/>
			<xsd:enumeration value="PERSONAL"/>
			<xsd:enumeration value="COMMERCIAL"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="DocumentFunction">
		<xsd:annotation>
			<xsd:documentation>Document function describes for what purpose was the document details captured and are planned to be used.
			</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:whiteSpace value="collapse"/>
			<xsd:enumeration value="IMPORT"/>
			<xsd:enumeration value="EXPORT"/>
			<xsd:enumeration value="BOTH"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="CustomsDocuments">
		<xsd:annotation>
			<xsd:documentation>Export Declaration's level Customs Document details.Document type required for the clearance of goods at Invoice level</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="50">
			<xsd:element name="CustomsDocument" type="CustomsDocument">
				<xsd:annotation>
					<xsd:documentation>Export Declaration's level Customs Document entry</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CustomsDocument">
		<xsd:all>
			<xsd:annotation>
				<xsd:documentation>Export Declaration's level Customs Document entry</xsd:documentation>
			</xsd:annotation>
			<xsd:element name="CustomsDocumentType" type="CustomsDocumentType">
				<xsd:annotation>
					<xsd:documentation>Export Declaration line's Customs Document Type (CHA:Power of Attorney,
					POA:Power of Attorney (Customer-based),PPY:Proof Of Payment,ROD:Receipt on Delivery,
					T2M:T2M Transport Accompanying Document,TAD:TAD Transport Accompanying Document T1,
					TCS:Transportation Charges Statement)</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="CustomsDocumentID" type="CustomsDocumentID">
				<xsd:annotation>
				<xsd:documentation>Export Declaration line's Customs Document ID</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="CustomsDocumentType">
		<xsd:annotation>
			<xsd:documentation>Export Declaration line's Customs Document Type (CHA:Power of Attorney,
			POA:Power of Attorney (Customer-based),PPY:Proof Of Payment,ROD:Receipt on Delivery,
			T2M:T2M Transport Accompanying Document,TAD:TAD Transport Accompanying Document T1,
			TCS:Transportation Charges Statement),ELP:Export Licenses and Permits</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="3"/>			
			<xsd:enumeration value="972"/>
			<xsd:enumeration value="AHC"/>
			<xsd:enumeration value="ATA"/>
			<xsd:enumeration value="ATR"/>
			<xsd:enumeration value="CHD"/>
			<xsd:enumeration value="CHP"/>
			<xsd:enumeration value="CIT"/>
			<xsd:enumeration value="COO"/>
			<xsd:enumeration value="DEX"/>
			<xsd:enumeration value="EU1"/>
			<xsd:enumeration value="EU2"/>
			<xsd:enumeration value="EUS"/>
			<xsd:enumeration value="FMA"/>
			<xsd:enumeration value="PHY"/>
			<xsd:enumeration value="VET"/>
			<xsd:enumeration value="VEX"/>
			<xsd:enumeration value="CRL"/>
			<xsd:enumeration value="CSD"/>
			<xsd:enumeration value="PPY"/>
			<xsd:enumeration value="CI2"/>
			<xsd:enumeration value="CIV"/>
			<xsd:enumeration value="DOV"/>
			<xsd:enumeration value="INV"/>
			<xsd:enumeration value="PFI"/>
			<xsd:enumeration value="ALC"/>
			<xsd:enumeration value="HLC"/>
			<xsd:enumeration value="JLC"/>
			<xsd:enumeration value="LIC"/>
			<xsd:enumeration value="LNP"/>
			<xsd:enumeration value="PLI"/>
			<xsd:enumeration value="DLI"/>
			<xsd:enumeration value="NID"/>
			<xsd:enumeration value="PAS"/>
			<xsd:enumeration value="CHA"/>
			<xsd:enumeration value="CPA"/>
			<xsd:enumeration value="POA"/>
			<xsd:enumeration value="BEX"/>
			<xsd:enumeration value="DGD"/>
			<xsd:enumeration value="IPA"/>
			<xsd:enumeration value="T2M"/>
			<xsd:enumeration value="TAD"/>
			<xsd:enumeration value="TCS"/>
			<xsd:enumeration value="ROD"/>
			<xsd:enumeration value="EXL"/>
			<xsd:enumeration value="HWB"/>
			<xsd:enumeration value="ELP"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CustomsDocumentID">
		<xsd:annotation>
			<xsd:documentation>Export Declaration line's Customs Document ID</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="InvoiceReferences">
		<xsd:annotation>
			<xsd:documentation>Customs Invoice References</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="100">
			<xsd:element name="InvoiceReference" type="InvoiceReference">
				<xsd:annotation>
					<xsd:documentation>Customs Invoice Reference entry</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="InvoiceReference">
	 	<xsd:annotation>
			<xsd:documentation>Customs Invoice Reference entry</xsd:documentation>
		</xsd:annotation>
		<xsd:all>
			<xsd:element name="InvoiceReferenceType" type="InvoiceReferenceType">
				<xsd:annotation>
					<xsd:documentation>Invoice Reference Type</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="InvoiceReferenceNumber" type="InvoiceReferenceNumber">
				<xsd:annotation>
					<xsd:documentation>Invoice Reference Number</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="InvoiceReferenceType">
		<xsd:annotation>
			<xsd:documentation>Invoice Reference Type (ACL: Parent Shipment ID for BBX, 
				CID: Customer Identifier, CN:Contract Number,CU:Consignor reference number, ITN: US Export declaration reference ID,
				UCN:Movement Reference number,MRN:Movement Reference number,OID: Order ID, PON: Purchase Order ID, 
				RMA: Return Materials Authorization (RMA) Number, AAM: AWB Ref #,
				ABT:Goods Declaration number, ACL: Parent shipment ID for bulk shipment identification,ADA: Buyer Reference number,AES:AES Post Clearance,
				AFD:1496 Item number, ANT:Consignee Reference number,BKN:Booking Number,BOL:Bill of Lading Number,CDN:Customs Declaration number,
				COD:Cash On Delivery, DSC:Weltpaket Reference, FF:Freight forwarder's reference number, FN:Freight bill number, FTR:Post Clearance Exemption US,
				HWB: Shipment Identifiers, IBC: Inbound center reference number, IPP:Insurance Policy Provider, LLR:Load list reference, MAB:Master Airwaybill Number,
				MWB: MAWB Reference number, NLR: ECCN - No License Required indicator (NLR), OBC: Outbound center reference number, PD: Vendor Reference Number,
				PRN: Pickup request number, RMA: RMA Number, RTL: Return Leg waybill number, SID: Shipment ID 15 Digit CODA, SS:Seller Reference number,
				SWN:Original Waybill number (Return))
			</xsd:documentation>
		</xsd:annotation>			
		<xsd:restriction base="xsd:string">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="3"/>
				<xsd:enumeration value="ACL"/>
				<xsd:enumeration value="CID"/>
				<xsd:enumeration value="CN"/>
				<xsd:enumeration value="CU"/>
				<xsd:enumeration value="ITN"/>
				<xsd:enumeration value="UCN"/>
				<xsd:enumeration value="MRN"/>
				<xsd:enumeration value="OID"/>
				<xsd:enumeration value="PON"/>
				<xsd:enumeration value="RMA"/>
				<xsd:enumeration value="AAM"/>
				<xsd:enumeration value="ABT"/>
				<xsd:enumeration value="ADA"/>
				<xsd:enumeration value="AES"/>
				<xsd:enumeration value="AFD"/>
				<xsd:enumeration value="ANT"/>
				<xsd:enumeration value="BKN"/>
				<xsd:enumeration value="BOL"/>
				<xsd:enumeration value="CDN"/>
				<xsd:enumeration value="COD"/>
				<xsd:enumeration value="DSC"/>
				<xsd:enumeration value="FF"/>
				<xsd:enumeration value="FN"/>
				<xsd:enumeration value="FTR"/>
				<xsd:enumeration value="HWB"/>
				<xsd:enumeration value="IBC"/>
				<xsd:enumeration value="IPP"/>
				<xsd:enumeration value="LLR"/>
				<xsd:enumeration value="MAB"/>
				<xsd:enumeration value="MWB"/>
				<xsd:enumeration value="NLR"/>
				<xsd:enumeration value="OBC"/>
				<xsd:enumeration value="PD"/>
				<xsd:enumeration value="PRN"/>
				<xsd:enumeration value="RTL"/>
				<xsd:enumeration value="SID"/>
				<xsd:enumeration value="SS"/>
				<xsd:enumeration value="SWN"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="InvoiceReferenceNumber">
		<xsd:annotation>
			<xsd:documentation>Invoice Reference Number</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:maxLength value="35"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="Importer">
		<xsd:sequence>
		<xsd:element name="CompanyName" type="CompanyNameValidator">
			<xsd:annotation>
				<xsd:documentation>The CompanyName element contains the name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SuiteDepartmentName" type="SuiteDepartmentName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SuiteDepartmentName element contains the suite or department name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 1 element contains the address line 1 to the Importer. It is mandatory field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 2 element contains the address line 2 to the Importer.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 3 element contains the address line 3 to the Importer.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the Importer address. It must be declared in the Importer element for Country/Region that used city</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division name of the Importer location, for instance State, County, Prefecture, etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The division code of the Importer location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Postal Code element contains the postal code of the Importer location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The Country Code element contains the Country/Region code of the Importer location. It must be 2 letters. Please refer to the Reference Data (DHL Country/Region) for country/region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryName" type="CountryName">
			<xsd:annotation>
				<xsd:documentation>The CountryName element contains Country/Region name of the Importer. Its value should be a valid DHL Country/Region name. This element must be declared once in the Importer element. Please refer to the Reference Data (DHL Country/Region) for a list of valid Country/Region names</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Contact" type="Contact">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Importer's contact information. It must be declared in the Importer element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains Importer’s suburb name. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetName" type="StreetName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Importer’s street name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BuildingName" type="BuildingName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Importer’s Building Name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetNumber" type="StreetNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Importer’s It is an optional field. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Importer’s contact information.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Importer’s Business Party Type Code. </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="RegistrationNumbers">
		<xsd:sequence>
			<xsd:element name="RegistrationNumber" type="RegistrationNumber" maxOccurs="10"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="RegistrationNumber">
		<xsd:all>
			<xsd:element name="Number" type="Number"/>
			<xsd:element name="NumberTypeCode" type="NumberTypeCode"/>
			<xsd:element name="NumberIssuerCountryCode" type="NumberIssuerCountryCode"/>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="Number">
			<xsd:annotation>
				<xsd:documentation>registration number</xsd:documentation>
			</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="35"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="NumberTypeCode">
		<xsd:annotation>
			<xsd:documentation>Registration Number type code: SDT:IOSS (Import One-Stop-Shop),
			SDT:LVG	(Overseas Registered Supplier),SDT:VOEC (VAT on E-Commerce),
			VAT:VAT/GST (VAT Registration),FTZ:FTZ (Free Trade Zone ID),DAN:DAN (Deferment Account Duties Only),
			TAN:TAN	(Deferment Account Tax Only),DTF:DTF (Deferment Account Duties, Taxes and Fees Only), RGP:REX (EU Registered Exporters ID),
			DLI:Driver's License,NID:National Identity Card,PAS:Passport, MID:Manufacturer ID</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="1"/>
			<xsd:maxLength value="3"/>
			<xsd:whiteSpace value="collapse"/>
				<xsd:enumeration value="SDT"/>
				<xsd:enumeration value="VAT"/>
				<xsd:enumeration value="FTZ"/>
				<xsd:enumeration value="DAN"/>
				<xsd:enumeration value="TAN"/>
				<xsd:enumeration value="DTF"/>
				<xsd:enumeration value="CNP"/>
				<xsd:enumeration value="DUN"/>
				<xsd:enumeration value="EIN"/>
				<xsd:enumeration value="EOR"/>
				<xsd:enumeration value="SSN"/>
				<xsd:enumeration value="FED"/>
				<xsd:enumeration value="STA"/>
				<xsd:enumeration value="RGP"/>
				<xsd:enumeration value="DLI"/>
				<xsd:enumeration value="NID"/>
				<xsd:enumeration value="PAS"/>
				<xsd:enumeration value="MID"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="NumberIssuerCountryCode">
			<xsd:annotation>
				<xsd:documentation>Registration Number Issuing Country Code</xsd:documentation>
			</xsd:annotation>
		<xsd:restriction base="xsd:token">
			<xsd:minLength value="2"/>
			<xsd:maxLength value="2"/>
			<xsd:whiteSpace value="collapse"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="Exporter">
		<xsd:sequence>
		<xsd:element name="CompanyName" type="CompanyNameValidator">
			<xsd:annotation>
				<xsd:documentation>The CompanyName element contains the name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SuiteDepartmentName" type="SuiteDepartmentName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SuiteDepartmentName element contains the suite or department name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 1 element contains the address line 1 to the Exporter. It is mandatory field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 2 element contains the address line 2 to the Exporter.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 3 element contains the address line 3 to the Exporter.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the Exporter address. It must be declared in the Exporter element for Country/Region that used city</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division name of the Exporter location, for instance State, County, Prefecture, etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The division code of the Exporter location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Postal Code element contains the postal code of the Exporter location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The Country Code element contains the Country/Region code of the Exporter location. It must be 2 letters. Please refer to the Reference Data (DHL Country/Region) for country/region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryName" type="CountryName">
			<xsd:annotation>
				<xsd:documentation>The CountryName element contains Country/Region name of the Exporter. Its value should be a valid DHL Country/Region name. This element must be declared once in the Exporter element. Please refer to the Reference Data (DHL Country/Region) for a list of valid Country/Region names</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Contact" type="Contact">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter's contact information. It must be declared in the Exporter element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains Exporter’s suburb name. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetName" type="StreetName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s street name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BuildingName" type="BuildingName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s Building Name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetNumber" type="StreetNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s It is an optional field. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s contact information.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Exporter’s Business Party Type Code. </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="Seller">
		<xsd:sequence>
		<xsd:element name="CompanyName" type="CompanyNameValidator">
			<xsd:annotation>
				<xsd:documentation>The CompanyName element contains the name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SuiteDepartmentName" type="SuiteDepartmentName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SuiteDepartmentName element contains the suite or department name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 1 element contains the address line 1 to the Seller. It is mandatory field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 2 element contains the address line 2 to the Seller.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 3 element contains the address line 3 to the Seller.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the Seller address. It must be declared in the Exporter element for Country/Region that used city</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division name of the Seller location, for instance State, County, Prefecture, etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The division code of the Seller location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Postal Code element contains the postal code of the Seller location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The Country Code element contains the Country/Region code of the Seller location. It must be 2 letters. Please refer to the Reference Data (DHL Country/Region) for country/region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryName" type="CountryName">
			<xsd:annotation>
				<xsd:documentation>The CountryName element contains Country/Region name of the Seller. Its value should be a valid DHL Country/Region name. This element must be declared once in the Exporter element. Please refer to the Reference Data (DHL Country/Region) for a list of valid Country/Region names</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Contact" type="Contact">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Seller's contact information. It must be declared in the Exporter element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains Seller's suburb name. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetName" type="StreetName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Seller’s street name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BuildingName" type="BuildingName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Seller’s Building Name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetNumber" type="StreetNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Seller’s It is an optional field. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Seller’s contact information.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Seller’s Business Party Type Code. </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="Payer">
		<xsd:sequence>
		<xsd:element name="CompanyName" type="CompanyNameValidator">
			<xsd:annotation>
				<xsd:documentation>The CompanyName element contains the name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="SuiteDepartmentName" type="SuiteDepartmentName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The SuiteDepartmentName element contains the suite or department name of the company</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine1" type="AddressLine">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 1 element contains the address line 1 to the Payer. It is mandatory field.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine2" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 2 element contains the address line 2 to the Payer.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="AddressLine3" type="AddressLine" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The AddressLine 3 element contains the address line 3 to the Payer.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="City" type="City">
			<xsd:annotation>
				<xsd:documentation>The City element contains the City of the Payer address. It must be declared in the Exporter element for Country/Region that used city</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Division" type="Division" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Division name of the Payer location, for instance State, County, Prefecture, etc</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="DivisionCode" type="DivisionCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The division code of the Payer location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="PostalCode" type="PostalCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Postal Code element contains the postal code of the Payer location</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryCode" type="CountryCode">
			<xsd:annotation>
				<xsd:documentation>The Country Code element contains the Country/Region code of the Payer location. It must be 2 letters. Please refer to the Reference Data (DHL Country/Region) for country/region codes</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CountryName" type="CountryName">
			<xsd:annotation>
				<xsd:documentation>The CountryName element contains Country/Region name of the Payer. Its value should be a valid DHL Country/Region name. This element must be declared once in the Exporter element. Please refer to the Reference Data (DHL Country/Region) for a list of valid Country/Region names</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Contact" type="Contact">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Payer's contact information. It must be declared in the Exporter element</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Suburb" type="Suburb" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The suburb element contains Payer’s suburb name. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetName" type="StreetName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Payer’s street name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BuildingName" type="BuildingName" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Payer’s Building Name.It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="StreetNumber" type="StreetNumber" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Payer’s It is an optional field. It is an optional field</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="RegistrationNumbers" type="RegistrationNumbers" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Payer’s contact information.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="BusinessPartyTypeCode" type="BusinessPartyTypeCode" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Contact element contains the Payer’s Business Party Type Code. </xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="Response_Label">
		<xsd:annotation>
			<xsd:documentation>Label</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
		<xsd:element name="HideAccount" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The HideAccount element to to enable XML Services client to display the visibility of the Account number on the label</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="LabelTemplate" type="LabelTemplate" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The LabelTemplate element indicates GLS transport label templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ReceiptTemplate" type="ReceiptTemplate" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ReceiptTemplate element indicates shipment receipt template. Note: This is only applicable for Country/Region Russia (RU) usage</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="ShipmentReceiptWithLabels" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The ShipmentReceiptWithLabels element indicates whether the shipment receipt should be generated in a separate document. Note: This is only applicable for Country/Region Russia (RU) usage, 8X4_RU_A4_PDF and SHIP_RECPT_A4_RU_PDF template</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomsInvoiceTemplate" type="CustomsInvoiceTemplate" minOccurs="0">
		<xsd:annotation>
				<xsd:documentation>The CustomsInvoiceTemplate element indicates the Customs Invoice templated used.</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Logo" type="YesNo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Logo element determines DHL logo to be printed in thermal label printout</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerLogo" type="CustomerLogo" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerLogo element consists of customer’s company logo image in base64 and its image size format to be printed in Transport label</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="Resolution" type="Resolution" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The Resolution element indicates the DPI resolution required for label output on selected printer</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerBarcodeType" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerBarcodeType element consists of customer’s barcode type to be printed in Transport label using these 8X4_CustBarCode_PDF and 8X4_CustBarCode_thermal templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerBarcodeCode" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerBarcodeCode element consists of customer’s barcode code to be printed in Transport label using these 8X4_CustBarCode_PDF and 8X4_CustBarCode_thermal templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		<xsd:element name="CustomerBarcodeText" type="xsd:string" minOccurs="0">
			<xsd:annotation>
				<xsd:documentation>The CustomerBarcodeText element consists of customer’s barcode text to be printed in Transport label using these 8X4_CustBarCode_PDF and 8X4_CustBarCode_thermal templates</xsd:documentation>
			</xsd:annotation>
		</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>