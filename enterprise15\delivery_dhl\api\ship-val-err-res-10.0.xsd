<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.dhl.com"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.dhl.com"
	xmlns:dhl="http://www.dhl.com/datatypes" elementFormDefault="unqualified">
	<xsd:import namespace="http://www.dhl.com/datatypes"
		schemaLocation="datatypes.xsd" />

	<xsd:element name="ShipmentValidateErrorResponse">
		<xsd:annotation>
			<xsd:documentation>Shipment Validation error response root element
			</xsd:documentation>
		</xsd:annotation>
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="Response" type="dhl:ErrorResponse" />
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>