<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- These credentials work only if the shipper is located in the US -->
        <record id="product_product_delivery_dhl_us" model="product.product">
            <field name="name">DHL US</field>
            <field name="default_code">Delivery_003</field>
            <field name="type">service</field>
            <field name="categ_id" ref="delivery.product_category_deliveries"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="list_price">0.0</field>
            <field name="invoice_policy">order</field>
        </record>
        <record id="delivery_carrier_dhl_dom_us" model="delivery.carrier">
            <field name="name">DHL US</field>
            <field name="product_id" ref="delivery_dhl.product_product_delivery_dhl_us"/>
            <field name="delivery_type">dhl</field>
            <field name="dhl_product_code">N</field>
            <field name="dhl_SiteID">v62_X4e7G4Ww0y</field>
            <field name="dhl_password">7UvboGP0tD</field>
            <field name="dhl_account_number">*********</field>
            <field name="dhl_default_package_type_id" ref="dhl_packaging_BOX"/>
        </record>

        <record id="product_product_delivery_dhl_intl_us" model="product.product">
            <field name="name">DHL US -&gt; International</field>
            <field name="default_code">Delivery_004</field>
            <field name="type">service</field>
            <field name="categ_id" ref="delivery.product_category_deliveries"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="list_price">0.0</field>
            <field name="invoice_policy">order</field>
        </record>
        <record id="delivery_carrier_dhl_intl_us" model="delivery.carrier">
            <field name="name">DHL US -&gt; International</field>
            <field name="product_id" ref="delivery_dhl.product_product_delivery_dhl_intl_us"/>
            <field name="delivery_type">dhl</field>
            <field name="dhl_product_code">D</field>
            <field name="dhl_SiteID">v62_X4e7G4Ww0y</field>
            <field name="dhl_password">7UvboGP0tD</field>
            <field name="dhl_account_number">*********</field>
            <field name="dhl_default_package_type_id" ref="dhl_packaging_BOX"/>
        </record>


        <!-- These credentials work only if the shipper is located in the EU -->
        <record id="product_product_delivery_dhl_eu" model="product.product">
            <field name="name">DHL EU</field>
            <field name="default_code">Delivery_003</field>
            <field name="type">service</field>
            <field name="categ_id" ref="delivery.product_category_deliveries"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="list_price">0.0</field>
            <field name="invoice_policy">order</field>
        </record>
        <record id="delivery_carrier_dhl_dom" model="delivery.carrier">
            <field name="name">DHL EU</field>
            <field name="product_id" ref="delivery_dhl.product_product_delivery_dhl_eu"/>
            <field name="delivery_type">dhl</field>
            <field name="dhl_product_code">N</field>
            <field name="dhl_SiteID">v62_X4e7G4Ww0y</field>
            <field name="dhl_password">7UvboGP0tD</field>
            <field name="dhl_account_number">*********</field>
            <field name="dhl_default_package_type_id" ref="dhl_packaging_BOX"/>
        </record>

        <record id="product_product_delivery_dhl_intl" model="product.product">
            <field name="name">DHL EU -&gt; International</field>
            <field name="default_code">Delivery_004</field>
            <field name="type">service</field>
            <field name="categ_id" ref="delivery.product_category_deliveries"/>
            <field name="sale_ok" eval="False"/>
            <field name="purchase_ok" eval="False"/>
            <field name="list_price">0.0</field>
            <field name="invoice_policy">order</field>
        </record>
        <record id="delivery_carrier_dhl_intl" model="delivery.carrier">
            <field name="name">DHL EU -&gt; International</field>
            <field name="product_id" ref="delivery_dhl.product_product_delivery_dhl_intl"/>
            <field name="delivery_type">dhl</field>
            <field name="dhl_product_code">D</field>
            <field name="dhl_SiteID">v62_X4e7G4Ww0y</field>
            <field name="dhl_password">7UvboGP0tD</field>
            <field name="dhl_account_number">*********</field>
            <field name="dhl_default_package_type_id" ref="dhl_packaging_BOX"/>
        </record>

    </data>
</odoo>
