# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_dhl
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-19 12:58+0000\n"
"PO-Revision-Date: 2016-09-19 12:58+0000\n"
"Language-Team: Armenian (https://www.transifex.com/odoo/teams/41243/hy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "6X4_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "6X4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "6X4_thermal"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_A4_TC_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_CI_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_CI_thermal"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_RU_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_thermal"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before paying (cross-"
"sell strategy, e.g. for computers: mouse, keyboard, etc.). An algorithm "
"figures out a list of accessories based on all the products added to cart."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_accessory_product_ids
msgid "Accessory Products"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_availability
msgid "Adds an availability status on the web product page."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_alternative_product_ids
msgid "Alternative Products"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_region_code:0
msgid "America"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_region_code:0
msgid "Asia Pacific"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_availability
msgid "Availability"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_availability_warning
msgid "Availability Warning"
msgstr ""

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_delivery_carrier
msgid "Carrier"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_public_categ_ids
msgid ""
"Categories can be published on the Shop page (online catalog grid) to help "
"customers find all the items within a category. To publish them, go to the "
"Shop page, hit Customize and turn *Product Categories* on. A product can "
"belong to several categories."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_dimension_unit:0
msgid "Centimeters"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "D - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_account_number
msgid "DHL Account Number"
msgstr ""

#. module: delivery_dhl
#: model:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_default_packaging_id
msgid "DHL Default Packaging Type"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_password
msgid "DHL Password"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:373
#, python-format
msgid "DHL Site ID is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_SiteID
msgid "DHL SiteID"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_dom
#: model:product.product,name:delivery_dhl.delivery_carrier_dhl_dom_product_product
msgid "DHL USA"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_intl
#: model:product.product,name:delivery_dhl.delivery_carrier_dhl_intl_product_product
msgid "DHL USA -> International"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:377
#, python-format
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:375
#, python-format
msgid "DHL password is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_quote_description
msgid "Description for the quote"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_description
msgid "Description for the website"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_dutiable
msgid "Dutiable Material"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_image_format:0
msgid "EPL2"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_region_code:0
msgid "Europe"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_product_image_ids
msgid "Images"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_dimension_unit:0
msgid "Inches"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "K - Express 9:00"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_weight_unit:0
msgid "Kilograms"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_label_image_format
msgid "Label Image Format"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_label_template
msgid "Label Template"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "N - Domestic Express"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:178
#, python-format
msgid "No service available for the selected product"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:81
#, python-format
msgid "No shipping available for the selected DHL product"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_image_format:0
msgid "PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_package_dimension_unit
msgid "Package Dimension Unit"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_package_weight_unit
msgid "Package Weight Unit"
msgstr ""

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_product_packaging
msgid "Packaging"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:395
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_weight_unit:0
msgid "Pounds"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_product_code
msgid "Product"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_rating_ids
msgid "Rating"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_rating_count
msgid "Rating count"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_rating_last_value
msgid "Rating value: 0=Unhappy, 10=Happy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_region_code
msgid "Region"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:84
#, python-format
msgid "Shipment created into DHL <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_size_x
msgid "Size X"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_size_y
msgid "Size Y"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_style_ids
msgid "Styles"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_alternative_product_ids
msgid ""
"Suggest more expensive alternatives to your customers (upsell strategy). "
"Those products show up on the product page."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "T - Express 12:00"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:383
#, python-format
msgid ""
"The address of the custommer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:391
#, python-format
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:397
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_published
msgid "Visible in Website"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_message_ids
msgid "Website Comments"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_public_categ_ids
msgid "Website Product Category"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_sequence
msgid "Website Sequence"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_url
msgid "Website URL"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_meta_description
msgid "Website meta description"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_meta_title
msgid "Website meta title"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_price
msgid "Website price"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_website_public_price
msgid "Website public price"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:102
#, python-format
msgid "You can't cancel DHL shipping without pickup date."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_image_format:0
msgid "ZPL2"
msgstr ""
