# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_dhl
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Anatolij, 2022
# <PERSON>lvi<PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__0
msgid "0 - Logistics Services"
msgstr "0 - Logistikos paslaugos"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__1
msgid "1 - Domestic Express 12:00"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__2
msgid "2 - B2C"
msgstr "2 - B2C"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__3
msgid "3 - B2C"
msgstr "3 - B2C"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__4
msgid "4 - Jetline"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__5
msgid "5 - Sprintline"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__6
msgid "6 - Secureline"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_a4_pdf
msgid "6X4_A4_PDF"
msgstr "6X4_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_pdf
msgid "6X4_PDF"
msgstr "6X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_thermal
msgid "6X4_thermal"
msgstr "6X4_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__7
msgid "7 - Express Easy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__8
msgid "8 - Express Easy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_pdf
msgid "8X4_A4_PDF"
msgstr "8X4_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_tc_pdf
msgid "8X4_A4_TC_PDF"
msgstr "8X4_A4_TC_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_pdf
msgid "8X4_CI_PDF"
msgstr "8X4_CI_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_thermal
msgid "8X4_CI_thermal"
msgstr "8X4_CI_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_pdf
msgid "8X4_PDF"
msgstr "8X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ru_a4_pdf
msgid "8X4_RU_A4_PDF"
msgstr "8X4_RU_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_thermal
msgid "8X4_thermal"
msgstr "8X4_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__9
msgid "9 - Europack"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__a
msgid "A - Auto Reversals"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__am
msgid "America"
msgstr "Amerika"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__ap
msgid "Asia Pacific"
msgstr "Azija ir Ramusis vandenynas"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__b
msgid "B - Break Bulk Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__c
msgid "C - Medical Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Kurjeris"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__c
msgid "Centimeters"
msgstr "Centimetrai"

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr "Pažymėkite, jeigu pakuotė yra apmuitinama."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__d
msgid "D - Express Worldwide"
msgstr "D - Pasaulinis ekpresas"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__delivery_type__dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__stock_package_type__package_carrier_type__dhl
msgid "DHL"
msgstr "DHL"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_account_number
msgid "DHL Account Number"
msgstr "DHL paskyros numeris"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr "DHL nustatymai"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_dom
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_eu
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_eu_product_template
msgid "DHL EU"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_intl
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_intl_product_template
msgid "DHL EU -> International"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_default_package_type_id
msgid "DHL Package Type"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_password
msgid "DHL Password"
msgstr "DHL slaptažodis"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_product_code
msgid "DHL Product"
msgstr ""

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_stock
msgid "DHL Shipping Methods"
msgstr "DHL Siuntimo metodai"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "DHL Site ID is missing, please modify your delivery method settings."
msgstr "Trūksta DHL Site ID, pakeiskite pristatymo metodo nustatymus."

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_SiteID
msgid "DHL SiteID"
msgstr "DHL SiteID"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_dom_us
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_us
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_us_product_template
msgid "DHL US"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_intl_us
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_intl_us
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_intl_us_product_template
msgid "DHL US -> International"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""
"Trūksta DHL paskyros numerio, pakeiskite pristatymo metodo nustatymus."

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "DHL password is missing, please modify your delivery method settings."
msgstr "Trūksta DHL slaptažodžio, pakeiskite pristatymo metodo nustatymus."

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_duty_payment
msgid "Dhl Duty Payment"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Dutiable Material"
msgstr "Apmuitinamos medžiagos"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Duties paid by"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__e
msgid "E - Express 9:00"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__epl2
msgid "EPL2"
msgstr "EPL2"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__eu
msgid "Europe"
msgstr "Europa"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__f
msgid "F - Freight Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__g
msgid "G - Domestic Economy Select"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__h
msgid "H - Economy Select"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "Hint: The destination may not require the dutiable option."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__i
msgid "I - Break Bulk Economy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__i
msgid "Inches"
msgstr "Coliai"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__j
msgid "J - Jumbo Box"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__k
msgid "K - Express 9:00"
msgstr "K - Ekspresas 9:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__k
msgid "Kilograms"
msgstr "Kilogramai"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__l
msgid "L - Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Label Format"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_image_format
msgid "Label Image Format"
msgstr "Etiketės ikonėlės formatas"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_template
msgid "Label Template"
msgstr "Etiketės šablonas"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__m
msgid "M - Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__n
msgid "N - Domestic Express"
msgstr "N - Vietinis ekspresas"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__o
msgid "O - DOM Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Options"
msgstr "Pasirinkimai"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__p
msgid "P - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_dimension_unit
msgid "Package Dimension Unit"
msgstr "Pakuotės tūrio vienetas"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_weight_unit
msgid "Package Weight Unit"
msgstr "Pakuotės svorio vienetas"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"Please define an incoterm in the associated sale order or set a default "
"incoterm for the company in the accounting's settings."
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Pateikite siuntimui bent vieną daiktą."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__l
msgid "Pounds"
msgstr "Svarai"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Tiekėjas"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__q
msgid "Q - Medical Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__r
msgid "R - GlobalMail Business"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__r
msgid "Recipient"
msgstr "Gavėjas"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_region_code
msgid "Region"
msgstr "Regionas"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__s
msgid "S - Same Day"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__s
msgid "Sender"
msgstr "Siuntėjas"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "Shipment created into DHL <br/> <b>Tracking Number : </b>%s"
msgstr "Siunta sukurta DHL <br/> <b>Sekimo numeris : </b>%s"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Siuntimo metodai"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__t
msgid "T - Express 12:00"
msgstr "T - Ekspresas 12:00"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"Kliento adreso nėra arba jis neteisingas (neužpildytas laukas(-ai) :\n"
" %s)"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"Praleistas arba neteisingas įmonės sandėlio adresas (trūksta lauko(ų) :\n"
"%s)"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid ""
"There is no price available for this shipping, you should rather try with "
"the DHL product %s"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__u
msgid "U - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_eu
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_intl
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_intl_us
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_us
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_eu_product_template
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_intl_product_template
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_intl_us_product_template
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_us_product_template
msgid "Units"
msgstr "Vienetai"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__v
msgid "V - Europack"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__w
msgid "W - Economy Select"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__x
msgid "X - Express Envelope"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__y
msgid "Y - Express 12:00"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "You can't cancel DHL shipping without pickup date."
msgstr "Negalite nutraukti DHL siuntimo be paėmimo datos."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__z
msgid "Z - Destination Charges"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__zpl2
msgid "ZPL2"
msgstr "ZPL2"
