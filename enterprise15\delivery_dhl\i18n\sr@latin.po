# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_dhl
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "0 - Logistics Services"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "1 - Domestic Express 12:00"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "2 - B2C"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "3 - B2C"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "4 - Jetline"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "5 - Sprintline"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "6 - Secureline"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "6X4_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "6X4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "6X4_thermal"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "7 - Express Easy"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "8 - Express Easy"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_A4_TC_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_CI_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_CI_thermal"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_RU_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_template:0
msgid "8X4_thermal"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "9 - Europack"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "A - Auto Reversals"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_region_code:0
msgid "America"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_region_code:0
msgid "Asia Pacific"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "B - Break Bulk Express"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "C - Medical Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_delivery_carrier
msgid "Carrier"
msgstr "Prevoznik"

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_dimension_unit:0
msgid "Centimeters"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier_dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "D - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_account_number
msgid "DHL Account Number"
msgstr ""

#. module: delivery_dhl
#: model:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_default_packaging_id
msgid "DHL Default Packaging Type"
msgstr ""

#. module: delivery_dhl
#: model:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_sale
#: model:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_stock
msgid "DHL Delivery Methods"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_password
msgid "DHL Password"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:382
#, python-format
msgid "DHL Site ID is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_SiteID
msgid "DHL SiteID"
msgstr ""

#. module: delivery_dhl
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_usa
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_usa_product_template
msgid "DHL USA"
msgstr ""

#. module: delivery_dhl
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_intl_product_template
msgid "DHL USA -> International"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:386
#, python-format
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:384
#, python-format
msgid "DHL password is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_dutiable
msgid "Dutiable Material"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "E - Express 9:00"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_image_format:0
msgid "EPL2"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_region_code:0
msgid "Europe"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "F - Freight Worldwide"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "G - Domestic Economy Select"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "H - Economy Select"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "I - Break Bulk Economy"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_dimension_unit:0
msgid "Inches"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "J - Jumbo Box"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "K - Express 9:00"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_weight_unit:0
msgid "Kilograms"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "L - Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_label_image_format
msgid "Label Image Format"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_label_template
msgid "Label Template"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "M - Express 10:30"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "N - Domestic Express"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:185
#, python-format
msgid "No service available for the selected product"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:88
#, python-format
msgid "No shipping available for the selected DHL product"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "O - DOM Express 10:30"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "P - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_image_format:0
msgid "PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_package_dimension_unit
msgid "Package Dimension Unit"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_package_weight_unit
msgid "Package Weight Unit"
msgstr ""

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_product_packaging
msgid "Packaging"
msgstr "Pakovanje"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:404
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_package_weight_unit:0
msgid "Pounds"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_product_code
msgid "Product"
msgstr "Proizvod"

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "Q - Medical Express"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "R - GlobalMail Business"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier_dhl_region_code
msgid "Region"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "S - Same Day"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:129
#, python-format
msgid "Shipment created into DHL <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "T - Express 12:00"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:392
#, python-format
msgid ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:400
#, python-format
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:406
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "U - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "V - Europack"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "W - Economy Select"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "X - Express Envelope"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "Y - Express 12:00"
msgstr ""

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:144
#, python-format
msgid "You can't cancel DHL shipping without pickup date."
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_product_code:0
msgid "Z - Destination Charges"
msgstr ""

#. module: delivery_dhl
#: selection:delivery.carrier,dhl_label_image_format:0
msgid "ZPL2"
msgstr ""
