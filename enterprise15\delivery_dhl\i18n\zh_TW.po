# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_dhl
# 
# Translators:
# se<PERSON> huang <<EMAIL>>, 2021
# eddie lin, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__0
msgid "0 - Logistics Services"
msgstr "0 - Logistics Services"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__1
msgid "1 - Domestic Express 12:00"
msgstr "1 - Domestic Express 12:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__2
msgid "2 - B2C"
msgstr "2 - B2C"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__3
msgid "3 - B2C"
msgstr "3 - B2C"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__4
msgid "4 - Jetline"
msgstr "4 - Jetline"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__5
msgid "5 - Sprintline"
msgstr "5 - Sprintline"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__6
msgid "6 - Secureline"
msgstr "6 - Secureline"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_a4_pdf
msgid "6X4_A4_PDF"
msgstr "6X4_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_pdf
msgid "6X4_PDF"
msgstr "6X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_thermal
msgid "6X4_thermal"
msgstr "6X4_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__7
msgid "7 - Express Easy"
msgstr "7 - Express Easy"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__8
msgid "8 - Express Easy"
msgstr "8 - Express Easy"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_pdf
msgid "8X4_A4_PDF"
msgstr "8X4_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_tc_pdf
msgid "8X4_A4_TC_PDF"
msgstr "8X4_A4_TC_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_pdf
msgid "8X4_CI_PDF"
msgstr "8X4_CI_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_thermal
msgid "8X4_CI_thermal"
msgstr "8X4_CI_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_pdf
msgid "8X4_PDF"
msgstr "8X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ru_a4_pdf
msgid "8X4_RU_A4_PDF"
msgstr "8X4_RU_A4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_thermal
msgid "8X4_thermal"
msgstr "8X4_thermal"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__9
msgid "9 - Europack"
msgstr "9 - Europack"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__a
msgid "A - Auto Reversals"
msgstr "A - Auto Reversals"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__am
msgid "America"
msgstr "美國(系統建立)"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__ap
msgid "Asia Pacific"
msgstr "Asia Pacific"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__b
msgid "B - Break Bulk Express"
msgstr "B - Break Bulk Express"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__c
msgid "C - Medical Express"
msgstr "C - Medical Express"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "承運商"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__c
msgid "Centimeters"
msgstr "公分"

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr "如果包裹需要繳稅，請勾選此項。"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__d
msgid "D - Express Worldwide"
msgstr "D - Express Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__delivery_type__dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__stock_package_type__package_carrier_type__dhl
msgid "DHL"
msgstr "DHL"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_account_number
msgid "DHL Account Number"
msgstr "DHL Account Number"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr "DHL Configuration"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_dom
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_eu
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_eu_product_template
msgid "DHL EU"
msgstr "DHL 歐洲"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_intl
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_intl_product_template
msgid "DHL EU -> International"
msgstr "DHL 歐洲 -> 國際"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_default_package_type_id
msgid "DHL Package Type"
msgstr "DHL Package Type"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_password
msgid "DHL Password"
msgstr "DHL的密碼"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_product_code
msgid "DHL Product"
msgstr "DHL Product"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_stock
msgid "DHL Shipping Methods"
msgstr "DHL"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "DHL Site ID is missing, please modify your delivery method settings."
msgstr "DHL Site ID is missing, please modify your delivery method settings."

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_SiteID
msgid "DHL SiteID"
msgstr "DHL SiteID"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_dom_us
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_us
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_us_product_template
msgid "DHL US"
msgstr "DHL 美國"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_intl_us
#: model:product.product,name:delivery_dhl.product_product_delivery_dhl_intl_us
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_intl_us_product_template
msgid "DHL US -> International"
msgstr "DHL 美國 -> 國際"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""
"DHL account number is missing, please modify your delivery method settings."

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "DHL doesn't support products with name greater than 75 characters."
msgstr "DHL 不支援名稱超過 75 個字元的產品。"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "DHL password is missing, please modify your delivery method settings."
msgstr "DHL password is missing, please modify your delivery method settings."

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_duty_payment
msgid "Dhl Duty Payment"
msgstr "Dhl Duty Payment"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Dutiable Material"
msgstr "Dutiable Material"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Duties paid by"
msgstr "Duties paid by"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__e
msgid "E - Express 9:00"
msgstr "E - Express 9:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__epl2
msgid "EPL2"
msgstr "EPL2"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__eu
msgid "Europe"
msgstr "歐洲"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__f
msgid "F - Freight Worldwide"
msgstr "F - Freight Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__g
msgid "G - Domestic Economy Select"
msgstr "G - Domestic Economy Select"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__h
msgid "H - Economy Select"
msgstr "H - Economy Select"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "Hint: The destination may not require the dutiable option."
msgstr "Hint: The destination may not require the dutiable option."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__i
msgid "I - Break Bulk Economy"
msgstr "I - Break Bulk Economy"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__i
msgid "Inches"
msgstr "英寸"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__j
msgid "J - Jumbo Box"
msgstr "J - Jumbo Box"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__k
msgid "K - Express 9:00"
msgstr "K - Express 9:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__k
msgid "Kilograms"
msgstr "公斤"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__l
msgid "L - Express 10:30"
msgstr "L - Express 10:30"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Label Format"
msgstr "Label Format"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_image_format
msgid "Label Image Format"
msgstr "Label Image Format"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_template
msgid "Label Template"
msgstr "Label Template"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__m
msgid "M - Express 10:30"
msgstr "M - Express 10:30"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__n
msgid "N - Domestic Express"
msgstr "N - Domestic Express"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__o
msgid "O - DOM Express 10:30"
msgstr "O - DOM Express 10:30"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Options"
msgstr "選項"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__p
msgid "P - Express Worldwide"
msgstr "P - Express Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_dimension_unit
msgid "Package Dimension Unit"
msgstr "Package Dimension Unit"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_weight_unit
msgid "Package Weight Unit"
msgstr "Package Weight Unit"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"Please define an incoterm in the associated sale order or set a default "
"incoterm for the company in the accounting's settings."
msgstr "請在關聯的銷售訂單中定義一個國際貿易術語, 或在會計設置中為公司設置一個預設的貿易術語。"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Please provide at least one item to ship."

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__l
msgid "Pounds"
msgstr "英鎊"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "服務商"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__q
msgid "Q - Medical Express"
msgstr "Q - Medical Express"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__r
msgid "R - GlobalMail Business"
msgstr "R - GlobalMail Business"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__r
msgid "Recipient"
msgstr "收件人"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_region_code
msgid "Region"
msgstr "地區"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__s
msgid "S - Same Day"
msgstr "S - Same Day"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__s
msgid "Sender"
msgstr "發送者"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "Shipment created into DHL <br/> <b>Tracking Number : </b>%s"
msgstr "送件已在 DHL 建立<br/><b>追蹤號碼：</b> %s"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_delivery_carrier
msgid "Shipping Methods"
msgstr "寄送方式"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_stock_package_type
msgid "Stock package type"
msgstr "庫存包裝類型"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__t
msgid "T - Express 12:00"
msgstr "T - Express 12:00"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/dhl_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid ""
"There is no price available for this shipping, you should rather try with "
"the DHL product %s"
msgstr "此運輸沒有可用價格，你應該嘗試使用 DHL 產品 %s"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__u
msgid "U - Express Worldwide"
msgstr "U - Express Worldwide"

#. module: delivery_dhl
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_eu
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_intl
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_intl_us
#: model:product.product,uom_name:delivery_dhl.product_product_delivery_dhl_us
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_eu_product_template
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_intl_product_template
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_intl_us_product_template
#: model:product.template,uom_name:delivery_dhl.product_product_delivery_dhl_us_product_template
msgid "Units"
msgstr "單位"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__v
msgid "V - Europack"
msgstr "V - Europack"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__w
msgid "W - Economy Select"
msgstr "W - Economy Select"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__x
msgid "X - Express Envelope"
msgstr "X - Express Envelope"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__y
msgid "Y - Express 12:00"
msgstr "Y - Express 12:00"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "You can't cancel DHL shipping without pickup date."
msgstr "DHL 運送如果未有取件日期，便不可取消。"

#. module: delivery_dhl
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
#, python-format
msgid "You cannot delete the commercial invoice sequence."
msgstr "不可刪除商業發票序號。"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__z
msgid "Z - Destination Charges"
msgstr "Z - Destination Charges"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__zpl2
msgid "ZPL2"
msgstr "ZPL2"
