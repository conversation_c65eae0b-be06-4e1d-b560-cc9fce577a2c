<?xml version="1.0" encoding="utf-8"?>
<odoo>

<record id="view_delivery_carrier_form_with_provider_dhl" model="ir.ui.view">
    <field name="name">delivery.carrier.form.provider.dhl</field>
    <field name="model">delivery.carrier</field>
    <field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
    <field name="arch" type="xml">
        <xpath expr="//page[@name='destination']" position='before'>
            <page string="DHL Configuration" name="dhl_configuration"
                 attrs="{'invisible': [('delivery_type', '!=', 'dhl')]}">
                <group>
                    <group>
                        <field name="dhl_SiteID" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_password" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_account_number" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_region_code" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_product_code" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                    </group>
                    <group>
                        <field name="dhl_default_package_type_id" attrs="{'required': [('delivery_type', '=', 'dhl')]}" domain="[('package_carrier_type', '=', 'dhl')]"/>
                        <field name="dhl_package_weight_unit" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_package_dimension_unit" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_label_image_format" string="Label Format" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                        <field name="dhl_label_template" attrs="{'required': [('delivery_type', '=', 'dhl')]}"/>
                    </group>
                    <group string="Options">
                        <field name="can_generate_return" invisible="1"/>
                        <field name="return_label_on_delivery" attrs="{'invisible': [('can_generate_return', '=', False)]}"/>
                        <field name="get_return_label_from_portal" attrs="{'invisible': [('return_label_on_delivery', '=', False)]}"/>
                        <field name="dhl_dutiable"/>
                        <field name="dhl_duty_payment" string='Duties paid by' attrs="{'required': [('delivery_type', '=', 'dhl')], 'invisible': [('dhl_dutiable', '=', False)]}"/>
                    </group>
                </group>
            </page>
        </xpath>
    </field>
</record>

</odoo>
