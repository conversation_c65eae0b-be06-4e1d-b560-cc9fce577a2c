# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_easypost
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>ra <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Tuomo Aura <<EMAIL>>, 2021\n"
"Language-Team: Finnish (https://www.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Copy your API keys in Odoo</b>\n"
"                <br/>"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Once your account is created, go to your Dashboard and click on the arrow next to your username to configure your carrier accounts. </b>\n"
"                <b>You can add new carrier accounts on the right side of the same page.</b>\n"
"                <br/>"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_stock_package_type_form_inherit_easypost
msgid ""
"<span attrs=\"{'invisible': [('package_carrier_type', '!=', "
"'easypost')]}\">Inches</span>"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "API keys"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Cancel"
msgstr "Peruuta"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Huolitsija"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__easypost_carrier
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_package_type__easypost_carrier
msgid "Carrier Prefix"
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__carrier_type
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier Type"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier accounts"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Carrrier Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_date
msgid "Created on"
msgstr "Luotu"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Default Package Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_package_type_id
msgid "Default Package Type for Easypost"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "Default Service Level"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__delivery_carrier_id
msgid "Delivery Carrier"
msgstr "Toimituksen huolitsija"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__display_name
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"Do not forget to load your Easypost carrier accounts for a valid "
"configuration."
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__epl2
msgid "EPL2"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__delivery_type__easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__stock_package_type__package_carrier_type__easypost
msgid "Easypost"
msgstr "Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type
msgid "Easypost Carrier Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type_id
msgid "Easypost Carrier Type ID, technical for API request"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Configuration"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_label_file_type
msgid "Easypost Label File Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_picking__ep_order_ref
msgid "Easypost Order Reference"
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_easypost_service
msgid "Easypost Service"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_stock
msgid "Easypost Shipping Methods"
msgstr "Easypost Toimitusmenetelmät"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Tutorial"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Website"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Easypost returned an error: %s"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Enter your API production key from Easypost account"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Enter your API test key from Easypost account."
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Go to"
msgstr "Siirry"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__id
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "If not set, the less expensive available service level will be chosen."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"It seems Easypost do not provide shipments for this order.                We"
" advise you to try with another package type or service level."
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Label Format"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost____last_update
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Load your Easypost carrier accounts"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Options"
msgstr "Vaihtoehdot"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__png
msgid "PNG"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Package type used in pack %s is not configured for easypost."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Production API Key"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Palveluntarjoaja"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Sale Order/Stock Picking is missing."
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Select"
msgstr "Valitse"

#. module: delivery_easypost
#: model:ir.actions.act_window,name:delivery_easypost.act_delivery_easypost_carrier_type
msgid "Select a carrier"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__name
msgid "Service Level Name"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:0
#, python-format
msgid "Shipment created into Easypost<br/><b>Tracking Numbers:</b> %s<br/>"
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Toimitustavat"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Sign up"
msgstr "Rekisteröidy"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Test API Key"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Default Package Type)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Delivery Carrier Type)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Production API Key)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Test API Key)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"There is no rate available for the selected service level for one of your "
"package. Please choose another service level."
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_picking
msgid "Transfer"
msgstr "Siirto"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Unknown error"
msgstr "Tuntematon virhe"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Unspecified field"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:0
#, python-format
msgid "You can't cancel Easypost shipping."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"You have no carrier linked to your Easypost Account.                Please "
"connect to Easypost, link your account to carriers and then retry."
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__zpl
msgid "ZPL"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "to create a new account:"
msgstr ""
