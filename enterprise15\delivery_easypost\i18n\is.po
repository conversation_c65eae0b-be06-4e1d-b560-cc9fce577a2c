# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_easypost
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-08-24 11:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Copy your API keys in Odoo</b>\n"
"                <br/>"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Once your account is created, go to your Dashboard and click on the arrow next to your username to configure your carrier accounts. </b>\n"
"                <b>You can add new carrier accounts on the right side of the same page.</b>\n"
"                <br/>"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "API keys"
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
msgid "Based on Rules"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Cancel"
msgstr "Hætta við"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_product_packaging__package_carrier_type
msgid "Carrier"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__easypost_carrier
#: model:ir.model.fields,field_description:delivery_easypost.field_product_packaging__easypost_carrier
msgid "Carrier Prefix"
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__carrier_type
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier Type"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier accounts"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Carrrier Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "DHL"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Default Package Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_packaging_id
msgid "Default Package Type for Easypost"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "Default Service Level"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__delivery_carrier_id
msgid "Delivery Carrier"
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier
msgid "Delivery Methods"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__display_name
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"Do not forget to load your Easypost carrier accounts for a valid "
"configuration."
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,easypost_label_file_type:0
msgid "EPL2"
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "Easypost"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type
msgid "Easypost Carrier Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type_id
msgid "Easypost Carrier Type ID, technical for API request"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Configuration"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_stock
msgid "Easypost Delivery Methods"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_label_file_type
msgid "Easypost Label File Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_picking__ep_order_ref
msgid "Easypost Order Reference"
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_easypost_service
msgid "Easypost Service"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Tutorial"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Website"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:33
#, python-format
msgid "Easypost returned an error: "
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Enter your API production key from Easypost account"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Enter your API test key from Easypost account."
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "FedEx"
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
msgid "Fixed Price"
msgstr "Fixed Price"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Go to"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__id
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__id
msgid "ID"
msgstr "Auðkenni"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "If not set, the less expensive available service level will be chosen."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:310
#, python-format
msgid ""
"It seems Easypost do not provide shipments for this order.                We"
" advise you to try with another package type or service level."
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Label File Type"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost____last_update
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Load your Easypost carrier accounts"
msgstr ""

#. module: delivery_easypost
#: selection:product.packaging,package_carrier_type:0
msgid "No carrier integration"
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,easypost_label_file_type:0
msgid "PDF"
msgstr "PDF"

#. module: delivery_easypost
#: selection:delivery.carrier,easypost_label_file_type:0
msgid "PNG"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:78
#: code:addons/delivery_easypost/models/easypost_request.py:85
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_product_packaging
msgid "Product Packaging"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:215
#, python-format
msgid "Product packaging used in pack %s is not configured for easypost."
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Production API Key"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Provider"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:73
#, python-format
msgid "Sale Order/Stock Picking is missing."
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Select"
msgstr "Select"

#. module: delivery_easypost
#: model:ir.actions.act_window,name:delivery_easypost.act_delivery_easypost_carrier_type
msgid "Select a carrier"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__name
msgid "Service Level Name"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:106
#, python-format
msgid "Shipping label for packages"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Sign up"
msgstr ""

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Test API Key"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:70
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Default Product Packaging)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:67
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Delivery Carrier Type)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:62
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Production API Key)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:64
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Test API Key)"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:80
#: code:addons/delivery_easypost/models/easypost_request.py:87
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:321
#, python-format
msgid ""
"There is no rate available for the selected service level for one of your "
"package. Please choose another service level."
msgstr ""

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "UPS"
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
msgid "USPS"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:128
#, python-format
msgid "You can't cancel Easypost shipping."
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:52
#, python-format
msgid ""
"You have no carrier linked to your Easypost Account.                Please "
"connect to Easypost, link your account to carriers and then retry."
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,easypost_label_file_type:0
msgid "ZPL"
msgstr ""

#. module: delivery_easypost
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "bpost"
msgstr ""

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:86
#, python-format
msgid "error_message"
msgstr ""

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "to create a new account:"
msgstr ""
