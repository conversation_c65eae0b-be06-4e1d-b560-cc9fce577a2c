# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_easypost
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Copy your API keys in Odoo</b>\n"
"                <br/>"
msgstr ""
"<b>Copiare le chiavi API in Odoo</b>\n"
"                <br/>"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Once your account is created, go to your Dashboard and click on the arrow next to your username to configure your carrier accounts. </b>\n"
"                <b>You can add new carrier accounts on the right side of the same page.</b>\n"
"                <br/>"
msgstr ""
"<b>Dopo aver creato l'account, andare sulla bacheca e fare clic sulla freccia accanto al nome utente per configurare i conti vettore. </b>\n"
"                <b>Sul lato destro della stessa pagina è possibile aggiungere nuovi conti vettore.</b>\n"
"                <br/>"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_stock_package_type_form_inherit_easypost
msgid ""
"<span attrs=\"{'invisible': [('package_carrier_type', '!=', "
"'easypost')]}\">Inches</span>"
msgstr ""
"<span attrs=\"{'invisible': [('package_carrier_type', '!=', "
"'easypost')]}\">Pollici</span>"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "API keys"
msgstr "Chiavi API"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Cancel"
msgstr "Annulla"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Vettore"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__easypost_carrier
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_package_type__easypost_carrier
msgid "Carrier Prefix"
msgstr "Prefisso vettore"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__carrier_type
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier Type"
msgstr "Tipologia vettore"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier accounts"
msgstr "Conti vettore"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Carrrier Type"
msgstr "Tipologia vettore"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Default Package Type"
msgstr "Tipo di imballo predefinito"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_package_type_id
msgid "Default Package Type for Easypost"
msgstr "Tipo di imballo predefinito per EasyPost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "Default Service Level"
msgstr "Livello di servizio predefinito"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__delivery_carrier_id
msgid "Delivery Carrier"
msgstr "Vettore di consegna"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__display_name
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"Do not forget to load your Easypost carrier accounts for a valid "
"configuration."
msgstr ""
"Per una corretta configurazione non dimenticarsi di caricare gli account del"
" vettore Easypost."

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__epl2
msgid "EPL2"
msgstr "EPL2"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__delivery_type__easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__stock_package_type__package_carrier_type__easypost
msgid "Easypost"
msgstr "Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type
msgid "Easypost Carrier Type"
msgstr "Tipo vettore Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type_id
msgid "Easypost Carrier Type ID, technical for API request"
msgstr "ID tecnico per richiesta API del tipo vettore EasyPost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Configuration"
msgstr "Configurazione Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_label_file_type
msgid "Easypost Label File Type"
msgstr "Tipo file di etichetta Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_picking__ep_order_ref
msgid "Easypost Order Reference"
msgstr "Riferimento ordine Easypost"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_easypost_service
msgid "Easypost Service"
msgstr "Servizio Easypost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_stock
msgid "Easypost Shipping Methods"
msgstr "Metodi di spedizione Easypost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Tutorial"
msgstr "Guida EasyPost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Website"
msgstr "sito web di EasyPost"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Easypost returned an error: %s"
msgstr "Easypost ha restituito un errore: %s"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Enter your API production key from Easypost account"
msgstr ""
"Inserire la chiave API di produzione proveniente dall'account EasyPost"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Enter your API test key from Easypost account."
msgstr "Inserire la chiave API di test proveniente dall'account EasyPost."

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Go to"
msgstr "Andare nel"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__id
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__id
msgid "ID"
msgstr "ID"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "If not set, the less expensive available service level will be chosen."
msgstr ""
"Se non impostato, viene scelto il livello di servizio disponibile meno "
"costoso."

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"It seems Easypost do not provide shipments for this order.                We"
" advise you to try with another package type or service level."
msgstr ""
"Sembra che EasyPost non fornisca spedizioni per questo ordine."
"                Consigliamo di provare con un altro tipo di imballo o "
"livello di servizio.."

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Label Format"
msgstr "Formato etichetta"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost____last_update
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Load your Easypost carrier accounts"
msgstr "Carica conti vettore EasyPost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Options"
msgstr "Opzioni"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__png
msgid "PNG"
msgstr "PNG"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Package type used in pack %s is not configured for easypost."
msgstr ""
"Il tipo di imballaggio utilizzato per il pacco %s non è configurato per "
"easypost."

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Fornire almeno un articolo da spedire."

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Production API Key"
msgstr "Chiave API di produzione"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Corriere"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Sale Order/Stock Picking is missing."
msgstr "Ordine di vendita/prelievo di magazzino mancante."

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Select"
msgstr "Seleziona"

#. module: delivery_easypost
#: model:ir.actions.act_window,name:delivery_easypost.act_delivery_easypost_carrier_type
msgid "Select a carrier"
msgstr "Seleziona un vettore"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__name
msgid "Service Level Name"
msgstr "Nome livello di servizio"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:0
#, python-format
msgid "Shipment created into Easypost<br/><b>Tracking Numbers:</b> %s<br/>"
msgstr ""
"Spedizione creata in Easypost<br/> <b>Codici di tracciabilità:</b>%s<br/>"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Metodi di spedizione"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Sign up"
msgstr "Registrati"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_package_type
msgid "Stock package type"
msgstr "Tipo di pacchetto stock"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Test API Key"
msgstr "Chiave API di test"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Default Package Type)"
msgstr ""
"Il vettore %s è mancante (Campo/i mancante/i:\n"
"tipo imballaggio predefinito)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Delivery Carrier Type)"
msgstr ""
"Vettore %s mancante (campi mancanti :\n"
" Tipologia vettore di consegna)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Production API Key)"
msgstr ""
"Vettore %s mancante (campi mancanti:\n"
"Chiave API di produzione)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Test API Key)"
msgstr ""
"Vettore %s mancante (campi mancanti :\n"
" Chiave API di test)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""
"Impossibile calcolare una stima di prezzo perché manca il peso del prodotto."

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Impossibile calcolare una stima della spedizione, manca il peso dei seguenti prodotti:\n"
"%s"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"There is no rate available for the selected service level for one of your "
"package. Please choose another service level."
msgstr ""
"Nessuna tariffa disponibile per il livello di servizio selezionato per uno "
"degli imballi. Scegliere un altro livello di servizio."

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_picking
msgid "Transfer"
msgstr "Trasferimento"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Unknown error"
msgstr "Errore sconosciuto"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Unspecified field"
msgstr "Campo non specificato"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:0
#, python-format
msgid "You can't cancel Easypost shipping."
msgstr "Impossibile annullare la spedizione Easypost."

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"You have no carrier linked to your Easypost Account.                Please "
"connect to Easypost, link your account to carriers and then retry."
msgstr ""
"Non sono presenti vettori collegati all'account EasyPost.                "
"Connettersi a EasyPost, collegare l'account ai vettori e poi riprovare."

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "to create a new account:"
msgstr "e creare un nuovo account:"
