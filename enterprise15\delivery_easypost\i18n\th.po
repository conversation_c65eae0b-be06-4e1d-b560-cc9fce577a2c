# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_easypost
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# Wichanon Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://www.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Copy your API keys in Odoo</b>\n"
"                <br/>"
msgstr ""
"<b>คัดลอกคีย์ API ของคุณใน Odoo</b>\n"
"                <br/>"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"<b>Once your account is created, go to your Dashboard and click on the arrow next to your username to configure your carrier accounts. </b>\n"
"                <b>You can add new carrier accounts on the right side of the same page.</b>\n"
"                <br/>"
msgstr ""
"<b>เมื่อสร้างบัญชีของคุณแล้ว ให้ไปที่แดชบอร์ดแล้วคลิกที่ลูกศรถัดจากชื่อผู้ใช้ของคุณเพื่อกำหนดค่าบัญชีผู้ให้บริการของคุณ </b>\n"
"                <b>คุณสามารถเพิ่มบัญชีผู้ให้บริการใหม่ได้ทางด้านขวาของหน้าเดียวกัน</b>\n"
"                <br/>"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_stock_package_type_form_inherit_easypost
msgid ""
"<span attrs=\"{'invisible': [('package_carrier_type', '!=', "
"'easypost')]}\">Inches</span>"
msgstr ""
"<span attrs=\"{'invisible': [('package_carrier_type', '!=', "
"'easypost')]}\">นิ้ว</span>"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "API keys"
msgstr "API คีย์"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Cancel"
msgstr "ยกเลิก"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "ผู้ให้บริการ"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__easypost_carrier
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_package_type__easypost_carrier
msgid "Carrier Prefix"
msgstr "คำนำหน้าผู้ให้บริการ"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__carrier_type
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier Type"
msgstr "ประเภทผู้ให้บริการ"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Carrier accounts"
msgstr "บัญชีผู้ให้บริการ"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Carrrier Type"
msgstr "ประเภทผู้ให้บริการ"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__create_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Default Package Type"
msgstr "ประเภทของบรรจุภัณฑ์เริ่มต้น"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_package_type_id
msgid "Default Package Type for Easypost"
msgstr "ประเภทของบรรจุภัณฑ์เริ่มต้นสำหรับ Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "Default Service Level"
msgstr "ค่าเริ่มต้นระดับการบริการ"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__delivery_carrier_id
msgid "Delivery Carrier"
msgstr "ผู้ให้บริการจัดส่ง"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__display_name
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid ""
"Do not forget to load your Easypost carrier accounts for a valid "
"configuration."
msgstr ""
"อย่าลืมโหลดบัญชีผู้ให้บริการ Easypost ของคุณสำหรับการกำหนดค่าที่ถูกต้อง"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__epl2
msgid "EPL2"
msgstr "EPL2"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__delivery_type__easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__stock_package_type__package_carrier_type__easypost
msgid "Easypost"
msgstr "Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type
msgid "Easypost Carrier Type"
msgstr "ประเภทการให้บริการ Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_delivery_type_id
msgid "Easypost Carrier Type ID, technical for API request"
msgstr "ไอดีประเภทผู้ให้บริการ Easypost ข้อมูลทางเทคนิคสำหรับคำขอ API"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Configuration"
msgstr "กำหนดค่า Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_label_file_type
msgid "Easypost Label File Type"
msgstr "ประเภทไฟล์ฉลาก Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_stock_picking__ep_order_ref
msgid "Easypost Order Reference"
msgstr "อ้างอิงคำสั่ง Easypost"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_easypost_service
msgid "Easypost Service"
msgstr "บริการ Easypost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_easypost.res_config_settings_view_form_stock
msgid "Easypost Shipping Methods"
msgstr "วิธีการการจัดส่ง Easypost "

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Tutorial"
msgstr "บทเรียน Easypost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Easypost Website"
msgstr "เว็บไซต์ Easypost"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Easypost returned an error: %s"
msgstr "Easypost ส่งคืนข้อผิดพลาด: %s"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Enter your API production key from Easypost account"
msgstr "ป้อนคีย์การผลิต API ของคุณจากบัญชี Easypost"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Enter your API test key from Easypost account."
msgstr "ป้อนคีย์ทดสอบ API ของคุณจากบัญชี Easypost"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Go to"
msgstr "ไปยัง"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__id
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__id
msgid "ID"
msgstr "ไอดี"

#. module: delivery_easypost
#: model:ir.model.fields,help:delivery_easypost.field_delivery_carrier__easypost_default_service_id
msgid "If not set, the less expensive available service level will be chosen."
msgstr "ถ้าไม่ได้ตั้งค่าไว้ ระบบจะเลือกระดับการบริการที่มีราคาไม่แพง"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"It seems Easypost do not provide shipments for this order.                We"
" advise you to try with another package type or service level."
msgstr ""
"ดูเหมือนว่า Easypost จะไม่จัดส่งสินค้าสำหรับคำสั่งซื้อนี้ "
"เราขอแนะนำให้คุณลองใช้แพ็คเกจหรือระดับบริการอื่น"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Label Format"
msgstr "รูปแบบฉลาก"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost____last_update
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_uid
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier_easypost__write_date
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Load your Easypost carrier accounts"
msgstr "โหลดบัญชีผู้ให้บริการ Easypost ของคุณ"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Options"
msgstr "ตัวเลือก"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__png
msgid "PNG"
msgstr "PNG"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Package type used in pack %s is not configured for easypost."
msgstr "ประเภทแพ็คเกจที่ใช้ในแพ็ค %sไม่ได้กำหนดค่าสำหรับ easypost"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "โปรดระบุอย่างน้อยหนึ่งรายการเพื่อจัดส่ง"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_production_api_key
msgid "Production API Key"
msgstr "การผลิต API คีย์"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "ผู้ให้บริการ"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Sale Order/Stock Picking is missing."
msgstr "ไม่มีคำสั่งขาย/การหยิบสต๊อก"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_easypost_carrier_type
msgid "Select"
msgstr "เลือก"

#. module: delivery_easypost
#: model:ir.actions.act_window,name:delivery_easypost.act_delivery_easypost_carrier_type
msgid "Select a carrier"
msgstr "เลือกผู้ให้บริการ"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_easypost_service__name
msgid "Service Level Name"
msgstr "ชื่อระดับบริการ"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:0
#, python-format
msgid "Shipment created into Easypost<br/><b>Tracking Numbers:</b> %s<br/>"
msgstr "การจัดส่งที่สร้างโดย Easypost<br/><b>หมายเลขติดตาม:</b> %s<br/>"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_delivery_carrier
msgid "Shipping Methods"
msgstr "วิธีการการจัดส่ง"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "Sign up"
msgstr "ลงชื่อ"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_package_type
msgid "Stock package type"
msgstr "ประเภทของแพ็คเกจ"

#. module: delivery_easypost
#: model:ir.model.fields,field_description:delivery_easypost.field_delivery_carrier__easypost_test_api_key
msgid "Test API Key"
msgstr "ทดสอบ API คีย์"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Default Package Type)"
msgstr ""
"%s ผู้ให้บริการหายไป (ช่องหายไป) :\n"
" ประเภทแพ็คเกจเริ่มต้น)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Delivery Carrier Type)"
msgstr ""
"%s ผู้ให้บริการหายไป (ช่องหายไป) :\n"
" ประเภทผู้ให้บริการจัดส่ง)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Production API Key)"
msgstr ""
" %sผู้ให้บริการหายไป (ช่องหายไป) :\n"
" Production API Key)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The %s carrier is missing (Missing field(s) :\n"
" Test API Key)"
msgstr ""
"%s ผู้ให้บริการหายไป (ช่องหายไป) :\n"
" Test API Key)"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr "ไม่สามารถคำนวณราคาโดยประมาณได้เนื่องจากไม่มีน้ำหนักของสินค้าของคุณ"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"ไม่สามารถคำนวณราคาจัดส่งโดยประมาณได้เนื่องจากไม่มีน้ำหนักของสินค้าต่อไปนี้:\n"
" %s"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"There is no rate available for the selected service level for one of your "
"package. Please choose another service level."
msgstr ""
"ไม่มีอัตราสำหรับระดับบริการที่เลือก ของแพ็กเกจใดแพ็กเกจหนึ่งของคุณ "
"โปรดเลือกระดับการบริการอื่น ๆ"

#. module: delivery_easypost
#: model:ir.model,name:delivery_easypost.model_stock_picking
msgid "Transfer"
msgstr "โอน"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Unknown error"
msgstr "ข้อผิดพลาดที่ไม่รู้จัก"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid "Unspecified field"
msgstr "ฟิลด์ที่ไม่ระบุ"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/delivery_carrier.py:0
#, python-format
msgid "You can't cancel Easypost shipping."
msgstr "คุณไม่สามารถยกเลิกการจัดส่ง Easypost ได้"

#. module: delivery_easypost
#: code:addons/delivery_easypost/models/easypost_request.py:0
#, python-format
msgid ""
"You have no carrier linked to your Easypost Account.                Please "
"connect to Easypost, link your account to carriers and then retry."
msgstr ""
"คุณไม่มีผู้ให้บริการที่เชื่อมโยงกับบัญชี Easypost ของคุณ โปรดเชื่อมต่อกับ "
"Easypost เพื่อเชื่อมโยงบัญชีของคุณกับผู้ให้บริการแล้วลองอีกครั้ง"

#. module: delivery_easypost
#: model:ir.model.fields.selection,name:delivery_easypost.selection__delivery_carrier__easypost_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: delivery_easypost
#: model_terms:ir.ui.view,arch_db:delivery_easypost.view_delivery_carrier_form_inherit_delivery_easypost
msgid "to create a new account:"
msgstr "สร้างบัญชีใหม่"
