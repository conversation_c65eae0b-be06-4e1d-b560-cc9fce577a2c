{"AmazonMws": ["UPS Rates", "USPS Rates", "FedEx Rates", "UPS Labels", "USPS Labels", "FedEx Labels", "UPS Tracking", "USPS Tracking", "FedEx Tracking"], "APC": ["parcelConnectBookService", "parcelConnectExpeditedDDP", "parcelConnectExpeditedDDU", "parcelConnectPriorityDDP", "parcelConnectPriorityDDPDelcon", "parcelConnectPriorityDDU", "parcelConnectPriorityDDUDelcon", "parcelConnectPriorityDDUPQW", "parcelConnectStandardDDU", "parcelConnectStandardDDUPQW", "parcelConnectPacketDDU"], "Aramex": ["Domestic", "PriorityDocumentExpress", "PriorityParcelExpress", "PriorityLetterExpress", "DeferredDocumentExpress", "DeferredParcelExpress", "GroundDocumentExpress", "GroundParcelExpress", "EconomyParcelExpress"], "ArrowXL": ["48HRBrownInstall", "48HRBrownInstallRemove", "48HROther", "48HROtherRemove", "48HRWhiteInstall", "48HRWhiteInstallRemove", "48HRWhiteRemove", "ExpressBrownInstall", "ExpressBrownInstallRemove", "ExpressOther", "ExpressOtherRemove", "ExpressWhiteInstall", "ExpressWhiteInstallRemove", "ExpressWhiteRemove", "StandardBrownInstall", "StandardBrownInstallRemove", "StandardBrownRemove", "StandardOther", "StandardOtherAssemble", "StandardOtherNoUnpack", "StandardOtherRemove", "StandardOtherUnpack", "StandardOtherUnpackLeavePackaging", "StandardReturns", "StandardWhiteInstall", "StandardWhiteInstallRemove", "StandardWhiteRemove"], "Asendia": ["PMI", "ePacket", "IPA", "ISAL"], "Asendia HK": ["NormalRegistered", "PostExpress"], "Australia Post": ["ExpressPost", "ExpressPostSignature", "ParcelPost", "ParcelPostSignature", "ParcelPostExtra", "ParcelPostWinePlusSignature"], "AxleHire": ["AxleHireDelivery"], "Boxberry": ["Boxberry"], "Canada Post": ["RegularParcel", "ExpeditedParcel", "Xpresspost", "XpresspostCertified", "Priority", "LibraryBooks", "ExpeditedParcelUSA", "PriorityWorldwideEnvelopeUSA", "PriorityWorldwidePakUSA", "PriorityWorldwideParcelUSA", "SmallPacketUSAAir", "TrackedPacketUSA", "TrackedPacketUSALVM", "XpresspostUSA", "XpresspostInternational", "InternationalParcelAir", "InternationalParcelSurface", "PriorityWorldwideEnvelopeIntl", "PriorityWorldwidePakIntl", "PriorityWorldwideParcelIntl", "SmallPacketInternationalAir", "SmallPacketInternationalSurface", "TrackedPacketInternational"], "Canpar": ["Ground", "SelectLetter", "SelectPak", "Select", "OvernightLetter", "OvernightPak", "Overnight", "SelectUSA", "USAPak", "USALetter", "USA", "International"], "CDL Last Mile Solutions": ["DISTRIBUTION", "Same Day"], "Colis PrivÃ©": ["<PERSON><PERSON><PERSON>"], "Deliv": ["Scheduled", "OnDemand"], "Deutsche Post": ["PacketPlus"], "DHL eCommerce Asia": ["Packet", "PacketPlus", "ParcelDirect", "ParcelDirectExpedited"], "DHL Express": ["BreakBulkEconomy", "BreakBulkExpress", "DomesticEconomySelect", "DomesticExpress", "DomesticExpress1030", "DomesticExpress1200", "EconomySelect", "EconomySelectNonDoc", "EuroPack", "EuropackNonDoc", "Express1030", "Express1030NonDoc", "Express1200NonDoc", "Express1200", "Express900", "Express900NonDoc", "ExpressEasy", "ExpressEasyNonDoc", "ExpressEnvelope", "ExpressWorldwide", "ExpressWorldwideB2C", "ExpressWorldwideB2CNonDoc", "ExpressWorldwideECX", "ExpressWorldwideNonDoc", "FreightWorldwide", "GlobalmailBusiness", "JetLine", "JumboBox", "LogisticsServices", "SameDay", "SecureLine", "SprintLine"], "DHL eCommerce": ["BPMExpeditedDomestic", "BPMGroundDomestic", "FlatsExpeditedDomestic", "FlatsGroundDomestic", "MediaMailGroundDomestic", "ParcelExpeditedMax", "ParcelPlusExpeditedDomestic", "ParcelPlusGroundDomestic", "ParcelsExpeditedDomestic", "ParcelsGroundDomestic", "MarketingParcelExpeditedDomestic", "MarketingParcelGroundDomestic"], "DHL eCommerce International": ["DHLPacketInternationalPriority", "DHLPacketInternationalStandard", "DHLPacketPlusInternational", "DHLPacketIPA", "DHLPacketISAL", "DHLParcelInternationalPriority", "DHLParcelInternationalStandard", "DHLParcelDirectInternationalPriority", "DHLParcelDirectInternationalExpedited"], "Dicom": ["Ground"], "Direct Link": ["RegisteredMail", "UntrackedMail", "ParcelMail"], "Doorman": ["DoormanDirect"], "DPD": ["DPDCLASSIC", "DPD8:30", "DPD10:00", "DPD12:00", "DPD18:00", "DPDEXPRESS", "DPDPARCELLETTER", "DPDPARCELLETTERPLUS", "DPDINTERNATIONALMAIL"], "DPD UK": ["AirExpressInternationalAir", "AirClassicInternationalAir", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FreightParcelSunday", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PalletDpdClassic", "ExpresspakDpdClassic", "ExpresspakSunday", "ParcelDpdClassic", "ParcelDpdTwoDay", "ParcelDpdNextDay", "ParcelDpd12", "ParcelDpd10", "ParcelDpd10", "ParcelReturnToShop", "ParcelSaturday", "ParcelSaturday12", "ParcelSaturday10", "ParcelSaturday10", "ParcelSunday12", "FreightParcelDpdClassic", "FreightParcelSunday12", "ExpresspakDpdNextDay", "ExpresspakDpd12", "ExpresspakDpd10", "ExpresspakDpd10", "ExpresspakSaturday", "ExpresspakSaturday12", "ExpresspakSaturday10", "ExpresspakSaturday10", "ExpresspakSunday12", "PalletSunday12", "PalletDpdTwoDay", "PalletDpdNextDay", "PalletDpd12", "PalletDpd10", "PalletSaturday", "PalletSaturday12", "PalletSaturday10", "FreightParcelDpdTwoDay", "FreightParcelDpdNextDay", "FreightParcelDpd12", "FreightParcelDpd10", "FreightParcelSaturday", "FreightParcelSaturday12", "FreightParcelSaturday10", "ParcelShipToShop"], "Estafeta": ["NextDayBy930", "NextDayBy1130", "NextDay", "Ground", "TwoDay", "LTL"], "Fastway": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "FedEx": ["FEDEX_GROUND", "FEDEX_2_DAY", "FEDEX_2_DAY_AM", "FEDEX_EXPRESS_SAVER", "STANDARD_OVERNIGHT", "FIRST_OVERNIGHT", "PRIORITY_OVERNIGHT", "INTERNATIONAL_ECONOMY", "INTERNATIONAL_FIRST", "INTERNATIONAL_PRIORITY", "GROUND_HOME_DELIVERY", "SMART_POST"], "FedEx SameDay City": ["EconomyService", "StandardService", "PriorityService", "LastMile"], "FedEx UK": ["FEDEX_NEXT_DAY_EARLY_MORNING", "FEDEX_NEXT_DAY_MID_MORNING", "FEDEX_NEXT_DAY_AFTERNOON", "FEDEX_NEXT_DAY_END_OF_DAY", "FEDEX_DISTANCE_DEFERRED", "FEDEX_NEXT_DAY_FREIGHT"], "FedEx SmartPost": ["SMART_POST"], "Globegistics": ["PMEI", "PMI", "eComDomestic", "eComDomesticBPM", "eComDomesticFlats", "eComEurope", "eComExpress", "eComExtra", "eComIPA", "eComISAL", "eComPacket", "eComPriority", "eComStandard", "eComTrackedDDP", "eComTrackedDDU"], "GSO": ["EarlyPriorityOvernight", "PriorityOvernight", "CaliforniaParcelService", "SaturdayDeliveryService", "EarlySaturdayService", "Ground", "Overnight"], "Interlink Express": ["InterlinkAirClassicInternationalAir", "InterlinkAirExpressInternationalAir", "InterlinkExpresspak1By10:30", "InterlinkExpresspak1By12", "InterlinkExpresspak1NextDay", "InterlinkExpresspak1Saturday", "InterlinkExpresspak1SaturdayBy10:30", "InterlinkExpresspak1SaturdayBy12", "InterlinkExpresspak1Sunday", "InterlinkExpresspak1SundayBy12", "InterlinkExpresspak5By10", "InterlinkExpresspak5By10:30", "InterlinkExpresspak5By12", "InterlinkExpresspak5NextDay", "InterlinkExpresspak5Saturday", "InterlinkExpresspak5SaturdayBy10", "InterlinkExpresspak5SaturdayBy10:30", "InterlinkExpresspak5SaturdayBy12", "InterlinkExpresspak5Sunday", "InterlinkExpresspak5SundayBy12", "InterlinkFreightBy10", "InterlinkFreightBy12", "InterlinkFreightNextDay", "InterlinkFreightSaturday", "InterlinkFreightSaturdayBy10", "InterlinkFreightSaturdayBy12", "InterlinkFreightSunday", "InterlinkFreightSundayBy12", "InterlinkParcelBy10", "InterlinkParcelBy10:30", "InterlinkParcelBy12", "InterlinkParcelDpdEuropeByRoad", "InterlinkParcelNextDay", "InterlinkParcelReturn", "InterlinkParcelReturnToShop", "InterlinkParcelSaturday", "InterlinkParcelSaturdayBy10", "InterlinkParcelSaturdayBy10:30", "InterlinkParcelSaturdayBy12", "InterlinkParcelShipToShop", "InterlinkParcelSunday", "InterlinkParcelSundayBy12", "InterlinkParcelTwoDay", "InterlinkPickupParcelDpdEuropeByRoad"], "LaserShip": ["SameDay", "NextDay"], "Liefery": ["TourAppointment20161124From1600To1900"], "LSO": ["GroundEarly", "GroundBasic", "PriorityBasic", "Priority<PERSON><PERSON><PERSON>", "PrioritySaturday", "Priority2ndDay", "SameDay"], "Network4": ["BronzeDelivery", "BronzeDeliveryAndCollection", "SilverDelivery", "SilverDeliveryAndCollection", "GoldDelivery", "GoldDeliveryAndCollection", "PlatinumDelivery", "PlatinumDeliveryAndCollection"], "Newgistics": ["ParcelSelect", "ParcelSelectLightweight", "Ground", "Express", "FirstClassMail", "PriorityMail", "BoundPrintedMatter"], "Ninja Van": ["Standard Courier", "Express Courier"], "Norco": ["EarlyOvernite", "MorningOvernite", "OneOvernite", "NextDayOvernite", "SaturdayOvernite", "2DayMetro", "Ground", "Overnight"], "OnTrac": ["Sunrise", "Gold", "OnTrac Gold", "Ground", "Overnight"], "OnTrac DirectPost": ["FirstClassMail", "PriorityMail", "BoundPrintedMatter", "MediaMail", "ParcelSelect", "ParcelSelectLightweight"], "Orange DS": ["OrangeDSInternational", "OrangeDSDomestic", "OrangeDSTracking", "OrangeDSSecureShip"], "Postmates": ["OnDemand"], "PostNL": ["EUPackSpecialCOD", "EUPackStandard", "BelgiumStatedAddressOnlyReturnNotHome", "BelgiumReturnNotHome", "BelgiumSignatureStatedAddressOnlyReturnNotHome", "BelgiumSignatureReturnNotHome", "BelgiumCODReturnNotHome", "BelgiumExtraCoverStatedAddressOnlyReturnNotHome", "BelgiumCODExtraCoverReturnNotHome", "LetterIDAgeCheck", "LetterAgeCheck", "LetterIDCheck", "StandardShipment", "COD", "ExtraCover", "SignatureStatedAddressOnly", "DeliveryToNeighbourReturnNotHome", "CODExtraCover", "CODReturnNotHome", "ExtraCoverReturnNotHome", "SignatureStatedAddressOnlyReturnNotHome", "CODExtraCoverReturnNotHome", "Signature", "StatedAddressOnly", "SignatureReturnNotHome", "StatedAddressOnlyReturnNotHome", "ParcelAgeCheck", "ParcelIDAgeCheck", "ParcelID<PERSON>heck"], "Purolator": ["PurolatorExpress", "PurolatorExpress12PM", "PurolatorExpressPack12PM", "PurolatorExpressBox12PM", "PurolatorExpressEnvelope12PM", "PurolatorExpress1030AM", "PurolatorExpress9AM", "PurolatorExpressBox", "PurolatorExpressBox1030AM", "PurolatorExpressBox9AM", "PurolatorExpressBoxEvening", "PurolatorExpressBoxInternational", "PurolatorExpressBoxInternational1030AM", "PurolatorExpressBoxInternational1200", "PurolatorExpressBoxInternational9AM", "PurolatorExpressBoxUS", "PurolatorExpressBoxUS1030AM", "PurolatorExpressBoxUS1200", "PurolatorExpressBoxUS9AM", "PurolatorExpressEnvelope", "PurolatorExpressEnvelope1030AM", "PurolatorExpressEnvelope9AM", "PurolatorExpressEnvelopeEvening", "PurolatorExpressEnvelopeInternational", "PurolatorExpressEnvelopeInternational1030AM", "PurolatorExpressEnvelopeInternational1200", "PurolatorExpressEnvelopeInternational9AM", "PurolatorExpressEnvelopeUS", "PurolatorExpressEnvelopeUS1030AM", "PurolatorExpressEnvelopeUS1200", "PurolatorExpressEnvelopeUS9AM", "PurolatorExpressEvening", "PurolatorExpressInternational", "PurolatorExpressInternational1030AM", "PurolatorExpressInternational1200", "PurolatorExpressInternational9AM", "PurolatorExpressPack", "PurolatorExpressPack1030AM", "PurolatorExpressPack9AM", "PurolatorExpressPackEvening", "PurolatorExpressPackInternational", "PurolatorExpressPackInternational1030AM", "PurolatorExpressPackInternational1200", "PurolatorExpressPackInternational9AM", "PurolatorExpressPackUS", "PurolatorExpressPackUS1030AM", "PurolatorExpressPackUS1200", "PurolatorExpressPackUS9AM", "PurolatorExpressUS", "PurolatorExpressUS1030AM", "PurolatorExpressUS1200", "PurolatorExpressUS9AM", "PurolatorGround", "PurolatorGround1030AM", "PurolatorGround9AM", "PurolatorGroundDistribution", "PurolatorGroundEvening", "PurolatorGroundRegional", "PurolatorGroundUS"], "Royal Mail": ["InternationalSigned", "InternationalStandard", "InternationalTracked", "InternationalTrackedAndSigned", "1stClass", "1stClassSignedFor", "2ndClass", "2ndClassSignedFor", "RoyalMail24", "RoyalMail24SignedFor", "RoyalMail48", "RoyalMail48SignedFor", "SpecialDeliveryGuaranteed1pm", "SpecialDeliveryGuaranteed9am", "StandardLetter1stClass", "StandardLetter1stClassSignedFor", "StandardLetter2ndClass", "StandardLetter2ndClassSignedFor", "Tracked24", "Tracked24HighVolume", "Tracked24HighVolumeSignature", "Tracked24Signature", "Tracked48", "Tracked48HighVolume", "Tracked48HighVolumeSignature", "Tracked48Signature"], "RR Donnelley": ["CourierServiceDDP", "CourierServiceDDU", "DomesticEconomyParcel", "DomesticParcelBPM", "DomesticPriorityParcel", "DomesticPriorityParcelBPM", "EMIService", "EconomyParcelService", "IPAService", "ISALService", "PMIService", "PriorityParcelDDP", "PriorityParcelDDU", "PriorityParcelDeliveryConfirmationDDP", "PriorityParcelDeliveryConfirmationDDU", "ePacketService"], "Spee-Dee": ["SpeeDeeDelivery"], "SprintShip": ["RoutedDeliveries", "OnDemand", "WhiteGloveDeliveries"], "TForce": ["SameDay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NextDay", "NextDayWhiteGlove"], "Uber RUSH": ["Scheduled", "OnDemand"], "UDS": ["DeliveryService"], "UPS": ["Ground", "UPSStandard", "UPSSaver", "Express", "ExpressPlus", "Expedited", "NextDayAir", "NextDayAirSaver", "NextDayAirEarlyAM", "2ndDayAir", "2ndDayAirAM", "3DaySelect"], "UPS Mail Innovations": ["First", "Priority", "ExpeditedMailInnovations", "PriorityMailInnovations", "EconomyMailInnovations"], "USPS": ["First", "Priority", "Express", "ParcelSelect", "LibraryMail", "MediaMail", "FirstClassMailInternational", "FirstClassPackageInternationalService", "PriorityMailInternational", "ExpressMailInternational"]}