<?xml version="1.0" encoding="utf-8"?>
<definitions xmlns:ns="http://fedex.com/ws/rate/v31" xmlns:s1="http://schemas.xmlsoap.org/wsdl/soap/" name="RateServiceDefinitions" targetNamespace="http://fedex.com/ws/rate/v31" xmlns="http://schemas.xmlsoap.org/wsdl/">
  <types>
    <xs:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://fedex.com/ws/rate/v31" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="RateReply" type="ns:RateReply" />
      <xs:element name="RateRequest" type="ns:RateRequest" />
      <xs:complexType name="AdditionalLabelsDetail">
        <xs:annotation>
          <xs:documentation>Specifies additional labels to be produced. All required labels for shipments will be produced without the need to request additional labels. These are only available as thermal labels.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:AdditionalLabelsType">
            <xs:annotation>
              <xs:documentation>The type of additional labels to return.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Count" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>The number of this type label to return</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="AdditionalLabelsType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER" />
          <xs:enumeration value="CONSIGNEE" />
          <xs:enumeration value="CUSTOMS" />
          <xs:enumeration value="DESTINATION" />
          <xs:enumeration value="MANIFEST" />
          <xs:enumeration value="ORIGIN" />
          <xs:enumeration value="RECIPIENT" />
          <xs:enumeration value="SHIPPER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Address">
        <xs:annotation>
          <xs:documentation>Descriptive data for a physical location. May be used as an actual physical address (place to which one could go), or as a container of "address parts" which should be handled as a unit (such as a city-state-ZIP combination within the US).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="StreetLines" type="xs:string">
            <xs:annotation>
              <xs:documentation>Combination of number, street name, etc. At least one line is required for a valid physical address; empty lines should not be included.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="City" type="xs:string">
            <xs:annotation>
              <xs:documentation>Name of city, town, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="StateOrProvinceCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifying abbreviation for US state, Canada province, etc. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PostalCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identification of a region (usually small) for mail/package delivery. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="UrbanizationCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>Relevant only to addresses in Puerto Rico.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CountryCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>The two-letter code used to identify a country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CountryName" type="xs:string">
            <xs:annotation>
              <xs:documentation>The fully spelt out name of a country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Residential" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>Indicates whether this address residential (as opposed to commercial).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="GeographicCoordinates" type="xs:string">
            <xs:annotation>
              <xs:documentation>The geographic coordinates cooresponding to this address.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="AlcoholDetail">
        <xs:annotation>
          <xs:documentation>Specifies details for a package containing alcohol</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="RecipientType" type="ns:AlcoholRecipientType">
            <xs:annotation>
              <xs:documentation>The license type that the recipient of the alcohol package.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="AlcoholRecipientType">
        <xs:annotation>
          <xs:documentation>Specifies the type of license that the recipient of the alcohol shipment has.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSUMER" />
          <xs:enumeration value="LICENSEE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="AncillaryFeeAndTax">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:AncillaryFeeAndTaxType" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="AncillaryFeeAndTaxType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CLEARANCE_ENTRY_FEE" />
          <xs:enumeration value="GOODS_AND_SERVICES_TAX" />
          <xs:enumeration value="HARMONIZED_SALES_TAX" />
          <xs:enumeration value="OTHER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="AssociatedFreightLineItemDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string">
            <xs:annotation>
              <xs:documentation>A freight line item identifier referring to a freight shipment line item that describes goods contained within this handling unit.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="B13AFilingOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_TO_STAMP" />
          <xs:enumeration value="FILED_ELECTRONICALLY" />
          <xs:enumeration value="MANUALLY_ATTACHED" />
          <xs:enumeration value="NOT_REQUIRED" />
          <xs:enumeration value="SUMMARY_REPORTING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="BarcodeSymbologyType">
        <xs:annotation>
          <xs:documentation>Identification of the type of barcode (symbology) used on FedEx documents and labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CODABAR" />
          <xs:enumeration value="CODE128" />
          <xs:enumeration value="CODE128B" />
          <xs:enumeration value="CODE128C" />
          <xs:enumeration value="CODE128_WIDEBAR" />
          <xs:enumeration value="CODE39" />
          <xs:enumeration value="CODE93" />
          <xs:enumeration value="I2OF5" />
          <xs:enumeration value="MANUAL" />
          <xs:enumeration value="PDF417" />
          <xs:enumeration value="POSTNET" />
          <xs:enumeration value="QR_CODE" />
          <xs:enumeration value="UCC128" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="BatteryClassificationDetail">
        <xs:annotation>
          <xs:documentation>Describes attributes of a battery or cell that are used for classification purposes. Typically this structure would be used to allow customers to declare batteries or cells for which full dangerous goods documentation and procedures are not required.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Material" type="ns:BatteryMaterialType">
            <xs:annotation>
              <xs:documentation>Describes the material composition of the battery or cell.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Packing" type="ns:BatteryPackingType">
            <xs:annotation>
              <xs:documentation>Describes the packing arrangement of the battery or cell with respect to other items within the same package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RegulatorySubType" type="ns:BatteryRegulatorySubType">
            <xs:annotation>
              <xs:documentation>A regulation specific classification for the battery or cell.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="BatteryMaterialType">
        <xs:annotation>
          <xs:documentation>Describes the material composition of a battery or cell.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LITHIUM_ION" />
          <xs:enumeration value="LITHIUM_METAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="BatteryPackingType">
        <xs:annotation>
          <xs:documentation>Describes the packing arrangement of a battery or cell with respect to other items within the same package.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONTAINED_IN_EQUIPMENT" />
          <xs:enumeration value="PACKED_WITH_EQUIPMENT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="BatteryRegulatorySubType">
        <xs:annotation>
          <xs:documentation>A regulation specific classification for a battery or cell.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="IATA_SECTION_II" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="BrokerDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:BrokerType" />
          <xs:element minOccurs="0" name="Broker" type="ns:Party" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="BrokerType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPORT" />
          <xs:enumeration value="IMPORT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="CarrierCodeType">
        <xs:annotation>
          <xs:documentation>Identification of a FedEx operating company (transportation).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FDXC" />
          <xs:enumeration value="FDXE" />
          <xs:enumeration value="FDXG" />
          <xs:enumeration value="FDXO" />
          <xs:enumeration value="FXCC" />
          <xs:enumeration value="FXFR" />
          <xs:enumeration value="FXSP" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CertificateOfOriginDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Certificate of Origin ( e.g. whether or not to include the instructions, image type, etc ...)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="DocumentFormat" type="ns:ShippingDocumentFormat">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ChargeBasisLevelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CURRENT_PACKAGE" />
          <xs:enumeration value="SUM_OF_PACKAGES" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CleansedAddressAndLocationDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="CountryCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>This represents the internal FedEx-system recognized country code.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="StateOrProvinceCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>This represents the internal FedEx-system recognized state or province code.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PostalCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>This represents the internal FedEx-system recognized postal code.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ServiceArea" type="xs:string" />
          <xs:element minOccurs="0" name="LocationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>The unique location identifier</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocationNumber" type="xs:int">
            <xs:annotation>
              <xs:documentation>The op-co specific numeric identifier for a location</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AirportId" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ClearanceBrokerageType">
        <xs:annotation>
          <xs:documentation>Specifies the type of brokerage to be applied to a shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER_INCLUSIVE" />
          <xs:enumeration value="BROKER_INCLUSIVE_NON_RESIDENT_IMPORTER" />
          <xs:enumeration value="BROKER_SELECT" />
          <xs:enumeration value="BROKER_SELECT_NON_RESIDENT_IMPORTER" />
          <xs:enumeration value="BROKER_UNASSIGNED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ClientDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data for the client submitting a transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="1" name="AccountNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>The FedEx account number associated with this transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" name="MeterNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>This number is assigned by FedEx and identifies the unique device from which the request is originating</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="IntegratorId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Only used in transactions which require identification of the FedEx Office integrator.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Region" type="ns:ExpressRegionCode">
            <xs:annotation>
              <xs:documentation>Indicates the region from which the transaction is submitted.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Localization" type="ns:Localization">
            <xs:annotation>
              <xs:documentation>The language to be used for human-readable Notification.localizedMessages in responses to the request containing this ClientDetail object. Different requests from the same client may contain different Localization data. (Contrast with TransactionDetail.localization, which governs data payload language/translation.)</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodAddTransportationChargeBasisType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COD_SURCHARGE" />
          <xs:enumeration value="NET_CHARGE" />
          <xs:enumeration value="NET_FREIGHT" />
          <xs:enumeration value="TOTAL_CUSTOMER_CHARGE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CodAddTransportationChargesDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="RateTypeBasis" type="ns:RateTypeBasisType">
            <xs:annotation>
              <xs:documentation>Select the type of rate from which the element is to be selected.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ChargeBasis" type="ns:CodAddTransportationChargeBasisType" />
          <xs:element minOccurs="0" name="ChargeBasisLevel" type="ns:ChargeBasisLevelType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodCollectionType">
        <xs:annotation>
          <xs:documentation>Identifies the type of funds FedEx should collect upon shipment delivery.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ANY" />
          <xs:enumeration value="CASH" />
          <xs:enumeration value="GUARANTEED_FUNDS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CodDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data required for a FedEx COD (Collect-On-Delivery) shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="CodCollectionAmount" type="ns:Money" />
          <xs:element minOccurs="0" name="AddTransportationChargesDetail" type="ns:CodAddTransportationChargesDetail">
            <xs:annotation>
              <xs:documentation>Specifies the details of the charges are to be added to the COD collect amount.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CollectionType" type="ns:CodCollectionType">
            <xs:annotation>
              <xs:documentation>Identifies the type of funds FedEx should collect upon package delivery</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CodRecipient" type="ns:Party">
            <xs:annotation>
              <xs:documentation>For Express this is the descriptive data that is used for the recipient of the FedEx Letter containing the COD payment. For Ground this is the descriptive data for the party to receive the payment that prints the COD receipt.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FinancialInstitutionContactAndAddress" type="ns:ContactAndAddress">
            <xs:annotation>
              <xs:documentation>When the FedEx COD payment type is not CASH, indicates the contact and address of the financial institution used to service the payment of the COD.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RemitToName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies the name of person or company receiving the secured/unsecured funds payment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ReferenceIndicator" type="ns:CodReturnReferenceIndicatorType">
            <xs:annotation>
              <xs:documentation>Indicates which type of reference information to include on the COD return shipping label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ReturnTrackingId" type="ns:TrackingId">
            <xs:annotation>
              <xs:documentation>Only used with multi-piece COD shipments sent in multiple transactions. Required on last transaction only.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodReturnReferenceIndicatorType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="INVOICE" />
          <xs:enumeration value="PO" />
          <xs:enumeration value="REFERENCE" />
          <xs:enumeration value="TRACKING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CommercialInvoice">
        <xs:annotation>
          <xs:documentation>CommercialInvoice element is required for electronic upload of CI data. It will serve to create/transmit an Electronic Commercial Invoice through the FedEx Systems. Customers are responsible for printing their own Commercial Invoice.If you would likeFedEx to generate a Commercial Invoice and transmit it to Customs. for clearance purposes, you need to specify that in the ShippingDocumentSpecification element. If you would like a copy of the Commercial Invoice that FedEx generated returned to you in reply it needs to be specified in the ETDDetail/RequestedDocumentCopies element. Commercial Invoice support consists of maximum of 99 commodity line items.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Comments" type="xs:string">
            <xs:annotation>
              <xs:documentation>Any comments that need to be communicated about this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Any freight charges that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TaxesOrMiscellaneousCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Any taxes or miscellaneous charges(other than Freight charges or Insurance charges) that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TaxesOrMiscellaneousChargeType" type="ns:TaxesOrMiscellaneousChargeType">
            <xs:annotation>
              <xs:documentation>Specifies which kind of charge is being recorded in the preceding field.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PackingCosts" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Any packing costs that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="HandlingCosts" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Any handling costs that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SpecialInstructions" type="xs:string">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DeclarationStatement" type="xs:string">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PaymentTerms" type="xs:string">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Purpose" type="ns:PurposeOfShipmentType">
            <xs:annotation>
              <xs:documentation>The reason for the shipment. Note: SOLD is not a valid purpose for a Proforma Invoice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="OriginatorName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Name of the International Expert that completed the Commercial Invoice different from Sender.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TermsOfSale" type="xs:string">
            <xs:annotation>
              <xs:documentation>Required for dutiable international Express or Ground shipments. This field is not applicable to an international PIB(document) or a non-document which does not require a Commercial Invoice.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CommercialInvoiceDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Commercial Invoice( e.g. image type) Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of a customer supplied image to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CommitDetail">
        <xs:annotation>
          <xs:documentation>Information about the transit time and delivery commitment date and time.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="CommodityName" type="xs:string">
            <xs:annotation>
              <xs:documentation>The Commodity applicable to this commitment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ServiceType" type="xs:string" />
          <xs:element minOccurs="0" name="ServiceDescription" type="ns:ServiceDescription">
            <xs:annotation>
              <xs:documentation>Descriptions and alternate identifiers for a service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AppliedOptions" type="ns:ServiceOptionType">
            <xs:annotation>
              <xs:documentation>Shows the specific combination of service options combined with the service type that produced this committment in the set returned to the caller.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AppliedSubOptions" type="ns:ServiceSubOptionDetail">
            <xs:annotation>
              <xs:documentation>Supporting detail for applied options identified in preceding field.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DerivedShipmentSignatureOption" type="ns:SignatureOptionDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DerivedPackageSignatureOptions" type="ns:SignatureOptionDetail" />
          <xs:element minOccurs="0" name="DerivedOriginDetail" type="ns:CleansedAddressAndLocationDetail" />
          <xs:element minOccurs="0" name="DerivedDestinationDetail" type="ns:CleansedAddressAndLocationDetail" />
          <xs:element minOccurs="0" name="CommitTimestamp" type="xs:dateTime">
            <xs:annotation>
              <xs:documentation>THe delivery commitment date/time. Express Only.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DayOfWeek" type="ns:DayOfWeekType">
            <xs:annotation>
              <xs:documentation>The delivery commitment day of the week.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TransitTime" type="ns:TransitTimeType">
            <xs:annotation>
              <xs:documentation>The number of transit days; applies to Ground and LTL Freight; indicates minimum transit time for SmartPost.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="MaximumTransitTime" type="ns:TransitTimeType">
            <xs:annotation>
              <xs:documentation>Maximum number of transit days, for SmartPost shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DestinationServiceArea" type="xs:string">
            <xs:annotation>
              <xs:documentation>The service area code for the destination of this shipment. Express only.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BrokerAddress" type="ns:Address">
            <xs:annotation>
              <xs:documentation>The address of the broker to be used for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BrokerLocationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>The FedEx location identifier for the broker.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BrokerCommitTimestamp" type="xs:dateTime">
            <xs:annotation>
              <xs:documentation>The delivery commitment date/time the shipment will arrive at the border.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BrokerCommitDayOfWeek" type="ns:DayOfWeekType">
            <xs:annotation>
              <xs:documentation>The delivery commitment day of the week the shipment will arrive at the border.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BrokerToDestinationDays" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>The number of days it will take for the shipment to make it from broker to destination</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ProofOfDeliveryDate" type="xs:date">
            <xs:annotation>
              <xs:documentation>The delivery commitment date for shipment served by GSP (Global Service Provider)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ProofOfDeliveryDayOfWeek" type="ns:DayOfWeekType">
            <xs:annotation>
              <xs:documentation>The delivery commitment day of the week for the shipment served by GSP (Global Service Provider)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CommitMessages" type="ns:Notification">
            <xs:annotation>
              <xs:documentation>Messages concerning the ability to provide an accurate delivery commitment on an International commit quote. These could be messages providing information about why a commitment could not be returned or a successful message such as "REQUEST COMPLETED"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DeliveryMessages" type="xs:string">
            <xs:annotation>
              <xs:documentation>Messages concerning the delivery commitment on an International commit quote such as "0:00 A.M. IF NO CUSTOMS DELAY"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DelayDetails" type="ns:DelayDetail">
            <xs:annotation>
              <xs:documentation>Information about why a shipment delivery is delayed and at what level (country/service etc.).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DocumentContent" type="ns:InternationalDocumentContentType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RequiredDocuments" type="ns:RequiredShippingDocumentType">
            <xs:annotation>
              <xs:documentation>Required documentation for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightCommitDetail" type="ns:FreightCommitDetail">
            <xs:annotation>
              <xs:documentation>Freight origin and destination city center information and total distance between origin and destination city centers.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CommitmentDelayType">
        <xs:annotation>
          <xs:documentation>The type of delay this shipment will encounter.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="HOLIDAY" />
          <xs:enumeration value="NON_WORKDAY" />
          <xs:enumeration value="NO_CITY_DELIVERY" />
          <xs:enumeration value="NO_HOLD_AT_LOCATION" />
          <xs:enumeration value="NO_LOCATION_DELIVERY" />
          <xs:enumeration value="NO_SERVICE_AREA_DELIVERY" />
          <xs:enumeration value="NO_SERVICE_AREA_SPECIAL_SERVICE_DELIVERY" />
          <xs:enumeration value="NO_SPECIAL_SERVICE_DELIVERY" />
          <xs:enumeration value="NO_ZIP_DELIVERY" />
          <xs:enumeration value="WEEKEND" />
          <xs:enumeration value="WEEKEND_SPECIAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Commodity">
        <xs:sequence>
          <xs:element minOccurs="0" name="Name" type="xs:string">
            <xs:annotation>
              <xs:documentation>FedEx internal commodity identifier</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NumberOfPieces" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>A free-form description of the commodity, which could be used for customs clearance documentation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Purpose" type="ns:CommodityPurposeType" />
          <xs:element minOccurs="0" name="CountryOfManufacture" type="xs:string" />
          <xs:element minOccurs="0" name="HarmonizedCode" type="xs:string" />
          <xs:element minOccurs="0" name="Weight" type="ns:Weight" />
          <xs:element minOccurs="0" name="Quantity" type="xs:decimal" />
          <xs:element minOccurs="0" name="QuantityUnits" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AdditionalMeasures" type="ns:Measure">
            <xs:annotation>
              <xs:documentation>Contains only additional quantitative information other than weight and quantity to calculate duties and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="UnitPrice" type="ns:Money" />
          <xs:element minOccurs="0" name="CustomsValue" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The value of the commodity for customs purposes. The field should be the unit price multiplied by the quantity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ExciseConditions" type="ns:EdtExciseCondition">
            <xs:annotation>
              <xs:documentation>Defines additional characteristic of commodity used to calculate duties and taxes</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ExportLicenseNumber" type="xs:string" />
          <xs:element minOccurs="0" name="ExportLicenseExpirationDate" type="xs:date" />
          <xs:element minOccurs="0" name="CIMarksAndNumbers" type="xs:string" />
          <xs:element minOccurs="0" name="PartNumber" type="xs:string" />
          <xs:element minOccurs="0" name="UsmcaDetail" type="ns:UsmcaCommodityDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CommodityPurposeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS" />
          <xs:enumeration value="CONSUMER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ConfigurableLabelReferenceEntry">
        <xs:sequence>
          <xs:element minOccurs="0" name="ZoneNumber" type="xs:positiveInteger" />
          <xs:element minOccurs="0" name="Header" type="xs:string" />
          <xs:element minOccurs="0" name="DataField" type="xs:string" />
          <xs:element minOccurs="0" name="LiteralValue" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ConsolidationKey">
        <xs:annotation>
          <xs:documentation>Uniquely identifies a consolidation, which is a logical container for a collection of shipments.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:ConsolidationType">
            <xs:annotation>
              <xs:documentation>Specifies the type of consolidation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Index" type="xs:string">
            <xs:annotation>
              <xs:documentation>Uniquely identifies the consolidation, within a given type and date.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Date" type="xs:date">
            <xs:annotation>
              <xs:documentation>The date on which the consolidation was created.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ConsolidationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="INTERNATIONAL_DISTRIBUTION_FREIGHT" />
          <xs:enumeration value="INTERNATIONAL_ECONOMY_DISTRIBUTION" />
          <xs:enumeration value="INTERNATIONAL_GROUND_DISTRIBUTION" />
          <xs:enumeration value="INTERNATIONAL_PRIORITY_DISTRIBUTION" />
          <xs:enumeration value="TRANSBORDER_DISTRIBUTION" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Contact">
        <xs:annotation>
          <xs:documentation>The descriptive data for a point-of-contact person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="ContactId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Client provided identifier corresponding to this contact information.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PersonName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's name.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Title" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's title.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CompanyName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the company this contact is associated with.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PhoneNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the phone number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PhoneExtension" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the phone extension associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TollFreePhoneNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies a toll free number, if any, associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PagerNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the pager number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FaxNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the fax number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EMailAddress" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the email address associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContactAndAddress">
        <xs:sequence>
          <xs:element minOccurs="0" name="Contact" type="ns:Contact" />
          <xs:element minOccurs="0" name="Address" type="ns:Address" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContentRecord">
        <xs:annotation>
          <xs:documentation>Details the contents of the package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="PartNumber" type="xs:string" />
          <xs:element minOccurs="0" name="ItemNumber" type="xs:string" />
          <xs:element minOccurs="0" name="ReceivedQuantity" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CurrencyExchangeRate">
        <xs:sequence>
          <xs:element minOccurs="0" name="FromCurrency" type="xs:string">
            <xs:annotation>
              <xs:documentation>The currency code for the original (converted FROM) currency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="IntoCurrency" type="xs:string">
            <xs:annotation>
              <xs:documentation>The currency code for the final (converted INTO) currency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Rate" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>Multiplier used to convert fromCurrency units to intoCurrency units.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomDeliveryWindowDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:CustomDeliveryWindowType">
            <xs:annotation>
              <xs:documentation>Indicates the type of custom delivery being requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RequestTime" type="xs:time">
            <xs:annotation>
              <xs:documentation>Time by which delivery is requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RequestRange" type="ns:DateRange">
            <xs:annotation>
              <xs:documentation>Range of dates for custom delivery request; only used if type is BETWEEN.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RequestDate" type="xs:date">
            <xs:annotation>
              <xs:documentation>Date for custom delivery request; only used for types of ON, BETWEEN, or AFTER.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomDeliveryWindowType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AFTER" />
          <xs:enumeration value="BEFORE" />
          <xs:enumeration value="BETWEEN" />
          <xs:enumeration value="ON" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomDocumentDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a custom-specified document, either at shipment or package level.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat">
            <xs:annotation>
              <xs:documentation>Common information controlling document production.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LabelPrintingOrientation" type="ns:LabelPrintingOrientationType">
            <xs:annotation>
              <xs:documentation>Applicable only to documents produced on thermal printers with roll stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LabelRotation" type="ns:LabelRotationType">
            <xs:annotation>
              <xs:documentation>Applicable only to documents produced on thermal printers with roll stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SpecificationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the formatting specification used to construct this custom document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomContent" type="ns:CustomLabelDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelBarcodeEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified barcode symbology.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Position" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="Format" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DataFields" type="xs:string" />
          <xs:element minOccurs="0" name="BarHeight" type="xs:int" />
          <xs:element minOccurs="0" name="ThinBarWidth" type="xs:int">
            <xs:annotation>
              <xs:documentation>Width of thinnest bar/space element in the barcode.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BarcodeSymbology" type="ns:BarcodeSymbologyType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelBoxEntry">
        <xs:annotation>
          <xs:documentation>Solid (filled) rectangular area on label.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="TopLeftCorner" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="BottomRightCorner" type="ns:CustomLabelPosition" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomLabelCoordinateUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="MILS" />
          <xs:enumeration value="PIXELS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomLabelDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="CoordinateUnits" type="ns:CustomLabelCoordinateUnits" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="TextEntries" type="ns:CustomLabelTextEntry" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="GraphicEntries" type="ns:CustomLabelGraphicEntry" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="BoxEntries" type="ns:CustomLabelBoxEntry" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="TextBoxEntries" type="ns:CustomLabelTextBoxEntry" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="BarcodeEntries" type="ns:CustomLabelBarcodeEntry" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelGraphicEntry">
        <xs:annotation>
          <xs:documentation>Image to be included from printer's memory, or from a local file for offline clients.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Position" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="PrinterGraphicId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Printer-specific index of graphic image to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FileGraphicFullName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Fully-qualified path and file name for graphic image to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelPosition">
        <xs:sequence>
          <xs:element minOccurs="0" name="X" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Horizontal position, relative to left edge of custom area.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Y" type="xs:int">
            <xs:annotation>
              <xs:documentation>Vertical position, relative to top edge of custom area.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelTextBoxEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified printer font (for thermal labels) or generic font/size (for plain paper labels).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="TopLeftCorner" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="BottomRightCorner" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="Position" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="Format" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DataFields" type="xs:string" />
          <xs:element minOccurs="0" name="ThermalFontId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Printer-specific font name for use with thermal printer labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FontName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Generic font name for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FontSize" type="xs:positiveInteger">
            <xs:annotation>
              <xs:documentation>Generic font size for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Rotation" type="ns:RotationType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelTextEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified printer font (for thermal labels) or generic font/size (for plain paper labels).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Position" type="ns:CustomLabelPosition" />
          <xs:element minOccurs="0" name="Format" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DataFields" type="xs:string" />
          <xs:element minOccurs="0" name="ThermalFontId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Printer-specific font name for use with thermal printer labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FontName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Generic font name for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FontSize" type="xs:positiveInteger">
            <xs:annotation>
              <xs:documentation>Generic font size for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Rotation" type="ns:RotationType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomerImageUsage">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:CustomerImageUsageType" />
          <xs:element minOccurs="0" name="Id" type="ns:ImageId" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerImageUsageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LETTER_HEAD" />
          <xs:enumeration value="SIGNATURE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomerReference">
        <xs:sequence>
          <xs:element minOccurs="0" name="CustomerReferenceType" type="ns:CustomerReferenceType" />
          <xs:element minOccurs="0" name="Value" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerReferenceType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BILL_OF_LADING" />
          <xs:enumeration value="CUSTOMER_REFERENCE" />
          <xs:enumeration value="DEPARTMENT_NUMBER" />
          <xs:enumeration value="ELECTRONIC_PRODUCT_CODE" />
          <xs:enumeration value="INTRACOUNTRY_REGULATORY_REFERENCE" />
          <xs:enumeration value="INVOICE_NUMBER" />
          <xs:enumeration value="PACKING_SLIP_NUMBER" />
          <xs:enumeration value="P_O_NUMBER" />
          <xs:enumeration value="RMA_ASSOCIATION" />
          <xs:enumeration value="SHIPMENT_INTEGRITY" />
          <xs:enumeration value="STORE_NUMBER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomerSpecifiedLabelDetail">
        <xs:annotation>
          <xs:documentation>Allows customer-specified control of label content.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="DocTabContent" type="ns:DocTabContent">
            <xs:annotation>
              <xs:documentation>If omitted, no doc tab will be produced (i.e. default is former NONE type).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomContentPosition" type="ns:RelativeVerticalPositionType">
            <xs:annotation>
              <xs:documentation>Controls the position of the customer specified content relative to the FedEx portion.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomContent" type="ns:CustomLabelDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ConfigurableReferenceEntries" type="ns:ConfigurableLabelReferenceEntry" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="MaskedData" type="ns:LabelMaskableDataType">
            <xs:annotation>
              <xs:documentation>Controls which data/sections will be suppressed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SecondaryBarcode" type="ns:SecondaryBarcodeType">
            <xs:annotation>
              <xs:documentation>For customers producing their own Ground labels, this field specifies which secondary barcode will be printed on the label; so that the primary barcode produced by FedEx has the correct SCNC.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TermsAndConditionsLocalization" type="ns:Localization" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RegulatoryLabels" type="ns:RegulatoryLabelContentDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AdditionalLabels" type="ns:AdditionalLabelsDetail">
            <xs:annotation>
              <xs:documentation>Controls the number of additional copies of supplemental labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AirWaybillSuppressionCount" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>This value reduces the default quantity of destination/consignee air waybill labels. A value of zero indicates no change to default. A minimum of one copy will always be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerSpecifiedLabelGenerationOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONTENT_ON_SHIPPING_LABEL_ONLY" />
          <xs:enumeration value="CONTENT_ON_SHIPPING_LABEL_PREFERRED" />
          <xs:enumeration value="CONTENT_ON_SUPPLEMENTAL_LABEL_ONLY" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomsClearanceDetail">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Brokers" type="ns:BrokerDetail" />
          <xs:element minOccurs="0" name="ClearanceBrokerage" type="ns:ClearanceBrokerageType">
            <xs:annotation>
              <xs:documentation>Interacts both with properties of the shipment and contractual relationship with the shipper.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomsOptions" type="ns:CustomsOptionDetail" />
          <xs:element minOccurs="0" name="ImporterOfRecord" type="ns:Party" />
          <xs:element minOccurs="0" name="RecipientCustomsId" type="ns:RecipientCustomsId">
            <xs:annotation>
              <xs:documentation>Specifies how the recipient is identified for customs purposes; the requirements on this information vary with destination country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DutiesPayment" type="ns:Payment" />
          <xs:element minOccurs="0" name="DocumentContent" type="ns:InternationalDocumentContentType" />
          <xs:element minOccurs="0" name="CustomsValue" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This represents the total commodity value of the shipment plus the charges required by the destination country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightOnValue" type="ns:FreightOnValueType">
            <xs:annotation>
              <xs:documentation>Identifies responsibilities with respect to loss, damage, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="InsuranceCharges" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Documents amount paid to third party for coverage of shipment content.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PartiesToTransactionAreRelated" type="xs:boolean" />
          <xs:element minOccurs="0" name="CommercialInvoice" type="ns:CommercialInvoice" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Commodities" type="ns:Commodity" />
          <xs:element minOccurs="0" name="ExportDetail" type="ns:ExportDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RegulatoryControls" type="ns:RegulatoryControlType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomsOptionDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:CustomsOptionType" />
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies additional description about customs options. This is a required field when the customs options type is "OTHER".</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomsOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COURTESY_RETURN_LABEL" />
          <xs:enumeration value="EXHIBITION_TRADE_SHOW" />
          <xs:enumeration value="FAULTY_ITEM" />
          <xs:enumeration value="FOLLOWING_REPAIR" />
          <xs:enumeration value="FOR_REPAIR" />
          <xs:enumeration value="ITEM_FOR_LOAN" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="REJECTED" />
          <xs:enumeration value="REPLACEMENT" />
          <xs:enumeration value="TRIAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="DangerousGoodsAccessibilityType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCESSIBLE" />
          <xs:enumeration value="INACCESSIBLE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DangerousGoodsContainer">
        <xs:annotation>
          <xs:documentation>Describes an approved container used to package dangerous goods commodities. This does not describe any individual inner receptacles that may be within this container.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="PackingType" type="ns:HazardousContainerPackingType">
            <xs:annotation>
              <xs:documentation>Indicates whether there are additional inner receptacles within this container.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ContainerType" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the type of this dangerous goods container, as specified by the IATA packing instructions. For example, steel cylinder, fiberboard box, plastic jerrican and steel drum.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RadioactiveContainerClass" type="ns:RadioactiveContainerClassType">
            <xs:annotation>
              <xs:documentation>Indicates the packaging type of the container used to package the radioactive materials.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NumberOfContainers" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Indicates the number of occurrences of this container with identical dangerous goods configuration.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="HazardousCommodities" type="ns:HazardousCommodityContent">
            <xs:annotation>
              <xs:documentation>Documents the kinds and quantities of all hazardous commodities in the current container.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DangerousGoodsDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Regulation" type="ns:HazardousCommodityRegulationType" />
          <xs:element minOccurs="0" name="Accessibility" type="ns:DangerousGoodsAccessibilityType" />
          <xs:element minOccurs="0" name="CargoAircraftOnly" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>Shipment is packaged/documented for movement ONLY on cargo aircraft.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Options" type="ns:HazardousCommodityOptionType">
            <xs:annotation>
              <xs:documentation>Indicates which kinds of hazardous content are in the current package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PackingOption" type="ns:DangerousGoodsPackingOptionType">
            <xs:annotation>
              <xs:documentation>Indicates whether there is additional customer provided packaging enclosing the approved dangerous goods containers.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ReferenceId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the configuration of this dangerous goods package. The common configuration is represented at the shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Containers" type="ns:DangerousGoodsContainer">
            <xs:annotation>
              <xs:documentation>Indicates one or more containers used to pack dangerous goods commodities.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Packaging" type="ns:HazardousCommodityPackagingDetail">
            <xs:annotation>
              <xs:documentation>Description of the packaging of this commodity, suitable for use on OP-900 and OP-950 forms.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Signatory" type="ns:DangerousGoodsSignatory">
            <xs:annotation>
              <xs:documentation>Name, title and place of the signatory for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EmergencyContactNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Telephone number to use for contact in the event of an emergency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Offeror" type="xs:string">
            <xs:annotation>
              <xs:documentation>Offeror's name or contract number, per DOT regulation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="InfectiousSubstanceResponsibleContact" type="ns:Contact">
            <xs:annotation>
              <xs:documentation>Specifies the contact of the party responsible for handling the infectious substances, if any, in the dangerous goods shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AdditionalHandling" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies additional handling information for the current package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RadioactivityDetail" type="ns:RadioactivityDetail">
            <xs:annotation>
              <xs:documentation>Specifies the radioactivity detail for the current package, if the package contains radioactive materials.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DangerousGoodsPackingOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="OVERPACK" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DangerousGoodsShippersDeclarationDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the 1421c form for dangerous goods shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DangerousGoodsSignatory">
        <xs:annotation>
          <xs:documentation>Specifies that name, title and place of the signatory responsible for the dangerous goods shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="ContactName" type="xs:string" />
          <xs:element minOccurs="0" name="Title" type="xs:string" />
          <xs:element minOccurs="0" name="Place" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the place where the form is signed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DateRange">
        <xs:sequence>
          <xs:element minOccurs="0" name="Begins" type="xs:date" />
          <xs:element minOccurs="0" name="Ends" type="xs:date" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DayOfWeekType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FRI" />
          <xs:enumeration value="MON" />
          <xs:enumeration value="SAT" />
          <xs:enumeration value="SUN" />
          <xs:enumeration value="THU" />
          <xs:enumeration value="TUE" />
          <xs:enumeration value="WED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DelayDetail">
        <xs:annotation>
          <xs:documentation>Information about why a shipment delivery is delayed and at what level( country/service etc.).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Date" type="xs:date">
            <xs:annotation>
              <xs:documentation>The date of the delay</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DayOfWeek" type="ns:DayOfWeekType" />
          <xs:element minOccurs="0" name="Level" type="ns:DelayLevelType">
            <xs:annotation>
              <xs:documentation>The attribute of the shipment that caused the delay(e.g. Country, City, LocationId, Zip, service area, special handling )</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Point" type="ns:DelayPointType">
            <xs:annotation>
              <xs:documentation>The point where the delay is occurring (e.g. Origin, Destination, Broker location)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Type" type="ns:CommitmentDelayType">
            <xs:annotation>
              <xs:documentation>The reason for the delay (e.g. holiday, weekend, etc.).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>The name of the holiday in that country that is causing the delay.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DelayLevelType">
        <xs:annotation>
          <xs:documentation>The attribute of the shipment that caused the delay(e.g. Country, City, LocationId, Zip, service area, special handling )</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CITY" />
          <xs:enumeration value="COUNTRY" />
          <xs:enumeration value="LOCATION" />
          <xs:enumeration value="POSTAL_CODE" />
          <xs:enumeration value="SERVICE_AREA" />
          <xs:enumeration value="SERVICE_AREA_SPECIAL_SERVICE" />
          <xs:enumeration value="SPECIAL_SERVICE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="DelayPointType">
        <xs:annotation>
          <xs:documentation>The point where the delay is occurring ( e.g. Origin, Destination, Broker location).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER" />
          <xs:enumeration value="DESTINATION" />
          <xs:enumeration value="ORIGIN" />
          <xs:enumeration value="ORIGIN_DESTINATION_PAIR" />
          <xs:enumeration value="PROOF_OF_DELIVERY_POINT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DeliveryOnInvoiceAcceptanceDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Recipient" type="ns:Party" />
          <xs:element minOccurs="0" name="TrackingId" type="ns:TrackingId">
            <xs:annotation>
              <xs:documentation>Specifies the tracking id for the return, if preassigned.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DestinationControlDetail">
        <xs:annotation>
          <xs:documentation>Data required to complete the Destionation Control Statement for US exports.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="StatementTypes" type="ns:DestinationControlStatementType" />
          <xs:element minOccurs="0" name="DestinationCountries" type="xs:string">
            <xs:annotation>
              <xs:documentation>Comma-separated list of up to four country codes, required for DEPARTMENT_OF_STATE statement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EndUser" type="xs:string">
            <xs:annotation>
              <xs:documentation>Name of end user, required for DEPARTMENT_OF_STATE statement.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DestinationControlStatementType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="DEPARTMENT_OF_COMMERCE" />
          <xs:enumeration value="DEPARTMENT_OF_STATE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Dimensions">
        <xs:sequence>
          <xs:element minOccurs="0" name="Length" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="Width" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="Height" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="Units" type="ns:LinearUnits" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Distance">
        <xs:annotation>
          <xs:documentation>Driving or other transportation distances, distinct from dimension measurements.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Value" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>Identifies the distance quantity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Units" type="ns:DistanceUnits">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure for the distance value.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DistanceUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="KM" />
          <xs:enumeration value="MI" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabContent">
        <xs:sequence>
          <xs:element minOccurs="0" name="DocTabContentType" type="ns:DocTabContentType" />
          <xs:element minOccurs="0" name="Zone001" type="ns:DocTabContentZone001" />
          <xs:element minOccurs="0" name="Barcoded" type="ns:DocTabContentBarcoded" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DocTabContentBarcoded">
        <xs:sequence>
          <xs:element minOccurs="0" name="Symbology" type="ns:BarcodeSymbologyType" />
          <xs:element minOccurs="0" name="Specification" type="ns:DocTabZoneSpecification" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocTabContentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BARCODED" />
          <xs:enumeration value="CUSTOM" />
          <xs:enumeration value="MINIMUM" />
          <xs:enumeration value="STANDARD" />
          <xs:enumeration value="ZONE001" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabContentZone001">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DocTabZoneSpecifications" type="ns:DocTabZoneSpecification" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocTabZoneJustificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT" />
          <xs:enumeration value="RIGHT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabZoneSpecification">
        <xs:sequence>
          <xs:element minOccurs="0" name="ZoneNumber" type="xs:positiveInteger" />
          <xs:element minOccurs="0" name="Header" type="xs:string" />
          <xs:element minOccurs="0" name="DataField" type="xs:string" />
          <xs:element minOccurs="0" name="LiteralValue" type="xs:string" />
          <xs:element minOccurs="0" name="Justification" type="ns:DocTabZoneJustificationType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocumentFormatOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="SUPPRESS_ADDITIONAL_LANGUAGES" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocumentFormatOptionsRequested">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Options" type="ns:DocumentFormatOptionType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DropoffType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS_SERVICE_CENTER" />
          <xs:enumeration value="DROP_BOX" />
          <xs:enumeration value="REGULAR_PICKUP" />
          <xs:enumeration value="REQUEST_COURIER" />
          <xs:enumeration value="STATION" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EMailDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="EmailAddress" type="xs:string" />
          <xs:element minOccurs="0" name="Name" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies the name associated with the email address.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EMailNotificationRecipientType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="RECIPIENT" />
          <xs:enumeration value="SHIPPER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EPaymentDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string" />
          <xs:element minOccurs="0" name="EPaymentProcessor" type="ns:EPaymentProcessorType" />
          <xs:element minOccurs="0" name="EPaymentMode" type="ns:EPaymentModeType" />
          <xs:element minOccurs="0" name="MaskedCreditCard" type="xs:string" />
          <xs:element minOccurs="0" name="CreditCardExpirationDate" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EPaymentModeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPLE_PAY" />
          <xs:enumeration value="CASH" />
          <xs:enumeration value="CHECK" />
          <xs:enumeration value="CREDIT_CARD" />
          <xs:enumeration value="GOOGLE_PAY" />
          <xs:enumeration value="PAYPAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="EPaymentProcessorType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="RETAIL_SINGLE_POINT_OF_SALE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EdtCommodityTax">
        <xs:sequence>
          <xs:element minOccurs="0" name="HarmonizedCode" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Taxes" type="ns:EdtTaxDetail" />
          <xs:element minOccurs="0" name="Total" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="EdtExciseCondition">
        <xs:sequence>
          <xs:element minOccurs="0" name="Category" type="xs:string" />
          <xs:element minOccurs="0" name="Value" type="xs:string">
            <xs:annotation>
              <xs:documentation>Customer-declared value, with data type and legal values depending on excise condition, used in defining the taxable value of the item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EdtRequestType">
        <xs:annotation>
          <xs:documentation>Specifies the types of Estimated Duties and Taxes to be included in a rate quotation for an international shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALL" />
          <xs:enumeration value="NONE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EdtTaxDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="TaxType" type="ns:EdtTaxType" />
          <xs:element minOccurs="0" name="EffectiveDate" type="xs:date" />
          <xs:element minOccurs="0" name="Name" type="xs:string" />
          <xs:element minOccurs="0" name="TaxableValue" type="ns:Money" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="Formula" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EdtTaxType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDITIONAL_TAXES" />
          <xs:enumeration value="CONSULAR_INVOICE_FEE" />
          <xs:enumeration value="CUSTOMS_SURCHARGES" />
          <xs:enumeration value="DUTY" />
          <xs:enumeration value="EXCISE_TAX" />
          <xs:enumeration value="FOREIGN_EXCHANGE_TAX" />
          <xs:enumeration value="GENERAL_SALES_TAX" />
          <xs:enumeration value="IMPORT_LICENSE_FEE" />
          <xs:enumeration value="INTERNAL_ADDITIONAL_TAXES" />
          <xs:enumeration value="INTERNAL_SENSITIVE_PRODUCTS_TAX" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="SENSITIVE_PRODUCTS_TAX" />
          <xs:enumeration value="STAMP_TAX" />
          <xs:enumeration value="STATISTICAL_TAX" />
          <xs:enumeration value="TRANSPORT_FACILITIES_TAX" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="EtdAttributeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="POST_SHIPMENT_UPLOAD_REQUESTED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EtdDetail">
        <xs:annotation>
          <xs:documentation>Electronic Trade document references used with the ETD special service.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Attributes" type="ns:EtdAttributeType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RequestedDocumentCopies" type="ns:RequestedShippingDocumentType">
            <xs:annotation>
              <xs:documentation>Indicates the types of shipping documents produced for the shipper by FedEx (see ShippingDocumentSpecification) which should be copied back to the shipper in the shipment result data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DocumentReferences" type="ns:UploadDocumentReferenceDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExportDeclarationDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Export Declaration.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="DocumentFormat" type="ns:ShippingDocumentFormat">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExportDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="B13AFilingOption" type="ns:B13AFilingOptionType" />
          <xs:element minOccurs="0" name="ExportComplianceStatement" type="xs:string">
            <xs:annotation>
              <xs:documentation>General field for exporting-country-specific export data (e.g. B13A for CA, FTSR Exemption or AES Citation for US).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PermitNumber" type="xs:string" />
          <xs:element minOccurs="0" name="DestinationControlDetail" type="ns:DestinationControlDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExpressFreightDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="PackingListEnclosed" type="xs:boolean" />
          <xs:element minOccurs="0" name="ShippersLoadAndCount" type="xs:positiveInteger" />
          <xs:element minOccurs="0" name="BookingConfirmationNumber" type="xs:string" />
          <xs:element minOccurs="0" name="ReferenceLabelRequested" type="xs:boolean" />
          <xs:element minOccurs="0" name="BeforeDeliveryContact" type="ns:ExpressFreightDetailContact" />
          <xs:element minOccurs="0" name="UndeliverableContact" type="ns:ExpressFreightDetailContact" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExpressFreightDetailContact">
        <xs:sequence>
          <xs:element minOccurs="0" name="Name" type="xs:string" />
          <xs:element minOccurs="0" name="Phone" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ExpressRegionCode">
        <xs:annotation>
          <xs:documentation>Indicates a FedEx Express operating region.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APAC" />
          <xs:enumeration value="CA" />
          <xs:enumeration value="EMEA" />
          <xs:enumeration value="LAC" />
          <xs:enumeration value="US" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FedExLocationType">
        <xs:annotation>
          <xs:documentation>Identifies a kind of FedEx facility.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_EXPRESS_STATION" />
          <xs:enumeration value="FEDEX_FACILITY" />
          <xs:enumeration value="FEDEX_FREIGHT_SERVICE_CENTER" />
          <xs:enumeration value="FEDEX_GROUND_TERMINAL" />
          <xs:enumeration value="FEDEX_HOME_DELIVERY_STATION" />
          <xs:enumeration value="FEDEX_OFFICE" />
          <xs:enumeration value="FEDEX_ONSITE" />
          <xs:enumeration value="FEDEX_SHIPSITE" />
          <xs:enumeration value="FEDEX_SHIP_AND_GET" />
          <xs:enumeration value="FEDEX_SMART_POST_HUB" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FlatbedTrailerDetail">
        <xs:annotation>
          <xs:documentation>Specifies the optional features/characteristics requested for a Freight shipment utilizing a flatbed trailer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Options" type="ns:FlatbedTrailerOption" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FlatbedTrailerOption">
        <xs:restriction base="xs:string">
          <xs:enumeration value="OVER_DIMENSION" />
          <xs:enumeration value="TARP" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightAddressLabelDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce the Freight handling-unit-level address labels. Note that the number of UNIQUE labels (the N as in 1 of N, 2 of N, etc.) is determined by total handling units.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat" />
          <xs:element minOccurs="0" name="Copies" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Indicates the number of copies to be produced for each unique label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="StartingPosition" type="ns:PageQuadrantType">
            <xs:annotation>
              <xs:documentation>Specifies the quadrant of the page on which the label printing will start.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DocTabContent" type="ns:DocTabContent">
            <xs:annotation>
              <xs:documentation>If omitted, no doc tab will be produced (i.e. default = former NONE type).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomContentPosition" type="ns:RelativeVerticalPositionType">
            <xs:annotation>
              <xs:documentation>Controls the position of the customer specified content relative to the FedEx portion.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomContent" type="ns:CustomLabelDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightBaseCharge">
        <xs:annotation>
          <xs:documentation>Individual charge which contributes to the total base charge for the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="FreightClass" type="ns:FreightClassType">
            <xs:annotation>
              <xs:documentation>Freight class for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RatedAsClass" type="ns:FreightClassType">
            <xs:annotation>
              <xs:documentation>Effective freight class used for rating this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NmfcCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>NMFC Code for commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>Customer-provided description for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Weight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Weight for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ChargeRate" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Rate or factor applied to this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ChargeBasis" type="ns:FreightChargeBasisType">
            <xs:annotation>
              <xs:documentation>Identifies the manner in which the chargeRate for this line item was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ExtendedAmount" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The net or extended charge for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightBaseChargeCalculationType">
        <xs:annotation>
          <xs:documentation>Specifies the way in which base charges for a Freight shipment or shipment leg are calculated.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LINE_ITEMS" />
          <xs:enumeration value="UNIT_PRICING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightChargeBasisType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CWT" />
          <xs:enumeration value="FLAT" />
          <xs:enumeration value="MINIMUM" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightClassType">
        <xs:annotation>
          <xs:documentation>These values represent the industry-standard freight classes used for FedEx Freight and FedEx National Freight shipment description. (Note: The alphabetic prefixes are required to distinguish these values from decimal numbers on some client platforms.)</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CLASS_050" />
          <xs:enumeration value="CLASS_055" />
          <xs:enumeration value="CLASS_060" />
          <xs:enumeration value="CLASS_065" />
          <xs:enumeration value="CLASS_070" />
          <xs:enumeration value="CLASS_077_5" />
          <xs:enumeration value="CLASS_085" />
          <xs:enumeration value="CLASS_092_5" />
          <xs:enumeration value="CLASS_100" />
          <xs:enumeration value="CLASS_110" />
          <xs:enumeration value="CLASS_125" />
          <xs:enumeration value="CLASS_150" />
          <xs:enumeration value="CLASS_175" />
          <xs:enumeration value="CLASS_200" />
          <xs:enumeration value="CLASS_250" />
          <xs:enumeration value="CLASS_300" />
          <xs:enumeration value="CLASS_400" />
          <xs:enumeration value="CLASS_500" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightCollectTermsType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="NON_RECOURSE_SHIPPER_SIGNED" />
          <xs:enumeration value="STANDARD" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightCommitDetail">
        <xs:annotation>
          <xs:documentation>Information about the Freight Service Centers associated with this shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="OriginDetail" type="ns:FreightServiceCenterDetail">
            <xs:annotation>
              <xs:documentation>Information about the origin Freight Service Center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DestinationDetail" type="ns:FreightServiceCenterDetail">
            <xs:annotation>
              <xs:documentation>Information about the destination Freight Service Center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalDistance" type="ns:Distance">
            <xs:annotation>
              <xs:documentation>The distance between the origin and destination FreightService Centers</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightGuaranteeDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:FreightGuaranteeType" />
          <xs:element minOccurs="0" name="Date" type="xs:date">
            <xs:annotation>
              <xs:documentation>Date for all Freight guarantee types.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightGuaranteeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="GUARANTEED_DATE" />
          <xs:enumeration value="GUARANTEED_MORNING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightOnValueType">
        <xs:annotation>
          <xs:documentation>Identifies responsibilities with respect to loss, damage, etc.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CARRIER_RISK" />
          <xs:enumeration value="OWN_RISK" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightRateDetail">
        <xs:annotation>
          <xs:documentation>Rate data specific to FedEx Freight or FedEx National Freight services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="QuoteNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>A unique identifier for a specific rate quotation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="QuoteType" type="ns:FreightRateQuoteType">
            <xs:annotation>
              <xs:documentation>Specifies whether the rate quote was automated or manual.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BaseChargeCalculation" type="ns:FreightBaseChargeCalculationType">
            <xs:annotation>
              <xs:documentation>Specifies how total base charge is determined.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="BaseCharges" type="ns:FreightBaseCharge">
            <xs:annotation>
              <xs:documentation>Freight charges which accumulate to the total base charge for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Notations" type="ns:FreightRateNotation">
            <xs:annotation>
              <xs:documentation>Human-readable descriptions of additional information on this shipment rating.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightRateNotation">
        <xs:annotation>
          <xs:documentation>Additional non-monetary data returned with Freight rates.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Code" type="xs:string">
            <xs:annotation>
              <xs:documentation>Unique identifier for notation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>Human-readable explanation of notation.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightRateQuoteType">
        <xs:annotation>
          <xs:documentation>Specifies the type of rate quote</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="AUTOMATED" />
          <xs:enumeration value="MANUAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightServiceCenterDetail">
        <xs:annotation>
          <xs:documentation>This class describes the relationship between a customer-specified address and the FedEx Freight / FedEx National Freight Service Center that supports that address.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="InterlineCarrierCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>Freight Industry standard non-FedEx carrier identification</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="InterlineCarrierName" type="xs:string">
            <xs:annotation>
              <xs:documentation>The name of the Interline carrier.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AdditionalDays" type="xs:int">
            <xs:annotation>
              <xs:documentation>Additional time it might take at the origin or destination to pickup or deliver the freight. This is usually due to the remoteness of the location. This time is included in the total transit time.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocalService" type="xs:string" />
          <xs:element minOccurs="0" name="LocalDistance" type="ns:Distance">
            <xs:annotation>
              <xs:documentation>Distance between customer address (pickup or delivery) and the supporting Freight / National Freight service center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocalDuration" type="xs:duration">
            <xs:annotation>
              <xs:documentation>Time to travel between customer address (pickup or delivery) and the supporting Freight / National Freight service center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocalServiceScheduling" type="ns:FreightServiceSchedulingType">
            <xs:annotation>
              <xs:documentation>Specifies when/how the customer can arrange for pickup or delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="LimitedServiceDays" type="ns:DayOfWeekType">
            <xs:annotation>
              <xs:documentation>Specifies days of operation if localServiceScheduling is LIMITED.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="GatewayLocationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Freight service center that is a gateway on the border of Canada or Mexico.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Location" type="xs:string">
            <xs:annotation>
              <xs:documentation>Alphabetical code identifying a Freight Service Center</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ContactAndAddress" type="ns:ContactAndAddress">
            <xs:annotation>
              <xs:documentation>Freight service center Contact and Address</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightServiceSchedulingType">
        <xs:annotation>
          <xs:documentation>Specifies the type of service scheduling offered from a Freight or National Freight Service Center to a customer-supplied address.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LIMITED" />
          <xs:enumeration value="STANDARD" />
          <xs:enumeration value="WILL_CALL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightShipmentDetail">
        <xs:annotation>
          <xs:documentation>Data applicable to shipments using FEDEX_FREIGHT_ECONOMY and FEDEX_FREIGHT_PRIORITY services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="FedExFreightAccountNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Account number used with FEDEX_FREIGHT service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FedExFreightBillingContactAndAddress" type="ns:ContactAndAddress">
            <xs:annotation>
              <xs:documentation>Used for validating FedEx Freight account number and (optionally) identifying third party payment on the bill of lading.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AlternateBilling" type="ns:Party">
            <xs:annotation>
              <xs:documentation>Used in connection with "Send Bill To" (SBT) identification of customer's account used for billing.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Role" type="ns:FreightShipmentRoleType">
            <xs:annotation>
              <xs:documentation>Indicates the role of the party submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CollectTermsType" type="ns:FreightCollectTermsType">
            <xs:annotation>
              <xs:documentation>Designates the terms of the "collect" payment for a Freight Shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DeclaredValuePerUnit" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Identifies the declared value for the shipment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DeclaredValueUnits" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the declared value units corresponding to the above defined declared value</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LiabilityCoverageDetail" type="ns:LiabilityCoverageDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Coupons" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifiers for promotional discounts offered to customers.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalHandlingUnits" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Total number of individual handling units in the entire shipment (for unit pricing).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ClientDiscountPercent" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>Estimated discount rate provided by client for unsecured rate quote.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PalletWeight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Total weight of pallets used in shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ShipmentDimensions" type="ns:Dimensions">
            <xs:annotation>
              <xs:documentation>Overall shipment dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Comment" type="xs:string">
            <xs:annotation>
              <xs:documentation>Description for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SpecialServicePayments" type="ns:FreightSpecialServicePayment">
            <xs:annotation>
              <xs:documentation>Specifies which party will pay surcharges for any special services which support split billing.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="HazardousMaterialsOfferor" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="LineItems" type="ns:FreightShipmentLineItem">
            <xs:annotation>
              <xs:documentation>Details of the commodities in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightShipmentLineItem">
        <xs:annotation>
          <xs:documentation>Description of an individual commodity or class of content in a shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string">
            <xs:annotation>
              <xs:documentation>A unique identifier assigned to this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightClass" type="ns:FreightClassType">
            <xs:annotation>
              <xs:documentation>Freight class for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Packaging" type="ns:PhysicalPackagingType">
            <xs:annotation>
              <xs:documentation>Specification of handling-unit packaging for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Pieces" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Number of pieces for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>Customer-provided description for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Weight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Weight for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Dimensions" type="ns:Dimensions" />
          <xs:element minOccurs="0" name="Volume" type="ns:Volume">
            <xs:annotation>
              <xs:documentation>Volume (cubic measure) for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightShipmentRoleType">
        <xs:annotation>
          <xs:documentation>Indicates the role of the party submitting the transaction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSIGNEE" />
          <xs:enumeration value="SHIPPER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightSpecialServicePayment">
        <xs:annotation>
          <xs:documentation>Specifies which party will be responsible for payment of any surcharges for Freight special services for which split billing is allowed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="SpecialService" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PaymentType" type="ns:FreightShipmentRoleType">
            <xs:annotation>
              <xs:documentation>Indicates who will pay for the special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="GeneralAgencyAgreementDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a General Agency Agreement document. Remaining content (business data) to be defined once requirements have been completed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityContent">
        <xs:annotation>
          <xs:documentation>Documents the kind and quantity of an individual hazardous commodity in a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Description" type="ns:HazardousCommodityDescription">
            <xs:annotation>
              <xs:documentation>Identifies and describes an individual hazardous commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Quantity" type="ns:HazardousCommodityQuantityDetail">
            <xs:annotation>
              <xs:documentation>Specifies the amount of the commodity in alternate units.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="InnerReceptacles" type="ns:HazardousCommodityInnerReceptacleDetail">
            <xs:annotation>
              <xs:documentation>This describes the inner receptacle details for a hazardous commodity within the dangerous goods container.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Options" type="ns:HazardousCommodityOptionDetail">
            <xs:annotation>
              <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RadionuclideDetail" type="ns:RadionuclideDetail">
            <xs:annotation>
              <xs:documentation>Specifies the details of any radio active materials within the commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NetExplosiveDetail" type="ns:NetExplosiveDetail">
            <xs:annotation>
              <xs:documentation>The total mass of the contained explosive substances, without the mass of any casings, bullets, shells, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityDescription">
        <xs:annotation>
          <xs:documentation>Identifies and describes an individual hazardous commodity.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string">
            <xs:annotation>
              <xs:documentation>Regulatory identifier for a commodity (e.g. "UN ID" value).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SequenceNumber" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>In conjunction with the regulatory identifier, this field uniquely identifies a specific hazardous materials commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PackingGroup" type="ns:HazardousCommodityPackingGroupType" />
          <xs:element minOccurs="0" name="PackingDetails" type="ns:HazardousCommodityPackingDetail" />
          <xs:element minOccurs="0" name="ReportableQuantity" type="xs:boolean" />
          <xs:element minOccurs="0" name="ProperShippingName" type="xs:string" />
          <xs:element minOccurs="0" name="TechnicalName" type="xs:string" />
          <xs:element minOccurs="0" name="Percentage" type="xs:decimal" />
          <xs:element minOccurs="0" name="HazardClass" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SubsidiaryClasses" type="xs:string" />
          <xs:element minOccurs="0" name="LabelText" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ProcessingOptions" type="ns:HazardousCommodityDescriptionProcessingOptionType">
            <xs:annotation>
              <xs:documentation>Indicates any special processing options to be applied to the description of the dangerous goods commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Authorization" type="xs:string">
            <xs:annotation>
              <xs:documentation>Information related to quantity limitations and operator or state variations as may be applicable to the dangerous goods commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityDescriptionProcessingOptionType">
        <xs:annotation>
          <xs:documentation>Specifies any special processing to be applied to the dangerous goods commodity description validation.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="INCLUDE_SPECIAL_PROVISIONS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityInnerReceptacleDetail">
        <xs:annotation>
          <xs:documentation>This describes information about the inner receptacles for the hazardous commodity in a particular dangerous goods container.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Quantity" type="ns:HazardousCommodityQuantityDetail">
            <xs:annotation>
              <xs:documentation>This specifies the quantity contained in the inner receptacle.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityLabelTextOptionType">
        <xs:annotation>
          <xs:documentation>Specifies how the commodity is to be labeled.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPEND" />
          <xs:enumeration value="OVERRIDE" />
          <xs:enumeration value="STANDARD" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityOptionDetail">
        <xs:annotation>
          <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="LabelTextOption" type="ns:HazardousCommodityLabelTextOptionType">
            <xs:annotation>
              <xs:documentation>Specifies how the customer wishes the label text to be handled for this commodity in this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomerSuppliedLabelText" type="xs:string">
            <xs:annotation>
              <xs:documentation>Text used in labeling the commodity under control of the labelTextOption field.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityOptionType">
        <xs:annotation>
          <xs:documentation>Indicates which kind of hazardous content is being reported.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BATTERY" />
          <xs:enumeration value="HAZARDOUS_MATERIALS" />
          <xs:enumeration value="LIMITED_QUANTITIES_COMMODITIES" />
          <xs:enumeration value="ORM_D" />
          <xs:enumeration value="REPORTABLE_QUANTITIES" />
          <xs:enumeration value="SMALL_QUANTITY_EXCEPTION" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityPackagingDetail">
        <xs:annotation>
          <xs:documentation>Identifies number and type of packaging units for hazardous commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Count" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Number of units of the type below.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Units" type="xs:string">
            <xs:annotation>
              <xs:documentation>Units in which the hazardous commodity is packaged.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityPackingDetail">
        <xs:annotation>
          <xs:documentation>Specifies documentation and limits for validation of an individual packing group/category.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="CargoAircraftOnly" type="xs:boolean" />
          <xs:element minOccurs="0" name="PackingInstructions" type="xs:string">
            <xs:annotation>
              <xs:documentation>Coded specification for how commodity is to be packed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityPackingGroupType">
        <xs:annotation>
          <xs:documentation>Identifies DOT packing group for a hazardous commodity.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DEFAULT" />
          <xs:enumeration value="I" />
          <xs:enumeration value="II" />
          <xs:enumeration value="III" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityQuantityDetail">
        <xs:annotation>
          <xs:documentation>Identifies amount and units for quantity of hazardous commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Amount" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>Number of units of the type below.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Units" type="xs:string">
            <xs:annotation>
              <xs:documentation>Units by which the hazardous commodity is measured. For IATA commodity, the units values are restricted based on regulation type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="QuantityType" type="ns:HazardousCommodityQuantityType">
            <xs:annotation>
              <xs:documentation>Specifies which measure of quantity is to be validated.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityQuantityType">
        <xs:annotation>
          <xs:documentation>Specifies the measure of quantity to be validated against a prescribed limit.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="GROSS" />
          <xs:enumeration value="NET" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="HazardousCommodityRegulationType">
        <xs:annotation>
          <xs:documentation>Identifies the source of regulation for hazardous commodity data.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADR" />
          <xs:enumeration value="DOT" />
          <xs:enumeration value="IATA" />
          <xs:enumeration value="ORMD" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="HazardousContainerPackingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALL_PACKED_IN_ONE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HoldAtLocationDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="PhoneNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Contact phone number for recipient of shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocationContactAndAddress" type="ns:ContactAndAddress">
            <xs:annotation>
              <xs:documentation>Contact and address of FedEx facility at which shipment is to be held.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocationType" type="ns:FedExLocationType">
            <xs:annotation>
              <xs:documentation>Type of facility at which package/shipment is to be held.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Location identification (for facilities identified by an alphanumeric location code).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocationNumber" type="xs:int">
            <xs:annotation>
              <xs:documentation>Location identification (for facilities identified by an numeric location code).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HomeDeliveryPremiumDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="HomeDeliveryPremiumType" type="ns:HomeDeliveryPremiumType" />
          <xs:element minOccurs="0" name="Date" type="xs:date" />
          <xs:element minOccurs="0" name="PhoneNumber" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HomeDeliveryPremiumType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPOINTMENT" />
          <xs:enumeration value="DATE_CERTAIN" />
          <xs:enumeration value="EVENING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ImageId">
        <xs:restriction base="xs:string">
          <xs:enumeration value="IMAGE_1" />
          <xs:enumeration value="IMAGE_2" />
          <xs:enumeration value="IMAGE_3" />
          <xs:enumeration value="IMAGE_4" />
          <xs:enumeration value="IMAGE_5" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="InternationalControlledExportDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:InternationalControlledExportType" />
          <xs:element minOccurs="0" name="ForeignTradeZoneCode" type="xs:string" />
          <xs:element minOccurs="0" name="EntryNumber" type="xs:string" />
          <xs:element minOccurs="0" name="LicenseOrPermitNumber" type="xs:string" />
          <xs:element minOccurs="0" name="LicenseOrPermitExpirationDate" type="xs:date" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="InternationalControlledExportType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="DEA_036" />
          <xs:enumeration value="DEA_236" />
          <xs:enumeration value="DEA_486" />
          <xs:enumeration value="DSP_05" />
          <xs:enumeration value="DSP_61" />
          <xs:enumeration value="DSP_73" />
          <xs:enumeration value="DSP_85" />
          <xs:enumeration value="DSP_94" />
          <xs:enumeration value="DSP_LICENSE_AGREEMENT" />
          <xs:enumeration value="FROM_FOREIGN_TRADE_ZONE" />
          <xs:enumeration value="WAREHOUSE_WITHDRAWAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="InternationalDocumentContentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="DOCUMENTS_ONLY" />
          <xs:enumeration value="NON_DOCUMENTS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="InternationalTrafficInArmsRegulationsDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="LicenseOrExemptionNumber" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LabelFormatType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMON2D" />
          <xs:enumeration value="LABEL_DATA_ONLY" />
          <xs:enumeration value="MAILROOM" />
          <xs:enumeration value="NO_LABEL" />
          <xs:enumeration value="OPERATIONAL_LABEL" />
          <xs:enumeration value="PRE_COMMON2D" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelMaskableDataType">
        <xs:annotation>
          <xs:documentation>Names for data elements / areas which may be suppressed from printing on labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMS_VALUE" />
          <xs:enumeration value="DIMENSIONS" />
          <xs:enumeration value="DUTIES_AND_TAXES_PAYOR_ACCOUNT_NUMBER" />
          <xs:enumeration value="FREIGHT_PAYOR_ACCOUNT_NUMBER" />
          <xs:enumeration value="PACKAGE_SEQUENCE_AND_COUNT" />
          <xs:enumeration value="SECONDARY_BARCODE" />
          <xs:enumeration value="SHIPPER_ACCOUNT_NUMBER" />
          <xs:enumeration value="SUPPLEMENTAL_LABEL_DOC_TAB" />
          <xs:enumeration value="TERMS_AND_CONDITIONS" />
          <xs:enumeration value="TOTAL_WEIGHT" />
          <xs:enumeration value="TRANSPORTATION_CHARGES_PAYOR_ACCOUNT_NUMBER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelOrderType">
        <xs:annotation>
          <xs:documentation>Specifies the order in which the labels will be returned</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="SHIPPING_LABEL_FIRST" />
          <xs:enumeration value="SHIPPING_LABEL_LAST" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelPrintingOrientationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BOTTOM_EDGE_OF_TEXT_FIRST" />
          <xs:enumeration value="TOP_EDGE_OF_TEXT_FIRST" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelRotationType">
        <xs:annotation>
          <xs:documentation>Relative to normal orientation for the printer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT" />
          <xs:enumeration value="NONE" />
          <xs:enumeration value="RIGHT" />
          <xs:enumeration value="UPSIDE_DOWN" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LabelSpecification">
        <xs:sequence>
          <xs:element minOccurs="0" name="LabelFormatType" type="ns:LabelFormatType" />
          <xs:element minOccurs="0" name="ImageType" type="ns:ShippingDocumentImageType" />
          <xs:element minOccurs="0" name="LabelStockType" type="ns:LabelStockType" />
          <xs:element minOccurs="0" name="LabelPrintingOrientation" type="ns:LabelPrintingOrientationType" />
          <xs:element minOccurs="0" name="LabelRotation" type="ns:LabelRotationType" />
          <xs:element minOccurs="0" name="LabelOrder" type="ns:LabelOrderType">
            <xs:annotation>
              <xs:documentation>Specifies the order in which the labels are requested to be returned</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PrintedLabelOrigin" type="ns:ContactAndAddress" />
          <xs:element minOccurs="0" name="CustomerSpecifiedDetail" type="ns:CustomerSpecifiedLabelDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LabelStockType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PAPER_4X6" />
          <xs:enumeration value="PAPER_4X6.75" />
          <xs:enumeration value="PAPER_4X8" />
          <xs:enumeration value="PAPER_4X9" />
          <xs:enumeration value="PAPER_7X4.75" />
          <xs:enumeration value="PAPER_8.5X11_BOTTOM_HALF_LABEL" />
          <xs:enumeration value="PAPER_8.5X11_TOP_HALF_LABEL" />
          <xs:enumeration value="STOCK_4X6" />
          <xs:enumeration value="STOCK_4X6.75" />
          <xs:enumeration value="STOCK_4X6.75_LEADING_DOC_TAB" />
          <xs:enumeration value="STOCK_4X6.75_TRAILING_DOC_TAB" />
          <xs:enumeration value="STOCK_4X8" />
          <xs:enumeration value="STOCK_4X9" />
          <xs:enumeration value="STOCK_4X9_LEADING_DOC_TAB" />
          <xs:enumeration value="STOCK_4X9_TRAILING_DOC_TAB" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LiabilityCoverageDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="CoverageType" type="ns:LiabilityCoverageType" />
          <xs:element minOccurs="0" name="CoverageAmount" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Identifies the Liability Coverage Amount. For Jan 2010 this value represents coverage amount per pound</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LiabilityCoverageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="NEW" />
          <xs:enumeration value="USED_OR_RECONDITIONED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LinearMeasure">
        <xs:annotation>
          <xs:documentation>Represents a one-dimensional measurement in small units (e.g. suitable for measuring a package or document), contrasted with Distance, which represents a large one-dimensional measurement (e.g. distance between cities).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Value" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>The numerical quantity of this measurement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Units" type="ns:LinearUnits">
            <xs:annotation>
              <xs:documentation>The units for this measurement.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LinearUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CM" />
          <xs:enumeration value="IN" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Localization">
        <xs:annotation>
          <xs:documentation>Identifies the representation of human-readable text.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="1" name="LanguageCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>Two-letter code for language (e.g. EN, FR, etc.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocaleCode" type="xs:string">
            <xs:annotation>
              <xs:documentation>Two-letter code for the region (e.g. us, ca, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Measure">
        <xs:sequence>
          <xs:element minOccurs="0" name="Quantity" type="xs:decimal" />
          <xs:element minOccurs="0" name="Units" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="MinimumChargeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER" />
          <xs:enumeration value="CUSTOMER_FREIGHT_WEIGHT" />
          <xs:enumeration value="EARNED_DISCOUNT" />
          <xs:enumeration value="MIXED" />
          <xs:enumeration value="RATE_SCALE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Money">
        <xs:sequence>
          <xs:element minOccurs="0" name="Currency" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="xs:decimal" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NetExplosiveClassificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="NET_EXPLOSIVE_CONTENT" />
          <xs:enumeration value="NET_EXPLOSIVE_MASS" />
          <xs:enumeration value="NET_EXPLOSIVE_QUANTITY" />
          <xs:enumeration value="NET_EXPLOSIVE_WEIGHT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="NetExplosiveDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:NetExplosiveClassificationType" />
          <xs:element minOccurs="0" name="Amount" type="xs:decimal" />
          <xs:element minOccurs="0" name="Units" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Notification">
        <xs:annotation>
          <xs:documentation>The descriptive data regarding the result of the submitted transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Severity" type="ns:NotificationSeverityType">
            <xs:annotation>
              <xs:documentation>The severity of this notification. This can indicate success or failure or some other information about the request. The values that can be returned are SUCCESS - Your transaction succeeded with no other applicable information. NOTE - Additional information that may be of interest to you about your transaction. WARNING - Additional information that you need to know about your transaction that you may need to take action on. ERROR - Information about an error that occurred while processing your transaction. FAILURE - FedEx was unable to process your transaction at this time due to a system failure. Please try again later</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Source" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the source of this notification. Combined with the Code it uniquely identifies this notification</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Code" type="xs:string">
            <xs:annotation>
              <xs:documentation>A code that represents this notification. Combined with the Source it uniquely identifies this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Message" type="xs:string">
            <xs:annotation>
              <xs:documentation>Human-readable text that explains this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LocalizedMessage" type="xs:string">
            <xs:annotation>
              <xs:documentation>The translated message. The language and locale specified in the ClientDetail. Localization are used to determine the representation. Currently only supported in a TrackReply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="MessageParameters" type="ns:NotificationParameter">
            <xs:annotation>
              <xs:documentation>A collection of name/value pairs that provide specific data to help the client determine the nature of an error (or warning, etc.) without having to parse the message string.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NotificationDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="NotificationType" type="ns:NotificationType">
            <xs:annotation>
              <xs:documentation>Indicates the type of notification that will be sent.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EmailDetail" type="ns:EMailDetail">
            <xs:annotation>
              <xs:documentation>Specifies the email notification details.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Localization" type="ns:Localization">
            <xs:annotation>
              <xs:documentation>Specifies the localization for this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NotificationEventType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ON_DELIVERY" />
          <xs:enumeration value="ON_ESTIMATED_DELIVERY" />
          <xs:enumeration value="ON_EXCEPTION" />
          <xs:enumeration value="ON_PICKUP_DRIVER_ARRIVED" />
          <xs:enumeration value="ON_PICKUP_DRIVER_ASSIGNED" />
          <xs:enumeration value="ON_PICKUP_DRIVER_DEPARTED" />
          <xs:enumeration value="ON_PICKUP_DRIVER_EN_ROUTE" />
          <xs:enumeration value="ON_SHIPMENT" />
          <xs:enumeration value="ON_TENDER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NotificationFormatType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="HTML" />
          <xs:enumeration value="TEXT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="NotificationParameter">
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the type of data contained in Value (e.g. SERVICE_TYPE, PACKAGE_SEQUENCE, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Value" type="xs:string">
            <xs:annotation>
              <xs:documentation>The value of the parameter (e.g. PRIORITY_OVERNIGHT, 2, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NotificationSeverityType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ERROR" />
          <xs:enumeration value="FAILURE" />
          <xs:enumeration value="NOTE" />
          <xs:enumeration value="SUCCESS" />
          <xs:enumeration value="WARNING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NotificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Op900Detail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the OP-900 form for hazardous materials packages.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Reference" type="ns:CustomerReferenceType">
            <xs:annotation>
              <xs:documentation>Identifies which reference type (from the package's customer references) is to be used as the source for the reference on this OP-900.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SignatureName" type="xs:string">
            <xs:annotation>
              <xs:documentation>Data field to be used when a name is to be printed in the document instead of (or in addition to) a signature image.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="OversizeClassType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="OVERSIZE_1" />
          <xs:enumeration value="OVERSIZE_2" />
          <xs:enumeration value="OVERSIZE_3" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PackageRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a package's rates, as calculated per a specific rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="RateType" type="ns:ReturnedRateType">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RatedWeightMethod" type="ns:RatedWeightMethod">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="MinimumChargeType" type="ns:MinimumChargeType">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BillingWeight" type="ns:Weight" />
          <xs:element minOccurs="0" name="DimWeight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>The dimensional weight of this package (if greater than actual).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="OversizeWeight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>The oversize weight of this package (if the package is oversize).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BaseCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The transportation charge only (prior to any discounts applied) for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalFreightDiscounts" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The sum of all discounts on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NetFreight" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This package's baseCharge - totalFreightDiscounts.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalSurcharges" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The sum of all surcharges on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NetFedExCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This package's netFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The sum of all taxes on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="NetCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This package's netFreight + totalSurcharges + totalTaxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalRebates" type="ns:Money" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="FreightDiscounts" type="ns:RateDiscount">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Rebates" type="ns:Rebate">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Surcharges" type="ns:Surcharge">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this package (either because of characteristics of the package itself, or because it is carrying per-shipment surcharges for the shipment of which it is a part).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Taxes" type="ns:Tax">
            <xs:annotation>
              <xs:documentation>All taxes applicable (or distributed to) this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="VariableHandlingCharges" type="ns:VariableHandlingCharges" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PackageSpecialServicesRequested">
        <xs:annotation>
          <xs:documentation>These special services are available at the package level for some or all service types. If the shipper is requesting a special service which requires additional data, the package special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SpecialServiceTypes" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the package special service types that are requested on this shipment. For a list of the valid package special service types, please consult your integration documentation or get the list of the available special services from the getAllSpecialServices method of the Validation Availability and Commitment service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CodDetail" type="ns:CodDetail">
            <xs:annotation>
              <xs:documentation>For use with FedEx Ground services only; COD must be present in shipment's special services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DangerousGoodsDetail" type="ns:DangerousGoodsDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="BatteryDetails" type="ns:BatteryClassificationDetail">
            <xs:annotation>
              <xs:documentation>Provides details about the batteries or cells that are contained within this specific package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DryIceWeight" type="ns:Weight" />
          <xs:element minOccurs="0" name="SignatureOptionDetail" type="ns:SignatureOptionDetail" />
          <xs:element minOccurs="0" name="PriorityAlertDetail" type="ns:PriorityAlertDetail" />
          <xs:element minOccurs="0" name="AlcoholDetail" type="ns:AlcoholDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PageQuadrantType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BOTTOM_LEFT" />
          <xs:enumeration value="BOTTOM_RIGHT" />
          <xs:enumeration value="TOP_LEFT" />
          <xs:enumeration value="TOP_RIGHT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Party">
        <xs:sequence>
          <xs:element minOccurs="0" name="AccountNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Tins" type="ns:TaxpayerIdentification" />
          <xs:element minOccurs="0" name="Contact" type="ns:Contact" />
          <xs:element minOccurs="0" name="Address" type="ns:Address" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Payment">
        <xs:sequence>
          <xs:element minOccurs="0" name="PaymentType" type="ns:PaymentType" />
          <xs:element minOccurs="0" name="Payor" type="ns:Payor" />
          <xs:element minOccurs="0" name="EPaymentDetail" type="ns:EPaymentDetail">
            <xs:annotation>
              <xs:documentation>FOR FEDEX INTERNAL USE ONLY</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PaymentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EPAYMENT" />
          <xs:enumeration value="SENDER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Payor">
        <xs:sequence>
          <xs:element minOccurs="0" name="ResponsibleParty" type="ns:Party" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PendingShipmentDetail">
        <xs:annotation>
          <xs:documentation>This information describes the kind of pending shipment being requested.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:PendingShipmentType" />
          <xs:element minOccurs="0" name="ExpirationDate" type="xs:date">
            <xs:annotation>
              <xs:documentation>Date after which the pending shipment will no longer be available for completion.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ProcessingOptions" type="ns:PendingShipmentProcessingOptionsRequested" />
          <xs:element minOccurs="0" name="RecommendedDocumentSpecification" type="ns:RecommendedDocumentSpecification">
            <xs:annotation>
              <xs:documentation>These are documents that are recommended to be included with the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PendingShipmentProcessingOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALLOW_MODIFICATIONS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PendingShipmentProcessingOptionsRequested">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Options" type="ns:PendingShipmentProcessingOptionType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PendingShipmentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PhysicalFormType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="GAS" />
          <xs:enumeration value="LIQUID" />
          <xs:enumeration value="SOLID" />
          <xs:enumeration value="SPECIAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PhysicalPackagingType">
        <xs:annotation>
          <xs:documentation>This enumeration rationalizes the former FedEx Express international "admissibility package" types (based on ANSI X.12) and the FedEx Freight packaging types. The values represented are those common to both carriers.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BAG" />
          <xs:enumeration value="BARREL" />
          <xs:enumeration value="BASKET" />
          <xs:enumeration value="BOX" />
          <xs:enumeration value="BUCKET" />
          <xs:enumeration value="BUNDLE" />
          <xs:enumeration value="CARTON" />
          <xs:enumeration value="CASE" />
          <xs:enumeration value="CONTAINER" />
          <xs:enumeration value="CRATE" />
          <xs:enumeration value="CYLINDER" />
          <xs:enumeration value="DRUM" />
          <xs:enumeration value="ENVELOPE" />
          <xs:enumeration value="HAMPER" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="PAIL" />
          <xs:enumeration value="PALLET" />
          <xs:enumeration value="PIECE" />
          <xs:enumeration value="REEL" />
          <xs:enumeration value="ROLL" />
          <xs:enumeration value="SKID" />
          <xs:enumeration value="TANK" />
          <xs:enumeration value="TUBE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PickupDetail">
        <xs:annotation>
          <xs:documentation>This class describes the pickup characteristics of a shipment (e.g. for use in a tag request).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="ReadyDateTime" type="xs:dateTime" />
          <xs:element minOccurs="0" name="LatestPickupDateTime" type="xs:dateTime" />
          <xs:element minOccurs="0" name="CourierInstructions" type="xs:string" />
          <xs:element minOccurs="0" name="RequestType" type="ns:PickupRequestType" />
          <xs:element minOccurs="0" name="RequestSource" type="ns:PickupRequestSourceType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PickupRequestSourceType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AUTOMATION" />
          <xs:enumeration value="CUSTOMER_SERVICE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PickupRequestType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FUTURE_DAY" />
          <xs:enumeration value="SAME_DAY" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PricingCodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL" />
          <xs:enumeration value="ALTERNATE" />
          <xs:enumeration value="BASE" />
          <xs:enumeration value="HUNDREDWEIGHT" />
          <xs:enumeration value="HUNDREDWEIGHT_ALTERNATE" />
          <xs:enumeration value="INTERNATIONAL_DISTRIBUTION" />
          <xs:enumeration value="INTERNATIONAL_ECONOMY_SERVICE" />
          <xs:enumeration value="LTL_FREIGHT" />
          <xs:enumeration value="PACKAGE" />
          <xs:enumeration value="SHIPMENT" />
          <xs:enumeration value="SHIPMENT_FIVE_POUND_OPTIONAL" />
          <xs:enumeration value="SHIPMENT_OPTIONAL" />
          <xs:enumeration value="SPECIAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PriorityAlertDetail">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="EnhancementTypes" type="ns:PriorityAlertEnhancementType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Content" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PriorityAlertEnhancementType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PRIORITY_ALERT_PLUS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ProductName">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="xs:string">
            <xs:annotation>
              <xs:documentation>The type of name (long, medium, short, etc.) to which this value refers.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Encoding" type="xs:string">
            <xs:annotation>
              <xs:documentation>The character encoding used to represent this product name. For example, UTF-8.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Value" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PurposeOfShipmentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="GIFT" />
          <xs:enumeration value="NOT_SOLD" />
          <xs:enumeration value="PERSONAL_EFFECTS" />
          <xs:enumeration value="REPAIR_AND_RETURN" />
          <xs:enumeration value="SAMPLE" />
          <xs:enumeration value="SOLD" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RadioactiveContainerClassType">
        <xs:annotation>
          <xs:documentation>Indicates the packaging type of the container used to package radioactive hazardous materials.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXCEPTED_PACKAGE" />
          <xs:enumeration value="INDUSTRIAL_IP1" />
          <xs:enumeration value="INDUSTRIAL_IP2" />
          <xs:enumeration value="INDUSTRIAL_IP3" />
          <xs:enumeration value="TYPE_A" />
          <xs:enumeration value="TYPE_B_M" />
          <xs:enumeration value="TYPE_B_U" />
          <xs:enumeration value="TYPE_C" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RadioactivityDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="TransportIndex" type="xs:decimal" />
          <xs:element minOccurs="0" name="SurfaceReading" type="xs:decimal" />
          <xs:element minOccurs="0" name="CriticalitySafetyIndex" type="xs:decimal" />
          <xs:element minOccurs="0" name="Dimensions" type="ns:Dimensions" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RadioactivityUnitOfMeasure">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BQ" />
          <xs:enumeration value="GBQ" />
          <xs:enumeration value="KBQ" />
          <xs:enumeration value="MBQ" />
          <xs:enumeration value="PBQ" />
          <xs:enumeration value="TBQ" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RadionuclideActivity">
        <xs:sequence>
          <xs:element minOccurs="0" name="Value" type="xs:decimal" />
          <xs:element minOccurs="0" name="UnitOfMeasure" type="ns:RadioactivityUnitOfMeasure" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RadionuclideDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Radionuclide" type="xs:string" />
          <xs:element minOccurs="0" name="Activity" type="ns:RadionuclideActivity" />
          <xs:element minOccurs="0" name="ExceptedPackagingIsReportableQuantity" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>Indicates whether packaging type "EXCEPTED" or "EXCEPTED_PACKAGE" is for radioactive material in reportable quantity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PhysicalForm" type="ns:PhysicalFormType" />
          <xs:element minOccurs="0" name="ChemicalForm" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RateDimensionalDivisorType">
        <xs:annotation>
          <xs:documentation>Indicates the reason that a dim divisor value was chose.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COUNTRY" />
          <xs:enumeration value="CUSTOMER" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="PRODUCT" />
          <xs:enumeration value="WAIVED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RateDiscount">
        <xs:sequence>
          <xs:element minOccurs="0" name="RateDiscountType" type="ns:RateDiscountType" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
          <xs:element minOccurs="0" name="Percent" type="xs:decimal" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RateDiscountType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BONUS" />
          <xs:enumeration value="COUPON" />
          <xs:enumeration value="EARNED" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="VOLUME" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateElementBasisType">
        <xs:annotation>
          <xs:documentation>Selects the value from a set of rate data to which the percentage is applied.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BASE_CHARGE" />
          <xs:enumeration value="NET_CHARGE" />
          <xs:enumeration value="NET_CHARGE_EXCLUDING_TAXES" />
          <xs:enumeration value="NET_FREIGHT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RateReply">
        <xs:annotation>
          <xs:documentation>The response to a RateRequest. The Notifications indicate whether the request was successful or not.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="1" name="HighestSeverity" type="ns:NotificationSeverityType">
            <xs:annotation>
              <xs:documentation>This indicates the highest level of severity of all the notifications returned in this reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" maxOccurs="unbounded" name="Notifications" type="ns:Notification">
            <xs:annotation>
              <xs:documentation>The descriptive data regarding the results of the submitted transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TransactionDetail" type="ns:TransactionDetail">
            <xs:annotation>
              <xs:documentation>Contains the CustomerTransactionId that was sent in the request.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" name="Version" type="ns:VersionId">
            <xs:annotation>
              <xs:documentation>The version of this reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RateReplyDetails" type="ns:RateReplyDetail">
            <xs:annotation>
              <xs:documentation>Each element contains all rate data for a single service. If service was specified in the request, there will be a single entry in this array; if service was omitted in the request, there will be a separate entry in this array for each service being compared.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RateReplyDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="ServiceType" type="xs:string" />
          <xs:element minOccurs="0" name="ServiceDescription" type="ns:ServiceDescription">
            <xs:annotation>
              <xs:documentation>Descriptions and alternate identifiers for a service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PackagingType" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AppliedOptions" type="ns:ServiceOptionType">
            <xs:annotation>
              <xs:documentation>Shows the specific combination of service options combined with the service type that produced this committment in the set returned to the caller.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AppliedSubOptions" type="ns:ServiceSubOptionDetail">
            <xs:annotation>
              <xs:documentation>Supporting detail for applied options identified in preceding field.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DeliveryStation" type="xs:string" />
          <xs:element minOccurs="0" name="DeliveryDayOfWeek" type="ns:DayOfWeekType" />
          <xs:element minOccurs="0" name="DeliveryTimestamp" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CommitDetails" type="ns:CommitDetail" />
          <xs:element minOccurs="0" name="DestinationAirportId" type="xs:string" />
          <xs:element minOccurs="0" name="IneligibleForMoneyBackGuarantee" type="xs:boolean" />
          <xs:element minOccurs="0" name="OriginServiceArea" type="xs:string">
            <xs:annotation>
              <xs:documentation>Not populated by FAST service in Jan07.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DestinationServiceArea" type="xs:string">
            <xs:annotation>
              <xs:documentation>Not populated by FAST service in Jan07.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TransitTime" type="ns:TransitTimeType">
            <xs:annotation>
              <xs:documentation>Not populated by FAST service in Jan07.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="MaximumTransitTime" type="ns:TransitTimeType">
            <xs:annotation>
              <xs:documentation>Maximum expected transit time</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SignatureOption" type="ns:SignatureOptionType">
            <xs:annotation>
              <xs:documentation>Not populated by FAST service in Jan07. Actual signature option applied, to allow for cases in wihch the original value conflicted with other service features in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ActualRateType" type="ns:ReturnedRateType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RatedShipmentDetails" type="ns:RatedShipmentDetail">
            <xs:annotation>
              <xs:documentation>Each element contains all rate data for a single rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RateRequest">
        <xs:sequence>
          <xs:element minOccurs="1" name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" name="ClientDetail" type="ns:ClientDetail" />
          <xs:element minOccurs="0" name="TransactionDetail" type="ns:TransactionDetail" />
          <xs:element minOccurs="1" name="Version" type="ns:VersionId" />
          <xs:element minOccurs="0" name="ReturnTransitAndCommit" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>Allows the caller to specify that the transit time and commit data are to be returned in the reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CarrierCodes" type="ns:CarrierCodeType">
            <xs:annotation>
              <xs:documentation>Candidate carriers for rate-shopping use case. This field is only considered if requestedShipment/serviceType is omitted.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="VariableOptions" type="ns:ServiceOptionType">
            <xs:annotation>
              <xs:documentation>Contains zero or more service options whose combinations are to be considered when replying with available services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ConsolidationKey" type="ns:ConsolidationKey">
            <xs:annotation>
              <xs:documentation>If provided, identifies the consolidation to which this open shipment should be added after successful creation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RequestedShipment" type="ns:RequestedShipment">
            <xs:annotation>
              <xs:documentation>The shipment for which a rate quote (or rate-shopping comparison) is desired.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RateRequestType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LIST" />
          <xs:enumeration value="NONE" />
          <xs:enumeration value="PREFERRED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateTypeBasisType">
        <xs:annotation>
          <xs:documentation>Select the type of rate from which the element is to be selected.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCOUNT" />
          <xs:enumeration value="LIST" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RatedPackageDetail">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="TrackingIds" type="ns:TrackingId">
            <xs:annotation>
              <xs:documentation>Echoed from the corresponding package in the rate request (if provided).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="GroupNumber" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Used with request containing PACKAGE_GROUPS, to identify which group of identical packages was used to produce a reply item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EffectiveNetDiscount" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The difference between "list" and "account" net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AdjustedCodCollectionAmount" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Ground COD is shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="OversizeClass" type="ns:OversizeClassType" />
          <xs:element minOccurs="0" name="PackageRateDetail" type="ns:PackageRateDetail">
            <xs:annotation>
              <xs:documentation>Rate data that are tied to a specific package and rate type combination.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RatedShipmentDetail">
        <xs:annotation>
          <xs:documentation>This class groups the shipment and package rating data for a specific rate type for use in a rating reply, which groups result data by rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="EffectiveNetDiscount" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The difference between "list" and "account" total net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="AdjustedCodCollectionAmount" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Express COD is shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ShipmentRateDetail" type="ns:ShipmentRateDetail">
            <xs:annotation>
              <xs:documentation>The shipment-level totals for this rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RatedPackages" type="ns:RatedPackageDetail">
            <xs:annotation>
              <xs:documentation>The package-level data for this rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RatedWeightMethod">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL" />
          <xs:enumeration value="AVERAGE_PACKAGE_WEIGHT_MINIMUM" />
          <xs:enumeration value="BALLOON" />
          <xs:enumeration value="DEFAULT_WEIGHT_APPLIED" />
          <xs:enumeration value="DIM" />
          <xs:enumeration value="FREIGHT_MINIMUM" />
          <xs:enumeration value="MIXED" />
          <xs:enumeration value="OVERSIZE" />
          <xs:enumeration value="OVERSIZE_1" />
          <xs:enumeration value="OVERSIZE_2" />
          <xs:enumeration value="OVERSIZE_3" />
          <xs:enumeration value="PACKAGING_MINIMUM" />
          <xs:enumeration value="WEIGHT_BREAK" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Rebate">
        <xs:sequence>
          <xs:element minOccurs="0" name="RebateType" type="ns:RebateType" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
          <xs:element minOccurs="0" name="Percent" type="xs:decimal" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RebateType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BONUS" />
          <xs:enumeration value="EARNED" />
          <xs:enumeration value="OTHER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RecipientCustomsId">
        <xs:annotation>
          <xs:documentation>Specifies how the recipient is identified for customs purposes; the requirements on this information vary with destination country.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:RecipientCustomsIdType">
            <xs:annotation>
              <xs:documentation>Specifies the kind of identification being used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Value" type="xs:string">
            <xs:annotation>
              <xs:documentation>Contains the actual ID value, of the type specified above.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RecipientCustomsIdType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMPANY" />
          <xs:enumeration value="INDIVIDUAL" />
          <xs:enumeration value="PASSPORT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RecommendedDocumentSpecification">
        <xs:annotation>
          <xs:documentation>Specifies the details about documents that are recommended to be included with the shipment for ease of shipment processing and transportation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Types" type="ns:RecommendedDocumentType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RecommendedDocumentType">
        <xs:annotation>
          <xs:documentation>Type of documents that are recommended to be included with the shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ANTIQUE_STATEMENT_EUROPEAN_UNION" />
          <xs:enumeration value="ANTIQUE_STATEMENT_UNITED_STATES" />
          <xs:enumeration value="ASSEMBLER_DECLARATION" />
          <xs:enumeration value="BEARING_WORKSHEET" />
          <xs:enumeration value="CERTIFICATE_OF_SHIPMENTS_TO_SYRIA" />
          <xs:enumeration value="COMMERCIAL_INVOICE_FOR_THE_CARIBBEAN_COMMON_MARKET" />
          <xs:enumeration value="CONIFEROUS_SOLID_WOOD_PACKAGING_MATERIAL_TO_THE_PEOPLES_REPUBLIC_OF_CHINA" />
          <xs:enumeration value="DECLARATION_FOR_FREE_ENTRY_OF_RETURNED_AMERICAN_PRODUCTS" />
          <xs:enumeration value="DECLARATION_OF_BIOLOGICAL_STANDARDS" />
          <xs:enumeration value="DECLARATION_OF_IMPORTED_ELECTRONIC_PRODUCTS_SUBJECT_TO_RADIATION_CONTROL_STANDARD" />
          <xs:enumeration value="ELECTRONIC_INTEGRATED_CIRCUIT_WORKSHEET" />
          <xs:enumeration value="FILM_AND_VIDEO_CERTIFICATE" />
          <xs:enumeration value="INTERIM_FOOTWEAR_INVOICE" />
          <xs:enumeration value="PACKING_LIST" />
          <xs:enumeration value="PRINTED_CIRCUIT_BOARD_WORKSHEET" />
          <xs:enumeration value="REPAIRED_WATCH_BREAKOUT_WORKSHEET" />
          <xs:enumeration value="STATEMENT_REGARDING_THE_IMPORT_OF_RADIO_FREQUENCY_DEVICES" />
          <xs:enumeration value="TOXIC_SUBSTANCES_CONTROL_ACT" />
          <xs:enumeration value="UNITED_STATES_CARIBBEAN_BASIN_TRADE_PARTNERSHIP_ACT_CERTIFICATE_OF_ORIGIN_NON_TEXTILES" />
          <xs:enumeration value="UNITED_STATES_CARIBBEAN_BASIN_TRADE_PARTNERSHIP_ACT_CERTIFICATE_OF_ORIGIN_TEXTILES" />
          <xs:enumeration value="UNITED_STATES_NEW_WATCH_WORKSHEET" />
          <xs:enumeration value="UNITED_STATES_WATCH_REPAIR_DECLARATION" />
          <xs:enumeration value="USMCA_CERTIFICATION_OF_ORIGIN_ENGLISH" />
          <xs:enumeration value="USMCA_CERTIFICATION_OF_ORIGIN_FRENCH" />
          <xs:enumeration value="USMCA_CERTIFICATION_OF_ORIGIN_SPANISH" />
          <xs:enumeration value="USMCA_COMMERCIAL_INVOICE_CERTIFICATION_OF_ORIGIN_ENGLISH" />
          <xs:enumeration value="USMCA_COMMERCIAL_INVOICE_CERTIFICATION_OF_ORIGIN_FRENCH" />
          <xs:enumeration value="USMCA_COMMERCIAL_INVOICE_CERTIFICATION_OF_ORIGIN_SPANISH" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RegulatoryControlType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FOOD_OR_PERISHABLE" />
          <xs:enumeration value="NOT_APPLICABLE_FOR_LOW_CUSTOMS_VALUE_EXCEPTION" />
          <xs:enumeration value="NOT_IN_FREE_CIRCULATION" />
          <xs:enumeration value="USMCA" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RegulatoryLabelContentDetail">
        <xs:annotation>
          <xs:documentation>Specifies details needed to generate any label artifacts required due to regulatory requirements.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:RegulatoryLabelType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="GenerationOptions" type="ns:CustomerSpecifiedLabelGenerationOptionType">
            <xs:annotation>
              <xs:documentation>Specifies how the customer requested the regulatory label to be generated.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RegulatoryLabelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALCOHOL_SHIPMENT_LABEL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RelativeVerticalPositionType">
        <xs:annotation>
          <xs:documentation>Describes the vertical position of an item relative to another item.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ABOVE" />
          <xs:enumeration value="BELOW" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RequestedPackageLineItem">
        <xs:sequence>
          <xs:element minOccurs="0" name="SequenceNumber" type="xs:positiveInteger">
            <xs:annotation>
              <xs:documentation>A unique identifier of each requested package line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="GroupNumber" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>An identifier of each group of identical packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="GroupPackageCount" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Used as the number or count of identical packages in a group.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="VariableHandlingChargeDetail" type="ns:VariableHandlingChargeDetail" />
          <xs:element minOccurs="0" name="InsuredValue" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Specifies the declared value for carriage of the package. The declared value for carriage represents the maximum liability of FedEx in connection with a shipment, including, but not limited to, any loss, damage, delay, mis-delivery, nondelivery, misinformation, any failure to provide information, or mis-delivery of information relating to the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Weight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Represents the gross weight of the package line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Dimensions" type="ns:Dimensions" />
          <xs:element minOccurs="0" name="PhysicalPackaging" type="ns:PhysicalPackagingType">
            <xs:annotation>
              <xs:documentation>Provides additional detail on how the customer has physically packaged this item. As of June 2009, required for packages moving under international and SmartPost services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AssociatedFreightLineItems" type="ns:AssociatedFreightLineItemDetail" />
          <xs:element minOccurs="0" name="ItemDescription" type="xs:string">
            <xs:annotation>
              <xs:documentation>Human-readable text describing the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ItemDescriptionForClearance" type="xs:string">
            <xs:annotation>
              <xs:documentation>Human-readable text describing the contents of the package to be used for clearance purposes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="10" name="CustomerReferences" type="ns:CustomerReference" />
          <xs:element minOccurs="0" name="SpecialServicesRequested" type="ns:PackageSpecialServicesRequested" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ContentRecords" type="ns:ContentRecord">
            <xs:annotation>
              <xs:documentation>Details the contents of the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ConveyanceDetail" type="ns:ShipperConveyanceDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RequestedShipment">
        <xs:sequence>
          <xs:element minOccurs="0" name="ShipTimestamp" type="xs:dateTime" />
          <xs:element minOccurs="0" name="DropoffType" type="ns:DropoffType" />
          <xs:element minOccurs="0" name="ServiceType" type="xs:string">
            <xs:annotation>
              <xs:documentation>This field contains the service type values, like PRIORITY_OVERNIGHT and FEDEX_GROUND.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PackagingType" type="xs:string">
            <xs:annotation>
              <xs:documentation>This field contains the packaging type values, like YOUR_PACKAGING and FEDEX_ENVELOPE.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="VariationOptions" type="ns:ShipmentVariationOptionDetail">
            <xs:annotation>
              <xs:documentation>The shipment variations for the current shipment expressed in key-value pairs.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalWeight" type="ns:Weight" />
          <xs:element minOccurs="0" name="TotalInsuredValue" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Specifies the total declared value for carriage of the shipment. The declared value for carriage represents the maximum liability of FedEx in connection with a shipment, including, but not limited to, any loss, damage, delay, mis-delivery, nondelivery, misinformation, any failure to provide information, or mis-delivery of information relating to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PreferredCurrency" type="xs:string">
            <xs:annotation>
              <xs:documentation>This attribute indicates the currency the caller requests to have used in all returned monetary values (when a choice is possible).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ShipmentAuthorizationDetail" type="ns:ShipmentAuthorizationDetail">
            <xs:annotation>
              <xs:documentation>Specifies details about the entity responsible for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Shipper" type="ns:Party" />
          <xs:element minOccurs="0" name="Recipient" type="ns:Party" />
          <xs:element minOccurs="0" name="RecipientLocationNumber" type="xs:string" />
          <xs:element minOccurs="0" name="Origin" type="ns:ContactAndAddress">
            <xs:annotation>
              <xs:documentation>Physical starting address for the shipment, if different from shipper's address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SoldTo" type="ns:Party">
            <xs:annotation>
              <xs:documentation>The sold-to party is used for customs clearance; for example, in support of US import customs rules. The need for this field could vary based on whether a sold-to party was specified on a consolidation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ShippingChargesPayment" type="ns:Payment" />
          <xs:element minOccurs="0" name="SpecialServicesRequested" type="ns:ShipmentSpecialServicesRequested" />
          <xs:element minOccurs="0" name="ExpressFreightDetail" type="ns:ExpressFreightDetail" />
          <xs:element minOccurs="0" name="FreightShipmentDetail" type="ns:FreightShipmentDetail">
            <xs:annotation>
              <xs:documentation>Data applicable to shipments using FEDEX_FREIGHT_ECONOMY and FEDEX_FREIGHT_PRIORITY services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DeliveryInstructions" type="xs:string">
            <xs:annotation>
              <xs:documentation>Used with Ground Home Delivery and Freight.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="VariableHandlingChargeDetail" type="ns:VariableHandlingChargeDetail" />
          <xs:element minOccurs="0" name="CustomsClearanceDetail" type="ns:CustomsClearanceDetail">
            <xs:annotation>
              <xs:documentation>Customs clearance data, used for both international and intra-country shipping.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PickupDetail" type="ns:PickupDetail">
            <xs:annotation>
              <xs:documentation>For use in "process tag" transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SmartPostDetail" type="ns:SmartPostShipmentDetail">
            <xs:annotation>
              <xs:documentation>Specifies the characteristics of a shipment pertaining to SmartPost services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="BlockInsightVisibility" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>If true, only the shipper/payor will have visibility of this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LabelSpecification" type="ns:LabelSpecification" />
          <xs:element minOccurs="0" name="ShippingDocumentSpecification" type="ns:ShippingDocumentSpecification">
            <xs:annotation>
              <xs:documentation>Contains data used to create additional (non-label) shipping documents.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RateRequestTypes" type="ns:RateRequestType">
            <xs:annotation>
              <xs:documentation>Specifies whether and what kind of rates the customer wishes to have quoted on this shipment. The reply will also be constrained by other data on the shipment and customer.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EdtRequestType" type="ns:EdtRequestType">
            <xs:annotation>
              <xs:documentation>Specifies whether the customer wishes to have Estimated Duties and Taxes provided with the rate quotation on this shipment. Only applies with shipments moving under international services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PackageCount" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>The total number of packages in the entire shipment (even when the shipment spans multiple transactions.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ShipmentOnlyFields" type="ns:ShipmentOnlyFieldsType">
            <xs:annotation>
              <xs:documentation>Specifies which package-level data values are provided at the shipment-level only. The package-level data values types specified here will not be provided at the package-level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ConfigurationData" type="ns:ShipmentConfigurationData">
            <xs:annotation>
              <xs:documentation>Specifies data structures that may be re-used multiple times with s single shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="RequestedPackageLineItems" type="ns:RequestedPackageLineItem">
            <xs:annotation>
              <xs:documentation>One or more package-attribute descriptions, each of which describes an individual package, a group of identical packages, or (for the total-piece-total-weight case) common characteristics all packages in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RequestedShippingDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN" />
          <xs:enumeration value="COMMERCIAL_INVOICE" />
          <xs:enumeration value="CUSTOMER_SPECIFIED_LABELS" />
          <xs:enumeration value="DANGEROUS_GOODS_SHIPPERS_DECLARATION" />
          <xs:enumeration value="EXPORT_DECLARATION" />
          <xs:enumeration value="FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING" />
          <xs:enumeration value="GENERAL_AGENCY_AGREEMENT" />
          <xs:enumeration value="LABEL" />
          <xs:enumeration value="PRO_FORMA_INVOICE" />
          <xs:enumeration value="RETURN_INSTRUCTIONS" />
          <xs:enumeration value="USMCA_CERTIFICATION_OF_ORIGIN" />
          <xs:enumeration value="USMCA_COMMERCIAL_INVOICE_CERTIFICATION_OF_ORIGIN" />
          <xs:enumeration value="VICS_BILL_OF_LADING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RequiredShippingDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CANADIAN_B13A" />
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN" />
          <xs:enumeration value="COMMERCIAL_INVOICE" />
          <xs:enumeration value="INTERNATIONAL_AIRWAY_BILL" />
          <xs:enumeration value="MAIL_SERVICE_AIRWAY_BILL" />
          <xs:enumeration value="SHIPPERS_EXPORT_DECLARATION" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ReturnAssociationDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="TrackingNumber" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies the tracking number of the master associated with the return shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ShipDate" type="xs:date" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ReturnEMailAllowedSpecialServiceType">
        <xs:annotation>
          <xs:documentation>These values are used to control the availability of certain special services at the time when a customer uses the e-mail label link to create a return shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="SATURDAY_DELIVERY" />
          <xs:enumeration value="SATURDAY_PICKUP" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ReturnEMailDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="MerchantPhoneNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AllowedSpecialServices" type="ns:ReturnEMailAllowedSpecialServiceType">
            <xs:annotation>
              <xs:documentation>Identifies the allowed (merchant-authorized) special services which may be selected when the subsequent shipment is created. Only services represented in EMailLabelAllowedSpecialServiceType will be controlled by this list.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ReturnInstructionsDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the return instructions( e.g. image type) Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat" />
          <xs:element minOccurs="0" name="CustomText" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies additional customer provided text to be inserted into the return document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ReturnShipmentDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="ReturnType" type="ns:ReturnType" />
          <xs:element minOccurs="0" name="Rma" type="ns:Rma" />
          <xs:element minOccurs="0" name="ReturnEMailDetail" type="ns:ReturnEMailDetail" />
          <xs:element minOccurs="0" name="ReturnAssociation" type="ns:ReturnAssociationDetail" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ReturnType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_TAG" />
          <xs:enumeration value="PENDING" />
          <xs:enumeration value="PRINT_RETURN_LABEL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ReturnedRateType">
        <xs:annotation>
          <xs:documentation>The "PAYOR..." rates are expressed in the currency identified in the payor's rate table(s). The "RATED..." rates are expressed in the currency of the origin country. Former "...COUNTER..." values have become "...RETAIL..." values, except for PAYOR_COUNTER and RATED_COUNTER, which have been removed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="NEGOTIATED" />
          <xs:enumeration value="PAYOR_ACCOUNT_PACKAGE" />
          <xs:enumeration value="PAYOR_ACCOUNT_SHIPMENT" />
          <xs:enumeration value="PAYOR_LIST_PACKAGE" />
          <xs:enumeration value="PAYOR_LIST_SHIPMENT" />
          <xs:enumeration value="PREFERRED_ACCOUNT_PACKAGE" />
          <xs:enumeration value="PREFERRED_ACCOUNT_SHIPMENT" />
          <xs:enumeration value="PREFERRED_LIST_PACKAGE" />
          <xs:enumeration value="PREFERRED_LIST_SHIPMENT" />
          <xs:enumeration value="PREFERRED_NEGOTIATED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Rma">
        <xs:annotation>
          <xs:documentation>June 2011 ITG 121203 IR-RMA number has been removed from this structure and added as a new customer reference type. The structure remains because of the reason field below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Reason" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RotationType">
        <xs:annotation>
          <xs:documentation>Describes the rotation of an item from its default orientation.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT" />
          <xs:enumeration value="NONE" />
          <xs:enumeration value="RIGHT" />
          <xs:enumeration value="UPSIDE_DOWN" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SecondaryBarcodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMON_2D" />
          <xs:enumeration value="NONE" />
          <xs:enumeration value="SSCC_18" />
          <xs:enumeration value="USPS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ServiceDescription">
        <xs:sequence>
          <xs:element minOccurs="0" name="ServiceType" type="xs:string" />
          <xs:element minOccurs="0" name="Code" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Names" type="ns:ProductName">
            <xs:annotation>
              <xs:documentation>Branded, translated, and/or localized names for this service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="AstraDescription" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ServiceOptionType">
        <xs:annotation>
          <xs:documentation>These values control the optional features of service that may be combined in a commitment/rate comparision transaction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_ONE_RATE" />
          <xs:enumeration value="FREIGHT_GUARANTEE" />
          <xs:enumeration value="SATURDAY_DELIVERY" />
          <xs:enumeration value="SMART_POST_ALLOWED_INDICIA" />
          <xs:enumeration value="SMART_POST_HUB_ID" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ServiceSubOptionDetail">
        <xs:annotation>
          <xs:documentation>Supporting detail for applied options identified in a rate quote.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="FreightGuarantee" type="ns:FreightGuaranteeType">
            <xs:annotation>
              <xs:documentation>Identifies the type of Freight Guarantee applied, if FREIGHT_GUARANTEE is applied to the rate quote.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SmartPostHubId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the smartPostHubId used during rate quote, if SMART_POST_HUB_ID is a variable option on the rate request.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="SmartPostIndicia" type="ns:SmartPostIndiciaType">
            <xs:annotation>
              <xs:documentation>Identifies the indicia used during rate quote, if SMART_POST_ALLOWED_INDICIA is a variable option on the rate request.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentAuthorizationDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="AccountNumber" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentConfigurationData">
        <xs:annotation>
          <xs:documentation>Specifies data structures that may be re-used multiple times with s single shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DangerousGoodsPackageConfigurations" type="ns:DangerousGoodsDetail">
            <xs:annotation>
              <xs:documentation>Specifies the data that is common to dangerous goods packages in the shipment. This is populated when the shipment contains packages with identical dangerous goods commodities.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentDryIceDetail">
        <xs:annotation>
          <xs:documentation>Shipment-level totals of dry ice data across all packages.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="PackageCount" type="xs:nonNegativeInteger">
            <xs:annotation>
              <xs:documentation>Total number of packages in the shipment that contain dry ice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalWeight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Total shipment dry ice weight for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ProcessingOptions" type="ns:ShipmentDryIceProcessingOptionsRequested" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentDryIceProcessingOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="SHIPMENT_LEVEL_DRY_ICE_ONLY" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentDryIceProcessingOptionsRequested">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Options" type="ns:ShipmentDryIceProcessingOptionType" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentEventNotificationDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="AggregationType" type="ns:ShipmentNotificationAggregationType" />
          <xs:element minOccurs="0" name="PersonalMessage" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="EventNotifications" type="ns:ShipmentEventNotificationSpecification" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentEventNotificationSpecification">
        <xs:sequence>
          <xs:element minOccurs="0" name="Role" type="ns:ShipmentNotificationRoleType" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Events" type="ns:NotificationEventType" />
          <xs:element minOccurs="0" name="NotificationDetail" type="ns:NotificationDetail" />
          <xs:element minOccurs="0" name="FormatSpecification" type="ns:ShipmentNotificationFormatSpecification" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentLegRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a single leg of a shipment's total/summary rates, as calculated per a specific rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="LegDescription" type="xs:string">
            <xs:annotation>
              <xs:documentation>Human-readable text describing the shipment leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LegOrigin" type="ns:Address">
            <xs:annotation>
              <xs:documentation>Origin for this leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LegOriginLocationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies the location id the origin of shipment leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LegDestination" type="ns:Address">
            <xs:annotation>
              <xs:documentation>Destination for this leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="LegDestinationLocationId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies the location id the destination of shipment leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateType" type="ns:ReturnedRateType">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateScale" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the rate scale used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateZone" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the rate zone used (based on origin and destination).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PricingCode" type="ns:PricingCodeType" />
          <xs:element minOccurs="0" name="RatedWeightMethod" type="ns:RatedWeightMethod">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="MinimumChargeType" type="ns:MinimumChargeType">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CurrencyExchangeRate" type="ns:CurrencyExchangeRate">
            <xs:annotation>
              <xs:documentation>Specifies the currency exchange performed on financial amounts for this rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SpecialRatingApplied" type="ns:SpecialRatingAppliedType">
            <xs:annotation>
              <xs:documentation>Indicates which special rating cases applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DimDivisor" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="DimDivisorType" type="ns:RateDimensionalDivisorType">
            <xs:annotation>
              <xs:documentation>Identifies the type of dim divisor that was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FuelSurchargePercent" type="xs:decimal" />
          <xs:element minOccurs="0" name="TotalBillingWeight" type="ns:Weight" />
          <xs:element minOccurs="0" name="TotalDimWeight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Sum of dimensional weights for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalBaseCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalFreightDiscounts" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalNetFreight" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalSurcharges" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalNetFedExCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Total of the transportation-based taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalNetCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalRebates" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalDutiesAndTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Total of all values under this shipment's dutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalNetChargeWithDutiesAndTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetCharge + totalDutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment AND duties, taxes and transportation charges are all paid by the same sender's account.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightRateDetail" type="ns:FreightRateDetail">
            <xs:annotation>
              <xs:documentation>Rate data specific to FedEx Freight and FedEx National Freight services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="FreightDiscounts" type="ns:RateDiscount">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Rebates" type="ns:Rebate">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Surcharges" type="ns:Surcharge">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Taxes" type="ns:Tax">
            <xs:annotation>
              <xs:documentation>All transportation-based taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DutiesAndTaxes" type="ns:EdtCommodityTax">
            <xs:annotation>
              <xs:documentation>All commodity-based duties and taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="VariableHandlingCharges" type="ns:VariableHandlingCharges">
            <xs:annotation>
              <xs:documentation>The "order level" variable handling charges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalVariableHandlingCharges" type="ns:VariableHandlingCharges">
            <xs:annotation>
              <xs:documentation>The total of all variable handling charges at both shipment (order) and package level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentNotificationAggregationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PER_PACKAGE" />
          <xs:enumeration value="PER_SHIPMENT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentNotificationFormatSpecification">
        <xs:sequence>
          <xs:element minOccurs="0" name="Type" type="ns:NotificationFormatType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentNotificationRoleType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="RECIPIENT" />
          <xs:enumeration value="SHIPPER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ShipmentOnlyFieldsType">
        <xs:annotation>
          <xs:documentation>These values identify which package-level data values will be provided at the shipment-level.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DIMENSIONS" />
          <xs:enumeration value="INSURED_VALUE" />
          <xs:enumeration value="WEIGHT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a shipment's total/summary rates, as calculated per a specific rate type. The "total..." fields may differ from the sum of corresponding package data for Multiweight or Express MPS.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="RateType" type="ns:ReturnedRateType">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateScale" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the rate scale used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateZone" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the rate zone used (based on origin and destination).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PricingCode" type="ns:PricingCodeType" />
          <xs:element minOccurs="0" name="RatedWeightMethod" type="ns:RatedWeightMethod">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="MinimumChargeType" type="ns:MinimumChargeType">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CurrencyExchangeRate" type="ns:CurrencyExchangeRate">
            <xs:annotation>
              <xs:documentation>Specifies the currency exchange performed on financial amounts for this rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SpecialRatingApplied" type="ns:SpecialRatingAppliedType">
            <xs:annotation>
              <xs:documentation>Indicates which special rating cases applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DimDivisor" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="DimDivisorType" type="ns:RateDimensionalDivisorType">
            <xs:annotation>
              <xs:documentation>Identifies the type of dim divisor that was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FuelSurchargePercent" type="xs:decimal" />
          <xs:element minOccurs="0" name="TotalBillingWeight" type="ns:Weight" />
          <xs:element minOccurs="0" name="TotalDimWeight" type="ns:Weight">
            <xs:annotation>
              <xs:documentation>Sum of dimensional weights for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalBaseCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalFreightDiscounts" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalNetFreight" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalSurcharges" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalNetFedExCharge" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Total of the transportation-based taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalNetCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalRebates" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalDutiesAndTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Total of all values under this shipment's dutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalAncillaryFeesAndTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>Identifies the total amount of the shipment-level fees and taxes that are not based on transportation charges or commodity-level estimated duties and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalDutiesTaxesAndFees" type="ns:Money">
            <xs:annotation>
              <xs:documentation>The total of the totalDutiesAndTaxes plus the totalAncillaryFeesAndTaxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalNetChargeWithDutiesAndTaxes" type="ns:Money">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetCharge + totalDutiesTaxesAndFees; some duties and taxes are only provided if estimated duties and taxes were calculated for this shipment AND duties, taxes and transportation charges are all paid by the same sender's account.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ShipmentLegRateDetails" type="ns:ShipmentLegRateDetail">
            <xs:annotation>
              <xs:documentation>Identifies the Rate Details per each leg in a Freight Shipment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightRateDetail" type="ns:FreightRateDetail">
            <xs:annotation>
              <xs:documentation>Rate data specific to FedEx Freight and FedEx National Freight services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="FreightDiscounts" type="ns:RateDiscount">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Rebates" type="ns:Rebate">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Surcharges" type="ns:Surcharge">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Taxes" type="ns:Tax">
            <xs:annotation>
              <xs:documentation>All transportation-based taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="DutiesAndTaxes" type="ns:EdtCommodityTax">
            <xs:annotation>
              <xs:documentation>All commodity-based duties and taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="AncillaryFeesAndTaxes" type="ns:AncillaryFeeAndTax">
            <xs:annotation>
              <xs:documentation>Identifies the shipment-level fees and taxes that are not based on transportation charges or commodity-level estimated duties and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="VariableHandlingCharges" type="ns:VariableHandlingCharges">
            <xs:annotation>
              <xs:documentation>The "order level" variable handling charges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TotalVariableHandlingCharges" type="ns:VariableHandlingCharges">
            <xs:annotation>
              <xs:documentation>The total of all variable handling charges at both shipment (order) and package level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentSpecialServicesRequested">
        <xs:annotation>
          <xs:documentation>These special services are available at the shipment level for some or all service types. If the shipper is requesting a special service which requires additional data (such as the COD amount), the shipment special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SpecialServiceTypes" type="xs:string">
            <xs:annotation>
              <xs:documentation>Indicates the shipment special service types that are requested on this shipment. For a list of the valid shipment special service types, please consult your integration documentation or get the list of the available special services from the getAllSpecialServices method of the Validation Availability and Commitment service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CodDetail" type="ns:CodDetail" />
          <xs:element minOccurs="0" name="DeliveryOnInvoiceAcceptanceDetail" type="ns:DeliveryOnInvoiceAcceptanceDetail" />
          <xs:element minOccurs="0" name="HoldAtLocationDetail" type="ns:HoldAtLocationDetail" />
          <xs:element minOccurs="0" name="EventNotificationDetail" type="ns:ShipmentEventNotificationDetail">
            <xs:annotation>
              <xs:documentation>This replaces eMailNotificationDetail</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ReturnShipmentDetail" type="ns:ReturnShipmentDetail" />
          <xs:element minOccurs="0" name="PendingShipmentDetail" type="ns:PendingShipmentDetail">
            <xs:annotation>
              <xs:documentation>This field should be populated for pending shipments (e.g. e-mail label) It is required by a PENDING_SHIPMENT special service type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="InternationalControlledExportDetail" type="ns:InternationalControlledExportDetail" />
          <xs:element minOccurs="0" name="InternationalTrafficInArmsRegulationsDetail" type="ns:InternationalTrafficInArmsRegulationsDetail" />
          <xs:element minOccurs="0" name="ShipmentDryIceDetail" type="ns:ShipmentDryIceDetail" />
          <xs:element minOccurs="0" name="HomeDeliveryPremiumDetail" type="ns:HomeDeliveryPremiumDetail" />
          <xs:element minOccurs="0" name="FlatbedTrailerDetail" type="ns:FlatbedTrailerDetail" />
          <xs:element minOccurs="0" name="FreightGuaranteeDetail" type="ns:FreightGuaranteeDetail" />
          <xs:element minOccurs="0" name="EtdDetail" type="ns:EtdDetail">
            <xs:annotation>
              <xs:documentation>Electronic Trade document references.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CustomDeliveryWindowDetail" type="ns:CustomDeliveryWindowDetail">
            <xs:annotation>
              <xs:documentation>Specification for date or range of dates on which delivery is to be attempted.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentVariationOptionDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string">
            <xs:annotation>
              <xs:documentation>Specifies the name or the key for the shipment variation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Values" type="xs:string">
            <xs:annotation>
              <xs:documentation>The values that are valid for the specified shipment variation in the context of the current shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipperConveyanceDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Id" type="xs:string">
            <xs:annotation>
              <xs:documentation>This indicates the identifier of a conveyance, such as a trailer ID.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentDispositionDetail">
        <xs:annotation>
          <xs:documentation>Each occurrence of this class specifies a particular way in which a kind of shipping document is to be produced and provided.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="DispositionType" type="ns:ShippingDocumentDispositionType">
            <xs:annotation>
              <xs:documentation>Values in this field specify how to create and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Grouping" type="ns:ShippingDocumentGroupingType">
            <xs:annotation>
              <xs:documentation>Specifies how to organize all documents of this type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EMailDetail" type="ns:ShippingDocumentEMailDetail">
            <xs:annotation>
              <xs:documentation>Specifies how to e-mail document images.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="PrintDetail" type="ns:ShippingDocumentPrintDetail">
            <xs:annotation>
              <xs:documentation>Specifies how a queued document is to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentDispositionType">
        <xs:annotation>
          <xs:documentation>Specifies how to return a shipping document to the caller.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONFIRMED" />
          <xs:enumeration value="DEFERRED_QUEUED" />
          <xs:enumeration value="DEFERRED_RETURNED" />
          <xs:enumeration value="DEFERRED_STORED" />
          <xs:enumeration value="EMAILED" />
          <xs:enumeration value="QUEUED" />
          <xs:enumeration value="RETURNED" />
          <xs:enumeration value="STORED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentEMailDetail">
        <xs:annotation>
          <xs:documentation>Specifies how to e-mail shipping documents.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="EMailRecipients" type="ns:ShippingDocumentEMailRecipient">
            <xs:annotation>
              <xs:documentation>Provides the roles and email addresses for e-mail recipients.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Grouping" type="ns:ShippingDocumentEMailGroupingType">
            <xs:annotation>
              <xs:documentation>Identifies the convention by which documents are to be grouped as e-mail attachments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Localization" type="ns:Localization">
            <xs:annotation>
              <xs:documentation>Specifies the language in which the email containing the document is requested to be composed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentEMailGroupingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BY_RECIPIENT" />
          <xs:enumeration value="NONE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentEMailRecipient">
        <xs:annotation>
          <xs:documentation>Specifies an individual recipient of e-mailed shipping document(s).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="RecipientType" type="ns:EMailNotificationRecipientType">
            <xs:annotation>
              <xs:documentation>Identifies the relationship of this recipient in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Address" type="xs:string">
            <xs:annotation>
              <xs:documentation>Address to which the document is to be sent.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentFormat">
        <xs:annotation>
          <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Dispositions" type="ns:ShippingDocumentDispositionDetail">
            <xs:annotation>
              <xs:documentation>Specifies how to create, organize, and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="TopOfPageOffset" type="ns:LinearMeasure">
            <xs:annotation>
              <xs:documentation>Specifies how far down the page to move the beginning of the image; allows for printing on letterhead and other pre-printed stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ImageType" type="ns:ShippingDocumentImageType" />
          <xs:element minOccurs="0" name="StockType" type="ns:ShippingDocumentStockType" />
          <xs:element minOccurs="0" name="ProvideInstructions" type="xs:boolean">
            <xs:annotation>
              <xs:documentation>For those shipping document types which have both a "form" and "instructions" component (e.g. General Agency Agreement), this field indicates whether to provide the instructions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="OptionsRequested" type="ns:DocumentFormatOptionsRequested" />
          <xs:element minOccurs="0" name="Localization" type="ns:Localization">
            <xs:annotation>
              <xs:documentation>Governs the language to be used for this individual document, independently from other content returned for the same shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentGroupingType">
        <xs:annotation>
          <xs:documentation>Specifies how to organize all shipping documents of the same type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSOLIDATED_BY_DOCUMENT_TYPE" />
          <xs:enumeration value="INDIVIDUAL" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ShippingDocumentImageType">
        <xs:annotation>
          <xs:documentation>Specifies the image format used for a shipping document.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EPL2" />
          <xs:enumeration value="PDF" />
          <xs:enumeration value="PNG" />
          <xs:enumeration value="ZPLII" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentPrintDetail">
        <xs:annotation>
          <xs:documentation>Specifies printing options for a shipping document.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="PrinterId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Provides environment-specific printer identification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentSpecification">
        <xs:annotation>
          <xs:documentation>Contains all data required for additional (non-label) shipping documents to be produced in conjunction with a specific shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="ShippingDocumentTypes" type="ns:RequestedShippingDocumentType">
            <xs:annotation>
              <xs:documentation>Indicates the types of shipping documents requested by the shipper.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="CertificateOfOrigin" type="ns:CertificateOfOriginDetail" />
          <xs:element minOccurs="0" name="CommercialInvoiceDetail" type="ns:CommercialInvoiceDetail" />
          <xs:element minOccurs="0" name="UsmcaCommercialInvoiceCertificationOfOriginDetail" type="ns:UsmcaCommercialInvoiceCertificationOfOriginDetail" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomPackageDocumentDetail" type="ns:CustomDocumentDetail">
            <xs:annotation>
              <xs:documentation>Specifies the production of each package-level custom document (the same specification is used for all packages).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomShipmentDocumentDetail" type="ns:CustomDocumentDetail">
            <xs:annotation>
              <xs:documentation>Specifies the production of a shipment-level custom document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ExportDeclarationDetail" type="ns:ExportDeclarationDetail" />
          <xs:element minOccurs="0" name="GeneralAgencyAgreementDetail" type="ns:GeneralAgencyAgreementDetail" />
          <xs:element minOccurs="0" name="UsmcaCertificationOfOriginDetail" type="ns:UsmcaCertificationOfOriginDetail" />
          <xs:element minOccurs="0" name="Op900Detail" type="ns:Op900Detail">
            <xs:annotation>
              <xs:documentation>Specifies the production of the OP-900 document for hazardous materials packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DangerousGoodsShippersDeclarationDetail" type="ns:DangerousGoodsShippersDeclarationDetail">
            <xs:annotation>
              <xs:documentation>Specifies the production of the 1421c document for dangerous goods shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="FreightAddressLabelDetail" type="ns:FreightAddressLabelDetail">
            <xs:annotation>
              <xs:documentation>Specifies the production of the OP-900 document for hazardous materials.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="ReturnInstructionsDetail" type="ns:ReturnInstructionsDetail">
            <xs:annotation>
              <xs:documentation>Specifies the production of the return instructions document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentStockType">
        <xs:annotation>
          <xs:documentation>Specifies the type of paper (stock) on which a document will be printed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="OP_900_LG_B" />
          <xs:enumeration value="OP_900_LL_B" />
          <xs:enumeration value="PAPER_4X6" />
          <xs:enumeration value="PAPER_LETTER" />
          <xs:enumeration value="STOCK_4X6" />
          <xs:enumeration value="STOCK_4X6.75_LEADING_DOC_TAB" />
          <xs:enumeration value="STOCK_4X6.75_TRAILING_DOC_TAB" />
          <xs:enumeration value="STOCK_4X8" />
          <xs:enumeration value="STOCK_4X9_LEADING_DOC_TAB" />
          <xs:enumeration value="STOCK_4X9_TRAILING_DOC_TAB" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SignatureOptionDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="OptionType" type="ns:SignatureOptionType" />
          <xs:element minOccurs="0" name="SignatureReleaseNumber" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SignatureOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADULT" />
          <xs:enumeration value="DIRECT" />
          <xs:enumeration value="INDIRECT" />
          <xs:enumeration value="NO_SIGNATURE_REQUIRED" />
          <xs:enumeration value="SERVICE_DEFAULT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SmartPostAncillaryEndorsementType">
        <xs:annotation>
          <xs:documentation>These values are mutually exclusive; at most one of them can be attached to a SmartPost shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDRESS_CORRECTION" />
          <xs:enumeration value="CARRIER_LEAVE_IF_NO_RESPONSE" />
          <xs:enumeration value="CHANGE_SERVICE" />
          <xs:enumeration value="FORWARDING_SERVICE" />
          <xs:enumeration value="RETURN_SERVICE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SmartPostIndiciaType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="MEDIA_MAIL" />
          <xs:enumeration value="PARCEL_RETURN" />
          <xs:enumeration value="PARCEL_SELECT" />
          <xs:enumeration value="PRESORTED_BOUND_PRINTED_MATTER" />
          <xs:enumeration value="PRESORTED_STANDARD" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SmartPostShipmentDetail">
        <xs:annotation>
          <xs:documentation>Data required for shipments handled under the SMART_POST and GROUND_SMART_POST service types.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="ProcessingOptionsRequested" type="ns:SmartPostShipmentProcessingOptionsRequested" />
          <xs:element minOccurs="0" name="Indicia" type="ns:SmartPostIndiciaType" />
          <xs:element minOccurs="0" name="AncillaryEndorsement" type="ns:SmartPostAncillaryEndorsementType" />
          <xs:element minOccurs="0" name="HubId" type="xs:string" />
          <xs:element minOccurs="0" name="CustomerManifestId" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SmartPostShipmentProcessingOptionType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="GROUND_TRACKING_NUMBER_REQUESTED" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SmartPostShipmentProcessingOptionsRequested">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Options" type="ns:SmartPostShipmentProcessingOptionType" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SpecialRatingAppliedType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_ONE_RATE" />
          <xs:enumeration value="FIXED_FUEL_SURCHARGE" />
          <xs:enumeration value="IMPORT_PRICING" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Surcharge">
        <xs:sequence>
          <xs:element minOccurs="0" name="SurchargeType" type="ns:SurchargeType" />
          <xs:element minOccurs="0" name="Level" type="ns:SurchargeLevelType" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SurchargeLevelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PACKAGE" />
          <xs:enumeration value="SHIPMENT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SurchargeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCOUNT_NUMBER_PROCESSING_FEE" />
          <xs:enumeration value="ADDITIONAL_HANDLING" />
          <xs:enumeration value="ADDRESS_CORRECTION" />
          <xs:enumeration value="ANCILLARY_FEE" />
          <xs:enumeration value="APPOINTMENT_DELIVERY" />
          <xs:enumeration value="BROKER_SELECT_OPTION" />
          <xs:enumeration value="CANADIAN_DESTINATION" />
          <xs:enumeration value="COD" />
          <xs:enumeration value="CUT_FLOWERS" />
          <xs:enumeration value="DANGEROUS_GOODS" />
          <xs:enumeration value="DELIVERY_AREA" />
          <xs:enumeration value="DELIVERY_CONFIRMATION" />
          <xs:enumeration value="DOCUMENTATION_FEE" />
          <xs:enumeration value="DRY_ICE" />
          <xs:enumeration value="EMAIL_LABEL" />
          <xs:enumeration value="EUROPE_FIRST" />
          <xs:enumeration value="EXCESS_VALUE" />
          <xs:enumeration value="EXHIBITION" />
          <xs:enumeration value="EXPORT" />
          <xs:enumeration value="EXTRA_SURFACE_HANDLING_CHARGE" />
          <xs:enumeration value="EXTREME_LENGTH" />
          <xs:enumeration value="FEDEX_INTRACOUNTRY_FEES" />
          <xs:enumeration value="FEDEX_TAG" />
          <xs:enumeration value="FICE" />
          <xs:enumeration value="FLATBED" />
          <xs:enumeration value="FREIGHT_GUARANTEE" />
          <xs:enumeration value="FREIGHT_ON_VALUE" />
          <xs:enumeration value="FREIGHT_TO_COLLECT" />
          <xs:enumeration value="FUEL" />
          <xs:enumeration value="HIGH_COST_SERVICE_AREA_DESTINATION" />
          <xs:enumeration value="HIGH_COST_SERVICE_AREA_ORIGIN" />
          <xs:enumeration value="HOLD_AT_LOCATION" />
          <xs:enumeration value="HOME_DELIVERY_APPOINTMENT" />
          <xs:enumeration value="HOME_DELIVERY_DATE_CERTAIN" />
          <xs:enumeration value="HOME_DELIVERY_EVENING" />
          <xs:enumeration value="INSIDE_DELIVERY" />
          <xs:enumeration value="INSIDE_PICKUP" />
          <xs:enumeration value="INSURED_VALUE" />
          <xs:enumeration value="INTERHAWAII" />
          <xs:enumeration value="LIFTGATE_DELIVERY" />
          <xs:enumeration value="LIFTGATE_PICKUP" />
          <xs:enumeration value="LIMITED_ACCESS_DELIVERY" />
          <xs:enumeration value="LIMITED_ACCESS_PICKUP" />
          <xs:enumeration value="METRO_DELIVERY" />
          <xs:enumeration value="METRO_PICKUP" />
          <xs:enumeration value="NON_MACHINABLE" />
          <xs:enumeration value="OFFSHORE" />
          <xs:enumeration value="ON_CALL_PICKUP" />
          <xs:enumeration value="ON_DEMAND_CARE" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="OUT_OF_DELIVERY_AREA" />
          <xs:enumeration value="OUT_OF_PICKUP_AREA" />
          <xs:enumeration value="OVERSIZE" />
          <xs:enumeration value="OVER_DIMENSION" />
          <xs:enumeration value="OVER_LENGTH" />
          <xs:enumeration value="PEAK" />
          <xs:enumeration value="PEAK_ADDITIONAL_HANDLING" />
          <xs:enumeration value="PEAK_OVERSIZE" />
          <xs:enumeration value="PEAK_RESIDENTIAL_DELIVERY" />
          <xs:enumeration value="PIECE_COUNT_VERIFICATION" />
          <xs:enumeration value="PRE_DELIVERY_NOTIFICATION" />
          <xs:enumeration value="PRIORITY_ALERT" />
          <xs:enumeration value="PROTECTION_FROM_FREEZING" />
          <xs:enumeration value="REGIONAL_MALL_DELIVERY" />
          <xs:enumeration value="REGIONAL_MALL_PICKUP" />
          <xs:enumeration value="REROUTE" />
          <xs:enumeration value="RESCHEDULE" />
          <xs:enumeration value="RESIDENTIAL_DELIVERY" />
          <xs:enumeration value="RESIDENTIAL_PICKUP" />
          <xs:enumeration value="RETURN_LABEL" />
          <xs:enumeration value="SATURDAY_DELIVERY" />
          <xs:enumeration value="SATURDAY_PICKUP" />
          <xs:enumeration value="SIGNATURE_OPTION" />
          <xs:enumeration value="TARP" />
          <xs:enumeration value="THIRD_PARTY_CONSIGNEE" />
          <xs:enumeration value="TRANSMART_SERVICE_FEE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Tax">
        <xs:sequence>
          <xs:element minOccurs="0" name="TaxType" type="ns:TaxType" />
          <xs:element minOccurs="0" name="Description" type="xs:string" />
          <xs:element minOccurs="0" name="Amount" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TaxType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPORT" />
          <xs:enumeration value="GST" />
          <xs:enumeration value="HST" />
          <xs:enumeration value="INTRACOUNTRY" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="PST" />
          <xs:enumeration value="VAT" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TaxesOrMiscellaneousChargeType">
        <xs:annotation>
          <xs:documentation>Specifice the kind of tax or miscellaneous charge being reported on a Commercial Invoice.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMISSIONS" />
          <xs:enumeration value="DISCOUNTS" />
          <xs:enumeration value="HANDLING_FEES" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="ROYALTIES_AND_LICENSE_FEES" />
          <xs:enumeration value="TAXES" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TaxpayerIdentification">
        <xs:sequence>
          <xs:element minOccurs="0" name="TinType" type="ns:TinType" />
          <xs:element minOccurs="0" name="Number" type="xs:string" />
          <xs:element minOccurs="0" name="Usage" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies the usage of Tax Identification Number in Shipment processing</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="EffectiveDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="ExpirationDate" type="xs:dateTime" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TinType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS_NATIONAL" />
          <xs:enumeration value="BUSINESS_STATE" />
          <xs:enumeration value="PERSONAL_NATIONAL" />
          <xs:enumeration value="PERSONAL_STATE" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingId">
        <xs:sequence>
          <xs:element minOccurs="0" name="TrackingIdType" type="ns:TrackingIdType" />
          <xs:element minOccurs="0" name="FormId" type="xs:string" />
          <xs:element minOccurs="0" name="TrackingNumber" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingIdType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPRESS" />
          <xs:enumeration value="FEDEX" />
          <xs:enumeration value="GROUND" />
          <xs:enumeration value="USPS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TransactionDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="CustomerTransactionId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Free form text to be echoed back in the reply. Used to match requests and replies.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Localization" type="ns:Localization">
            <xs:annotation>
              <xs:documentation>Governs data payload language/translations (contrasted with ClientDetail.localization, which governs Notification.localizedMessage language selection).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TransitTimeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EIGHTEEN_DAYS" />
          <xs:enumeration value="EIGHT_DAYS" />
          <xs:enumeration value="ELEVEN_DAYS" />
          <xs:enumeration value="FIFTEEN_DAYS" />
          <xs:enumeration value="FIVE_DAYS" />
          <xs:enumeration value="FOURTEEN_DAYS" />
          <xs:enumeration value="FOUR_DAYS" />
          <xs:enumeration value="NINETEEN_DAYS" />
          <xs:enumeration value="NINE_DAYS" />
          <xs:enumeration value="ONE_DAY" />
          <xs:enumeration value="SEVENTEEN_DAYS" />
          <xs:enumeration value="SEVEN_DAYS" />
          <xs:enumeration value="SIXTEEN_DAYS" />
          <xs:enumeration value="SIX_DAYS" />
          <xs:enumeration value="TEN_DAYS" />
          <xs:enumeration value="THIRTEEN_DAYS" />
          <xs:enumeration value="THREE_DAYS" />
          <xs:enumeration value="TWELVE_DAYS" />
          <xs:enumeration value="TWENTY_DAYS" />
          <xs:enumeration value="TWO_DAYS" />
          <xs:enumeration value="UNKNOWN" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UploadDocumentIdProducer">
        <xs:annotation>
          <xs:documentation>Specifies the application that is responsible for managing the document id.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER" />
          <xs:enumeration value="FEDEX_CAFE" />
          <xs:enumeration value="FEDEX_CSHP" />
          <xs:enumeration value="FEDEX_FXRS" />
          <xs:enumeration value="FEDEX_GSMW" />
          <xs:enumeration value="FEDEX_GTM" />
          <xs:enumeration value="FEDEX_INET" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UploadDocumentProducerType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER" />
          <xs:enumeration value="FEDEX_CAFE" />
          <xs:enumeration value="FEDEX_CLS" />
          <xs:enumeration value="FEDEX_FIDT" />
          <xs:enumeration value="FEDEX_FXRS" />
          <xs:enumeration value="FEDEX_GSMW" />
          <xs:enumeration value="FEDEX_GTM" />
          <xs:enumeration value="OTHER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="UploadDocumentReferenceDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="LineNumber" type="xs:nonNegativeInteger" />
          <xs:element minOccurs="0" name="CustomerReference" type="xs:string" />
          <xs:element minOccurs="0" name="Description" type="xs:string">
            <xs:annotation>
              <xs:documentation>Description of the uploaded document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="DocumentProducer" type="ns:UploadDocumentProducerType" />
          <xs:element minOccurs="0" name="DocumentType" type="ns:UploadDocumentType" />
          <xs:element minOccurs="0" name="DocumentId" type="xs:string" />
          <xs:element minOccurs="0" name="DocumentIdProducer" type="ns:UploadDocumentIdProducer" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="UploadDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN" />
          <xs:enumeration value="COMMERCIAL_INVOICE" />
          <xs:enumeration value="ETD_LABEL" />
          <xs:enumeration value="NET_RATE_SHEET" />
          <xs:enumeration value="OTHER" />
          <xs:enumeration value="PRO_FORMA_INVOICE" />
          <xs:enumeration value="USMCA_CERTIFICATION_OF_ORIGIN" />
          <xs:enumeration value="USMCA_COMMERCIAL_INVOICE_CERTIFICATION_OF_ORIGIN" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="UsmcaCertificationOfOriginDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat" />
          <xs:element minOccurs="0" name="BlanketPeriod" type="ns:DateRange" />
          <xs:element minOccurs="0" name="CertifierSpecification" type="ns:UsmcaCertifierSpecificationType" />
          <xs:element minOccurs="0" name="ImporterSpecification" type="ns:UsmcaImporterSpecificationType" />
          <xs:element minOccurs="0" name="ProducerSpecification" type="ns:UsmcaProducerSpecificationType" />
          <xs:element minOccurs="0" name="Producer" type="ns:Party" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="UsmcaCertifierSpecificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPORTER" />
          <xs:enumeration value="IMPORTER" />
          <xs:enumeration value="PRODUCER" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="UsmcaCommercialInvoiceCertificationOfOriginDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="Format" type="ns:ShippingDocumentFormat" />
          <xs:element minOccurs="0" maxOccurs="unbounded" name="CustomerImageUsages" type="ns:CustomerImageUsage" />
          <xs:element minOccurs="0" name="FormVersion" type="xs:string" />
          <xs:element minOccurs="0" name="CertifierSpecification" type="ns:UsmcaCertifierSpecificationType" />
          <xs:element minOccurs="0" name="ProducerSpecification" type="ns:UsmcaProducerSpecificationType" />
          <xs:element minOccurs="0" name="Producer" type="ns:Party" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="UsmcaCommodityDetail">
        <xs:sequence>
          <xs:element minOccurs="0" name="OriginCriterion" type="ns:UsmcaOriginCriterionCode" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="UsmcaImporterSpecificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="UNKNOWN" />
          <xs:enumeration value="VARIOUS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UsmcaOriginCriterionCode">
        <xs:restriction base="xs:string">
          <xs:enumeration value="A" />
          <xs:enumeration value="B" />
          <xs:enumeration value="C" />
          <xs:enumeration value="D" />
          <xs:enumeration value="E" />
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UsmcaProducerSpecificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AVAILABLE_UPON_REQUEST" />
          <xs:enumeration value="SAME_AS_EXPORTER" />
          <xs:enumeration value="VARIOUS" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="VariableHandlingChargeDetail">
        <xs:annotation>
          <xs:documentation>This definition of variable handling charge detail is intended for use in Jan 2011 corp load.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="FixedValue" type="ns:Money" />
          <xs:element minOccurs="0" name="PercentValue" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>Actual percentage (10 means 10%, which is a mutiplier of 0.1)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateElementBasis" type="ns:RateElementBasisType">
            <xs:annotation>
              <xs:documentation>Select the value from a set of rate data to which the percentage is applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="RateTypeBasis" type="ns:RateTypeBasisType">
            <xs:annotation>
              <xs:documentation>Select the type of rate from which the element is to be selected.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VariableHandlingCharges">
        <xs:sequence>
          <xs:element minOccurs="0" name="VariableHandlingCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="FixedVariableHandlingCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="PercentVariableHandlingCharge" type="ns:Money" />
          <xs:element minOccurs="0" name="TotalCustomerCharge" type="ns:Money" />
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Volume">
        <xs:annotation>
          <xs:documentation>Three-dimensional volume/cubic measurement.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Units" type="ns:VolumeUnits" />
          <xs:element minOccurs="0" name="Value" type="xs:decimal" />
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="VolumeUnits">
        <xs:annotation>
          <xs:documentation>Units of three-dimensional volume/cubic measure.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUBIC_FT" />
          <xs:enumeration value="CUBIC_M" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Weight">
        <xs:annotation>
          <xs:documentation>The descriptive data for the heaviness of an object.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="Units" type="ns:WeightUnits">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure associated with a weight value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="Value" type="xs:decimal">
            <xs:annotation>
              <xs:documentation>Identifies the weight value of a package/shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="WeightUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="KG" />
          <xs:enumeration value="LB" />
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="WebAuthenticationDetail">
        <xs:annotation>
          <xs:documentation>Used in authentication of the sender's identity.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" name="ParentCredential" type="ns:WebAuthenticationCredential">
            <xs:annotation>
              <xs:documentation>This was renamed from cspCredential.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" name="UserCredential" type="ns:WebAuthenticationCredential">
            <xs:annotation>
              <xs:documentation>Credential used to authenticate a specific software application. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="WebAuthenticationCredential">
        <xs:annotation>
          <xs:documentation>Two part authentication string used for the sender's identity</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="1" name="Key" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifying part of authentication credential. This value is provided by FedEx after registration</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" name="Password" type="xs:string">
            <xs:annotation>
              <xs:documentation>Secret part of authentication key. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VersionId">
        <xs:annotation>
          <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="1" fixed="crs" name="ServiceId" type="xs:string">
            <xs:annotation>
              <xs:documentation>Identifies a system or sub-system which performs an operation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" fixed="31" name="Major" type="xs:int">
            <xs:annotation>
              <xs:documentation>Identifies the service business level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" fixed="0" name="Intermediate" type="xs:int">
            <xs:annotation>
              <xs:documentation>Identifies the service interface level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="1" fixed="0" name="Minor" type="xs:int">
            <xs:annotation>
              <xs:documentation>Identifies the service code level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </types>
  <message name="RateRequest">
    <part name="RateRequest" element="ns:RateRequest" />
  </message>
  <message name="RateReply">
    <part name="RateReply" element="ns:RateReply" />
  </message>
  <portType name="RatePortType">
    <operation name="getRates" parameterOrder="RateRequest">
      <input message="ns:RateRequest" />
      <output message="ns:RateReply" />
    </operation>
  </portType>
  <binding name="RateServiceSoapBinding" type="ns:RatePortType">
    <s1:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <operation name="getRates">
      <s1:operation soapAction="http://fedex.com/ws/rate/v31/getRates" style="document" />
      <input>
        <s1:body use="literal" />
      </input>
      <output>
        <s1:body use="literal" />
      </output>
    </operation>
  </binding>
  <service name="RateService">
    <port name="RateServicePort" binding="ns:RateServiceSoapBinding">
      <s1:address location="https://wsbeta.fedex.com:443/web-services/rate" />
    </port>
  </service>
</definitions>