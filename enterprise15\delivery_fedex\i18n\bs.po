# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_fedex
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid ", click on \"Get Production Key\" and follow all the steps."
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<br/>According to your needs, you will need to contact different "
"certifications :"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Account Number"
msgstr "Broj računa"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Advanced Services with Label Certification"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Advanced Services without Label Certification"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_droppoff_type:0
msgid "BUSINESS_SERVICE_CENTER"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
msgid "Based on Rules"
msgstr "Bazirano na pravilima"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_product_packaging__package_carrier_type
msgid "Carrier"
msgstr "Prevoznik"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Certification Process"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "DHL"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_droppoff_type:0
msgid "DROP_BOX"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_default_packaging_id
msgid "Default Package Type"
msgstr ""

#. module: delivery_fedex
#: model:ir.model,name:delivery_fedex.model_delivery_carrier
msgid "Delivery Methods"
msgstr "Metode ispuruke"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_developer_key
msgid "Developer Key"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_file_type:0
msgid "EPL2"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "Easypost"
msgstr ""

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:233
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_label_file_type
msgid "FEDEX Label File Type"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "FEDEX_2_DAY"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "FEDEX_2_DAY_AM"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "FEDEX_GROUND"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "FIRST_OVERNIGHT"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "FedEx"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_account_number
msgid "FedEx Account Number"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_fedex.res_config_settings_view_form_stock
msgid "FedEx Delivery Methods"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_saturday_delivery
msgid "FedEx Saturday Delivery"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "FedEx Web Services \"Move to Production\""
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Configuration"
msgstr ""

#. module: delivery_fedex
#: model:delivery.carrier,name:delivery_fedex.delivery_carrier_fedex_inter
#: model:product.product,name:delivery_fedex.product_product_delivery_fedex_inter
#: model:product.template,name:delivery_fedex.product_product_delivery_fedex_inter_product_template
msgid "Fedex International"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_label_stock_type
msgid "Fedex Label Stock Type"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_service_type
msgid "Fedex Service Type"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Tutorial"
msgstr ""

#. module: delivery_fedex
#: model:delivery.carrier,name:delivery_fedex.delivery_carrier_fedex_us
#: model:product.product,name:delivery_fedex.product_product_delivery_fedex_us
#: model:product.template,name:delivery_fedex.product_product_delivery_fedex_us_product_template
msgid "Fedex US"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Website"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_weight_unit
msgid "Fedex Weight Unit"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_droppoff_type
msgid "Fedex drop-off type"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
msgid "Fixed Price"
msgstr "Fiksna cijena"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Go to"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "INTERNATIONAL_ECONOMY"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "INTERNATIONAL_PRIORITY"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_weight_unit:0
msgid "KG"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_weight_unit:0
msgid "LB"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_meter_number
msgid "Meter Number"
msgstr ""

#. module: delivery_fedex
#: selection:product.packaging,package_carrier_type:0
msgid "No carrier integration"
msgstr "Nema integracije prevoznika"

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:440
#, python-format
msgid "No packages for this picking"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Once your account is created, go to"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_4X6"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_4X8"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_4X9"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_7X4.75"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_8.5X11_BOTTOM_HALF_LABEL"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_8.5X11_TOP_HALF_LABEL"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "PAPER_LETTER"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_file_type:0
msgid "PDF"
msgstr "PDF"

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_file_type:0
msgid "PNG"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "PRIORITY_OVERNIGHT"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_developer_password
msgid "Password"
msgstr "Šifra"

#. module: delivery_fedex
#: model:ir.model,name:delivery_fedex.model_product_packaging
msgid "Product Packaging"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Provajder"

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_droppoff_type:0
msgid "REGULAR_PICKUP"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_droppoff_type:0
msgid "REQUEST_COURIER"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_service_type:0
msgid "STANDARD_OVERNIGHT"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_droppoff_type:0
msgid "STATION"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "STOCK_4X6"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "STOCK_4X6.75_LEADING_DOC_TAB"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "STOCK_4X6.75_TRAILING_DOC_TAB"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "STOCK_4X8"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "STOCK_4X9_LEADING_DOC_TAB"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_stock_type:0
msgid "STOCK_4X9_TRAILING_DOC_TAB"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Saturday Delivery"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Screenshot"
msgstr ""

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:463
#, python-format
msgid "Shipment N° %s has been cancelled"
msgstr ""

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:424
#, python-format
msgid "Shipment created into Fedex <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:370
#, python-format
msgid ""
"Shipment created into Fedex<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,help:delivery_fedex.field_delivery_carrier__fedex_saturday_delivery
msgid ""
"Special service:Saturday Delivery, can be requested on following days.\n"
"                                                                                 Thursday:\n"
"1.FEDEX_2_DAY.\n"
"Friday:\n"
"1.PRIORITY_OVERNIGHT.\n"
"2.FIRST_OVERNIGHT.\n"
"                                                                                 3.INTERNATIONAL_PRIORITY.\n"
"(To Select Countries)"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Standard Services"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "The last step is the"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid ""
"These certifications usually require that you contact the FedEx support team"
" by email."
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "UPS"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
msgid "USPS"
msgstr ""

#. module: delivery_fedex
#: model:product.product,uom_name:delivery_fedex.product_product_delivery_fedex_inter
#: model:product.product,uom_name:delivery_fedex.product_product_delivery_fedex_us
#: model:product.template,uom_name:delivery_fedex.product_product_delivery_fedex_inter_product_template
#: model:product.template,uom_name:delivery_fedex.product_product_delivery_fedex_us_product_template
msgid "Unit(s)"
msgstr "kom (komad)"

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:239
#, python-format
msgid ""
"Warning:\n"
"%s"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,fedex_label_file_type:0
msgid "ZPLII"
msgstr ""

#. module: delivery_fedex
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "bpost"
msgstr ""

#. module: delivery_fedex
#: model:product.product,weight_uom_name:delivery_fedex.product_product_delivery_fedex_inter
#: model:product.product,weight_uom_name:delivery_fedex.product_product_delivery_fedex_us
#: model:product.template,weight_uom_name:delivery_fedex.product_product_delivery_fedex_inter_product_template
#: model:product.template,weight_uom_name:delivery_fedex.product_product_delivery_fedex_us_product_template
msgid "kg"
msgstr "kg"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "to create a FedEx account of the following type:"
msgstr ""
