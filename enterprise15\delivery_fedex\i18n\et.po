# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_fedex
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# Leaanika Randmets, 2022
# Birgit Vijar, 2024
# Anna, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid ", click on \"Get Production Key\" and follow all the steps."
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<br/>According to your needs, you will need to contact different "
"certifications :"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Account Number"
msgstr "Konto number"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Advanced Services with Label Certification"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Advanced Services without Label Certification"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_droppoff_type__business_service_center
msgid "BUSINESS_SERVICE_CENTER"
msgstr "BUSINESS_SERVICE_CENTER"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Transpordifirma"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Certification Process"
msgstr "Sertifitseerimisprotsess"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_document_stock_type
msgid "Commercial Invoice Type"
msgstr "Kaubandusliku arve tüüp"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_droppoff_type__drop_box
msgid "DROP_BOX"
msgstr "DROP_BOX"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_developer_key
msgid "Developer Key"
msgstr "Developer Key"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Duties paid by"
msgstr "Tollimaksud on tasutud "

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_file_type__epl2
msgid "EPL2"
msgstr "EPL2"

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"Error:\n"
"%s"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_label_file_type
msgid "FEDEX Label File Type"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_2_day
msgid "FEDEX_2_DAY"
msgstr "FEDEX_2_DAY"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_2_day_am
msgid "FEDEX_2_DAY_AM"
msgstr "FEDEX_2_DAY_AM"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_3_day_freight
msgid "FEDEX_3_DAY_FREIGHT"
msgstr "FEDEX_3_DAY_FREIGHT"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_express_saver
msgid "FEDEX_EXPRESS_SAVER"
msgstr "FEDEX_EXPRESS_SAVER"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_ground
msgid "FEDEX_GROUND"
msgstr "FEDEX_GROUND"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_next_day_afternoon
msgid "FEDEX_NEXT_DAY_AFTERNOON"
msgstr "FEDEX_NEXT_DAY_AFTERNOON"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_next_day_early_morning
msgid "FEDEX_NEXT_DAY_EARLY_MORNING"
msgstr "FEDEX_NEXT_DAY_EARLY_MORNING"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_next_day_end_of_day
msgid "FEDEX_NEXT_DAY_END_OF_DAY"
msgstr "FEDEX_NEXT_DAY_END_OF_DAY"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__fedex_next_day_mid_morning
msgid "FEDEX_NEXT_DAY_MID_MORNING"
msgstr "FEDEX_NEXT_DAY_MID_MORNING"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__first_overnight
msgid "FIRST_OVERNIGHT"
msgstr "FIRST_OVERNIGHT"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__delivery_type__fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__stock_package_type__package_carrier_type__fedex
msgid "FedEx"
msgstr "FedEx"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_account_number
msgid "FedEx Account Number"
msgstr "FedEx kontonumber"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_saturday_delivery
msgid "FedEx Saturday Delivery"
msgstr "FedEx'i kohaletoimetamine laupäeviti"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_fedex.res_config_settings_view_form_stock
msgid "FedEx Shipping Methods"
msgstr "FedEx tarneviis"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "FedEx Web Services \"Move to Production\""
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Configuration"
msgstr "Fedex seaded"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_droppoff_type
msgid "Fedex Drop-Off Type"
msgstr ""

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_duty_payment
msgid "Fedex Duty Payment"
msgstr "FedExi tollimakse"

#. module: delivery_fedex
#: model:delivery.carrier,name:delivery_fedex.delivery_carrier_fedex_inter
#: model:product.product,name:delivery_fedex.product_product_delivery_fedex_inter
#: model:product.template,name:delivery_fedex.product_product_delivery_fedex_inter_product_template
msgid "Fedex International"
msgstr "Fedex rahvusvaheline"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_default_package_type_id
msgid "Fedex Package Type"
msgstr "FedExi pakendi tüüp"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_service_type
msgid "Fedex Service Type"
msgstr "FedExi teenuse tüüp"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Tutorial"
msgstr "Fedex õpetus"

#. module: delivery_fedex
#: model:delivery.carrier,name:delivery_fedex.delivery_carrier_fedex_us
#: model:product.product,name:delivery_fedex.product_product_delivery_fedex_us
#: model:product.template,name:delivery_fedex.product_product_delivery_fedex_us_product_template
msgid "Fedex US"
msgstr "Fedex US"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Website"
msgstr "Fedex veebileht"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_weight_unit
msgid "Fedex Weight Unit"
msgstr "FedExi kaalühik"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Go to"
msgstr "Mine"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__international_economy
msgid "INTERNATIONAL_ECONOMY"
msgstr "INTERNATIONAL_ECONOMY"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__international_priority
msgid "INTERNATIONAL_PRIORITY"
msgstr "INTERNATIONAL_PRIORITY"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_weight_unit__kg
msgid "KG"
msgstr "KG"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_weight_unit__lb
msgid "LB"
msgstr "LB"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Label Format"
msgstr "Sildi formaat"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_label_stock_type
msgid "Label Type"
msgstr "Sildi tüüp"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_meter_number
msgid "Meter Number"
msgstr ""

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:0
#, python-format
msgid "No packages for this picking"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Once your account is created, go to"
msgstr "Kui konto on loodud, mine"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Options"
msgstr "Seaded"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_4x6
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_4x6
msgid "PAPER_4X6"
msgstr "PAPER_4X6"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_4x6_75
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_4x6_75
msgid "PAPER_4X6.75"
msgstr "PAPER_4X6.75"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_4x8
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_4x8
msgid "PAPER_4X8"
msgstr "PAPER_4X8"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_4x9
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_4x9
msgid "PAPER_4X9"
msgstr "PAPER_4X9"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_7x4_75
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_7x4_75
msgid "PAPER_7X4.75"
msgstr "PAPER_7X4.75"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_8_5x11_bottom_half_label
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_8_5x11_bottom_half_label
msgid "PAPER_8.5X11_BOTTOM_HALF_LABEL"
msgstr "PAPER_8.5X11_BOTTOM_HALF_LABEL"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_8_5x11_top_half_label
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_8_5x11_top_half_label
msgid "PAPER_8.5X11_TOP_HALF_LABEL"
msgstr "PAPER_8.5X11_TOP_HALF_LABEL"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__paper_letter
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__paper_letter
msgid "PAPER_LETTER"
msgstr "PAPER_LETTER"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_file_type__png
msgid "PNG"
msgstr "PNG"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__priority_overnight
msgid "PRIORITY_OVERNIGHT"
msgstr "PRIORITY_OVERNIGHT"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Package Weight Unit"
msgstr "Pakendi kaaluühik"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__fedex_developer_password
msgid "Password"
msgstr "Salasõna"

#. module: delivery_fedex
#: model:ir.model.fields,field_description:delivery_fedex.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Teenusepakkuja"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_droppoff_type__regular_pickup
msgid "REGULAR_PICKUP"
msgstr "REGULAR_PICKUP"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_droppoff_type__request_courier
msgid "REQUEST_COURIER"
msgstr "REQUEST_COURIER"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_duty_payment__recipient
msgid "Recipient"
msgstr "Saaja"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_service_type__standard_overnight
msgid "STANDARD_OVERNIGHT"
msgstr "STANDARD_OVERNIGHT"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_droppoff_type__station
msgid "STATION"
msgstr "STATION"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x6
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x6
msgid "STOCK_4X6"
msgstr "STOCK_4X6"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x6_75
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x6_75
msgid "STOCK_4X6.75"
msgstr "STOCK_4X6.75"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x6_75_leading_doc_tab
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x6_75_leading_doc_tab
msgid "STOCK_4X6.75_LEADING_DOC_TAB"
msgstr "STOCK_4X6.75_LEADING_DOC_TAB"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x6_75_trailing_doc_tab
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x6_75_trailing_doc_tab
msgid "STOCK_4X6.75_TRAILING_DOC_TAB"
msgstr "STOCK_4X6.75_TRAILING_DOC_TAB"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x8
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x8
msgid "STOCK_4X8"
msgstr "STOCK_4X8"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x9
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x9
msgid "STOCK_4X9"
msgstr "STOCK_4X9"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x9_leading_doc_tab
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x9_leading_doc_tab
msgid "STOCK_4X9_LEADING_DOC_TAB"
msgstr "STOCK_4X9_LEADING_DOC_TAB"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_document_stock_type__stock_4x9_trailing_doc_tab
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_stock_type__stock_4x9_trailing_doc_tab
msgid "STOCK_4X9_TRAILING_DOC_TAB"
msgstr "STOCK_4X9_TRAILING_DOC_TAB"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Saturday Delivery"
msgstr "Saturday Delivery"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Screenshot"
msgstr "Ekraanipilt"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_duty_payment__sender
msgid "Sender"
msgstr "Saatja"

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "Saadetis #%s on tühistatud"

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:0
#, python-format
msgid "Shipment created into Fedex <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:0
#, python-format
msgid ""
"Shipment created into Fedex<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""
"Saadetis on loodud Fedex'is<br/><b>Jälgimisnumber:</b> "
"%s<br/><b>Pakendid:</b> %s"

#. module: delivery_fedex
#: model:ir.model,name:delivery_fedex.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Tarneviisid"

#. module: delivery_fedex
#: model:ir.model.fields,help:delivery_fedex.field_delivery_carrier__fedex_saturday_delivery
msgid ""
"Special service:Saturday Delivery, can be requested on following days.\n"
"                                                                                 Thursday:\n"
"1.FEDEX_2_DAY.\n"
"Friday:\n"
"1.PRIORITY_OVERNIGHT.\n"
"2.FIRST_OVERNIGHT.\n"
"                                                                                 3.INTERNATIONAL_PRIORITY.\n"
"(To Select Countries)"
msgstr ""

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "Standard Services"
msgstr "Standardteenused"

#. module: delivery_fedex
#: model:ir.model,name:delivery_fedex.model_stock_package_type
msgid "Stock package type"
msgstr "Ladusta pakendi tüüp"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "The last step is the"
msgstr "Viimane samm on"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid ""
"These certifications usually require that you contact the FedEx support team"
" by email."
msgstr ""

#. module: delivery_fedex
#: model:product.product,uom_name:delivery_fedex.product_product_delivery_fedex_inter
#: model:product.product,uom_name:delivery_fedex.product_product_delivery_fedex_us
#: model:product.template,uom_name:delivery_fedex.product_product_delivery_fedex_inter_product_template
#: model:product.template,uom_name:delivery_fedex.product_product_delivery_fedex_us_product_template
msgid "Units"
msgstr "tk"

#. module: delivery_fedex
#: code:addons/delivery_fedex/models/delivery_fedex.py:0
#, python-format
msgid ""
"Warning:\n"
"%s"
msgstr "Hoiatus: %s"

#. module: delivery_fedex
#: model:ir.model.fields.selection,name:delivery_fedex.selection__delivery_carrier__fedex_label_file_type__zplii
msgid "ZPLII"
msgstr "ZPLII"

#. module: delivery_fedex
#: model_terms:ir.ui.view,arch_db:delivery_fedex.view_delivery_carrier_form_with_provider_fedex
msgid "to create a FedEx account of the following type:"
msgstr ""
