# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_iot
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Spanish (Mexico) (https://www.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Automatically print the shipping labels using this printer."
msgstr "Imprima de forma automática la etiqueta de envío con esta impresora."

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid ""
"Choose the scales you want to use for this operation type. Those scales can "
"be used to weigh the packages created."
msgstr ""
"Seleccione las balanzas que quiere utilizar para esta operación. Estas "
"balanzas se pueden usar para pesar los paquetes creados."

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Asistente de selección de embalaje"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_ip
msgid "Domain Address"
msgstr "Dirección de dominio"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_iot_device
msgid "IOT Device"
msgstr "Dispositivo IOT"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_identifier
msgid "Identifier"
msgstr "Identificador"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.view_picking_type_form_inherit
msgid "IoT"
msgstr "IoT"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.iot_device_view_form_inherit
msgid "Linked Operation Types"
msgstr "Tipos de operaciones vinculadas."

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manual Measurement"
msgstr "Medidas manuales"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Lea las medidas manualmente desde el dispositivo"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_iot_device__picking_type_ids
msgid "Operation Types"
msgstr "Tipos de operación"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipo de recolección"

#. module: delivery_iot
#. openerp-web
#: code:addons/delivery_iot/static/src/xml/iot_widgets_templates.xml:0
#, python-format
msgid "Read weight"
msgstr "Leer el peso"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_id
msgid "Scale"
msgstr "Balanza"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid "Scales"
msgstr "Balanzas"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Shipping Labels Printer"
msgstr "Impresora de etiquetas de envio "

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking
msgid "Transfer"
msgstr "Traslado"
