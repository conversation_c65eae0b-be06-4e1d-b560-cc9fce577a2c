# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_iot
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://www.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Automatically print the shipping labels using this printer."
msgstr "Imprimă automat etichetele de expediere folosind această imprimantă."

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid ""
"Choose the scales you want to use for this operation type. Those scales can "
"be used to weigh the packages created."
msgstr ""
"Alegeți scaunele pe care doriți să le utilizați pentru acest tip de "
"operație. Aceste scaune pot fi utilizate pentru a măsura pachetele create."

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Expert de selecție a pachetului de livrare"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_ip
msgid "Domain Address"
msgstr "Adresa de domeniu"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_iot_device
msgid "IOT Device"
msgstr "Dispozitiv IOT"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_identifier
msgid "Identifier"
msgstr "Identificator"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.view_picking_type_form_inherit
msgid "IoT"
msgstr "IoT"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.iot_device_view_form_inherit
msgid "Linked Operation Types"
msgstr "Tipuri de operații asociate"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manual Measurement"
msgstr "Măsurare manuală"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Citiți manual măsurarea de pe dispozitiv"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_iot_device__picking_type_ids
msgid "Operation Types"
msgstr "Tip operație"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking_type
msgid "Picking Type"
msgstr "Tip ridicare"

#. module: delivery_iot
#. openerp-web
#: code:addons/delivery_iot/static/src/xml/iot_widgets_templates.xml:0
#, python-format
msgid "Read weight"
msgstr "Citește greutatea"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_id
msgid "Scale"
msgstr "Cântar"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid "Scales"
msgstr "Cântare"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Shipping Labels Printer"
msgstr "Imprimantă etichete de expediere"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking
msgid "Transfer"
msgstr "Transfer"
