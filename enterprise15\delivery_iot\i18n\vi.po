# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_iot
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> Than<PERSON>hu<PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Automatically print the shipping labels using this printer."
msgstr "Tự động in nhãn vận chuyển sử dụng máy in này."

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid ""
"Choose the scales you want to use for this operation type. Those scales can "
"be used to weigh the packages created."
msgstr ""
"Chọn cân bạn muốn sử dụng cho loại hoạt động này. Những chiếc cân đó có thể "
"được sử dụng để cân các gói hàng đã tạo."

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Hướng dẫn chọn gói giao hàng"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_ip
msgid "Domain Address"
msgstr "Địa chỉ tên miền"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_iot_device
msgid "IOT Device"
msgstr "Thiết bị IoT"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_identifier
msgid "Identifier"
msgstr "Nhận dạng"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.view_picking_type_form_inherit
msgid "IoT"
msgstr "IoT"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.iot_device_view_form_inherit
msgid "Linked Operation Types"
msgstr "Loại hoạt động liên kết"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manual Measurement"
msgstr "Đo thủ công"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Đọc số đo từ thiết bị theo cách thủ công"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_iot_device__picking_type_ids
msgid "Operation Types"
msgstr "Kiểu hoạt động"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking_type
msgid "Picking Type"
msgstr "Kiểu lấy hàng"

#. module: delivery_iot
#. openerp-web
#: code:addons/delivery_iot/static/src/xml/iot_widgets_templates.xml:0
#, python-format
msgid "Read weight"
msgstr "Đọc khối lượng"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_id
msgid "Scale"
msgstr "Cân"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid "Scales"
msgstr "Cân"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Shipping Labels Printer"
msgstr "Máy in nhãn vận chuyển"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking
msgid "Transfer"
msgstr "Chuyển"
