# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_iot
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Automatically print the shipping labels using this printer."
msgstr ""

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,help:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid ""
"Choose the scales you want to use for this operation type. Those scales can "
"be used to weigh the packages created."
msgstr "選擇要用於此操作類型的磅秤。這些磅秤可用於為建立的包裹進行秤重。"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "交貨包裹選擇嚮導"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_ip
msgid "Domain Address"
msgstr "網域地址"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_iot_device
msgid "IOT Device"
msgstr "IOT 設備"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_identifier
msgid "Identifier"
msgstr "標識"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.view_picking_type_form_inherit
msgid "IoT"
msgstr "物聯網"

#. module: delivery_iot
#: model_terms:ir.ui.view,arch_db:delivery_iot.iot_device_view_form_inherit
msgid "Linked Operation Types"
msgstr "已連接的操作類型"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manual Measurement"
msgstr "手動量度"

#. module: delivery_iot
#: model:ir.model.fields,help:delivery_iot.field_choose_delivery_package__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "從裝置手動讀取量度值"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_iot_device__picking_type_ids
msgid "Operation Types"
msgstr "作業類型"

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking_type
msgid "Picking Type"
msgstr "提貨類型"

#. module: delivery_iot
#. openerp-web
#: code:addons/delivery_iot/static/src/xml/iot_widgets_templates.xml:0
#, python-format
msgid "Read weight"
msgstr "讀取重量"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__iot_device_id
msgid "Scale"
msgstr "比例"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_choose_delivery_package__available_scale_ids
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_scale_ids
msgid "Scales"
msgstr "磅秤"

#. module: delivery_iot
#: model:ir.model.fields,field_description:delivery_iot.field_stock_picking_type__iot_printer_id
msgid "Shipping Labels Printer"
msgstr ""

#. module: delivery_iot
#: model:ir.model,name:delivery_iot.model_stock_picking
msgid "Transfer"
msgstr "調撥"
