<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_picking_type_form_inherit" model="ir.ui.view">
        <field name="name">stock.picking.type.inherit</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock.view_picking_type_form"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet" position="inside">
                <group>
                    <group string="IoT" attrs='{"invisible": [("code", "not in", ["incoming", "outgoing", "internal"])]}'>
                        <field name="iot_scale_ids" widget="many2many_tags"/>
                        <field name="iot_printer_id"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
