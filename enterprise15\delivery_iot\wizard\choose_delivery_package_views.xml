<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="choose_delivery_package_view_form_inherit" model="ir.ui.view">
        <field name="name">choose.delivery.package.form.inherit</field>
        <field name="model">choose.delivery.package</field>
        <field name="inherit_id" ref="delivery.choose_delivery_package_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='package_weight']" position="after">
                <field name="iot_ip" invisible="1"/>
                <field name="available_scale_ids" invisible="1"/>
                <field name="iot_device_identifier" invisible="1"/>
                <field name="manual_measurement" invisible="1"/>
                <field name="iot_device_id" widget="field_many2one_iot_scale"
                    attrs="{'invisible': [('delivery_package_type_id', '=', False)]}"
                    options="{'value_field': 'shipping_weight', 'identifier': 'iot_device_identifier', 'ip_field': 'iot_ip', 'manual_measurement_field': 'manual_measurement'}"
                    domain="[('id', 'in', available_scale_ids)]"/>
            </xpath>
        </field>
    </record>
</odoo>
