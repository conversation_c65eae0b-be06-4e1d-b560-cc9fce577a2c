<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.ups.com/XMLSchema/XOLTWS/Error/v1.1" xmlns:error="http://www.ups.com/XMLSchema/XOLTWS/Error/v1.1" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" version="201707">
	<xsd:element name="Errors">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="ErrorDetail" type="error:ErrorDetailType" maxOccurs="unbounded"/>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
	<xsd:complexType name="ErrorDetailType">
		<xsd:sequence>
			<xsd:element name="Severity" type="xsd:string"/>
			<xsd:element name="PrimaryErrorCode" type="error:CodeType"/>
			<xsd:element name="MinimumRetrySeconds" type="xsd:string" minOccurs="0"/>
			<xsd:element name="Location" type="error:LocationType" minOccurs="0"/>
			<xsd:element name="SubErrorCode" type="error:CodeType" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="AdditionalInformation" type="error:AdditionalInfoType" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="ElementLevelInformation" type="error:ElementLevelInformationType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ElementLevelInformationType">
		<xsd:sequence>
			<xsd:element name="Level" type="xsd:string"/>
			<xsd:element name="ElementIdentifier" type="error:ElementIdentifierType" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ElementIdentifierType">
		<xsd:sequence>
			<xsd:element name="Code" type="xsd:string"/>
			<xsd:element name="Value" type="xsd:string"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CodeType">
		<xsd:sequence>
			<xsd:element name="Code" type="xsd:string"/>
			<xsd:element name="Description" type="xsd:string"/>
			<xsd:element name="Digest" type="xsd:string" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AdditionalInfoType">
		<xsd:sequence>
			<xsd:element name="Type" type="xsd:string"/>
			<xsd:element name="Value" type="error:AdditionalCodeDescType" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AdditionalCodeDescType">
		<xsd:sequence>
			<xsd:element name="Code" type="xsd:string"/>
			<xsd:element name="Description" type="xsd:string" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="LocationType">
		<xsd:sequence>
			<xsd:element name="LocationElementName" type="xsd:string" minOccurs="0"/>
			<xsd:element name="XPathOfElement" type="xsd:string" minOccurs="0"/>
			<xsd:element name="OriginalValue" type="xsd:string" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
