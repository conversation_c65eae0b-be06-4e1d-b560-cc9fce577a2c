<?xml version="1.0" encoding="UTF-8"?>
<!-- UPS Rate Service WSDL Release Date Dec 29, 2007 -->
<!-- Copyright 2007-2008 United Parcel Service of America, Inc. All rights reserved.   -->
<wsdl:definitions name="RateWS" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:error="http://www.ups.com/XMLSchema/XOLTWS/Error/v1.1" xmlns:upss="http://www.ups.com/XMLSchema/XOLTWS/UPSS/v1.0" xmlns:rate="http://www.ups.com/XMLSchema/XOLTWS/Rate/v1.1" xmlns:tns="http://www.ups.com/WSDL/XOLTWS/Rate/v1.1" targetNamespace="http://www.ups.com/WSDL/XOLTWS/Rate/v1.1">
	<wsdl:types>
		<xsd:schema>
			<!-- This schema defines the UPS Security header used for authorization purposes -->
			<xsd:import namespace="http://www.ups.com/XMLSchema/XOLTWS/UPSS/v1.0" schemaLocation="UPSSecurity.xsd"/>
			<!--  This schema defines the error detail data types returned within SOAPFaults to provide more specific information pertaining to the problem. -->
			<xsd:import namespace="http://www.ups.com/XMLSchema/XOLTWS/Error/v1.1" schemaLocation="Error1.1.xsd"/>
			<!-- This schema defines the Rate service data types -->
			<xsd:import namespace="http://www.ups.com/XMLSchema/XOLTWS/Rate/v1.1" schemaLocation="RateWebServiceSchema.xsd"/>
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="RateRequestMessage">
		<wsdl:part name="Body" element="rate:RateRequest"/>
		<wsdl:part name="UPSSecurity" element="upss:UPSSecurity"/>
	</wsdl:message>
	<wsdl:message name="RateResponseMessage">
		<wsdl:part name="Body" element="rate:RateResponse"/>
	</wsdl:message>
	<wsdl:message name="RateErrorMessage">
		<wsdl:part name="RateError" element="error:Errors"/>
	</wsdl:message>
	<wsdl:portType name="RatePortType">
		<wsdl:operation name="ProcessRate">
			<wsdl:input name="RateRequest" message="tns:RateRequestMessage"/>
			<wsdl:output name="RateResponse" message="tns:RateResponseMessage"/>
			<wsdl:fault name="RateError" message="tns:RateErrorMessage"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="RateBinding" type="tns:RatePortType">
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="ProcessRate">
			<soap:operation soapAction="http://onlinetools.ups.com/webservices/RateBinding/v1.1" style="document"/>
			<wsdl:input name="RateRequest">
				<soap:body parts="Body" use="literal"/>
				<soap:header message="tns:RateRequestMessage" part="UPSSecurity" use="literal">
					<soap:headerfault message="tns:RateErrorMessage" part="RateError" use="literal"/>
				</soap:header>
			</wsdl:input>
			<wsdl:output name="RateResponse">
				<soap:body parts="Body" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="RateError">
				<soap:fault name="RateError" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="RateService">
		<wsdl:port name="RatePort" binding="tns:RateBinding">
			<!-- Production URL -->
			<!-- <soap:address location="https://onlinetools.ups.com/webservices/Rate"/> -->
			<!-- CIE (Customer Integration Environment) URL -->
			<soap:address location="https://wwwcie.ups.com/webservices/Rate"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
