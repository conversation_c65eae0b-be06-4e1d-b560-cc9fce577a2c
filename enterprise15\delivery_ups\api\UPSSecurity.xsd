<xsd:schema targetNamespace="http://www.ups.com/XMLSchema/XOLTWS/UPSS/v1.0" xmlns:upss="http://www.ups.com/XMLSchema/XOLTWS/UPSS/v1.0" xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<xsd:element name="UPSSecurity">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="UsernameToken">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="Username" type="xsd:string"/>
							<xsd:element name="Password" type="xsd:string"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
				<xsd:element name="ServiceAccessToken">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="AccessLicenseNumber" type="xsd:string"/>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>
