<?xml version="1.0" encoding="UTF-8"?>
<!-- UPS Void Shipment Service WSDL Release Date Mar 11, 2008 -->
<!-- Copyright 2007-2008 United Parcel Service of America, Inc. All rights reserved.   -->
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:error="http://www.ups.com/XMLSchema/XOLTWS/Error/v1.1" xmlns:upss="http://www.ups.com/XMLSchema/XOLTWS/UPSS/v1.0" xmlns:common="http://www.ups.com/XMLSchema/XOLTWS/Common/v1.0" xmlns:void="http://www.ups.com/XMLSchema/XOLTWS/Void/v1.1" xmlns:tns="http://www.ups.com/WSDL/XOLTWS/Void/v1.1" targetNamespace="http://www.ups.com/WSDL/XOLTWS/Void/v1.1">
	<wsdl:types>
		<xsd:schema>
			<!-- This schema defines the UPS Security header used for authorization purposes -->
			<xsd:import namespace="http://www.ups.com/XMLSchema/XOLTWS/UPSS/v1.0" schemaLocation="UPSSecurity.xsd"/>
			<!--  This schema defines the error detail data types returned within SOAPFaults to provide more specific information pertaining to the problem. -->
			<xsd:import namespace="http://www.ups.com/XMLSchema/XOLTWS/Error/v1.1" schemaLocation="Error1.1.xsd"/>
			<!-- This schema defines the Void Shipment service data types -->
			<xsd:import namespace="http://www.ups.com/XMLSchema/XOLTWS/Void/v1.1" schemaLocation="VoidWebServiceSchema.xsd"/>
		</xsd:schema>
	</wsdl:types>
	<wsdl:message name="VoidRequestMessage">
		<wsdl:part name="Body" element="void:VoidShipmentRequest"/>
		<wsdl:part name="UPSSecurity" element="upss:UPSSecurity"/>
	</wsdl:message>
	<wsdl:message name="VoidResponseMessage">
		<wsdl:part name="Body" element="void:VoidShipmentResponse"/>
	</wsdl:message>
	<wsdl:message name="VoidErrorMessage">
		<wsdl:part name="VoidError" element="error:Errors"/>
	</wsdl:message>
	<wsdl:portType name="VoidPortType">
		<wsdl:operation name="ProcessVoid">
			<wsdl:input name="VoidShipmentRequest" message="tns:VoidRequestMessage"/>
			<wsdl:output name="VoidShipmentResponse" message="tns:VoidResponseMessage"/>
			<wsdl:fault name="VoidError" message="tns:VoidErrorMessage"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="VoidBinding" type="tns:VoidPortType">
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="ProcessVoid">
			<soap:operation soapAction="http://onlinetools.ups.com/webservices/VoidBinding/v1.1" style="document"/>
			<wsdl:input name="VoidShipmentRequest">
				<soap:body parts="Body" use="literal"/>
				<soap:header message="tns:VoidRequestMessage" part="UPSSecurity" use="literal">
					<soap:headerfault message="tns:VoidErrorMessage" part="VoidError" use="literal"/>
				</soap:header>
			</wsdl:input>
			<wsdl:output name="VoidShipmentResponse">
				<soap:body parts="Body" use="literal"/>
			</wsdl:output>
			<wsdl:fault name="VoidError">
				<soap:fault name="VoidError" use="literal"/>
			</wsdl:fault>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="VoidService">
		<wsdl:port name="VoidPort" binding="tns:VoidBinding">
			<!-- Production URL -->
			<!-- <soap:address location="https://onlinetools.ups.com/webservices/Void"/> -->
			<!-- CIE (Customer Integration Environment) URL -->
			<soap:address location="https://wwwcie.ups.com/webservices/Void"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
