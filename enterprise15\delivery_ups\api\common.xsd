<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema targetNamespace="http://www.ups.com/XMLSchema/XOLTWS/Common/v1.0" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:ups="http://www.ups.com/XMLSchema" xmlns:common="http://www.ups.com/XMLSchema/XOLTWS/Common/v1.0" elementFormDefault="qualified" version="201707">
	<xsd:element name="Request" type="common:RequestType"/>
	<xsd:element name="Response" type="common:ResponseType"/>
	<xsd:element name="ClientInformation" type="common:ClientInformationType"/>
	<xsd:complexType name="ClientInformationType">
		<xsd:sequence>
			<xsd:element name="Property" minOccurs="0" maxOccurs="unbounded">
				<xsd:complexType>
					<xsd:simpleContent>
						<xsd:extension base="xsd:string">
							<xsd:attribute name="Key" type="xsd:string" use="required"/>
						</xsd:extension>
					</xsd:simpleContent>
				</xsd:complexType>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="RequestType">
		<xsd:sequence>
			<xsd:element name="RequestOption" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="SubVersion" type="xsd:string" minOccurs="0"/>
			<xsd:element name="TransactionReference" type="common:TransactionReferenceType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="TransactionReferenceType">
		<xsd:sequence>
			<xsd:element name="CustomerContext" type="xsd:string" minOccurs="0"/>
			<xsd:element name="TransactionIdentifier" type="xsd:string" minOccurs="0" ups:usage="notused"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ResponseType">
		<xsd:sequence>
			<xsd:element name="ResponseStatus" type="common:CodeDescriptionType"/>
			<xsd:element name="Alert" type="common:CodeDescriptionType" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="AlertDetail" type="common:DetailType" minOccurs="0" maxOccurs="unbounded"/>
			<xsd:element name="TransactionReference" type="common:TransactionReferenceType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="CodeDescriptionType">
		<xsd:sequence>
			<xsd:element name="Code" type="xsd:string"/>
			<xsd:element name="Description" type="xsd:string"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DetailType">
		<xsd:sequence>
			<xsd:element name="Code" type="xsd:string"/>
			<xsd:element name="Description" type="xsd:string"/>
			<xsd:element name="ElementLevelInformation" type="common:ElementLevelInformationType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ElementLevelInformationType">
		<xsd:sequence>
			<xsd:element name="Level" type="xsd:string"/>
			<xsd:element name="ElementIdentifier" type="common:ElementIdentifierType" minOccurs="0" maxOccurs="unbounded"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ElementIdentifierType">
		<xsd:sequence>
			<xsd:element name="Code" type="xsd:string"/>
			<xsd:element name="Value" type="xsd:string"/>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
