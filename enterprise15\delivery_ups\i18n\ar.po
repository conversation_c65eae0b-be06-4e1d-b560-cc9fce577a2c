# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_ups
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"A shipment cannot have a KGS/IN or LBS/CM as its unit of measurements. "
"Configure it from the delivery method."
msgstr ""
"لا يمكن أن يكون للشحنو وحدات قياس كجم/إنش أو رطل/سم. قم بتهيئتها من طريقة "
"التوصيل. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Access License number is Invalid. Provide a valid number (Length should be "
"0-35 alphanumeric characters)"
msgstr ""
"رقم رخصة الوصول غير صالح. الرجاء تحديد رقم صالح (يجب أن يكون الطول بين 0-35 "
"خانة من الحروف والأرقام) "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this delivery provider."
msgstr "رقم رخصة الوصول غير صالح لمزود التوصيل هذا. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this provider.Please re-license."
msgstr "رقم رخصة الوصول غير صالح لهذا المزود. الرجاء إعادة تعيين الرخصة. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is revoked contact UPS to get access."
msgstr "رقم رخصة الوصول ملغي. يرجى التواصل مع UPS للحصول على صلاحية الوصول. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Authorization system is currently unavailable , try again later."
msgstr "نظام التصريح غير متوفر حالياً، الرجاء المحاولة مجدداً لاحقاً. "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr "إصدار الفاتورة على حسابي"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr "خيار التمويل COD"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Cancel shipment not available at this time , Please try again Later."
msgstr "خيار إلغاء الشحنة غير متوفر حالياً. الرجاء المحاولة مجدداً لاحقاً. "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "شركة الشحن"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr "شيك مقبول الدفع أو حوالة نقدية"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr "سنتيمترات"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr "شيك بنكي، أو شيك مقبول الدفع أو حوالة نقدية"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr "الدفع عند الاستلام"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Duties paid by"
msgstr "تُدفع الجمارك من قِبَل "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr "EPL"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"خطأ:\n"
"%s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Exceeds Total Number of allowed pieces per World Wide Express Shipment."
msgstr "يفوق الرقم الإجمالي للقطع المسموح بها لكل شحنة للشحن الدولي السريع. "

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""
"إذا تم تحديده، سيُطلب من مستخدمي المتجر الإلكتروني تسجيل أرقام حساباتهم على UPS\n"
"ليتم تحصيل رسوم التسليم منها. "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr "إنشات"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr "كيلوجرامات"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Label Format"
msgstr "تنسيق بطاقة العنوان "

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Options"
msgstr "الخيارات "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr "PDF "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr "وحدة حجم الحزمة "

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Package Weight Unit"
msgstr "وحدة وزن الحزمة "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Packages %s do not have a positive shipping weight."
msgstr "وزن الحزم %s ليس رقماً إيجابياً. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid City in the warehouse address."
msgstr "الرجاء كتابة اسم مدينة صالح في عنوان المستودع. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in recipient's address."
msgstr "الرجاء كتابة اسم دولة صالح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in the warehouse address."
msgstr "الرجاء كتابة اسم دولة صالح في عنوان المستودع. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid State in the warehouse address."
msgstr "الرجاء كتابة اسم ولاية صحيح في عنوان المستودع. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Zip in the warehouse address."
msgstr "الرجاء كتابة Zip صالح في عنوان المستودع. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the recipient address."
msgstr "الرجاء كتابة اسم مدينة صالح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the shipper's address."
msgstr "الرجاء كتابة اسم مدينة صالح في عنوان الشاحن. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid country in the shipper's address."
msgstr "الرجاء كتابة اسم دولة صالح في عنوان الشاحن. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Please provide a valid package type available for service and selected "
"locations."
msgstr "الرجاء تحديد نوع حزمة صالح متوفر للخدمة والموقع المختارين. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid phone number for the recipient."
msgstr "الرجاء كتابة رقم هاتف صالح للمستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper Number/Carrier Account."
msgstr "الرجاء كتابة رقم الشاحن/حساب شركة الشحن الصحيح. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper number/Carrier Account."
msgstr "الرجاء كتابة رقم الشاحن/حساب شركة الشحن الصحيح. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper phone number."
msgstr "الرجاء كتابة رقم هاتف صالح للشاحن. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the recipient address."
msgstr "الرجاء كتابة اسم ولاية صالح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the shipper's address."
msgstr "الرجاء كتابة اسم ولاية صالح في عنوان الشاحن. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in shipper's address."
msgstr "الرجاء كتابة اسم شارع صالح في عنوان الشاحن. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the recipient address."
msgstr "الرجاء كتابة اسم شارع صالح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the warehouse address."
msgstr "الرجاء كتابة اسم شارع صحيح في عنوان المستودع. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid warehouse Phone Number"
msgstr "الرجاء كتابة رقم هاتف صحيح للمستودع "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the recipient address."
msgstr "الرجاء كتابة رمز zip صحيح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the shipper's address."
msgstr "الرجاء كتابة رمز zip صحيح في عنوان الشاحن. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the warehouse address."
msgstr "الرجاء كتابة رمز zip صحيح في عنوان المستودع. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zipcode in the recipient address."
msgstr "الرجاء كتابة رمز zip صحيح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship"
msgstr "الرجاء تحديد عنصر واحد على الأقل لشحنه "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "يرجى اختيار عنصر واحد على الأقل لشحنه."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the recipient address."
msgstr "الرجاء كتابة اسم دولة صحيح في عنوان المستلم. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the warehouse address."
msgstr "الرجاء كتابة اسم دولة صحيح في عنوان المستودع. "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr "أرطال"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Access License Number not found in the UPS database"
msgstr "لم يتم العثور على رقم رخصة الوصول هذا في قاعدة بيانات UPS "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Tracking Ref. Number is invalid."
msgstr "الرقم المرجعي للتتبع غير صحيح. "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "المزود"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr "المستلم"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr ""
"يجب أن يتكون رقم هاتف المستلم من 10 خانات على الأقل منها أحرف وأرقام. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension cannot exceed the length of 4."
msgstr "لا يمكن أن يتخطى طول وصلة هاتف المستلم 4 خانات. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension must contain only numbers."
msgstr "يجب أن تحتوي وصلة هاتف المستلم على أرقام فقط. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Return label generated<br/><b>Tracking Numbers:</b> %s<br/><b>Packages:</b> "
"%s"
msgstr ""
"تم إنشاء بطاقة عنوان الإرجاع<br/><b>أرقام التتبع:</b> %s<br/><b>الحزم:</b> "
"%s"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr "SPL"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sales Order"
msgstr "أمر البيع"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr "التوصيل يوم السبت "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr "المرسل"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "لقد تم إلغاء الشحنة رقم %s  "

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""
"تم إنشاء الشحنة في UPS<br/> <b>رقم التتبع:</b> %s<br/><b>الحِزَم:</b> %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr ""
"يجي أن يتكون رقم هاتف الشاحم من 10 خانات على الأقل من الأحرف والأرقام. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper number must contain alphanumeric characters only."
msgstr "يجب أن يحتوي رقم الشاحن على خانات من أحرف وأرقام فقط. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension cannot exceed the length of 4."
msgstr "لا يمكن أن يتخطى طول وصلة هاتف الشاحن 4 خانات. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension must contain only numbers."
msgstr "يجب أن تحتوي وصلة هاتف الشاحن على أرقام فقط. "

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Shipping Methods"
msgstr "طُرُق الشَّحن"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_package_type
msgid "Stock package type"
msgstr "نوع طرود المخزون "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The UserId is currently locked out; please try again in 24 hours."
msgstr "معرف المستخدم مقفل حالياً؛ الرجاء المحاولة مجدداً بعد 24 ساعة. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"عنوان شركة مفقود أو خاطئ.\n"
"(الحقل (الحقول)المفقودة: %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"عنوان المستودع الخاص بك مفقود أو خاطئ.\n"
"(الحقل (الحقول) المفقودة: %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr "لا يمكن إتمام عملية التوصيل لأن وزن منتجك مفقود. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"تعذر احتساب سعر الشحن لعدم تحديد الوزن للمنتج (المنتجات) التالية: \n"
" %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The maximum number of user access attempts was exceeded. So please try again"
" later"
msgstr ""
"لقد تخطيت الحد الأقصى المسموح به لمحاولات الوصول. الرجاء المحاولة من جديد "
"لاحقاً "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"عنوان المستلم مفقود أو خاطئ.\n"
"(الحقل (الحقول) المفقودة: %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The requested service is unavailable between the selected locations."
msgstr "الخدمة المطوبة غير متاحة حالياً بين المواقع المحددة. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid from the requested warehouse, please choose "
"another service."
msgstr ""
"الخدمة المحددة غير صالحة من المستودع المطلوب. الرجاء اختيار خدمة أخرى. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid to the recipient address, please choose "
"another service."
msgstr "الخدمة المحددة غير صالحة إلى عنوان المستلم. الرجاء اختيار خدمة أخرى. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is not possible from your warehouse to the recipient "
"address, please choose another service."
msgstr ""
"الخدمة المحددة غير ممكنة من مستودعك إلى عنوان المستلم. الرجاء اختيار خدمة "
"أخرى. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The selected service is not valid with the selected packaging."
msgstr "الخدمة المحددة غير صالحة مع طريقة التعبئة المحددة. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from delivery method"
msgstr ""
"نظام القياسات هذا غير صالح للدولة المحددة. الرجاء التبديل من رطل/إنش إلى "
"كجم/سم (أو العكس). قم بتهيئتها من طريقة التوصيل. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from the delivery "
"method."
msgstr ""
"نظام القياسات هذا غير صالح للدولة المحددة. الرجاء التبديل من رطل/إنش إلى "
"كجم/سم (أو العكس). قم بتهيئتها من طريقة التوصيل. "

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""
"تقوم خدمة القيمة المضافة هذه بتفعيل نظام UPS لتحصيل مدفوعات الشحنة من عميلك."
" "

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr "ستتيح لك خدمة القيمة المضافة هذه شحن الطرد يوم السبت أيضاً. "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__delivery_type__ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__stock_package_type__package_carrier_type__ups
msgid "UPS"
msgstr "UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS Access Key"
msgstr "مفتاح الوصول الخاص بـ UPS "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr "رقم حساب UPS "

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.product,name:delivery_ups.product_product_delivery_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr "UPS بلجيكا"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr "إعدادات UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr "نوع ملف بطاقة عنوان UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_package_type_id
msgid "UPS Package Type"
msgstr "نوع حزمة UPS "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr "كلمة مرور UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr "توصيل UPS يوم السبت "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "UPS Server Not Found"
msgstr "لم يتم العثور على خادم UPS "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr "نوع خدمة UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
msgid "UPS Shipper Number"
msgstr "رقم شاحن UPS "

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Shipping Methods"
msgstr "طرق شحن UPS "

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.product,name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr "UPS الولايات المتحدة الأمريكية "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr "اسم مستخدم UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr "رقم حساب UPS "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"UPS address lines can only contain a maximum of 35 characters. You can split"
" the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""
"يمكن أن تحتوي بنود عنوان UPS على 35 خانة كحد أقصى. بإمكانك تجزئة عناوين جهات"
" الاتصال لعدة بنود لتجنب تلك المحدودية. "

#. module: delivery_ups
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_be
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_be_product_template
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "Units"
msgstr "الوحدات"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr "دفع جمارك UPS "

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr "وحدة وزن حزم Ups"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Username/Password is invalid for this delivery provider."
msgstr "اسم المستخدم/كلمة المرور غير صحيحة لمزود التوصيل هذا. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr ""
"يجب أن يتكون رقم هاتف المستودع من 10 خانات على الأقل من الأحرف والأرقام. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must contain only numbers."
msgstr "يجب أن يحتوي رقم هاتف المستودع على أرقام فقط. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse PhoneExtension cannot exceed the length of 4."
msgstr "يجب ألا تتعدى وصلة هاتف المستودع 4 خانات. "

#. module: delivery_ups
#: code:addons/delivery_ups/models/sale.py:0
#, python-format
msgid "You must enter an UPS account number."
msgstr "عليك إدخال رقم حساب UPS "

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"
