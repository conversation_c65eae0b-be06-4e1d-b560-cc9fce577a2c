# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_ups
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON>r<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"A shipment cannot have a KGS/IN or LBS/CM as its unit of measurements. "
"Configure it from the delivery method."
msgstr ""
"Lähetyksen mittayksikkö ei voi olla KGS/IN tai LBS/CM. Määritä se "
"toimitustavasta."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Access License number is Invalid. Provide a valid number (Length should be "
"0-35 alphanumeric characters)"
msgstr ""
"Pääsyn lisenssin numero on virheellinen. Anna kelvollinen numero (Pituuden "
"tulisi olla 0-35 aakkosnumeerista merkkiä)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this delivery provider."
msgstr "Pääsyn lisenssin numero ei ole voimassa tälle palveluntarjoajalle."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this provider.Please re-license."
msgstr ""
"Pääsyn lisenssin numero ei ole voimassa tälle palveluntarjoajalle. Ole hyvä "
"ja lisensoi uudelleen."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is revoked contact UPS to get access."
msgstr ""
"Pääsyn lisenssinumero on peruutettu ota yhteyttä UPS:iin saadaksesi pääsyn."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Authorization system is currently unavailable , try again later."
msgstr ""
"Valtuutusjärjestelmä ei ole tällä hetkellä käytettävissä, yritä myöhemmin "
"uudelleen."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr "Laskuta omaa tiliäni"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr "Postiennakko-rahoitusvaihtoehto"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Cancel shipment not available at this time , Please try again Later."
msgstr ""
"Peruuta lähetys ei ole tällä hetkellä saatavilla. Yritä myöhemmin uudelleen."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Huolitsija"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr "Käteisšekki tai rahatilaus"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr "Senttimetriä"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr "Šekki, kassashekki tai rahamääräys"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr "Postiennakko"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Duties paid by"
msgstr "Tullit maksaa"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr "EPL"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"Virhe:\n"
"%s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Exceeds Total Number of allowed pieces per World Wide Express Shipment."
msgstr ""
"Ylittää sallittujen kappaleiden kokonaismäärän World Wide Express "
"-lähetyksessä."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""
"Jos valintaruutu on valittuna, verkkokaupan käyttäjiltä kysytään UPS-tilinumeroa\n"
"ja toimitusmaksut veloitetaan sen perusteella."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr "Tuumaa"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr "Kg"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Label Format"
msgstr "Etiketin muoto"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Options"
msgstr "Vaihtoehdot"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr "PDF"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr "Pakkauskoon yksikkö"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Package Weight Unit"
msgstr "Pakkauksen painoyksikkö"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Packages %s do not have a positive shipping weight."
msgstr "Paketeilla %s ei ole positiivista lähetyspainoa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid City in the warehouse address."
msgstr "Ilmoita varasto-osoitteessa voimassa oleva kaupunki."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in recipient's address."
msgstr "Anna vastaanottajan osoitteessa voimassa oleva maa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in the warehouse address."
msgstr "Ilmoita varasto-osoitteessa voimassa oleva maa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid State in the warehouse address."
msgstr "Ilmoita varasto-osoitteessa voimassa oleva osavaltio."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Zip in the warehouse address."
msgstr "Anna varasto-osoitteessa voimassa oleva postinumero."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the recipient address."
msgstr "Ilmoita vastaanottajan osoitteessa kelvollinen kaupunki."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the shipper's address."
msgstr "Ilmoita lähettäjän osoitteessa voimassa oleva kaupunki."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid country in the shipper's address."
msgstr "Ilmoita voimassa oleva maa lähettäjän osoitteessa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Please provide a valid package type available for service and selected "
"locations."
msgstr ""
"Ilmoita voimassa oleva pakettityyppi, joka on saatavilla palvelua ja "
"valittuja sijainteja varten."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid phone number for the recipient."
msgstr "Anna vastaanottajan voimassa oleva puhelinnumero."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper Number/Carrier Account."
msgstr "Ilmoita voimassa oleva lähettäjän numero tai kuljetustili."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper number/Carrier Account."
msgstr "Ilmoita voimassa oleva lähettäjän numero tai kuljetustili."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper phone number."
msgstr "Anna voimassa oleva lähettäjän puhelinnumero."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the recipient address."
msgstr "Anna vastaanottajan osoitteessa voimassa oleva osavaltio."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the shipper's address."
msgstr "Ilmoita lähettäjän osoitteessa voimassa oleva osavaltio."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in shipper's address."
msgstr "Ilmoita voimassa oleva katu lähettäjän osoitteessa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the recipient address."
msgstr "Anna vastaanottajan osoitteessa kelvollinen katu."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the warehouse address."
msgstr "Anna varasto-osoitteessa kelvollinen katu."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid warehouse Phone Number"
msgstr "Anna voimassa oleva varaston puhelinnumero"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the recipient address."
msgstr "Ilmoita vastaanottajan osoitteessa voimassa oleva postinumero."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the shipper's address."
msgstr "Ilmoita voimassa oleva postinumero lähettäjän osoitteessa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the warehouse address."
msgstr "Anna varasto-osoitteessa voimassa oleva postinumero."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zipcode in the recipient address."
msgstr "Ilmoita vastaanottajan osoitteessa voimassa oleva postinumero."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship"
msgstr "Anna vähintään yksi lähetettävä tuote"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Anna vähintään yksi lähetettävä tuote."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the recipient address."
msgstr "Aseta vastaanottajan osoitteeseen kelvollinen maa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the warehouse address."
msgstr "Aseta varasto-osoitteeseen kelvollinen maa."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr "Paunaa"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Access License Number not found in the UPS database"
msgstr "UPS:n tietokannasta ei löydy annettua käyttöoikeuskoodin numeroa"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Tracking Ref. Number is invalid."
msgstr "Annettu seurantaviite on virheellinen."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Palveluntarjoaja"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr "Vastaanottaja"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr ""
"Vastaanottajan puhelimen on oltava vähintään 10 aakkosnumeerista merkkiä."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension cannot exceed the length of 4."
msgstr ""
"Vastaanottajan puhelinnumeron alanumero ei voi olla pidempi kuin neljä "
"numeroa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension must contain only numbers."
msgstr "Vastaanottajan puhelinnumeon alanumero saa sisältää vain numeroita."

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Return label generated<br/><b>Tracking Numbers:</b> %s<br/><b>Packages:</b> "
"%s"
msgstr ""
"Luotu palautusetiketti<br/><b>Seurantanumerot</b>: %s<br/><b>Paketit</b>: %s"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr "SPL"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr "Lauantaitoimitus"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr "Lähettäjä"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "Lähetys #%s on peruutettu"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""
"Lähetys luotu UPS:n<br/><b>seurantanumeroihin</b>: %s<br/><b>Paketit</b>: %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr "Lähettäjän puhelimen on oltava vähintään 10 aakkosnumeerista merkkiä."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper number must contain alphanumeric characters only."
msgstr "Lähettäjän numero saa sisältää ainoastaan aakkosnumeerisia merkkejä."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension cannot exceed the length of 4."
msgstr ""
"Lähettäjän puhelinnumeron alanumeron pituus voi olla enintään neljä merkkiä."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension must contain only numbers."
msgstr "Lähettäjän puhelinliittymän on sisällettävä vain numeroita."

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Toimitustavat"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_package_type
msgid "Stock package type"
msgstr "Varastopaketin tyyppi"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The UserId is currently locked out; please try again in 24 hours."
msgstr "UserId on tällä hetkellä lukittu; yritä uudelleen 24 tunnin kuluttua."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Yrityksesi osoite puuttuu tai on väärä.\n"
"(Puuttuvat kentät: %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Varastosi osoite puuttuu tai on väärä.\n"
"(Puuttuvat kentät: %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr "Toimitusta ei voida suorittaa, koska tuotteesi paino puuttuu."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Arvioitua toimitushintaa ei voida laskea, koska seuraavan tuotteen (seuraavien tuotteiden) paino puuttuu:\n"
" %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The maximum number of user access attempts was exceeded. So please try again"
" later"
msgstr ""
"Käyttäjän käyttöyritysten enimmäismäärä ylitettiin. Yritä myöhemmin "
"uudelleen"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Vastaanottajan osoite puuttuu tai on väärä.\n"
"(Puuttuvat kentät : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The requested service is unavailable between the selected locations."
msgstr "Pyydetty palvelu ei ole käytettävissä valittujen paikkojen välillä."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid from the requested warehouse, please choose "
"another service."
msgstr "Valittu palvelu ei käy pyydetystä varastosta, valitse toinen palvelu."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid to the recipient address, please choose "
"another service."
msgstr ""
"Valittu palvelu ei käy vastaanottajan osoitteeseen, valitse toinen palvelu."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is not possible from your warehouse to the recipient "
"address, please choose another service."
msgstr ""
"Valittu palvelu ei ole mahdollinen varastostasi vastaanottajan osoitteeseen,"
" valitse toinen palvelu."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The selected service is not valid with the selected packaging."
msgstr "Valittu palvelu ei ole voimassa valitun pakkauksen kanssa."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from delivery method"
msgstr ""
"Tämä mittausjärjestelmä ei ole voimassa valitussa maassa. Vaihda LBS/IN-"
"järjestelmästä KGS/CM-järjestelmään (tai päinvastoin). Määritä se "
"toimitustavasta"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from the delivery "
"method."
msgstr ""
"Tämä mittausjärjestelmä ei ole voimassa valitussa maassa. Vaihda LBS/IN-"
"järjestelmästä KGS/CM-järjestelmään (tai päinvastoin). Määritä se "
"toimitustavasta."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""
"Tämän lisäarvopalvelun avulla UPS voi periä lähetyksen maksun asiakkaaltasi."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr "Tämän lisäarvopalvelun avulla voit lähettää paketin myös lauantaina."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__delivery_type__ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__stock_package_type__package_carrier_type__ups
msgid "UPS"
msgstr "UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS Access Key"
msgstr "UPS:n pääsyavain"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr "UPS-tilin numero"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.product,name:delivery_ups.product_product_delivery_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr "UPS BE"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr "UPS-asetukset"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr "UPS-tarran tiedostotyyppi"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_package_type_id
msgid "UPS Package Type"
msgstr "UPS-paketin tyyppi"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr "UPS-salasana"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr "UPS lauantaitoimitus"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "UPS Server Not Found"
msgstr "UPS-palvelinta ei löydy"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr "UPS-palvelun tyyppi"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
msgid "UPS Shipper Number"
msgstr "UPS-lähettäjän numero"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Shipping Methods"
msgstr "UPS:n toimitustavat"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.product,name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr "UPS US"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr "UPS Käyttäjätunnus"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr "UPS-tilin numero"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"UPS address lines can only contain a maximum of 35 characters. You can split"
" the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""
"UPS-osoiterivillä voi olla enintään 35 merkkiä. Voit jakaa yhteysosoitteet "
"useammalle riville tämän rajoituksen välttämiseksi."

#. module: delivery_ups
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_be
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_be_product_template
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "Units"
msgstr "Yksiköt"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr "UPS tullien maksu"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr "UPS paketin painoyksikkö"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Username/Password is invalid for this delivery provider."
msgstr ""
"Käyttäjätunnus/salasana on virheellinen tälle toimituspalvelun tarjoajalle."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr ""
"Varaston puhelinnumeron on oltava vähintään 10 aakkosnumeerista merkkiä."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must contain only numbers."
msgstr "Varaston puhelinnumeroon on sisällettävä vain numeroita."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse PhoneExtension cannot exceed the length of 4."
msgstr ""
"Varaston puhelinnumeron alanumero ei voi ylittää neljän merkin pituutta."

#. module: delivery_ups
#: code:addons/delivery_ups/models/sale.py:0
#, python-format
msgid "You must enter an UPS account number."
msgstr "Sinun on annettava UPS-tilin numero."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"
