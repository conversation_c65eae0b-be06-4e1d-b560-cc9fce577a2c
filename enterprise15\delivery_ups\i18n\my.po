# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_ups
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-07 09:57+0000\n"
"PO-Revision-Date: 2016-09-07 09:57+0000\n"
"Language-Team: Burmese (https://www.transifex.com/odoo/teams/41243/my/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: my\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Carrier"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_dimension_unit:0
msgid "Centimeters"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "EPL"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_dimension_unit:0
msgid "Inches"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_weight_unit:0
msgid "Kilograms"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "PDF"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_product_packaging
msgid "Packaging"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:207
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_weight_unit:0
msgid "Pounds"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "SPL"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:175
#, python-format
msgid "Shipment N° %s has been cancelled"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:139
#, python-format
msgid "Shipment created into UPS <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:146
#, python-format
msgid "Shipping label for packages"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:182
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:192
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:212
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:209
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:202
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS 2nd Day"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS 2nd Day AM"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS 3-day Select"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_access_number
msgid "UPS AccessLicenseNumber"
msgstr ""

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.product,name:delivery_ups.delivery_carrier_ups_be_product_product
msgid "UPS BE"
msgstr ""

#. module: delivery_ups
#: model:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_default_packaging_id
msgid "UPS Default Packaging Type"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Ground"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_label_file_type
msgid "UPS Label File Type"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Next Day"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Next Day AM"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Next Day Air Saver"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_passwd
msgid "UPS Password"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Saver"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_default_service_type
msgid "UPS Service Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_shipper_number
msgid "UPS Shipper Number"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Standard"
msgstr ""

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.product,name:delivery_ups.delivery_carrier_ups_us_product_product
msgid "UPS US"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_username
msgid "UPS Username"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Worldwide Expedited"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Worldwide Express"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Worldwide Express Freight"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_default_service_type:0
msgid "UPS Worldwide Express Plus"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_package_dimension_unit
msgid "Units for UPS Package Size"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_package_weight_unit
msgid "Ups package weight unit"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "ZPL"
msgstr ""
