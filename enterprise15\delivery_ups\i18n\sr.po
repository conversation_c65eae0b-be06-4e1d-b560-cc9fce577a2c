# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_ups
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
msgid "Based on Rules"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_product_packaging__package_carrier_type
msgid "Carrier"
msgstr "Prevoznik"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_stock_picking__ups_carrier_account
msgid "Carrier Account"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_cod_funds_code:0
msgid "Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_dimension_unit:0
msgid "Centimeters"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_cod_funds_code:0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "DHL"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Delivery Methods"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "EPL"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "Easypost"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:113
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "FedEx"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
msgid "Fixed Price"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_dimension_unit:0
msgid "Inches"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_weight_unit:0
msgid "Kilograms"
msgstr ""

#. module: delivery_ups
#: selection:product.packaging,package_carrier_type:0
msgid "No carrier integration"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "PDF"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:209
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_weight_unit:0
msgid "Pounds"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_product_packaging
msgid "Product Packaging"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "SPL"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sale Order"
msgstr "Nalog za prodaju"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:231
#, python-format
msgid "Shipment N° %s has been cancelled"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:202
#, python-format
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:187
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:197
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:215
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:211
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:219
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "UPS"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS AccessLicenseNumber"
msgstr ""

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.product,name:delivery_ups.product_product_delivery_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_packaging_id
msgid "UPS Default Packaging Type"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Delivery Methods"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_service_type
#: model:ir.model.fields,field_description:delivery_ups.field_stock_picking__ups_service_type
msgid "UPS Service Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
msgid "UPS Shipper Number"
msgstr ""

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.product,name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
msgid "USPS"
msgstr ""

#. module: delivery_ups
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_be
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_be_product_template
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "Unit(s)"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Units for UPS Package Size"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Value Added Services"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "ZPL"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,delivery_type:0
#: selection:product.packaging,package_carrier_type:0
msgid "bpost"
msgstr ""

#. module: delivery_ups
#: model:product.product,weight_uom_name:delivery_ups.product_product_delivery_ups_be
#: model:product.product,weight_uom_name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,weight_uom_name:delivery_ups.product_product_delivery_ups_be_product_template
#: model:product.template,weight_uom_name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "kg"
msgstr "kg"
