# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_ups
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order_ups_bill_my_account
msgid "Bill My Account"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_cod_funds_code
msgid "COD Funding Option"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Carrier"
msgstr "Prevoznik"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_stock_picking_ups_carrier_account
msgid "Carrier Account"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_cod_funds_code:0
msgid "Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_dimension_unit:0
msgid "Centimeters"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_cod_funds_code:0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_cod
msgid "Collect on Delivery"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "EPL"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:113
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier_ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order_ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_dimension_unit:0
msgid "Inches"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_weight_unit:0
msgid "Kilograms"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "PDF"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_product_packaging
msgid "Packaging"
msgstr "Pakovanje"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:221
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_package_weight_unit:0
msgid "Pounds"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Quotation"
msgstr "Ponuda"

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "SPL"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_saturday_delivery
msgid "Saturday Delivery"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:226
#, python-format
msgid "Shipment N° %s has been cancelled"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:197
#, python-format
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:196
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:206
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:226
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:223
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:216
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier_ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier_ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_access_number
msgid "UPS AccessLicenseNumber"
msgstr ""

#. module: delivery_ups
#: model:product.product,name:delivery_ups.product_product_delivery_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr ""

#. module: delivery_ups
#: model:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_default_packaging_id
msgid "UPS Default Packaging Type"
msgstr ""

#. module: delivery_ups
#: model:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Delivery Methods"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_label_file_type
msgid "UPS Label File Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_passwd
msgid "UPS Password"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_default_service_type
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order_ups_service_type
#: model:ir.model.fields,field_description:delivery_ups.field_stock_picking_ups_service_type
msgid "UPS Service Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_shipper_number
msgid "UPS Shipper Number"
msgstr ""

#. module: delivery_ups
#: model:product.product,name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_username
msgid "UPS Username"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_package_dimension_unit
msgid "Units for UPS Package Size"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier_ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr ""

#. module: delivery_ups
#: model:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Value Added Services"
msgstr ""

#. module: delivery_ups
#: selection:delivery.carrier,ups_label_file_type:0
msgid "ZPL"
msgstr ""
