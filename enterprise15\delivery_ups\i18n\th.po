# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_ups
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# Wichanon Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://www.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"A shipment cannot have a KGS/IN or LBS/CM as its unit of measurements. "
"Configure it from the delivery method."
msgstr ""
"การจัดส่งต้องไม่มี KGS/IN หรือ LBS/CM เป็นหน่วยวัด กำหนดค่าจากวิธีการจัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Access License number is Invalid. Provide a valid number (Length should be "
"0-35 alphanumeric characters)"
msgstr ""
"หมายเลขใบอนุญาตการเข้าถึงไม่ถูกต้อง ต้องระบุตัวเลขที่ถูกต้อง (ความยาวควรเป็น"
" 0-35 อักขระที่เป็นตัวอักษรและตัวเลขคละกัน)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this delivery provider."
msgstr "หมายเลขใบอนุญาตการเข้าถึงไม่ถูกต้องสำหรับผู้ให้บริการจัดส่งรายนี้"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this provider.Please re-license."
msgstr ""
"หมายเลขใบอนุญาตการเข้าถึงไม่ถูกต้องสำหรับผู้ให้บริการรายนี้ "
"โปรดขอใบอนุญาตใหม่"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is revoked contact UPS to get access."
msgstr "หมายเลขใบอนุญาตการเข้าถึงถูกเพิกถอน ติดต่อ UPS เพื่อเข้าถึง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Authorization system is currently unavailable , try again later."
msgstr "หมายเลขใบอนุญาตการเข้าถึงถูกเพิกถอน ติดต่อ UPS เพื่อเข้าถึง"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr "เรียกเก็บเงินบัญชีของฉัน"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr "ตัวเลือกเงินทุน COD"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Cancel shipment not available at this time , Please try again Later."
msgstr "ไม่สามารถยกเลิกการจัดส่งได้ในขณะนี้ โปรดลองอีกครั้งในภายหลัง"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "ผู้ให้บริการ"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr "แคชเชียร์เช็คหรือธนาณัติ"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr "เซนติเมตร"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr "เช็ค แคชเชียร์เช็ค หรือธนาณัติ"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr "เก็บเงินปลายทาง"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Duties paid by"
msgstr "ค่าอากรจ่ายโดย"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr "EPL"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"ผิดพลาด:\n"
"%s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Exceeds Total Number of allowed pieces per World Wide Express Shipment."
msgstr "เกินจำนวนที่อนุญาตต่อการขนส่งด่วนทั่วโลก"

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""
"หากเลือก ผู้ใช้อีคอมเมิร์ซจะได้รับแจ้งหมายเลขบัญชี UPS\n"
"และจะมีการคิดค่าธรรมเนียมการจัดส่ง"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr "นิ้ว"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr "กิโลกรัม"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Label Format"
msgstr "รูปแบบฉลาก"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Options"
msgstr "ตัวเลือก"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr "PDF"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr "หน่วยขนาดบรรจุภัณฑ์"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Package Weight Unit"
msgstr "หน่วยน้ำหนักบรรจุภัณฑ์"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Packages %s do not have a positive shipping weight."
msgstr "แพ็คเกจ%s ไม่มีน้ำหนักการจัดส่งที่เป็นบวก"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid City in the warehouse address."
msgstr "โปรดระบุเมืองที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in recipient's address."
msgstr "โปรดระบุประเทศที่ถูกต้องในที่อยู่ของผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in the warehouse address."
msgstr "โปรดระบุประเทศที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid State in the warehouse address."
msgstr "โปรดระบุสถานะที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Zip in the warehouse address."
msgstr "โปรดระบุรหัสไปรษณีย์ที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the recipient address."
msgstr "โปรดระบุเมืองที่ถูกต้องในที่อยู่ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the shipper's address."
msgstr "โปรดระบุเมืองที่ถูกต้องในที่อยู่ของผู้จัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid country in the shipper's address."
msgstr "โปรดระบุประเทศที่ถูกต้องในที่อยู่ของผู้จัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Please provide a valid package type available for service and selected "
"locations."
msgstr "โปรดระบุประเภทแพ็คเกจที่ถูกต้องสำหรับบริการและตำแหน่งที่เลือก"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid phone number for the recipient."
msgstr "โปรดระบุหมายเลขโทรศัพท์ที่ถูกต้องสำหรับผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper Number/Carrier Account."
msgstr "โปรดระบุหมายเลขผู้จัดส่ง/บัญชีผู้ให้บริการที่ถูกต้อง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper number/Carrier Account."
msgstr "โปรดระบุหมายเลขผู้จัดส่ง/บัญชีผู้ให้บริการที่ถูกต้อง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper phone number."
msgstr "โปรดระบุหมายเลขโทรศัพท์ของผู้จัดส่งที่ถูกต้อง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the recipient address."
msgstr "โปรดระบุสถานะที่ถูกต้องในที่อยู่ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the shipper's address."
msgstr "โปรดระบุสถานะที่ถูกต้องในที่อยู่ของผู้จัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in shipper's address."
msgstr "โปรดระบุถนนที่ถูกต้องในที่อยู่ของผู้จัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the recipient address."
msgstr "โปรดระบุถนนที่ถูกต้องในที่อยู่ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the warehouse address."
msgstr "โปรดระบุถนนที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid warehouse Phone Number"
msgstr "โปรดระบุหมายเลขโทรศัพท์ของโกดังสินค้าที่ถูกต้อง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the recipient address."
msgstr "โปรดระบุรหัสไปรษณีย์ที่ถูกต้องในที่อยู่ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the shipper's address."
msgstr "โปรดระบุรหัสไปรษณีย์ที่ถูกต้องในที่อยู่ของผู้จัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the warehouse address."
msgstr "โปรดระบุรหัสไปรษณีย์ที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zipcode in the recipient address."
msgstr "โปรดระบุรหัสไปรษณีย์ที่ถูกต้องในที่อยู่ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship"
msgstr "โปรดระบุสินค้าสำหรับจัดส่งอย่างน้อยหนึ่งรายการ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "โปรดระบุอย่างน้อยหนึ่งรายการในการจัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the recipient address."
msgstr "โปรดตั้งค่าประเทศที่ถูกต้องในที่อยู่ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the warehouse address."
msgstr "โปรดกำหนดประเทศที่ถูกต้องในที่อยู่โกดังสินค้า"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr "ปอนด์"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Access License Number not found in the UPS database"
msgstr "ไม่พบหมายเลขใบอนุญาตการเข้าถึงที่ระบุในฐานข้อมูลของ UPS"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Tracking Ref. Number is invalid."
msgstr "ระบุหมายเลขสำหรับอ้างอิงการติดตามไม่ถูกต้อง"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "ผู้ให้บริการ"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr "ผู้รับ"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr ""
"โทรศัพท์ผู้รับต้องมีอักขระที่เป็นตัวอักษรและตัวเลขคละกันอย่างน้อย 10 ตัว"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension cannot exceed the length of 4."
msgstr "ส่วนขยายโทรศัพท์ของผู้รับต้องมีความยาวไม่เกิน 4"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension must contain only numbers."
msgstr "ส่วนขยายโทรศัพท์ของผู้รับต้องมีเฉพาะตัวเลขเท่านั้น"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Return label generated<br/><b>Tracking Numbers:</b> %s<br/><b>Packages:</b> "
"%s"
msgstr ""
"สร้างฉลากการส่งคืน<br/><b>หมายเลขติดตาม:</b> %s<br/><b>แพ็คเกจ:</b> %s"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr "SPL"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr "จัดส่งวันเสาร์"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr "ผู้ส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "การจัดส่ง #%s ถูกยกเลิก"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""
"สร้างการจัดส่งใน UPS<br/><b>หมายเลขติดตาม:</b> %s<br/><b>แพ็คเกจ:</b> %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr ""
"โทรศัพท์ของผู้จัดส่งต้องมีอักขระที่เป็นตัวอักษรและตัวเลขคละกันอย่างน้อย 10 "
"ตัว"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper number must contain alphanumeric characters only."
msgstr "หมายเลขผู้จัดส่งต้องมีอักขระที่เป็นตัวอักษรและตัวเลขคละกันเท่านั้น"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension cannot exceed the length of 4."
msgstr "ส่วนขยายโทรศัพท์ของผู้จัดส่งต้องมีความยาวไม่เกิน 4"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension must contain only numbers."
msgstr "ส่วนขยายโทรศัพท์ของผู้จัดส่งต้องมีเฉพาะตัวเลขเท่านั้น"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Shipping Methods"
msgstr "วิธีการการจัดส่ง"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_package_type
msgid "Stock package type"
msgstr "ประเภทของสต๊อกบรรจุภัณฑ์"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The UserId is currently locked out; please try again in 24 hours."
msgstr "UserId ถูกล็อกอยู่ในขณะนี้ กรุณาลองใหม่อีกครั้งในอีก 24 ชั่วโมง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"ที่อยู่บริษัทของคุณสูญหายหรือไม่ถูกต้อง\n"
"(ฟิลด์ที่ขาดหายไป : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"ที่อยู่โกดังสินค้าของคุณหายไปหรือไม่ถูกต้อง\n"
"(ฟิลด์ที่ขาดหายไป : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr "ไม่สามารถจัดส่งได้เนื่องจากไม่มีน้ำหนักของสินค้า"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"ไม่สามารถคำนวณราคาจัดส่งโดยประมาณได้เนื่องจากไม่มีน้ำหนักของสินค้าต่อไปนี้:\n"
" %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The maximum number of user access attempts was exceeded. So please try again"
" later"
msgstr ""
"เกินจำนวนครั้งสูงสุดของความพยายามในการเข้าถึงของผู้ใช้แล้ว "
"ดังนั้นโปรดลองอีกครั้งในภายหลัง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"ที่อยู่ผู้รับหายไปหรือไม่ถูกต้อง\n"
"(ฟิลด์ที่ขาดหายไป : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The requested service is unavailable between the selected locations."
msgstr "บริการที่ร้องขอไม่สามารถใช้ได้ระหว่างตำแหน่งที่เลือก"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid from the requested warehouse, please choose "
"another service."
msgstr "บริการที่เลือกไม่ถูกต้องจากโกดังสินค้าที่ร้องขอ โปรดเลือกบริการอื่น"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid to the recipient address, please choose "
"another service."
msgstr "บริการที่เลือกไม่ถูกต้องสำหรับที่อยู่ผู้รับ โปรดเลือกบริการอื่น"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is not possible from your warehouse to the recipient "
"address, please choose another service."
msgstr ""
"ไม่สามารถใช้บริการที่เลือกจากโกดังสินค้าของคุณไปยังที่อยู่ผู้รับได้ "
"กรุณาเลือกบริการอื่น"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The selected service is not valid with the selected packaging."
msgstr "บริการที่เลือกไม่ถูกต้องกับบรรจุภัณฑ์ที่เลือก"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from delivery method"
msgstr ""
"ระบบการวัดนี้ไม่ถูกต้องสำหรับประเทศที่เลือก โปรดเปลี่ยนจาก LBS/IN เป็น "
"KGS/CM (หรือกลับกัน) กำหนดค่าจากวิธีการจัดส่ง"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from the delivery "
"method."
msgstr ""
"ระบบการวัดนี้ไม่ถูกต้องสำหรับประเทศที่เลือก โปรดเปลี่ยนจาก LBS/IN เป็น "
"KGS/CM (หรือกลับกัน) กำหนดค่าจากวิธีการจัดส่ง"

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""
"บริการเพิ่มมูลค่านี้ทำให้ UPS สามารถเรียกเก็บเงินค่าขนส่งจากลูกค้าของคุณได้"

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr "บริการเพิ่มมูลค่าจะช่วยให้คุณสามารถจัดส่งแพ็คเกจได้ในวันเสาร์"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__delivery_type__ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__stock_package_type__package_carrier_type__ups
msgid "UPS"
msgstr "UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS Access Key"
msgstr "คีย์การเข้าถึง UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr "เลขที่บัญชี UPS"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.product,name:delivery_ups.product_product_delivery_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr "UPS BE"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr "การกำหนดค่า UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr "ประเภทไฟล์ฉลาก UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_package_type_id
msgid "UPS Package Type"
msgstr "ประเภทแพ็กเกจ UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr "UPS รหัสผ่าน"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr "UPS จัดส่งวันเสาร์"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "UPS Server Not Found"
msgstr "ไม่พบเซิร์ฟเวอร์ UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr "ประเภทบริการ UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
msgid "UPS Shipper Number"
msgstr "หมายเลขผู้จัดส่งของ UPS"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Shipping Methods"
msgstr "วิธีการจัดส่งของ UPS"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.product,name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr "UPS US"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr "ชื่อผู้ใช้ UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr "เลขที่บัญชี UPS"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"UPS address lines can only contain a maximum of 35 characters. You can split"
" the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""
"บรรทัดที่อยู่ของ UPS สามารถมีอักขระได้สูงสุด 35 ตัวเท่านั้น "
"คุณสามารถแบ่งที่อยู่สำหรับติดต่อออกเป็นหลายบรรทัดเพื่อหลีกเลี่ยงข้อจำกัดนี้ได้"

#. module: delivery_ups
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_be
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_be_product_template
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "Units"
msgstr "หน่วย"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr "การชำระภาษีของ Ups"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr "Ups หน่วยน้ำหนักบรรจุภัณฑ์"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Username/Password is invalid for this delivery provider."
msgstr "ชื่อผู้ใช้/รหัสผ่านไม่ถูกต้องสำหรับผู้ให้บริการจัดส่งรายนี้"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr ""
"โทรศัพท์โกดังต้องมีอักขระที่เป็นตัวอักษรและตัวเลขคละกันอย่างน้อย 10 ตัว"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must contain only numbers."
msgstr "โทรศัพท์โกดังต้องมีเฉพาะตัวเลขเท่านั้น"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse PhoneExtension cannot exceed the length of 4."
msgstr "ส่วนขยายโทรศัพท์โกดังไม่สามารถเกินความยาว 4 ได้"

#. module: delivery_ups
#: code:addons/delivery_ups/models/sale.py:0
#, python-format
msgid "You must enter an UPS account number."
msgstr "คุณต้องป้อนหมายเลขบัญชี UPS"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"
