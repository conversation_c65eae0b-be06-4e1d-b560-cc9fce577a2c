# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_ups
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Tr<PERSON><PERSON> <<EMAIL>>, 2021
# Vo Thanh Thuy, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"A shipment cannot have a KGS/IN or LBS/CM as its unit of measurements. "
"Configure it from the delivery method."
msgstr ""
"Một lô hàng không thể dùng KGS/IN hoặc LBS/CM làm đơn vị đo. Định cấu hình "
"trong phương thức giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Access License number is Invalid. Provide a valid number (Length should be "
"0-35 alphanumeric characters)"
msgstr ""
"Số giấy phép truy cập không hợp lệ. Cung cấp số hợp lệ (Độ dài phải từ 0-35 "
"ký tự chứa chữ và số)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this delivery provider."
msgstr "Số giấy phép truy cập của dịch vụ giao hàng này không hợp lệ."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is invalid for this provider.Please re-license."
msgstr ""
"Số giấy phép truy cập của nhà cung cấp này không hợp lệ.Vui lòng cấp lại "
"giấy phép."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Access License number is revoked contact UPS to get access."
msgstr "Số giấy phép truy cập bị thu hồi, liên hệ UPS để có quyền truy cập."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Authorization system is currently unavailable , try again later."
msgstr "Hệ thống ủy quyền hiện không khả dụng, hãy thử lại sau."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr "Xuất hóa đơn tài khoản"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr "Tùy chọn quỹ COD"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Cancel shipment not available at this time , Please try again Later."
msgstr "Không thể hủy lô hàng vào thời điểm này, vui lòng thử lại sau."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Nhà vận chuyển"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr "Séc của thu ngân hoặc MoneyOrder"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr "Centimet"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr "Séc, séc của thu ngân hoặc MoneyOrder"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr "Thu tiền khi giao hàng COD"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Duties paid by"
msgstr "Người trả thuế"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr "EPL"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"Lỗi:\n"
"%s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Exceeds Total Number of allowed pieces per World Wide Express Shipment."
msgstr "Vượt quá tổng số kiện cho phép cho mỗi lô hàng World Wide Express."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""
"Nếu được chọn, người dùng ecommerce sẽ được hỏi về số tài khoản UPS của họ\n"
"và phí vận chuyển sẽ được tính vào đó."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr "Inch"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr "Kilogram"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Label Format"
msgstr "Định dạng nhãn"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Options"
msgstr "Tùy chọn"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr "PDF"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr "Đơn vị kích thước gói hàng"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Package Weight Unit"
msgstr "Đơn vị khối lượng gói"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Packages %s do not have a positive shipping weight."
msgstr "Gói hàng %s không có khối lượng vận chuyển dương."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid City in the warehouse address."
msgstr "Vui lòng cung cấp tên thành phố hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in recipient's address."
msgstr "Vui lòng cung cấp tên quốc gia hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Country in the warehouse address."
msgstr "Vui lòng cung cấp tên quốc gia hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid State in the warehouse address."
msgstr "Vui lòng cung cấp tên bang hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid Zip in the warehouse address."
msgstr "Vui lòng cung cấp mã Zip hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the recipient address."
msgstr "Vui lòng cung cấp tên thành phố hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid city in the shipper's address."
msgstr "Vui lòng cung cấp tên thành phố hợp lệ trong địa chỉ người giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid country in the shipper's address."
msgstr "Vui lòng cung cấp tên quốc gia hợp lệ trong địa chỉ người giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"Please provide a valid package type available for service and selected "
"locations."
msgstr ""
"Vui lòng cung cấp kiểu đóng gói hợp lệ cho dịch vụ và vị trí được chọn."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid phone number for the recipient."
msgstr "Vui lòng cung cấp số điện thoại hợp lệ cho người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper Number/Carrier Account."
msgstr "Vui lòng cung cấp Tài khoản vận chuyển/Mã số người giao hàng hợp lệ."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper number/Carrier Account."
msgstr "Vui lòng cung cấp Tài khoản vận chuyển/Mã số người giao hàng hợp lệ."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid shipper phone number."
msgstr "Vui lòng cung cấp số điện thoại hợp lệ của người giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the recipient address."
msgstr "Vui lòng cung cấp tên bang hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid state in the shipper's address."
msgstr "Vui lòng cung cấp tên bang hợp lệ trong địa chỉ người giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in shipper's address."
msgstr "Vui lòng cung cấp tên phố hợp lệ trong địa chỉ người giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the recipient address."
msgstr "Vui lòng cung cấp tên phố hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid street in the warehouse address."
msgstr "Vui lòng cung cấp tên phố hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid warehouse Phone Number"
msgstr "Vui lòng cung cấp số điện thoại hợp lệ của nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the recipient address."
msgstr "Vui lòng cung cấp mã zip hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the shipper's address."
msgstr "Vui lòng cung cấp mã zip hợp lệ trong địa chỉ người giao hàng."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zip code in the warehouse address."
msgstr "Vui lòng cung cấp mã zip hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide a valid zipcode in the recipient address."
msgstr "Vui lòng cung cấp mã zip hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship"
msgstr "Vui lòng cung cấp ít nhất một mặt hàng để vận chuyển"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "Vui lòng cung cấp ít nhất một mặt hàng để vận chuyển."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the recipient address."
msgstr "Vui lòng cung cấp tên quốc gia hợp lệ trong địa chỉ người nhận."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Please set a valid country in the warehouse address."
msgstr "Vui lòng cung cấp tên quốc gia hợp lệ trong địa chỉ nhà kho."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr "Pound"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Access License Number not found in the UPS database"
msgstr ""
"Không tìm thấy số giấy phép truy cập đã cung cấp trong cơ sở dữ liệu UPS"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Provided Tracking Ref. Number is invalid."
msgstr "Mã số tham chiếu theo dõi hàng đã cung cấp không hợp lệ."

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Nhà cung cấp"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr "Người nhận"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr "Số điện thoại người nhận phải có ít nhất 10 ký tự chữ số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension cannot exceed the length of 4."
msgstr "Số máy nhánh của người nhận không được vượt quá 4 số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Recipient PhoneExtension must contain only numbers."
msgstr "Máy nhánh người nhận chỉ được phép chứa số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Return label generated<br/><b>Tracking Numbers:</b> %s<br/><b>Packages:</b> "
"%s"
msgstr ""
"Nhãn trả hàng đã được tạo<br/><b>Số theo dõi:</b> %s<br/><b>Gói hàng:</b> %s"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr "SPL"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sales Order"
msgstr "Đơn bán hàng"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr "Giao hàng thứ 7"

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr "Người gửi"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "Lô hàng #%s đã bị hủy"

#. module: delivery_ups
#: code:addons/delivery_ups/models/delivery_ups.py:0
#, python-format
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%s<br/><b>Packages:</b> %s"
msgstr ""
"Lô hàng đã được tạo trong UPS<br/><b>Số theo dõi:</b> %s<br/><b>Gói "
"hàng:</b> %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr "Số điện thoại người giao hàng phải có ít nhất 10 ký tự chữ số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper number must contain alphanumeric characters only."
msgstr "Số người giao hàng chỉ được chứa ký tự chữ số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension cannot exceed the length of 4."
msgstr "Số máy nhánh của người giao hàng không được vượt quá 4 số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Shipper phone extension must contain only numbers."
msgstr "Máy nhánh người giao hàng chỉ được phép chứa số."

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Phương thức vận chuyển"

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_package_type
msgid "Stock package type"
msgstr "Kiểu đóng gói kho"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The UserId is currently locked out; please try again in 24 hours."
msgstr "ID người dùng hiện bị khóa, vui lòng thử lại 24 giờ sau."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Địa chỉ công ty của bạn bị thiếu hoặc sai.\n"
"(Trường bị thiếu : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Địa chỉ nhà kho bị thiếu hoặc sai.\n"
"(Trường bị thiếu : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The delivery cannot be done because the weight of your product is missing."
msgstr "Không thể giao hàng vì thiếu khối lượng của sản phẩm."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Không thể tính giá vận chuyển ước tính vì thiếu khối lượng của (các) sản phẩm sau: \n"
" %s"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The maximum number of user access attempts was exceeded. So please try again"
" later"
msgstr ""
"Đã vượt quá số lần thử truy cập người dùng tối đa. Vui lòng thử lại sau"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""
"Địa chỉ người nhận bị thiếu hoặc sai.\n"
"(Trường bị thiếu : %s)"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The requested service is unavailable between the selected locations."
msgstr "Dịch vụ được yêu cầu không khả dụng giữa hai vị trí đã chọn."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid from the requested warehouse, please choose "
"another service."
msgstr ""
"Dịch vụ được chọn không hợp lệ cho nhà kho được yêu cầu, vui lòng chọn dịch "
"vụ khác."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is invalid to the recipient address, please choose "
"another service."
msgstr ""
"Dịch vụ được chọn không hợp lệ cho địa chỉ người nhận, vui lòng chọn dịch vụ"
" khác."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"The selected service is not possible from your warehouse to the recipient "
"address, please choose another service."
msgstr ""
"Dịch vụ được chọn không thể sử dụng giữa nhà kho và địa chỉ người nhận, vui "
"lòng chọn dịch vụ khác."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "The selected service is not valid with the selected packaging."
msgstr "Dịch vụ được chọn không hợp lệ cho gói hàng được chọn."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from delivery method"
msgstr ""
"Hệ thống đo lường không hợp lệ cho quốc gia được chọn. Vui lòng chuyển đổi "
"từ LBS/IN sang KGS/CM (hoặc ngược lại). Hãy định cấu hình từ phương thức "
"giao hàng"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"This measurement system is not valid for the selected country. Please switch"
" from LBS/IN to KGS/CM (or vice versa). Configure it from the delivery "
"method."
msgstr ""
"Hệ thống đo lường không hợp lệ cho quốc gia được chọn. Vui lòng chuyển đổi "
"từ LBS/IN sang KGS/CM (hoặc ngược lại). Hãy định cấu hình từ phương thức "
"giao hàng."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""
"Dịch vụ giá trị gia tăng này cho phép UPS nhận thanh toán của lô hàng từ "
"khách hàng của bạn."

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr ""
"Dịch vụ giá trị gia tăng này sẽ cho phép bạn vận chuyển gói hàng vào thứ 7."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__delivery_type__ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__stock_package_type__package_carrier_type__ups
msgid "UPS"
msgstr "UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS Access Key"
msgstr "Khóa truy cập UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr "Số tài khoản UPS"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.product,name:delivery_ups.product_product_delivery_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr "UPS Bỉ"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr "Cấu hình UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr "Kiểu tệp nhãn UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_package_type_id
msgid "UPS Package Type"
msgstr "Kiểu đóng gói UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr "Mật khẩu UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr "UPS giao hàng thứ 7"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "UPS Server Not Found"
msgstr "Không tìm thấy máy chủ UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr "Kiểu dịch vụ UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
msgid "UPS Shipper Number"
msgstr "Mã số người giao hàng UPS"

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Shipping Methods"
msgstr "Phương thức vận chuyển UPS"

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.product,name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr "UPS Hoa Kỳ"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr "Tên người dùng UPS"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr "Số tài khoản UPS"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid ""
"UPS address lines can only contain a maximum of 35 characters. You can split"
" the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""
"Dòng địa chỉ UPS chỉ có thể chứa tối đa 35 ký tự. Bạn có thể tách địa chỉ "
"thành nhiều dòng để không vượt quá giới hạn này."

#. module: delivery_ups
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_be
#: model:product.product,uom_name:delivery_ups.product_product_delivery_ups_us
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_be_product_template
#: model:product.template,uom_name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "Units"
msgstr "Đơn vị"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr "Thanh toán thuế Ups"

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr "Đơn vị khối lượng gói Ups"

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Username/Password is invalid for this delivery provider."
msgstr "Tên người dùng/Mật mã không hợp lệ cho dịch vụ vận chuyển này."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr "Số điện thoại nhà kho phải có ít nhất 10 ký tự chữ số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse Phone must contain only numbers."
msgstr "Số điện thoại nhà kho chỉ được chứa số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/ups_request.py:0
#, python-format
msgid "Warehouse PhoneExtension cannot exceed the length of 4."
msgstr "Số máy nhánh của nhà kho không được vượt quá 4 số."

#. module: delivery_ups
#: code:addons/delivery_ups/models/sale.py:0
#, python-format
msgid "You must enter an UPS account number."
msgstr "Bạn phải nhập số tài khoản UPS."

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr "ZPL"
