<odoo>
    <data noupdate="1">

        <record id="ups_packaging_01" model="stock.package.type">
            <field name="name">UPS Letter</field>
            <field name="shipper_package_code">01</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">0.5</field>
        </record>

        <record id="ups_packaging_02" model="stock.package.type">
            <field name="name">UPS Package/customer supplied</field>
            <field name="shipper_package_code">02</field>
            <field name="height">10</field>
            <field name="width">10</field>
            <field name="packaging_length">10</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>
        <record id="ups_packaging_03" model="stock.package.type">
            <field name="name">UPS Tube</field>
            <field name="shipper_package_code">03</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>
        <record id="ups_packaging_04" model="stock.package.type">
            <field name="name">UPS Pak</field>
            <field name="shipper_package_code">04</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>
        <record id="ups_packaging_21" model="stock.package.type">
            <field name="name">UPS Express Box</field>
            <field name="shipper_package_code">21</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>
        <record id="ups_packaging_24" model="stock.package.type">
            <field name="name">UPS 25KG Box</field>
            <field name="shipper_package_code">24</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">25</field>
        </record>
        <record id="ups_packaging_25" model="stock.package.type">
            <field name="name">UPS 10KG Box</field>
            <field name="shipper_package_code">25</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">10</field>
        </record>
        <record id="ups_packaging_30" model="stock.package.type">
            <field name="name">UPS Pallet</field>
            <field name="shipper_package_code">30</field>
            <field name="height">10</field>
            <field name="width">10</field>
            <field name="packaging_length">10</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">1000</field>
        </record>
        <record id="ups_packaging_2a" model="stock.package.type">
            <field name="name">UPS Small Express Box</field>
            <field name="shipper_package_code">2a</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>
        <record id="ups_packaging_2b" model="stock.package.type">
            <field name="name">UPS Medium Express Box</field>
            <field name="shipper_package_code">2b</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>
        <record id="ups_packaging_2c" model="stock.package.type">
            <field name="name">UPS Large Express Box</field>
            <field name="shipper_package_code">2c</field>
            <field name="package_carrier_type">ups_rest</field>
            <field name="max_weight">68</field>
        </record>

    </data>
</odoo>