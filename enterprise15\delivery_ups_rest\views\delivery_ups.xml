<odoo>
  <record id="view_delivery_carrier_form" model="ir.ui.view">
        <field name="name">delivery.carrier.form.ups</field>
        <field name="model">delivery.carrier</field>
        <field name="inherit_id" ref="delivery.view_delivery_carrier_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='destination']" position='before'>
                <page string="UPS Configuration" name="ups_configuration"
                    attrs="{'invisible': [('delivery_type', '!=', 'ups_rest')]}">
                    <group>
                        <group>
                            <field name="ups_shipper_number" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                            <field name="ups_client_id" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                            <field name="ups_client_secret" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                            <field name="ups_default_service_type" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                        </group>
                        <group>
                            <field name="ups_default_packaging_id" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}" domain="[('package_carrier_type', '=', 'ups_rest')]"/>
                            <field name="ups_package_weight_unit" string="Package Weight Unit" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                            <field name="ups_package_dimension_unit" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                            <field name="ups_label_file_type" string="Label Format" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                        </group>
                        <group string="Options" name="ups_vas">
                            <field name="ups_bill_my_account" attrs="{'invisible': [('delivery_type', '!=', 'ups_rest')]}"/>
                            <field name="ups_saturday_delivery" string="Saturday Delivery" attrs="{'required': [('delivery_type', '=', 'ups_rest')], 'invisible': [('ups_default_service_type', 'in', ['03','11','13','59','12','65','08'])]}"/>
                            <field name="ups_cod" attrs="{'required': [('delivery_type', '=', 'ups_rest')], 'invisible': [('ups_default_service_type', '=', '96')]}"/>
                            <field name="ups_cod_funds_code" attrs="{'required': [('delivery_type', '=', 'ups_rest')], 'invisible': [('ups_cod', '=', False)]}" widget="radio"/>
                            <field name="can_generate_return" invisible="1"/>
                            <field name="return_label_on_delivery" attrs="{'invisible': [('can_generate_return', '=', False)]}"/>
                            <field name="get_return_label_from_portal" attrs="{'invisible': [('return_label_on_delivery', '=', False)]}"/>
                            <field name="ups_duty_payment" string="Duties paid by" attrs="{'required': [('delivery_type', '=', 'ups_rest')]}"/>
                        </group>
                    </group>
                    <group string="How to setup UPS?" attrs="{'invisible': [('delivery_type', '!=', 'ups_rest')]}">
                        <ol>
                            <li>Create an account on <a href="http://www.ups.com/" target="_blank">ups.com</a></li>
                            <li>From there, get your <b>account number</b></li>
                            <img style="width:90%; height:100%" src='/delivery_ups_rest/static/src/img/ups1.png' alt="ups step 1"/>
                            <li>Log in with your account on <a href="http://developer.ups.com/" target="_blank">developer.ups.com</a> and go to Apps</li>
                            <img style="width:90%; height:100%" src='/delivery_ups_rest/static/src/img/ups2.png' alt="ups step 2"/>
                            <li>Create an app</li>
                            <img style="width:90%; height:80%" src='/delivery_ups_rest/static/src/img/ups3.png' alt="ups step 3"/>
                            <li>Fill your address and contact information</li>
                            <li>Name your app,
                                <ul>
                                    <li>the callback is your odoo address https://"mycompany".odoo.com</li>
                                    <li>add products <b>Authorization, Address validation, Locator, Paperless Documents, Shipping and Rating</b></li>
                                </ul>
                            </li>
                            <img style="width:90%; height:80%" src='/delivery_ups_rest/static/src/img/ups4.png' alt="ups step 4"/>
                            <li>Save and Accept terms and your app is created!</li>
                            <img style="width:90%; height:100%" src='/delivery_ups_rest/static/src/img/ups5.png' alt="ups step 5"/>
                            <li>Now open your app, you'll have your <b>Client ID</b> and <b>Client Secret</b></li>
                            <img style="width:90%; height:100%" src='/delivery_ups_rest/static/src/img/ups6.png' alt="ups step 6"/>
                            <li>Now go to odoo and create a shipping method for UPS, using Account Number (step2) and Client ID + Client Secret (step 8). Also ensure you use correct dimensions and weight unit for your country</li>
                            <li>you're ready to go!</li>
                        </ol>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>