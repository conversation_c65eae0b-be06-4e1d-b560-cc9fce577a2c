# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_usps
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 11:33+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: Bulgarian (https://www.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_usps
#: selection:delivery.carrier,usps_intl_non_delivery_option:0
msgid "Abandon"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_account_validated
msgid "Account Validated"
msgstr ""

#. module: delivery_usps
#: model:ir.model,name:delivery_usps.model_delivery_carrier
msgid "Carrier"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier_usps_account_validated
msgid "Check this box if your account is validated by USPS"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:64
#, python-format
msgid "Company phone number is invalid. Please insert a US phone number."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_content_type
msgid "Content Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_delivery_nature
msgid "Delivery Nature"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_content_type:0
msgid "Documents"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_delivery_nature:0
msgid "Domestic"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:102
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_service:0
msgid "Express"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_service:0
msgid "First Class"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_first_class_mail_type:0
msgid "Flat"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_mail_type:0
msgid "Flat Rate"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
#: selection:delivery.carrier,usps_international_regular_container:0
#: selection:delivery.carrier,usps_mail_type:0
msgid "Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
#: selection:delivery.carrier,usps_international_regular_container:0
msgid "Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.ui.view,arch_db:delivery_usps.usps_shipping_request
msgid "From Company"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_content_type:0
msgid "Gift"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_delivery_nature:0
msgid "International"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_size_container:0
msgid "Large"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_mail_type:0
msgid "Large Envelope"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
msgid "Large Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
#: selection:delivery.carrier,usps_international_regular_container:0
msgid "Legal Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_first_class_mail_type:0
#: selection:delivery.carrier,usps_mail_type:0
msgid "Letter"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_machinable
msgid "Machinable"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
msgid "Medium Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_content_type:0
msgid "Merchandise"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_intl_non_delivery_option
msgid "Non delivery option"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_container:0
msgid "Non-rectangular"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_label_file_type:0
msgid "PDF"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_mail_type:0
msgid "Package"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_first_class_mail_type:0
msgid "Package Service"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_custom_container_girth
msgid "Package girth (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_custom_container_height
msgid "Package height (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_custom_container_length
msgid "Package length (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_custom_container_width
msgid "Package width (in inches)"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
#: selection:delivery.carrier,usps_international_regular_container:0
msgid "Padded Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_first_class_mail_type:0
msgid "Parcel"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier_usps_machinable
msgid ""
"Please check on USPS website to ensure that your package is machinable."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:81
#, python-format
msgid ""
"Please choose another service (maximum weight of this service is 4 pounds)"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:69
#, python-format
msgid "Please enter a valid ZIP code in recipient address"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:62
#, python-format
msgid "Please enter a valid ZIP code in your Company address"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:76
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:60
#, python-format
msgid ""
"Please set country U.S.A in your company address, Service is only available "
"for U.S.A"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_first_class_mail_type:0
msgid "Postcard"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_service:0
msgid "Priority"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:371
#, python-format
msgid "Recipient address cannot be found. Please check the address exists."
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_container:0
msgid "Rectangular"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_intl_non_delivery_option:0
msgid "Redirect"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_redirect_partner_id
msgid "Redirect Partner"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_size_container:0
msgid "Regular"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_container:0
msgid "Regular < 12 inch"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_content_type:0
#: selection:delivery.carrier,usps_intl_non_delivery_option:0
msgid "Return"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_content_type:0
msgid "Sample"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:163
#, python-format
msgid "Shipment N° %s has been cancelled"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:143
#, python-format
msgid "Shipment created into USPS <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
msgid "Small Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_domestic_regular_container:0
msgid "Small Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: selection:delivery.carrier,usps_label_file_type:0
msgid "TIF"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:58
#, python-format
msgid ""
"The address of your company is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:79
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:67
#, python-format
msgid ""
"The recipient address is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:164
#, python-format
msgid "The selected USPS service (%s) cannot be used to deliver this package."
msgstr ""

#. module: delivery_usps
#: model:ir.ui.view,arch_db:delivery_usps.usps_shipping_request
msgid "To Company"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_international_regular_container
msgid "Type of USPS International regular container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_domestic_regular_container
msgid "Type of USPS domestic regular container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_container
msgid "Type of container"
msgstr ""

#. module: delivery_usps
#: model:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "USPS Configuration"
msgstr ""

#. module: delivery_usps
#: model:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_sale
#: model:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_stock
msgid "USPS Delivery Methods"
msgstr ""

#. module: delivery_usps
#: model:product.product,name:delivery_usps.product_product_delivery_usps_domestic
#: model:product.template,name:delivery_usps.product_product_delivery_usps_domestic_product_template
msgid "USPS Domestic Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:73
#, python-format
msgid ""
"USPS Domestic is used only to ship inside of the U.S.A. Please change the "
"delivery method into USPS International."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_first_class_mail_type
msgid "USPS First Class Mail Type"
msgstr ""

#. module: delivery_usps
#: model:product.product,name:delivery_usps.product_product_delivery_usps_international
#: model:product.template,name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "USPS International Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:71
#, python-format
msgid ""
"USPS International is used only to ship outside of the U.S.A. Please change "
"the delivery method into USPS Domestic."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_label_file_type
msgid "USPS Label File Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_mail_type
msgid "USPS Mail Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_service
msgid "USPS Service"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_username
msgid "USPS User ID"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier_usps_size_container
msgid "Usps Size Container"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:373
#, python-format
msgid "Your company or recipient ZIP code is incorrect."
msgstr ""
