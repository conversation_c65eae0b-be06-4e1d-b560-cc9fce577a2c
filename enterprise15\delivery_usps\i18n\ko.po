# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_usps
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:38+0000\n"
"Last-Translator: Sarah <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__abandon
msgid "Abandon"
msgstr "취소"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Account Validated"
msgstr "승인된 계정"

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Check this box if your account is validated by USPS"
msgstr "USPS에서 계정을 확인한 경우 이 확인란을 선택하십시오"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Company phone number is invalid. Please insert a US phone number."
msgstr "회사 전화 번호가 잘못되었습니다. 미국 전화 번호를 입력하십시오."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_content_type
msgid "Content Type"
msgstr "내용 유형"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_delivery_nature
msgid "Delivery Nature"
msgstr "배송 환경"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__documents
msgid "Documents"
msgstr "문서"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__domestic
msgid "Domestic"
msgstr "국내"

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""
"오류 :\n"
"%s"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__express
msgid "Express"
msgstr "특급"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__first_class
msgid "First Class"
msgstr "퍼스크 클래스"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__flat
msgid "Flat"
msgstr "고정 소수점"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatrate
msgid "Flat Rate"
msgstr "고정 비율"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_box
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatratebox
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatratebox
msgid "Flat Rate Box"
msgstr "고정 비율 상자"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatrateenv
msgid "Flat Rate Envelope"
msgstr "고정 비율 봉투"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__gift
msgid "Gift"
msgstr "선물"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__international
msgid "International"
msgstr "국제"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "Label Format"
msgstr "라벨 양식"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__large
msgid "Large"
msgstr "크게"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__largeenvelope
msgid "Large Envelope"
msgstr "큰 봉투"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__lg_flat_rate_box
msgid "Large Flat Rate Box"
msgstr "큰 고정 비율 상자"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__legal_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__legalflatrateenv
msgid "Legal Flat Rate Envelope"
msgstr "리갈 고정 비율 봉투"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__letter
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__letter
msgid "Letter"
msgstr "편지"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_machinable
msgid "Machinable"
msgstr "가공 가능"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__md_flat_rate_box
msgid "Medium Flat Rate Box"
msgstr "중간 고정 비율 상자"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__merchandise
msgid "Merchandise"
msgstr "상품"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_intl_non_delivery_option
msgid "Non delivery option"
msgstr "비 배송 선택사항"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__nonrectangular
msgid "Non-rectangular"
msgstr "직사각형이 아님"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "Options"
msgstr "옵션"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__package
msgid "Package"
msgstr "포장품"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_girth
msgid "Package Girth"
msgstr "패키지 둘레"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_height
msgid "Package Height"
msgstr "패키지 높이"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_length
msgid "Package Length"
msgstr "패키지 길이"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__package_service
msgid "Package Service"
msgstr "패키지 서비스"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_width
msgid "Package Width"
msgstr "패키지 폭"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__padded_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__paddedflatrateenv
msgid "Padded Flat Rate Envelope"
msgstr "패딩 고정 비율 봉투"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__parcel
msgid "Parcel"
msgstr "소포"

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_machinable
msgid ""
"Please check on USPS website to ensure that your package is machinable."
msgstr "패키지가 가공 가능한지 USPS 웹 사이트를 확인하십시오."

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"Please choose another service (maximum weight of this service is 4 pounds)"
msgstr "다른 서비스를 선택하십시오 (이 서비스의 최대 무게는 4 파운드입니다)"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please enter a valid ZIP code in recipient address"
msgstr "수신자 주소에 유효한 우편 번호를 입력하십시오"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please enter a valid ZIP code in your Company address"
msgstr "회사 주소에 유효한 우편 번호를 입력하십시오"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr "최소한 하나의 배송 품목을 입력해 주세요."

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"Please set country U.S.A in your company address, Service is only available "
"for U.S.A"
msgstr "회사 주소로 국가 USA를 설정하십시오. 서비스는 미국에서만 가능합니다"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__postcard
msgid "Postcard"
msgstr "엽서"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__priority
msgid "Priority"
msgstr "우선 순위"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "공급업체"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Quantity for each move line should be less than 1000."
msgstr "각 이동 라인의 수량은 1000보다 작아야합니다."

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Recipient address cannot be found. Please check the address exists."
msgstr "받는 사람 주소를 찾을 수 없습니다. 주소가 있는지 확인하십시오."

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__rectangular
msgid "Rectangular"
msgstr "직사각형"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__redirect
msgid "Redirect"
msgstr "리디렉션"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_redirect_partner_id
msgid "Redirect Partner"
msgstr "반송 파트너"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__regular
msgid "Regular"
msgstr "정기"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__variable
msgid "Regular < 12 inch"
msgstr "일반 < 12인치"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__return
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__return
msgid "Return"
msgstr "반납"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__sample
msgid "Sample"
msgstr "견본"

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment #%s has been cancelled"
msgstr "#%s 배송이 취소되었습니다"

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:0
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment created into USPS <br/> <b>Tracking Number : </b>%s"
msgstr "USPS 배송 <br/> <b>조회 번호 : </b>%s"

#. module: delivery_usps
#: model:ir.model,name:delivery_usps.model_delivery_carrier
msgid "Shipping Methods"
msgstr "선적 방법"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_box
msgid "Small Flat Rate Box"
msgstr "작은 고정 비율 상자"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_envelope
msgid "Small Flat Rate Envelope"
msgstr "작은 고정 비율 봉투"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__tif
msgid "TIF"
msgstr "TIF"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""
"회사 주소가 없거나 잘못되었습니다. (누락된 필드 : \n"
"%s)"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"다음 품목의 중량 정보가 누락되어 예상 배송 비용을 계산할 수 없습니다.\n"
"%s"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""
"수신자 주소가 없거나 잘못되었습니다. (누락된 필드 : \n"
"%s)"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "The selected USPS service (%s) cannot be used to deliver this package."
msgstr "선택한 USPS 서비스(%s)를 사용하여이 패키지를 배송할 수 없습니다."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_international_regular_container
msgid "Type of USPS International regular container"
msgstr "USPS International 일반 컨테이너 유형"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_domestic_regular_container
msgid "Type of USPS domestic regular container"
msgstr "USPS 국내 일반 컨테이너의 유형"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_container
msgid "Type of container"
msgstr "컨테이너 유형"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__delivery_type__usps
msgid "USPS"
msgstr "USPS"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "USPS Configuration"
msgstr "USPS 환경 설정"

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_domestic
#: model:product.product,name:delivery_usps.product_product_delivery_usps_domestic
#: model:product.template,name:delivery_usps.product_product_delivery_usps_domestic_product_template
msgid "USPS Domestic Flat Rate Envelope"
msgstr "USPS 국내 고정 비율 봉투"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"USPS Domestic is used only to ship inside of the U.S.A. Please change the "
"delivery method into USPS International."
msgstr "USPS Domestic은 미국 내 배송에만 사용됩니다. 배송 방법을 USPS International로 변경하십시오."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_first_class_mail_type
msgid "USPS First Class Mail Type"
msgstr "USPS 퍼스트 클래스 메일 유형"

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_international
#: model:product.product,name:delivery_usps.product_product_delivery_usps_international
#: model:product.template,name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "USPS International Flat Rate Box"
msgstr "USPS 국제 고정 비율 상자"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"USPS International is used only to ship outside of the U.S.A. Please change "
"the delivery method into USPS Domestic."
msgstr ""
"USPS International은 미국 이외의 지역으로 만 배송됩니다. 배송 방법을 USPS Domestic로 변경하십시오."

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_label_file_type
msgid "USPS Label File Type"
msgstr "USPS 파일 유형 꼬리표"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_mail_type
msgid "USPS Mail Type"
msgstr "USPS 메일 유형"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_service
msgid "USPS Service"
msgstr "USPS 서비스"

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_stock
msgid "USPS Shipping Methods"
msgstr "USPS 선적 방법"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_username
msgid "USPS User ID"
msgstr "USPS 사용자 ID"

#. module: delivery_usps
#: model:product.product,uom_name:delivery_usps.product_product_delivery_usps_domestic
#: model:product.product,uom_name:delivery_usps.product_product_delivery_usps_international
#: model:product.template,uom_name:delivery_usps.product_product_delivery_usps_domestic_product_template
#: model:product.template,uom_name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "Units"
msgstr "단위"

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_size_container
msgid "Usps Size Container"
msgstr "Usps 컨테이너 크기"

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Your company or recipient ZIP code is incorrect."
msgstr "회사 또는 수신자 우편 번호가 잘못되었습니다."

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "inch"
msgstr "인치"
