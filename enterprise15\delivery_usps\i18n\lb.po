# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_usps
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 11:38+0000\n"
"PO-Revision-Date: 2019-08-26 09:35+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__abandon
msgid "Abandon"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Account Validated"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_account_validated
msgid "Check this box if your account is validated by USPS"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Company phone number is invalid. Please insert a US phone number."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_content_type
msgid "Content Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_delivery_nature
msgid "Delivery Nature"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__documents
msgid "Documents"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__domestic
msgid "Domestic"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__express
msgid "Express"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__first_class
msgid "First Class"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__flat
msgid "Flat"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatrate
msgid "Flat Rate"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_box
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatratebox
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__flatratebox
msgid "Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__flatrateenv
msgid "Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__gift
msgid "Gift"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_delivery_nature__international
msgid "International"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__large
msgid "Large"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__largeenvelope
msgid "Large Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__lg_flat_rate_box
msgid "Large Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__legal_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__legalflatrateenv
msgid "Legal Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__letter
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__letter
msgid "Letter"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_machinable
msgid "Machinable"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__md_flat_rate_box
msgid "Medium Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__merchandise
msgid "Merchandise"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_intl_non_delivery_option
msgid "Non delivery option"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__nonrectangular
msgid "Non-rectangular"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__pdf
msgid "PDF"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_mail_type__package
msgid "Package"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__package_service
msgid "Package Service"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_girth
msgid "Package girth (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_height
msgid "Package height (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_length
msgid "Package length (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_custom_container_width
msgid "Package width (in inches)"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__padded_flat_rate_envelope
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_international_regular_container__paddedflatrateenv
msgid "Padded Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__parcel
msgid "Parcel"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,help:delivery_usps.field_delivery_carrier__usps_machinable
msgid ""
"Please check on USPS website to ensure that your package is machinable."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"Please choose another service (maximum weight of this service is 4 pounds)"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please enter a valid ZIP code in recipient address"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please enter a valid ZIP code in your Company address"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"Please set country U.S.A in your company address, Service is only available "
"for U.S.A"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_first_class_mail_type__postcard
msgid "Postcard"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_service__priority
msgid "Priority"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Quantity for each move line should be less than 1000."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Recipient address cannot be found. Please check the address exists."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__rectangular
msgid "Rectangular"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__redirect
msgid "Redirect"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_redirect_partner_id
msgid "Redirect Partner"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_size_container__regular
msgid "Regular"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_container__variable
msgid "Regular < 12 inch"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__return
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_intl_non_delivery_option__return
msgid "Return"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_content_type__sample
msgid "Sample"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment N° %s has been cancelled"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/delivery_usps.py:0
#: code:addons/delivery_usps/models/delivery_usps.py:0
#, python-format
msgid "Shipment created into USPS <br/> <b>Tracking Number : </b>%s"
msgstr ""

#. module: delivery_usps
#: model:ir.model,name:delivery_usps.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_box
msgid "Small Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_domestic_regular_container__sm_flat_rate_envelope
msgid "Small Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__usps_label_file_type__tif
msgid "TIF"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The address of your company is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The estimated price cannot be computed because the weight of your product is"
" missing."
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"The recipient address is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "The selected USPS service (%s) cannot be used to deliver this package."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_international_regular_container
msgid "Type of USPS International regular container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_domestic_regular_container
msgid "Type of USPS domestic regular container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_container
msgid "Type of container"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields.selection,name:delivery_usps.selection__delivery_carrier__delivery_type__usps
msgid "USPS"
msgstr ""

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.view_delivery_carrier_form_with_provider_usps
msgid "USPS Configuration"
msgstr ""

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_domestic
#: model:product.product,name:delivery_usps.product_product_delivery_usps_domestic
#: model:product.template,name:delivery_usps.product_product_delivery_usps_domestic_product_template
msgid "USPS Domestic Flat Rate Envelope"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"USPS Domestic is used only to ship inside of the U.S.A. Please change the "
"delivery method into USPS International."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_first_class_mail_type
msgid "USPS First Class Mail Type"
msgstr ""

#. module: delivery_usps
#: model:delivery.carrier,name:delivery_usps.delivery_carrier_usps_international
#: model:product.product,name:delivery_usps.product_product_delivery_usps_international
#: model:product.template,name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "USPS International Flat Rate Box"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid ""
"USPS International is used only to ship outside of the U.S.A. Please change "
"the delivery method into USPS Domestic."
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_label_file_type
msgid "USPS Label File Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_mail_type
msgid "USPS Mail Type"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_service
msgid "USPS Service"
msgstr ""

#. module: delivery_usps
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_usps.res_config_settings_view_form_stock
msgid "USPS Shipping Methods"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_username
msgid "USPS User ID"
msgstr ""

#. module: delivery_usps
#: model:product.product,uom_name:delivery_usps.product_product_delivery_usps_domestic
#: model:product.product,uom_name:delivery_usps.product_product_delivery_usps_international
#: model:product.template,uom_name:delivery_usps.product_product_delivery_usps_domestic_product_template
#: model:product.template,uom_name:delivery_usps.product_product_delivery_usps_international_product_template
msgid "Units"
msgstr ""

#. module: delivery_usps
#: model:ir.model.fields,field_description:delivery_usps.field_delivery_carrier__usps_size_container
msgid "Usps Size Container"
msgstr ""

#. module: delivery_usps
#: code:addons/delivery_usps/models/usps_request.py:0
#, python-format
msgid "Your company or recipient ZIP code is incorrect."
msgstr ""
