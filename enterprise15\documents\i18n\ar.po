# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents"
msgstr "%sمستندات"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents (%s locked)"
msgstr "%s مستندات ( %sمقفلة) "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_search_panel.js:0
#, python-format
msgid "%s file(s) not moved because they are locked by another user"
msgstr "%sلم يتم نقل الملف (الملفات) لأنه تم قفلها من قبل مستخدم آخر"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"
msgstr ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"مقفل \" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "+ Add a tag "
msgstr "+ إضافة علامة تصنيف  "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr "، انتهت صلاحيته في "

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". سوف تظهر الملفات الممسوحة تلقائياً في مساحة عملك. بعد ذلك، قم بمعالجة "
"مستنداتك دفعة واحدة باستخدام أداة التقسيم: أطلق الإجراءات التي يحددها "
"المستخدم، واطلب توقيعاً، وقم بتحويل فواتير الموردين باستخدام الذكاء "
"الاصطناعي، وما إلى ذلك. "

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2021"
msgstr "2021"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2022"
msgstr "2022"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr ""
"<b class=\"tip_title\">نصيحة: اجعل شركتك رقمية بالكامل وخالية من الأوراق</b>"
" "

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid ""
"<b> File uploaded by: </b> %s <br/>\n"
"                               <b> Link created by: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "
msgstr ""
"<b> تم رفع الملف من قِبَل: </b> %s <br/>\n"
"                               <b> أُنشئ الرابط بواسطة: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr "<b>بايت</b>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>قم بإلغاء تحديد هذه الصفحة</b> حيث أننا نود معالجة كافة فواتير الموردين "
"أولاً. "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr "<b>جيجابايت</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr "<b>كيلوبايت</b> "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr "<b>ميجابايت</b> "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>يعمل بواسطة"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"
msgstr ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" title=\"مساحة "
"العمل \"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"
msgstr ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"جاري التحميل \"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"
msgstr "<i class=\"fa fa-circle-thin o_record_selector\" title=\"تحديد المستند \"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/>  تنزيل الكل "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr "<i class=\"fa fa-download fa-fw\"/> تنزيل "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-globe\" title=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" title=\"رابط المستند \"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr "<i class=\"fa fa-link\"/> الذهاب إلى الرابط "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"
msgstr ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"علامات التصنيف \"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>  رفع"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr "<span class=\"o_stat_text\">الإجراءات</span> "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">المستندات</span> "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">السجل <br/> ذي الصلة</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span style=\"color:white;\">&amp;nbsp;Documents.</span>"
msgstr "<span style=\"color:white;\">والمستندات.</span> "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"المستند المطلوب \">المستند المطلوب</span> "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> طلب</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>المستند المطلوب</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before the link expires (planned on <t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            طلب المستند: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            مرحباً <t t-out=\"object.owner_id.name or ''\">أودو بوت</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">أودو بوت</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) يطلب منك تقديم الملف التالي:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">مثال على ملاحظة.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        قم برفع المستند المطلوب\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            الرجاء تزويدنا بالمستند المفقود قبل انتهاء صلاحية الرابط (مخطط في<t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">شركتك</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                تنتهي صلاحية هذا الرابط بتاريخ <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                            </t>\n"
"                            مشغل بواسطة <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">مستندات أودو</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"قاموس بايثون سيتم تقييمه لتوفير قيم افتراضية عند إنشاء سجلات جديدة لهذا "
"اللقب. "

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"سوف تكون هناك مجموعة من الشروط والإجراءات متُاحة لكافة المرفقات التي تستوفي "
"الشروط "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr "سوف تكتسب مساحة العمل نفس علامات تصنيف مساحة العمل الرئيسية "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "الوصول"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr "مجموعات الوصول"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr "صلاحيات الوصول"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr "رمز الوصول "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
#, python-format
msgid "Action"
msgstr "إجراء"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Action Button Name"
msgstr "اسم زر الإجراء "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr "عدد الإجراءات "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr "اسم الإجراء"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Actions"
msgstr "الإجراءات"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
msgid "Active"
msgstr "نشط"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr "الأنشطة"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Activity"
msgstr "النشاط"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr "ملاحظة النشاط"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "نوع النشاط"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr "نوع النشاط"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "إضافة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add File"
msgstr "إضافة ملف "

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "إضافة رابط"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Add a Link"
msgstr "إضافة رابط "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add new file"
msgstr "إضافة ملف جديد "

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "مدير"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr "إعلانات"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr "لقب"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr "أمان ألقاب جهات الاتصال "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr "اسم اللقب"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr "نطاق اللقب"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr "النموذج الملقب "

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "تم رفع كافة الملفات "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr "يتيح "

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr "يتيح لك إدارة مستنداتك. "

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Already linked Documents"
msgstr "المستندات المربوطة بالفعل "

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""
"طريقة سهلة لمعالجة البريد الوارد هي تهيئة الماسح الضوئي لإرسال ملفات PDF إلى"
" "

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"من الطرق السهلة لمعالجة البريد الوارد هي تهيئة الماسح الضوئي الخاص بك لإرسال"
" الملفات بصيغة PDF إلى البريد الإلكتروني الخاص بمساحة عملك. سوف تظهر الملفات"
" الممسوحة تلقائياً في مساحة عملك. بعد ذلك، قم بمعالجة مستنداتك دفعة واحدة "
"باستخدام أداة التقسيم: أطلق الإجراءات التي يحددها المستخدم، واطلب توقيعاً، "
"وقم بتحويل فواتير الموردين باستخدام الذكاء الاصطناعي، وما إلى ذلك. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Archive"
msgstr "أرشفة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Archive original file(s)"
msgstr "أرشفة الملف (الملفات) الأصلية "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Archived"
msgstr "مؤرشف"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Are you confirm deletion ?"
msgstr "أترغب في تأكيد الحذف؟ "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"فلنقم بتقسيم ملف الـPDF ومعالجته دفعة واحدة، حيث يحتوي على عدة مستندات. "

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr "طلب التصديق "

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr "الأصول"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr "مرفق بـ"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "مرفق"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "وصف المرفق "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "اسم المرفق"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "نوع المرفق "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_view.js:0
#, python-format
msgid "Attachments Kanban"
msgstr "طريقة عرض كانبان للمرفقات "

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid ""
"Automate your inbox using scanned documents or emails sent to <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."
msgstr ""
"قم بأتمتة صندوق الوارد الخاص بك باستخدام المستندات الممسوحة أو رسائل البريد "
"الإلكتروني المرسلة إلى <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> لقب "
"البريد الإلكتروني</span>. "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr "القواعد المُتاحة"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr "فاتورة"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr "العلامة التجارية 1"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr "العلامة التجارية 2"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr "منشورات "

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr "بتحديدك لمجلد، سوف تقوم الأنشطة المرفوعة بإنشاء مستند "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr "يمكنه الرفع "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Cancel"
msgstr "إلغاء "

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr "ضع مستنداتك الداخلية في فئات وقم بمشاركتها وتتبعها. "

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr "فئة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Checksum/SHA1"

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Choose a record to link"
msgstr "اختر سجلاً لربطه "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "اضغط على بطاقة <b>لاختيار المستند</b>. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "اضغط على الصورة المصغرة <b>لمعاينة المستند</b>. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"اضغط على <b>أداة تقسيم الصفحة</b>: لا نريد تقسيم هاتين الصفحتين حيث أنهما "
"ينتميان لنفس المستند. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click the cross to <b>exit preview</b>."
msgstr "اضعط على علامة × <b>للخروج من وضع المعاينة</b>. "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "الشركة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr "نوع الشرط"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "الشروط"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "التهيئة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Configure Email Servers"
msgstr "تهيئة خوادم البريد الإلكتروني "

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "جهة الاتصال"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr "يحتوي على "

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr "العقود"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "إنشاء"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Bill"
msgstr "إنشاء فاتورة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr "إنشاء مشاركة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr "إنشاء نشاط جديد"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr "المعايير "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "رسالة مرتدة مخصصة"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr "أيام "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "المستند الافتراضي "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr "القيم الافتراضية"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr "القيم الافتراضية للمستندات المرفوعة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr "إهمال "

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr "مهمَل "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr "الوصف"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Discard"
msgstr "تجاهل "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list
msgid "Document"
msgstr "المستند"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "عدد المستندات"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "اسم المستند"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr "مالك المستند"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "طلب مستند"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr "طلب مستند {{ object.name != False and ': '+ object.name or '' }}"

#. module: documents
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Document Request: %s Uploaded by: %s"
msgstr "طلب مستند: %s تم الرفع بواسطة: %s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document Request: Send by email"
msgstr "طلب المستند: الإرسال عن طريق البريد الإلكتروني "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document Thumbnail"
msgstr "الصورة المصغرة للمستند "

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr "إجراء علامة تصنيف سير عمل المستند "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr "قائمة المستندات"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.module.category,name:documents.module_category_documents_management
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
#, python-format
msgid "Documents"
msgstr "المستندات"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "المستندات المرتبطة بسجل "

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr "مشاركة المستندات"

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr "مساحة عمل المستندات "

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "مجموعة مخصصات إنشاء المستندات "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr "لا يحتوي على "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "النطاق"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Done"
msgstr "المنتهية"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Downlaod all files"
msgstr "تحميل كافة الملفات"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__download
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Download"
msgstr "تحميل"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Download all files"
msgstr "تحميل كافة الملفات "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__downloadupload
msgid "Download and Upload"
msgstr "تنزيل ورفع "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Drop files here to upload"
msgstr "أسقط الملفات هنا ليتم رفعها"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr "تاريخ الاستحقاق في"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr "نوع الاستحقاق"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Edit the linked Record"
msgstr "تحرير السجل المربوط "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "البريد الإلكتروني cc"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#: code:addons/documents/static/src/js/documents_document_viewer.js:0
#, python-format
msgid "Error"
msgstr "خطأ"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr "علامات التصنيف المستثناة "

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr "نفقة "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr "يحتوي هذا المستند على وجه بالفعل "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "مُفضلة لـ"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "الملف"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "محتوى الملف (base64) "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "حجم الملف"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "مركزية الملفات "

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr "المالية"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr "مالي "

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr "المالي "

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr "السنوات المالية "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr "المجلد"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome مثال: fa-tasks "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Generate URL"
msgstr "إنشاء رابط URL "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid ""
"Groups able to see the workspace and read its documents without create/edit "
"rights."
msgstr ""
"المجموعات القادرة على رؤية مساحة العمل وقراءة المستندات دون صلاحيات "
"الإنشاء/التحرير. "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr "المجموعات القادرة على رؤية مساحة العمل وقراءة/إنشاء/تحرير المستندات. "

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr "الموارد البشرية"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
#, python-format
msgid "History"
msgstr "السجل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr "المُعرف"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"مُعرف السجل الأصلي الذي يحتوي اللقب (مثال: 'المشروع' يحمل لقب إنشاء المهمة)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_document__message_unread
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"إذا كان محدداً، سوف يتم إرسال هذا المحتوى تلقائياً إلى المستخدمين غير المصرح"
" لهم عوضاً عن الرسالة الافتراضية. "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Image/Video"
msgstr "صورة/فيديو "

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr "صور"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "صندوق الوارد"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid ""
"Incoming letters sent to <span class=\"o_folder_description_alias\">inbox "
"email alias</span> will be added to your inbox automatically."
msgstr ""
"سوف تتم إضافة الرسائل الواردة التي يتم إرسالها إلى <span "
"class=\"o_folder_description_alias\">لقب البريد الإلكتروني</span> إلى صندوق "
"الوارد الخاص بك تلقائياً. "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "المحتوى المفهرس"

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr "داخلي"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "مرفق قابل للتحرير "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "مفضل "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr "المعرفة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document____last_update
#: model:ir.model.fields,field_description:documents.field_documents_facet____last_update
#: model:ir.model.fields,field_description:documents.field_documents_folder____last_update
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_share____last_update
#: model:ir.model.fields,field_description:documents.field_documents_tag____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr "قانوني "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"فلنقم بمعالجة الستندات في صندوق الوارد الخاص بك.<br/><i>نصيحة: استخدم علامات"
" التصنيف لإضفاء هيكل لعمليتك.</i> "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process these bills: send to Finance workspace."
msgstr "فلنقم بمعالجة تلك الفواتير: أرسلها لمساحة العمل المالية. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "فلنقم بمعالجة هذا المستند القادم من ماسحنا الضوئي. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"فلنقم بإضافة علامة تصنيف لهذا البريد الإلكتروني لتعليمه كقانوني<br/> "
"<i>نصيحة: يمكن تصميم الإجراءات لتلائم عملياتك تماماً وفقاً لمساحة العمل.</i>"
" "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr "قم بقصر مجموعات القراءة فقط على المستندات التي يمتلكونها. "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr "قم بقصر مجموعات الكتابة فقط على المستندات التي يمتلكونها. "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "الرابط"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr "الربط بالسجل "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__email_cc
msgid "List of cc from incoming emails."
msgstr "قائمة cc من رسائل البريد الإلكتروني الواردة."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr "مباشر "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Loading"
msgstr "جاري التحميل"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Lock"
msgstr "إغلاق"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr "مقفل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "مقفل بواسطة"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr "تسجيل ملاحظة..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "تسجيل الدخول"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "تسجيل الخروج"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "MB"
msgstr "ميجابايت "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_main_attachment_id
#: model:ir.model.fields,field_description:documents.field_documents_share__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr "التعيين كمسودة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr "تعيين الكل كمنتهي  "

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr "التسويق"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "نوع تنسيق الملف"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr "متفرقات "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "النموذج "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "النماذج "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr "شهور"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr "النقل لصندوق الوارد "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr "النقل إلى مساحة العمل "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Multiple values"
msgstr "قيم متعددة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "مستنداتي"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr "مفضلاتي"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "الاسم"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr "اسم رابط المشاركة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Name or Category contains: %s"
msgstr "يحتوي الاسم أو الفئة على: %s "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New File"
msgstr "ملف جديد "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New Group"
msgstr "مجموعة جديدة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "No document has been selected"
msgstr "لم يتم تحديد أي مستند "

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr "لا توجد أي روابط تمت مشاركتها "

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not a file"
msgstr "ليس ملفاً "

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not attached"
msgstr "ليس مرفقاً "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "ملاحظة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل فغير المقروءة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "شعار أودو"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "موقع أودو الإلكتروني "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr "سجل واحد كحد أقصى "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Open chatter"
msgstr "فتح الدردشة "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"معرف اختياري لمناقشة (سجل) الذي سيتم إرفاق كافة الرسائل الواردة به، حتى وإن "
"لم يقوموا بالرد عليه. إذا تم تعيين قيمة له، سيعطل هذا إنشاء السجلات الجديدة "
"بالكامل. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Or send emails to"
msgstr "أو أرسل رسائل البريد الإلكتروني إلى "

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr "غير ذلك"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr "المستندات الخاصة بك فقط "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr "المستندات الخاصة بك فقط (كتابة) "

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid ""
"Own Documents Only may not be enabled for write groups if it is not enabled "
"for read groups."
msgstr ""
"قد لا تكون \"المستندات الخاصة بك فقط\" مفعلة لمجموعات الكتابة إذا لم تكن "
"مفعلة لمجموعات القراءة. "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "المالك"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{document.create_uid.name}"
msgstr "المالك: #{document.create_uid.name} "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "PDF/Document"
msgstr "PDF/مستند "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "النموذج الرئيسي  "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "معرف مناقشة السجل الرئيسي "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr "مساحة العمل الرئيسية "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"النموذج الرئيسي الذي يحتفظ بلقب البريد الإلكتروني. ليس بالضرورة أن يكون "
"النموذج الذي يحتفظ بمرجع لقب البريد الإلكتروني هو النموذج المحدد في الحقل "
"alias_model_id (مثال: المشروع (النموذج الرئيسي) والمهمة (النموذج)) "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"سياسة لنشر رسالة على المستند باستخدام بوابة البريد الإلكتروني.\n"
"- الجميع: بمقدور الجميع النشر\n"
"- الشركاء: الشركاء المعتمدين فقط\n"
"- المتابعين: فقط متابعي المستند ذي الصلة أو أعضاء القنوات التالية.\n"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr "العروض التقديمية "

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr "المشروع"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr "صلاحيات القراءة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr "مجموعات القراءة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "السجل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr "معرف مناقشة السجل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
msgid "Related Workspace"
msgstr "مساحات العمل ذات الصلة "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "Remaining Pages"
msgstr "الصفحات المتبقية "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr "إزالة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Replace"
msgstr "استبدال"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr "استبدال بـ"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#, python-format
msgid "Request"
msgstr "طلب"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "طلب نشاط "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "طلب إلى "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/systray.xml:0
#, python-format
msgid "Request a Document"
msgstr "طلب مستند"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "طلب ملف"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Requested"
msgstr "مطلوب "

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "المستند المطلوب"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr "علامات التصنيف المطلوبة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "اسم نموذج Res "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "معرف المصدر"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "نموذج الموارد"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "اسم المصدر"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "المسؤول "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "استعادة"

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "Restricted Folder"
msgstr "مجلد مقيد "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr "المبيعات"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr "جدولة نشاط "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Search more..."
msgstr "البحث عن المزيد... "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Select All: Shift + A"
msgstr "تحديد الكل: Shift + A "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Select tags"
msgstr "تحديد علامات التصنيف "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"قم بإرسال هذه الرسالة إلى القسم القانوني عن طريق تعيين علامات التصنيف "
"الصحيحة. "

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr "الإرسال إلى القسم القانوني "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
msgid "Set As 2022 Contracts"
msgstr "التعيين كعقود 2022 "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr "تعيين جهة اتصال"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr "تعيين مالك"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr "تعيين علامات التصنيف "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr "قم بتعيين النشاط لدى صاحب المستند "

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "الإعدادات"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Share"
msgstr "مشاركة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr "مشاركة الروابط"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share domain"
msgstr "مشاركة النطاق "

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "Share link"
msgstr "مشاركة الرابط "

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr "مشاركة الروابط"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share selected records"
msgstr "مشاركة السجلات المحددة "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Share this domain"
msgstr "مشاركة هذا النطاق "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Share this selection"
msgstr "مشاركة هذا الاختيار "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr "نوع المشاركة "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr "تم إنشاء هذا المستند عن طريق المشاركة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Shared"
msgstr "مشارك"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr "المستندات التي تمت مشاركتها "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr "علامات التصنيف التي تمت مشاركتها "

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr "المشاركات ورسائل البريد الإلكتروني "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Size"
msgstr "الحجم"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr "تم ربط نموذج محدد "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Split"
msgstr "تقسيم"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#, python-format
msgid "Split PDF"
msgstr "تقسيم ملف PDF "

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "الحالة"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr "مساحات العمل الفرعية "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "الملخص"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "علامة تصنيف "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr "فئات علامات التصنيف "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr "فئة علامة التصنيف "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "اسم علامة التصنيف "

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr "توجد علامة تصنيف بالفعل لهذا الوجه "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr "فئات علامات التصنيف المحددة لمساحة العمل هذه "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Tags"
msgstr "علامات التصنيف "

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr "القوالب "

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr "النص"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr ""
"يتم إظهار النص عند التمرير فوق فئة علامات التصنيف هذه أو علامات تصنيفها "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"النموذج (مستندات أودو) الذي يقترن معه هذا اللقب. سوف تتسبب أي رسالة بريد "
"إلكتروني واردة لا ترُد على سجل موجود بإنشاء سجل جديد من نفس نوع هذا النموذج "
"(مثلًا: مهمة مشروع) "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"اسم لقب البريد الإلكتروني، مثلًا: 'وظائف' إذا كنت ترغب في جمع الرسائل "
"المرسلة لـ <<EMAIL>> "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"مالك السجلات المنشأة عند استلام رسائل بريد إلكتروني على هذا اللقب. إذا لم "
"يتم تعيين قيمة لهذا الحقل، سيحاول النظام معرفة المالك الصحيح حسب عنوان "
"البريد الإلكتروني للمرسل، أو سيستخدم حساب المشرف إذا لم يجد حسابًا مرتبطًا "
"بعنوان البريد الإلكتروني."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr "رمز الوصول هذا موجود بالفعل "

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "هذا المرفق مستند بالفعل "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr "سوف يكون هذا المرفق متاحاً فقط لمجموعات المستخدمين المحددة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"This document has been requested.\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"لقد تم طلب هذا المستند.\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">قم برفعه</b>. "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This link has expired"
msgstr "انتهت صلاحية هذا الرابط"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr "سوف تكون مساحة العمل هذه متاحة فقط للشركة المحددة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "صورة مصغرة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Tip: configure your scanner to send all documents to this address."
msgstr "نصيحة: قم بتهيئة ماسحك الضوئي لإرسال كافة المستندات إلى هذا العنوان. "

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "نصيحة: اجعل شركتك رقمية بالكامل دون استخدام أوراق "

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr "بانتظار التصديق "

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "بانتظار التصديق "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr "تلميح "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "تكون القيمة صحيحة إذا كان بمقدورنا تحرير مرفق الرابط. "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
msgid "Type"
msgstr "النوع"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط الاستثنائي المسجل."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "URL"
msgstr "رابط URL "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Un-archive"
msgstr "إلغاء الأرشفة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "إلغاء القفل "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Unnamed"
msgstr "غير مسمى"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل غير المقروءة "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
#, python-format
msgid "Upload"
msgstr "رفع"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"font-weight-normal\">a file or</span> drag <span "
"class=\"font-weight-normal\">it here.</span>"
msgstr ""
"قم برفع <span class=\"font-weight-normal\">الملف أو</span> سحبه <span "
"class=\"font-weight-normal\">إلى هنا.</span> "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Upload Document"
msgstr "رفع المستند "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr "الرفع عن طريق البريد الإلكتروني "

#. module: documents
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Upload file request"
msgstr "رفع طلب الملف "

#. module: documents
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "المستخدم"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr "ضريبة القيمة المضافة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr "صالحة حتى"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr "تصديق "

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr "تم التصديق "

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr "مقاطع الفيديو"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"أتود أن تصبح شركتك رقمية بالكامل <b>دون الحاجة إلى استخدام الأوراق </b>؟ "
"فلنستكشف مستندات أودو. "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "أسابيع"

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr "إجراءات سير العمل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr "قاعدة سير العمل"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr "مساحة العمل"

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
msgid "Workspaces"
msgstr "مساحات العمل "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"هذا رائع... لقد قمت بمعالجة 6 مستندات في بضع ثوان، أنت رائع.<br/>لقد انتهت "
"الجولة. والآن، حاول رفع مستنداتك الخاصة. "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr "صلاحيات الكتابة"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr "مجموعات الكتابة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr "اكتب تلميحاً لهذا الإجراء هنا "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "بإمكانك إما رفع ملف من جهازك أو نسخ ولصق رابط الملف. "

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "You cannot create recursive folders."
msgstr "لا يمكنك إنشاء مستندات متداخلة. "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Youtube Video"
msgstr "مقطع فيديو على اليوتيوب "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragEnter(section.id, valueId)"
msgstr "_onDragEnter(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragLeave"
msgstr "_onDragLeave"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDrop(section.id, valueId)"
msgstr "_onDrop(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "delete"
msgstr "حذف"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "document"
msgstr "مستند"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents"
msgstr "المستندات "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents selected"
msgstr "المستندات المحددة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents shared by"
msgstr "المستندات التي تمت مشاركتها من قِبَل "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.Category"
msgstr "documents.SearchPanel.Category"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.FiltersGroup"
msgstr "documents.SearchPanel.FiltersGroup"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "download"
msgstr "تنزيل "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr "مثال: مناقشة المقترح "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr "مثال: المالية "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "مثال: نفقة مفقودة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr "مثال: الحالة "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "مثال: للتصديق "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr "مثال: تصديق المستند "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "مثال: https://www.youtube.com/watch?v=CP96yVnXNrY "

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "restore"
msgstr "استعادة "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#, python-format
msgid "select"
msgstr "تحديد "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "selected"
msgstr "محدد "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "shared by"
msgstr "تمت مشاركته بواسطة"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "كود الحالة: %s, الرسالة: %s "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "toggle favorite"
msgstr "تبديل المفضلة "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "unnamed"
msgstr "غير مسمى "
