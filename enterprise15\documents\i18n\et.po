# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# R<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> Randmets, 2022
# JanaAvalah, 2023
# Birgit Vijar, 2024
# Anna, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Anna, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents"
msgstr "%s Dokumenti"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents (%s locked)"
msgstr "%s Dokumenti (%s lukustatud)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_search_panel.js:0
#, python-format
msgid "%s file(s) not moved because they are locked by another user"
msgstr "%s faili pole liigutatud, sest nad on teise kasutaja poolt lukustatud"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"
msgstr ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "+ Add a tag "
msgstr "+ Lisa silt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr ", aegub "

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". Skaneeritud failid ilmuvad automaatselt teie tööaknasse. Seejärel töödelge"
" töödelge oma dokumente erinevate tööriistaga: käivitage kasutaja määratud "
"toimingud, küsige allkirja, konverteerige tarnija arveid koos AI-ga jne."

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2021"
msgstr "2021"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2022"
msgstr "2022"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">Nõuanne: Alusta paberivaba ettevõtlusega</b>"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid ""
"<b> File uploaded by: </b> %s <br/>\n"
"                               <b> Link created by: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "
msgstr ""
"<b> Faili üles laadija: </b> %s <br/>\n"
"                               <b> Lingi looja: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr "<b>Baidid</b>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Eemaldage see leht</b>, kuna esmalt on vaja kõik arved läbi töötada."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr "<b>Gb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr "<b>Kb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr "<b>Mb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>Tootja"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"
msgstr ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"
msgstr ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"
msgstr "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/>  Laadi kõik alla"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr "<i class=\"fa fa-download fa-fw\"/> Laadi alla"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-globe\" title=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" title=\"Document url\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr "<i class=\"fa fa-link\"/> Mine URL'ile"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"
msgstr ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "Laadi üles"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr "<span class=\"o_stat_text\">Tegevused</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokumendid</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">Seotud <br/> Kirje</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span style=\"color:white;\">&amp;nbsp;Documents.</span>"
msgstr "<span style=\"color:white;\">&amp;nbsp;Dokumendid.</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Requested Document\">Dokumendipäring</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> Päring</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>Dokumendipäring</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before the link expires (planned on <t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- PÄIS -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Dokumendipäringt: <br/>\n"
"                                            % if object.name:\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                                                    ${object.name | safe}\n"
"                                                </span>\n"
"                                            % endif\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img src=\"/logo.png?company=${object.create_uid.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.create_uid.company_id.name}\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- SISU -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                           Tere, ${object.owner_id.name},\n"
"                                            <br/><br/>\n"
"                                            ${object.create_uid.name} (${object.create_uid.email}) palub teil esitada järgmine dokument:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    % if object.name:\n"
"                                                        <b>${object.name | safe}</b>\n"
"                                                    % endif\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    % if object.activity_note:\n"
"                                                        <i>${object.activity_note | safe}</i>\n"
"                                                    % endif\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a href=\"${object.full_url}\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Laadida üles järgmine dokument\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Palun esitage meile järgmine dokument, enne kui link kaotab kehtivuse (planeeritud ${object.date_deadline}).\n"
"                                            % if user and user.signature:\n"
"                                                <br/>\n"
"                                                ${user.signature | safe}\n"
"                                                <br/>\n"
"                                            % endif\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- JALUS -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        ${object.create_uid.company_id.name}\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        ${object.create_uid.company_id.phone}\n"
"                                        % if object.create_uid.company_id.email\n"
"                                            | <a href=\"'mailto:%s' % ${object.create_uid.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.create_uid.company_id.email}</a>\n"
"                                        % endif\n"
"                                        % if object.create_uid.company_id.website\n"
"                                            | <a href=\"'%s' % ${object.create_uid.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                                            ${object.create_uid.company_id.website}\n"
"                                        </a>\n"
"                                        % endif\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- TOOTJA -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            % if object.date_deadline:\n"
"                                See link kaotab kehtivuse <b>${object.date_deadline}.</b><br/>\n"
"                            % endif\n"
"                            Tootja <a target=\"_blank\" href=\"https://www.odoo.com/page/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Pythoni sõnastik, mida hinnatakse vaikimisi väärtuste määramiseks uute "
"kirjete loomiseks sellele aliasele."

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Tingimuste ja tegevuste kogumik, mis on saadaval kõikidele tingimusi "
"täitvatele manustele"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr "Tööalad pärivad selle ülemtööala sildid"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Ligipääs"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr "Ligipääsu gruppid"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr "Ligipääsuõigused"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr "Juurdepääsu võti"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
#, python-format
msgid "Action"
msgstr "Toiming"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Action Button Name"
msgstr "Tegevuse nupu nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr "Tegevuse arv"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr "Toimingu nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Actions"
msgstr "Toimingud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
msgid "Active"
msgstr "Tegev"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr "Tegevused"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Activity"
msgstr "Tegevus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandlik kohendus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr "Activity Note"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Tegevuse seis"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Tegevuse tüüp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tegevuse liigi sümbol"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr "Tegevuse tüüp"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Lisa"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add File"
msgstr "Lisa fail"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "Lisa URL"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Add a Link"
msgstr "Lisa link"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add new file"
msgstr "Lisa uus fail"

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Administraator"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr "Reklaamid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr "Alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr "Turvakontakti alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr "Aliase nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr "Aliase tegevusvaldkond"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr "Aliase mudel"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "Kõik failid on üles laaditud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr "Lubab"

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr "Võimaldab teil hallata dokumente."

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Already linked Documents"
msgstr "Juba seotud dokumendid"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""
"Lihtne moodus sissetulevate meilide haldamiseks on seadistada skänner "
"selliselt, et PDFid saadetakse"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"Lihtne moodus sissetulevate meilide haldamiseks on seadistada oma skännerid "
"selliselt, et PDFid saadetaks teie tööala e-mailile. Skännitud failid "
"ilmuvad automaatselt teie tööalale. Seejärel töötage korraga läbi oma "
"dokumente, kasutades poolitamise tööriista: käivitage kasutajaga määratud "
"tegevusi, taotlege allkirju, konverteerige tarnija arveid AI abil ümber jne."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Archive"
msgstr "Arhiveeri"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Archive original file(s)"
msgstr "Arhiveeri originaalfail(id)."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Are you confirm deletion ?"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Kuna see PDF sisaldab mitut dokumenti, poolitame ja töötleme faile korraga."

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr "Küsi kinnitust"

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr "Varad"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr "Lisatud millele"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Manus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "Manuse kirjeldus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Manuse nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "Manuse tüüp"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_view.js:0
#, python-format
msgid "Attachments Kanban"
msgstr "Manuste kanban"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid ""
"Automate your inbox using scanned documents or emails sent to <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."
msgstr ""
"Automatiseerige oma postkast kasutatades skanneeritud dokumente või "
"meilidega, mis on saadetud <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr "Saadaval reeglid"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr "Arve"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr "Bränd 1"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr "Bränd 2"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr "Brošüürid"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr "Faili defineerides genereevad üleslaadimise tegevused dokumendi."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr "Saab üles laadida"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Cancel"
msgstr "Tühista"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr ""
"Kategoriseeri, jaga ja hoia järge kõikidel oma sisemistel dokumentidel."

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr "Kategooria"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Kontrollsumma/SHA1"

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Choose a record to link"
msgstr "Vali, millega soovid siduda"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "Vajutage kaardile <b>dokumendi valimiseks</b>."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Vajutage pildile <b>dokumendi eelvaate</b>saamiseks."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Vajutage <b>lehe eraldajale</b>: me ei soovi poolitada neid kahte lehte, "
"kuna nad kuuluvad sama dokumendi alla."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click the cross to <b>exit preview</b>."
msgstr "Vajuta ristile, et <b>sulgeda eelvaade</b>."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "Ettevõte"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr "Tingimuse tüüp"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "Tingimused"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Seadistus"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Configure Email Servers"
msgstr "Seadista e-posti serverid"

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Kontakt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr "Sisaldab"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr "Lepingud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Loo"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Bill"
msgstr "Loo arve"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr "Looge jagamine"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr "Loo uus tegevus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Loonud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Loomise kuupäev"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr "Kriteerium"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Kohandatud automaatsõnum"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr "päev(a)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Vaikimisi kasutaja"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr "Vaikeväärtused"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr "Vaikimisi väärtused üleslaetud dokumentidele"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
#, python-format
msgid "Delete"
msgstr "Kustuta"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr "Amortiseerige"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr "Ei kasutata enam"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr "Kirjeldus"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Discard"
msgstr "Loobu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list
msgid "Document"
msgstr "Dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Dokumendi arv"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "Dokumendi nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr "Dokumendi omanik"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "Dokumendipäring"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""
"Dokumenditaotlus {{ object.name != False ja ': '+ object.name or '' }}"

#. module: documents
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Document Request: %s Uploaded by: %s"
msgstr "Dokumendipäring: %s Üleslaadija: %s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document Request: Send by email"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document Thumbnail"
msgstr "Dokumendi pilt"

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr "Dokumendi töövoo sildi tegevus"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr "Dokumendi nimekiri"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.module.category,name:documents.module_category_documents_management
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
#, python-format
msgid "Documents"
msgstr "Dokumendid"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "Documents Link to Record"

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr "Dokumentide jagamine"

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr "Dokumentide tööala"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "Documents creation mixin"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr "Ei sisalda"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "Domeen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Done"
msgstr "Tehtud"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Downlaod all files"
msgstr "Laadige kõik failid alla"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__download
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Download"
msgstr "Laadige alla"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Download all files"
msgstr "Laadige kõik failid alla"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__downloadupload
msgid "Download and Upload"
msgstr "Allalaadimine ja üleslaadimine"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Drop files here to upload"
msgstr "Pane oma fail üleslaadimiseks siia"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr "Tähtaeg"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr "Tähtaja liik"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Edit the linked Record"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "Koopia"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#: code:addons/documents/static/src/js/documents_document_viewer.js:0
#, python-format
msgid "Error"
msgstr "Viga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr "Välistatud sildid"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr "Kulu"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr "Aegunud"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr "Taoline aspekt on selles failis juba olemas"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "Lemmik"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Fail"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "Faili sisu (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Faili suurus"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "Faili koondamine"

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr "Finants"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr "Finants"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr "Maksualane"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr "Majandusaastad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr "Kaust"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad (partnerid)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon nt. fa-tasks"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Generate URL"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr "Grupeeri"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid ""
"Groups able to see the workspace and read its documents without create/edit "
"rights."
msgstr ""
"Grupid, kes näevad tööala ja saavad selle dokumente lugeda ilma loomis- ja "
"muutmisõigusteta."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr "Grupid, kes saavad tööala dokumente lugeda, muuta ja luua."

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr "Personal"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
#, python-format
msgid "History"
msgstr "Ajalugu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Algse kirje ülem ID, millele kuulub alias (nt: projekt, millele kuulub "
"ülesande loomise alias)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Ikoon"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_document__message_unread
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_unread
msgid "If checked, new messages require your attention."
msgstr "Kui märgitud, siis uued sõnumid nõuavad Su tähelepanu."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningatel sõnumitel saatmiserror."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Kui määratud, saadetekse see sisu vaikimisi sisu asemel automaatselt "
"lubamatutele kasutajale."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Image/Video"
msgstr "Pilt/Video"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr "Pildid"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Saabuvad"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid ""
"Incoming letters sent to <span class=\"o_folder_description_alias\">inbox "
"email alias</span> will be added to your inbox automatically."
msgstr ""
"Saabuvad kirjad, mis on saadetud <span "
"class=\"o_folder_description_alias\">e-maili aliasele</span> , lisatakse "
"automaatselt Teie postkasti."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Indekseeritud sisu"

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr "Sisemine"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "Muudetav manus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "On lemmikutesse lisatud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr "Jälgija"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr "Teadmised"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document____last_update
#: model:ir.model.fields,field_description:documents.field_documents_facet____last_update
#: model:ir.model.fields,field_description:documents.field_documents_folder____last_update
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_share____last_update
#: model:ir.model.fields,field_description:documents.field_documents_tag____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr "Õiguslik"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Haldame teie postkasti dokumente.<br/><i> Nipp: Kasutage dokumentide "
"filtreerimiseks ja struktureerimiseks silte.</i>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process these bills: send to Finance workspace."
msgstr "Töötleme need arved läbi: saada Finants tööalale."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "Töötleme selle dokumendi läbi meie skänneri."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Märgime selle arve ära kui õiguslik<br/> <i>Nipid: tegevused saavad olla "
"tööalale vastavalt olla loodud teie protsessile sobivalt."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr ""
"Piira dokumentide loetavuse gruppe vastavalt sellele, mille omanikud nad on."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr ""
"Piira dokumentide kirjutamisgruppe vastavalt sellele, mille omanikud nad on."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Link"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr "Link to record"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__email_cc
msgid "List of cc from incoming emails."
msgstr "Saabuvate kirjade koopiate loend."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr "Online"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Loading"
msgstr "Laadimine"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Lock"
msgstr "Lukusta"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr "Lukustatud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "Lukustatud"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr "Kirjuta logimärkus..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Sisselogimine"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "Logi välja"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "MB"
msgstr "MB"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_main_attachment_id
#: model:ir.model.fields,field_description:documents.field_documents_share__message_main_attachment_id
msgid "Main Attachment"
msgstr "Peamine manus"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr "Märgi mustandiks"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr "Märgi kõik tehtuks"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr "Turundus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi edastamise veateade"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "Sõnumid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "Failitüüp"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr "Muu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Mudel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "Mudelid"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr "kuu järel"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr "Liiguta postkasti"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr "Liiguta tööalale"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Multiple values"
msgstr "Mitu väärtust"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Minu tegevuse tähtaeg"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "Minu dokumendid"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr "Minu lemmikud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Nimi"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr "Jagamislingi nimi"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Name or Category contains: %s"
msgstr "Nimi või kategooria sisaldab: %s"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New File"
msgstr "Uus fail"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New Group"
msgstr "Uus grupp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Järgmine tegevus kalendris"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse kuupäev"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "No document has been selected"
msgstr "Ühtegi dokumenti pole valitud"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr "Jagatud lingid puuduvad"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not a file"
msgstr "Ei ole fail"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not attached"
msgstr "Pole manustatud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "Märkus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimingute arv"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr "Veateadete arv"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Tegutsemist nõudvate sõnumite arv"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kohaletoimetamise veateatega sõnumite arv"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_unread_counter
msgid "Number of unread messages"
msgstr "Lugemata sõnumite arv"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo logo"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo Veebileht"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr "Ühe kirje limiit"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Open chatter"
msgstr "Avage vestlus"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Seose (kirje) valikuline ID, mille külge lisatakse kõik sissetulevad "
"sõnumid, isegi kui sellele ei vastatud. Kui määratud, siis kaob täielikult "
"võimalus luua uusi kirjeid."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Or send emails to"
msgstr "Või saada meilid"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr "Muu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr "Ainult enda dokumendid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr "Ainult oma dokumendid (kirjutamine)"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid ""
"Own Documents Only may not be enabled for write groups if it is not enabled "
"for read groups."
msgstr ""
"Ainult oma dokumendid ei tohi olla lubatud kirjutusrühmadele, kui see ei ole"
" lubatud lugemisrühmadele."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Omanik"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{document.create_uid.name}"
msgstr "Omanik: #{document.create_uid.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "PDF/Document"
msgstr "PDF/Dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "Ülemmudel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Ülemkirje seose ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr "Peamine tööala"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Ülemmudel, millel on alias. Aliase mudeli viide ei pruugi olla mudel, mis on"
" antud nii: alias_model_id (näide: projekt (ülem_mudel) ja ülesanne (mudel))"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Dokumendil sõnumite postitamise reeglid mailgateway'd kasutades.\n"
"- igaüks: igaüks saab postitada\n"
"- partnerid: ainult autenditud partnerid\n"
"- jälgijad: ainult dokumendiga seotud jälgijad või nende kanalite liikmed\n"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr "Esitlused"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr "Projekt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr "Lugemisõigus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr "Lugemisgrupp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "Record"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Kirje seose ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
msgid "Related Workspace"
msgstr "Seotud tööala"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "Remaining Pages"
msgstr "Järelejäänud leheküljed"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr "Eemalda"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Replace"
msgstr "Asenda"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr "Asenda"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#, python-format
msgid "Request"
msgstr "Päring"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "Taotle tegevust"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "Esita päring "

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/systray.xml:0
#, python-format
msgid "Request a Document"
msgstr "Esitage dokumendipäring"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "Esitage failipäring"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Requested"
msgstr "Päring esitatud"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "Dokumendipäring"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr "Nõutud sildid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "Res Mudeli Nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "Ressursi ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "Ressursimudel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Ressursi nimi"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "Vastutaja"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Taasta"

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "Restricted Folder"
msgstr "Piiratud kaust"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise veateade"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr "Müük"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr "Planeeri tegevus"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Search more..."
msgstr "Otsi veel..."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Select All: Shift + A"
msgstr "Vali kõik: Shift + A"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Select tags"
msgstr "Vali sildid"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "Saatke see kiri õigusosakonda, määrates õiged sildid külge."

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr "Saatke Õiguslikku."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr "Järjestus"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
msgid "Set As 2022 Contracts"
msgstr "Määra 2022. lepinguteks"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr "Määra kontakt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr "Määra omanik"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr "Määra sildid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr "Määrake dokumendi omanikule tegevus"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Seaded"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Share"
msgstr "Jaga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr "Jaga linke"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share domain"
msgstr "Jagage domeeni"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "Share link"
msgstr "Jagage linki"

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr "Jaga linke"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share selected records"
msgstr "Jagage valitud kirjeid"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Share this domain"
msgstr "Jaga seda domeeni"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Share this selection"
msgstr "Jaga seda valikut"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr "Jagamise tüüp"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr "Selle dokumendi loomiseks kasutatud jagamine"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Shared"
msgstr "Jagatud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr "Jagatud dokumendid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr "Jagatud sildid"

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr "Jagamised ja e-kirjad"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Size"
msgstr "Suurus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr "Konkreetne seotud mudel"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Split"
msgstr "Poolita"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#, python-format
msgid "Split PDF"
msgstr "Poolita PDF"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "Staatus"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr "Alamtööalad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "Kokkuvõte"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Silt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr "Siltide kategooriad"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr "Sildi kategooria"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Sildi nimi"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr "Sellele aspektile on juba silt olemas"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr "Sellele tööalale määratud sildi kategooriad"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Tags"
msgstr "Sildid"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr "Mallid"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr "Tekst"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr ""
"Tekst, mida kuvatakse, kui liigutakse selle sildi või sildikategooria peale"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Aliasele vastav mudel (Odoo dokumendi laadi). Iga sissetulev kiri, mis ei "
"vasta olemasolevale kirjele, põhjustab uue mudeli kirje loomise (nt projekti"
" ülesanne)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"E-maili aliase nimi, nt \"job\" kui soovid saada e-kirju "
"<<EMAIL>>"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"E-maili saamisel loodud kirjete omanik, mis tulevad sellele e-posti "
"aliasele. Kui see väli pole määratud, siis süsteem üritab leida õige omaniku"
" lähtuvalt saatja (From) aadressist või kasutab administraatori kontot, kui "
"ei leita ühtegi süsteemikontot sellise e-posti aadressiga."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr "See ligipääsuluba juba eksisteerib"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "See manus on juba dokument"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr "See manus on saadaval ainult valitud kasutajagruppidel"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"This document has been requested.\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"Selle dokumendi kohta on esitatud päring..\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Laadige see üles</b>."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This link has expired"
msgstr "See link on aegunud"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr "See tööala on saadaval ainult valitud ettevõttele"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "Pisipilt"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""
"Vihje: konfigureerige oma skanner nii, et kõik dokumendid saadetakse sellele"
" aadressile."

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "Nipp: Hakake paberivabaks ettevõtteks"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr "Kinnitada"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "Kinnitada"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr "Vihje"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "Tõene, kui saame muuta lingi manust."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
msgid "Type"
msgstr "Tüüp"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "URL"
msgstr "URL"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Un-archive"
msgstr "Eemalda arhiivist"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "Lukusta lahti"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Unnamed"
msgstr "Nimetu"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread
msgid "Unread Messages"
msgstr "Lugemata sõnumid"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lugemata sõnumite loendur"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
#, python-format
msgid "Upload"
msgstr "Üleslaadimine"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"font-weight-normal\">a file or</span> drag <span "
"class=\"font-weight-normal\">it here.</span>"
msgstr ""
"Laadige üles <span class=\"font-weight-normal\">fail või</span> lohistage "
"<span class=\"font-weight-normal\">see siia.</span>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Upload Document"
msgstr "Lae dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr "Laadige e-mailiga üles"

#. module: documents
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Upload file request"
msgstr "Lae üles faili päring"

#. module: documents
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Kasutaja"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr "KMKR nr"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr "Kehtiv kuni"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr "Kinnita"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr "Kinnitatud"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr "Videod"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Soovite hakata <b>paberivabaks ettevõtteks</b>? Avastame Odoo Dokumendid."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "nädala järel"

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr "Töövoo toimingud"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr "Töövoo reegel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr "Tööala"

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
msgid "Workspaces"
msgstr "Tööalad"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Vau... 6 dokumenti said mõne sekundiga läbi töödeldud. <br/>Tuur on läbitud."
" Proovige nüüd iseseisvalt omi dokumente üles laadida."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr "Kirjutusõigus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr "Kirjutamisgrupp"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr "Kirjutage sellele tegevusele siia töövihje"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Saate faili üles laadida oma arvutisse või kopeerida / kleepida veebilehe "
"lingi oma failile"

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "You cannot create recursive folders."
msgstr "Te ei saa luua korduvaid kaustu."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Youtube Video"
msgstr "Youtube Video"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragEnter(section.id, valueId)"
msgstr "_onDragEnter(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragLeave"
msgstr "_onDragLeave"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDrop(section.id, valueId)"
msgstr "_onDrop(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "delete"
msgstr "kustuta"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "document"
msgstr "dokument"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents"
msgstr "dokumendid"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents selected"
msgstr "valitud dokumendid"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents shared by"
msgstr "dokumendid, mis on jagatud"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.Category"
msgstr "documents.SearchPanel.Category"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.FiltersGroup"
msgstr "documents.SearchPanel.FiltersGroup"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "download"
msgstr "laadi alla"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr "nt aruta ettepanekut"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr "nt. Finants"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "nt. Kulu puudub"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr "nt- Staatus"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "nt. Kinnitamiseks"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr "nt. Kinnita dokumen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "nt: https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "ainult_lugemiseks"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "restore"
msgstr "taasta"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#, python-format
msgid "select"
msgstr "vali"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "selected"
msgstr "valitud"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "shared by"
msgstr "jagatud"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "staatuse kood: %s, sõnum: %s"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "toggle favorite"
msgstr "toggle lemmik"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "unnamed"
msgstr "nimetu"
