# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON><PERSON><PERSON> <i3rendszer<PERSON><EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# krnkris, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# Tamás Dombos, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: gezza <<EMAIL>>, 2023\n"
"Language-Team: Hungarian (https://www.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents"
msgstr "%s dokumentum"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents (%s locked)"
msgstr "%s dokumentum (%s zárolt)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_search_panel.js:0
#, python-format
msgid "%s file(s) not moved because they are locked by another user"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"
msgstr ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "+ Add a tag "
msgstr "+ Címke hozzáadása"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr ", lejárt ekkor:"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2021"
msgstr "2021"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2022"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr ""

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid ""
"<b> File uploaded by: </b> %s <br/>\n"
"                               <b> Link created by: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr "<b>Bájt</b>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr "<b>GB</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr "<b>KB</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr "<b>MB</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>Támogatja a(z)"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"
msgstr ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"
msgstr ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"
msgstr "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/>  Összes letöltése"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr "<i class=\"fa fa-download fa-fw\"/> Letöltés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-globe\" title=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" title=\"Document url\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr "<i class=\"fa fa-link\"/> Webcím felkeresése"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"
msgstr ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>  Feltöltés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr "<span class=\"o_stat_text\">Műveletek</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokumentumok</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span style=\"color:white;\">&amp;nbsp;Documents.</span>"
msgstr "<span style=\"color:white;\">&amp;nbsp;dokumentumok.</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b>Kérés</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>Kért dokumentum</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before the link expires (planned on <t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Egy Python szótár, ami kiértékelésre kerül az alapértelmezett értékek "
"szolgáltatása érdekében ezen álnév új rekordjainak létrehozásakor."

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Feltételek és műveletek gyűjteménye, amely a feltételnek megfelelő összes "
"mellékletnél elérhető"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr "A munkaterület örökölni fogja szülő munkaterület címkéit"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Hozzáférés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr "Csoport hozzáférés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr "Hozzáférési jogok"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr "Hozzáférési token"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
#, python-format
msgid "Action"
msgstr "Művelet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Action Button Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr "Műveletszám"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr "Művelet neve"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr "Művelet szükséges"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Actions"
msgstr "Műveletek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
msgid "Active"
msgstr "Aktív"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr "Tevékenységek"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Activity"
msgstr "Tevékenység"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tevékenység kivétel dekoráció"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr "Tevékenység megjegyzés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Tevékenység állapota"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Tevékenység típus"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tevékenység típus ikon"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr "Tevékenységtípus"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Hozzáadás"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add File"
msgstr "Fájl hozzáadása"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "Webcím hozzáadása"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Add a Link"
msgstr "Hivatkozás  hozzáadása"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add new file"
msgstr "Új fájl"

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Adminisztrátor"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr "Hirdetések"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr "Álnév"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr "Álnév kapcsolat biztonság"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr "Álnév elnevezése"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr "Álnév tartomány"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr "Álnevesített modell"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "Minden fájl feltöltve"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr "Lehetővé teszi"

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr "Lehetővé teszi a dokumentumok kezelését."

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Already linked Documents"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Archive"
msgstr "Archiválás"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Archive original file(s)"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Archived"
msgstr "Archivált"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Are you confirm deletion ?"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr ""

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr "Vagyontárgyak"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr "Csatolva a következőhöz"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Melléklet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr "Mellékletek száma"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "Melléklet leírása"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Csatolmány neve"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "Melléklet típusa"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_view.js:0
#, python-format
msgid "Attachments Kanban"
msgstr "Kanban mellékletek"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid ""
"Automate your inbox using scanned documents or emails sent to <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr "Elérhető szabályok"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr "Számla"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr "Márka 1"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr "Márka 2"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr "Brosúrák"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr "Feltölthet"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Cancel"
msgstr "Mégsem"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr "Kategória"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Ellenőrző összeg/SHA1"

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Choose a record to link"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "Vállalat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr "Feltétel típusa"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "Feltételek"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Konfiguráció"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Configure Email Servers"
msgstr "E-mail szerverek beállítása"

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Kapcsolat"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr "Tartalmaz"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr "Szerződések"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Létrehozás"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Bill"
msgstr "Számla létrehozása"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr "Új megosztás"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr "Új tevékenység"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Létrehozta"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Létrehozva"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr "Kritériumok"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr "nap"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Alapértelmezett felhasználó"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr "Alapértelmezett értékek"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr "A feltöltött dokumentumok alapértelmezett értékei"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
#, python-format
msgid "Delete"
msgstr "Törlés"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr "Elavult"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr "Leírás"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Discard"
msgstr "Elvetés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr "Név megjelenítése"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list
msgid "Document"
msgstr "Dokumentum"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Dokumentum száma"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "Dokumentum név"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr "Dokumentum tulajdonosa"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "Dokumentum kérése"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""

#. module: documents
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Document Request: %s Uploaded by: %s"
msgstr ""

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document Request: Send by email"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document Thumbnail"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr "Dokumentum munkafolyamat címke akció"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr "Dokumentum lista"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.module.category,name:documents.module_category_documents_management
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
#, python-format
msgid "Documents"
msgstr "Dokumentumok"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr "Dokumentumok megosztása"

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr "Dokumentumok munkaterület"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr "Nem tartalmazza"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "Tartomány"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Done"
msgstr "Kész"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Downlaod all files"
msgstr "Összes fájl letöltése"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__download
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Download"
msgstr "Letöltés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Download all files"
msgstr "Összes fájl letöltése"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__downloadupload
msgid "Download and Upload"
msgstr "Letöltés és feltöltés"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Drop files here to upload"
msgstr "Ejtsen ide fájlokat a feltöltésükhöz"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr "Esedékes"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr "Esedékesség típusa"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Edit the linked Record"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "Másolat"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#: code:addons/documents/static/src/js/documents_document_viewer.js:0
#, python-format
msgid "Error"
msgstr "Hiba"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr "Kizárt címkék"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr "Költség"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr "Lejárt"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "Kedvencnek jelölte: "

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Fájl"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "Fájltartalom (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Fájlméret"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "Fájlok központosítása"

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr "Pénzügy"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr "Pénzügyi"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr "Költségvetés"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr "Költségvetési év"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr "Mappa"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr "Követők"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr "Követők (Partnerek)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikon pld: fa-tasks"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Generate URL"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr "Csoportosítás"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid ""
"Groups able to see the workspace and read its documents without create/edit "
"rights."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr "Személyügy"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr "Van üzenet"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
#, python-format
msgid "History"
msgstr "Előzmények"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr "Azonosító"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Az álnevet tartalmazó szülő rekord azonosítója (példa: a feladat "
"létrehozására szolgáló álnevet tartalmazó projekt)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kivétel tevékenységet jelző ikon"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_document__message_unread
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ha be van jelölve, akkor az új üzenetek figyelmet igényelnek."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Ha be van jelölve, akkor néhány üzenetnél kézbesítési hiba lépett fel."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Image/Video"
msgstr "Kép/Videó"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr "Képek"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Beérkező levelek"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid ""
"Incoming letters sent to <span class=\"o_folder_description_alias\">inbox "
"email alias</span> will be added to your inbox automatically."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Indexált tartalom"

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr "Belső"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "Kedvelt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr "Követő"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr "Tudás"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document____last_update
#: model:ir.model.fields,field_description:documents.field_documents_facet____last_update
#: model:ir.model.fields,field_description:documents.field_documents_folder____last_update
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_share____last_update
#: model:ir.model.fields,field_description:documents.field_documents_tag____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule____last_update
msgid "Last Modified on"
msgstr "Legutóbb módosítva"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr "Frissítve "

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr "Jogi"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process these bills: send to Finance workspace."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Hivatkozás"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__email_cc
msgid "List of cc from incoming emails."
msgstr ""

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr "Élő"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Loading"
msgstr "Betöltés"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Lock"
msgstr "Zárolás"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr "Zárolt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "Zárolta"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr "Megjegyzés rögzítése..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Bejelentkezés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "Kilépés"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "MB"
msgstr "MB"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_main_attachment_id
#: model:ir.model.fields,field_description:documents.field_documents_share__message_main_attachment_id
msgid "Main Attachment"
msgstr "Fő melléklet"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr "Megjelöl piszkozatként"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr "Késznek jelöl mindent"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr "Marketing"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr "Üzenetkézbesítési hiba"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "Üzenetek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "MIME típus"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr "Egyéb"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Modell"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "Modellek"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Hónap"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Multiple values"
msgstr "Többszörös érték"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Tevékenységeim határideje"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "Dokumentumaim"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr "Kedvenceim"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Név"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr "Megosztási hivatkozás neve"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Name or Category contains: %s"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New File"
msgstr "Új fájl"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New Group"
msgstr "Új csoport"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Következő tevékenység naptár esemény"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Következő tevékenység határideje"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Következő tevékenység összegzés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Következő tevékenység típusa"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "No document has been selected"
msgstr "Nincs dokumentum kiválasztva"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr "Nincsenek megosztott hivatkozások"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not a file"
msgstr "Nem fájl"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not attached"
msgstr "Nincs mellékelve"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "Megjegyzés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr "Műveletek száma"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr "Hibák száma"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Üzenetek száma, melyek műveletet igényelnek"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kézbesítési hibával rendelkező üzenetek száma"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_unread_counter
msgid "Number of unread messages"
msgstr "Olvasatlan üzenetek száma"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo logó"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Az Odoo weboldala"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Open chatter"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Az üzenetlista (rekord) választható azonosítója, amelyhez minden beérkező "
"üzenet csatolásra kerül, még akkor is, ha senki sem válaszolt azokra. Ha "
"beállításra kerül, az új rekordok létrehozása teljesen kikapcsolásra kerül."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Or send emails to"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr "Egyéb"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr "Csak a saját dokumentumok"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr "Csak a saját dokumentumok (Írás)"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid ""
"Own Documents Only may not be enabled for write groups if it is not enabled "
"for read groups."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Tulajdonos"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{document.create_uid.name}"
msgstr "Tulajdonos: #{document.create_uid.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "PDF/Document"
msgstr "PDF/Dokumentum"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "Szülő modell"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Szülő rekord szál azonosító"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr "Szülő munkaterület"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Az álnevet tartalmazó szülő modell. Az álnév hivatkozást tartalmazó modell "
"nem feltétlenül azonos az alias_model_id által megadott modellel (például: "
"projekt (parent_model) és feladat (model))."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"A dokumentumra küldött üzenetek szabályai a levélátjáró használatával.\n"
"- mindenki: mindenki tud üzenni\n"
"- partnerek: csak a hitelesített partnerek\n"
"- követők: csak a kapcsolódó dokumentum követői vagy a következő csatornák tagjai\n"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr "Prezentációk"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr "Projekt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr "Olvasás hozzáférés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr "Olvasás csoportok"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "Bejegyzés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Bejegyzés üzenetlista azonosító"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
msgid "Related Workspace"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "Remaining Pages"
msgstr "Hátralevő oldalak"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr "Eltávolítás"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Replace"
msgstr "Kicserél"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr "Kicserélés erre"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#, python-format
msgid "Request"
msgstr "Kérelem"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/systray.xml:0
#, python-format
msgid "Request a Document"
msgstr "Dokumentum kérése"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "Fájl kérése"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Requested"
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "Kért dokumentum"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr "Szükséges címkék"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "Erőforrás azonosítója"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "Erőforrás modell"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Erőforrás neve"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "Felelős"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Felelős felhasználó"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Visszaállítás"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS kézbesítési hiba"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr "Értékesítés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr "Ütemezett művelet"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Search more..."
msgstr "További keresés..."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Select All: Shift + A"
msgstr "Mindent kijelöl: Shift + A"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Select tags"
msgstr "Címkék kiválasztása"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
msgid "Set As 2022 Contracts"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr "Kapcsolat beállítása"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr "Tulajdonos beállítása"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr "Címkék beállítása"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Beállítások"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Share"
msgstr "Megosztás"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr "Hivatkozások megosztása"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share domain"
msgstr "Tartomány megosztása"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "Share link"
msgstr "Hivatkozás megosztása"

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr "Hivatkozások megosztása"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share selected records"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Share this domain"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Share this selection"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr "Megosztás típusa"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Shared"
msgstr "Megosztott"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr "Megosztott dokumentumok"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr "Megosztott címkék"

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr "Megosztások és E-mailek"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Size"
msgstr "Méret"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Split"
msgstr "Felosztás"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#, python-format
msgid "Split PDF"
msgstr "PDF darabolása"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "Állapot"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tevékenységeken alapuló állapot\n"
"Lejárt: A tevékenység határideje lejárt\n"
"Ma: A határidő ma van\n"
"Tervezett: Jövőbeli határidő."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr "Al-munkaterületek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "Összefoglaló"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Címke"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr "Címke kategóriák"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr "Címke kategória"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Címke neve"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Tags"
msgstr "Címkék"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr "Sablonok"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr "Szöveg"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"A modell (Odoo dokumentum típus), amelyre ez az álnév vonatkozik. Bármely "
"bejövő e-mail, amely nem válaszol egy meglévő rekordra, ezen modell egy új "
"rekordjának létrehozását fogja előidézni (pl. egy projekt feladatot)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Az e-mail álnév neve, pl. 'állások', ha erre az e-mailcímre szeretne "
"leveleket kapni: <<EMAIL>>"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Az e-mailek ezen álnévre történő beérkezésekor létrehozásra kerülő rekordok "
"tulajdonosa. Ha ez a mező nem kerül beállításra, akkor a rendszer "
"megpróbálja megtalálni a megfelelő tulajdonost a küldő (Feladó) címe "
"alapján, vagy az adminisztrátor fiókot használja, ha nem található "
"rendszerfelhasználó ahhoz a címhez."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr "Ez a hozzáférési token már létezik"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"This document has been requested.\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This link has expired"
msgstr "Ez a hivatkozás lejárt"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "Hüvelykujj"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr "Jóváhagyásra vár"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "Jóváhagyásra vár"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr "Helyi súgó"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
msgid "Type"
msgstr "Típus"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kivétel tevékenység típusa a rekordon."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "URL"
msgstr "Webcím"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Un-archive"
msgstr "Aktiválás"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "Feloldás"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Unnamed"
msgstr "Névtelen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread
msgid "Unread Messages"
msgstr "Olvasatlan üzenetek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Olvasatlan üzenetek száma"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
#, python-format
msgid "Upload"
msgstr "Feltöltés"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"font-weight-normal\">a file or</span> drag <span "
"class=\"font-weight-normal\">it here.</span>"
msgstr ""
"Töltsön fel <span class=\"font-weight-normal\">egy fájlt, vagy</span> húzza "
"<span class=\"font-weight-normal\">ide.</span>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Upload Document"
msgstr "Dokumentum feltöltése"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr "Feltöltés e-mailen keresztül"

#. module: documents
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Upload file request"
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Felhasználó"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr "ÁFA"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr "Érvényesség vége"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr "Jóváhagyás"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr "Jóváhagyott"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr "Videók"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr "Honlap üzenetek"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr "Weboldali kommunikációs előzmények"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "hét"

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr "Munkafolyamat műveletek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr "Munkafolyamat szabály"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr "Munkaterület"

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
msgid "Workspaces"
msgstr "Munkaterületek"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr "Írás hozzásférés"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr "Írás csoportok"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr "Írjon ide egy tippet a művelet számára!"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Feltölthet egy fájlt a számítógépéről, vagy másolhat/beilleszthet egy, a "
"fájlra mutató internetes hivatkozást."

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "You cannot create recursive folders."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Youtube Video"
msgstr "Youtube videó"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragEnter(section.id, valueId)"
msgstr "_onDragEnter(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragLeave"
msgstr "_onDragLeave"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDrop(section.id, valueId)"
msgstr "_onDrop(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "delete"
msgstr "törlés"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "document"
msgstr "dokumentum"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents"
msgstr "dokumentumok"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents selected"
msgstr "kiválasztott dokumentumok"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents shared by"
msgstr "dokumentumokat megosztotta"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.Category"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.FiltersGroup"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "download"
msgstr "letöltés"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr "pl.: javaslat megbeszélése"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr "pl.: Pénzügy"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "pl.: Hiányzó kiadás"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr "pl.: Állapot"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "pl.: Érvényesítendő"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr "pl.: Dokumentum érvényesítése"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "pl.: https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "restore"
msgstr "helyreállít"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#, python-format
msgid "select"
msgstr "kiválaszt"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "selected"
msgstr "kiválasztva"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "shared by"
msgstr "megosztotta"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "állapotkód: %s, üzenet: %s"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "toggle favorite"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "unnamed"
msgstr "névtelen"
