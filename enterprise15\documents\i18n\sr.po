# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-11-12 14:54+0000\n"
"PO-Revision-Date: 2018-08-24 11:37+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:271
#, python-format
msgid "+ Add a tag "
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:50
#, python-format
msgid ""
"<b>Actions</b> allows you to assign tags, automate actions like creating a "
"task or vendor bill,</br> create activities for users, etc.</br> Actions can"
" be customized through the Configuration menu."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:26
#, python-format
msgid "<b>Close the preview</b> to go back to your selection of documents"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:60
#, python-format
msgid ""
"<b>Use tags</b> to easily filter documents. Tags are defined according to "
"the folder."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-arrow-right\"/> Configure Email Servers"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span style=\"color:white;\">&amp;nbsp;Documents.</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.inherit_mail_attachment_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Access Groups"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
msgid "Action"
msgstr "Радња"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:103
#: model:ir.ui.menu,name:documents.WorkflowRules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Actions"
msgstr "Радње"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activity"
msgstr "Активност"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr ""

#. module: documents
#: selection:documents.workflow.action,action:0
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Dodaj"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_kanban.xml:8
#, python-format
msgid "Add URL"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "All locked files"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr ""

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:159
#, python-format
msgid "Archive"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Archived"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_selector.xml:50
#, python-format
msgid "Attached To"
msgstr "Приложено"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.document_debug_action
#: model:ir.ui.menu,name:documents.attachment_list_view_temp
msgid "Attachments"
msgstr "Прилози"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_view.js:27
#, python-format
msgid "Attachments Kanban"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_ir_attachment__available_rule_ids
msgid "Available Rules"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Cancel"
msgstr "Otkaži"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__facet_id
msgid "Category"
msgstr "Kategorija"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:45
#, python-format
msgid "Click here to <b>close the discussion board</b>"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:17
#, python-format
msgid "Click on a card to <b>view the document</b>."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "Kompanija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "Uslovi"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Konfiguracija"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model:ir.model.fields,field_description:documents.field_ir_attachment__partner_id
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Contact"
msgstr "Kontakt"

#. module: documents
#: selection:documents.workflow.tag.criteria,operator:0
msgid "Contains"
msgstr "Sadrži"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Kreiraj"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Create a new activity"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_tree
msgid "Create a tag"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__create_uid
msgid "Created by"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: documents
#: selection:documents.workflow.rule,create_model:0
msgid "Credit note"
msgstr ""

#. module: documents
#: selection:documents.workflow.rule,condition_type:0
msgid "Criteria"
msgstr ""

#. module: documents
#: selection:documents.request_wizard,activity_date_deadline_range_type:0
#: selection:documents.share,activity_date_deadline_range_type:0
#: selection:documents.workflow.rule,activity_date_deadline_range_type:0
msgid "Days"
msgstr "Dani"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Default Folders"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:161
#, python-format
msgid "Delete"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
msgid "Description"
msgstr "Opis"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__display_name
msgid "Display Name"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Doc"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model_terms:ir.ui.view,arch_db:documents.documents_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Document"
msgstr "Dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_tag_criteria
msgid "Document Workflow Tag Criteria"
msgstr ""

#. module: documents
#: selection:documents.share,type:0
msgid "Document list"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:64
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__attachment_ids
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
#, python-format
msgid "Documents"
msgstr "Dokumenti"

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Folder"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr ""

#. module: documents
#: model:ir.module.category,name:documents.module_category_documents_management
msgid "Documents management"
msgstr ""

#. module: documents
#: selection:documents.workflow.tag.criteria,operator:0
msgid "Does not contain"
msgstr ""

#. module: documents
#: selection:documents.share,type:0
#: selection:documents.workflow.rule,condition_type:0
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "Domen"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Done"
msgstr "Završeno"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Downlaod all files"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:149
#: selection:documents.share,action:0
#, python-format
msgid "Download"
msgstr ""

#. module: documents
#: selection:documents.share,action:0
msgid "Download and Upload"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_controller.js:718
#, python-format
msgid "Drop files here to upload"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "E.g. Finance"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "E.g. Missing Expense"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form_list
msgid "E.g. Status"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "E.g. Validate document"
msgstr ""

#. module: documents
#: selection:ir.attachment,type:0
msgid "Empty"
msgstr ""

#. module: documents
#: selection:documents.share,state:0
msgid "Expired"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
msgid "Facet"
msgstr ""

#. module: documents
#: sql_constraint:documents.facet:0
msgid "Facet already exists in this folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_ir_attachment__favorited_ids
msgid "Favorite of"
msgstr ""

#. module: documents
#: selection:ir.attachment,type:0
msgid "File"
msgstr "Datoteka"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
#: model:ir.model.fields,field_description:documents.field_ir_attachment__folder_id
msgid "Folder"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_selector.xml:9
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
#, python-format
msgid "Folders"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Future Activities"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:21
#, python-format
msgid ""
"Go to the <b>images, videos & PDF viewer</b> by clicking on the preview "
"area."
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_business_option
msgid "Has Business Option"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Image"
msgstr "Slika"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:40
#, python-format
msgid "It will be sent to the followers of this document"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet____last_update
#: model:ir.model.fields,field_description:documents.field_documents_folder____last_update
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_share____last_update
#: model:ir.model.fields,field_description:documents.field_documents_tag____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria____last_update
msgid "Last Modified on"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__write_uid
msgid "Last Updated by"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__write_date
msgid "Last Updated on"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Late Activities"
msgstr ""

#. module: documents
#: selection:documents.share,state:0
msgid "Live"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Loading"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:154
#, python-format
msgid "Lock"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_ir_attachment__lock_uid
msgid "Locked by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Prijava"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:69
#, python-format
msgid "MB"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.tag_action
#: model:ir.actions.act_window,name:documents.tree_tag_action
msgid "Manage document tags"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.facet_action
msgid "Manage tag categories"
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Manager"
msgstr "Menadžer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all Activities as done"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Model"
msgstr "Model"

#. module: documents
#: selection:documents.request_wizard,activity_date_deadline_range_type:0
#: selection:documents.share,activity_date_deadline_range_type:0
#: selection:documents.workflow.rule,activity_date_deadline_range_type:0
msgid "Months"
msgstr "Meseci"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Folder"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:143
#, python-format
msgid "Multiple values"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "My Activities"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "My Documents"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "My Favorites"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "My locked files"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_form
msgid "NName"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
msgid "Name"
msgstr "Ime"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_model.js:227
#, python-format
msgid "No Source"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "Napomena"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__operator
msgid "Operator"
msgstr "Operator"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model:ir.model.fields,field_description:documents.field_ir_attachment__owner_id
msgid "Owner"
msgstr "Vlasnik"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{attachment.create_uid.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "PDF"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Folder"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "Roditeljski Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: documents
#: selection:documents.workflow.rule,create_model:0
msgid "Product template"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: documents
#: selection:documents.workflow.action,action:0
msgid "Remove"
msgstr "Уклони"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:153
#, python-format
msgid "Replace"
msgstr ""

#. module: documents
#: selection:documents.workflow.action,action:0
msgid "Replace by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request"
msgstr "Zahtev"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_kanban.xml:11
#, python-format
msgid "Request Document"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/systray.xml:7
#, python-format
msgid "Request a Document"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Requested files"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "Odgovoran"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:162
#, python-format
msgid "Restore"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Rule name"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Select the tag categories to be used"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
msgid "Sequence"
msgstr "Niz"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration
#: model:ir.actions.act_window,name:documents.documents_settings_action
#: model:ir.ui.menu,name:documents.Settings_menu
msgid "Settings"
msgstr "Podešavanje"

#. module: documents
#. openerp-web
#: code:addons/documents/models/share.py:132
#: code:addons/documents/static/src/xml/documents_inspector.xml:151
#: code:addons/documents/static/src/xml/documents_kanban.xml:14
#, python-format
msgid "Share"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr ""

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shared Links"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__attachment_ids
msgid "Shared attachments"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: documents
#: selection:documents.workflow.rule,create_model:0
msgid "Signature template"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:68
#, python-format
msgid "Size"
msgstr "Veličina"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "Status"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub folders"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "Pregled"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__tag_id
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
msgid "Tag Categories"
msgstr ""

#. module: documents
#: sql_constraint:documents.tag:0
msgid "Tag already exists for this facet"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "Tag categories from parent folders will be shared to their sub folders"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:93
#: code:addons/documents/static/src/xml/documents_selector.xml:16
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_tag_ids
#: model:ir.model.fields,field_description:documents.field_ir_attachment__tag_ids
#: model:ir.ui.menu,name:documents.Category_menu
#, python-format
msgid "Tags"
msgstr "Oznake"

#. module: documents
#: selection:documents.workflow.rule,create_model:0
msgid "Task"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: documents
#: sql_constraint:documents.share:0
msgid "This access token already exists"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This folder will only be available for the selected company"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "This folder will only be available for the selected user groups"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:30
#, python-format
msgid ""
"This icons gets you to the <b>discussion board</b>, to chat with followers "
"of this document"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This link has expired"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:69
#, python-format
msgid ""
"To delete files, archive them first. </br>Archived documents can still be "
"accessed using the Archive option in the Filters top menu."
msgstr ""

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Today Activities"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
msgid "Tooltip"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:35
#, python-format
msgid "Try to <b>post a message</b>"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_ir_attachment__type
msgid "Type"
msgstr "Tip"

#. module: documents
#: selection:ir.attachment,type:0
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "URL"
msgstr "URL"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:162
#, python-format
msgid "Un-archive"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread
msgid "Unread Messages"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_kanban.xml:5
#: model_terms:ir.ui.view,arch_db:documents.share_page
#, python-format
msgid "Upload"
msgstr ""

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"font-weight-normal\">a file or</span> drag <span class"
"=\"font-weight-normal\">it here.</span>"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:55
#, python-format
msgid ""
"Use <b>folders</b> to organize documents by departments, or group of "
"interests"
msgstr ""

#. module: documents
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Korisnik"

#. module: documents
#: model:ir.model,name:documents.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr ""

#. module: documents
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor bill"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_view_search_inherit
msgid "Video"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:13
#, python-format
msgid "Want to become a <b>paperless company</b>? Let's discover Odoo DMS."
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: documents
#: selection:documents.request_wizard,activity_date_deadline_range_type:0
#: selection:documents.share,activity_date_deadline_range_type:0
#: selection:documents.workflow.rule,activity_date_deadline_range_type:0
msgid "Weeks"
msgstr "Nedelje"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tour.js:64
#, python-format
msgid ""
"When you <b>upload documents</b>, they appear in the selected folder and "
"tags."
msgstr ""

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_tag_criteria__workflow_rule_id
msgid "Workflow Rule"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_ir_attachment__type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:54
#, python-format
msgid "documents selected"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents shared by"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "hover text description"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "shared by"
msgstr ""
