# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <robert.fry<PERSON><PERSON>@linserv.se>, 2021
# 3eec91a23d05c632ffac786ac42b81b8_b6fff7b <8985b7bc57db860af29969457dbb51b3_1018915>, 2021
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-<PERSON>: 2021-09-14 12:39+0000\n"
"Last-Translator: Mikael Åkerberg <<EMAIL>>, 2023\n"
"Language-Team: Swedish (https://www.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents"
msgstr "%s Dokument"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents (%s locked)"
msgstr "%s Dokument (%s låst)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_search_panel.js:0
#, python-format
msgid "%s file(s) not moved because they are locked by another user"
msgstr ""
"%s fil(er) kan inte flyttas eftersom de är låsta av en annan användare"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"
msgstr ""
"&amp;nbsp;\n"
"                           <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "+ Add a tag "
msgstr "+ Lätt till etikett "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr ", upphörde att gälla den"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
". De skannade filerna visas automatiskt i arbetsområdet. Behandla sedan dina"
" dokument i stor skala med det delaverktyget: starta användardefinierade "
"åtgärder, begära en underskrift, konvertera till leverantörsfakturor med AI "
"osv."

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2021"
msgstr "2021"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2022"
msgstr "2022"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">Tips: Bli ett papperslöst företag</b>"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid ""
"<b> File uploaded by: </b> %s <br/>\n"
"                               <b> Link created by: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "
msgstr ""
"<b> Fil uppladdad av: </b> %s <br/>\n"
"                               <b> Länk skapad av: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr "<b>Bytes</b>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Avmarkera den här sidan</b> eftersom vi planerar att behandla alla "
"räkningar först."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr "<b>Gb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr "<b>Kb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr "<b>Mb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>Drivs av"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"
msgstr ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"
msgstr ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"
msgstr "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr "<i class=\"fa fa-download fa-fw\"/> Ladda ner alla"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr "<i class=\"fa fa-download fa-fw\"/> Ladda ner"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-globe\" title=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" title=\"Document url\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr "<i class=\"fa fa-link\"/> Gå till URL"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"
msgstr ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/> Ladda upp"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr "<span class=\"o_stat_text\">Åtgärder</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokument</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">Relaterad <br/> Rekord</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span style=\"color:white;\">&amp;nbsp;Documents.</span>"
msgstr "<span style=\"color:white;\">&amp;nbsp;Dokument.</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"Requested Document\">Begärt dokument</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b> Förfrågan</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>Begärt dokument</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before the link expires (planned on <t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Begäran om dokument: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hej <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                           <br/><br/>\n"
"                                           <t t-out=\"object.create_uid.name or ''\">OdooBot</t><t t-out=\"object.create_uid.email or ''\">(<EMAIL>)</t> ber dig tillhandahålla följande dokument:\n"
"                                           <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Exempel på en anteckning.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Ladda upp det begärda dokumentet\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Vänligen ge oss det saknade dokumentet innan länken löper ut (planerad till <t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">Ditt företag</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                           <t t-if=\"object.date_deadline\"> \n"
"                                Den här länken upphör att gälla <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                           </t>\n"
"                            Drivs av <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"En Python-katalog som används för standardvärden när nya poster skapas för "
"detta alias."

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"En uppsättning villkor och åtgärder som kommer att vara tillgängliga för "
"alla bilagor som matchar villkoren"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr "Ett arbetsområde ärver taggarna från det överordnade arbetsområdet"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "Åtkomst"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr "Rättighetsgrupper"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr "Åtkomsträttigheter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr "Åtkomst Token"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
#, python-format
msgid "Action"
msgstr "Åtgärd"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Action Button Name"
msgstr "Namn på åtgärdsknapp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr "Åtgärdsräkning"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr "Åtgärdsnamn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr "Åtgärd krävs"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Actions"
msgstr "Åtgärder"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
msgid "Active"
msgstr "Aktiv"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr "Aktiviteter"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Activity"
msgstr "Aktivitet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoration för aktivitetsundantag"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr "Aktivitetsmeddelande"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "Aktivitetstillstånd"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "Aktivitetstyp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon för aktivitetstyp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr "Aktivitets typ"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "Lägg till"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add File"
msgstr "Lägg till fil"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "Lägg till URL"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Add a Link"
msgstr "Lägg till en länk"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add new file"
msgstr "Lägg till ny fil"

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "Administratör"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr "Annonser"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr "Alias"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr "Alias kontaktsäkerhet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr "Aliasnamn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr "Aliasdomän"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr "Aliasobjekt"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "Alla filer har laddats upp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr "Gör det möjligt att"

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr "Gör det möjligt för dig att hantera dina dokument."

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Already linked Documents"
msgstr "Redan länkade dokument"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr ""
"Ett enkelt sätt att behandla inkommande e-post är att konfigurera skannern "
"så att den skickar PDF-filer till"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"Ett enkelt sätt att behandla inkommande e-post är att konfigurera skannern "
"så att den skickar PDF-filer till e-postadressen i arbetsområdet. De "
"skannade filerna visas automatiskt i arbetsområdet. Behandla sedan dina "
"dokument i stor skala med det delade verktyget: starta användardefinierade "
"åtgärder, begära en underskrift, konvertera till leverantörsfakturor med AI "
"osv."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Archive"
msgstr "Arkiv"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Archive original file(s)"
msgstr "Arkivera originalfil(er)"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Archived"
msgstr "Arkiverad"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Are you confirm deletion ?"
msgstr "Bekräftar du radering?"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Eftersom den här PDF-filen innehåller flera dokument kan vi dela upp den och"
" behandla den i stor skala."

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr "Be om validering"

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr "Tillgångar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr "Bifogad vid"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "Bilaga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr "Antal Bilagor"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "Beskrivning för bilaga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "Namn på bilaga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "Bilaga Typ"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_view.js:0
#, python-format
msgid "Attachments Kanban"
msgstr "Bilagor Kanban"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid ""
"Automate your inbox using scanned documents or emails sent to <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."
msgstr ""
"Automatisera din inkorg med hjälp av skannade dokument eller "
"e-postmeddelanden som skickas till <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr "Tillgängliga regler"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr "Faktura"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr "Varumärke 1"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr "Varumärke 2"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr "Broschyrer"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr ""
"Genom att definiera en mapp kommer uppladdningsaktiviteterna att generera "
"ett dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr "Kan ladda upp"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Cancel"
msgstr "Avbryt"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr "Kategorisera, dela och håll reda på alla dina interna dokument."

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr "Kategori"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "Kontrollsumma/SHA1"

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Choose a record to link"
msgstr "Välj en post att länka"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "Klicka på ett kort för att <b>välja dokumentet</b>."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Klicka på en miniatyrbild för att <b>förhandsgranska dokumentet</b>."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Klicka på <b>sidseparatorn</b>: vi vill inte dela upp de här två sidorna "
"eftersom de hör till samma dokument."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "Företag"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr "Typ av tillstånd"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "Villkor"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "Konfiguration"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Configure Email Servers"
msgstr "Konfigurera e-postservrar"

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "Kontakt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr "Innehåller"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr "Avtal"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Skapa"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Bill"
msgstr "Skapa faktura"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr "Skapa delning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr "Skapa en ny aktivitet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "Skapad av"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "Registeringsdatum"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr "Kriterier"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Anpassat studs-meddelande"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dagar"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Standardanvändare"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr "Standardvärden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr "Standardvärden för uppladdade dokument"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
#, python-format
msgid "Delete"
msgstr "Ta bort"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr "Ogilla"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr "Avslutad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr "Beskrivning"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Discard"
msgstr "Avbryt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list
msgid "Document"
msgstr "Dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "Antal dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "Dokumentets namn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr "Dokumentets ägare"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "Begäran om dokument"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr ""
"Begäran om dokument {{ object.name !.= False och ': '+ object.name eller '' "
"}}"

#. module: documents
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Document Request: %s Uploaded by: %s"
msgstr "Dokumentförfrågan: %s Uppladdad av: %s"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document Request: Send by email"
msgstr "Begäran om dokument: Skicka via e-post"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document Thumbnail"
msgstr "Miniatyrbild av ett dokument"

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr "Dokumentarbetsflöde Tagg åtgärd"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr "Dokumentlista"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.module.category,name:documents.module_category_documents_management
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
#, python-format
msgid "Documents"
msgstr "Dokument"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "Dokument Länk till posten"

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr "Dokument Dela"

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr "Arbetsområde för dokument"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "Mixin för skapande av dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr "Innehåller inte"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "Domän"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Done"
msgstr "Klar"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Downlaod all files"
msgstr "Nedladdning av alla filer"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__download
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Download"
msgstr "Hämta"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Download all files"
msgstr "Ladda ner alla filer"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__downloadupload
msgid "Download and Upload"
msgstr "Ladda ner och ladda upp"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Drop files here to upload"
msgstr "Släpp filer här för att ladda upp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr "Förfallodatum"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr "Typ av förfallodag"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Edit the linked Record"
msgstr "Redigera den länkade posten"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "E-post cc"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#: code:addons/documents/static/src/js/documents_document_viewer.js:0
#, python-format
msgid "Error"
msgstr "Fel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr "Uteslutna taggar"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr "Utlägg"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr "Förfallen"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr "Facet finns redan i den här mappen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "Favorit av"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "Fil"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "Filinnehåll (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "Filstorlek"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "Centralisering av filer"

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr "Finans"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr "Finansiella tillgångar"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr "Skattemässig hemvist"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr "Räkenskapsår"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr "Mapp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr "Följare"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr "Följare (kontakter)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeikon t.ex fa-tasks"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Generate URL"
msgstr "Generera URL"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr "Gruppera efter"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid ""
"Groups able to see the workspace and read its documents without create/edit "
"rights."
msgstr ""
"Grupper som kan se arbetsområdet och läsa dess dokument utan att ha rätt att"
" skapa/redigera."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr ""
"Grupper som kan se arbetsområdet och läsa/skapa/redigera dess dokument."

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr "HR"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr "Har Meddelande"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
#, python-format
msgid "History"
msgstr "Historik"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID på överliggande post med aliaset (exempel: projekt som har alias för "
"skapa uppgift)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon för att indikera en undantagsaktivitet."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_document__message_unread
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Om den är markerad så finns det meddelanden som kräver din uppmärksamhet."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Om markerad så har det blivit ett leveransfel för några meddelanden."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Om angiven kommer detta innehåll automatiskt att skickas ut till obehöriga "
"användare istället för standardmeddelandet."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Image/Video"
msgstr "Bild/Video"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr "Bilder"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "Inkorg"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid ""
"Incoming letters sent to <span class=\"o_folder_description_alias\">inbox "
"email alias</span> will be added to your inbox automatically."
msgstr ""
"Inkommande brev som skickas till <span "
"class=\"o_folder_description_alias\">inbox email alias</span> läggs "
"automatiskt till din inkorg."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "Indexerat innehåll"

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr "Intern"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "Är redigerbar bilaga"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "Favoriseras"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr "Är följare"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr "Kunskap"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document____last_update
#: model:ir.model.fields,field_description:documents.field_documents_facet____last_update
#: model:ir.model.fields,field_description:documents.field_documents_folder____last_update
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_share____last_update
#: model:ir.model.fields,field_description:documents.field_documents_tag____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule____last_update
msgid "Last Modified on"
msgstr "Senast redigerad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr "Juridisk"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Låt oss bearbeta dokument i din inkorg.<br/><i>Tips: Använd taggar för att "
"filtrera dokument och strukturera processen.</i>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process these bills: send to Finance workspace."
msgstr "Låt oss behandla dessa räkningar: skicka till arbetsområdet Ekonomi."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "Låt oss bearbeta det här dokumentet som kommer från vår skanner."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Låt oss märka det här mailet som juridiskt<br/> <i>Tips: Åtgärderna kan "
"anpassas till din process, beroende på arbetsområdet.</i>"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr "Begränsa läsgrupper till de dokument som de är ägare till."

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr "Begränsa skrivgrupper till de dokument som de äger."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "Länk"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr "Länk till posten"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__email_cc
msgid "List of cc from incoming emails."
msgstr "Lista på CC från inkommande e-post."

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr "Live"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Loading"
msgstr "Laddar"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Lock"
msgstr "Lås"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr "Låst"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "Låst av"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr "Registrera en notering..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "Användarnamn"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "Logga ut"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "MB"
msgstr "MB"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_main_attachment_id
#: model:ir.model.fields,field_description:documents.field_documents_share__message_main_attachment_id
msgid "Main Attachment"
msgstr "Huvudbilaga"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr "Markera som utkast"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr "Markera allt som klart"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr "Marknadsföring"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr "Fel vid leverans av meddelande"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "Meddelanden"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "Mime typ"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr "Diverse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "Modell"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "Modeller"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Månader"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr "Flytta till inkorgen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr "Flytta till Arbetsområde"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Multiple values"
msgstr "Flera värden"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Min Aktivitets Deadline"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "Mina dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr "Mina favoriter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "Namn"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr "Namnet på delade länken"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Name or Category contains: %s"
msgstr "Namnet eller kategorin innehåller: %s"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New File"
msgstr "Ny fil"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New Group"
msgstr "Ny prisgrupp"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nästa Aktivitets Kalender Händelse"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nästa slutdatum för aktivitet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "Nästa aktivitetssummering"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "Nästa aktivitetstyp"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "No document has been selected"
msgstr "Inget dokument har valts"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr "Inga delade länkar"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not a file"
msgstr "Inte en fil"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not attached"
msgstr "Inte bifogad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "Anteckning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal åtgärder"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fel"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antal meddelanden som kräver en åtgärd"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal meddelanden med leveransfel"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_unread_counter
msgid "Number of unread messages"
msgstr "Antal olästa meddelanden"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo-webbplats"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr "Begränsning till en post"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Open chatter"
msgstr "Öppet prat"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Or send emails to"
msgstr "Eller skicka e-post till"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr "Annat"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr "Endast egna dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr "Endast egna dokument (Skriv)"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid ""
"Own Documents Only may not be enabled for write groups if it is not enabled "
"for read groups."
msgstr ""
"Endast egna dokument får inte aktiveras för skrivgrupper om det inte är "
"aktiverat för läsgrupper."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "Ägare"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{document.create_uid.name}"
msgstr "Ägare: #{document.create_uid.name}"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "PDF/Document"
msgstr "PDF/Dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "Parent Model"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Överordnad post tråd ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr "Överordnat Arbetsområde"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Överordnad modell innehåller alias. Modellen som innehåller aliasreferensen "
"är inte nödvändigtvis den modell som ges av alias_model_id (exempel: projekt"
" (parent_model) och aktivitet (modell))"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Princip för att publicera ett meddelande på dokumentet med hjälp av mailgateway.\n"
"- alla: alla kan posta\n"
"- partner: endast autentiserade partner\n"
"- följare: endast följare av det relaterade dokumentet eller medlemmar av följande kanaler\n"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr "Presentationer"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr "Projekt"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr "Läsrättigheter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr "Läsgrupper"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "Spela in"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Record Thread ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
msgid "Related Workspace"
msgstr "Relaterat Arbetsområde"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "Remaining Pages"
msgstr "Återstående sidor"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr "Ta bort"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Replace"
msgstr "Ersätt"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr "Ersätt med"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#, python-format
msgid "Request"
msgstr "Ärende"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "Begär aktivitet"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "Ärende"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/systray.xml:0
#, python-format
msgid "Request a Document"
msgstr "Begär ett dokument"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "Begär en fil"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Requested"
msgstr "Begärda"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "Begärt dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr "Obligatoriska taggar"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "Res Modellnamn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "Resurs ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "Resursmodell"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "Resursnamn"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "Ansvarig"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "Ansvarig användare"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "Återställ"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveransfel"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr "Försäljning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr "Planera aktivitet"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Search more..."
msgstr "Sök mer..."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Select All: Shift + A"
msgstr "Välj alla: Shift + A"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Select tags"
msgstr "Välj taggar"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Skicka detta brev till den juridiska avdelningen genom att ange de rätta "
"etiketterna."

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr "Skicka till Juridiska avdelningen"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
msgid "Set As 2022 Contracts"
msgstr "Ange som 2022-kontrakt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr "Ange kontakt"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr "Ange Ägare"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr "Ange Etiketter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr "Ange aktiviteten för dokumentägaren"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "Inställningar"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Share"
msgstr "Utdelning"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr "Dela länkar"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share domain"
msgstr "Dela domän"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "Share link"
msgstr "Dela länk"

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr "Dela länkar"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share selected records"
msgstr "Dela utvalda poster"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Share this domain"
msgstr "Dela denna domän"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Share this selection"
msgstr "Dela detta urval"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr "Dela typ"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr "Delning som använts för att skapa detta dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Shared"
msgstr "Delad"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr "Delade dokument"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr "Delade Etiktter"

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr "Delningar och e-postmeddelanden"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Size"
msgstr "Storlek"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr "Specifik modell som är kopplad"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Split"
msgstr "Dela"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#, python-format
msgid "Split PDF"
msgstr "Dela PDF"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "Status"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baserad på aktiviteter\n"
"Försenade: Leveranstidpunkten har passerat\n"
"Idag: Aktivitetsdatum är idag\n"
"Kommande: Framtida aktiviteter."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr "Underordnade arbetsområden"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "Sammandrag"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "Etikett"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr "Etikettkategorier"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr "Etikettkategori"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "Etikettnamn"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr "Det finns redan en etikett för denna facet"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr "Etikettkategorier som definierats för det här arbetsområdet"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Tags"
msgstr "Etiketter"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr "Mallar"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr "Text"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr ""
"Text som visas när du håller muspekaren på denna etikettkategori eller dess "
"etiketter"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Klassen (Odoo-dokumenttyp) som detta alias är knutet till. All inkommande "
"e-post som inte ingår i en pågående diskussion skapar en ny post av denna "
"klass (t ex Aktivitet)"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Namnet på e-post-alias, eg 'jobb' om du vill fånga e-post för "
"<<EMAIL>>"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr "Den här åtkomsttoken finns redan"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "Denna bilaga är redan ett dokument"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr ""
"Den här bilagan kommer endast att vara tillgänglig för de valda "
"användargrupperna"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"This document has been requested.\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""
"Detta dokument har begärts.\n"
"               <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Ladda upp det</b>."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This link has expired"
msgstr "Den här länken har gått ut"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr ""
"Detta arbetsområde kommer endast att vara tillgänglig för det valda "
"företaget"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "Miniatyr"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Tip: configure your scanner to send all documents to this address."
msgstr ""
"Tips: Konfigurera skannern så att alla dokument skickas till den här "
"adressen."

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "Tips: Bli ett papperslöst företag"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr "Att granska"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "Att granska"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr "Tips"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "Sant om vi kan redigera länkbilagan."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
msgid "Type"
msgstr "Typ"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ av undantagsaktivitet i posten."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "URL"
msgstr "URL"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Un-archive"
msgstr "Återkalla arkivering"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "Lås upp"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Unnamed"
msgstr "Namnlös"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread
msgid "Unread Messages"
msgstr "Olästa meddelanden"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Räknare olästa meddelanden"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
#, python-format
msgid "Upload"
msgstr "Ladda upp"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"font-weight-normal\">a file or</span> drag <span "
"class=\"font-weight-normal\">it here.</span>"
msgstr ""
"Ladda upp <span class=\"font-weight-normal\">en fil eller</span> dra <span "
"class=\"font-weight-normal\">den hit.</span>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Upload Document"
msgstr "Ladda upp dokment"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr "Ladda upp via e-post"

#. module: documents
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Upload file request"
msgstr "Begäran om uppladdning av fil"

#. module: documents
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "Användare"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr "Skatteregistreringsnummer"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr "Giltigt till"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr "Bekräfta"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr "Bekräftat"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr "Videor"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Vill ni bli ett <b>papperslöst företag</b>? Låt oss upptäcka Odoo Documents."

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr "Webbplatsmeddelanden"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr "Webbplatsens kommunikationshistorik"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Veckor"

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr "Åtgärder i arbetsflödet"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr "Arbetsflödesregel"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr "Arbetsområde"

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
msgid "Workspaces"
msgstr "Arbetsområden"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Wow... 6 dokument behandlade på några sekunder, du är nöjd.<br/>Rundturen är"
" avslutad. Prova att ladda upp dina egna dokument nu."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr "Skrivrättigheter"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr "Skrivgrupper"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr "Skriv ett verktygstips för åtgärden här"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr ""
"Du kan antingen ladda upp en fil från din dator eller ange en URL till din "
"fil."

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "You cannot create recursive folders."
msgstr "Du kan inte skapa rekursiva mappar."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Youtube Video"
msgstr "Youtube Video"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragEnter(section.id, valueId)"
msgstr "_onDragEnter(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragLeave"
msgstr "_onDragLeave"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDrop(section.id, valueId)"
msgstr "_onDrop(section.id, valueId)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "delete"
msgstr "tabort"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "document"
msgstr "dokument"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents"
msgstr "dokument"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents selected"
msgstr "utvalda dokument"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents shared by"
msgstr "dokument som delas av"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.Category"
msgstr "documents.SearchPanel.Category"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.FiltersGroup"
msgstr "documents.SearchPanel.FiltersGroup"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "download"
msgstr "nedladdning"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr "e.g Diskutera offert"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr "t.ex. Ekonomi"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "t.ex. Saknad Utgift"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr "t.ex. Status"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "t.ex. För att validera"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr "t.ex. Validera dokumentet"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "t.ex. https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "restore"
msgstr "återställ"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#, python-format
msgid "select"
msgstr "välj"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "selected"
msgstr "valda"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "shared by"
msgstr "delad av"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "statuskod: %s, meddelande: %s"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "toggle favorite"
msgstr "växla favorit"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "unnamed"
msgstr "namnlös"
