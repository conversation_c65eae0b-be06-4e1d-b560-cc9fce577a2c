# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents
# 
# Translators:
# <PERSON>, 2021
# 李振維, 2021
# <PERSON><PERSON> huang <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents"
msgstr "%s 個文件"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "%s Documents (%s locked)"
msgstr "%s 個文件（%s 已鎖定）"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_search_panel.js:0
#, python-format
msgid "%s file(s) not moved because they are locked by another user"
msgstr "%s 文件未被移動，因為它們被另一用戶鎖定"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid ""
"&amp;nbsp;\n"
"                            <i class=\"fa fa-lock oe_inline\" title=\"Locked\" attrs=\"{'invisible': [('lock_uid', '=', False)]}\"/>"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "+ Add a tag "
msgstr "添加新標籤"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid ", expired on"
msgstr "，已過期"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
". Scanned files will appear automatically in your workspace. Then, process "
"your documents in bulk with the split tool: launch user defined actions, "
"request a signature, convert to vendor bills with AI, etc."
msgstr ""
"處理收到的郵件的一個簡單方法是配置掃描儀以將PDF發送到您的工作區電子郵件。掃描的文檔將自動出現在您的工作區中。然後，使用分拆工具批量處理文檔：啟動用戶定義的動作、請求籤名、使用AI轉換為供應商賬單等。"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2017
msgid "2021"
msgstr ""

#. module: documents
#: model:documents.tag,name:documents.documents_finance_fiscal_year_2018
msgid "2022"
msgstr "2022"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid "<b class=\"tip_title\">Tip: Become a paperless company</b>"
msgstr "<b class=\"tip_title\">提示：成為一間無紙化公司</b>"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid ""
"<b> File uploaded by: </b> %s <br/>\n"
"                               <b> Link created by: </b> %s <br/>\n"
"                               <a class=\"btn btn-primary\" href=\"/web#id=%s&model=documents.share&view_type=form\" target=\"_blank\">\n"
"                                  <b>%s</b>\n"
"                               </a>\n"
"                             "
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Bytes</b>"
msgstr "<b>字節</b>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr "<b>取消選擇此頁面</b>，因為我們計劃先處理所有賬單。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Gb</b>"
msgstr "<b>千兆字節</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Kb</b>"
msgstr "<b>千字節</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.format_file_size
msgid "<b>Mb</b>"
msgstr "<b>Mb</b>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<br/>Powered by"
msgstr "<br/>官方技術支援"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa fa-folder o_documents_folder_color mr-2\" "
"title=\"Workspace\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"<i class=\"fa fa-circle-o-notch fa-spin text-white fa-3x mb8\" role=\"img\" "
"aria-label=\"Loading\" title=\"Loading\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-circle-thin o_record_selector\" title=\"Select document\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-download fa-fw\"/>  Download All"
msgstr "全部下載"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-download fa-fw\"/> Download"
msgstr "<i class=\"fa fa-download fa-fw\"/>下載"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<i class=\"fa fa-globe\" title=\"Document url\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "<i class=\"fa fa-link\"/> Go to URL"
msgstr "<i class=\"fa fa-link\"/>轉到URL"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid ""
"<i class=\"fa fa-tag o_documents_tag_color ml-2\" attrs=\"{'invisible': "
"[('tag_ids', '=', [])]}\" title=\"Tags\"/>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<i class=\"fa fa-upload\"/>  Upload"
msgstr "<i class=\"fa fa-upload\"/>上傳"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Actions</span>"
msgstr "<span class=\"o_stat_text\">動作</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_document_res_partner_view
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">文件</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "<span class=\"o_stat_text\">Related <br/> Record</span>"
msgstr "<span class=\"o_stat_text\">相關<br/>記錄</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "<span style=\"color:white;\">&amp;nbsp;Documents.</span>"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "<span title=\"Requested Document\">Requested Document</span>"
msgstr "<span title=\"要求的文件\">要求的文件</span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b> Request</b></span>"
msgstr "<span><b>請求</b></span>"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "<span><b>Requested Document</b></span>"
msgstr "<span><b>請求的文件</b></span>"

#. module: documents
#: model:mail.template,body_html:documents.mail_template_document_request
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"                    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"                    <tbody>\n"
"                        <!-- HEADER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\">\n"
"                                        <span style=\"font-size: 10px;\">\n"
"                                            Document Request: <br/>\n"
"                                            <t t-if=\"object.name\">\n"
"                                                <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Inbox Financial</span>\n"
"                                            </t>\n"
"                                        </span><br/>\n"
"                                    </td><td valign=\"middle\" align=\"right\">\n"
"                                        <img t-attf-src=\"/logo.png?company={{ object.create_uid.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.create_uid.company_id.name\"/>\n"
"                                    </td></tr>\n"
"                                    <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- CONTENT -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                                        <div>\n"
"                                            Hello <t t-out=\"object.owner_id.name or ''\">OdooBot</t>,\n"
"                                            <br/><br/>\n"
"                                            <t t-out=\"object.create_uid.name or ''\">OdooBot</t> (<t t-out=\"object.create_uid.email or ''\"><EMAIL></t>) asks you to provide the following document:\n"
"                                            <br/><br/>\n"
"                                            <center>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.name\">\n"
"                                                        <b t-out=\"object.name or ''\">Inbox Financial</b>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <div>\n"
"                                                    <t t-if=\"object.activity_note\">\n"
"                                                        <i t-out=\"object.activity_note or ''\">Example of a note.</i>\n"
"                                                    </t>\n"
"                                                </div>\n"
"                                                <br/>\n"
"                                                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                                                    <a t-att-href=\"object.full_url\" style=\"background-color: #875A7B; padding: 20px 30px 20px 30px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                                        Upload the requested document\n"
"                                                    </a>\n"
"                                                </div>\n"
"                                            </center><br/>\n"
"                                            Please provide us with the missing document before the link expires (planned on <t t-out=\"object.date_deadline or ''\">2021-05-17</t>).\n"
"                                            <t t-if=\"user and user.signature\">\n"
"                                                <br/>\n"
"                                                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"                                                <br/>\n"
"                                            </t>\n"
"                                        </div>\n"
"                                    </td></tr>\n"
"                                    <tr><td style=\"text-align:center;\">\n"
"                                      <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <!-- FOOTER -->\n"
"                        <tr>\n"
"                            <td align=\"center\" style=\"min-width: 590px;\">\n"
"                                <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                                    <tr><td valign=\"middle\" align=\"left\">\n"
"                                        <t t-out=\"object.create_uid.company_id.name or ''\">YourCompany</t>\n"
"                                    </td></tr>\n"
"                                    <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                                        <t t-out=\"object.create_uid.company_id.phone or ''\">******-123-4567</t>\n"
"                                        <t t-if=\"object.create_uid.company_id.email\">\n"
"                                            | <a t-attf-href=\"'mailto:%s' % {{ object.create_uid.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.email or ''\"><EMAIL></a>\n"
"                                        </t>\n"
"                                        <t t-if=\"object.create_uid.company_id.website\">\n"
"                                            | <a t-attf-href=\"'%s' % {{ object.create_uid.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.create_uid.company_id.website or ''\">http://www.example.com</a>\n"
"                                        </t>\n"
"                                    </td></tr>\n"
"                                </table>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </tbody>\n"
"                    </table>\n"
"                    </td></tr>\n"
"                    <!-- POWERED BY -->\n"
"                    <tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"                        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"                          <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"                            <t t-if=\"object.date_deadline\">\n"
"                                This link expires on <b t-out=\"object.date_deadline or ''\">2021-05-17</b>.<br/>\n"
"                            </t>\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com/app/documents\" style=\"color: #875A7B;\">Odoo Documents</a>\n"
"                          </td></tr>\n"
"                        </table>\n"
"                    </td></tr>\n"
"                </table>\n"
"            "
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "當建立別名的新記錄時，一個Python字典被提供作為預設值"

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr "一組條件和操作，可用於與條件匹配的所有附件"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__parent_folder_id
msgid "A workspace will inherit the tags of its parent workspace"
msgstr "工作區將繼承其母工作區的標籤"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Access"
msgstr "存取"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__group_ids
msgid "Access Groups"
msgstr "存取組"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Access Rights"
msgstr "存取權"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__access_token
msgid "Access Token"
msgstr "存取代碼(token)"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__action
#, python-format
msgid "Action"
msgstr "動作"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__name
msgid "Action Button Name"
msgstr "動作按鈕名稱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__action_count
msgid "Action Count"
msgstr "動作數目"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Action Name"
msgstr "動作名稱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.ui.menu,name:documents.workflow_rules_menu
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Actions"
msgstr "動作"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__active
msgid "Active"
msgstr "啟用"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_ids
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Activities"
msgstr "活動"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Activity"
msgstr "活動"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_note
msgid "Activity Note"
msgstr "活動說明"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: documents
#: model:ir.model,name:documents.model_mail_activity_type
msgid "Activity Type"
msgstr "活動類型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_type_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_type_id
msgid "Activity type"
msgstr "活動類型"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__add
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "Add"
msgstr "增加"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add File"
msgstr "加入文件"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_url_form
msgid "Add Url"
msgstr "加入網址"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Add a Link"
msgstr "加入連結"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Add new file"
msgstr "加入新文件"

#. module: documents
#: model:res.groups,name:documents.group_documents_manager
msgid "Administrator"
msgstr "管理員"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_ads
msgid "Ads"
msgstr "廣告"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_id
msgid "Alias"
msgstr "別名"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_contact
msgid "Alias Contact Security"
msgstr "別名聯絡人安全"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_name
msgid "Alias Name"
msgstr "別名"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_domain
msgid "Alias domain"
msgstr "別名網域"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_model_id
msgid "Aliased Model"
msgstr "別名的模型"

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "All files uploaded"
msgstr "所有檔案已上傳"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__action
msgid "Allows to"
msgstr "允許"

#. module: documents
#: model:ir.module.category,description:documents.module_category_documents_management
msgid "Allows you to manage your documents."
msgstr "允許你管理文件。"

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Already linked Documents"
msgstr "已連結文件"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to"
msgstr "處理傳入郵件的一種簡單方法是將掃瞄器配置為將 PDF 發送到"

#. module: documents
#: model_terms:digest.tip,tip_description:documents.digest_tip_documents_0
msgid ""
"An easy way to process incoming mails is to configure your scanner to send "
"PDFs to your workspace email. Scanned files will appear automatically in "
"your workspace. Then, process your documents in bulk with the split tool: "
"launch user defined actions, request a signature, convert to vendor bills "
"with AI, etc."
msgstr ""
"處理收到郵件的一個簡單方法，是將掃瞄器配置至將 PDF "
"發送到你的工作區電郵。掃瞄的文件會自動在你的工作區出現。然後，你可使用分拆工具大批處理文件：例如啟動用戶定義的操作、要求簽名、利用 AI "
"轉換為供應商賬單等。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Archive"
msgstr "歸檔"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Archive original file(s)"
msgstr "封存原始文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Archived"
msgstr "已歸檔"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Are you confirm deletion ?"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr "由於此 PDF 包含多個文件檔，所以讓我們分拆並批量處理吧。"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_afv
msgid "Ask for Validation"
msgstr "請求驗證"

#. module: documents
#: model:documents.facet,name:documents.documents_marketing_assets
msgid "Assets"
msgstr "資產"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Attached To"
msgstr "附加於"

#. module: documents
#: model:ir.model,name:documents.model_ir_attachment
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_id
msgid "Attachment"
msgstr "附件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_attachment_count
#: model:ir.model.fields,field_description:documents.field_documents_share__message_attachment_count
msgid "Attachment Count"
msgstr "附件數"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__description
msgid "Attachment Description"
msgstr "附件描述"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_name
msgid "Attachment Name"
msgstr "附件名稱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__attachment_type
msgid "Attachment Type"
msgstr "附件類型"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_kanban_view.js:0
#, python-format
msgid "Attachments Kanban"
msgstr ""

#. module: documents
#: model_terms:documents.folder,description:documents.documents_finance_folder
msgid ""
"Automate your inbox using scanned documents or emails sent to <span "
"class=\"o_folder_description_alias\"><strong>inbox-financial</strong> email "
"alias</span>."
msgstr ""
"使用掃瞄的文件或發送至 <span class=\"o_folder_description_alias\"><strong>inbox-"
"financial</strong></span> 電子郵件別名的電子郵件，使你的收件箱自動化。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__available_rule_ids
msgid "Available Rules"
msgstr "可用規則"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_bill
msgid "Bill"
msgstr "帳單"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand1_folder
msgid "Brand 1"
msgstr "品牌 1"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_brand2_folder
msgid "Brand 2"
msgstr "品牌 2"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_brochures
msgid "Brochures"
msgstr "宣傳冊"

#. module: documents
#: model:ir.model.fields,help:documents.field_mail_activity_type__folder_id
msgid "By defining a folder, the upload activities will generate a document"
msgstr "通過定義資料夾，上載活動將生成一個文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__can_upload
msgid "Can Upload"
msgstr "可以上傳"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Cancel"
msgstr "取消"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid "Categorize, share and keep track of all your internal documents."
msgstr "對所有內部文件進行分類、共享和跟蹤。"

#. module: documents
#: model:ir.model,name:documents.model_documents_facet
#: model:ir.model.fields,field_description:documents.field_documents_tag__facet_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__facet_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Category"
msgstr "類別"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__checksum
msgid "Checksum/SHA1"
msgstr "校驗碼/SHA1"

#. module: documents
#: code:addons/documents/models/workflow.py:0
#, python-format
msgid "Choose a record to link"
msgstr "選擇一個要連結的記錄"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "按下卡片以<b>選擇文件</b>。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "按下縮圖以<b>預覽文件</b>。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr "按下<b>分頁符號</b>：我們不想分拆這兩個頁面，因為它們屬於同一個文件。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Click the cross to <b>exit preview</b>."
msgstr "按一下交叉圖示，以<b>離開預覽</b>。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__company_id
#: model:ir.model.fields,field_description:documents.field_documents_folder__company_id
msgid "Company"
msgstr "公司"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__condition_type
msgid "Condition type"
msgstr "條件類型"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Conditions"
msgstr "條件"

#. module: documents
#: model:ir.ui.menu,name:documents.Config
msgid "Configuration"
msgstr "配置"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Configure Email Servers"
msgstr "配置電郵伺服器"

#. module: documents
#: model:ir.model,name:documents.model_res_partner
#: model:ir.model.fields,field_description:documents.field_documents_document__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__partner_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_partner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Contact"
msgstr "聯絡人"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Contains"
msgstr "包含"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_Contracts
#: model:documents.tag,name:documents.documents_internal_template_contracts
msgid "Contracts"
msgstr "合約"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "建立"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mark
msgid "Create Bill"
msgstr "建立帳單"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_share_id
msgid "Create Share"
msgstr "建立分享"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_option
msgid "Create a new activity"
msgstr "建立新‎‎活動"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_uid
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Created by"
msgstr "創立者"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__create_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__create_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__create_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__create_date
#: model:ir.model.fields,field_description:documents.field_documents_share__create_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__create_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__create_date
msgid "Created on"
msgstr "建立於"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Creation Date"
msgstr "建立日期"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__criteria
msgid "Criteria"
msgstr "準則"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自訂彈跳訊息"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__days
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__days
msgid "Days"
msgstr "天數"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "預設使用者"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_defaults
msgid "Default Values"
msgstr "預設值"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Default values for uploaded documents"
msgstr "上載文件預設值‎"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_view_tree
#, python-format
msgid "Delete"
msgstr "刪除"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_deprecate
msgid "Deprecate"
msgstr "棄用"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_status_deprecated
msgid "Deprecated"
msgstr "棄用"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__description
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Description"
msgstr "說明"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Discard"
msgstr "取消"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__display_name
#: model:ir.model.fields,field_description:documents.field_documents_facet__display_name
#: model:ir.model.fields,field_description:documents.field_documents_folder__display_name
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__display_name
#: model:ir.model.fields,field_description:documents.field_documents_share__display_name
#: model:ir.model.fields,field_description:documents.field_documents_tag__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__display_name
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: documents
#: model:ir.model,name:documents.model_documents_document
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_view_list
msgid "Document"
msgstr "文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_count
#: model:ir.model.fields,field_description:documents.field_res_partner__document_count
msgid "Document Count"
msgstr "文件數"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Document Name"
msgstr "單據名稱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__owner_id
msgid "Document Owner"
msgstr "文件擁有人"

#. module: documents
#: model:ir.model,name:documents.model_documents_request_wizard
msgid "Document Request"
msgstr "‎文件請求‎"

#. module: documents
#: model:mail.template,subject:documents.mail_template_document_request
msgid ""
"Document Request {{ object.name != False and ': '+ object.name or '' }}"
msgstr "‎文件請求‎ {{ object.name != False and ':'+ object.name or '' }}."

#. module: documents
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/document.py:0
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Document Request: %s Uploaded by: %s"
msgstr "文件請求：%s，由 %s 上載"

#. module: documents
#: model:mail.template,name:documents.mail_template_document_request
msgid "Document Request: Send by email"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "Document Thumbnail"
msgstr ""

#. module: documents
#: model:ir.model,name:documents.model_documents_workflow_action
msgid "Document Workflow Tag Action"
msgstr "文件工作流程標籤動作"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__ids
msgid "Document list"
msgstr "文件清單"

#. module: documents
#. openerp-web
#: code:addons/documents/models/folder.py:0
#: code:addons/documents/models/res_partner.py:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:documents.facet,name:documents.documents_finance_documents
#: model:ir.actions.act_window,name:documents.document_action
#: model:ir.model.fields,field_description:documents.field_documents_folder__document_ids
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__document_ids
#: model:ir.model.fields,field_description:documents.field_res_users__document_count
#: model:ir.module.category,name:documents.module_category_documents_management
#: model:ir.ui.menu,name:documents.dashboard
#: model:ir.ui.menu,name:documents.menu_root
#: model_terms:ir.ui.view,arch_db:documents.action_view_search
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
#, python-format
msgid "Documents"
msgstr "文件"

#. module: documents
#: model:ir.model,name:documents.model_documents_link_to_record_wizard
msgid "Documents Link to Record"
msgstr "文件連結至記錄"

#. module: documents
#: model:ir.model,name:documents.model_documents_share
msgid "Documents Share"
msgstr "文件共享‎"

#. module: documents
#: model:ir.model,name:documents.model_documents_folder
msgid "Documents Workspace"
msgstr "文件工作區"

#. module: documents
#: model:ir.model,name:documents.model_documents_mixin
msgid "Documents creation mixin"
msgstr "文件建立混入程式"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Does not contain"
msgstr "不包含"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__domain
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_share__type__domain
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__condition_type__domain
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Domain"
msgstr "Domain"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Done"
msgstr "完成"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Downlaod all files"
msgstr "下載所有文件"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__download
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Download"
msgstr "下載"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Download all files"
msgstr "下載所有文件"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__action__downloadupload
msgid "Download and Upload"
msgstr "下載和上傳"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Drop files here to upload"
msgstr "拖放文件到此處以上載"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range
msgid "Due Date In"
msgstr "截止日期至"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_date_deadline_range_type
msgid "Due type"
msgstr "截止類型"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Edit the linked Record"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__email_cc
msgid "Email cc"
msgstr "副本寄送"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#: code:addons/documents/static/src/js/documents_document_viewer.js:0
#, python-format
msgid "Error"
msgstr "錯誤"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__excluded_tag_ids
msgid "Excluded Tags"
msgstr "不包括的標籤"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_expense
msgid "Expense"
msgstr "費用"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__expired
msgid "Expired"
msgstr "過期"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_facet_name_unique
msgid "Facet already exists in this folder"
msgstr "小平面已在此資料夾中‎"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__favorited_ids
msgid "Favorite of"
msgstr "最愛："

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__binary
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "File"
msgstr "文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__datas
msgid "File Content (base64)"
msgstr "檔案內容 (base64)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__file_size
msgid "File Size"
msgstr "文件大小"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.res_config_settings_view_form
msgid "Files Centralization"
msgstr "檔案集中處理"

#. module: documents
#: model:documents.folder,name:documents.documents_finance_folder
msgid "Finance"
msgstr "財務"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_financial
msgid "Financial"
msgstr "財務"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_fiscal
msgid "Fiscal"
msgstr "財務"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_fiscal_year
msgid "Fiscal years"
msgstr "財政年度"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__folder_id
msgid "Folder"
msgstr "檔案夾"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_follower_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_partner_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Generate URL"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
msgid "Group By"
msgstr "分組依據"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__read_group_ids
msgid ""
"Groups able to see the workspace and read its documents without create/edit "
"rights."
msgstr "組別可以查看工作區並讀取其文檔，而無需創建/編輯權限。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__group_ids
msgid "Groups able to see the workspace and read/create/edit its documents."
msgstr "能夠查看工作區並讀取/創建/編輯其文檔的組。"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_hr
msgid "HR"
msgstr "人力資源"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__has_message
#: model:ir.model.fields,field_description:documents.field_documents_share__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.model.fields,field_description:documents.field_documents_document__previous_attachment_ids
#, python-format
msgid "History"
msgstr "歷史"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__id
#: model:ir.model.fields,field_description:documents.field_documents_facet__id
#: model:ir.model.fields,field_description:documents.field_documents_folder__id
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__id
#: model:ir.model.fields,field_description:documents.field_documents_share__id
#: model:ir.model.fields,field_description:documents.field_documents_tag__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__id
msgid "ID"
msgstr "ID"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "上級記錄ID支援別名(例如:專案支援任務建立別名)"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction
#: model:ir.model.fields,help:documents.field_documents_document__message_unread
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction
#: model:ir.model.fields,help:documents.field_documents_share__message_unread
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error
#: model:ir.model.fields,help:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error
#: model:ir.model.fields,help:documents.field_documents_share__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "設置後，此內容將自動發送給未經授權的用戶，而不是默認消息。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Image/Video"
msgstr "圖片 / 影片"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_images
msgid "Images"
msgstr "圖片"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_inbox
#: model:documents.tag,name:documents.documents_internal_status_inbox
#: model:mail.activity.type,name:documents.mail_documents_activity_data_Inbox
msgid "Inbox"
msgstr "收件箱"

#. module: documents
#: model_terms:documents.folder,description:documents.documents_internal_folder
msgid ""
"Incoming letters sent to <span class=\"o_folder_description_alias\">inbox "
"email alias</span> will be added to your inbox automatically."
msgstr ""
"發送到<span "
"class=\"o_folder_description_alias\">收件箱電子郵件別名的</span>來信將被自動添加到你的收件箱。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__index_content
msgid "Indexed Content"
msgstr "索引的內容"

#. module: documents
#: model:documents.folder,name:documents.documents_internal_folder
msgid "Internal"
msgstr "內部"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_editable_attachment
msgid "Is Editable Attachment"
msgstr "是可編輯附件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_favorited
msgid "Is Favorited"
msgstr "已被加入最愛"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_is_follower
#: model:ir.model.fields,field_description:documents.field_documents_share__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_knowledge
msgid "Knowledge"
msgstr "知識"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document____last_update
#: model:ir.model.fields,field_description:documents.field_documents_facet____last_update
#: model:ir.model.fields,field_description:documents.field_documents_folder____last_update
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard____last_update
#: model:ir.model.fields,field_description:documents.field_documents_share____last_update
#: model:ir.model.fields,field_description:documents.field_documents_tag____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action____last_update
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_share__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_uid
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__write_date
#: model:ir.model.fields,field_description:documents.field_documents_facet__write_date
#: model:ir.model.fields,field_description:documents.field_documents_folder__write_date
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__write_date
#: model:ir.model.fields,field_description:documents.field_documents_share__write_date
#: model:ir.model.fields,field_description:documents.field_documents_tag__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__write_date
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_legal
msgid "Legal"
msgstr "法律"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr "讓我們處理收件箱中的文件。<br/><span>提示：使用標籤以篩選文件並組織流程。</span>"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process these bills: send to Finance workspace."
msgstr "讓我們處理這些賬單：發送到財務工作區。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "讓我們處理這個文件，由我們的掃瞄器開始。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr "讓我們把這封郵件標記為法律法件。<br/><span>提示：根據工作空間，動作可以根據你的流程進行調整。</span>"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific
msgid "Limit Read Groups to the documents of which they are owner."
msgstr "將讀取組限制為其所有者的文件。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__user_specific_write
msgid "Limit Write Groups to the documents of which they are owner."
msgstr "將寫組限制在他們所擁有的文件中。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_link_to_record_form_view
msgid "Link"
msgstr "連結"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__create_model__link_to_record
msgid "Link to record"
msgstr "連結至記錄"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__email_cc
msgid "List of cc from incoming emails."
msgstr "來自傳入電子郵件的 cc 清單。"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_share__state__live
msgid "Live"
msgstr "生效"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Loading"
msgstr "正在上載"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Lock"
msgstr "鎖定"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__is_locked
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Locked"
msgstr "已鎖定"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__lock_uid
msgid "Locked by"
msgstr "鎖定者"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Log a note..."
msgstr "記錄備註..."

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Login"
msgstr "登入"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Logout"
msgstr "登出"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "MB"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_main_attachment_id
#: model:ir.model.fields,field_description:documents.field_documents_share__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_mad
msgid "Mark As Draft"
msgstr "標記為草稿"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__remove_activities
msgid "Mark all as Done"
msgstr "全部標記為完成"

#. module: documents
#: model:documents.folder,name:documents.documents_marketing_folder
msgid "Marketing"
msgstr "市場行銷"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__message_ids
msgid "Messages"
msgstr "訊息"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__mimetype
msgid "Mime Type"
msgstr "MIME 類型"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Miscellaneous"
msgstr "雜項"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__model_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Model"
msgstr "模型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__accessible_model_ids
msgid "Models"
msgstr "模型"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__months
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__months
msgid "Months"
msgstr "月"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_mti
msgid "Move To Inbox"
msgstr "移動至收件箱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__folder_id
msgid "Move to Workspace"
msgstr "移動至工作區"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Multiple values"
msgstr "多重數值"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Documents"
msgstr "我的文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "My Favorites"
msgstr "我的最愛"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__name
#: model:ir.model.fields,field_description:documents.field_documents_facet__name
#: model:ir.model.fields,field_description:documents.field_documents_folder__name
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__name
#: model:ir.model.fields,field_description:documents.field_documents_share__name
#: model:ir.model.fields,field_description:documents.field_documents_tag__name
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Name"
msgstr "名稱"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Name of the share link"
msgstr "分享連結名稱"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Name or Category contains: %s"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New File"
msgstr "新增文件"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "New Group"
msgstr "新增組別"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "No document has been selected"
msgstr "未選取文件"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.share_action
msgid "No shared links"
msgstr "沒有共享連結"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not a file"
msgstr "不是檔案"

#. module: documents
#: code:addons/documents/models/document.py:0
#, python-format
msgid "Not attached"
msgstr "未有附件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__activity_note
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_note
msgid "Note"
msgstr "備註"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_needaction_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要處理的消息數量"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_has_error_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,help:documents.field_documents_share__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息的數量"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Logo"
msgstr "Odoo 徽標"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "Odoo Website"
msgstr "Odoo 網站"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__limited_to_single_record
msgid "One record limit"
msgstr "一個記錄限制"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Open chatter"
msgstr "開啟對話視窗"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "所有的進來的信件都將附上一條追蹤（記錄）選配的ID，即使它們不曾回覆過它。如果設定了，這個將完全阻止新記錄的建立。"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Or send emails to"
msgstr "或發送電郵至"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_other
msgid "Other"
msgstr "其他"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Own Documents Only"
msgstr "只限自己的文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__user_specific_write
msgid "Own Documents Only (Write)"
msgstr "只限自己的文件（寫入）。"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_folder_check_user_specific
msgid ""
"Own Documents Only may not be enabled for write groups if it is not enabled "
"for read groups."
msgstr "如果沒有為讀組啟用，則不能為寫組啟用 \"僅限自己的文檔\"。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__owner_id
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__criteria_owner_id
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "Owner"
msgstr "所有者"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "Owner: #{document.create_uid.name}"
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
msgid "PDF/Document"
msgstr "PDF/文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_model_id
msgid "Parent Model"
msgstr "上級模型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "上級記錄線程ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__parent_folder_id
#: model_terms:ir.ui.view,arch_db:documents.folder_view_search
msgid "Parent Workspace"
msgstr "母工作區"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"載有該別名的母項模型。載有別名參照的模型，不一定是 alias_model_id 提供的模型，例如：專案（parent_model）與任務（model）"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"通過信件網關在文件上提交一個消息政策。\n"
"- 任何人：任何人都可以提交\n"
"- 合作夥伴：只有認證過的合作夥伴\n"
"- 跟隨者：只有相關文件或下列頻道成員的跟隨者\n"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_presentations
msgid "Presentations"
msgstr "展示"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_project
msgid "Project"
msgstr "專案"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Read Access"
msgstr "讀存取"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__read_group_ids
msgid "Read Groups"
msgstr "讀取組別"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__resource_ref
msgid "Record"
msgstr "記錄"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__alias_force_thread_id
msgid "Record Thread ID"
msgstr "記錄追蹤ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__domain_folder_id
msgid "Related Workspace"
msgstr "相關工作區"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "Remaining Pages"
msgstr "剩餘頁數"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__remove
msgid "Remove"
msgstr "移除"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Replace"
msgstr "更換"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_action__action__replace
msgid "Replace by"
msgstr "更換為"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__empty
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
#, python-format
msgid "Request"
msgstr "請求"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__request_activity_id
msgid "Request Activity"
msgstr "請求活動"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "Request To"
msgstr "請求要"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/systray.xml:0
#, python-format
msgid "Request a Document"
msgstr "請求文件"

#. module: documents
#: model:ir.actions.act_window,name:documents.action_request_form
msgid "Request a file"
msgstr "請求文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid "Requested"
msgstr "已請求"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_md
msgid "Requested Document"
msgstr "已請求文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__required_tag_ids
msgid "Required Tags"
msgstr "必需標籤"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model_name
msgid "Res Model Name"
msgstr "資源模型名稱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_id
msgid "Resource ID"
msgstr "資源ID"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_model
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__res_model
msgid "Resource Model"
msgstr "資源模型"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__res_name
msgid "Resource Name"
msgstr "資源名稱"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_user_id
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_user_id
msgid "Responsible"
msgstr "負責人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Restore"
msgstr "還原"

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "Restricted Folder"
msgstr "受限制資料夾"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_has_sms_error
#: model:ir.model.fields,field_description:documents.field_documents_share__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_knowledge_sales
msgid "Sales"
msgstr "銷售"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_option
msgid "Schedule Activity"
msgstr "安排活動"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Search more..."
msgstr "搜尋更多..."

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#, python-format
msgid "Select All: Shift + A"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_inspector.js:0
#, python-format
msgid "Select tags"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "通過分配正確的標籤，把這封信寄到法律部門。"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_internal_legal
msgid "Send to Legal"
msgstr "發送至法律部"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__sequence
#: model:ir.model.fields,field_description:documents.field_documents_folder__sequence
#: model:ir.model.fields,field_description:documents.field_documents_tag__sequence
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__sequence
msgid "Sequence"
msgstr "序號"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_2018contracts
msgid "Set As 2022 Contracts"
msgstr ""

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__partner_id
msgid "Set Contact"
msgstr "設置聯絡人"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__user_id
msgid "Set Owner"
msgstr "設定擁有者"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__tag_action_ids
msgid "Set Tags"
msgstr "設定標籤"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__has_owner_activity
msgid "Set the activity on the document owner"
msgstr "在文件擁有人上設置活動"

#. module: documents
#: model:ir.actions.act_window,name:documents.configuration_action
#: model:ir.actions.act_window,name:documents.settings_action
#: model:ir.ui.menu,name:documents.settings_menu
msgid "Settings"
msgstr "設定"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
#, python-format
msgid "Share"
msgstr "分享"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__share_link_ids
msgid "Share Links"
msgstr "分享連結"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share domain"
msgstr ""

#. module: documents
#: code:addons/documents/controllers/main.py:0
#, python-format
msgid "Share link"
msgstr "分享連結"

#. module: documents
#: model:ir.actions.act_window,name:documents.share_action
msgid "Share links"
msgstr "分享連結"

#. module: documents
#: code:addons/documents/models/share.py:0
#, python-format
msgid "Share selected records"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Share this domain"
msgstr "分享此域"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Share this selection"
msgstr "共享此選擇"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__type
msgid "Share type"
msgstr "分享類型"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__create_share_id
msgid "Share used to create this document"
msgstr "用於建立此文件的共享"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "Shared"
msgstr "分享"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__document_ids
msgid "Shared Documents"
msgstr "共享文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__tag_ids
msgid "Shared Tags"
msgstr "已分享標籤"

#. module: documents
#: model:ir.ui.menu,name:documents.share_menu
msgid "Shares & Emails"
msgstr "共享和電子郵件"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Size"
msgstr "尺寸"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__link_model
msgid "Specific Model Linked"
msgstr "特定模型已連結"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Split"
msgstr "拆分"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#: code:addons/documents/static/src/xml/documents_thread.xml:0
#, python-format
msgid "Split PDF"
msgstr "分拆 PDF"

#. module: documents
#: model:documents.facet,name:documents.documents_finance_status
#: model:documents.facet,name:documents.documents_internal_status
#: model:ir.model.fields,field_description:documents.field_documents_share__state
msgid "Status"
msgstr "狀態"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__children_folder_ids
msgid "Sub workspaces"
msgstr "子工作區"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__activity_summary
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__activity_summary
msgid "Summary"
msgstr "摘要"

#. module: documents
#: model:ir.model,name:documents.model_documents_tag
#: model:ir.model.fields,field_description:documents.field_documents_facet__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__tag_id
#: model:ir.model.fields,field_description:documents.field_mail_activity_type__tag_ids
#: model_terms:ir.ui.view,arch_db:documents.tag_view_search
msgid "Tag"
msgstr "標籤"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__facet_ids
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Tag Categories"
msgstr "標籤分類"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tag Category"
msgstr "標籤類別"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "Tag Name"
msgstr "標籤名稱"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_tag_facet_name_unique
msgid "Tag already exists for this facet"
msgstr "此方面標籤已存在"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_folder__facet_ids
msgid "Tag categories defined for this workspace"
msgstr "為此工作區定義的標籤類別"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: model:ir.actions.act_window,name:documents.facet_action
#: model:ir.model.fields,field_description:documents.field_documents_document__tag_ids
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__tag_ids
#: model:ir.ui.menu,name:documents.category_menu
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
#: model_terms:ir.ui.view,arch_db:documents.facet_view_tree
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
#, python-format
msgid "Tags"
msgstr "標籤"

#. module: documents
#: model:documents.facet,name:documents.documents_internal_template
msgid "Templates"
msgstr "模板"

#. module: documents
#: model:documents.tag,name:documents.documents_internal_template_text
msgid "Text"
msgstr "文字"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_facet__tooltip
msgid "Text shown when hovering on this tag category or its tags"
msgstr "滑鼠經過停留在此標籤類別或其標籤上時，顯示的文本"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"相應於這個別名對應的模型(Odoo單據種類)。任何一封不屬於對某個已存在的記錄的到來信件，將導致此模組中新記錄的建立(例如，一個新的專案任務)。"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "信件別名的名稱，例如，如果您要收取 <<EMAIL>> 的電子信件,別名就要被設為：\"jobs\""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_share__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"根據此別名接收到的信件，建立記錄的所有人。若此欄位沒有設定，系統將試圖根據發送者(From)地址來查找正確的所有者，如果對於那個地址沒有系統使用者被發現，將使用系統管理員帳戶。"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_share_share_unique
msgid "This access token already exists"
msgstr "此存取代碼已存在"

#. module: documents
#: model:ir.model.constraint,message:documents.constraint_documents_document_attachment_unique
msgid "This attachment is already a document"
msgstr "此附件已是文件"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__group_ids
msgid "This attachment will only be available for the selected user groups"
msgstr "此附件只限指定用戶組別可用"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_single
msgid ""
"This document has been requested.\n"
"                <b onclick=\"$('.o_request_upload').trigger('click')\" style=\"cursor:pointer;\">Upload it</b>."
msgstr ""

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.not_available
msgid "This link has expired"
msgstr ""

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__company_id
#: model:ir.model.fields,help:documents.field_documents_folder__company_id
msgid "This workspace will only be available to the selected company"
msgstr "此工作區只限已選公司可用"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__thumbnail
msgid "Thumbnail"
msgstr "縮圖"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#, python-format
msgid "Tip: configure your scanner to send all documents to this address."
msgstr "提示：將你的掃瞄器配置為將所有文件發送到這個地址。"

#. module: documents
#: model:digest.tip,name:documents.digest_tip_documents_0
msgid "Tip: Become a paperless company"
msgstr "小提示：成為無紙化公司"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_tc
#: model:documents.tag,name:documents.documents_internal_status_tc
msgid "To Validate"
msgstr "待驗證"

#. module: documents
#: model:mail.activity.type,name:documents.mail_documents_activity_data_tv
msgid "To validate"
msgstr "待驗證"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_facet__tooltip
#: model:ir.model.fields,field_description:documents.field_documents_workflow_rule__note
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "Tooltip"
msgstr "工具提示"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__is_editable_attachment
msgid "True if we can edit the link attachment."
msgstr "如果我們可以編輯連結的附件，則為 true（真）。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__type
msgid "Type"
msgstr "類型"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__url
#: model:ir.model.fields,field_description:documents.field_documents_share__full_url
#: model:ir.model.fields.selection,name:documents.selection__documents_document__type__url
#: model_terms:ir.ui.view,arch_db:documents.document_view_search
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
#: model_terms:ir.ui.view,arch_db:documents.share_page
#: model_terms:ir.ui.view,arch_db:documents.share_single
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
msgid "URL"
msgstr "網址"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Un-archive"
msgstr "取消歸檔"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_form
msgid "Unlock"
msgstr "解鎖"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "Unnamed"
msgstr "未命名的"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread
msgid "Unread Messages"
msgstr "未讀消息"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__message_unread_counter
#: model:ir.model.fields,field_description:documents.field_documents_share__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀消息計數器"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_views.xml:0
#: model_terms:ir.ui.view,arch_db:documents.share_page
#, python-format
msgid "Upload"
msgstr "上傳"

#. module: documents
#: model_terms:ir.actions.act_window,help:documents.document_action
msgid ""
"Upload <span class=\"font-weight-normal\">a file or</span> drag <span "
"class=\"font-weight-normal\">it here.</span>"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Upload Document"
msgstr "上傳文件"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__email_drop
msgid "Upload by Email"
msgstr "通過郵件上傳"

#. module: documents
#: code:addons/documents/models/mail_activity.py:0
#, python-format
msgid "Upload file request"
msgstr "上載文件請求"

#. module: documents
#: model:res.groups,name:documents.group_documents_user
msgid "User"
msgstr "使用者"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_documents_vat
msgid "VAT"
msgstr "增值稅"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_share__date_deadline
msgid "Valid Until"
msgstr "有效期至"

#. module: documents
#: model:documents.workflow.rule,name:documents.documents_rule_finance_validate
msgid "Validate"
msgstr "驗證"

#. module: documents
#: model:documents.tag,name:documents.documents_finance_status_validated
#: model:documents.tag,name:documents.documents_internal_status_validated
msgid "Validated"
msgstr "已驗證"

#. module: documents
#: model:documents.tag,name:documents.documents_marketing_assets_Videos
msgid "Videos"
msgstr "影片"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr "想成為一家<b>無紙化公司</b>嗎？我們來看看Odoo文檔。"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,field_description:documents.field_documents_share__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__website_message_ids
#: model:ir.model.fields,help:documents.field_documents_share__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: documents
#: model:ir.model.fields.selection,name:documents.selection__documents_request_wizard__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_share__activity_date_deadline_range_type__weeks
#: model:ir.model.fields.selection,name:documents.selection__documents_workflow_rule__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "周"

#. module: documents
#: model:ir.actions.act_window,name:documents.workflow_rule_action
msgid "Workflow Actions"
msgstr "工作流動作"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_workflow_action__workflow_rule_id
msgid "Workflow Rule"
msgstr "工作流規則"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_document__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_facet__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_request_wizard__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_share__folder_id
#: model:ir.model.fields,field_description:documents.field_documents_tag__folder_id
#: model_terms:ir.ui.view,arch_db:documents.facet_view_search
#: model_terms:ir.ui.view,arch_db:documents.folder_view_tree
msgid "Workspace"
msgstr "Workspace"

#. module: documents
#: model:ir.actions.act_window,name:documents.folder_action
#: model:ir.ui.menu,name:documents.folder_menu
msgid "Workspaces"
msgstr "工作區"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/tours/documents.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr "哇！6个文檔在幾秒鐘內處理完畢。<br/>導覽結束。現在嘗試上載您自己的文檔。"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "Write Access"
msgstr "寫入存取"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_folder__group_ids
msgid "Write Groups"
msgstr "寫入組別"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "Write a tooltip for the action here"
msgstr "為此處的動作添加提示"

#. module: documents
#: model:ir.model.fields,help:documents.field_documents_document__attachment_type
msgid ""
"You can either upload a file from your computer or copy/paste an internet "
"link to your file."
msgstr "您可以從您的電腦上傳一個檔案或者'複製/貼上'一個網路連結到您的檔案。"

#. module: documents
#: code:addons/documents/models/folder.py:0
#, python-format
msgid "You cannot create recursive folders."
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "Youtube Video"
msgstr "YouTube 影片"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragEnter(section.id, valueId)"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDragLeave"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "_onDrop(section.id, valueId)"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "delete"
msgstr "刪除"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "document"
msgstr "單據"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents"
msgstr "文件"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "documents selected"
msgstr "文件已選擇"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_page
msgid "documents shared by"
msgstr "文件分享者"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.Category"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_search_panel.xml:0
#, python-format
msgid "documents.SearchPanel.FiltersGroup"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "download"
msgstr "下載"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.share_view_form
#: model_terms:ir.ui.view,arch_db:documents.share_view_form_popup
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Discuss proposal"
msgstr "例如.討論提案"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.folder_view_form
msgid "e.g. Finance"
msgstr "例如金融"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_request_form_view
msgid "e.g. Missing Expense"
msgstr "例如：缺失的費用"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.facet_view_form
msgid "e.g. Status"
msgstr "例如：狀態"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.tag_view_form
msgid "e.g. To Validate"
msgstr "例如，待驗證"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.workflow_rule_form_view
msgid "e.g. Validate document"
msgstr "例如：已驗證文件"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.documents_upload_url_view
msgid "e.g. https://www.youtube.com/watch?v=CP96yVnXNrY"
msgstr "例. https://www.youtube.com/watch?v=CP96yVnXNrY"

#. module: documents
#: model:ir.model.fields,field_description:documents.field_documents_link_to_record_wizard__is_readonly_model
msgid "is_readonly_model"
msgstr "is_readonly_model"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "restore"
msgstr "還原"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#: code:addons/documents/static/src/owl/components/pdf_page/pdf_page.xml:0
#, python-format
msgid "select"
msgstr "選取"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/xml/documents_inspector.xml:0
#, python-format
msgid "selected"
msgstr "已選取"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.public_page_layout
msgid "shared by"
msgstr "分享者"

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/js/documents_controller_mixin.js:0
#, python-format
msgid "status code: %s, message: %s"
msgstr "狀態代碼：%s，訊息：%s"

#. module: documents
#: model_terms:ir.ui.view,arch_db:documents.document_view_kanban
msgid "toggle favorite"
msgstr ""

#. module: documents
#. openerp-web
#: code:addons/documents/static/src/owl/components/pdf_manager/pdf_manager.js:0
#, python-format
msgid "unnamed"
msgstr "未命名"
