id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_documents_attachment_base_group_user,documents_attachment_base_group_user,model_documents_document,base.group_user,1,1,0,0
access_documents_facet_base_group_user,documents_facet_base_group_user,model_documents_facet,base.group_user,1,0,0,0
access_documents_tag_base_group_user,documents_tag_base_group_user,model_documents_tag,base.group_user,1,0,0,0
access_documents_share_base_group_user,documents_share_base_group_user,model_documents_share,base.group_user,0,0,0,0
access_documents_folder_base_group_user,documents_folder_base_group_user,model_documents_folder,base.group_user,1,0,0,0
access_documents_workflow_action_base_group_user,documents_workflow_action_base_group_user,model_documents_workflow_action,base.group_user,1,0,0,0
access_documents_workflow_rule_base_group_user,documents_workflow_rule_base_group_user,model_documents_workflow_rule,base.group_user,1,0,0,0

access_documents_attachment_group_user,documents_attachment_group_user,model_documents_document,documents.group_documents_user,1,1,1,0
access_documents_facet_group_user,documents_facet_group_user,model_documents_facet,documents.group_documents_user,1,0,0,0
access_documents_tag_group_user,documents_tag_group_user,model_documents_tag,documents.group_documents_user,1,0,0,0
access_documents_share_group_user,documents_share_group_user,model_documents_share,documents.group_documents_user,1,1,1,1
access_documents_folder_group_user,documents_folder_group_user,model_documents_folder,documents.group_documents_user,1,0,0,0
access_documents_workflow_action_group_user,documents_workflow_action_group_user,model_documents_workflow_action,documents.group_documents_user,1,0,0,0
access_documents_workflow_rule_group_user,documents_workflow_rule_group_user,model_documents_workflow_rule,documents.group_documents_user,1,0,0,0

access_documents_attachment_group_manager,documents_attachment_group_manager,model_documents_document,documents.group_documents_manager,1,1,1,1
access_documents_facet_group_manager,documents_facet_group_manager,model_documents_facet,documents.group_documents_manager,1,1,1,1
access_documents_tag_group_manager,documents_tag_group_manager,model_documents_tag,documents.group_documents_manager,1,1,1,1
access_documents_share_group_manager,documents_share_group_manager,model_documents_share,documents.group_documents_manager,1,1,1,1
access_documents_folder_group_manager,documents_folder_group_manager,model_documents_folder,documents.group_documents_manager,1,1,1,1
access_documents_workflow_action_group_manager,documents_workflow_action_group_manager,model_documents_workflow_action,documents.group_documents_manager,1,1,1,1
access_documents_workflow_rule_group_manager,documents_workflow_rule_group_manager,model_documents_workflow_rule,documents.group_documents_manager,1,1,1,1

access_documents_request_wizard,access.documents.request_wizard,model_documents_request_wizard,documents.group_documents_user,1,1,1,0
access_documents_link_to_record_wizard,access.documents.link_to_record_wizard,model_documents_link_to_record_wizard,documents.group_documents_user,1,1,1,0
