<odoo>

        <record id="module_category_documents_management" model="ir.module.category">
            <field name="name">Documents</field>
            <field name="description">Allows you to manage your documents.</field>
            <field name="sequence">1</field>
        </record>

        <record id="group_documents_user" model="res.groups">
            <field name="name">User</field>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="category_id" ref="base.module_category_productivity_documents"/>
        </record>

        <record id="group_documents_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="category_id" ref="base.module_category_productivity_documents"/>
            <field name="implied_ids" eval="[(4, ref('group_documents_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <!-- folder rules -->
        <data noupdate="1">

        <record id="documents_folder_global_rule" model="ir.rule">
            <field name="name">Documents.folder: company</field>
            <field name="model_id" ref="model_documents_folder"/>
            <field name="domain_force">['|',
                ('company_id', '=', False),
                ('company_id',  'in', company_ids)]</field>
        </record>

        <record id="documents_folder_groups_rule" model="ir.rule">
            <field name="name">Documents.folder: groups</field>
            <field name="model_id" ref="model_documents_folder"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">[
                '|', '|',
                    '&amp;',
                        ('read_group_ids', '=', False),
                        ('group_ids', '=', False),
                    ('group_ids', 'in', [g.id for g in user.groups_id]),
                    ('read_group_ids', 'in', [g.id for g in user.groups_id]),
                ]</field>
        </record>

        <record id="documents_folder_manager_rule" model="ir.rule">
            <field name="name">Documents.folder: manager rule</field>
            <field name="model_id" ref="model_documents_folder"/>
            <field name="groups" eval="[(4, ref('documents.group_documents_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

        <!-- documents rules -->

        <record id="documents_document_global_rule" model="ir.rule">
            <field name="name">Documents.document: global</field>
            <field name="model_id" ref="model_documents_document"/>
            <field name="domain_force">['|',
                ('folder_id.company_id', '=', False),
                ('folder_id.company_id', 'in', company_ids)]</field>
        </record>

        <!--
            The two following rule apply to base.group_user (instead of group_documents_user) so users can access
            documents on which they have an activity scheduled for them.
        -->
        <record id="documents_document_readonly_rule" model="ir.rule">
            <field name="name">Documents.document: readonly rule</field>
            <field name="model_id" ref="model_documents_document"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">[
                '&amp;',
                    ('folder_id.read_group_ids', 'in', [g.id for g in user.groups_id]),
                    '|',
                        ('folder_id.user_specific', '=', False),
                        ('owner_id', '=', user.id)
                ]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="documents_document_write_rule" model="ir.rule">
            <field name="name">Documents.document: folder write groups</field>
            <field name="model_id" ref="model_documents_document"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">[
                '|',
                    '&amp;',
                        ('folder_id.group_ids', 'in', [g.id for g in user.groups_id]),
                        '|',
                            ('folder_id.user_specific_write', '=', False),
                            ('owner_id', '=', user.id),
                    '&amp;',
                        ('folder_id.read_group_ids', '=', False),
                        ('folder_id.group_ids', '=', False)]
                    </field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="documents_document_manager_rule" model="ir.rule">
            <field name="name">Documents.document: manager rule</field>
            <field name="model_id" ref="model_documents_document"/>
            <field name="groups" eval="[(4, ref('documents.group_documents_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

        <!-- share links rules -->

        <record id="documents_share_folder_company_rule" model="ir.rule">
            <field name="name">Documents.share: company</field>
            <field name="model_id" ref="model_documents_share"/>
            <field name="domain_force">['|',
                ('folder_id.company_id', '=', False),
                ('folder_id.company_id', 'in', company_ids)]</field>
        </record>

        <record id="documents_share_folder_create_uid_rule" model="ir.rule">
            <field name="name">Documents.share: create uid</field>
            <field name="model_id" ref="model_documents_share"/>
            <field name="groups" eval="[(4, ref('group_documents_user'))]"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
        </record>

        <record id="documents_share_manager_rule" model="ir.rule">
            <field name="name">Documents.share: manager rule</field>
            <field name="model_id" ref="model_documents_share"/>
            <field name="groups" eval="[(4, ref('group_documents_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

    </data>
</odoo>
