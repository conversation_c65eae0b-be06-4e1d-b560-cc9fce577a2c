<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="documents.component.PdfPage" owl="1">
        <div class="o_pdf_page" t-att-class="{ o_pdf_thumbnail_dragover: state.isHover, o_pdf_page_selected: props.isSelected, o_pdf_page_preview: props.isPreview }">
            <div t-if="!props.isPreview" class="o_documents_pdf_button_wrapper">
                <i class="o_documents_pdf_page_selector fa" t-att-class="{'fa-check-circle': props.isSelected, 'fa-circle-thin': !props.isSelected }" aria-label="select" title="select" t-on-click="_onClickSelect"/>
            </div>
            <div class="o_documents_pdf_canvas_wrapper"
                draggable="true"
                t-on-dragstart="_onDragStart"
                t-on-drop="_onDrop"
                t-on-dragover="_onDragOver"
                t-on-dragenter="_onDragEnter"
                t-on-dragleave="_onDragLeave"
                t-on-click="_onClickWrapper"
                t-ref="canvasWrapper">
            </div>
        </div>
    </t>
</templates>
