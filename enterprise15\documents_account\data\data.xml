<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Workflow Rules -->

        <!-- create vendor bill -->

        <record id="vendor_bill_rule_financial" model="documents.workflow.rule" forcecreate="0">
            <field name="sequence">10</field>
            <field name="name">Create Bill</field>
            <field name="remove_activities">True</field>
            <field name="create_model">account.move.in_invoice</field>
            <field name="domain_folder_id" ref="documents.documents_finance_folder"/>
        </record>

        <record id="documents_replace_validated" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="vendor_bill_rule_financial"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_status"/>
            <field name="tag_id" ref="documents.documents_finance_status_validated"/>
        </record>

        <record id="documents_replace_documents_vendor_bill" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="vendor_bill_rule_financial"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_documents"/>
            <field name="tag_id" ref="documents.documents_finance_documents_bill"/>
        </record>

        <record id="documents_replace_vendor_bill_year" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="vendor_bill_rule_financial"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_fiscal_year"/>
            <field name="tag_id" ref="documents.documents_finance_fiscal_year_2018"/>
        </record>

        <!-- Create bill -->

        <record id="documents.documents_rule_internal_mark" model="documents.workflow.rule" forcecreate="0">
            <field name="name">Create Bill</field>
            <field name="remove_activities">True</field>
            <field name="create_model">account.move.in_invoice</field>
        </record>
        <!-- create vendor refund -->

        <record id="credit_note_rule" model="documents.workflow.rule" forcecreate="0">
            <field name="sequence">11</field>
            <field name="name">Create Vendor Refund</field>
            <field name="remove_activities">True</field>
            <field name="create_model">account.move.in_refund</field>
            <field name="domain_folder_id" ref="documents.documents_finance_folder"/>
        </record>

        <record id="documents_replace_inbox_credit_note" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="credit_note_rule"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_status"/>
            <field name="tag_id" ref="documents.documents_finance_status_validated"/>
        </record>

        <record id="documents_replace_documents_credit_note" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="credit_note_rule"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_documents"/>
            <field name="tag_id" ref="documents.documents_finance_documents_bill"/>
        </record>

        <!-- create customer invoice -->

        <record id="customer_invoice_rule" model="documents.workflow.rule" forcecreate="0">
            <field name="sequence">11</field>
            <field name="name">Create Customer Invoice</field>
            <field name="remove_activities">True</field>
            <field name="create_model">account.move.out_invoice</field>
            <field name="domain_folder_id" ref="documents.documents_finance_folder"/>
        </record>

        <record id="documents_replace_inbox_customer_invoice" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="customer_invoice_rule"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_status"/>
            <field name="tag_id" ref="documents.documents_finance_status_validated"/>
        </record>

        <record id="documents_replace_documents_customer_invoice" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="customer_invoice_rule"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_documents"/>
            <field name="tag_id" ref="documents.documents_finance_documents_bill"/>
        </record>

        <!-- create credit note -->

        <record id="credit_note_rule" model="documents.workflow.rule" forcecreate="0">
            <field name="sequence">11</field>
            <field name="name">Create Credit Note</field>
            <field name="remove_activities">True</field>
            <field name="create_model">account.move.out_refund</field>
            <field name="domain_folder_id" ref="documents.documents_finance_folder"/>
        </record>

        <record id="documents_replace_inbox_credit_note" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="credit_note_rule"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_status"/>
            <field name="tag_id" ref="documents.documents_finance_status_validated"/>
        </record>

        <record id="documents_replace_documents_credit_note" model="documents.workflow.action" forcecreate="0">
            <field name="workflow_rule_id" ref="credit_note_rule"/>
            <field name="action">replace</field>
            <field name="facet_id" ref="documents.documents_finance_documents"/>
            <field name="tag_id" ref="documents.documents_finance_documents_bill"/>
        </record>

        <!-- activity types -->

        <record id="mail_documents_activity_data_vat" model="mail.activity.type">
            <field name="name">Tax Statement</field>
            <field name="delay_count">3</field>
            <field name="delay_unit">months</field>
            <field name="res_model">documents.document</field>
            <field name="delay_from">previous_activity</field>
        </record>

        <record id="mail_documents_activity_data_vat" model="mail.activity.type">
            <field name="chaining_type">trigger</field>
            <field name="triggered_next_type_id" ref="mail_documents_activity_data_vat"/>
        </record>


        <record id="mail_documents_activity_data_fs" model="mail.activity.type">
            <field name="name">Financial Statement</field>
            <field name="delay_count">12</field>
            <field name="delay_unit">months</field>
            <field name="res_model">res.partner</field>
            <field name="delay_from">previous_activity</field>
        </record>

        <record id="mail_documents_activity_data_fs" model="mail.activity.type">
            <field name="chaining_type">trigger</field>
            <field name="triggered_next_type_id" ref="mail_documents_activity_data_fs"/>
        </record>
    </data>
</odoo>
