# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
# 
# Translators:
# <PERSON>ied<PERSON><PERSON>-Nesselbosch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>W<PERSON><PERSON>en Sie diese Seite ab</b>, da wir zunächst alle Eingangsrechnungen "
"bearbeiten wollen."

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Eine Reihe an Bedingungen und Aktionen, die für alle Anhänge verfügbar sind,"
" die den Bedingungen entprechen"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr "Es existiert bereits eine Einstellung für dieses Journal"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Kontoabstimmungs-Widget"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr "Buchhaltung"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder
msgid "Accounting Workspace"
msgstr "Buchhaltungsarbeitsbereich"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reconcile_model__activity_type_id
msgid "Activity type"
msgstr "Aktivitätstyp"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Da diese PDF-Datei mehrere Dokumente enthält, sollten wir sie aufteilen und "
"im Ganzen verarbeiten."

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "Dateianhang"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr "Zentralisieren sie Dateien, die in der Buchhaltung angehängt wurden"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "<b>Wählen Sie ein Dokument aus</b>, indem Sie auf eine Karte klicken."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""
"Klicken Sie auf eine Miniaturansicht, um eine <b>Vorschau des Dokuments</b> "
"anzuzeigen."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Klicken Sie auf den <b>Seitentrenner</b>: Diese beiden Seiten sollen nicht "
"getrennt werden, da sie zum selben Dokument gehören."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click the cross to <b>exit preview</b>."
msgstr "Klicken Sie auf das Kreuz, um die <b>Vorschau zu verlassen</b>."

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen "

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Erstellen"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.vendor_bill_rule_financial
msgid "Create Bill"
msgstr "Rechnung erstellen"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.credit_note_rule
msgid "Create Credit Note"
msgstr "Gutschrift erzeugen"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.customer_invoice_rule
msgid "Create Customer Invoice"
msgstr "Kundenrechnung erstellen"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_refund
msgid "Credit note"
msgstr "Gutschrift"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_invoice
msgid "Customer invoice"
msgstr "Kundenrechnung"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr "Dokumente Kontoeinstellungen"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Export-Assistent für Buchhaltungsberichte"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "Finanzbericht"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "Ordner"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr "Ordner, in dem die generierte Datei gespeichert werden soll"

#. module: documents_account
#: code:addons/documents_account/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Generierte Dokumente"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "ID"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr "Journal"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr "Journal- und Ordnereinstellungen"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "Journale"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr "Zu synchronisierende Journale"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Verarbeiten Sie Dokumente in Ihrem Posteingang.<br/><i>Tipp: Verwenden Sie "
"Stichwörter, um Dokumente zu filtern und Ihre Prozesse zu strukturieren.</i>"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process these bills: turn them into vendor bills."
msgstr ""
"Verarbeiten Sie diese Rechnungen: Verwandeln Sie sie in "
"Lieferantenrechnungen."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "Verarbeiten wir dieses Dokument, das vom Scanner kommt."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Kennzeichnen Sie diese E-Mail als „rechtlich“<br/><i>Hinweis: Aktionen "
"können je nach Arbeitsbereich auf Ihren Prozess zugeschnitten werden.</i>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Voreinstellungen, um Buchungssätze während eines Rechnungs- und "
"Zahlungsabgleichs zu erstellen."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_move_line__reconciliation_invoice_id
msgid "Reconciliation Invoice"
msgstr "Abstimmung Rechnung"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_payment__document_request_line_id
msgid "Reconciliation Journal Entry Line"
msgstr "Abstimmungsbuchungszeile"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_md
msgid "Reconciliation request"
msgstr "Abstimmungsanfrage"

#. module: documents_account
#: code:addons/documents_account/models/account_move.py:0
#, python-format
msgid "Request Document for %s"
msgstr "Dokument anfordern für %s"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Senden Sie dieses Schreiben an die Rechtsabteilung, indem Sie die richtigen "
"Stichwörter zuweisen."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "Stichwörter"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr "Steuerbilanz"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_refund
msgid "Vendor Credit Note"
msgstr "Lieferantengutschrift"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_invoice
msgid "Vendor bill"
msgstr "Lieferantenrechnung"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Möchten Sie ein<b>papierloses Unternehmen</b> werden? Entdecken Sie Odoo "
"Dokumente."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "Arbeitsbereich"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Wow ... Sie haben 6 Dokumente in wenigen Sekunden verarbeitet, super. <br/> "
"Versuchen Sie jetzt, Ihre eigenen Dokumente hochzuladen."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder
msgid "account default folder"
msgstr "Standard-Benutzerordner"
