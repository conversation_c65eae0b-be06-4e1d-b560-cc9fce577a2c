# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
# 
# Translators:
# <PERSON> Ha<PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Persian (https://www.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>این صفحه را لغو انتخاب کنید</b> زیرا قصد داریم ابتدا همه صورتحساب ها را "
"پردازش کنیم."

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"مقادیر ست شده در اینجا خاص "
"شرکت هستند.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"مجموعه ای از شرایط و اقداماتی که برای همه پیوست های منطبق با شرایط در دسترس "
"خواهد بود"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr "تنظیماتی از قبل برای این روزنامه وجود دارد"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "ویجت تطبیق حساب"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr "حسابداری "

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder
msgid "Accounting Workspace"
msgstr "فضای کاری حسابداری"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reconcile_model__activity_type_id
msgid "Activity type"
msgstr "نوع فعالیت"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"از آنجایی که این PDF حاوی چندین سند است، بیایید به صورت انبوه تقسیم و پردازش"
" کنیم."

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "پیوست"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr "فایل ها و اسناد حسابداری را متمرکز کنید"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "برای <b>انتخاب سند</b> روی کارت کلیک کنید."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "برای <b>پیش نمایش سند</b> روی تصویر کوچک کلیک کنید."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"روی <b>جداکننده صفحه</b> کلیک کنید: ما نمی‌خواهیم این دو صفحه را جدا کنیم "
"زیرا به یک سند تعلق دارند."

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "شرکت"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "ایجاد"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.vendor_bill_rule_financial
msgid "Create Bill"
msgstr "ایجاد صورتحساب"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.credit_note_rule
msgid "Create Credit Note"
msgstr "یادداشت اعتباری ایجاد کنید"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.customer_invoice_rule
msgid "Create Customer Invoice"
msgstr "ایجاد فاکتور مشتری"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "ایجاد توسط"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_refund
msgid "Credit note"
msgstr "سند بستانکاری"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_invoice
msgid "Customer invoice"
msgstr "فاکتور مشتری"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr "تنظیمات حساب اسناد"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "ویزارد خروجی برای گزارش‌های حسابداری"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "صورتهای مالی"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "پوشه"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr "پوشه ای که فایل تولید شده را در آن ذخیره کنید"

#. module: documents_account
#: code:addons/documents_account/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "اسناد تولید شده"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "شناسه"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr "روزنامه"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "داده روزنامه"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move_line
msgid "Journal Item"
msgstr "آیتم روزنامه"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr "تنظیمات روزنامه و پوشه"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "دفترروزنامه‌‌ها"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr "روزنامه ها برای همگام سازی"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "آخرین به روز رسانی توسط"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "آخرین به‌روز‌رسانی در"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"بیایید اسناد را در صندوق ورودی خود پردازش کنیم.<br/><i>نکته: از برچسب‌ها "
"برای فیلتر کردن اسناد و ساختار فرآیند خود استفاده کنید.</i>"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process these bills: turn them into vendor bills."
msgstr ""
"بیایید این صورت‌حساب‌ها را پردازش کنیم: آنها را به صورت‌حساب‌های فروشنده "
"تبدیل کنیم."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "بیایید این سند را که از اسکنر ما آمده است پردازش کنیم."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"از پیش تنظیم شده برای ایجاد ورودی های روزنامه در حین تطبیق فاکتورها و پرداخت"
" ها"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_move_line__reconciliation_invoice_id
msgid "Reconciliation Invoice"
msgstr "تطبیق فاکتور"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_payment__document_request_line_id
msgid "Reconciliation Journal Entry Line"
msgstr "سطر ورودی روزنامه تطبیق"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_md
msgid "Reconciliation request"
msgstr ""

#. module: documents_account
#: code:addons/documents_account/models/account_move.py:0
#, python-format
msgid "Request Document for %s"
msgstr "درخواست سند برای %s"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "این نامه را با اختصاص برچسب های مناسب به بخش حقوقی ارسال کنید."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "برچسب‌ها"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr ""

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_refund
msgid "Vendor Credit Note"
msgstr "سند بستانکار فروشنده"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_invoice
msgid "Vendor bill"
msgstr "صورتحساب فروشنده"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"آیا می خواهید به یک <b>شرکت بدون کاغذ</b> تبدیل شوید؟ بیایید اسناد Odoo را "
"کشف کنیم."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "فضای کار"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"وای... 6 سند در چند ثانیه پردازش شد، کا شما خوب است.<br/>تور کامل شد. اکنون "
"اسناد خود را بارگذاری کنید."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder
msgid "account default folder"
msgstr "پوشه پیش فرض حساب"
