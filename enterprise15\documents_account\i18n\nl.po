# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Schakel deze pagina uit</b>, aangezien we van plan zijn om eerst alle "
"facturen te verwerken."

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" aria-label=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Een set van condities en acties welke beschikbaar is voor alle bijlagen die "
"aan deze condities voldoen"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr "Er bestaat al een instelling voor dit dagboek"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Boekhoudkundige verwerkingswidget"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr "Boekhouding"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder
msgid "Accounting Workspace"
msgstr "Werkruimte boekhouding"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reconcile_model__activity_type_id
msgid "Activity type"
msgstr "Soort activiteit"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Omdat deze pdf meerdere documenten bevat, kunnen we deze splitsen en in bulk"
" verwerken."

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "Bijlage"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr "Centraliseer boekhoudkundige bestanden en documenten"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "Klik op een kaart om het <b>document te selecteren</b>."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr ""
"Klik op een miniatuur om <b>een voorbeeld van het document</b> te zien."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Klik op het <b>paginascheidingsteken</b>: we willen deze twee pagina's niet "
"splitsen omdat ze tot hetzelfde document behoren."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click the cross to <b>exit preview</b>."
msgstr "Klik op het kruis om <b>de preview te verlaten</b>."

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Aanmaken"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.vendor_bill_rule_financial
msgid "Create Bill"
msgstr "Leveranciersfactuur aanmaken"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.credit_note_rule
msgid "Create Credit Note"
msgstr "Creditfactuur aanmaken"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.customer_invoice_rule
msgid "Create Customer Invoice"
msgstr "Klantfactuur aanmaken"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_refund
msgid "Credit note"
msgstr "Creditfactuur"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_invoice
msgid "Customer invoice"
msgstr "Klantfactuur"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr "Documenten account instellingen"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Exporteer wizard voor boekhoudkundige rapporten"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "Financieel overzicht"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "Map"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr "Map waar het gegenereerde bestand moet bewaard worden"

#. module: documents_account
#: code:addons/documents_account/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Gegenereerde documenten"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "ID"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr "Dagboek"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr "Dagboek en map instellingen"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "Dagboeken"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr "Dagboeken te synchroniseren"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Laten we documenten in je inbox verwerken.<br/><i> Tip: gebruik labels om "
"documenten te filteren en je proces te structureren.</i>"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process these bills: turn them into vendor bills."
msgstr ""
"Laten we deze facturen verwerken: verander ze in leveranciersrekeningen."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "Laten we dit document, afkomstig van onze scanner, verwerken."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Laten we deze e-mail voorzien van een juridisch label<br/> <i>Tips: acties "
"kunnen worden aangepast aan het proces, afhankelijk van de werkruimte.</i>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Voorinstelling om boekingen aan te maken bij het afletteren van facturen met"
" betalingen"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_move_line__reconciliation_invoice_id
msgid "Reconciliation Invoice"
msgstr "Factuur afletteren"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_payment__document_request_line_id
msgid "Reconciliation Journal Entry Line"
msgstr "Afletterboeking"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_md
msgid "Reconciliation request"
msgstr "Afletter aanvraag"

#. module: documents_account
#: code:addons/documents_account/models/account_move.py:0
#, python-format
msgid "Request Document for %s"
msgstr "Documentverzoek voor %s"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr ""
"Stuur deze brief naar de juridische afdeling door de juiste labels toe te "
"wijzen."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "Labels"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr "BTW aangifte"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_refund
msgid "Vendor Credit Note"
msgstr "Leveranciers creditfactuur"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_invoice
msgid "Vendor bill"
msgstr "Leveranciersfactuur"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Wil je een <b>papierloos bedrijf</b> worden? Laten we Odoo-documenten "
"ontdekken."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "Werkruimte"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Wauw ... 6 documenten verwerkt in een paar seconden, je bent goed. <br/>De "
"rondleiding is voltooid. Probeer nu je eigen documenten te uploaden."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder
msgid "account default folder"
msgstr "Boekhouding standaard map"
