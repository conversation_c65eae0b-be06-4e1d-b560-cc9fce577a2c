# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:43+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Скасуйте вибір цієї сторінки</b> оскільки ми плануємо спочатку обробити "
"всі рахунки."

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Набір дій та умов, які будуть доступні всім прикріпленням, що відповідають "
"умовам"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr "Для цього журналу вже існує параметр"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Віджет узгодження рахунку"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr "Бухоблік"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder
msgid "Accounting Workspace"
msgstr "Папка бухобліку"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reconcile_model__activity_type_id
msgid "Activity type"
msgstr "Тип дії"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Оскільки цей PDF містить кілька документі, давайте розділимо їх і обробимо "
"групою."

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "Прикріплення"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr "Централізуйте бухгалтерські файли та документи"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a card to <b>select the document</b>."
msgstr "Натисніть на картку, щоб <b>обрати документ</b>."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Клікніть на мініатюру, щоби <b>переглянути попередньо документ</b>."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Натисніть на <b>роздільник сторінок</b>: ми не хочемо розділяти ці дві "
"сторінки, оскільки вони належать до одного документу."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Click the cross to <b>exit preview</b>."
msgstr "Натисніть на хрестик, щоби <b>закрити превʼю</b>."

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "Компанія"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Створити"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.vendor_bill_rule_financial
msgid "Create Bill"
msgstr "Створити рахунок"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.credit_note_rule
msgid "Create Credit Note"
msgstr "Створити сторно"

#. module: documents_account
#: model:documents.workflow.rule,name:documents_account.customer_invoice_rule
msgid "Create Customer Invoice"
msgstr "Створити рахунок клієнта"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "Створив"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "Створено на"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_refund
msgid "Credit note"
msgstr "Сторно"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_out_invoice
msgid "Customer invoice"
msgstr "Рахунок клієнта"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr "Налаштування обліку документів"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Помічник експорту для бухгалтерських звітів"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "Банківська виписка"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "Папка"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr "Папка для зберігання створеного файлу"

#. module: documents_account
#: code:addons/documents_account/wizard/report_export_wizard.py:0
#, python-format
msgid "Generated Documents"
msgstr "Згенеровані документи"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "ID"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr "Журнал"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr "Налаштування журналу та папки"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "Журнали"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr "Журнали для синхронізації"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting____last_update
msgid "Last Modified on"
msgstr "Останні зміни на"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Давайте обробимо документи у вашій вхідній пошті.<br/><i>Примітка: "
"Використовуйте теги, щоби фільтрувати документи та структурувати ваш "
"процес.</i>"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process these bills: turn them into vendor bills."
msgstr "Давайте обробимо ці рахунки: перетворіть їх на рахунки постачальника."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Let's process this document, coming from our scanner."
msgstr "Давайте обробимо цей документ, що надійшов з нашого сканера."

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Позначте цей лист, як юридичний<br/> <i>Порада: дії можна адаптувати до "
"вашого процесу, відповідно до папки.</i>"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Заздалегідь встановлюйте записи журналу під час виставлення рахунків-фактур "
"та відповідних платежів"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_move_line__reconciliation_invoice_id
msgid "Reconciliation Invoice"
msgstr "Узгодження рахунку"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__document_request_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_payment__document_request_line_id
msgid "Reconciliation Journal Entry Line"
msgstr "Узгодження рядка запису журналу"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_md
msgid "Reconciliation request"
msgstr "Запит на узгодження"

#. module: documents_account
#: code:addons/documents_account/models/account_move.py:0
#, python-format
msgid "Request Document for %s"
msgstr "Запитати документ %s"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "Надішліть цей лист до юридичного відділу, додавши теги справа."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "Теги"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr "Податкова виписка"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_refund
msgid "Vendor Credit Note"
msgstr "Сторно постачальника"

#. module: documents_account
#: model:ir.model.fields.selection,name:documents_account.selection__documents_workflow_rule__create_model__account_move_in_invoice
msgid "Vendor bill"
msgstr "Рахунок постачальника"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr ""
"Хочете стати <b>безпаперовою компанією</b>? Відкрийте для себе Odoo "
"Документи."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "Папка"

#. module: documents_account
#. openerp-web
#: code:addons/documents_account/static/src/js/tour.js:0
#, python-format
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br/>The tour is "
"complete. Try uploading your own documents now."
msgstr ""
"Вау... 6 документів оброблені за кілька секунд, Ви молодці.<br/>Тур "
"завершено. Тепер спробуйте завантажити ваші власні документи."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder
msgid "account default folder"
msgstr "типова папка для бухобліку"
