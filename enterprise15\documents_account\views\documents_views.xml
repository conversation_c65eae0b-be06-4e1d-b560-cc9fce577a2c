<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_folder_settings_installer" model="ir.actions.act_window">
         <field name="name">Journals to synchronize</field>
         <field name="type">ir.actions.act_window</field>
         <field name="res_model">documents.account.folder.setting</field>
         <field name="view_mode">tree,form</field>
    </record>

    <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.documents</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="70"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('o_documents_block')]" position="attributes">
                    <attribute name="invisible">0</attribute>
                </xpath>
                <xpath expr="//div[hasclass('o_documents_block')]" position="inside">
                    <div class="row mt16 o_settings_container">
                        <div class="col-xs-12 col-md-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="documents_account_settings"/>
                            </div>
                            <div class="o_setting_right_pane o_documents_right_pane">
                                <label for="documents_account_settings"/>
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                                <div class="row">
                                    <div class="text-muted col-md-12">
                                        Centralize accounting files and documents
                                    </div>
                                </div>
                                <div class="content-group" attrs="{'invisible' : [('documents_account_settings', '=', False)]}">
                                    <div class="row mt16">
                                        <label class="o_form_label col-lg-3" for="product_folder" string="Workspace"/>
                                       <field name="account_folder" attrs="{'required' : [('documents_account_settings', '=', True)]}"/>
                                    </div>
                                    <div class="row col-lg-4">
                                        <button type="action" name="%(documents_account.action_folder_settings_installer)d" string="Journals" icon="fa-arrow-right" class="btn-link"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
    </record>

    <record id="documents_folder_setting_view_list" model="ir.ui.view">
        <field name="name">documents folder setting list view</field>
        <field name="model">documents.account.folder.setting</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="company_id" invisible="1"/>
                <field name="journal_id" domain="[('company_id', '=', company_id), ('type', 'not in', ['cash', 'bank'])]"/>
                <field name="folder_id" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]"/>
                <field name="tag_ids" widget="many2many_tags" domain="[('folder_id', 'parent_of', folder_id)]"/>
            </tree>
        </field>
    </record>

    <record id="documents_folder_setting_view_form" model="ir.ui.view">
        <field name="name">documents folder setting form view</field>
        <field name="model">documents.account.folder.setting</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="company_id" readonly="1"/>
                        <field name="journal_id" domain="[('company_id', '=', company_id)]"/>
                        <field name="folder_id" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]"/>
                        <field name="tag_ids" widget="many2many_tags" domain="[('folder_id', 'parent_of', folder_id)]"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
