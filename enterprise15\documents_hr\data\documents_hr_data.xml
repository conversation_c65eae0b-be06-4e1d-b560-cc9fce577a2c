<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Folders -->

        <record id="documents_hr_folder" model="documents.folder" forcecreate="0">
            <field name="name">HR</field>
            <field name="group_ids" eval="[(4, ref('hr.group_hr_user'))]"/>
            <field name="read_group_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="user_specific">True</field>
            <field name="sequence">12</field>
        </record>

        <!-- Facets -->

        <record id="documents_hr_documents" model="documents.facet" forcecreate="0">
            <field name="name">Documents</field>
            <field name="sequence">6</field>
            <field name="folder_id" ref="documents_hr_folder"/>
        </record>

        <!-- Tags -->

        <record id="documents_hr_documents_absences" model="documents.tag" forcecreate="0">
            <field name="name">Absences</field>
            <field name="facet_id" ref="documents_hr_documents"/>
            <field name="sequence">10</field>
        </record>

        <record id="documents_hr_documents_employees" model="documents.tag" forcecreate="0">
            <field name="name">Employees Documents</field>
            <field name="facet_id" ref="documents_hr_documents"/>
            <field name="sequence">12</field>
        </record>

        <record id="documents_hr_documents_Cerification" model="documents.tag" forcecreate="0">
            <field name="name">Certifications</field>
            <field name="facet_id" ref="documents_hr_documents"/>
            <field name="sequence">14</field>
        </record>

        <!-- company default setting -->

        <record id="base.main_company" model="res.company">
            <field name="documents_hr_folder" ref="documents_hr_folder"/>
        </record>

    </data>
</odoo>
