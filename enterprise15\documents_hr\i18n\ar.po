# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"القيم المحددة هنا تنطبق على "
"المؤسسة الحالية فقط. \" aria-label=\"Values set here are company-specific.\""
" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Default tags\n"
"                                </span>"
msgstr ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    علامات التصنيف الافتراضية\n"
"                                </span>"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_absences
msgid "Absences"
msgstr "الغيابات "

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr "قم بمركزة مستندات موظفيك (العقود، إيصالات الدفع، وما إلى ذلك.) "

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_Cerification
msgid "Certifications"
msgstr "الشهادات"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "المستند"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "عدد المستندات"

#. module: documents_hr
#: model:documents.facet,name:documents_hr.documents_hr_documents
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "المستندات"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "إعدادات الموارد البشرية للمستندات "

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
msgid "Employee"
msgstr "الموظف"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_employees
msgid "Employees Documents"
msgstr "مستندات الموظفين "

#. module: documents_hr
#: model:documents.folder,name:documents_hr.documents_hr_folder
msgid "HR"
msgstr "الموارد البشرية"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "الموارد البشرية"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "Users"
msgstr "المستخدمين "

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "مساحة العمل"

#. module: documents_hr
#: code:addons/documents_hr/models/hr_employee.py:0
#, python-format
msgid "You must set an address on the employee to use Documents features."
msgstr "عليك تعيين عنوان للموظف حتى تتمكن من استخدام خصائص المستندات. "

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "مساحة عمل الموارد البشرية "

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "مساحة العمل الافتراضية للموارد البشرية "
