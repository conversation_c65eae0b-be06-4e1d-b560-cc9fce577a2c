# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Default tags\n"
"                                </span>"
msgstr ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    デフォルトタグ\n"
"                                </span>"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_absences
msgid "Absences"
msgstr "不在"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr "従業員のドキュメントを一元管理（契約、給与明細など）"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_Cerification
msgid "Certifications"
msgstr "検定"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "会社"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "ドキュメント"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "ドキュメント数"

#. module: documents_hr
#: model:documents.facet,name:documents_hr.documents_hr_documents
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "ドキュメント"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "ドキュメント人事設定"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
msgid "Employee"
msgstr "従業員"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_employees
msgid "Employees Documents"
msgstr "従業員ドキュメント"

#. module: documents_hr
#: model:documents.folder,name:documents_hr.documents_hr_folder
msgid "HR"
msgstr "HR"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "人材"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "Users"
msgstr "ユーザ"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "ワークスペース"

#. module: documents_hr
#: code:addons/documents_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You must set a private address on the Employee in order to use Document's "
"features."
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "人事ワークスペース"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "人事デフォルト作業区"
