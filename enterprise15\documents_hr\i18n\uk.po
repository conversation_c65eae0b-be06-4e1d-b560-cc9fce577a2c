# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Default tags\n"
"                                </span>"
msgstr ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Теги за замовчуванням\n"
"                                </span>"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_absences
msgid "Absences"
msgstr "Відсутності"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr ""
"Централізуйте документи ваших співробітників (контракти, розрахункові листи "
"тощо)"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_Cerification
msgid "Certifications"
msgstr "Сертифікації"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "Документ"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "Підрахунок документів"

#. module: documents_hr
#: model:documents.facet,name:documents_hr.documents_hr_documents
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "Документи"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "Налаштування документів відділу кадрів"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
msgid "Employee"
msgstr "Співробітник"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_employees
msgid "Employees Documents"
msgstr "Документи співробітників"

#. module: documents_hr
#: model:documents.folder,name:documents_hr.documents_hr_folder
msgid "HR"
msgstr "HR"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "Кадри"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "Users"
msgstr "Користувачі"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "Папка"

#. module: documents_hr
#: code:addons/documents_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You must set a private address on the Employee in order to use Document's "
"features."
msgstr ""
"Ви повинні встановити приватну адресу для працівника, щоб використовувати "
"функції документа."

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "Папка відділу кадрів"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "папка відділу кадрів за замовчуванням"
