# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# V<PERSON>huy, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Giá trị đặt ở đây áp dụng cho"
" công ty cụ thể. \" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Default tags\n"
"                                </span>"
msgstr ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Thẻ mặc định\n"
"                                </span>"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_absences
msgid "Absences"
msgstr "Vắng mặt"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr ""
"Tập trung các tài liệu của nhân viên của bạn (hợp đồng, phiếu lương, v.v.)"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_Cerification
msgid "Certifications"
msgstr "Chứng chỉ"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "Tài liệu"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "Số lượng tài liệu"

#. module: documents_hr
#: model:documents.facet,name:documents_hr.documents_hr_documents
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "Tài liệu"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "Thiết lập tài liệu HR"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
msgid "Employee"
msgstr "Nhân viên"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_employees
msgid "Employees Documents"
msgstr "Tài liệu nhân viên"

#. module: documents_hr
#: model:documents.folder,name:documents_hr.documents_hr_folder
msgid "HR"
msgstr "Nhân sự"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "Nguồn lực nhân sự"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "Users"
msgstr "Người dùng"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "Không gian làm việc"

#. module: documents_hr
#: code:addons/documents_hr/models/hr_employee.py:0
#, python-format
msgid "You must set an address on the employee to use Documents features."
msgstr "Bạn phải đặt địa chỉ cho nhân viên để sử dụng tính năng Tài liệu."

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "Không gian làm việc HR"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "không gian làm việc mặc định HR"
