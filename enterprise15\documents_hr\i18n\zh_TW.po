# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"此處設定的值是特定於每間公司的。\" aria-"
"label=\"此處設定的值是特定於每間公司的。\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid ""
"<span name=\"documents_hr_tags\" class=\"o_form_label\" invisible=\"1\" attrs=\"{'invisible' : [('documents_hr_settings', '=', False)]}\">\n"
"                                    Default tags\n"
"                                </span>"
msgstr ""

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_absences
msgid "Absences"
msgstr "缺勤"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Centralize your employees' documents (contracts, payslips, etc.)"
msgstr "集中員工的文件（合約、工資單等）"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_Cerification
msgid "Certifications"
msgstr "認證"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_config_settings
msgid "Config Settings"
msgstr "設定"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_ids
msgid "Document"
msgstr "文件"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_hr_employee__document_count
msgid "Document Count"
msgstr "文件數"

#. module: documents_hr
#: model:documents.facet,name:documents_hr.documents_hr_documents
#: model:ir.model.fields,field_description:documents_hr.field_res_users__document_count
#: model_terms:ir.ui.view,arch_db:documents_hr.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:documents_hr.res_users_view_form
msgid "Documents"
msgstr "文件"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_settings
msgid "Documents Hr Settings"
msgstr "文件人力資源設置"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_hr_employee
msgid "Employee"
msgstr "員工"

#. module: documents_hr
#: model:documents.tag,name:documents_hr.documents_hr_documents_employees
msgid "Employees Documents"
msgstr "員工文件"

#. module: documents_hr
#: model:documents.folder,name:documents_hr.documents_hr_folder
msgid "HR"
msgstr "人力資源"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_settings
msgid "Human Resources"
msgstr "人力資源"

#. module: documents_hr
#: model:ir.model,name:documents_hr.model_res_users
msgid "Users"
msgstr "使用者"

#. module: documents_hr
#: model_terms:ir.ui.view,arch_db:documents_hr.res_config_settings_view_form
msgid "Workspace"
msgstr "Workspace"

#. module: documents_hr
#: code:addons/documents_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You must set a private address on the Employee in order to use Document's "
"features."
msgstr ""

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_company__documents_hr_folder
msgid "hr Workspace"
msgstr "人力資源工作區"

#. module: documents_hr
#: model:ir.model.fields,field_description:documents_hr.field_res_config_settings__documents_hr_folder
msgid "hr default workspace"
msgstr "人力資源預設工作區"
