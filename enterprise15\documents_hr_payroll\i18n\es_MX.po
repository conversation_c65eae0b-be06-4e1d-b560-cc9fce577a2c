# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_payroll
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_hr_payslips_tags
msgid "Documents Hr Payslips Tags"
msgstr "Etiquetas de documentos de Recursos Humanos sobre recibos de nómina"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_payroll_folder_id
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_payroll_folder_id
msgid "Documents Payroll Folder"
msgstr "Carpeta de documentos de nómina"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "Recibo de nómina"

#. module: documents_hr_payroll
#: model:documents.folder,name:documents_hr_payroll.documents_payroll_folder
msgid "Payroll"
msgstr "Nómina"

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.res_config_settings_view_form
msgid "Payroll Workspace"
msgstr "Espacio de trabajo de nómina"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_hr_payslips_tags
msgid "Payslip"
msgstr "Recibo de nómina"

#. module: documents_hr_payroll
#: model:documents.tag,name:documents_hr_payroll.documents_hr_documents_payslips
msgid "Payslips"
msgstr "Recibos de nómina"
