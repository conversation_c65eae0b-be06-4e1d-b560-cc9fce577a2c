# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_payroll
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "設定"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_hr_payslips_tags
msgid "Documents Hr Payslips Tags"
msgstr "記錄人力資源工資單標籤"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_company__documents_payroll_folder_id
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_payroll_folder_id
msgid "Documents Payroll Folder"
msgstr "文件工資資料夾"

#. module: documents_hr_payroll
#: model:ir.model,name:documents_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "薪資單"

#. module: documents_hr_payroll
#: model:documents.folder,name:documents_hr_payroll.documents_payroll_folder
msgid "Payroll"
msgstr "薪酬管理"

#. module: documents_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_hr_payroll.res_config_settings_view_form
msgid "Payroll Workspace"
msgstr "工資發放工作區"

#. module: documents_hr_payroll
#: model:ir.model.fields,field_description:documents_hr_payroll.field_res_config_settings__documents_hr_payslips_tags
msgid "Payslip"
msgstr "薪資單"

#. module: documents_hr_payroll
#: model:documents.tag,name:documents_hr_payroll.documents_hr_documents_payslips
msgid "Payslips"
msgstr "薪資單"
