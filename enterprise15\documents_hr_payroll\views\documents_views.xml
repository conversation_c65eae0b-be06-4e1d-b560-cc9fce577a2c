<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.documents</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="70"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='documents_hr_folder']" position="after">
                    <div class="content-group" attrs="{'invisible' : [('documents_hr_settings', '=', False)]}">
                        <div class="row mt16">
                            <label class="o_form_label col-lg-3" for="documents_payroll_folder_id" string="Payroll Workspace"/>
                            <field name="documents_payroll_folder_id"/>
                        </div>
                    </div>
                </xpath>
                <xpath expr="//span[@name='documents_hr_tags']" position="after">
                    <div class="row" attrs="{'invisible' : [('documents_hr_settings', '=', False)]}">
                        <label for="documents_hr_payslips_tags" class="col-sm-3 o_light_label"/>
                        <field name="documents_hr_payslips_tags"
                                domain="[('folder_id','=', documents_hr_folder)]"
                                widget="many2many_tags"/>
                    </div>
                </xpath>
            </field>
    </record>

</odoo>
