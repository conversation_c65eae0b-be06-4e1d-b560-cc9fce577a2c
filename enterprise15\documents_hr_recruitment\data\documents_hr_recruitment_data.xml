<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="documents_recruitment_folder" model="documents.folder" forcecreate="0">
            <field name="name">Recruitment</field>
            <field name="group_ids" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
            <field name="sequence">20</field>
        </record>

        <record id="base.main_company" model="res.company">
            <field name="recruitment_folder_id" ref="documents_recruitment_folder"/>
        </record>

        <record id="documents_recruitment_documents_facet" model="documents.facet" forcecreate="0">
            <field name="name">Status</field>
            <field name="sequence">5</field>
            <field name="folder_id" ref="documents_recruitment_folder"/>
        </record>

        <record id="documents_recruitment_new_tag" model="documents.tag" forcecreate="0">
            <field name="name">Inbox</field>
            <field name="facet_id" ref="documents_recruitment_documents_facet"/>
            <field name="sequence">10</field>
        </record>

        <record id="documents_recruitment_plans_tag" model="documents.tag" forcecreate="0">
            <field name="name">Recruitment Reserve</field>
            <field name="facet_id" ref="documents_recruitment_documents_facet"/>
            <field name="sequence">15</field>
        </record>

        <record id="documents_recruitment_sheet_tag" model="documents.tag" forcecreate="0">
            <field name="name">Cancelled</field>
            <field name="facet_id" ref="documents_recruitment_documents_facet"/>
            <field name="sequence">20</field>
        </record>

        <!-- Workflow Rules -->
        <record id="documents_applicant_rule" model="documents.workflow.rule" forcecreate="0">
            <field name="sequence">20</field>
            <field name="name">Create an Applicant</field>
            <field name="create_model">hr.applicant</field>
            <field name="domain_folder_id" ref="documents_recruitment_folder"/>
        </record>

    </data>
</odoo>
