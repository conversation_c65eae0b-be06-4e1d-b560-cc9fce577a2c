# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_recruitment
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"القيم المحددة هنا تنطبق على "
"المؤسسة الحالية فقط. \" aria-label=\"Values set here are company-specific.\""
" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"مجموعة من الشروط والإجراءات التي سوف تكون متُاحة لكافة المرفقات التي تستوفي "
"الشروط "

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_applicant
#: model:ir.model.fields.selection,name:documents_hr_recruitment.selection__documents_workflow_rule__create_model__hr_applicant
msgid "Applicant"
msgstr "المتقدم للوظيفة "

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_sheet_tag
msgid "Cancelled"
msgstr "ملغي"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Centralize files attached to applications and job positions"
msgstr "قم بمركزة الملفات المرفقة بالتقديمات ومناصب العمل "

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "إنشاء"

#. module: documents_hr_recruitment
#: model:documents.workflow.rule,name:documents_hr_recruitment.documents_applicant_rule
msgid "Create an Applicant"
msgstr "إنشاء ملف متقدم للعمل "

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Default Tags"
msgstr "علامات التصنيف الافتراضية "

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__documents_recruitment_settings
msgid "Documents Recruitment Settings"
msgstr "إعدادات مستندات التوظيف "

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_new_tag
msgid "Inbox"
msgstr "صندوق الوارد"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: documents_hr_recruitment
#: model:documents.folder,name:documents_hr_recruitment.documents_recruitment_folder
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__documents_recruitment_settings
msgid "Recruitment"
msgstr "التوظيف"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_plans_tag
msgid "Recruitment Reserve"
msgstr "طلبات المتقدمين المعلقة "

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_tag_ids
msgid "Recruitment Tag"
msgstr "علامة تصنيف التوظيف "

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_tag_ids
msgid "Recruitment Tags"
msgstr "علامات تصنيف التوظيف "

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_folder_id
msgid "Recruitment Workspace"
msgstr "مساحة عمل التوظيف "

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_folder_id
msgid "Recruitment default workspace"
msgstr "مساحة العمل الافتراضية للتوظيف "

#. module: documents_hr_recruitment
#: model:documents.facet,name:documents_hr_recruitment.documents_recruitment_documents_facet
msgid "Status"
msgstr "الحالة"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Workspace"
msgstr "مساحة العمل"
