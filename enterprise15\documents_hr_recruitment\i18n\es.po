# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_recruitment
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Un conjunto de condiciones y acciones estarán disponibles para todos los "
"archivos adjuntos que coincidan con las condiciones"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_applicant
#: model:ir.model.fields.selection,name:documents_hr_recruitment.selection__documents_workflow_rule__create_model__hr_applicant
msgid "Applicant"
msgstr "Candidato"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_sheet_tag
msgid "Cancelled"
msgstr "Cancelado"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Centralize files attached to applications and job positions"
msgstr "Centraliza archivos adjuntos a solicitudes y puestos de trabajo"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Crear"

#. module: documents_hr_recruitment
#: model:documents.workflow.rule,name:documents_hr_recruitment.documents_applicant_rule
msgid "Create an Applicant"
msgstr "Crear un candidato"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Default Tags"
msgstr "Etiquetas predeterminadas"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__documents_recruitment_settings
msgid "Documents Recruitment Settings"
msgstr "Ajustes de documentos para contratación"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_new_tag
msgid "Inbox"
msgstr "Bandeja de entrada"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: documents_hr_recruitment
#: model:documents.folder,name:documents_hr_recruitment.documents_recruitment_folder
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__documents_recruitment_settings
msgid "Recruitment"
msgstr "Proceso de selección"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_plans_tag
msgid "Recruitment Reserve"
msgstr "Reserva de contratación"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_tag_ids
msgid "Recruitment Tag"
msgstr "Etiqueta de reclutamiento"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_tag_ids
msgid "Recruitment Tags"
msgstr "Etiquetas de reclutamiento"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_folder_id
msgid "Recruitment Workspace"
msgstr "Espacio de trabajo de reclutamiento"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_folder_id
msgid "Recruitment default workspace"
msgstr "Espacio de trabajo de reclutamiento predeterminado"

#. module: documents_hr_recruitment
#: model:documents.facet,name:documents_hr_recruitment.documents_recruitment_documents_facet
msgid "Status"
msgstr "Estado"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Workspace"
msgstr "Espacio de trabajo"
