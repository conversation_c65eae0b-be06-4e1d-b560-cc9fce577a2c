# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_recruitment
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Ram<PERSON><PERSON> ViaLaurea <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Sąlygų ir veiksmų, kurie bus prieinami visiems sąlygas atitinkantiems "
"priedams, rinkinys"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_applicant
#: model:ir.model.fields.selection,name:documents_hr_recruitment.selection__documents_workflow_rule__create_model__hr_applicant
msgid "Applicant"
msgstr "Kandidatas"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_sheet_tag
msgid "Cancelled"
msgstr "Atšauktas"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Centralize files attached to applications and job positions"
msgstr "Centralizuoti prie paraiškų ir darbo pozicijų pridėtus failus"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_company
msgid "Companies"
msgstr "Įmonės"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Sukurti"

#. module: documents_hr_recruitment
#: model:documents.workflow.rule,name:documents_hr_recruitment.documents_applicant_rule
msgid "Create an Applicant"
msgstr ""

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Default Tags"
msgstr "Žymos pagal nutylėjimą"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__documents_recruitment_settings
msgid "Documents Recruitment Settings"
msgstr ""

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_new_tag
msgid "Inbox"
msgstr "Gauti"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Pareigos"

#. module: documents_hr_recruitment
#: model:documents.folder,name:documents_hr_recruitment.documents_recruitment_folder
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__documents_recruitment_settings
msgid "Recruitment"
msgstr "Įdarbinimas"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_plans_tag
msgid "Recruitment Reserve"
msgstr ""

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_tag_ids
msgid "Recruitment Tag"
msgstr ""

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_tag_ids
msgid "Recruitment Tags"
msgstr ""

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_folder_id
msgid "Recruitment Workspace"
msgstr ""

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_folder_id
msgid "Recruitment default workspace"
msgstr ""

#. module: documents_hr_recruitment
#: model:documents.facet,name:documents_hr_recruitment.documents_recruitment_documents_facet
msgid "Status"
msgstr "Būsena"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Workspace"
msgstr "Darbo erdvė"
