# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_hr_recruitment
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" aria-label=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Een set van condities en acties welke beschikbaar is voor alle bijlagen die "
"aan deze condities voldoen"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_applicant
#: model:ir.model.fields.selection,name:documents_hr_recruitment.selection__documents_workflow_rule__create_model__hr_applicant
msgid "Applicant"
msgstr "Kandidaat"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_sheet_tag
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Centralize files attached to applications and job positions"
msgstr ""
"Centraliseer bestanden die aan sollicitaties en vacatures zijn gekoppeld."

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Aanmaken"

#. module: documents_hr_recruitment
#: model:documents.workflow.rule,name:documents_hr_recruitment.documents_applicant_rule
msgid "Create an Applicant"
msgstr "Maak een sollicitant."

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Default Tags"
msgstr "Standaard labels"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__documents_recruitment_settings
msgid "Documents Recruitment Settings"
msgstr "Documenten recruitment instellingen"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_new_tag
msgid "Inbox"
msgstr "Postvak in"

#. module: documents_hr_recruitment
#: model:ir.model,name:documents_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Functie"

#. module: documents_hr_recruitment
#: model:documents.folder,name:documents_hr_recruitment.documents_recruitment_folder
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__documents_recruitment_settings
msgid "Recruitment"
msgstr "Werving & Selectie"

#. module: documents_hr_recruitment
#: model:documents.tag,name:documents_hr_recruitment.documents_recruitment_plans_tag
msgid "Recruitment Reserve"
msgstr "Recruitment reserve"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_tag_ids
msgid "Recruitment Tag"
msgstr "Werving label"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_tag_ids
msgid "Recruitment Tags"
msgstr "Werving labels"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_company__recruitment_folder_id
msgid "Recruitment Workspace"
msgstr "Werving & Selectie werkruimte"

#. module: documents_hr_recruitment
#: model:ir.model.fields,field_description:documents_hr_recruitment.field_res_config_settings__recruitment_folder_id
msgid "Recruitment default workspace"
msgstr "Werving & Selectie standaard werkruimte"

#. module: documents_hr_recruitment
#: model:documents.facet,name:documents_hr_recruitment.documents_recruitment_documents_facet
msgid "Status"
msgstr "Status"

#. module: documents_hr_recruitment
#: model_terms:ir.ui.view,arch_db:documents_hr_recruitment.res_config_settings_view_form
msgid "Workspace"
msgstr "Werkruimte"
