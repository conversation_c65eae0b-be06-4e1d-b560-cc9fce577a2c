<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_individual_account" model="mail.template">
            <field name="name">Payroll: Individual Account</field>
            <field name="model_id" ref="hr_payroll.model_hr_employee"/>
            <field name="subject">{{ object.name }}, your individual account is available for you</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="partner_to">{{ object.address_home_id.id }}</field>
            <field name="body_html" type="html">
    <table border="0" cellpadding="0" cellspacing="0" style="width:100%; margin:0px auto;"><tbody>
        <tr><td valign="top" style="text-align: left; font-size: 14px;">
            Dear <t t-esc="object.name"></t>, your individual account is available for you.<br/><br/>
            Please find the PDF in your employee portal.<br/><br/>
            Have a nice day,<br/>
            The HR Team
        </td></tr>
    </tbody></table>
                </field>
                <field name="lang">{{ object.address_home_id.lang }}</field>
                <field name="auto_delete" eval="True"/>
          </record>

        <record id="mail_template_281_10" model="mail.template">
            <field name="name">Payroll: 281.10 Declaration</field>
            <field name="model_id" ref="hr_payroll.model_hr_employee"/>
            <field name="subject">{{ object.name }}, your 281.10 declaration is available for you</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="partner_to">{{ object.address_home_id.id }}</field>
            <field name="body_html" type="html">
    <table border="0" cellpadding="0" cellspacing="0" style="width:100%; margin:0px auto;"><tbody>
        <tr><td valign="top" style="text-align: left; font-size: 14px;">
            Dear <t t-esc="object.name"></t>, your 281.10 declaration is available for you.<br/><br/>
            Please find the PDF in your employee portal.<br/><br/>
            Have a nice day,<br/>
            The HR Team
        </td></tr>
    </tbody></table>
                </field>
                <field name="lang">{{ object.address_home_id.lang }}</field>
                <field name="auto_delete" eval="True"/>
          </record>

        <record id="mail_template_281_45" model="mail.template">
            <field name="name">Payroll: 281.45 Declaration</field>
            <field name="model_id" ref="hr_payroll.model_hr_employee"/>
            <field name="subject">{{ object.name }}, your 281.45 declaration is available for you</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="partner_to">{{ object.address_home_id.id }}</field>
            <field name="body_html" type="html">
    <table border="0" cellpadding="0" cellspacing="0" style="width:100%; margin:0px auto;"><tbody>
        <tr><td valign="top" style="text-align: left; font-size: 14px;">
            Dear <t t-esc="object.name"></t>, your 281.45 declaration is available for you.<br/><br/>
            Please find the PDF in your employee portal.<br/><br/>
            Have a nice day,<br/>
            The HR Team
        </td></tr>
    </tbody></table>
                </field>
                <field name="lang">{{ object.address_home_id.lang }}</field>
                <field name="auto_delete" eval="True"/>
          </record>
    </data>
</odoo>
