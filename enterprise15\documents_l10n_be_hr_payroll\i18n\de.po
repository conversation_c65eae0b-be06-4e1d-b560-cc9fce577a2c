# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_l10n_be_hr_payroll
# 
# Translators:
# <PERSON>ied<PERSON><PERSON>ling-Nesselbosch, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-08 11:15+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.10 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""
"<strong>Warnung: </strong> Um die 281.10 Bögen in das Mitarbeiterportal "
"einstellen zu können, müssen Sie in der Konfiguration der Dokumenten-App die"
" Option \"Personalwesen\" aktivieren."

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.45 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""
"<strong>Warnung: </strong> Um die 281.45 Bögen in das Mitarbeiterportal "
"einstellen zu können, müssen Sie in der Konfiguration der Dokumenten-App die"
" Option \"Personalwesen\" aktivieren."

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_10
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.10 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Hallo <t t-esc=\"object.name\"/>, Ihre 281.10 Erklärung ist für Sie verfügbar.<br/><br/>\n"
"            Das PDF finden Sie in Ihrem Mitarbeiterportal.<br/><br/>\n"
"            Wir wünschen Ihnen einen schönen Tag,<br/>\n"
"            Das HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_45
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.45 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Hallo <t t-esc=\"object.name\"/>, unsere 281.45 Erklärung ist für Sie verfügbar you.<br/><br/>\n"
"            Das PDF finden Sie in Ihrem Mitarbeiterportal.<br/><br/>\n"
"            Wir wünschen Ihnen einen schönen Tag,<br/>\n"
"            Das HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your individual account is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Hallo <t t-esc=\"object.name\"/>, Ihr individuelles Konto ist für Sie verfügbar.<br/><br/>\n"
"            Das PDF finden Sie in Ihrem Mitarbeiterportal.<br/><br/>\n"
"            Wir wünschen Ihnen einen schönen Tag,<br/>\n"
"            Das HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgien: Sozialbilanz"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgien: Sozialversicherungsausweis"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_individual_account_wizard
msgid "HR Individual Account Report By Employee"
msgstr "HR-Einzelkontobericht nach Mitarbeiter"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "HR-Lohnabrechnung 281.10 Assistent"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "HR-Lohnabrechnung 281.45 Assistent"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "Individual Accounts PDF"
msgstr "Einzelkonto PDF"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "Payroll: 281.10 Declaration"
msgstr "Lohnbuchhaltung: 281.10 Erklärung"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "Payroll: 281.45 Declaration"
msgstr "Lohnbuchhaltung: 281.45 Erklärung"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "Payroll: Individual Account"
msgstr "Lohnbuchhaltung: Individuelles Konto"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Post in Documents"
msgstr "In Dokumente buchen"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "The individual account sheets have been posted in the employee portal."
msgstr ""
"Die einzelnen Kontoblätter wurden in das Mitarbeiterportal eingestellt."

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "There is no individual account to post for this period."
msgstr ""
"Für diesen Zeitraum gibt es kein individuelles Konto, das zu buchen wäre."

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"This will add all the sheets in the employee portal. Are you sure you want "
"to proceed ?"
msgstr ""
"Dadurch werden alle Blätter im Mitarbeiterportal hinzugefügt. Sind Sie "
"sicher, dass Sie fortfahren möchten?"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "{{ object.name }}, your 281.10 declaration is available for you"
msgstr "{{ object.name }}, Ihre 281.10 Erklärung ist für Sie verfügbar"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "{{ object.name }}, your 281.45 declaration is available for you"
msgstr "{{ object.name }}, Ihre 281.45-Erklärung ist für Sie verfügbar"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "{{ object.name }}, your individual account is available for you"
msgstr "{{ object.name }}, Ihr individuelles Konto ist für Sie verfügbar"
