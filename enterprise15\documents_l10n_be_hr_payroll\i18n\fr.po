# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_l10n_be_hr_payroll
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-08 11:15+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.10 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""
"<strong>Attention :</strong> Afin de publier les fiches 281.10 dans le "
"portail des employés, vous devez Activer \"Ressources Humaines\" dans la "
"configuration de l'application \"Document\"."

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.45 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""
"<strong>Attention : </strong>Afin d'afficher les fiches 281.45 dans le "
"portail des employés, vous devez Activer \"Ressources Humaines\" dans la "
"configuration de l'application \"Document\"."

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_10
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.10 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Bonjour <t t-esc=\"object.name\"/>, votre fiche 281.10 est disponible.<br/><br/>\n"
"            Vous trouverez le PDF dans votre portail employé.<br/><br/>\n"
"            Belle journée,<br/>\n"
"            L'équipe RH\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_45
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.45 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Bonjour <t t-esc=\"object.name\"/>, votre fiche 281.45 est disponible.<br/><br/>\n"
"            Vous trouverez le PDF dans votre portail employé.<br/><br/>\n"
"            Belle journée,<br/>\n"
"            L'équipe RH\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your individual account is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Bonjour <t t-esc=\"object.name\"/>, votre compte individuel est disponible.<br/><br/>\n"
"            Vous trouverez le PDF dans votre portail employé.<br/><br/>\n"
"            Belle journée,<br/>\n"
"            L'équipe RH\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgique : Bilan social"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgique : Attestation de sécurité sociale"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_individual_account_wizard
msgid "HR Individual Account Report By Employee"
msgstr "Décompte individuel par employé"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "Assistant pour 281.10"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "Assistant pour 281.45"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "Individual Accounts PDF"
msgstr "Comptes Individuels PDF"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "Payroll: 281.10 Declaration"
msgstr "Paie : Fiche 281.10"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "Payroll: 281.45 Declaration"
msgstr "Paie : Fiche 281.45"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "Payroll: Individual Account"
msgstr "Paie : Compte individuel"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Post in Documents"
msgstr "Publier dans Documents"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "The individual account sheets have been posted in the employee portal."
msgstr ""
"Les fiches des comptes individuels ont été mises en ligne sur le portail "
"employé."

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "There is no individual account to post for this period."
msgstr "Il n'y a pas de compte individuel à publier pour cette période."

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"This will add all the sheets in the employee portal. Are you sure you want "
"to proceed ?"
msgstr ""
"Cela ajoute toutes les feuilles dans le portail des employés. Êtes-vous sûr "
"de vouloir continuer ?"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "{{ object.name }}, your 281.10 declaration is available for you"
msgstr "{{ object.name }}, votre fiche 281.10 est disponible"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "{{ object.name }}, your 281.45 declaration is available for you"
msgstr "{{ object.name }}, votre fiche 281.45 est disponible"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "{{ object.name }}, your individual account is available for you"
msgstr "{{ object.name }}, votre compte individuel est disponible"
