# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_l10n_be_hr_payroll
# 
# Translators:
# Friederike Fasterling-Nesselbosch, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-08 11:15+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: Friederike Fasterling-Nesselbosch, 2022\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.10 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""
"<strong>Attenzione</strong>: Per pubblicare i fogli 281.10 nel portale dei "
"dipendenti, devi attivare \" Risorse Umane\" nella configurazione dell'app "
"\" Documenti\"."

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.45 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""
"<strong>Attenzione</strong>: Per pubblicare i fogli 281.45 nel portale dei "
"dipendenti, devi attivare \" Risorse Umane\" nella configurazione dell'app "
"\" Documenti\"."

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_10
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.10 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Ciao <t t-esc=\"object.name\"/>, La tua dichiarazione 281.10 è disponibile per te.<br/><br/>\n"
"Trovi il PDF nel tuo portale dei dipendenti.\n"
"<br/>\n"
"            Buona giornata,<br/>\n"
"            Il Team HR\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_45
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.45 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Ciao <t t-esc=\"object.name\"/>, La tua dichiarazione 281.45 è disponibile per te.<br/><br/>\n"
"Trovi il PDF nel tuo portale dei dipendenti.\n"
"<br/>\n"
"            Buona giornata,<br/>\n"
"            Il Team HR\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your individual account is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Ciao <t t-esc=\"object.name\"/>, Il tuo conto individuale è disponibile per te.\n"
"            Trova il PDF nel tuo portale dei dipendenti.<br/><br/>\n"
"            Buona giornata,<br/>\n"
"            Il Team HR\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgio: Bilancio sociale"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgio: Certificato di sicurezza sociale"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_individual_account_wizard
msgid "HR Individual Account Report By Employee"
msgstr "Rapporto del conto individuale HR per dipendente"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "HR Payroll 281.10 Wizard"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "HR Payroll 281.45 Wizard"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "Individual Accounts PDF"
msgstr "Conti individuali PDF"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "Payroll: 281.10 Declaration"
msgstr "Libro paga: Dichiarazione 281.10"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "Payroll: 281.45 Declaration"
msgstr "Libro paga: Dichiarazione 281.45"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "Payroll: Individual Account"
msgstr "Libro paga: Conto individuale"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Post in Documents"
msgstr "Registrare in Documenti"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "The individual account sheets have been posted in the employee portal."
msgstr ""
"Le schede dei conti individuali sono state pubblicate nel portale dei "
"dipendenti."

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "There is no individual account to post for this period."
msgstr "Non c'è un conto individuale da registrare per questo periodo."

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"This will add all the sheets in the employee portal. Are you sure you want "
"to proceed ?"
msgstr ""
"Questo aggiungerà tutti i fogli nel portale dei dipendenti. Sei sicuro di "
"voler procedere?"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "{{ object.name }}, your 281.10 declaration is available for you"
msgstr "{{ object.name }}, la tua dichiarazione 281.10 è disponibile per te"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "{{ object.name }}, your 281.45 declaration is available for you"
msgstr "{{ object.name }}, la tua dichiarazione 281.45 è disponibile per te"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "{{ object.name }}, your individual account is available for you"
msgstr "{{ object.name }}, il tuo account individuale è disponibile per te"
