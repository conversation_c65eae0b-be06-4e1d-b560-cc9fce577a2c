# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_l10n_be_hr_payroll
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-08 11:15+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.10 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"<strong>Warning: </strong>In order to post the 281.45 sheets in the employee"
" portal, you have to Enable \"Human Resources\" in the configuration of the "
"app \"Document\"."
msgstr ""

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_10
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.10 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Beste <t t-esc=\"object.name\"/>, je fiche 281.10 is beschikbaar voor je.<br/><br/>\n"
"            Je kan de PDF vinden in het werknemersportaal.<br/><br/>\n"
"            Prettige dag,<br/>\n"
"            Het HR-team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_281_45
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your 281.45 declaration is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Beste <t t-esc=\"object.name\"/>, je fiche 281.45 is voor je beschikbaar.<br/><br/>\n"
"            Je kan de PDF vinden in het werknemersportaal.<br/><br/>\n"
"            Prettige dag,<br/>\n"
"            Het HR-team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,body_html:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Dear <t t-esc=\"object.name\"/>, your individual account is available for you.<br/><br/>\n"
"            Please find the PDF in your employee portal.<br/><br/>\n"
"            Have a nice day,<br/>\n"
"            The HR Team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"        <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"            Beste <t t-esc=\"object.name\"/>, je individuele rekening is beschikbaar.<br/><br/>\n"
"            Je kan de PDF vinden in je werknemersportaal.<br/><br/>\n"
"            Prettige dag,<br/>\n"
"            Het HR-team\n"
"        </td></tr>\n"
"    </tbody></table>\n"
"                "

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "België: Sociale balans"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "België: Certificaat Sociale Zekerheid"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_individual_account_wizard
msgid "HR Individual Account Report By Employee"
msgstr "HR Individueel Accountrapport per werknemer"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "HR Payroll 281.10 Wizard"

#. module: documents_l10n_be_hr_payroll
#: model:ir.model,name:documents_l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "HR Payroll 281.45 Wizard"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "Individual Accounts PDF"
msgstr ""

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "Payroll: 281.10 Declaration"
msgstr "Lonen: Fiche 281.20"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "Payroll: 281.45 Declaration"
msgstr "Lonen: Fiche 281.45"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,name:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "Payroll: Individual Account"
msgstr "Lonen: Individuele rekening"

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Post in Documents"
msgstr "In documenten boeken"

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "The individual account sheets have been posted in the employee portal."
msgstr ""

#. module: documents_l10n_be_hr_payroll
#: code:addons/documents_l10n_be_hr_payroll/wizard/hr_individual_account_reports.py:0
#, python-format
msgid "There is no individual account to post for this period."
msgstr ""

#. module: documents_l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:documents_l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid ""
"This will add all the sheets in the employee portal. Are you sure you want "
"to proceed ?"
msgstr ""
"Hierdoor worden alle fiches in het werknemersportaal toegevoegd. Weet je "
"zeker dat je door wilt gaan?"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_10
msgid "{{ object.name }}, your 281.10 declaration is available for you"
msgstr "{{ object.name }}, je fiche 281.10 is beschikbaar"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_281_45
msgid "{{ object.name }}, your 281.45 declaration is available for you"
msgstr "{{ object.name }}, je fiche 281.45 is beschikbaar"

#. module: documents_l10n_be_hr_payroll
#: model:mail.template,subject:documents_l10n_be_hr_payroll.mail_template_individual_account
msgid "{{ object.name }}, your individual account is available for you"
msgstr "{{ object.name }}, je individuele rekening is beschikbaar"
