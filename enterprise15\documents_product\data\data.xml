<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">


        <record id="documents_product_folder" model="documents.folder" forcecreate="0">
            <field name="name">Products</field>
            <field name="sequence">14</field>
        </record>

        <record id="base.main_company" model="res.company">
            <field name="product_folder" ref="documents_product_folder"/>
        </record>

        <record id="documents_product_documents_facet" model="documents.facet" forcecreate="0">
            <field name="name">Documents</field>
            <field name="sequence">5</field>
            <field name="folder_id" ref="documents_product_folder"/>
        </record>

        <record id="documents_product_new_tag" model="documents.tag" forcecreate="0">
            <field name="name">New</field>
            <field name="facet_id" ref="documents_product_documents_facet"/>
            <field name="sequence">6</field>
        </record>

        <record id="documents_product_plans_tag" model="documents.tag" forcecreate="0">
            <field name="name">Plans</field>
            <field name="facet_id" ref="documents_product_documents_facet"/>
            <field name="sequence">6</field>
        </record>

        <record id="documents_product_sheet_tag" model="documents.tag" forcecreate="0">
            <field name="name">DataSheets</field>
            <field name="facet_id" ref="documents_product_documents_facet"/>
            <field name="sequence">7</field>
        </record>

        <record id="documents_product_specs_tag" model="documents.tag" forcecreate="0">
            <field name="name">Specs</field>
            <field name="facet_id" ref="documents_product_documents_facet"/>
            <field name="sequence">8</field>
        </record>

        <record id="documents_product_msds_tag" model="documents.tag" forcecreate="0">
            <field name="name">MSDS</field>
            <field name="facet_id" ref="documents_product_documents_facet"/>
            <field name="sequence">9</field>
        </record>

    </data>
</odoo>
