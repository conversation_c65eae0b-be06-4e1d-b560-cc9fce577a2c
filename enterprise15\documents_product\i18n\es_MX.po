# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí"
" son específicos de la empresa.\" aria-label=\"Los valores establecidos aquí"
" son específicos de la empresa.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Un conjunto de condiciones y acciones que estarán disponibles para todos los"
" archivos adjuntos que coincidan con las condiciones"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr "Centralizar los archivos adjuntos a los productos"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Crear"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "DataSheets"
msgstr "Hojas de datos"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr "Etiquetas predeterminadas"

#. module: documents_product
#: model:documents.facet,name:documents_product.documents_product_documents_facet
msgid "Documents"
msgstr "Documentos"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr "Ajustes de documentos para productos"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "MSDS"
msgstr "Ficha de datos de seguridad"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "New"
msgstr "Nuevo"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Plans"
msgstr "Planos"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "Producto"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr "Etiquetas de producto"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
msgid "Product Workspace"
msgstr "Espacio de trabajo para productos"

#. module: documents_product
#: model:ir.model.fields.selection,name:documents_product.selection__documents_workflow_rule__create_model__product_template
msgid "Product template"
msgstr "Plantilla de producto"

#. module: documents_product
#: model:documents.folder,name:documents_product.documents_product_folder
msgid "Products"
msgstr "Productos"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Specs"
msgstr "Especificaciones"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr "Espacio de trabajo"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default workspace"
msgstr "espacio de trabajo predeterminado del producto"
