# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" aria-label=\"Waardes die hier ingesteld "
"staan zijn bedrijfsspecifiek.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Een set van condities en acties welke beschikbaar is voor alle bijlagen die "
"aan deze condities voldoen"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr "Centraliseer bestanden gekoppeld aan producten"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Aanmaken"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "DataSheets"
msgstr "DataSheets"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr "Standaard labels"

#. module: documents_product
#: model:documents.facet,name:documents_product.documents_product_documents_facet
msgid "Documents"
msgstr "Documenten"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr "Product documenten instellingen"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "MSDS"
msgstr "MSDS"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "New"
msgstr "Nieuw"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Plans"
msgstr "Plannen"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "Product"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr "Productlabels"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
msgid "Product Template"
msgstr "Productsjabloon"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
msgid "Product Workspace"
msgstr "Product werkruimte"

#. module: documents_product
#: model:ir.model.fields.selection,name:documents_product.selection__documents_workflow_rule__create_model__product_template
msgid "Product template"
msgstr "Productsjabloon"

#. module: documents_product
#: model:documents.folder,name:documents_product.documents_product_folder
msgid "Products"
msgstr "Producten"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Specs"
msgstr "Specificaties"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr "Werkruimte"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default workspace"
msgstr "product standaard werkruimte"
