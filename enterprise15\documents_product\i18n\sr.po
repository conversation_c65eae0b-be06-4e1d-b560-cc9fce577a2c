# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * documents_product
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Kreiraj"

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Credit note"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__dms_product_settings
msgid "Dms Product Settings"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_ir_attachment
msgid "Document"
msgstr "Dokument"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__has_business_option
msgid "Has Business Option"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__dms_product_settings
msgid "Product Folder"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Product template"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Select the folder to be used for product's documents"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Select the tags to be applied to product's new documents"
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Signature template"
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Task"
msgstr ""

#. module: documents_product
#: selection:documents.workflow.rule,create_model:0
msgid "Vendor bill"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default folder"
msgstr ""
