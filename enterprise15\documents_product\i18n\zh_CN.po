# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Jeffery CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"此值仅针对当前公司。\" aria-"
"label=\"此值仅针对当前公司。\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr "一组适用于符合所有条件附件的状态动作集合"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr "集中附加到产品的文件"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "创建"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "DataSheets"
msgstr "数据表"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr "默认标记"

#. module: documents_product
#: model:documents.facet,name:documents_product.documents_product_documents_facet
msgid "Documents"
msgstr "文件"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr "文件产品设置"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "MSDS"
msgstr "MSDS"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "New"
msgstr "新建"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Plans"
msgstr "计划"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "产品"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr "产品标签"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
msgid "Product Template"
msgstr "产品模板"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
msgid "Product Workspace"
msgstr "产品工作区"

#. module: documents_product
#: model:ir.model.fields.selection,name:documents_product.selection__documents_workflow_rule__create_model__product_template
msgid "Product template"
msgstr "产品模板"

#. module: documents_product
#: model:documents.folder,name:documents_product.documents_product_folder
msgid "Products"
msgstr "产品"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Specs"
msgstr "规格"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr "工作区"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default workspace"
msgstr "产品默认工作区"
