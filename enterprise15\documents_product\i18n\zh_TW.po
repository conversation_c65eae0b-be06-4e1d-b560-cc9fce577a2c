# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# se<PERSON> huang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<PERSON>.<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"此處設定的值是特定於每間公司的。\" aria-"
"label=\"此處設定的值是特定於每間公司的。\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr "一組條件和操作，可用於與條件匹配的所有附件"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr "集中附加到產品的文件"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "公司"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr "設定"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "建立"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "DataSheets"
msgstr "數據表"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr "默認標籤"

#. module: documents_product
#: model:documents.facet,name:documents_product.documents_product_documents_facet
msgid "Documents"
msgstr "文件"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr "文件產品設置"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "MSDS"
msgstr "MSDS"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "New"
msgstr "新增"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Plans"
msgstr "計劃"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "商品"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tags
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tags
msgid "Product Tags"
msgstr "產品標籤"

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
msgid "Product Template"
msgstr "產品模板"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder
msgid "Product Workspace"
msgstr "產品工作區"

#. module: documents_product
#: model:ir.model.fields.selection,name:documents_product.selection__documents_workflow_rule__create_model__product_template
msgid "Product template"
msgstr "產品模板"

#. module: documents_product
#: model:documents.folder,name:documents_product.documents_product_folder
msgid "Products"
msgstr "產品"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Specs"
msgstr "規格"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr "Workspace"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder
msgid "product default workspace"
msgstr "產品預設工作區"
