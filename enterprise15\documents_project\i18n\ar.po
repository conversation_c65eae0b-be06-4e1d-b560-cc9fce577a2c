# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"القيم المحددة هنا تنطبق على "
"المؤسسة الحالية فقط. \" aria-label=\"Values set here are company-specific.\""
" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"مجموعة الشروط والإجراءات التي سوف تكون متُاحة لكافة المرفقات التي تستوفي "
"الشروط "

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Centralize files attached to projects and tasks"
msgstr "قم بمركزة الملفات المرفقة بالمشاريع والمهام "

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "إنشاء"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "إنشاء مهمة"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Default Tags"
msgstr "علامات التصنيف الافتراضية "

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__documents_project_settings
msgid "Documents Project Settings"
msgstr "إعدادات مشاريع المستندات "

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__documents_project_settings
msgid "Project"
msgstr "المشروع"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_tags
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_tags
msgid "Project Tags"
msgstr "علامات تصنيف المشاريع "

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_folder
msgid "Project Workspace"
msgstr "مساحة عمل المشروع"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "المهمة"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Workspace"
msgstr "مساحة العمل"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_folder
msgid "project default workspace"
msgstr "مساحة العمل الافتراضية للمشروع "
