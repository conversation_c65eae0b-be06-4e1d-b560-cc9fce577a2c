# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2022\n"
"Language-Team: Azerbaijani (https://www.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Burada qeyd olunan dəyərlər "
"şirkətə məxsusdur.\" aria-label=\"Burada qeyd olunan dəyərlər şirkətə "
"məxsusdur.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr ""
"Bu şərtlərə uyğun olan bütün əlavələr üçün mövcud olacaq şərt və "
"əməliyyatlar dəsti"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Centralize files attached to projects and tasks"
msgstr "Layihələrə və tapşırıqlara əlavə olunmuş faylları mərkəzləşdirin"

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "Yaradın"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "Tapşırıq yarat"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Default Tags"
msgstr "Susmaya Görə Teqlər"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__documents_project_settings
msgid "Documents Project Settings"
msgstr "Layihə Parametrləri Sənədləri"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__documents_project_settings
msgid "Project"
msgstr "Layihə"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_tags
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_tags
msgid "Project Tags"
msgstr "Layuihə İşarələri"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_folder
msgid "Project Workspace"
msgstr "Layihənin İş Sahəsi"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "Tapşırıq"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Workspace"
msgstr "İş sahəsi "

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_folder
msgid "project default workspace"
msgstr "layihənin susmaya görə iş sahəsi"
