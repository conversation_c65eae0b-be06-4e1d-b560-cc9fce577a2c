# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_workflow_rule
msgid ""
"A set of condition and actions which will be available to all attachments "
"matching the conditions"
msgstr "조건과 일치하는 모든 첨부 파일에서 사용할 수 있는 일련의 조건 및 작업"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Centralize files attached to projects and tasks"
msgstr "프로젝트와 업무의 첨부파일을 한곳에 모으세요"

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_company
msgid "Companies"
msgstr "회사"

#. module: documents_project
#: model:ir.model,name:documents_project.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_workflow_rule__create_model
msgid "Create"
msgstr "작성"

#. module: documents_project
#: model:documents.workflow.rule,name:documents_project.documents_project_rule
msgid "Create a Task"
msgstr "작업 만들기"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Default Tags"
msgstr "기본 태그"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__documents_project_settings
msgid "Documents Project Settings"
msgstr "프로젝트 문서 설정"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__documents_project_settings
msgid "Project"
msgstr "프로젝트"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_tags
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_tags
msgid "Project Tags"
msgstr "프로젝트 태그"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_company__project_folder
msgid "Project Workspace"
msgstr "프로젝트 저장공간"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model:ir.model.fields.selection,name:documents_project.selection__documents_workflow_rule__create_model__project_task
msgid "Task"
msgstr "작업"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.res_config_settings_view_form
msgid "Workspace"
msgstr "저장공간"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_res_config_settings__project_folder
msgid "project default workspace"
msgstr "프로젝트 기본 저장공간"
