<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="documents_spreadsheet_folder" model="documents.folder" forcecreate="0">
            <field name="name">Spreadsheet</field>
            <field name="description" type="html">
                <p>Manage and work with all the <strong>spreadsheets</strong> created in other applications.</p>
                <p>By default, the spreadsheets in this workspace will only be seen and updated by their <strong>creator</strong>.</p>
                <br/>
                <p>If you want to work together on those spreadsheets :</p>
                <ol>
                    <li>Move them to another workspace</li>
                    <li>Ask an admin to configure the workspace to be accessible to the users you want.</li>
                    <li>Enjoy collaborative work on your spreadsheets.</li>
                </ol>
            </field>
            <field name="sequence">30</field>
            <field name="group_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="read_group_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="user_specific" eval="True"/>
            <field name="user_specific_write" eval="True"/>
        </record>

        <record id="base.main_company" model="res.company">
            <field name="documents_spreadsheet_folder_id" ref="documents_spreadsheet_folder"/>
        </record>
    </data>
</odoo>
