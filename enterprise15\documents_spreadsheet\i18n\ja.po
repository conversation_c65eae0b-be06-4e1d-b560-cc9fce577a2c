# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-20 12:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_spreadsheet
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
#, python-format
msgid "\"%s\" saved as template"
msgstr "\"%s\" 個がテンプレートとして保存されました"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_page
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_single
msgid ""
"#{ isSpreadsheet and 'Odoo Spreadsheets not available for download' or ''}"
msgstr ""

#. module: documents_spreadsheet
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Columns left"
msgstr "左に %s 列"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Columns right"
msgstr "右に %s 列"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Rows above"
msgstr "上に %s 行"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Rows below"
msgstr "下に %s 行"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "(List #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "(Pivot #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "(Undefined)"
msgstr "(未定義)"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid ""
"<strong>Spreadsheets</strong>\n"
"                            <span class=\"fa fa-lg fa-building-o ml-1\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr "このスプレッドシートとユーザーの組み合わせはすでにあります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A conditional count across a range."
msgstr "範囲全体の条件付きカウント。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A conditional sum across a range."
msgstr "範囲全体の条件付き合計。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A dataset needs to be defined"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A number raised to a power."
msgstr "累乗された数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A number with the sign reversed."
msgstr "符号が逆になっている数字。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A random number between 0 inclusive and 1 exclusive."
msgstr "0を含み1を除く0から1の間の乱数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A range needs to be defined"
msgstr "範囲を定義して下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A specified number, unchanged."
msgstr "指定された番号、変更なし。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A substring from the end of a specified string."
msgstr "指定された文字列の末尾からの部分文字列。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "ABOUT"
msgstr "およそ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Absolute value of a number."
msgstr "数字の絶対値"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__active
msgid "Active"
msgstr "有効"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Add a new filter..."
msgstr "新しいフィルタを追加..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Add another rule"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Also modify formulas"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "An issue occurred while auto-saving"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Angle from the X axis to a point (x,y), in radians."
msgstr "X軸から点(x、y)までのラジアン角度。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Annual yield of a security paying interest at maturity."
msgstr "満期利息を支払う証券の年間利回り"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Annual yield of a security paying periodic interest."
msgstr "定期的に利息を支払う証券の年間利回り"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Anonymous"
msgstr "匿名"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid ""
"Any user will be able to create a new spreadsheet based on this template."
msgstr "すべてのユーザーは、このテンプレートに基づいて新しいスプレッドシートを作成できます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Appends strings to one another."
msgstr "文字列を相互に追加します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "April"
msgstr "4月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Are you sure you want to delete this sheet ?"
msgstr "このシートを削除してもよろしいですか？"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Argument must be a reference to a cell or range."
msgstr "引数はセルまたは範囲への参照である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Ascending (A ⟶ Z)"
msgstr "昇順 (A ⟶ Z)"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Ask an admin to configure the workspace to be accessible to the users you "
"want."
msgstr "管理者に頼んで、希望するユーザがアクセスできるように作業区を設定してもらいましょう。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "August"
msgstr "8月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average magnitude of deviations from mean."
msgstr "平均からの標準偏差"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of a set of values from a table-like range."
msgstr "表のような範囲からの値の平均。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of values depending on criteria."
msgstr "基準に応じた値の平均。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of values depending on multiple criteria."
msgstr "複数の基準に応じた値の平均。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_all_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_list_side_panel.xml:0
#, python-format
msgid "Back"
msgstr "戻る"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Background color"
msgstr "背景色"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bar"
msgstr "バー"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#, python-format
msgid "Blank"
msgstr "空白"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bold"
msgstr "太字"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Borders"
msgstr "枠線"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bottom"
msgstr "下"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"By default, the spreadsheets in this workspace will only be seen and updated"
" by their <strong>creator</strong>."
msgstr "デフォルトでは、この作業区内のスプレッドシートは、<strong>作成者</strong>のみが表示および更新できます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cannot dispatch commands in the finalize state"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Cannot sort. To sort, select only cells or only merges that have the same "
"size."
msgstr "ソートできません。ソートするには、同じサイズのセルまたはマージのみを選択して下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Categories / Labels"
msgstr "カテゴリ / ラベル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cell values"
msgstr "セル値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Center"
msgstr "中央"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr "スプレッドシートを一元管理"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Chart"
msgstr "チャート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Chart type"
msgstr "チャートタイプ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Circular reference"
msgstr "循環参照"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear Format"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear cells"
msgstr "セルをクリア"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear column %s"
msgstr "%s 列目をクリア"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear columns %s - %s"
msgstr "%s - %s 列目をクリア"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear row %s"
msgstr "%s 行目をクリア"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear rows %s - %s"
msgstr "%s - %s 行目をクリア"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_revision
msgid "Collaborative spreadsheet revision"
msgstr "スプレッドシートの共同修正"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Color scale"
msgstr "カラースケール"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column left"
msgstr "左の列"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column number of a specified cell."
msgstr "指定したセルの列番号。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column right"
msgstr "右の列"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Combines text from multiple strings and/or arrays."
msgstr "複数の文字列や配列からのテキストを結合します。"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__commands
msgid "Commands"
msgstr "コマンド"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr "会社"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Computes the number of periods needed for an investment to reach a value."
msgstr "投資額が目標額に達するまでに必要な期間数を計算します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Concatenates elements of arrays with delimiter."
msgstr "配列の要素を区切り文字で連結します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Concatenation of two values."
msgstr "2つの値の連結。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Conditional formatting"
msgstr "条件付き書式"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Confirm"
msgstr "確認"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Contains"
msgstr "含む"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr "貢献者"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a date string to a date value."
msgstr "日付文字列を日付値に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a specified string to lowercase."
msgstr "指定された文字列を小文字に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a specified string to uppercase."
msgstr "指定された文字列を大文字に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a time string into its serial number representation."
msgstr "時間文字列をシリアル番号表現に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts an angle value in radians to degrees."
msgstr "ラジアン単位の角度値を度に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts from another base to decimal."
msgstr "別の基数から小数に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts hour/minute/second into a time."
msgstr "時間/分/秒をtime形式に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts year/month/day into a date."
msgstr "年/月/日を日付に変換します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Copy"
msgstr "コピー"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Copy of %s"
msgstr " %sのコピー"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cosecant of an angle provided in radians."
msgstr "ラジアンで提供される角度の余割。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cosine of an angle provided in radians."
msgstr "ラジアンで提供される角度のコサイン。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cotangent of an angle provided in radians."
msgstr "ラジアンで提供される角度の余接。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Count"
msgstr "カウント"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Count values depending on multiple criteria."
msgstr "複数の基準に応じて値をカウントします。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Counts number of unique values in a range, filtered by a set of criteria."
msgstr "一連の基準でフィルタリングされた、範囲内の一意の値の数をカウントします。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts number of unique values in a range."
msgstr "範囲内の一意の値の数をカウントします。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts values and text from a table-like range."
msgstr "表のような範囲から値とテキストをカウントします。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts values from a table-like range."
msgstr "表のような範囲から値をカウントします。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#, python-format
msgid "Create"
msgstr "作成"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/documents_views.xml:0
#, python-format
msgid "Create Spreadsheet"
msgstr "スプレッドシートを作成"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Create chart"
msgstr "チャートを作成する"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr "作成者"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr "作成日"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Current date and time as a date value."
msgstr "date値としての現在の日付と時刻。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Current date as a date value."
msgstr "date値としての現在の日付。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cut"
msgstr "カット"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__data
#, python-format
msgid "Data"
msgstr "データ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Data Series"
msgstr "データシリーズ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Data series include title"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Date"
msgstr "日付"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date (9/26/2008)"
msgstr "日付 (9/26/2008)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date a number of months before/after another date."
msgstr "別の日付の前/後の月数の日付。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date time (9/26/2008 22:43:00)"
msgstr "日時 (9/26/2008 22:43:00)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#, python-format
msgid "Day"
msgstr "日"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Day of the month that a specific date falls on."
msgstr "特定の日付が該当する月の日。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Day of the week of the date provided (as number)."
msgstr "指定された日付の曜日(数値として)。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "December"
msgstr "12月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Decrease decimal places"
msgstr "小数点以下の桁数を減らす"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Default Value"
msgstr "デフォルト値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete"
msgstr "削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cell and shift left"
msgstr "セルを削除して左方向にシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cell and shift up"
msgstr "セルを削除して上方向にシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cells"
msgstr "セルを削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete column %s"
msgstr "%s 列目を削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete columns %s - %s"
msgstr "%s - %s 行目を削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete row %s"
msgstr "%s 行目を削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete rows %s - %s"
msgstr "%s - %s 行目を削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete values"
msgstr "値を削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Depreciation via declining balance method."
msgstr "定率法による減価償却"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Descending (Z ⟶ A)"
msgstr "降順 (Z ⟶ A)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Did not find value '%s' in [[FUNCTION_NAME]] evaluation."
msgstr " [[FUNCTION_NAME]] 検証において値'%s' が見つかりませんでした。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Difference of two numbers."
msgstr "2つの数字の違い。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Dimensions"
msgstr "ディメンション"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr "表示名"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot_dialog.xml:0
#, python-format
msgid "Display missing cells only"
msgstr "欠落しているセルのみを表示する"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__document_id
msgid "Document"
msgstr "ドキュメント"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__documents_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__documents_spreadsheet_folder_id
msgid "Documents Spreadsheet Folder"
msgstr "ドキュメントスプレッドシートフォルダ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Does not contain"
msgstr "有価証券の価格に基づく割引率"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Domain"
msgstr "ドメイン"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Download"
msgstr "ダウンロード"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Duplicate"
msgstr "複製"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Duplicated Label"
msgstr "重複したラベル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Duration (27:51:38)"
msgstr "Duration (27:51:38)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
#, python-format
msgid "Edit"
msgstr "編集"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Edit link"
msgstr "リンクを編集"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Else"
msgstr "そうしないと"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Ends with"
msgstr "終了値"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Enjoy collaborative work on your spreadsheets."
msgstr "スプレッドシートを使用した共同作業をお楽しみ下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Equal."
msgstr "同一。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Euler's number, e (~2.718) raised to a power."
msgstr "オイラーの数、e (~2.718) を累乗します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Evaluation of function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr "関数[[FUNCTION_NAME]]により、ゼロ除算エラーが発生しました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Exact number of years between two dates."
msgstr "2つの日付の間の正確な年数"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Exponential"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "February"
msgstr "2月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Field Matching"
msgstr "フィールドマッチング"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "File"
msgstr "ファイル"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__raw
msgid "File Content (raw)"
msgstr "フィールドの内容(生)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Fill Color"
msgstr "塗り潰し"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/filters_evaluation_plugin.js:0
#, python-format
msgid "Filter \"%s\" not found"
msgstr "フィルター \"%s\" は見つかりません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Filter properties"
msgstr "フィルタープロパティ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/global_filters_side_panel.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Filters"
msgstr "フィルタ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Find and Replace"
msgstr "検索と置換"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Find and replace"
msgstr "検索と置換"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "First position of string found in text, case-sensitive."
msgstr "テキストで見つかった文字列の最初の位置。大文字と小文字が区別されます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "First position of string found in text, ignoring case."
msgstr "大文字と小文字を区別せずに、テキストで見つかった文字列の最初の位置。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Font Size"
msgstr "フォントサイズ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Font size"
msgstr "フォントサイズ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format"
msgstr "フォーマット"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format as percent"
msgstr "パーセント形式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format cells if..."
msgstr "次の場合にセルをフォーマットします..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format rules"
msgstr "フォーマットルール"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Formatting style"
msgstr "フォーマットスタイル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Formula"
msgstr "計算式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has an argument that has been declared with more than one "
"type whose type 'META'. The 'META' type can only be declared alone."
msgstr "機能 ${name} に'META'型を持つ複数の型で宣言された引数があります。META'型は単独でしか宣言できません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has at mandatory arguments declared after optional ones. "
"All optional arguments must be after all mandatory arguments."
msgstr ""
"関数${name} には、オプションの引数の後に必須の引数が宣言されています。すべてのオプションの引数は、すべての必須の引数の後になければなりません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has no-repeatable arguments declared after repeatable ones."
" All repeatable arguments must be declared last."
msgstr ""
"関数 ${name} に繰返し可能な引数の後に繰返し不可能な引数が宣言されています。繰返し可能な引数は全て最後に宣言されている必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects its parameters to be single values or single cell "
"references, not ranges."
msgstr "関数 %sはそのパラメータが範囲ではなく、単一の値または単一のセル参照であることを想定しています。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects the parameter %s to be a single value or a single cell "
"reference, not a range."
msgstr "関数 %s はパラメータ%sが、範囲ではなく、単一の値または単一のセル参照であることを想定しています。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects the parameter %s to be reference to a cell or range, not"
" a %s."
msgstr "関数 %sはパラメータ%sが、%sではなく、セルまたは範囲の参照であることを想定しています。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Function PIVOT takes an even number of arguments."
msgstr " 関数PIVOTは偶数の引数が必要です。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr "関数[[FUNCTION_NAME]]は、ゼロ除算エラーを引き起こしました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] didn't find any result"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range and criterion to be in "
"pairs."
msgstr "機能[[FUNCTION_NAME]]は、条件範囲と基準はペアであることを期待しています。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range to have the same dimension"
msgstr "機能[[FUNCTION_NAME]]を期待は同じ次元を持つことが条件範囲"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] parameter 2 value (%s) is out of range."
msgstr "関数 [[FUNCTION_NAME]] パラメータ 2 値 (%s) は範囲外です。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] parameter 2 value is out of range."
msgstr "関数 [[FUNCTION_NAME]] パラメータ 2 値 は範囲外です。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Future value of an annuity investment."
msgstr "年金投資の将来価値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "General"
msgstr "一般"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "General (no specific format)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the absolute ID of an element in the pivot"
msgstr " ピボット内の要素の絶対IDを取得します"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/list_functions.js:0
#, python-format
msgid "Get the header of a list."
msgstr "リストのヘッダを取得します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the header of a pivot."
msgstr "ピボットのヘッダーを取得します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/list_functions.js:0
#, python-format
msgid "Get the value from a list."
msgstr "リストから値を取得します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the value from a pivot."
msgstr "ピボットから値を取得します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Gets character associated with number."
msgstr "数値に関連付けられた文字を取得します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Greater than or equal to."
msgstr "大なりイコール"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr "ハンドラ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide column %s"
msgstr "%s 列目を非表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide columns"
msgstr "列を非表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide columns %s - %s"
msgstr "列を非表示 %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide formulas"
msgstr "式を非表示にする"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide gridlines"
msgstr "目盛線を非表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide row %s"
msgstr "行を非表示 %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide rows"
msgstr "行を非表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide rows %s - %s"
msgstr "行を非表示 %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Horizontal align"
msgstr "水平アライン"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Horizontal lookup"
msgstr "水平検索"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hour component of a specific time."
msgstr "特定の時間のHour要素。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cosecant of any real number."
msgstr "任意の実数の双曲線余割。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cosine of any real number."
msgstr "任意の実数の双曲線余弦。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cotangent of any real number."
msgstr "任意の実数の双曲線コタンジェント。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic secant of any real number."
msgstr "任意の実数の双曲線セカント。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic sine of any real number."
msgstr "任意の実数の双曲線正弦。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic tangent of any real number."
msgstr "任意の実数の双曲線正接。"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr "ID"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "ISO week number of the year."
msgstr "その年のISO週番号。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icon Set"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icon set"
msgstr "アイコン設定"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icons"
msgstr "アイコン"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "If you want to work together on those spreadsheets :"
msgstr "これらのスプレッドシートで協働したいのであれば:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "In [[FUNCTION_NAME]] evaluation, cannot find '%s' within '%s'."
msgstr " [[FUNCTION_NAME]] の検証で、 '%s' 内の '%s'が見つかりません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Increase decimal places"
msgstr "小数点以下の桁数を増やす"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert"
msgstr "挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns"
msgstr "列 %s を挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns left"
msgstr "左に %s 列挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns right"
msgstr "右に %s 列挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows"
msgstr "%s行を挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows above"
msgstr "上に %s 行挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows below"
msgstr "下に %s 行挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells"
msgstr "セルを挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells and shift down"
msgstr "セルを挿入し、下方向へシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells and shift right"
msgstr "セルを挿入し、右方向へシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column"
msgstr "列を挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column left"
msgstr "列を左に挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column right"
msgstr "列を右に挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#, python-format
msgid "Insert in Spreadsheet"
msgstr "スプレッドシートに挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert link"
msgstr "リンクを挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/insert_list_spreadsheet_menu.xml:0
#, python-format
msgid "Insert list in spreadsheet"
msgstr "スプレッドシートにリストを挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Insert pivot cell"
msgstr "ピボットセルに挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row"
msgstr "行を挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row above"
msgstr "行を上に挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row below"
msgstr "行を下に挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "Insert the first"
msgstr "リストの先頭の"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Internal rate of return given periodic cashflows."
msgstr "期間キャッシュ・フローを考慮した場合の内部収益率"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Maxpoint formula"
msgstr "無効な最大点公式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Midpoint formula"
msgstr "無効な中点公式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Minpoint formula"
msgstr "無効な最小点公式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid expression"
msgstr "無効な式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid formula"
msgstr "無効な式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid lower inflection point formula"
msgstr "無効な下部変曲点の公式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected %s maximum, but "
"got %s instead."
msgstr " 関数%s用の引数の数が無効です。最大%sを予測していますが、代わりに%sを取得しました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected %s minimum, but "
"got %s instead."
msgstr " 関数%s用の引数の数が無効です。最少%sを予測していますが、代わりに%sを取得しました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected all arguments "
"after position %s to be supplied by groups of %s arguments"
msgstr ""
"関数%s 用の引数の数が無効です。Expected all arguments after position位置 "
"%s以降の全ての引数は%s引数のグループに供給されるのを想定しています。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid reference"
msgstr "無効な参照"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet"
msgstr "無効なシート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet name"
msgstr "無効なシート名"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet name: %s"
msgstr "無効なシート名: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid upper inflection point formula"
msgstr "無効な上部変曲点の公式"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse cosine of a value, in radians."
msgstr "値の逆余弦ラジアン表記。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse cotangent of a value."
msgstr "値の逆コタンジェント。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic cosine of a number."
msgstr "数の逆双曲線余弦。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic cotangent of a value."
msgstr "値の逆双曲線余接。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic sine of a number."
msgstr "数値の逆双曲線正弦。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic tangent of a number."
msgstr "数値の逆双曲線正接。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse sine of a value, in radians."
msgstr "ラジアン値の逆正弦。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse tangent of a value, in radians."
msgstr "ラジアンの値の逆正接、。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is between"
msgstr "間か"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is empty"
msgstr "空か"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is equal to"
msgstr "は次のものと等しい"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is greater than"
msgstr "次のものより大きい…"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is greater than or equal to"
msgstr "次のものより大きい、又はそれに等しい:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is less than"
msgstr "次の数値を下回る:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is less than or equal to"
msgstr "次の数値を下回る、又はそれに等しい:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not between"
msgstr "間ではないか"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not empty"
msgstr "空ではないか"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not equal to"
msgstr "次の数値に等しくない:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Italic"
msgstr "斜体"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "January"
msgstr "1月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "July"
msgstr "7月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "June"
msgstr "6月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Label"
msgstr "ラベル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Labels are invalid"
msgstr "ラベルが無効です"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Last day of a month before or after a date."
msgstr "日付の前または後の月の最終日。"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr "最終更新日時"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Last updated at"
msgstr "最終更新日時は"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Left"
msgstr "左側"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Legend position"
msgstr "凡例ポジション"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Length of a string."
msgstr "文字列の長さ。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Less than or equal to."
msgstr "以下。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Less than."
msgstr "未満。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Line"
msgstr "行"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Linear"
msgstr "定額法"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Link"
msgstr "リンク"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/odoo_menu_link_cell.js:0
#, python-format
msgid "Link an Odoo menu"
msgstr "Odooメニューへリンク"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/insert_action_link_menu/insert_action_link_menu.xml:0
#, python-format
msgid "Link menu in spreadsheet"
msgstr "スプレッドシートでメニューのリンクを貼る"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Link sheet"
msgstr "シートへリンク"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#, python-format
msgid "List Name"
msgstr "リスト名"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet_extended.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "List properties"
msgstr "リストプロパティ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Loading..."
msgstr "読込中..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logarithmic"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `and` operator."
msgstr "論理 `and`演算子。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `or` operator."
msgstr "論理 `or`演算子。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `xor` operator."
msgstr "論理 `xor`演算子。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Look up a value."
msgstr "値を検索します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Lower inflection point must be smaller then upper inflection point"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
#, python-format
msgid "Make a copy"
msgstr "コピーを作成"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Manage and work with all the <strong>spreadsheets</strong> created in other "
"applications."
msgstr "他のアプリで作成された全ての <strong>スプレッドシート</strong>を管理し、利用して業務を行います。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "March"
msgstr "3月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Match case"
msgstr "一致の場合"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Match entire cell content"
msgstr "セル全部の内容を一致させる"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Match this filter to a field for each pivot/list"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum numeric value in a dataset."
msgstr "データセット内の最大数値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum of values from a table-like range."
msgstr "テーブル状の範囲からの値の最大値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum value in a numeric dataset."
msgstr "数値データセット内の最大値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maxpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "May"
msgstr "5月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Measure"
msgstr "測定対象"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Measures"
msgstr "分析対象"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Median value in a numeric dataset."
msgstr "数値データセットの中央値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Menu Items"
msgstr "メニュー項目"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Merge Cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Merged cells are preventing this operation. Unmerge those cells and try "
"again."
msgstr "マージされたセルがこの操作を妨げています。セルをアンマージして、もう一度試して下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Merging these cells will only preserve the top-leftmost value. Merge anyway?"
msgstr "これらのセルをマージすると一番左上の値のみが保持されます。マージしますか？"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Midpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Midpoint must be smaller then Maximum"
msgstr "中間点は最大点より小さい必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum must be smaller then Maximum"
msgstr "最小は最大より小さい必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum must be smaller then Midpoint"
msgstr "最小は中間点より小さい必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum numeric value in a dataset."
msgstr "データセット内の最小の数値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum of values from a table-like range."
msgstr "テーブル状の範囲からの値の最小値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum value in a numeric dataset."
msgstr "数値データセット内の最小値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minpoint"
msgstr "最小ポイント"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minute component of a specific time."
msgstr "特定の時間の分のコンポーネント。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/list_autofill_plugin.js:0
#, python-format
msgid "Missing list #%s"
msgstr "不明なリスト数%s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot"
msgstr "不明なピボット"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot #%s"
msgstr "不明なピボット数%s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Model"
msgstr "モデル"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_ir_model
msgid "Models"
msgstr "モデル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Modified Macaulay duration."
msgstr "修正済マコーレー・デュレーション"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Modulo (remainder) operator."
msgstr "モジュロ（剰余）演算子。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Month"
msgstr "月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Month of the year a specific date falls in"
msgstr "特定の日がみなされる月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "More formats"
msgstr "その他のフォマット"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "More than one match found in DGET evaluation."
msgstr "DGET評価で複数の一致が見つかりました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Move left"
msgstr "左に移動"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Move right"
msgstr "右に動く"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Move them to another workspace"
msgstr "それらを他の作業区へ移動"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr "自分のテンプレート"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr "名称"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Net working days between two dates (specifying weekends)."
msgstr "二つの日付（指定週末）の間の正味営業日。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Net working days between two provided days."
msgstr "2提供日の間の純営業日。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "New"
msgstr "新規"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "New %s filter"
msgstr "新規%s フィルタ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "New Chart"
msgstr "新しいグラフ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_template_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "New Spreadsheet"
msgstr "新規スプレッドシート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "New sheet"
msgstr "新しいシート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_service.js:0
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#, python-format
msgid "New sheet inserted in '%s'"
msgstr "‘%s’に挿入された新シート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_template_dialog.js:0
#, python-format
msgid "New sheet saved in Documents"
msgstr "新しいシートは、ドキュメントに保存されています"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr "新しいスプレッドシート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_service.js:0
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#, python-format
msgid "New spreadsheet created in Documents"
msgstr "ドキュメントで作成した新しいスプレッドシート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet_template/spreadsheet_template_action.js:0
#, python-format
msgid "New spreadsheet template created"
msgstr "作成された新しいスプレッドシートのテンプレート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Next"
msgstr "次"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "No match."
msgstr "一致はありません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "None"
msgstr "なし"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Not equal."
msgstr "不一致。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Not implemented operator %s for kind of conditional formatting:  %s"
msgstr "条件付き書式: %sの種類に演算子 %sが実装されていません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_cache.js:0
#, python-format
msgid "Not implemented: %s"
msgstr "実装されていません: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "November"
msgstr "11月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Nth largest element from a data set."
msgstr "データセットからN番目の最大の要素。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Nth smallest element in a data set."
msgstr "データセット内のN番目の最小要素。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number"
msgstr "番号"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number (1,000.12)"
msgstr "数値（1,000.12）"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of columns in a specified array or range."
msgstr "指定した配列または範囲内の列数"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of days between two dates."
msgstr "2つの日付の間の日数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of empty values."
msgstr "空の値の数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of periods for an investment to reach a value."
msgstr "投資額が一定額に達するまでの期間"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of rows in a specified array or range."
msgstr "指定した配列または範囲内の行数"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of working days from start date."
msgstr "開始日から営業日の数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numbers"
msgstr "数字"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numerical average value in a dataset, ignoring text."
msgstr "データセット内の数値の平均値、テキストを無視します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numerical average value in a dataset."
msgstr "データセット内の数値の平均値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "October"
msgstr "10月"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "One number divided by another."
msgstr "別の分の1人の数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paint Format"
msgstr "ペイント形式"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__parent_revision_id
msgid "Parent Revision"
msgstr "親改訂"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste"
msgstr "ペースト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste format only"
msgstr "書式のみを貼り付け"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste special"
msgstr "特殊な貼り付け"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste value only"
msgstr "値のみを貼り付け"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste values only"
msgstr "値のみを貼り付け"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percent (10.12%)"
msgstr "パーセント（10.12パーセント）"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percentage"
msgstr "パーセント"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percentile"
msgstr "パーセンタイル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Pie"
msgstr "パイ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Pivot name"
msgstr "ピボット名"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet_extended.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Pivot properties"
msgstr "ピボット・プロパティ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Please enter a valid sheet name"
msgstr "有効なシート名を入力してください。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "Please, close all other Odoo tabs and reload the current page."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Position of item in range that matches value."
msgstr "値と一致する範囲内の項目の位置。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Positive square root of a positive number."
msgstr "正の数の正の平方根。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Present value of an annuity investment."
msgstr "年金投資の現在価値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Preview"
msgstr "プレビュー表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Preview text"
msgstr "プレビューテキスト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Previous"
msgstr "前"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Price of a security paying periodic interest."
msgstr "定期的に利子を支払う証券の価格"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Product of two numbers"
msgstr "2つの数の積"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Product of values from a table-like range."
msgstr "テーブルのような範囲の値の積。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Quarter"
msgstr "四半期"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Random integer between two values, inclusive."
msgstr "2つの値までの間のランダムな整数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Re-insert list"
msgstr "再挿入リスト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Re-insert pivot"
msgstr "再挿入ピボット"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/list_autofill_plugin.js:0
#, python-format
msgid "Record #"
msgstr "レコード数"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Redo"
msgstr "もとに戻す"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Refresh"
msgstr "更新"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Refresh all data"
msgstr "全てのデータを更新"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Refresh values"
msgstr "値をリフレッシュ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Related Model"
msgstr "関連モデル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Relation"
msgstr "関係"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "Reload"
msgstr "リロード"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Remove"
msgstr "削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Remove link"
msgstr "リンクを削除"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Removes space characters."
msgstr "空白文字を削除します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_name.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rename"
msgstr "名前の変更"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rename Sheet"
msgstr "名前の変更シート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replace"
msgstr "置き換える"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replace all"
msgstr "全てを入替"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replaces existing text with new text in a string."
msgstr "文字列内の新しいテキストを既存のテキストを置き換えます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replaces part of a text string with different text."
msgstr "別のテキストとテキスト文字列の一部を置き換えます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Result of multiplying a series of numbers together."
msgstr "一緒に一連の番号を乗算した結果。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Return the current value of a spreadsheet filter."
msgstr "スプレッドシートフィルタの現在の値を返します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns a value depending on multiple logical expressions."
msgstr "複数の論理式に応じた値を返します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns opposite of provided logical value."
msgstr "提供する論理値の反対を返します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Returns the maximum value in a range of cells, filtered by a set of "
"criteria."
msgstr "基準のセットによってフィルタリングセルの範囲内の最大値を返します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Returns the minimum value in a range of cells, filtered by a set of "
"criteria."
msgstr "基準のセットによってフィルタリングセルの範囲内の最小値を返します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns value depending on logical expression."
msgstr "戻り値は、論理式に応じた値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Reverse icons"
msgstr "リバースアイコン"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__revision_id
msgid "Revision"
msgstr "更新"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_revision_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_revision
msgid "Revisions"
msgstr "更新"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Right"
msgstr "右側"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds a number according to standard rules."
msgstr "標準ルールに従って番号を丸めます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds a number up to the nearest odd integer."
msgstr "ラウンド最も近い奇数の整数の数まで。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds down a number."
msgstr "数切り捨て。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds number down to nearest multiple of factor."
msgstr "ラウンドは、値の最も近い倍数にまで番号。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds number up to nearest multiple of factor."
msgstr "ラウンドは、値の最も近い倍数までの番号を付けます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds up a number."
msgstr "数切り上げられます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row above"
msgstr "上の行"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row below"
msgstr "下の行"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row number of a specified cell."
msgstr "指定したセルの行番号。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Save"
msgstr "保存"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
#, python-format
msgid "Save as Template"
msgstr "テンプレートとして保存"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Saved"
msgstr "保存しました"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Saving"
msgstr "保存"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Search"
msgstr "検索"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Search in formulas"
msgstr "数式で検索"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Secant of an angle provided in radians."
msgstr "ラジアンで提供さ角度の割線。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "See records"
msgstr "レコードを見る"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Select a color..."
msgstr "色を選択..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select a menu..."
msgstr "メニューを選択..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#, python-format
msgid "Select a spreadsheet to insert your list"
msgstr "リストを挿入するスプレッドシートを選択"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Select a spreadsheet to insert your pivot"
msgstr "あなたのピボットを挿入するスプレッドシートを選択します"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select an Odoo menu to link in your spreadsheet"
msgstr "Odooメニューを選択してスプレッドシートにリンクを貼る"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#, python-format
msgid "Select the number of records to insert"
msgstr "挿入するレコードの数を選ぶ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "September"
msgstr "9月"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr "付番"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Series"
msgstr "シリーズ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sheet"
msgstr "表"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift down"
msgstr "下にシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift left"
msgstr "左にシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift right"
msgstr "右にシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift up"
msgstr "上にシフト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Show formulas"
msgstr "数式を表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Show gridlines"
msgstr "目盛線を表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sine of an angle provided in radians."
msgstr "ラジアンで提供された角度のサイン。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Single color"
msgstr "単色"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Single value from a table-like range."
msgstr "テーブルのような範囲から単一の値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Some required fields are not valid"
msgstr "必要なフィールドで有効でないものがあります"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort column"
msgstr "列をソート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort columns"
msgstr "列を並び替え"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort range"
msgstr "範囲を並べ替え"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#, python-format
msgid "Sorting"
msgstr "並べ替え"

#. module: documents_spreadsheet
#: model:documents.folder,name:documents_spreadsheet.documents_spreadsheet_folder
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_revision_view_search
msgid "Spreadsheet"
msgstr "スプレッドシート"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr "スプレッドシートのコントリビュータ"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr "スプレッドシートのプレビュー"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "スプレッドシート修正"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "スプレッドシート スナップショット"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr "スプレッドシートのテンプレート"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr "スプレッドシートのテンプレートの保存ウィザード"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr "スプレッドシートのテンプレート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/documents_inspector.js:0
#, python-format
msgid ""
"Spreadsheets mass download not yet supported.\n"
" Download spreadsheets individually instead."
msgstr ""
"スプレッドシートの一括ダウンロードはまだサポートされていません。\n"
"代わりにスプレッドシートを個別にダウンロードして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Stacked barchart"
msgstr "積み上げ棒グラフ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population (text as 0)."
msgstr "全体の母集団の標準偏差（文字列なら0）。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population from table."
msgstr "テーブルから全人口の標準偏差。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population."
msgstr "全人口の標準偏差。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of population sample from table."
msgstr "テーブルからの人口のサンプルの標準偏差。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of sample (text as 0)."
msgstr "試料の標準偏差（文字列は0とする）。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation."
msgstr "標準偏差。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Starts with"
msgstr "以下で始まる:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Strictly greater than."
msgstr "より厳密に大きいです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Strikethrough"
msgstr "取消線"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Substring from beginning of specified string."
msgstr "指定された文字列の先頭から部分。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of a series of numbers and/or cells."
msgstr "数字および/または一連のセルの合計。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of two numbers."
msgstr "2つの数の合計。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of values from a table-like range."
msgstr "テーブルのような範囲からの値の合計。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sums a range depending on multiple criteria."
msgstr "複数の基準に応じた範囲を合計します。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Tangent of an angle provided in radians."
msgstr "ラジアンで提供さ角度のタンジェント。"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr "テンプレート名"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Tests whether two strings are identical."
msgstr "二つの文字列が同一であるかどうかをテストします。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Text"
msgstr "テキスト"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Text Color"
msgstr "テキストの色"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The anchor must be part of the provided zone"
msgstr "アンカーは提供されたゾーンの一部である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "The argument %s is not a valid measure. Here are the measures: %s"
msgstr "引数 %sは有効な対策ではありません。ここでの対策は以下のとおりです。: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The argument is missing. Please provide a value"
msgstr "引数がありません。値を指定して下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The base (%s) must be between 2 and 36 inclusive."
msgstr "基数(%s)は2以上36以下でなければならない。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cashflow_amounts must include negative and positive values."
msgstr "cashflow_amountsには負と正の値を含めて下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cell you are trying to edit has been deleted."
msgstr "編集しようとしているセルは削除されました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The chart definition is invalid for an unknown reason"
msgstr "チャート定義が原因不明で無効です"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cost (%s) must be strictly positive."
msgstr "原価(%s)は必ず正の値である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The covariance of a dataset."
msgstr "データセットの共分散。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The criteria range contains %s row, it must be at least 2 rows."
msgstr "基準範囲には%s行が含まれ、少なくとも2行でなければなりません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The dataset is invalid"
msgstr "データセットが無効です"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The date_string (%s) cannot be parsed to date/time."
msgstr "date_string (%s) は日付/時間に解析できません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The day_count_convention (%s) must be between 0 and 4 inclusive."
msgstr "day_count_convention (%s) は 0以上4以下にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The day_count_convention (%s) must between 0 and 4 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The discount (%s) must be different from -1."
msgstr "割引 (%s) は -1と異なる必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The divisor must be different from 0."
msgstr "除数は 0 以外にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The divisor must be different from zero."
msgstr "除数は ゼロ以外にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The end_date (%s) must be positive or null."
msgstr "end_date (%s)は正の値またはNULLである必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The exponent (%s) must be an integer when the base is negative."
msgstr "基数が負の場合、指数(%s) は整数にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The factor (%s) must be positive when the value (%s) is positive."
msgstr "係数 (%s) は値 (%s) が正のとき、正である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/list_data_source.js:0
#, python-format
msgid "The field %s does not exist or you do not have access to that field"
msgstr "フィールド%sが存在しないか、そのフィールドへのアクセス権がありません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The field (%s) must be one of %s or must be a number between 1 and %s "
"inclusive."
msgstr "フィールド (%s)は%sの1つであるか、1と%s以下の間の数である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The field (%s) must be one of %s."
msgstr "フィールド (%s) は%sの1つである必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The field must be a number or a string"
msgstr "フィールドは数値または文字列である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The first value must be a number"
msgstr "最初の値は数値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The frequency (%s) must be one of %s"
msgstr "頻度(%s)は %sのうち1つである必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The frequency (%s) must be one of %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] expects a boolean value, but '%s' is a text, "
"and cannot be coerced to a number."
msgstr "関数[[FUNCTION_NAME]]は、ブール値であるべきですが、 '%s' はテキストであり、数値にすることはできません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] expects a number value, but '%s' is a string,"
" and cannot be coerced to a number."
msgstr "関数[[FUNCTION_NAME]]は数値を想定していますが、 '%s'は文字列であり、数値にすることができません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The function [[FUNCTION_NAME]] result cannot be negative"
msgstr "関数 [[FUNCTION_NAME]]結果は負の数であることはできません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] result must be greater than or equal "
"01/01/1900."
msgstr "関数[[FUNCTION_NAME]]結果は01/01/1900より大きいかそれと同等でなくてはなりません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The future_value (%s) must be strictly positive."
msgstr "future_value (%s) は必ず正の値である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The high (%s) must be greater than or equal to the low (%s)."
msgstr "high (%s) は low(%s)より大きいか同等である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The life (%s) must be strictly positive."
msgstr "life (%s) は必ず正の値である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The logarithm of a number, base e (euler's number)."
msgstr "数値の対数、底e（オイラー数）。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The maturity (%s) must be strictly greater than the settlement (%s)."
msgstr "満期(%s) は支払(%s)よりも必ず大きい必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The maxpoint must be a number"
msgstr "最大点は数値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The midpoint must be a number"
msgstr "中点は数値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The minpoint must be a number"
msgstr "最小点は数値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The month (%s) must be between 1 and 12 inclusive."
msgstr "月 (%s) は1以上12以下にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The net present value of an investment based on a series of periodic cash "
"flows and a discount rate."
msgstr "一連の期間キャッシュフローと割引率に基づく投資の正味現在価値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number of numeric values in dataset."
msgstr "データセット内の数値の数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number of values in a dataset."
msgstr "データセット内の値の数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number pi."
msgstr "数のパイ。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number_of_characters (%s) must be positive or null."
msgstr "number_of_characters (%s) は正の値かNULLにして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The occurrenceNumber (%s) must be positive or null."
msgstr "occurrenceNumber (%s) は正の値またはNULLにして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The period (%s) must be less than or equal to %s."
msgstr "期間 (%s) は %sより少ないか同等にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The period (%s) must be strictly positive."
msgstr "期間 (%s) は必ず正の値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The position (%s) must be greater than or equal to 1."
msgstr "ポジション (%s)は1以上にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The present_value (%s) must be strictly positive."
msgstr "present_value (%s)は必ず正の値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The price (%s) must be strictly positive."
msgstr "価格は (%s)は必ず正の値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The range is invalid"
msgstr "範囲が無効です"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The range must be a single row or a single column."
msgstr "範囲は1つのみの行または列である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate (%s) must be positive or null."
msgstr "割合 (%s)は正の値かNULLにして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate (%s) must be strictly positive."
msgstr "割合 (%s) は必ず正の値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate_guess (%s) must be strictly greater than -1."
msgstr "rate_guess (%s) は必ず -1より大きくして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The redemption (%s) must be strictly positive."
msgstr "還元 (%s) は必ず正の値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The result_range must be a single row or a single column."
msgstr "result_rangeは1つの行または1つの列である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rule is invalid for an unknown reason"
msgstr "規則は不明な理由で無効です。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The salvage (%s) must be positive or null."
msgstr "サルベージ (%s) は正の値かNULLにして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The sample covariance of a dataset."
msgstr "データセットの標本共分散。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The second argument is missing. Please provide a value"
msgstr "第2引数がありません。値を指定して下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The second value must be a number"
msgstr "2番目の値は数値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The settlement (%s) must be greater than or equal to the issue (%s)."
msgstr "決済(%s)は発行 (%s)以上にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The start_date (%s) must be positive or null."
msgstr "start_date (%s)は正の値またはNULLにして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The starting_at (%s) must be greater than or equal to 1."
msgstr "starting_at (%s) は1以上にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The table_number (%s) is out of range."
msgstr "table_number (%s) は範囲外です"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The text_to_search must be non-empty."
msgstr " text_to_searchは空にしないで下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The time_string (%s) cannot be parsed to date/time."
msgstr "time_string (%s) は日付/時刻に解析できません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The type (%s) is out of range."
msgstr "タイプ (%s) は範囲外です。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The type (%s) must be 1, 2 or 3."
msgstr "タイプ (%s) は 1, 2 または3にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) cannot be between -1 and 1 inclusive."
msgstr "値 (%s) には-1から1までの値を含めることはできません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be a valid base %s representation."
msgstr "値 (%s) は有効なベース%s表現である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be between -1 and 1 exclusive."
msgstr "値 (%s) は -1 から1 より小さい間である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be between -1 and 1 inclusive."
msgstr "値 (%s)は-1 以上1 以下である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be greater than or equal to 1."
msgstr "値 (%s)は 1以上である必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be positive or null."
msgstr "値 (%s) は正の値またはNULLである必要があります。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be strictly positive."
msgstr "値 (%s)は必ず正の値にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The weekend (%s) must be a number or a string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The weekend (%s) must be a string or a number in the range 1-7 or 11-17."
msgstr "週末 (%s)は 1-7 または11-17の範囲の文字列または数にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The weekend (%s) must be different from '1111111'."
msgstr "週末 (%s)は'1111111'と異なる必要があります"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The year (%s) must be between 0 and 9999 inclusive."
msgstr "年(%s)は0から9999以下の間にして下さい。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The yield (%s) must be positive or null."
msgstr "利回り (%s) は正の数かNULLにして下さい"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "There is no pivot with id \"%s\""
msgstr "ピボットは、ID\"%s\"はありません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "This formula depends on invalid values"
msgstr "この式は、無効な値に依存します"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"This formula has over 100 parts. It can't be processed properly, consider "
"splitting it into multiple cells"
msgstr "この式は、100を超える部分があります。これは、複数のセルにそれを分割を検討し、適切に処理することはできません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "This operation is not allowed with multiple selections."
msgstr "この操作は、複数選択を許可されていません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"This operation is not possible due to a merge. Please remove the merges "
"first than try again."
msgstr "この操作はマージのためできません。マージを削除してから再試行して下さい。"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr "サムネイル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Time (10:43:00 PM)"
msgstr "時間（10時43分00秒PM）"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Time Range"
msgstr "期間"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Title"
msgstr "タイトル"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Toggle favorite"
msgstr "トグルのお気に入り"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Top"
msgstr "上"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_pivot_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Total"
msgstr "合計"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Truncates a number."
msgstr "数を切り捨てます。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Type"
msgstr "タイプ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_data_source.js:0
#, python-format
msgid "Unable to fetch the label of %s of model %s"
msgstr "%s のラベル(%sモデルの)を取得できません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Underline"
msgstr "下線"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Undo"
msgstr "戻す"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unexpected token: %s"
msgstr "予期せぬトークン: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unhide all columns"
msgstr "全ての列を再表示"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unhide all rows"
msgstr "すべての行を非表示にする"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_cache.js:0
#, python-format
msgid "Unknown operator: %s"
msgstr "不明なオペレータ: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unknown token: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unmatched left parenthesis"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/constants.js:0
#, python-format
msgid "Untitled spreadsheet"
msgstr "無題スプレッドシート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet_template/spreadsheet_template_service.js:0
#, python-format
msgid "Untitled spreadsheet template"
msgstr "無題スプレッドシートのテンプレート"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Update chart"
msgstr "更新チャート"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr "ユーザ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value"
msgstr "値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value at a given percentile of a dataset exclusive of 0 and 1."
msgstr "0と1を除いた既定のデータセットのパーセンタイルの値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value at a given percentile of a dataset."
msgstr "データセットの既定のパーセンタイルにおける値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value if it is not an error, otherwise 2nd argument."
msgstr "値がエラーでない場合は、第2引数。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value interpreted as a percentage."
msgstr "値はパーセンテージとして解釈しました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Value nearest to a specific quartile of a dataset exclusive of 0 and 4."
msgstr "0と4を除いた、データセットの特定の四分位数に最も近い値。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value nearest to a specific quartile of a dataset."
msgstr "データセットの特定の四分位数に最も近い値"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of a population from a table-like range."
msgstr "テーブルのような範囲から母集団の分散。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of entire population (text as 0)."
msgstr "全体の母集団の分散（文字列は0とする）。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of entire population."
msgstr "全人口の分散。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of population sample from table-like range."
msgstr "テーブルのような範囲からの人口のサンプルの分散。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of sample (text as 0)."
msgstr "試料の分散（文字列は0とする）。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance."
msgstr "分散。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Vertical axis position"
msgstr "縦軸の位置"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Vertical lookup."
msgstr "垂直検索。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "View"
msgstr "ビュー"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"We found data next to your selection. Since this data was not selected, it "
"will not be sorted. Do you want to extend your selection?"
msgstr "選択範囲の隣にデータがあります。このデータが選択されていないので並べ替えはされません。選択範囲を広げますか？"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#, python-format
msgid "Week"
msgstr "週"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Week number of the year."
msgstr "年間の週番号。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Weighted average."
msgstr "加重平均。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "When value is"
msgstr "値が以下の時:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "When weekend is a string (%s) it must be composed of \"0\" or \"1\"."
msgstr "週末が文字列 (%s) の時、 \"0\" または \"1\"で構成される必要があります"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is `true` or `false`."
msgstr "値が `true`もしくは` false`かどうかです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is a number."
msgstr "値かどうかです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is an error."
msgstr "値がエラーかどうかです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is non-textual."
msgstr "値が非テキストであるかどうかです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is text."
msgstr "値がテキストかどうかです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether the provided value is even."
msgstr "値が偶数であるかどうかです。"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr "ワークスペース"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong function call"
msgstr "間違った関数呼び出し"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong number of Argument[]. Expected an even number of Argument[]."
msgstr "引数の数が誤っています[]。偶数の引数となるべきです[]."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong number of arguments. Expected an even number of arguments."
msgstr "引数の数が誤っています。偶数の引数となるべきです。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Year"
msgstr "年"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Year specified by a given date."
msgstr "年は、特定の日付で指定されました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "[[FUNCTION_NAME]] cannot be called from the spreadsheet."
msgstr "[[FUNCTION_NAME]]は、スプレッドシートから呼び出すことはできません。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of bounds range."
msgstr "[[FUNCTION_NAME]]は範囲外と評価されます"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of range column value %s."
msgstr "[[FUNCTION_NAME]] は列の値 %sの範囲外と評価されます"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of range row value %s."
msgstr "[[FUNCTION_NAME]] は行の値 %sの範囲外と評価されます"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] expects number values."
msgstr "[[FUNCTION_NAME]]には数値が入ります"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] expects the weight to be positive or equal to 0."
msgstr "[[FUNCTION_NAME]] は重量が正の値または0と同等を予測しています"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has mismatched argument count %s vs %s."
msgstr "[[FUNCTION_NAME]] は引数の数が %s と %sで不一致です"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has mismatched range sizes."
msgstr "[[FUNCTION_NAME]]が一致しない範囲サイズです"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has no valid input data."
msgstr "[[FUNCTION_NAME]]に有効な入力データがありません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "and"
msgstr "と"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "by default"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot_dialog.xml:0
#, python-format
msgid "has no cell missing from this sheet"
msgstr "このシートからセル削除がありません"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.js:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.js:0
#, python-format
msgid "never"
msgstr "しない"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_revision_parent_revision_unique
msgid "o-spreadsheet revision refused due to concurrency"
msgstr "o-スプレッドシートの修正が同時実行のため拒否されました。"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "optional"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "records of the list."
msgstr "レコードを挿入"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "repeatable"
msgstr "繰り返し可能"
