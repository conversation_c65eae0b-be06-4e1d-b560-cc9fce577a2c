# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# Will Sensors, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-20 12:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Will Sensors, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: documents_spreadsheet
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
#, python-format
msgid "\"%s\" saved as template"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_page
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_single
msgid ""
"#{ isSpreadsheet and 'Odoo Spreadsheets not available for download' or ''}"
msgstr ""

#. module: documents_spreadsheet
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Columns left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Columns right"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Rows above"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Rows below"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "(List #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "(Pivot #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "(Undefined)"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid ""
"<strong>Spreadsheets</strong>\n"
"                            <span class=\"fa fa-lg fa-building-o ml-1\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A conditional count across a range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A conditional sum across a range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A dataset needs to be defined"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A number raised to a power."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A number with the sign reversed."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A random number between 0 inclusive and 1 exclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A range needs to be defined"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A specified number, unchanged."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A substring from the end of a specified string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "ABOUT"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Absolute value of a number."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__active
msgid "Active"
msgstr "Aktīvs Sistēmā"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Add a new filter..."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Add another rule"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Also modify formulas"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "An issue occurred while auto-saving"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Angle from the X axis to a point (x,y), in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Annual yield of a security paying interest at maturity."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Annual yield of a security paying periodic interest."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Anonymous"
msgstr "Anonīms"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid ""
"Any user will be able to create a new spreadsheet based on this template."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Appends strings to one another."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "April"
msgstr "Aprīlis"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Are you sure you want to delete this sheet ?"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Argument must be a reference to a cell or range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Ascending (A ⟶ Z)"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Ask an admin to configure the workspace to be accessible to the users you "
"want."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "August"
msgstr "Augusts"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average magnitude of deviations from mean."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of a set of values from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of values depending on criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of values depending on multiple criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_all_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_list_side_panel.xml:0
#, python-format
msgid "Back"
msgstr "Back"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Background color"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bar"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#, python-format
msgid "Blank"
msgstr "Tukšs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bold"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Borders"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bottom"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"By default, the spreadsheets in this workspace will only be seen and updated"
" by their <strong>creator</strong>."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Cancel"
msgstr "Atcelt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cannot dispatch commands in the finalize state"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Cannot sort. To sort, select only cells or only merges that have the same "
"size."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Categories / Labels"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cell values"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Center"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Chart"
msgstr "Tabula"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Chart type"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Circular reference"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear Format"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear column %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear columns %s - %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear row %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear rows %s - %s"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_revision
msgid "Collaborative spreadsheet revision"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Color scale"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column number of a specified cell."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column right"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Combines text from multiple strings and/or arrays."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__commands
msgid "Commands"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr "Uzņēmumi"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Computes the number of periods needed for an investment to reach a value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Concatenates elements of arrays with delimiter."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Concatenation of two values."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Conditional formatting"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas iestatījumi"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Confirm"
msgstr "Apstiprināt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Contains"
msgstr "Satur"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr "Atbalstītāji"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a date string to a date value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a specified string to lowercase."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a specified string to uppercase."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a time string into its serial number representation."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts an angle value in radians to degrees."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts from another base to decimal."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts hour/minute/second into a time."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts year/month/day into a date."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Copy"
msgstr "Kopēt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Copy of %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cosecant of an angle provided in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cosine of an angle provided in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cotangent of an angle provided in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Count"
msgstr "Skaits"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Count values depending on multiple criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Counts number of unique values in a range, filtered by a set of criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts number of unique values in a range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts values and text from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts values from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#, python-format
msgid "Create"
msgstr "Izveidot"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/documents_views.xml:0
#, python-format
msgid "Create Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Create chart"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Current date and time as a date value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Current date as a date value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cut"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__data
#, python-format
msgid "Data"
msgstr "Datne"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Data Series"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Data series include title"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Date"
msgstr "Datums"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date (9/26/2008)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date a number of months before/after another date."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date time (9/26/2008 22:43:00)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#, python-format
msgid "Day"
msgstr "Diena"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Day of the month that a specific date falls on."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Day of the week of the date provided (as number)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "December"
msgstr "Decembris"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Decrease decimal places"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Default Value"
msgstr "Noklusētā Vērtība"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete"
msgstr "Izdzēst"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cell and shift left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cell and shift up"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete column %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete columns %s - %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete row %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete rows %s - %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete values"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Depreciation via declining balance method."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Descending (Z ⟶ A)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Did not find value '%s' in [[FUNCTION_NAME]] evaluation."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Difference of two numbers."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Dimensions"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot_dialog.xml:0
#, python-format
msgid "Display missing cells only"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__document_id
msgid "Document"
msgstr "Document"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__documents_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__documents_spreadsheet_folder_id
msgid "Documents Spreadsheet Folder"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Does not contain"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Domain"
msgstr "Filtrs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Download"
msgstr "Download"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Duplicate"
msgstr "Dublēt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Duplicated Label"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Duration (27:51:38)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
#, python-format
msgid "Edit"
msgstr "Labot"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Edit link"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Else"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Ends with"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Enjoy collaborative work on your spreadsheets."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Equal."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Euler's number, e (~2.718) raised to a power."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Evaluation of function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Exact number of years between two dates."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Exponential"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "February"
msgstr "Februāris"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Field Matching"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "File"
msgstr "Fails"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__raw
msgid "File Content (raw)"
msgstr "Faila saturs (neapstrādāts)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Fill Color"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/filters_evaluation_plugin.js:0
#, python-format
msgid "Filter \"%s\" not found"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Filter properties"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/global_filters_side_panel.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Filters"
msgstr "Filtri"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Find and Replace"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Find and replace"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "First position of string found in text, case-sensitive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "First position of string found in text, ignoring case."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Font Size"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Font size"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format"
msgstr "Formatēt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format as percent"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format cells if..."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format rules"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Formatting style"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Formula"
msgstr "Formula"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has an argument that has been declared with more than one "
"type whose type 'META'. The 'META' type can only be declared alone."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has at mandatory arguments declared after optional ones. "
"All optional arguments must be after all mandatory arguments."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has no-repeatable arguments declared after repeatable ones."
" All repeatable arguments must be declared last."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects its parameters to be single values or single cell "
"references, not ranges."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects the parameter %s to be a single value or a single cell "
"reference, not a range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects the parameter %s to be reference to a cell or range, not"
" a %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Function PIVOT takes an even number of arguments."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] didn't find any result"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range and criterion to be in "
"pairs."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range to have the same dimension"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] parameter 2 value (%s) is out of range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] parameter 2 value is out of range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Future value of an annuity investment."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "General"
msgstr "General"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "General (no specific format)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the absolute ID of an element in the pivot"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/list_functions.js:0
#, python-format
msgid "Get the header of a list."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the header of a pivot."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/list_functions.js:0
#, python-format
msgid "Get the value from a list."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the value from a pivot."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Gets character associated with number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Greater than or equal to."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide column %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide columns"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide columns %s - %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide formulas"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide gridlines"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide row %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide rows"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide rows %s - %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Horizontal align"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Horizontal lookup"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hour component of a specific time."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cosecant of any real number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cosine of any real number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cotangent of any real number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic secant of any real number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic sine of any real number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic tangent of any real number."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr "ID"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "ISO week number of the year."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icon Set"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icon set"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icons"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "If you want to work together on those spreadsheets :"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "In [[FUNCTION_NAME]] evaluation, cannot find '%s' within '%s'."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Increase decimal places"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns right"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows above"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows below"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells and shift down"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells and shift right"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column right"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#, python-format
msgid "Insert in Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert link"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/insert_list_spreadsheet_menu.xml:0
#, python-format
msgid "Insert list in spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Insert pivot cell"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row above"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row below"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "Insert the first"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Internal rate of return given periodic cashflows."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Maxpoint formula"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Midpoint formula"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Minpoint formula"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid expression"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid formula"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid lower inflection point formula"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected %s maximum, but "
"got %s instead."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected %s minimum, but "
"got %s instead."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected all arguments "
"after position %s to be supplied by groups of %s arguments"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid reference"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet name"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet name: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid upper inflection point formula"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse cosine of a value, in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse cotangent of a value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic cosine of a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic cotangent of a value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic sine of a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic tangent of a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse sine of a value, in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse tangent of a value, in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is between"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is empty"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is equal to"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is greater than"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is greater than or equal to"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is less than"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is less than or equal to"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not between"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not empty"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not equal to"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Italic"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "January"
msgstr "Janvāris"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "July"
msgstr "Jūlijs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "June"
msgstr "Jūnijs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Label"
msgstr "Uzraksts"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Labels are invalid"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunoja"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunots"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Last day of a month before or after a date."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Last updated at"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Left"
msgstr "Left"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Legend position"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Length of a string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Less than or equal to."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Less than."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Line"
msgstr "Line"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Linear"
msgstr "Lineāra"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Link"
msgstr "Saite"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/odoo_menu_link_cell.js:0
#, python-format
msgid "Link an Odoo menu"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/insert_action_link_menu/insert_action_link_menu.xml:0
#, python-format
msgid "Link menu in spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Link sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#, python-format
msgid "List Name"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet_extended.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "List properties"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Loading..."
msgstr "Notiek ielāde..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logarithmic"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `and` operator."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `or` operator."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `xor` operator."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Look up a value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Lower inflection point must be smaller then upper inflection point"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
#, python-format
msgid "Make a copy"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Manage and work with all the <strong>spreadsheets</strong> created in other "
"applications."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "March"
msgstr "Marts"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Match case"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Match entire cell content"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Match this filter to a field for each pivot/list"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum numeric value in a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum of values from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum value in a numeric dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maxpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "May"
msgstr "Maijs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Measure"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Measures"
msgstr "Mērs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Median value in a numeric dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Menu Items"
msgstr "Menu Items"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Merge Cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Merged cells are preventing this operation. Unmerge those cells and try "
"again."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Merging these cells will only preserve the top-leftmost value. Merge anyway?"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Midpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Midpoint must be smaller then Maximum"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum must be smaller then Maximum"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum must be smaller then Midpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum numeric value in a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum of values from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum value in a numeric dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minute component of a specific time."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/list_autofill_plugin.js:0
#, python-format
msgid "Missing list #%s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot #%s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Model"
msgstr "Modelis"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_ir_model
msgid "Models"
msgstr "Models"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Modified Macaulay duration."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Modulo (remainder) operator."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Month"
msgstr "Mēnesis"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Month of the year a specific date falls in"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "More formats"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "More than one match found in DGET evaluation."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Move left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Move right"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Move them to another workspace"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr "Nosaukums"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Net working days between two dates (specifying weekends)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Net working days between two provided days."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "New"
msgstr "Jauns"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "New %s filter"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "New Chart"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_template_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "New Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "New sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_service.js:0
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#, python-format
msgid "New sheet inserted in '%s'"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_template_dialog.js:0
#, python-format
msgid "New sheet saved in Documents"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_service.js:0
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#, python-format
msgid "New spreadsheet created in Documents"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet_template/spreadsheet_template_action.js:0
#, python-format
msgid "New spreadsheet template created"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Next"
msgstr "Nākamais"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "No match."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "None"
msgstr "Nav"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Not equal."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Not implemented operator %s for kind of conditional formatting:  %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_cache.js:0
#, python-format
msgid "Not implemented: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "November"
msgstr "Novembris"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Nth largest element from a data set."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Nth smallest element in a data set."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number"
msgstr "Numurs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number (1,000.12)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of columns in a specified array or range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of days between two dates."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of empty values."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of periods for an investment to reach a value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of rows in a specified array or range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of working days from start date."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numbers"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numerical average value in a dataset, ignoring text."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numerical average value in a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "October"
msgstr "Oktobris"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "One number divided by another."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paint Format"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__parent_revision_id
msgid "Parent Revision"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste format only"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste special"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste value only"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste values only"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percent (10.12%)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percentage"
msgstr "Procentuālā attiecība"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percentile"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Pie"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Pivot name"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet_extended.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Pivot properties"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Please enter a valid sheet name"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "Please, close all other Odoo tabs and reload the current page."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Position of item in range that matches value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Positive square root of a positive number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Present value of an annuity investment."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Preview"
msgstr "Priekšskatīt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Preview text"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Previous"
msgstr "Iepriekšējais"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Price of a security paying periodic interest."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Product of two numbers"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Product of values from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Quarter"
msgstr "Ceturksnis"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Random integer between two values, inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Re-insert list"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Re-insert pivot"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/list_autofill_plugin.js:0
#, python-format
msgid "Record #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Redo"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Refresh"
msgstr "Atsvaidzināt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Refresh all data"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Refresh values"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Related Model"
msgstr "Saistītais modelis"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Relation"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "Reload"
msgstr "Pārlādēt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Remove"
msgstr "Noņemt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Remove link"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Removes space characters."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_name.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rename"
msgstr "Pārdēvēt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rename Sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replace"
msgstr "Aizstāt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replace all"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replaces existing text with new text in a string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replaces part of a text string with different text."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Result of multiplying a series of numbers together."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Return the current value of a spreadsheet filter."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns a value depending on multiple logical expressions."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns opposite of provided logical value."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Returns the maximum value in a range of cells, filtered by a set of "
"criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Returns the minimum value in a range of cells, filtered by a set of "
"criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns value depending on logical expression."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Reverse icons"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__revision_id
msgid "Revision"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_revision_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_revision
msgid "Revisions"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Right"
msgstr "Right"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds a number according to standard rules."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds a number up to the nearest odd integer."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds down a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds number down to nearest multiple of factor."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds number up to nearest multiple of factor."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds up a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row above"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row below"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row number of a specified cell."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Save"
msgstr "Saglabāt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
#, python-format
msgid "Save as Template"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Saved"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Saving"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Search"
msgstr "Meklēt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Search in formulas"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Secant of an angle provided in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "See records"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Select a color..."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select a menu..."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#, python-format
msgid "Select a spreadsheet to insert your list"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Select a spreadsheet to insert your pivot"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select an Odoo menu to link in your spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#, python-format
msgid "Select the number of records to insert"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "September"
msgstr "Septembris"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr "Secība"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Series"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift down"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift left"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift right"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift up"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Show formulas"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Show gridlines"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sine of an angle provided in radians."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Single color"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Single value from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Some required fields are not valid"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort column"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort columns"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort range"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#, python-format
msgid "Sorting"
msgstr ""

#. module: documents_spreadsheet
#: model:documents.folder,name:documents_spreadsheet.documents_spreadsheet_folder
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_revision_view_search
msgid "Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/documents_inspector.js:0
#, python-format
msgid ""
"Spreadsheets mass download not yet supported.\n"
" Download spreadsheets individually instead."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Stacked barchart"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population (text as 0)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population from table."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of population sample from table."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of sample (text as 0)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Starts with"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Strictly greater than."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Strikethrough"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Substring from beginning of specified string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of a series of numbers and/or cells."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of two numbers."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of values from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sums a range depending on multiple criteria."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Tangent of an angle provided in radians."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr "Sagataves nosaukums"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Tests whether two strings are identical."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Text"
msgstr "Text"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Text Color"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The anchor must be part of the provided zone"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "The argument %s is not a valid measure. Here are the measures: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The argument is missing. Please provide a value"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The base (%s) must be between 2 and 36 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cashflow_amounts must include negative and positive values."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cell you are trying to edit has been deleted."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The chart definition is invalid for an unknown reason"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cost (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The covariance of a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The criteria range contains %s row, it must be at least 2 rows."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The dataset is invalid"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The date_string (%s) cannot be parsed to date/time."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The day_count_convention (%s) must be between 0 and 4 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The day_count_convention (%s) must between 0 and 4 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The discount (%s) must be different from -1."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The divisor must be different from 0."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The divisor must be different from zero."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The end_date (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The exponent (%s) must be an integer when the base is negative."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The factor (%s) must be positive when the value (%s) is positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/list_data_source.js:0
#, python-format
msgid "The field %s does not exist or you do not have access to that field"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The field (%s) must be one of %s or must be a number between 1 and %s "
"inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The field (%s) must be one of %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The field must be a number or a string"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The first value must be a number"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The frequency (%s) must be one of %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The frequency (%s) must be one of %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] expects a boolean value, but '%s' is a text, "
"and cannot be coerced to a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] expects a number value, but '%s' is a string,"
" and cannot be coerced to a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The function [[FUNCTION_NAME]] result cannot be negative"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] result must be greater than or equal "
"01/01/1900."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The future_value (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The high (%s) must be greater than or equal to the low (%s)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The life (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The logarithm of a number, base e (euler's number)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The maturity (%s) must be strictly greater than the settlement (%s)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The maxpoint must be a number"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The midpoint must be a number"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The minpoint must be a number"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The month (%s) must be between 1 and 12 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The net present value of an investment based on a series of periodic cash "
"flows and a discount rate."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number of numeric values in dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number of values in a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number pi."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number_of_characters (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The occurrenceNumber (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The period (%s) must be less than or equal to %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The period (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The position (%s) must be greater than or equal to 1."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The present_value (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The price (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The range is invalid"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The range must be a single row or a single column."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate_guess (%s) must be strictly greater than -1."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The redemption (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The result_range must be a single row or a single column."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rule is invalid for an unknown reason"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The salvage (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The sample covariance of a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The second argument is missing. Please provide a value"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The second value must be a number"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The settlement (%s) must be greater than or equal to the issue (%s)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The start_date (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The starting_at (%s) must be greater than or equal to 1."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The table_number (%s) is out of range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The text_to_search must be non-empty."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The time_string (%s) cannot be parsed to date/time."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The type (%s) is out of range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The type (%s) must be 1, 2 or 3."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) cannot be between -1 and 1 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be a valid base %s representation."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be between -1 and 1 exclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be between -1 and 1 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be greater than or equal to 1."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be strictly positive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The weekend (%s) must be a number or a string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The weekend (%s) must be a string or a number in the range 1-7 or 11-17."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The weekend (%s) must be different from '1111111'."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The year (%s) must be between 0 and 9999 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The yield (%s) must be positive or null."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "There is no pivot with id \"%s\""
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "This formula depends on invalid values"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"This formula has over 100 parts. It can't be processed properly, consider "
"splitting it into multiple cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "This operation is not allowed with multiple selections."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"This operation is not possible due to a merge. Please remove the merges "
"first than try again."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Time (10:43:00 PM)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Time Range"
msgstr "Laika diapozons"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Title"
msgstr "Nosaukums"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Toggle favorite"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Top"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_pivot_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Total"
msgstr "Summa"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Truncates a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Type"
msgstr "Tips"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_data_source.js:0
#, python-format
msgid "Unable to fetch the label of %s of model %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Underline"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Undo"
msgstr "Atsaukt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unexpected token: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unhide all columns"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unhide all rows"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_cache.js:0
#, python-format
msgid "Unknown operator: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unknown token: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unmatched left parenthesis"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/constants.js:0
#, python-format
msgid "Untitled spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet_template/spreadsheet_template_service.js:0
#, python-format
msgid "Untitled spreadsheet template"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Update chart"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr "Lietotājs"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value"
msgstr "Vērtība"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value at a given percentile of a dataset exclusive of 0 and 1."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value at a given percentile of a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value if it is not an error, otherwise 2nd argument."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value interpreted as a percentage."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Value nearest to a specific quartile of a dataset exclusive of 0 and 4."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value nearest to a specific quartile of a dataset."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of a population from a table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of entire population (text as 0)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of entire population."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of population sample from table-like range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of sample (text as 0)."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Vertical axis position"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Vertical lookup."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "View"
msgstr "Skatīt"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"We found data next to your selection. Since this data was not selected, it "
"will not be sorted. Do you want to extend your selection?"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#, python-format
msgid "Week"
msgstr "Nedēļa"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Week number of the year."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Weighted average."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "When value is"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "When weekend is a string (%s) it must be composed of \"0\" or \"1\"."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is `true` or `false`."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is a number."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is an error."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is non-textual."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is text."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether the provided value is even."
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong function call"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong number of Argument[]. Expected an even number of Argument[]."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong number of arguments. Expected an even number of arguments."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Year"
msgstr "Gads"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Year specified by a given date."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "[[FUNCTION_NAME]] cannot be called from the spreadsheet."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of bounds range."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of range column value %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of range row value %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] expects number values."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] expects the weight to be positive or equal to 0."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has mismatched argument count %s vs %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has mismatched range sizes."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has no valid input data."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "and"
msgstr "and"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "by default"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot_dialog.xml:0
#, python-format
msgid "has no cell missing from this sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.js:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.js:0
#, python-format
msgid "never"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_revision_parent_revision_unique
msgid "o-spreadsheet revision refused due to concurrency"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "optional"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "records of the list."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "repeatable"
msgstr ""
