# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# Odo<PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>wu<PERSON><PERSON> Jaengsawang <<EMAIL>>, 2023
# <PERSON>, 2023
# Wil Odoo, 2023
# Ped Sansiri, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-20 12:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>ppiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: documents_spreadsheet
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
#, python-format
msgid "\"%s\" saved as template"
msgstr "บันทึก \"%s\" เป็นเทมเพลตแล้ว"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_page
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.share_single
msgid ""
"#{ isSpreadsheet and 'Odoo Spreadsheets not available for download' or ''}"
msgstr ""

#. module: documents_spreadsheet
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Columns left"
msgstr "%s คอลัมน์ด้านซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Columns right"
msgstr "%s คอลัมน์ด้านขวา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Rows above"
msgstr "%s แถวด้านบน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "%s Rows below"
msgstr "%s แถวด้านล่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "(List #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "(Pivot #"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "(Undefined)"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid ""
"<strong>Spreadsheets</strong>\n"
"                            <span class=\"fa fa-lg fa-building-o ml-1\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr "มีสเปรดชีตและผู้ใช้ผสมกันอยู่แล้ว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A conditional count across a range."
msgstr "การนับแบบมีเงื่อนไขในช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A conditional sum across a range."
msgstr "ผลรวมตามเงื่อนไขในช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A dataset needs to be defined"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A number raised to a power."
msgstr "ตัวเลขยกกำลัง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A number with the sign reversed."
msgstr "ตัวเลขที่มีเครื่องหมายกลับด้าน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A random number between 0 inclusive and 1 exclusive."
msgstr "ตัวเลขสุ่มระหว่าง 0 รวมและ 1 ไม่รวม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A range needs to be defined"
msgstr "จำเป็นต้องกำหนดช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A specified number, unchanged."
msgstr "หมายเลขที่ระบุไม่มีการเปลี่ยนแปลง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "A substring from the end of a specified string."
msgstr "สตริงย่อยจากส่วนท้ายของสตริงที่ระบุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "ABOUT"
msgstr "เกี่ยวกับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Absolute value of a number."
msgstr "ค่าสัมบูรณ์ของตัวเลข"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Add a new filter..."
msgstr "เพิ่มตัวกรองใหม่..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Add another rule"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Also modify formulas"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "An issue occurred while auto-saving"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Angle from the X axis to a point (x,y), in radians."
msgstr "มุมจากแกน X ถึงจุด (x,y) มีหน่วยเป็นเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Annual yield of a security paying interest at maturity."
msgstr "อัตราผลตอบแทนรายปีของหลักทรัพย์ที่จ่ายดอกเบี้ยเมื่อครบกำหนด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Annual yield of a security paying periodic interest."
msgstr "อัตราผลตอบแทนประจำปีของหลักทรัพย์ที่จ่ายดอกเบี้ยเป็นงวด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Anonymous"
msgstr "ผู้ใช้นิรนาม"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid ""
"Any user will be able to create a new spreadsheet based on this template."
msgstr "ผู้ใช้จะสามารถสร้างสเปรดชีตใหม่โดยใช้เทมเพลตนี้ได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Appends strings to one another."
msgstr "ผนวกสตริงเข้าด้วยกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "April"
msgstr "เมษายน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Are you sure you want to delete this sheet ?"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Argument must be a reference to a cell or range."
msgstr "ข้อโต้แย้งจะต้องอ้างอิงถึงเซลล์หรือช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Ascending (A ⟶ Z)"
msgstr "เรียงลำดับ (A ⟶ Z)"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Ask an admin to configure the workspace to be accessible to the users you "
"want."
msgstr ""
"ขอให้ผู้ดูแลระบบกำหนดค่าของเวิร์กสเปซเพื่อให้ผู้ใช้ที่คุณต้องการสามารถเข้าถึงได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "August"
msgstr "สิงหาคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average magnitude of deviations from mean."
msgstr "ขนาดเฉลี่ยของการเบี่ยงเบนจากค่าเฉลี่ย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of a set of values from a table-like range."
msgstr "ค่าเฉลี่ยของชุดค่าจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of values depending on criteria."
msgstr "ค่าเฉลี่ยขึ้นอยู่กับเกณฑ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Average of values depending on multiple criteria."
msgstr "ค่าเฉลี่ยของค่าขึ้นอยู่กับหลายเกณฑ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_all_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_list_side_panel.xml:0
#, python-format
msgid "Back"
msgstr "กลับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Background color"
msgstr "สีพื้นหลัง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bar"
msgstr "แถบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#, python-format
msgid "Blank"
msgstr "ว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bold"
msgstr "ตัวหนา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Borders"
msgstr "ขอบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Bottom"
msgstr "Bottom"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"By default, the spreadsheets in this workspace will only be seen and updated"
" by their <strong>creator</strong>."
msgstr ""
"ตามค่าเริ่มต้น สเปรดชีตในเวิร์กสเปซนี้จะเห็นและอัปเดตโดย "
"<strong>ผู้สร้าง</strong> เท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Cancel"
msgstr "ยกเลิก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cannot dispatch commands in the finalize state"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Cannot sort. To sort, select only cells or only merges that have the same "
"size."
msgstr ""
"ไม่สามารถจัดเรียงได้ เมื่อต้องการเรียงลำดับ "
"ให้เลือกเฉพาะเซลล์หรือเฉพาะการผสานที่มีขนาดเท่ากัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Categories / Labels"
msgstr "หมวดหมู่ / ป้ายกำกับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cell values"
msgstr "ค่าของเซลล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Center"
msgstr "ตรงกลาง"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr "รวมศูนย์สเปรดชีตของคุณ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Chart"
msgstr "แผนภูมิ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Chart type"
msgstr "ประเภทแผนภูมิ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Circular reference"
msgstr "การอ้างอิงแบบวงกลม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear Format"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear column %s"
msgstr "ล้างคอลัมน์ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear columns %s - %s"
msgstr "ล้างคอลัมน์ %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear row %s"
msgstr "ล้างแถว %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Clear rows %s - %s"
msgstr "ล้างแถว %s - %s"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_revision
msgid "Collaborative spreadsheet revision"
msgstr "การแก้ไขสเปรดชีตร่วมกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Color scale"
msgstr "ระดับสี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column left"
msgstr "คอลัมน์ด้านซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column number of a specified cell."
msgstr "หมายเลขคอลัมน์ของเซลล์ที่ระบุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Column right"
msgstr "คอลัมน์ด้านขวา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Combines text from multiple strings and/or arrays."
msgstr "รวมข้อความจากสตริงและ/หรืออาร์เรย์หลายรายการ"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__commands
msgid "Commands"
msgstr "คำสั่ง"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Computes the number of periods needed for an investment to reach a value."
msgstr "คำนวณจำนวนงวดที่จำเป็นสำหรับการลงทุนเพื่อให้ได้มูลค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Concatenates elements of arrays with delimiter."
msgstr "เชื่อมต่อองค์ประกอบของอาร์เรย์ด้วยตัวคั่น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Concatenation of two values."
msgstr "การต่อกันของสองค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Conditional formatting"
msgstr "การจัดรูปแบบตามเงื่อนไข"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "การตั้งค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
#, python-format
msgid "Confirm"
msgstr "ยืนยัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Contains"
msgstr "ประกอบด้วย"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr "ผู้ให้ข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a date string to a date value."
msgstr "แปลงสตริงวันที่ให้เป็นค่าวันที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a specified string to lowercase."
msgstr "แปลงสตริงที่ระบุเป็นตัวพิมพ์เล็ก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a specified string to uppercase."
msgstr "แปลงสตริงที่ระบุเป็นตัวพิมพ์ใหญ่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts a time string into its serial number representation."
msgstr "แปลงสตริงเวลาเป็นการแสดงหมายเลขซีเรียล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts an angle value in radians to degrees."
msgstr "แปลงค่ามุมในหน่วยเรเดียนเป็นองศา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts from another base to decimal."
msgstr "แปลงจากฐานอื่นเป็นทศนิยม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts hour/minute/second into a time."
msgstr "แปลง ชั่วโมง/นาที/วินาที เป็นเวลา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Converts year/month/day into a date."
msgstr "แปลงปี/เดือน/วันเป็นวันที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Copy"
msgstr "คัดลอก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Copy of %s"
msgstr "สำเนาของ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cosecant of an angle provided in radians."
msgstr "โคซีแคนต์ของมุมเป็นหน่วยเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cosine of an angle provided in radians."
msgstr "โคไซน์ของมุมเป็นหน่วยเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cotangent of an angle provided in radians."
msgstr "โคแทนเจนต์ของมุมเป็นหน่วยเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Count"
msgstr "จำนวน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Count values depending on multiple criteria."
msgstr "นับค่าโดยขึ้นอยู่กับเกณฑ์หลายข้อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Counts number of unique values in a range, filtered by a set of criteria."
msgstr "นับจำนวนค่าที่ไม่ซ้ำกันในช่วง โดยกรองตามชุดเกณฑ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts number of unique values in a range."
msgstr "นับจำนวนของค่าที่ไม่ซ้ำกันในช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts values and text from a table-like range."
msgstr "นับค่าและข้อความจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Counts values from a table-like range."
msgstr "นับค่าจากช่วงที่มีลักษณะคล้ายตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/template_dialog.xml:0
#, python-format
msgid "Create"
msgstr "สร้าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/documents_views.xml:0
#, python-format
msgid "Create Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Create chart"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Current date and time as a date value."
msgstr "วันที่และเวลาปัจจุบันเป็นค่าวันที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Current date as a date value."
msgstr "วันที่ปัจจุบันเป็นค่าวันที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Cut"
msgstr "ตัด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__data
#, python-format
msgid "Data"
msgstr "ข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Data Series"
msgstr "ชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Data series include title"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Date"
msgstr "วันที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date (9/26/2008)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date a number of months before/after another date."
msgstr "วันที่ของจำนวนเดือนก่อน/หลังวันที่อื่น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Date time (9/26/2008 22:43:00)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#, python-format
msgid "Day"
msgstr "วัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Day of the month that a specific date falls on."
msgstr "วันของเดือนที่มีวันที่ระบุเจาะจง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Day of the week of the date provided (as number)."
msgstr "วันในสัปดาห์ของวันที่ที่ระบุ (ตามตัวเลข)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "December"
msgstr "ธันวาคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Decrease decimal places"
msgstr "ลดตำแหน่งทศนิยม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Default Value"
msgstr "ค่าเริ่มต้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete"
msgstr "ลบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cell and shift left"
msgstr "ลบเซลล์และเลื่อนไปทางซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cell and shift up"
msgstr "ลบเซลล์และเลื่อนขึ้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete cells"
msgstr "ลบเซลล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete column %s"
msgstr "ลบคอลัมน์ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete columns %s - %s"
msgstr "ลบคอลัมน์ %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete row %s"
msgstr "ลบแถว %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete rows %s - %s"
msgstr "ลบแถว %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Delete values"
msgstr "ลบค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Depreciation via declining balance method."
msgstr "ค่าเสื่อมราคาโดยวิธียอดลดลง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Descending (Z ⟶ A)"
msgstr "เรียงลำดับจาก (Z ⟶ A)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Did not find value '%s' in [[FUNCTION_NAME]] evaluation."
msgstr "ไม่พบค่า '%s' ในการประเมิน [[FUNCTION_NAME]]"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Difference of two numbers."
msgstr "ผลต่างของตัวเลขสองตัว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Dimensions"
msgstr "มิติ"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot_dialog.xml:0
#, python-format
msgid "Display missing cells only"
msgstr "แสดงเฉพาะเซลล์ที่หายไป"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__document_id
msgid "Document"
msgstr "เอกสาร"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__documents_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__documents_spreadsheet_folder_id
msgid "Documents Spreadsheet Folder"
msgstr "โฟลเดอร์สเปรดชีตเอกสาร"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Does not contain"
msgstr "ไม่มี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Domain"
msgstr "โดเมน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Download"
msgstr "ดาวน์โหลด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Duplicate"
msgstr "ซ้ำ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Duplicated Label"
msgstr "ป้ายกำกับที่ซ้ำกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Duration (27:51:38)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
#, python-format
msgid "Edit"
msgstr "แก้ไข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Edit link"
msgstr "แก้ไขลิงก์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Else"
msgstr "อื่น ๆ "

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Ends with"
msgstr "ลงท้ายด้วย"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Enjoy collaborative work on your spreadsheets."
msgstr "เพลิดเพลินกับการทำงานร่วมกันบนสเปรดชีตของคุณ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Equal."
msgstr "เท่ากัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Euler's number, e (~2.718) raised to a power."
msgstr "เลขออยเลอร์ e (~2.718) ยกกำลัง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Evaluation of function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr ""
"การประเมินฟังก์ชัน [[FUNCTION_NAME]] ทำให้เกิดข้อผิดพลาดในการหารด้วยศูนย์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Exact number of years between two dates."
msgstr "จำนวนปีที่แน่นอนระหว่างวันที่สองวัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Exponential"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "February"
msgstr "กุมภาพันธ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Field Matching"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "File"
msgstr "ไฟล์"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__raw
msgid "File Content (raw)"
msgstr "เนื้อหาไฟล์ (raw)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Fill Color"
msgstr "เติมสี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/filters_evaluation_plugin.js:0
#, python-format
msgid "Filter \"%s\" not found"
msgstr "ไม่พบตัวกรอง \"%s\""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Filter properties"
msgstr "คุณสมบัติการกรอง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/global_filters_side_panel.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Filters"
msgstr "ตัวกรอง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Find and Replace"
msgstr "ค้นหาและแทนที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Find and replace"
msgstr "ค้นหาและแทนที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "First position of string found in text, case-sensitive."
msgstr "พบตำแหน่งแรกของสตริงในข้อความ คำนึงถึงตัวพิมพ์เล็กและตัวพิมพ์ใหญ่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "First position of string found in text, ignoring case."
msgstr ""
"พบตำแหน่งแรกของสตริงในข้อความ โดยไม่คำนึงถึงตัวพิมพ์เล็กและตัวพิมพ์ใหญ่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Font Size"
msgstr "ขนาดฟอนต์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Font size"
msgstr "ขนาดฟอนต์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format"
msgstr "รูปแบบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format as percent"
msgstr "จัดรูปแบบเป็นเปอร์เซ็นต์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format cells if..."
msgstr "จัดรูปแบบเซลล์ถ้า..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Format rules"
msgstr "กฎรูปแบบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Formatting style"
msgstr "การจัดรูปแบบสไตล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Formula"
msgstr "สูตร"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has an argument that has been declared with more than one "
"type whose type 'META'. The 'META' type can only be declared alone."
msgstr ""
"ฟังก์ชัน ${name} "
"มีข้อโต้แย้งที่ได้รับการประกาศให้มีมากกว่าหนึ่งประเภทซึ่งมีประเภท 'META' "
"ประเภท \"META\" สามารถประกาศได้เพียงอย่างเดียว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has at mandatory arguments declared after optional ones. "
"All optional arguments must be after all mandatory arguments."
msgstr ""
"ฟังก์ชัน ${name} มีที่ข้อโต้แย้งบังคับที่ประกาศหลังข้อโต้แย้งทางเลือก "
"ข้อโต้แย้งทางเลือกทั้งหมดต้องอยู่หลังข้อโต้แย้งบังคับทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function ${name} has no-repeatable arguments declared after repeatable ones."
" All repeatable arguments must be declared last."
msgstr ""
"ฟังก์ชัน ${name} "
"มีการประกาศข้อโต้แย้งที่ทำซ้ำไม่ได้หลังจากข้อโต้แย้งที่ทำซ้ำได้ "
"ข้อโต้แย้งที่ทำซ้ำได้ทั้งหมดจะต้องประกาศเป็นลำดับสุดท้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects its parameters to be single values or single cell "
"references, not ranges."
msgstr ""
"ฟังก์ชัน %s คาดว่าพารามิเตอร์จะเป็นค่าเดียวหรือการอ้างอิงเซลล์เดียว "
"ไม่ใช่ช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects the parameter %s to be a single value or a single cell "
"reference, not a range."
msgstr ""
"ฟังก์ชัน %s คาดว่าพารามิเตอร์ %s จะเป็นค่าเดียวหรือการอ้างอิงเซลล์เดียว "
"ไม่ใช่ช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function %s expects the parameter %s to be reference to a cell or range, not"
" a %s."
msgstr "ฟังก์ชัน %s คาดว่าพารามิเตอร์ %s จะอ้างอิงถึงเซลล์หรือช่วง ไม่ใช่ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Function PIVOT takes an even number of arguments."
msgstr "ฟังก์ชัน PIVOT รับข้อโต้แย้งเป็นจำนวนคู่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] caused a divide by zero error."
msgstr "ฟังก์ชัน [[FUNCTION_NAME]] ทำให้เกิดข้อผิดพลาดในการหารด้วยศูนย์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] didn't find any result"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range and criterion to be in "
"pairs."
msgstr "ฟังก์ชัน [[FUNCTION_NAME]] คาดว่า criteria_range และเกณฑ์จะเป็นคู่กัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Function [[FUNCTION_NAME]] expects criteria_range to have the same dimension"
msgstr "ฟังก์ชัน [[FUNCTION_NAME]] คาดว่า criteria_range จะมีมิติเดียวกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] parameter 2 value (%s) is out of range."
msgstr "ฟังก์ชัน [[FUNCTION_NAME]] ค่าพารามิเตอร์ 2 (%s) อยู่นอกช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Function [[FUNCTION_NAME]] parameter 2 value is out of range."
msgstr "ฟังก์ชั่น [[FUNCTION_NAME]] ค่าพารามิเตอร์ 2 อยู่นอกช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Future value of an annuity investment."
msgstr "มูลค่าในอนาคตของการลงทุนรายปี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "General"
msgstr "ทั่วไป"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "General (no specific format)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the absolute ID of an element in the pivot"
msgstr "รับ ID สัมบูรณ์ขององค์ประกอบในสาระสำคัญ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/list_functions.js:0
#, python-format
msgid "Get the header of a list."
msgstr "รับส่วนหัวของรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the header of a pivot."
msgstr "รับส่วนหัวของสาระสำคัญ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/list_functions.js:0
#, python-format
msgid "Get the value from a list."
msgstr "รับค่าจากรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Get the value from a pivot."
msgstr "รับค่าจากสาระสำคัญ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Gets character associated with number."
msgstr "รับตัวอักษรที่เกี่ยวข้องกับตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Greater than or equal to."
msgstr "มากกว่าหรือเท่ากับ"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr "ตัวจัดการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide column %s"
msgstr "ซ่อนคอลัมน์ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide columns"
msgstr "ซ่อนคอลัมน์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide columns %s - %s"
msgstr "ซ่อนคอลัมน์ %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide formulas"
msgstr "ซ่อนสูตร"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide gridlines"
msgstr "ซ่อนเส้นตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide row %s"
msgstr "ซ่อนแถว %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide rows"
msgstr "ซ่อนแถว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hide rows %s - %s"
msgstr "ซ่อนแถว %s - %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Horizontal align"
msgstr "จัดแนวนอน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Horizontal lookup"
msgstr "การค้นหาแนวนอน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hour component of a specific time."
msgstr "ส่วนประกอบชั่วโมงของเวลาที่กำหนด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cosecant of any real number."
msgstr "จำนวนจริงของไฮเปอร์โบลิกโคซีแคนต์ใดๆ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cosine of any real number."
msgstr "จำนวนจริงของโคไซน์ไฮเปอร์โบลิกใดๆ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic cotangent of any real number."
msgstr "จำนวนจริงของโคแทนเจนต์ไฮเปอร์โบลิกใดๆ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic secant of any real number."
msgstr "จำนวนจริงของซีแคนท์ไฮเปอร์โบลิกใดๆ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic sine of any real number."
msgstr "จำนวนจริงของไฮเปอร์โบลิกไซน์ใดๆ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Hyperbolic tangent of any real number."
msgstr "จำนวนจริงของแทนเจนต์ไฮเปอร์โบลิกใดๆ"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr "รหัส"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "ISO week number of the year."
msgstr "หมายเลขสัปดาห์ ISO ของปี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icon Set"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icon set"
msgstr "ชุดไอคอน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Icons"
msgstr "ไอคอน"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "If you want to work together on those spreadsheets :"
msgstr "หากคุณต้องการทำงานร่วมกันบนสเปรดชีตเหล่านั้น ให้ทำดังนี้:"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "In [[FUNCTION_NAME]] evaluation, cannot find '%s' within '%s'."
msgstr "ในการประเมิน [[FUNCTION_NAME]] ไม่พบ '%s' ภายใน '%s'"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Increase decimal places"
msgstr "เพิ่มตำแหน่งทศนิยม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert"
msgstr "แทรก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns"
msgstr "แทรก %s คอลัมน์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns left"
msgstr "แทรก %s คอลัมน์ทางซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s columns right"
msgstr "แทรก %s คอลัมน์ทางขวา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows"
msgstr "แทรก %s แถว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows above"
msgstr "แทรก %s แถวด้านบน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert %s rows below"
msgstr "แทรก %s แถวด้านล่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells"
msgstr "แทรกเซลล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells and shift down"
msgstr "แทรกเซลล์และเลื่อนลง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert cells and shift right"
msgstr "แทรกเซลล์และเลื่อนไปทางขวา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column"
msgstr "แทรกคอลัมน์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column left"
msgstr "แทรกคอลัมน์ทางซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert column right"
msgstr "แทรกคอลัมน์ทางขวา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#: code:addons/documents_spreadsheet/static/src/xml/pivot.xml:0
#, python-format
msgid "Insert in Spreadsheet"
msgstr "แทรกในสเปรดชีต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert link"
msgstr "แทรกลิงก์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/insert_list_spreadsheet_menu.xml:0
#, python-format
msgid "Insert list in spreadsheet"
msgstr "แทรกรายการในสเปรดชีต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Insert pivot cell"
msgstr "แทรกเซลล์ pivot"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row"
msgstr "แทรกแถว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row above"
msgstr "แทรกแถวด้านบน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Insert row below"
msgstr "แทรกแถวด้านล่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "Insert the first"
msgstr "ใส่อันแรก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Internal rate of return given periodic cashflows."
msgstr "อัตราผลตอบแทนภายในตามกระแสเงินสดเป็นงวด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Maxpoint formula"
msgstr "สูตร Maxpoint ไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Midpoint formula"
msgstr "สูตร Midpoint ไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid Minpoint formula"
msgstr "สูตร Minpoint ไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid expression"
msgstr "ตัวสั่งงานไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid formula"
msgstr "สูตรไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid lower inflection point formula"
msgstr "สูตรจุดเปลี่ยนเว้าล่างไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected %s maximum, but "
"got %s instead."
msgstr ""
"จำนวนอาร์กิวเมนต์สำหรับฟังก์ชัน %s ไม่ถูกต้อง คาดว่าจะมี %s สูงสุด แต่ได้ %s"
" แทน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected %s minimum, but "
"got %s instead."
msgstr ""
"จำนวนอาร์กิวเมนต์สำหรับฟังก์ชัน %s ไม่ถูกต้อง คาดว่าจะมี %s ขั้นต่ำ แต่ได้ "
"%s แทน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Invalid number of arguments for the %s function. Expected all arguments "
"after position %s to be supplied by groups of %s arguments"
msgstr ""
"จำนวนอาร์กิวเมนต์สำหรับฟังก์ชัน %s ไม่ถูกต้อง "
"คาดว่าอาร์กิวเมนต์ทั้งหมดหลังจากตำแหน่ง %s "
"จะถูกจัดเตรียมโดยกลุ่มของอาร์กิวเมนต์ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid reference"
msgstr "การอ้างอิงไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet"
msgstr "แผ่นงานไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet name"
msgstr "ชื่อแผ่นงานไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid sheet name: %s"
msgstr "ชื่อแผ่นงานไม่ถูกต้อง: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Invalid upper inflection point formula"
msgstr "สูตรจุดเปลี่ยนเว้าบนไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse cosine of a value, in radians."
msgstr "โคไซน์ผกผันของค่ามีหน่วยเป็นเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse cotangent of a value."
msgstr "โคแทนเจนต์ผกผันของค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic cosine of a number."
msgstr "โคไซน์ไฮเปอร์โบลิกผกผันของตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic cotangent of a value."
msgstr "โคแทนเจนต์ไฮเปอร์โบลิกผกผันของค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic sine of a number."
msgstr "ไซน์ไฮเปอร์โบลิกผกผันของตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse hyperbolic tangent of a number."
msgstr "แทนเจนต์ไฮเปอร์โบลิกผกผันของตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse sine of a value, in radians."
msgstr "ไซน์ผกผันของค่า มีหน่วยเป็นเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Inverse tangent of a value, in radians."
msgstr "แทนเจนต์ผกผันของค่า มีหน่วยเป็นเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is between"
msgstr "อยู่ระหว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is empty"
msgstr "ว่างเปล่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is equal to"
msgstr "เท่ากับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is greater than"
msgstr "มีค่ามากกว่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is greater than or equal to"
msgstr "มากกว่าหรือเท่ากับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is less than"
msgstr "น้อยกว่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is less than or equal to"
msgstr "น้อยกว่าหรือเท่ากับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not between"
msgstr "ไม่อยู่ระหว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not empty"
msgstr "ไม่ว่างเปล่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Is not equal to"
msgstr "ไม่เท่ากับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Italic"
msgstr "ตัวอักษรเส้นเอียง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "January"
msgstr "มกราคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "July"
msgstr "กรกฎาคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "June"
msgstr "มิถุนายน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Label"
msgstr "ป้ายกำกับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Labels are invalid"
msgstr "ป้ายกำกับไม่ถูกต้อง"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision____last_update
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Last day of a month before or after a date."
msgstr "วันสุดท้ายของเดือนก่อนหรือหลังวันที่"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr "วันที่อัปเดตล่าสุด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Last updated at"
msgstr "อัปเดตล่าสุดเมื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Left"
msgstr "Left"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Legend position"
msgstr "ตำแหน่ง Legend"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Length of a string."
msgstr "ความยาวของสตริง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Less than or equal to."
msgstr "น้อยกว่าหรือเท่ากับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Less than."
msgstr "น้อยกว่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Line"
msgstr "Line"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Linear"
msgstr "เชิงเส้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Link"
msgstr "ลิงค์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/odoo_menu_link_cell.js:0
#, python-format
msgid "Link an Odoo menu"
msgstr "ลิงก์เมนู Odoo"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/insert_action_link_menu/insert_action_link_menu.xml:0
#, python-format
msgid "Link menu in spreadsheet"
msgstr "เมนูลิงก์ในสเปรดชีต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Link sheet"
msgstr "แผ่นลิงค์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#, python-format
msgid "List Name"
msgstr "รายชื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet_extended.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "List properties"
msgstr "คุณสมบัติรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Loading..."
msgstr "กำลังโหลด..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logarithmic"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `and` operator."
msgstr "ตัวดำเนินการเชิงตรรกะ `และ`"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `or` operator."
msgstr "ตัวดำเนินการเชิงตรรกะ `หรือ`"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Logical `xor` operator."
msgstr "ตัวดำเนินการเชิงตรรกะ `xor` "

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Look up a value."
msgstr "ค้นหาค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Lower inflection point must be smaller then upper inflection point"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
#, python-format
msgid "Make a copy"
msgstr "ทำสำเนา"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid ""
"Manage and work with all the <strong>spreadsheets</strong> created in other "
"applications."
msgstr ""
"จัดการและทำงานกับ<strong>สเปรดชีต</strong>ทั้งหมดที่สร้างขึ้นในแอปพลิเคชันอื่น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "March"
msgstr "มีนาคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Match case"
msgstr "กรณีการจับคู่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Match entire cell content"
msgstr "จับคู่เนื้อหาเซลล์ทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Match this filter to a field for each pivot/list"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum numeric value in a dataset."
msgstr "ค่าตัวเลขสูงสุดในชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum of values from a table-like range."
msgstr "ค่าสูงสุดจากช่วงที่มีลักษณะคล้ายตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maximum value in a numeric dataset."
msgstr "ค่าสูงสุดในชุดข้อมูลตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Maxpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "May"
msgstr "พฤษภาคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Measure"
msgstr "วัด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Measures"
msgstr "ตัวชี้วัด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Median value in a numeric dataset."
msgstr "ค่ามัธยฐานในชุดข้อมูลตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Menu Items"
msgstr "รายการเมนู"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Merge Cells"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Merged cells are preventing this operation. Unmerge those cells and try "
"again."
msgstr ""
"เซลล์ที่รวมเข้าด้วยกันกำลังขัดขวางการดำเนินการนี้ "
"เลิกรวมเซลล์เหล่านั้นแล้วลองอีกครั้ง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Merging these cells will only preserve the top-leftmost value. Merge anyway?"
msgstr "การรวมเซลล์เหล่านี้จะคงเฉพาะค่าบนซ้ายสุดเท่านั้น รวมแล้วใช่ไหม?"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Midpoint"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Midpoint must be smaller then Maximum"
msgstr "จุดกึ่งกลางต้องน้อยกว่าค่าสูงสุด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum must be smaller then Maximum"
msgstr "ค่าต่ำสุดต้องน้อยกว่าค่าสูงสุด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum must be smaller then Midpoint"
msgstr "ค่าต่ำสุดต้องน้อยกว่าจุดกึ่งกลาง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum numeric value in a dataset."
msgstr "ค่าตัวเลขขั้นต่ำในชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum of values from a table-like range."
msgstr "ค่าขั้นต่ำจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minimum value in a numeric dataset."
msgstr "ค่าต่ำสุดในชุดข้อมูลที่เป็นตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minpoint"
msgstr "จุดขั้นต่ำ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Minute component of a specific time."
msgstr "องค์ประกอบนาทีของเวลาที่กำหนด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/list_autofill_plugin.js:0
#, python-format
msgid "Missing list #%s"
msgstr "รายชื่อที่ขาดหายไป #%s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot"
msgstr "Pivot ที่ขาดหายไป"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#, python-format
msgid "Missing pivot #%s"
msgstr "Pivot ที่ขาดหายไป #%s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Model"
msgstr "โมเดล"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_ir_model
msgid "Models"
msgstr "โมเดล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Modified Macaulay duration."
msgstr "แก้ไขระยะเวลา Macaulay"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Modulo (remainder) operator."
msgstr "ตัวดำเนินการโมดูโล (ส่วนที่เหลือ)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Month"
msgstr "เดือน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Month of the year a specific date falls in"
msgstr "เดือนของปีที่มีวันที่ตรงกับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "More formats"
msgstr "รูปแบบเพิ่มเติม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "More than one match found in DGET evaluation."
msgstr "พบมากกว่าหนึ่งรายการที่ตรงกันในการประเมิน DGET"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Move left"
msgstr "เลื่อนไปทางซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Move right"
msgstr "เลื่อนไปทางขวา"

#. module: documents_spreadsheet
#: model_terms:documents.folder,description:documents_spreadsheet.documents_spreadsheet_folder
msgid "Move them to another workspace"
msgstr "ย้ายไปยังพื้นที่ทำงานอื่น"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr "เทมเพลตของฉัน"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr "ชื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Net working days between two dates (specifying weekends)."
msgstr "วันทำการสุทธิระหว่างวันที่สองวัน (ระบุวันหยุดสุดสัปดาห์)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Net working days between two provided days."
msgstr "วันทำการสุทธิระหว่างสองวันที่ให้ไว้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "New"
msgstr "ใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "New %s filter"
msgstr "ตัวกรอง %s ใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "New Chart"
msgstr "แผนภูมิใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_template_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "New Spreadsheet"
msgstr "สเปรดชีตใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "New sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_service.js:0
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#, python-format
msgid "New sheet inserted in '%s'"
msgstr "แผ่นงานใหม่ถูกแทรกใน '%s'"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_template_dialog.js:0
#, python-format
msgid "New sheet saved in Documents"
msgstr "แผ่นงานใหม่ที่บันทึกไว้ในเอกสาร"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr "สเปรดชีตใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet/spreadsheet_service.js:0
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#, python-format
msgid "New spreadsheet created in Documents"
msgstr "สเปรดชีตใหม่ที่สร้างในเอกสาร"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet_template/spreadsheet_template_action.js:0
#, python-format
msgid "New spreadsheet template created"
msgstr "สร้างเทมเพลตสเปรดชีตใหม่แล้ว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Next"
msgstr "ต่อไป"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "No match."
msgstr "ไม่มีการแข่งขัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "None"
msgstr "ไม่มี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Not equal."
msgstr "ไม่เท่ากับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Not implemented operator %s for kind of conditional formatting:  %s"
msgstr "ไม่ได้ใช้ตัวดำเนินการ %s สำหรับการจัดรูปแบบตามเงื่อนไข: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_cache.js:0
#, python-format
msgid "Not implemented: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "November"
msgstr "พฤศจิกายน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Nth largest element from a data set."
msgstr "องค์ประกอบที่ใหญ่เป็นอันดับ Nth จากชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Nth smallest element in a data set."
msgstr "องค์ประกอบที่เล็กที่สุดอันดับที่ N ในชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number"
msgstr "หมายเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number (1,000.12)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of columns in a specified array or range."
msgstr "จำนวนคอลัมน์ในอาร์เรย์หรือช่วงที่กำหนด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of days between two dates."
msgstr "จำนวนวันระหว่างวันที่สองวัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of empty values."
msgstr "จำนวนค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of periods for an investment to reach a value."
msgstr "จำนวนงวดที่การลงทุนจะถึงมูลค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of rows in a specified array or range."
msgstr "จำนวนแถวในอาร์เรย์หรือช่วงที่ระบุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Number of working days from start date."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numbers"
msgstr "จำนวน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numerical average value in a dataset, ignoring text."
msgstr "ค่าเฉลี่ยตัวเลขในชุดข้อมูล โดยไม่สนใจข้อความ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Numerical average value in a dataset."
msgstr "ค่าเฉลี่ยตัวเลขในชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "October"
msgstr "ตุลาคม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "One number divided by another."
msgstr "จำนวนหนึ่งหารด้วยอีกจำนวนหนึ่ง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paint Format"
msgstr "รูปแบบการทาสี"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__parent_revision_id
msgid "Parent Revision"
msgstr "การแก้ไขหลัก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste"
msgstr "วาง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste format only"
msgstr "วางรูปแบบเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste special"
msgstr "วางแบบพิเศษ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste value only"
msgstr "วางค่าเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Paste values only"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percent (10.12%)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percentage"
msgstr "ร้อยละ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Percentile"
msgstr "เปอร์เซ็นต์ไทล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Pie"
msgstr "พาย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Pivot name"
msgstr "ชื่อ Pivot"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet_extended.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Pivot properties"
msgstr "คุณสมบัติ Pivot"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Please enter a valid sheet name"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "Please, close all other Odoo tabs and reload the current page."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Position of item in range that matches value."
msgstr "ตำแหน่งของรายการในช่วงที่ตรงกับค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Positive square root of a positive number."
msgstr "รากที่สองที่เป็นบวกของจำนวนบวก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Present value of an annuity investment."
msgstr "มูลค่าปัจจุบันของการลงทุนรายปี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Preview"
msgstr "ดูตัวอย่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Preview text"
msgstr "แสดงตัวอย่างข้อความ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Previous"
msgstr "Previous"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Price of a security paying periodic interest."
msgstr "ราคาหลักทรัพย์ที่จ่ายดอกเบี้ยเป็นงวด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Product of two numbers"
msgstr "ผลคูณของตัวเลขสองตัว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Product of values from a table-like range."
msgstr "ผลคูณของค่าจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Quarter"
msgstr "ไตรมาส"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Random integer between two values, inclusive."
msgstr "จำนวนเต็มสุ่มระหว่างสองค่า รวมอยู่ด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Re-insert list"
msgstr "แทรกรายการอีกครั้ง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Re-insert pivot"
msgstr "ใส่ pivot อีกครั้ง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/list_autofill_plugin.js:0
#, python-format
msgid "Record #"
msgstr "บันทึก #"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Redo"
msgstr "ทำซ้ำ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Refresh"
msgstr "รีเฟรช"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "Refresh all data"
msgstr "รีเฟรชข้อมูลทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.xml:0
#, python-format
msgid "Refresh values"
msgstr "รีเฟรชค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Related Model"
msgstr "โมเดลที่เกี่ยวข้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Relation"
msgstr "ความสัมพันธ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/collaborative_cross_tab_bus_warning.js:0
#, python-format
msgid "Reload"
msgstr "โหลดใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Remove"
msgstr "ลบออก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Remove link"
msgstr "นำลิงก์ออก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Removes space characters."
msgstr "ลบตักอักษรที่เว้นวรรค"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_name.xml:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rename"
msgstr "ตั้งชื่อใหม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rename Sheet"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replace"
msgstr "แทนที่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replace all"
msgstr "แทนที่ทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replaces existing text with new text in a string."
msgstr "แทนที่ข้อความที่มีอยู่ด้วยข้อความใหม่ในสตริง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Replaces part of a text string with different text."
msgstr "แทนที่ส่วนหนึ่งของสตริงข้อความด้วยข้อความอื่น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Result of multiplying a series of numbers together."
msgstr "ผลลัพธ์ของการคูณชุดตัวเลขเข้าด้วยกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "Return the current value of a spreadsheet filter."
msgstr "ส่งคืนค่าปัจจุบันของตัวกรองสเปรดชีต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns a value depending on multiple logical expressions."
msgstr "ส่งกลับค่าโดยขึ้นอยู่กับตัวสั่งงานเชิงตรรกะหลายรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns opposite of provided logical value."
msgstr "ส่งกลับค่าตรงข้ามกับค่าตรรกะที่ระบุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Returns the maximum value in a range of cells, filtered by a set of "
"criteria."
msgstr "ส่งกลับค่าสูงสุดในช่วงของเซลล์ โดยกรองตามชุดเกณฑ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Returns the minimum value in a range of cells, filtered by a set of "
"criteria."
msgstr "ส่งกลับค่าต่ำสุดในช่วงของเซลล์ โดยกรองตามชุดเกณฑ์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Returns value depending on logical expression."
msgstr "ส่งกลับค่าขึ้นอยู่กับตัวสั่งงานเชิงตรรกะ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Reverse icons"
msgstr "ไอคอนย้อนกลับ"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_revision__revision_id
msgid "Revision"
msgstr "การแก้ไข"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_revision_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_revision
msgid "Revisions"
msgstr "การแก้ไข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Right"
msgstr "Right"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds a number according to standard rules."
msgstr "ปัดเศษตัวเลขตามกฎมาตรฐาน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds a number up to the nearest odd integer."
msgstr "ปัดเศษตัวเลขขึ้นให้เป็นจำนวนเต็มคี่ที่ใกล้ที่สุด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds down a number."
msgstr "ปัดเศษลง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds number down to nearest multiple of factor."
msgstr "ปัดเศษตัวเลขลงเป็นผลคูณที่ใกล้ที่สุดของตัวประกอบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds number up to nearest multiple of factor."
msgstr "ปัดเศษตัวเลขขึ้นเป็นผลคูณที่ใกล้ที่สุดของตัวประกอบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Rounds up a number."
msgstr "ปัดเศษขึ้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row above"
msgstr "แถวด้านบน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row below"
msgstr "แถวด้านล่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Row number of a specified cell."
msgstr "หมายเลขแถวของเซลล์ที่ระบุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Save"
msgstr "บันทึก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
#, python-format
msgid "Save as Template"
msgstr "บันทึกเป็นเทมเพลต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Saved"
msgstr "บันทึกแล้ว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Saving"
msgstr "บันทึก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Search"
msgstr "ค้นหา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Search in formulas"
msgstr "ค้นหาในสูตร"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Secant of an angle provided in radians."
msgstr "ซีแคนต์ของมุมที่ระบุเป็นเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/menu_item_registry.js:0
#, python-format
msgid "See records"
msgstr "ดูบันทึก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Select a color..."
msgstr "เลือกสี..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select a menu..."
msgstr "เลือกเมนู..."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/list_controller.js:0
#, python-format
msgid "Select a spreadsheet to insert your list"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/pivot_view.js:0
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.js:0
#, python-format
msgid "Select a spreadsheet to insert your pivot"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/ir_menu_selector/ir_menu_selector.js:0
#, python-format
msgid "Select an Odoo menu to link in your spreadsheet"
msgstr "เลือกเมนู Odoo เพื่อลิงก์ในสเปรดชีตของคุณ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_component.js:0
#, python-format
msgid "Select the number of records to insert"
msgstr "เลือกจำนวนเรคคอร์ดที่จะแทรก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_date_value.js:0
#, python-format
msgid "September"
msgstr "กันยายน"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Series"
msgstr "ซีรีส์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sheet"
msgstr "ชีต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift down"
msgstr "เลื่อนลง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift left"
msgstr "เลื่อนไปทางซ้าย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift right"
msgstr "เลื่อนไปทางขวา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Shift up"
msgstr "เลื่อนขึ้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Show formulas"
msgstr "แสดงสูตร"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Show gridlines"
msgstr "แสดงเส้นตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sine of an angle provided in radians."
msgstr "ไซน์ของมุมเป็นหน่วยเรเดียน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Single color"
msgstr "สีเดียว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Single value from a table-like range."
msgstr "ค่าเดียวจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/filter_editor_side_panel.js:0
#, python-format
msgid "Some required fields are not valid"
msgstr "ช่องที่ต้องกรอกบางช่องไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort column"
msgstr "จัดเรียงคอลัมน์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort columns"
msgstr "จัดเรียงคอลัมน์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sort range"
msgstr "จัดเรียงช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.xml:0
#, python-format
msgid "Sorting"
msgstr "การเรียงลำดับ"

#. module: documents_spreadsheet
#: model:documents.folder,name:documents_spreadsheet.documents_spreadsheet_folder
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_revision_view_search
msgid "Spreadsheet"
msgstr "สเปรดชีต"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr "ผู้ร่วมให้ข้อมูลสเปรดชีต"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr "ดูตัวอย่างสเปรดชีต"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr "การแก้ไขสเปรดชีต"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr "ภาพรวมสเปรดชีต"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr "เทมเพลตสเปรดชีต"

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr "โปรแกรมบันทึกเทมเพลตสเปรดชีต"

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr "เทมเพลตสเปรดชีต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/documents_inspector.js:0
#, python-format
msgid ""
"Spreadsheets mass download not yet supported.\n"
" Download spreadsheets individually instead."
msgstr ""
"ยังไม่รองรับการดาวน์โหลดสเปรดชีตจำนวนมาก\n"
"ดาวน์โหลดสเปรดชีตทีละรายการแทน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Stacked barchart"
msgstr "แผนภูมิแท่งแบบซ้อน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population (text as 0)."
msgstr "ค่าเบี่ยงเบนมาตรฐานของประชากรทั้งหมด (ข้อความเป็น 0)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population from table."
msgstr "ค่าเบี่ยงเบนมาตรฐานของประชากรทั้งหมดจากตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of entire population."
msgstr "ค่าเบี่ยงเบนมาตรฐานของประชากรทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of population sample from table."
msgstr "ค่าเบี่ยงเบนมาตรฐานของกลุ่มตัวอย่างประชากรจากตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation of sample (text as 0)."
msgstr "ค่าเบี่ยงเบนมาตรฐานของกลุ่มตัวอย่าง (ข้อความเป็น 0)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Standard deviation."
msgstr "ค่าเบี่ยงเบนมาตรฐาน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Starts with"
msgstr "เริ่มต้นด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Strictly greater than."
msgstr "ใหญ่กว่า.."

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Strikethrough"
msgstr "ขีดทับ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Substring from beginning of specified string."
msgstr "สตริงย่อยจากจุดเริ่มต้นของสตริงที่ระบุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of a series of numbers and/or cells."
msgstr "ผลรวมของชุดตัวเลขและ/หรือเซลล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of two numbers."
msgstr "ผลรวมของตัวเลขสองตัว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sum of values from a table-like range."
msgstr "ผลรวมของค่าจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Sums a range depending on multiple criteria."
msgstr "รวมช่วงโดยขึ้นอยู่กับเกณฑ์หลายรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Tangent of an angle provided in radians."
msgstr "แทนเจนต์ของมุมที่มีหน่วยเป็นเรเดียน"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr "ชื่อแม่แบบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Tests whether two strings are identical."
msgstr "ทดสอบว่าสตริงทั้งสองเหมือนกันหรือไม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Text"
msgstr "ข้อความ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Text Color"
msgstr "สีข้อความ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The anchor must be part of the provided zone"
msgstr "จุดยึดจะต้องเป็นส่วนหนึ่งของโซนที่จัดไว้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "The argument %s is not a valid measure. Here are the measures: %s"
msgstr "อาร์กิวเมนต์ %s ไม่ใช่การวัดที่ถูกต้อง ต่อไปนี้เป็นมาตรการ: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The argument is missing. Please provide a value"
msgstr "อาร์กิวเมนต์หายไป กรุณาระบุค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The base (%s) must be between 2 and 36 inclusive."
msgstr "ฐาน (%s) ต้องอยู่ระหว่างรวม 2 และ 36"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cashflow_amounts must include negative and positive values."
msgstr "cashflow_amounts ต้องมีค่าลบและค่าบวก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cell you are trying to edit has been deleted."
msgstr "เซลล์ที่คุณกำลังพยายามแก้ไขถูกลบไปแล้ว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The chart definition is invalid for an unknown reason"
msgstr "คำจำกัดความของแผนภูมิไม่ถูกต้องโดยไม่ทราบสาเหตุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The cost (%s) must be strictly positive."
msgstr "ต้นทุน (%s) จะต้องเป็นบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The covariance of a dataset."
msgstr "ความแปรปรวนร่วมของชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The criteria range contains %s row, it must be at least 2 rows."
msgstr "ช่วงเกณฑ์มี %s แถว ซึ่งต้องมีอย่างน้อย 2 แถว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The dataset is invalid"
msgstr "ชุดข้อมูลไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The date_string (%s) cannot be parsed to date/time."
msgstr "ไม่สามารถแยก date_string (%s) เป็นวันที่/เวลาได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The day_count_convention (%s) must be between 0 and 4 inclusive."
msgstr "day_count_convention (%s) ต้องอยู่ระหว่าง 0 ถึง 4"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The day_count_convention (%s) must between 0 and 4 inclusive."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The discount (%s) must be different from -1."
msgstr "ส่วนลด (%s) ต้องแตกต่างจาก -1"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The divisor must be different from 0."
msgstr "ตัวหารจะต้องแตกต่างจาก 0"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The divisor must be different from zero."
msgstr "ตัวหารจะต้องแตกต่างจากศูนย์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The end_date (%s) must be positive or null."
msgstr "end_date (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The exponent (%s) must be an integer when the base is negative."
msgstr "เลขยกกำลัง (%s) จะต้องเป็นจำนวนเต็มเมื่อฐานเป็นลบ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The factor (%s) must be positive when the value (%s) is positive."
msgstr "ตัวประกอบ (%s) จะต้องเป็นบวก เมื่อค่า (%s) เป็นบวก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/list_data_source.js:0
#, python-format
msgid "The field %s does not exist or you do not have access to that field"
msgstr "ไม่มีฟิลด์ %s หรือคุณไม่มีสิทธิ์เข้าถึงฟิลด์นั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The field (%s) must be one of %s or must be a number between 1 and %s "
"inclusive."
msgstr ""
"ฟิลด์ (%s) จะต้องเป็นหนึ่งใน %s หรือต้องเป็นตัวเลขระหว่าง 1 ถึง %s "
"รวมอยู่ด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The field (%s) must be one of %s."
msgstr "ฟิลด์ (%s) ต้องเป็นหนึ่งใน %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The field must be a number or a string"
msgstr "ฟิลด์ต้องเป็นตัวเลขหรือสตริง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The first value must be a number"
msgstr "ค่าแรกต้องเป็นตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The frequency (%s) must be one of %s"
msgstr "ความถี่ (%s) จะต้องเป็นหนึ่งใน %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The frequency (%s) must be one of %s."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] expects a boolean value, but '%s' is a text, "
"and cannot be coerced to a number."
msgstr ""
"ฟังก์ชัน [[FUNCTION_NAME]] ต้องการค่าบูลีน แต่ '%s' เป็นข้อความ "
"และไม่สามารถบังคับเป็นตัวเลขได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] expects a number value, but '%s' is a string,"
" and cannot be coerced to a number."
msgstr ""
"ฟังก์ชัน [[FUNCTION_NAME]] ต้องการค่าตัวเลข แต่ '%s' เป็นสตริง "
"และไม่สามารถบังคับเป็นตัวเลขได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The function [[FUNCTION_NAME]] result cannot be negative"
msgstr "ผลลัพธ์ของฟังก์ชัน [[FUNCTION_NAME]] ไม่สามารถเป็นค่าลบได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The function [[FUNCTION_NAME]] result must be greater than or equal "
"01/01/1900."
msgstr ""
"ผลลัพธ์ของฟังก์ชัน [[FUNCTION_NAME]] ต้องมากกว่าหรือเท่ากับ 01/01/1900"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The future_value (%s) must be strictly positive."
msgstr "Future_value (%s) ต้องเป็นค่าบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The high (%s) must be greater than or equal to the low (%s)."
msgstr "ค่าสูงสุด (%s) ต้องมากกว่าหรือเท่ากับค่าต่ำสุด (%s)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The life (%s) must be strictly positive."
msgstr "life (%s) ต้องเป็นค่าบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The logarithm of a number, base e (euler's number)."
msgstr "ลอการิทึมของตัวเลข ฐาน e (เลขออยเลอร์)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The maturity (%s) must be strictly greater than the settlement (%s)."
msgstr "ระยะเวลาครบกำหนด (%s) ต้องมากกว่าการชำระหนี้ (%s) เท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The maxpoint must be a number"
msgstr "ค่าสูงสุดต้องเป็นตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The midpoint must be a number"
msgstr "ค่ากึ่งกลางต้องเป็นตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The minpoint must be a number"
msgstr "ค่าต่ำสุดต้องเป็นตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The month (%s) must be between 1 and 12 inclusive."
msgstr "เดือน (%s) ต้องมีตัวเลขระหว่าง 1 ถึง 12 รวมอยู่ด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The net present value of an investment based on a series of periodic cash "
"flows and a discount rate."
msgstr ""
"มูลค่าปัจจุบันสุทธิของการลงทุนโดยพิจารณาจากกระแสเงินสดเป็นงวดและอัตราคิดลด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number of numeric values in dataset."
msgstr "จำนวนค่าตัวเลขในชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number of values in a dataset."
msgstr "จำนวนค่าในชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number pi."
msgstr "หมายเลข pi"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The number_of_characters (%s) must be positive or null."
msgstr "number_of_Characters (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The occurrenceNumber (%s) must be positive or null."
msgstr "occurrenceNumber (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The period (%s) must be less than or equal to %s."
msgstr "ระยะเวลา (%s) ต้องน้อยกว่าหรือเท่ากับ %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The period (%s) must be strictly positive."
msgstr "ระยะเวลา (%s) ต้องเป็นค่าบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The position (%s) must be greater than or equal to 1."
msgstr "ตำแหน่ง (%s) ต้องมากกว่าหรือเท่ากับ 1"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The present_value (%s) must be strictly positive."
msgstr "present_value (%s) จะต้องเป็นบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The price (%s) must be strictly positive."
msgstr "ราคา (%s) จะต้องเป็นบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The range is invalid"
msgstr "ช่วงไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The range must be a single row or a single column."
msgstr "ช่วงต้องเป็นแถวเดียวหรือคอลัมน์เดียว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate (%s) must be positive or null."
msgstr "อัตรา (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate (%s) must be strictly positive."
msgstr "อัตรา (%s) จะต้องเป็นบวกอย่างเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rate_guess (%s) must be strictly greater than -1."
msgstr "Rate_guess (%s) ต้องมากกว่า -1 เท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The redemption (%s) must be strictly positive."
msgstr "การไถ่ถอน (%s) ต้องเป็นค่าบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The result_range must be a single row or a single column."
msgstr "result_range ต้องเป็นแถวเดียวหรือคอลัมน์เดียว"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The rule is invalid for an unknown reason"
msgstr "กฎไม่ถูกต้องโดยไม่ทราบสาเหตุ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The salvage (%s) must be positive or null."
msgstr "การกอบกู้ (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The sample covariance of a dataset."
msgstr "ความแปรปรวนร่วมตัวอย่างของชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The second argument is missing. Please provide a value"
msgstr "อาร์กิวเมนต์ที่สองหายไป กรุณาระบุค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The second value must be a number"
msgstr "ค่าที่สองต้องเป็นตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The settlement (%s) must be greater than or equal to the issue (%s)."
msgstr "ข้อตกลง (%s) ต้องมากกว่าหรือเท่ากับปัญหา (%s)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The start_date (%s) must be positive or null."
msgstr "start_date (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The starting_at (%s) must be greater than or equal to 1."
msgstr "starting_at (%s) ต้องมากกว่าหรือเท่ากับ 1"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The table_number (%s) is out of range."
msgstr "table_number (%s) อยู่นอกช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The text_to_search must be non-empty."
msgstr "text_to_search ต้องไม่ว่างเปล่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The time_string (%s) cannot be parsed to date/time."
msgstr "ไม่สามารถแยกวิเคราะห์ time_string (%s) เป็นวันที่/เวลาได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The type (%s) is out of range."
msgstr "ประเภท (%s) อยู่นอกช่วง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The type (%s) must be 1, 2 or 3."
msgstr "ประเภท (%s) ต้องเป็น 1, 2 หรือ 3"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) cannot be between -1 and 1 inclusive."
msgstr "ค่า (%s) ต้องไม่มีตัวเลขระหว่าง -1 ถึง 1 รวมอยู่ด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be a valid base %s representation."
msgstr "ค่า (%s) จะต้องเป็นตัวแทนฐาน %s ที่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be between -1 and 1 exclusive."
msgstr "ค่า (%s) ต้องอยู่ระหว่าง -1 ถึง 1 เท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be between -1 and 1 inclusive."
msgstr "ค่า (%s) ต้องมีตัวเลขระหว่าง -1 ถึง 1 รวมอยู่ด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be greater than or equal to 1."
msgstr "ค่า (%s) ต้องมากกว่าหรือเท่ากับ 1"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be positive or null."
msgstr "ค่า (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The value (%s) must be strictly positive."
msgstr "ค่า (%s) ต้องเป็นค่าบวกเท่านั้น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The weekend (%s) must be a number or a string."
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"The weekend (%s) must be a string or a number in the range 1-7 or 11-17."
msgstr "วันหยุดสุดสัปดาห์ (%s) ต้องเป็นสตริงหรือตัวเลขในช่วง 1-7 หรือ 11-17"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The weekend (%s) must be different from '1111111'."
msgstr "วันหยุดสุดสัปดาห์ (%s) ต้องแตกต่างจาก '1111111'"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The year (%s) must be between 0 and 9999 inclusive."
msgstr "ปี (%s) ต้องมีตัวเลขระหว่าง 0 ถึง 9999 รวมอยู่ด้วย"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "The yield (%s) must be positive or null."
msgstr "อัตราผลตอบแทน (%s) ต้องเป็นค่าบวกหรือค่าว่าง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "There is no pivot with id \"%s\""
msgstr "ไม่มีจุดพิวอทที่มีรหัส \"%s\""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "This formula depends on invalid values"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"This formula has over 100 parts. It can't be processed properly, consider "
"splitting it into multiple cells"
msgstr ""
"สูตรนี้มีมากกว่า 100 ส่วน ไม่สามารถประมวลผลได้อย่างถูกต้อง "
"โปรดพิจารณาแบ่งออกเป็นหลายเซลล์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "This operation is not allowed with multiple selections."
msgstr "การดำเนินการนี้ไม่ได้รับอนุญาตให้มีการเลือกหลายรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"This operation is not possible due to a merge. Please remove the merges "
"first than try again."
msgstr ""
"ไม่สามารถดำเนินการนี้ได้เนื่องจากการผสานรวม "
"โปรดลบการผสานออกก่อนแล้วลองอีกครั้ง"

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr "ภาพขนาดย่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Time (10:43:00 PM)"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Time Range"
msgstr "ช่วงเวลา"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Title"
msgstr "คำนำหน้าชื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/control_panel/spreadsheet_control_panel.xml:0
#, python-format
msgid "Toggle favorite"
msgstr "สลับรายการโปรด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Top"
msgstr "Top"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/components/spreadsheet_pivot_dialog.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_autofill_plugin.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/plugins/ui/pivot_structure_plugin.js:0
#, python-format
msgid "Total"
msgstr "รวม"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Truncates a number."
msgstr "ตัดทอนตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Type"
msgstr "ประเภท"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_data_source.js:0
#, python-format
msgid "Unable to fetch the label of %s of model %s"
msgstr "ไม่สามารถดึงป้ายกำกับของ %s ของโมเดล %s ได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Underline"
msgstr "ขีดเส้นใต้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Undo"
msgstr "เลิกทำ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unexpected token: %s"
msgstr "โทเค็นที่ไม่คาดคิด: %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unhide all columns"
msgstr "ยกเลิกการซ่อนคอลัมน์ทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unhide all rows"
msgstr "ยกเลิกการซ่อนแถวทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_cache.js:0
#, python-format
msgid "Unknown operator: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unknown token: %s"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Unmatched left parenthesis"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/constants.js:0
#, python-format
msgid "Untitled spreadsheet"
msgstr "สเปรดชีตที่ไม่มีชื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/actions/spreadsheet_template/spreadsheet_template_service.js:0
#, python-format
msgid "Untitled spreadsheet template"
msgstr "เทมเพลตสเปรดชีตที่ไม่มีชื่อ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Update chart"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value"
msgstr "มูลค่า"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value at a given percentile of a dataset exclusive of 0 and 1."
msgstr "ค่าในเปอร์เซ็นไทล์ที่กำหนดของชุดข้อมูลซึ่งไม่รวม 0 และ 1"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value at a given percentile of a dataset."
msgstr "ค่าที่เป็นเปอร์เซ็นไทล์ที่กำหนดของชุดข้อมูล"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value if it is not an error, otherwise 2nd argument."
msgstr "ถ้าค่าไม่ใช่ข้อผิดพลาด จะเป็นอาร์กิวเมนต์ที่ 2"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value interpreted as a percentage."
msgstr "ค่าตีความเป็นเปอร์เซ็นต์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"Value nearest to a specific quartile of a dataset exclusive of 0 and 4."
msgstr "ค่าที่ใกล้กับควอไทล์เฉพาะของชุดข้อมูลซึ่งไม่รวม 0 และ 4 มากที่สุด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Value nearest to a specific quartile of a dataset."
msgstr "ค่าที่ใกล้กับควอไทล์เฉพาะของชุดข้อมูลมากที่สุด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of a population from a table-like range."
msgstr "ความแปรปรวนของประชากรจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of entire population (text as 0)."
msgstr "ความแปรปรวนของประชากรทั้งหมด (ข้อความเป็น 0)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of entire population."
msgstr "ความแปรปรวนของประชากรทั้งหมด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of population sample from table-like range."
msgstr "ความแปรปรวนของกลุ่มตัวอย่างประชากรจากช่วงที่เหมือนตาราง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance of sample (text as 0)."
msgstr "ความแปรปรวนของกลุ่มตัวอย่าง (ข้อความเป็น 0)"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Variance."
msgstr "ความแปรปรวน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Vertical axis position"
msgstr "ตำแหน่งแกนแนวตั้ง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Vertical lookup."
msgstr "การค้นหาแนวตั้ง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "View"
msgstr "มุมมอง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid ""
"We found data next to your selection. Since this data was not selected, it "
"will not be sorted. Do you want to extend your selection?"
msgstr ""
"เราพบข้อมูลถัดจากการเลือกของคุณ เนื่องจากไม่ได้เลือกข้อมูลนี้ "
"จึงไม่สามารถจัดเรียงได้ คุณต้องการขยายการเลือกของคุณหรือไม่?"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#, python-format
msgid "Week"
msgstr "สัปดาห์"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Week number of the year."
msgstr "หมายเลขสัปดาห์ของปี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Weighted average."
msgstr "ค่าเฉลี่ยถ่วงน้ำหนัก"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "When value is"
msgstr "เมื่อมีค่าเป็น"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "When weekend is a string (%s) it must be composed of \"0\" or \"1\"."
msgstr "เมื่อสุดสัปดาห์เป็นสตริง (%s) จะต้องประกอบด้วย \"0\" หรือ \"1\""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is `true` or `false`."
msgstr "ไม่ว่าค่าจะเป็น `true` หรือ `false`"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is a number."
msgstr "ไม่ว่าค่าจะเป็นตัวเลขหรือไม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is an error."
msgstr "ไม่ว่าค่าจะเป็นข้อผิดพลาดหรือไม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is non-textual."
msgstr "ไม่ว่าจะเป็นค่าที่ไม่ใช่ข้อความ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether a value is text."
msgstr "ไม่ว่าค่าจะเป็นข้อความหรือไม่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Whether the provided value is even."
msgstr "ไม่ว่าค่าที่ระบุจะเป็นเลขคู่หรือไม่"

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr "พื้นที่ทำงาน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong function call"
msgstr "การเรียกใช้ฟังก์ชันไม่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong number of Argument[]. Expected an even number of Argument[]."
msgstr "จำนวนอาร์กิวเมนต์ไม่ถูกต้อง [] คาดว่าอาร์กิวเมนต์ [] จะเป็นเลขคู่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Wrong number of arguments. Expected an even number of arguments."
msgstr "จำนวนอาร์กิวเมนต์ไม่ถูกต้อง คาดว่าจะมีอาร์กิวเมนต์เป็นจำนวนคู่"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/helpers/pivot_helpers.js:0
#: code:addons/documents_spreadsheet/static/src/xml/spreadsheet.xml:0
#, python-format
msgid "Year"
msgstr "ปี"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "Year specified by a given date."
msgstr "ปีที่ระบุตามวันที่กำหนด"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/registries/pivot_functions.js:0
#, python-format
msgid "[[FUNCTION_NAME]] cannot be called from the spreadsheet."
msgstr "[[FUNCTION_NAME]] ไม่สามารถเรียกจากสเปรดชีตได้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of bounds range."
msgstr "[[FUNCTION_NAME]] ประเมินเป็นช่วงนอกขอบเขต"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of range column value %s."
msgstr "[[FUNCTION_NAME]] ประเมินค่าคอลัมน์ที่อยู่นอกช่วง %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] evaluates to an out of range row value %s."
msgstr "[[FUNCTION_NAME]] ประเมินค่าแถวที่อยู่นอกช่วง %s"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] expects number values."
msgstr "[[FUNCTION_NAME]] ต้องการค่าตัวเลข"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] expects the weight to be positive or equal to 0."
msgstr "[[FUNCTION_NAME]] คาดว่าน้ำหนักจะเป็นบวกหรือเท่ากับ 0"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has mismatched argument count %s vs %s."
msgstr "[[FUNCTION_NAME]] จำนวนอาร์กิวเมนต์ %s เทียบกับ %s ไม่ตรงกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has mismatched range sizes."
msgstr "[[FUNCTION_NAME]] มีขนาดช่วงไม่ตรงกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "[[FUNCTION_NAME]] has no valid input data."
msgstr "[[FUNCTION_NAME]] ไม่มีข้อมูลอินพุตที่ถูกต้อง"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "and"
msgstr "และ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "by default"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/xml/pivot_dialog.xml:0
#, python-format
msgid "has no cell missing from this sheet"
msgstr "ไม่มีเซลล์ใดหายไปจากชีตนี้"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/side_panels/list/listing_details_side_panel.js:0
#: code:addons/documents_spreadsheet/static/src/side_panels/pivot/pivot_details_side_panel.js:0
#, python-format
msgid "never"
msgstr "ไม่เคย"

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_revision_parent_revision_unique
msgid "o-spreadsheet revision refused due to concurrency"
msgstr "การแก้ไข o-spreadsheet ถูกปฏิเสธเนื่องจากการทำงานพร้อมกัน"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "optional"
msgstr ""

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/spreadsheet_selector_dialog.xml:0
#, python-format
msgid "records of the list."
msgstr "บันทึกของรายการ"

#. module: documents_spreadsheet
#. openerp-web
#: code:addons/documents_spreadsheet/static/src/js/o_spreadsheet/o_spreadsheet.js:0
#, python-format
msgid "repeatable"
msgstr "ทำซ้ำได้"
