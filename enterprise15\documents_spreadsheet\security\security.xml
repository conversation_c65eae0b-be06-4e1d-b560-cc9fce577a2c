<odoo>
    <data noupdate="1">

        <record id="spreadsheet_manager_templates" model="ir.rule">
            <field name="name">Spreadsheet_template: manager</field>
            <field name="model_id" ref="model_spreadsheet_template"/>
            <field name="groups" eval="[(4, ref('documents.group_documents_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

        <record id="spreadsheet_own_templates" model="ir.rule">
            <field name="name">Spreadsheet_template: manage own</field>
            <field name="model_id" ref="model_spreadsheet_template"/>
            <field name="groups" eval="[(4, ref('documents.group_documents_user'))]"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="perm_read" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

    </data>
</odoo>
