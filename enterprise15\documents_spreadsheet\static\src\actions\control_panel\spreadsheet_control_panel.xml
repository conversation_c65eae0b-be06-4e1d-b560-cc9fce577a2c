<?xml version="1.0" encoding="utf-8"?>
<templates>

<t t-name="documents_spreadsheet.SpreadsheetControlPanel" owl="1">
    <ControlPanel display="controlPanelDisplay">
        <t t-set-slot="control-panel-top-right">
            <div t-if="props.isSpreadsheetSynced !== undefined" class="o_spreadsheet_status o_spreadsheet_sync_status text-muted">
                <t t-if="props.isSpreadsheetSynced">
                    <i class="fa fa-check"/>  Saved
                </t>
                <t t-else="">
                    <i class="fa fa-spin fa-spinner"/> Saving
                </t>
            </div>
            <div t-if="props.numberOfConnectedUsers !== undefined" class="o_spreadsheet_status o_spreadsheet_number_users text-muted">
                <t t-if="props.numberOfConnectedUsers === 1">
                    <i class="fa fa-user"> 1</i>
                </t>
                <t t-else="">
                    <i class="fa fa-users text-info"> <t t-esc="props.numberOfConnectedUsers"/></i>
                </t>
            </div>
        </t>
        <t t-set-slot="control-panel-top-left">
            <t t-call="documents_spreadsheet.Breadcrumbs" />
        </t>
    </ControlPanel>
</t>

<div t-name="documents_spreadsheet.Breadcrumbs" t-inherit="web.Breadcrumbs" t-inherit-mode="primary" owl="1">
    <xpath expr="//ol[hasclass('breadcrumb')]/li[hasclass('active')]" position="replace">
        <li class="breadcrumb-item active">
            <SpreadsheetName name="props.spreadsheetName" isReadonly="props.isReadonly"/>
            <a
                title="Toggle favorite"
                t-on-click="_toggleFavorited"
                t-attf-class="o_spreadsheet_favorite fa fa-lg fa-star{{!props.isFavorited ? '-o' : ' favorite_button_enabled'}}"/>
        </li>
    </xpath>
</div>

</templates>
