<?xml version="1.0" encoding="utf-8"?>
<templates>
    <div t-name="documents_spreadsheet.ListingDetailsSidePanel" class="o-listing-details-side-panel" owl="1">
        <t t-set="def" t-value="listDefinition"/>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">List Name</div>
            <div t-esc="getters.getListDisplayName(props.listId)"/>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Model</div>
            <div><t t-esc="def.modelDisplayName"/> (<t t-esc="def.model"/>)</div>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Domain</div>
            <DomainComponentAdapter Component="DomainSelector" model="def.model" domain="def.domain" t-key="'list_' + def.id"/>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title" t-if="def.orderBy.length !== 0">Sorting</div>
            <t t-foreach="def.orderBy" t-as="sort" t-key="sort">
                <div t-esc="formatSort(sort)"/>
            </t>
            <div class="o_pivot_last_update"><i>Last updated at <t t-esc="getLastUpdate()"/></i></div>
            <div class="btn btn-link o_refresh_list" t-on-click="refresh()"><i class="fa fa-arrow-right mr-1"/>Refresh values</div><br/>
        </div>
    </div>
</templates>
