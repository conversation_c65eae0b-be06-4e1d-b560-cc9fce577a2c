<?xml version="1.0" encoding="utf-8"?>
<templates>
    <div t-name="documents_spreadsheet.PivotDetailsSidePanel" class="o_spreadsheet_pivot_side_panel" owl="1">
        <t t-set="def" t-value="pivotDefinition"/>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Pivot name</div>
            <div t-esc="getters.getPivotDisplayName(props.pivotId)"/>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Model</div>
            <div><t t-esc="def.modelDisplayName"/> (<t t-esc="def.model"/>)</div>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Domain</div>
            <DomainComponentAdapter Component="DomainSelector" model="def.model" domain="def.domain" t-key="'pivot_' + props.pivotId"/>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Dimensions</div>
            <t t-foreach="def.dimensions" t-as="gp" t-key="gb">
                <div t-esc="getters.getFormattedGroupBy(props.pivotId, gp)"/>
            </t>
        </div>
        <div class="o_side_panel_section">
            <div class="o_side_panel_title">Measures</div>
            <t t-foreach="def.measures" t-as="measure" t-key="measure">
                <div t-esc="getters.getFormattedHeader(props.pivotId, 'measure', measure.field)"/>
            </t>
            <div class="o_pivot_last_update"><i>Last updated at <t t-esc="getLastUpdate()"/></i></div>
            <div class="btn btn-link o_refresh_measures" t-on-click="refresh()"><i class="fa fa-arrow-right mr-1"/>Refresh values</div><br/>
        </div>
    </div>
</templates>
