<?xml version="1.0" encoding="utf-8"?>
<templates>
    <div t-name="documents_spreadsheet.PivotSidePanel" class="o_spreadsheet_pivot_list_side_panel" owl="1">
        <t t-set="pivotId" t-value="getters.getSelectedPivotId()"/>
        <t t-if="pivotId">
            <PivotDetailsSidePanel pivotId="pivotId"/>
            <div class="o-sidePanelButtons">
                <button t-on-click="resetSelectedPivot" class="o-sidePanelButton o_pivot_cancel">Back</button>
            </div>
        </t>
        <t t-else="1">
            <div t-on-click="selectPivot(pivotId)" t-foreach="getters.getPivotIds()" t-as="pivotId" t-key="pivotId">
                <div class="o_side_panel_select">
                    <span t-esc="getters.getPivotDisplayName(pivotId)"/>
                </div>
            </div>
        </t>
    </div>
</templates>
