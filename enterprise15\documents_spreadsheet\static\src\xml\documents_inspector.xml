<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template t-name="spreadsheet.DocumentsInspector.previews" t-inherit="documents.DocumentsInspector.previews" t-inherit-mode="extension">
        <xpath expr="(//div[@name='document_preview']/div)[last()]" position="before">
                <t t-elif="recordData.isSheet">
                    <t t-set="size" t-value="nbPreviews === 1 ? '268x130' : nbPreviews === 2 ? '120x130' : '120x75'"/>
                    <img class="o_document_spreadsheet o_documents_image_background o_spreadsheet_resize"
                    t-attf-src="/documents/image/#{record.res_id}/#{size}?field=thumbnail&amp;unique="
                    t-att-data-id="record.data.res_id"
                    draggable="false"
                    t-att-title="record.data.name"/>
                </t>
        </xpath>
    </template>
</odoo>
