<?xml version="1.0" encoding="utf-8"?>
<templates>
    <t t-name="documents_spreadsheet.PivotView.buttons" t-inherit="web.PivotView.Buttons" t-inherit-mode="primary" owl="1">
        <xpath expr="//button[hasclass('o_pivot_flip_button')]" position="before">
            <button t-if="canInsertPivot and env.isSmall"
                    class="btn btn-secondary fa fa-file-text-o o_pivot_add_spreadsheet"
                    t-att-disabled="!model.hasData() or model.metaData.activeMeasures.length === 0 or model.useSampleModel"
                    title="Insert in Spreadsheet" aria-label="Insert in Spreadsheet"
                    t-on-click="onInsertInSpreadsheet()"/>
        </xpath>
        <xpath expr="//div[hasclass('btn-group')][1]" position="after">
            <t t-if="canInsertPivot and !env.isSmall">
                <div class="btn-group" role="toolbar" aria-label="Insert in Spreadsheet">
                    <button class="btn btn-secondary o_pivot_add_spreadsheet"
                            t-att-disabled="!model.hasData() or model.metaData.activeMeasures.length === 0 or model.useSampleModel"
                            t-on-click="onInsertInSpreadsheet()">
                        Insert in Spreadsheet
                    </button>
                </div>
            </t>
        </xpath>
    </t>
</templates>
