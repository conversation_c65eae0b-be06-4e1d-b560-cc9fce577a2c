<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="documents_spreadsheet.TemplateDialog" owl="1">
        <div>
            <Dialog t-if="state.isOpen" title="dialogTitle" t-on-dialog-closed="state.isOpen = false" contentClass="'o-spreadsheet-templates-dialog'">
                <div class="o_searchview">
                    <SearchBar fields="props.searchView.fields"/>
                </div>
                <div class="o-pager">
                    <Pager
                        t-if="state.templatesCount"
                        t-on-pager-changed="_onPagerChanged"
                        size="state.templatesCount"
                        currentMinimum="state.currentMinimum"
                        limit="limit" editable="false"/>
                </div>
                <div class="o-templates-container">
                    <div class="o-template o-blank-template">
                        <img
                            tabindex="0"
                            t-on-focus="_selectTemplate(null)"
                            t-att-class="_isSelected(null) ? 'o-template-selected': ''"
                            src="documents_spreadsheet/static/img/spreadsheet.svg"
                            t-att-title="Blank"/>
                        <div>Blank</div>
                    </div>
                    <div
                        t-foreach="state.templates" t-as="template"
                        t-key="template.id"
                        class="o-template">
                        <img
                            tabindex="0"
                            t-att-class="_isSelected(template.id) ? 'o-template-selected': ''"
                            t-on-focus="_selectTemplate(template.id)"
                            t-attf-src="/web/image?model=spreadsheet.template&amp;id={{template.id}}&amp;field=thumbnail"
                            t-att-title="template.name"/>
                        <div t-esc="template.name"/>
                    </div>
                </div>
                <t t-set-slot="buttons">
                    <button
                        class="btn btn-primary o-spreadsheet-create"
                        t-att-disabled="_buttonDisabled()"
                        t-on-click="_createSpreadsheet">
                        <t>Create</t>
                    </button>
                    <button class="btn btn-secondary" t-on-click="state.isOpen = false">
                        <t>Cancel</t>
                    </button>
                </t>
            </Dialog>
        </div>
    </t>
</templates>
