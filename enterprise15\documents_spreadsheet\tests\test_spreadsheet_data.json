{"version": 10, "sheets": [{"id": "sh1", "name": "Sheet1", "colNumber": 26, "rowNumber": 100, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": {"style": 1, "content": ""}, "A2": {"style": 1, "content": ""}, "A3": {"style": 2, "content": "=PIVOT.HEADER(\"1\",\"country_id\",\"20\")"}, "A4": {"style": 2, "content": "=PIVOT.HEADER(\"1\",\"country_id\",\"75\")"}, "A5": {"style": 2, "content": "=PIVOT.HEADER(\"1\",\"country_id\",\"101\")"}, "A6": {"style": 2, "content": "=PIVOT.HEADER(\"1\",\"country_id\",\"false\")"}, "A7": {"style": 2, "content": "=PIVOT.HEADER(\"1\")"}, "B1": {"style": 2, "content": "=PIVOT.HEADER(\"1\",\"create_date:month\",\"11/2023\")"}, "B2": {"style": 3, "content": "=PIVOT.HEADER(\"1\",\"create_date:month\",\"11/2023\",\"measure\",\"__count\")"}, "B3": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"20\",\"create_date:month\",\"11/2023\")"}, "B4": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"75\",\"create_date:month\",\"11/2023\")"}, "B5": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"101\",\"create_date:month\",\"11/2023\")"}, "B6": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"false\",\"create_date:month\",\"11/2023\")"}, "B7": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"create_date:month\",\"11/2023\")"}, "C1": {"style": 2, "content": "=PIVOT.HEADER(\"1\")"}, "C2": {"style": 3, "content": "=PIVOT.HEADER(\"1\",\"measure\",\"__count\")"}, "C3": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"20\")"}, "C4": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"75\")"}, "C5": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"101\")"}, "C6": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\",\"country_id\",\"false\")"}, "C7": {"format": "#,##0.00", "content": "=PIVOT(\"1\",\"__count\")"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true}, {"id": "sh2", "name": "Sheet2", "rows": {}, "cols": {}, "merges": [], "cells": {"A1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"display_name\")", "dependencies": []}}, "B1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"phone\")", "dependencies": []}}, "C1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"email\")", "dependencies": []}}, "D1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"activity_ids\")", "dependencies": []}}, "E1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"city\")", "dependencies": []}}, "F1": {"style": 2, "formula": {"text": "=LIST.HEADER(\"1\",\"country_id\")", "dependencies": []}}, "A2": {"formula": {"text": "=LIST(\"1\",\"1\",\"display_name\")", "dependencies": []}}, "B2": {"formula": {"text": "=LIST(\"1\",\"1\",\"phone\")", "dependencies": []}}, "C2": {"formula": {"text": "=LIST(\"1\",\"1\",\"email\")", "dependencies": []}}, "D2": {"formula": {"text": "=LIST(\"1\",\"1\",\"activity_ids\")", "dependencies": []}}, "E2": {"formula": {"text": "=LIST(\"1\",\"1\",\"city\")", "dependencies": []}}, "F2": {"formula": {"text": "=LIST(\"1\",\"1\",\"country_id\")", "dependencies": []}}, "A3": {"formula": {"text": "=LIST(\"1\",\"2\",\"display_name\")", "dependencies": []}}, "B3": {"formula": {"text": "=LIST(\"1\",\"2\",\"phone\")", "dependencies": []}}, "C3": {"formula": {"text": "=LIST(\"1\",\"2\",\"email\")", "dependencies": []}}, "D3": {"formula": {"text": "=LIST(\"1\",\"2\",\"activity_ids\")", "dependencies": []}}, "E3": {"formula": {"text": "=LIST(\"1\",\"2\",\"city\")", "dependencies": []}}, "F3": {"formula": {"text": "=LIST(\"1\",\"2\",\"country_id\")", "dependencies": []}}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true}], "entities": {}, "styles": {"1": {"fillColor": "#f2f2f2"}, "2": {"bold": true, "fillColor": "#f2f2f2"}, "3": {"fillColor": "#f2f2f2", "textColor": "#756f6f"}}, "borders": {}, "uniqueFigureIds": true, "pivots": {"1": {"model": "res.partner", "rowGroupBys": ["country_id"], "colGroupBys": ["create_date:month"], "measures": [{"field": "__count", "operator": "sum"}], "domain": [], "context": {"default_is_company": true}, "id": "1"}}, "lists": {"1": {"model": "res.partner", "domain": [], "orderBy": [], "context": {"lang": "en_US", "tz": "Europe/Brussels", "uid": 2, "allowed_company_ids": [1], "default_is_company": true}, "columns": ["display_name", "phone", "email", "activity_ids", "city", "country_id"], "id": "1"}}, "globalFilters": [{"id": "de5794c5-75fe-4f74-b9cb-6c59937a2c69", "label": "MyFilter1", "type": "relation", "rangeType": "year", "fields": {"1": {"field": "user_ids", "type": "many2many"}}, "listFields": {"1": {"field": "user_ids", "type": "many2many"}}, "defaultValue": [2], "modelName": "res.users", "pivotFields": {"1": {"field": "user_ids", "type": "many2many"}}}]}