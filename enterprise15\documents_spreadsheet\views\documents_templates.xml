<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="documents_spreadsheet.share_page" name="Multiple Documents Share with Spreadsheets" inherit_id="documents.share_page">
    <xpath expr="//article" position="before">
        <t t-set="isSpreadsheet" t-value="document.mimetype == 'application/o-spreadsheet'"/>
    </xpath>
    <xpath expr="//article" position="attributes">
        <attribute name="t-attf-class" separator=" " add="#{isSpreadsheet and 'spreadsheet_fade' or ''}"/>
        <attribute name="t-attf-title" >#{ isSpreadsheet and 'Odoo Spreadsheets not available for download' or ''}</attribute>
    </xpath>
    <xpath expr="//article//h6/span[contains(@t-if, 'document_request')]" position="after">
        <span t-elif="isSpreadsheet" t-esc="document.name"/>
    </xpath>
    <xpath expr="//article/div[hasclass('o_card_right')]/a[contains(@t-if, 'not document_request')]" position="attributes">
        <attribute name="t-if" separator=" " add="and not isSpreadsheet"/>
    </xpath>
</template>

<template id="documents_spreadsheet.share_single" name="Documents Share Single File with Spreadsheets" inherit_id="documents.share_single">
    <xpath expr="//article" position="before">
        <t t-set="isSpreadsheet" t-value="document.mimetype == 'application/o-spreadsheet'"/>
    </xpath>
    <xpath expr="//article" position="attributes">
        <attribute name="t-attf-class" separator=" " add="#{isSpreadsheet and 'spreadsheet_fade' or ''}"/>
        <attribute name="t-attf-title" >#{ isSpreadsheet and 'Odoo Spreadsheets not available for download' or ''}</attribute>
    </xpath>
    <xpath expr="//article//h3/span[contains(@t-if, 'document_request')]" position="after">
        <span t-elif="isSpreadsheet" t-esc="document.name"/>
    </xpath>
    <xpath expr="//div[hasclass('o_docs_single_container')]/div[hasclass('o_docs_single_actions')]" position="attributes">
        <attribute name="t-if">not isSpreadsheet</attribute>
    </xpath>
</template>

</odoo>
