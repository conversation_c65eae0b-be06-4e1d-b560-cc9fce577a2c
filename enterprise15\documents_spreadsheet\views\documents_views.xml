<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="spreadsheet_document_view_kanban" model="ir.ui.view">
        <field name="name">spreadsheet.documents.document.kanban</field>
        <field name="model">documents.document</field>
        <field name="inherit_id" ref="documents.document_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//templates" position="before">
                <field name="handler"/>
            </xpath>
            <xpath expr="//div[@name='document_preview']" position="before">
                <t t-set="isSpreadsheet" t-value="record.handler.raw_value === 'spreadsheet'"/>
            </xpath>
            <xpath expr="//div[@name='document_preview']" position="attributes">
                <attribute name="t-attf-class" separator=" " add="#{(isSpreadsheet) ? 'o_document_spreadsheet' : ''}"/>
            </xpath>
            <xpath expr="(//div[@name='document_preview']/div)[last()]" position="before">
                <img t-elif="isSpreadsheet" t-attf-src="/documents/image/#{record.id.raw_value}?field=thumbnail&amp;unique=" width="100" height="100" alt="Spreadsheet Preview" class="o_attachment_image o_spreadsheet_resize"/>
            </xpath>
        </field>
    </record>
    <record id="documents_document_view_list" model="ir.ui.view">
        <field name="name">documents.document.view.list</field>
        <field name="model">documents.document</field>
        <field name="inherit_id" ref="documents.documents_view_list"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
                <field name="handler" invisible="1"/>
            </xpath>
        </field>
    </record>
    <!-- Spreadsheet contributor -->
    <record id="spreadsheet_contributor_action" model="ir.actions.act_window">
        <field name="name">Contributors</field>
        <field name="res_model">spreadsheet.contributor</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="document_view_search_spreadsheet" model="ir.ui.view">
        <field name="model">documents.document</field>
        <field name="inherit_id" ref="documents.document_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='pdf_doc']" position='after'>
                <filter string="Spreadsheet" name="spreadsheet" domain="[('handler', '=', 'spreadsheet')]"/>
            </xpath>
        </field>
    </record>

    <record id="spreadsheet_contributor_view_tree" model="ir.ui.view">
        <field name="name">spreadsheet.contributor</field>
        <field name="model">spreadsheet.contributor</field>
        <field name="arch" type="xml">
            <tree>
                <field name="document_id"/>
                <field name="user_id"/>
            </tree>
        </field>
    </record>
    <!-- Spreadsheet Templates -->
    <record id="spreadsheet_template_action" model="ir.actions.act_window">
        <field name="name">Spreadsheet Templates</field>
        <field name="res_model">spreadsheet.template</field>
        <field name="view_mode">tree</field>
        <field name="context">{
            'search_default_my_template_filter': 1,
        }</field>
    </record>

    <record id="spreadsheet_template_view_tree" model="ir.ui.view">
        <field name="name">spreadsheet.template</field>
        <field name="model">spreadsheet.template</field>
        <field name="arch" type="xml">
            <tree create="0" editable="top" js_class="spreadsheet_template_list">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="create_uid" optional="show"/>
                <field name="create_date" optional="show"/>
                <field name="data" widget="binary" optional="show"/>
                <field name="thumbnail" widget="image" options="{'size':[0,90]}" optional="hide"/>
                <button string="Make a copy" class="float-right" name="copy" type="object" icon="fa-clone"/>
                <button string="Edit" class="float-right" name="edit_template" icon="fa-pencil"/>
                <button string="New spreadsheet" class="o-new-spreadsheet float-right" name="create_spreadsheet" icon="fa-plus"/>
            </tree>
        </field>
    </record>

    <record id="spreadsheet_template_view_search" model="ir.ui.view">
        <field name="name">spreadsheet search view</field>
        <field name="model">spreadsheet.template</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <filter string="My Templates" name="my_template_filter" domain="[('create_uid', '=', uid)]"/>
            </search>
        </field>
    </record>

    <!-- Spreadsheet revisions -->
    <record id="spreadsheet_revision_action" model="ir.actions.act_window">
        <field name="name">Revisions</field>
        <field name="res_model">spreadsheet.revision</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'search_default_group_by_document_id': 1,
        }</field>
    </record>

    <record id="spreadsheet_revision_view_search" model="ir.ui.view">
        <field name="name">spreadsheet.revision search view</field>
        <field name="model">spreadsheet.revision</field>
        <field name="arch" type="xml">
            <search>
                <field name="document_id"/>
                <filter string="Spreadsheet" name="group_by_document_id" context="{'group_by':'document_id'}"/>
            </search>
        </field>
    </record>


    <record id="spreadsheet_revision_view_tree" model="ir.ui.view">
        <field name="name">spreadsheet.revision list view</field>
        <field name="model">spreadsheet.revision</field>
        <field name="arch" type="xml">
            <tree>
                <field name="create_date"/>
                <field name="revision_id"/>
            </tree>
        </field>
    </record>

    <record id="spreadsheet_contributor_view_form" model="ir.ui.view">
        <field name="name">spreadsheet.revision form view</field>
        <field name="model">spreadsheet.revision</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="create_date"/>
                            <field name="revision_id"/>
                            <field name="document_id"/>
                        </group>
                        <field name="commands" widget="text"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Add in Documents/Configuration -->
    <menuitem id="menu_technical_spreadsheet_template"
        name="Spreadsheet Templates"
        parent="documents.Config" sequence="10"
        action="spreadsheet_template_action"/>

    <!-- Technical / Spreadsheet -->
    <menuitem id="menu_technical_spreadsheet"
        name="Spreadsheet"
        sequence="5"
        parent="base.menu_custom"/>

    <!-- Add in Technical/Spreadsheet -->
    <menuitem id="menu_technical_spreadsheet_contributor"
        name="Contributors"
        parent="menu_technical_spreadsheet" sequence="1"
        action="spreadsheet_contributor_action"/>
    <menuitem id="menu_technical_spreadsheet_revision"
        name="Revisions"
        parent="menu_technical_spreadsheet" sequence="10"
        action="spreadsheet_revision_action"/>
</odoo>
