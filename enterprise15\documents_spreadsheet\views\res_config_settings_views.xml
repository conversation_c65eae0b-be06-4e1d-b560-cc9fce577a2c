<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form_inherit_documents_spreadsheet" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.documents.spreadsheet</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="70"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('o_documents_block')]" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>
            <xpath expr="//div[hasclass('o_documents_block')]" position="inside">
                <div class="row mt16 o_settings_container">
                    <div class="col-xs-12 col-md-6 o_setting_box">
                        <div class="o_setting_right_pane o_documents_right_pane">
                            <strong>Spreadsheets</strong>
                            <span class="fa fa-lg fa-building-o ml-1" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                            <div class="row">
                                <div class="text-muted col-md-12">
                                    Centralize your spreadsheets
                                </div>
                            </div>
                            <div name="documents_spreadsheet_folder" class="content-group">
                                <div class="row mt16">
                                    <label class="o_form_label col-lg-3" for="documents_spreadsheet_folder_id" string="Workspace"/>
                                    <field name="documents_spreadsheet_folder_id" required="1"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
