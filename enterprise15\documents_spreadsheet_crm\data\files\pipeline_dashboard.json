{"version": 9, "sheets": [{"id": "dc957ee9-52f8-4fef-9c58-0ac5c002292f", "name": "Dashboard", "colNumber": 26, "rowNumber": 94, "rows": {}, "cols": {"0": {"size": 113}, "1": {"size": 113}, "2": {"size": 113}, "3": {"size": 113}, "4": {"size": 113}, "5": {"size": 113}, "6": {"size": 113}, "7": {"size": 46}, "8": {"size": 199}, "9": {"size": 123}, "10": {"size": 123}, "11": {"size": 123}}, "merges": ["A30:C31", "E30:G31", "A56:C57", "A39:D40", "I30:L31", "A1:L2"], "cells": {"A30": {"style": 1, "content": "Opportunities by Stage"}, "A31": {"style": 1, "content": ""}, "B30": {"style": 1, "content": ""}, "B31": {"style": 1, "content": ""}, "C30": {"style": 1, "content": ""}, "C31": {"style": 1, "content": ""}, "E30": {"style": 1, "content": "Lost Opportunities by Stage"}, "E31": {"style": 1, "content": ""}, "F30": {"style": 1, "content": ""}, "F31": {"style": 1, "content": ""}, "G30": {"style": 1, "content": ""}, "G31": {"style": 1, "content": ""}, "B32": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"measure\",\"__count\")", "dependencies": [], "value": "Count"}}, "C32": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"1\",\"measure\",\"expected_revenue\")", "dependencies": [], "value": "Expected Revenue"}}, "A33": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",1))", "dependencies": [], "value": "#ERROR"}}, "B33": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",1))", "dependencies": [], "value": "#ERROR"}}, "C33": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",1))", "dependencies": [], "value": "#ERROR"}}, "A34": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",2))", "dependencies": [], "value": "#ERROR"}}, "B34": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",2))", "dependencies": [], "value": "#ERROR"}}, "C34": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",2))", "dependencies": [], "value": "#ERROR"}}, "A35": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",3))", "dependencies": [], "value": "#ERROR"}}, "B35": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",3))", "dependencies": [], "value": "#ERROR"}}, "C35": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",3))", "dependencies": [], "value": "#ERROR"}}, "A36": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",4))", "dependencies": [], "value": "#ERROR"}}, "B36": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",4))", "dependencies": [], "value": "#ERROR"}}, "C36": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"1\",\"stage_id\",4))", "dependencies": [], "value": "#ERROR"}}, "A37": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"1\")", "dependencies": [], "value": "Total"}}, "B37": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"__count\")", "dependencies": [], "value": 13}}, "C37": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"1\",\"expected_revenue\")", "dependencies": [], "value": 290100}}, "F32": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"4\",\"measure\",\"__count\")", "dependencies": [], "value": "Count"}}, "G32": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"4\",\"measure\",\"expected_revenue\")", "dependencies": [], "value": "Expected Revenue"}}, "E33": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"4\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",1))", "dependencies": [], "value": "#ERROR"}}, "F33": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"4\",\"__count\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",1))", "dependencies": [], "value": "#ERROR"}}, "G33": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"4\",\"expected_revenue\",\"stage_id\",PIVOT.POSITION(\"4\",\"stage_id\",1))", "dependencies": [], "value": "#ERROR"}}, "E37": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"4\")", "dependencies": [], "value": "Total"}}, "F37": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"4\",\"__count\")", "dependencies": [], "value": ""}}, "G37": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"4\",\"expected_revenue\")", "dependencies": [], "value": ""}}, "A39": {"style": 1, "content": "Actuals vs Target"}, "A32": {"style": 4, "content": ""}, "E32": {"style": 4, "content": ""}, "B41": {"style": 2, "content": "Actuals"}, "A42": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "January 2021"}}, "B42": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"01/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A43": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "February 2021"}}, "B43": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"02/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A44": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "March 2021"}}, "B44": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"03/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A45": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "April 2021"}}, "B45": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"04/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A46": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "May 2021"}}, "B46": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"05/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A47": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "June 2021"}}, "B47": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"06/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A48": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "July 2021"}}, "B48": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"07/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A49": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "August 2021"}}, "B49": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"08/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A50": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "September 2021"}}, "B50": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"09/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": 42600}}, "A51": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "October 2021"}}, "B51": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"10/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": 130000}}, "A52": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "November 2021"}}, "B52": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"11/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": 117500}}, "A53": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": "December 2021"}}, "B53": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"2\",\"expected_revenue\",\"date_deadline:month\",\"12/\"&FILTER.VALUE(\"Year\"))", "dependencies": [], "value": ""}}, "A54": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"2\")", "dependencies": [], "value": "Total"}}, "B54": {"format": "#,##0.00", "formula": {"text": "=sum(|0|)", "dependencies": ["B42:B53"], "value": 290100}}, "A56": {"style": 1, "content": "Loss Reasons"}, "A57": {"style": 1, "content": ""}, "B56": {"style": 1, "content": ""}, "B57": {"style": 1, "content": ""}, "C56": {"style": 1, "content": ""}, "C57": {"style": 1, "content": ""}, "A41": {"style": 4, "content": ""}, "B58": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"3\",\"measure\",\"__count\")", "dependencies": [], "value": "Count"}}, "C58": {"style": 2, "formula": {"text": "=PIVOT.HEADER(\"3\",\"measure\",\"expected_revenue\")", "dependencies": [], "value": "Expected Revenue"}}, "A59": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"3\",\"lost_reason\",PIVOT.POSITION(\"3\",\"lost_reason\",1))", "dependencies": [], "value": "#ERROR"}}, "B59": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"3\",\"__count\",\"lost_reason\",PIVOT.POSITION(\"3\",\"lost_reason\",1))", "dependencies": [], "value": "#ERROR"}}, "C59": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"3\",\"expected_revenue\",\"lost_reason\",PIVOT.POSITION(\"3\",\"lost_reason\",1))", "dependencies": [], "value": "#ERROR"}}, "A60": {"style": 3, "formula": {"text": "=PIVOT.HEADER(\"3\")", "dependencies": [], "value": "Total"}}, "B60": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"3\",\"__count\")", "dependencies": [], "value": ""}}, "C60": {"format": "#,##0.00", "formula": {"text": "=PIVOT(\"3\",\"expected_revenue\")", "dependencies": [], "value": ""}}, "A58": {"style": 4, "content": ""}, "C41": {"style": 5, "content": "Target"}, "D41": {"style": 5, "content": "Perf."}, "C42": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C43": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C44": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C45": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C46": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C47": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C48": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C49": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C50": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C51": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C52": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C53": {"style": 6, "format": "#,##0.00", "content": "10000"}, "C54": {"format": "#,##0.00", "formula": {"text": "=sum(|0|)", "dependencies": ["C42:C53"], "value": 120000}}, "D42": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B42", "C42"], "value": 0}}, "D43": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B43", "C43"], "value": 0}}, "D44": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B44", "C44"], "value": 0}}, "D45": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B45", "C45"], "value": 0}}, "D46": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B46", "C46"], "value": 0}}, "D47": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B47", "C47"], "value": 0}}, "D48": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B48", "C48"], "value": 0}}, "D49": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B49", "C49"], "value": 0}}, "D50": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B50", "C50"], "value": 4.26}}, "D51": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B51", "C51"], "value": 13}}, "D52": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B52", "C52"], "value": 11.75}}, "D53": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B53", "C53"], "value": 0}}, "D54": {"format": "0.00%", "formula": {"text": "=iferror(|0|/|1|,0)", "dependencies": ["B54", "C54"], "value": 2.4175}}, "A1": {"style": 7, "formula": {"text": "=\"CRM Dashboard - \"&FILTER.VALUE(\"Year\")", "dependencies": [], "value": "CRM Dashboard - 2021"}}, "L1": {"style": 7, "content": ""}, "L2": {"style": 7, "content": ""}, "M1": {"style": 8, "content": ""}, "M2": {"style": 8, "content": ""}, "N1": {"style": 8, "content": ""}, "N2": {"style": 8, "content": ""}, "O1": {"style": 8, "content": ""}, "O2": {"style": 8, "content": ""}, "B39": {"style": 1, "content": ""}, "C39": {"style": 1, "content": ""}, "D39": {"style": 1, "content": ""}, "A40": {"style": 1, "content": ""}, "B40": {"style": 1, "content": ""}, "C40": {"style": 1, "content": ""}, "D40": {"style": 1, "content": ""}, "B1": {"style": 7, "content": ""}, "C1": {"style": 7, "content": ""}, "D1": {"style": 7, "content": ""}, "E1": {"style": 7, "content": ""}, "F1": {"style": 7, "content": ""}, "G1": {"style": 7, "content": ""}, "H1": {"style": 7, "content": ""}, "I1": {"style": 7, "content": ""}, "J1": {"style": 7, "content": ""}, "K1": {"style": 7, "content": ""}, "A2": {"style": 7, "content": ""}, "B2": {"style": 7, "content": ""}, "C2": {"style": 7, "content": ""}, "D2": {"style": 7, "content": ""}, "E2": {"style": 7, "content": ""}, "F2": {"style": 7, "content": ""}, "G2": {"style": 7, "content": ""}, "H2": {"style": 7, "content": ""}, "I2": {"style": 7, "content": ""}, "J2": {"style": 7, "content": ""}, "K2": {"style": 7, "content": ""}, "I32": {"style": 3, "formula": {"text": "=LIST.HEADER(\"1\",\"name\")", "dependencies": [], "value": "Opportunity"}}, "J32": {"style": 3, "formula": {"text": "=LIST.HEADER(\"1\",\"user_id\")", "dependencies": [], "value": "Salesperson"}}, "K32": {"style": 3, "format": "#,##0.00", "formula": {"text": "=LIST.HEADER(\"1\",\"expected_revenue\")", "dependencies": [], "value": "Expected Revenue"}}, "L32": {"style": 3, "formula": {"text": "=LIST.HEADER(\"1\",\"stage_id\")", "dependencies": [], "value": "Stage"}}, "I33": {"formula": {"text": "=LIST(\"1\",\"1\",\"name\")", "dependencies": [], "value": "Need 20 Desks"}}, "J33": {"formula": {"text": "=LIST(\"1\",\"1\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K33": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"1\",\"expected_revenue\")", "dependencies": [], "value": 60000}}, "L33": {"formula": {"text": "=LIST(\"1\",\"1\",\"stage_id\")", "dependencies": [], "value": "Proposition"}}, "I34": {"formula": {"text": "=LIST(\"1\",\"2\",\"name\")", "dependencies": [], "value": "Quote for 150 carpets"}}, "J34": {"formula": {"text": "=LIST(\"1\",\"2\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K34": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"2\",\"expected_revenue\")", "dependencies": [], "value": 40000}}, "L34": {"formula": {"text": "=LIST(\"1\",\"2\",\"stage_id\")", "dependencies": [], "value": "New"}}, "I35": {"formula": {"text": "=LIST(\"1\",\"3\",\"name\")", "dependencies": [], "value": "Quote for 12 Tables"}}, "J35": {"formula": {"text": "=LIST(\"1\",\"3\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K35": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"3\",\"expected_revenue\")", "dependencies": [], "value": 40000}}, "L35": {"formula": {"text": "=LIST(\"1\",\"3\",\"stage_id\")", "dependencies": [], "value": "New"}}, "I36": {"formula": {"text": "=LIST(\"1\",\"4\",\"name\")", "dependencies": [], "value": "DeltaPC: 10 Computer Desks"}}, "J36": {"formula": {"text": "=LIST(\"1\",\"4\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K36": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"4\",\"expected_revenue\")", "dependencies": [], "value": 35000}}, "L36": {"formula": {"text": "=LIST(\"1\",\"4\",\"stage_id\")", "dependencies": [], "value": "Qualified"}}, "I37": {"formula": {"text": "=LIST(\"1\",\"5\",\"name\")", "dependencies": [], "value": "Info about services"}}, "J37": {"formula": {"text": "=LIST(\"1\",\"5\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K37": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"5\",\"expected_revenue\")", "dependencies": [], "value": 25000}}, "L37": {"formula": {"text": "=LIST(\"1\",\"5\",\"stage_id\")", "dependencies": [], "value": "Qualified"}}, "I38": {"formula": {"text": "=LIST(\"1\",\"6\",\"name\")", "dependencies": [], "value": "Office Design Project"}}, "J38": {"formula": {"text": "=LIST(\"1\",\"6\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K38": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"6\",\"expected_revenue\")", "dependencies": [], "value": 24000}}, "L38": {"formula": {"text": "=LIST(\"1\",\"6\",\"stage_id\")", "dependencies": [], "value": "New"}}, "I39": {"formula": {"text": "=LIST(\"1\",\"7\",\"name\")", "dependencies": [], "value": "Quote for 600 Chairs"}}, "J39": {"formula": {"text": "=LIST(\"1\",\"7\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K39": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"7\",\"expected_revenue\")", "dependencies": [], "value": 22500}}, "L39": {"formula": {"text": "=LIST(\"1\",\"7\",\"stage_id\")", "dependencies": [], "value": "Qualified"}}, "I40": {"formula": {"text": "=LIST(\"1\",\"8\",\"name\")", "dependencies": [], "value": "Customizable Desk"}}, "J40": {"formula": {"text": "=LIST(\"1\",\"8\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K40": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"8\",\"expected_revenue\")", "dependencies": [], "value": 15000}}, "L40": {"formula": {"text": "=LIST(\"1\",\"8\",\"stage_id\")", "dependencies": [], "value": "Proposition"}}, "I41": {"formula": {"text": "=LIST(\"1\",\"9\",\"name\")", "dependencies": [], "value": "Open Space Design"}}, "J41": {"formula": {"text": "=LIST(\"1\",\"9\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K41": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"9\",\"expected_revenue\")", "dependencies": [], "value": 11000}}, "L41": {"formula": {"text": "=LIST(\"1\",\"9\",\"stage_id\")", "dependencies": [], "value": "Proposition"}}, "I42": {"formula": {"text": "=LIST(\"1\",\"10\",\"name\")", "dependencies": [], "value": "Office Design and Architecture"}}, "J42": {"formula": {"text": "=LIST(\"1\",\"10\",\"user_id\")", "dependencies": [], "value": "<PERSON>"}}, "K42": {"format": "#,##0.00", "formula": {"text": "=LIST(\"1\",\"10\",\"expected_revenue\")", "dependencies": [], "value": 9000}}, "L42": {"formula": {"text": "=LIST(\"1\",\"10\",\"stage_id\")", "dependencies": [], "value": "Proposition"}}, "I30": {"style": 1, "content": "Top 10 Open Leads"}, "I31": {"style": 1, "content": ""}, "J30": {"style": 1, "content": ""}, "J31": {"style": 1, "content": ""}, "K30": {"style": 1, "content": ""}, "K31": {"style": 1, "content": ""}, "L30": {"style": 1, "content": ""}, "L31": {"style": 1, "content": ""}, "I44": {"style": 9, "content": "[Click here to go to pipeline](odoo://view/{\"viewType\":\"kanban\",\"action\":{\"domain\":\"[[\\\"type\\\", \\\"=\\\", \\\"opportunity\\\"]]\",\"context\":{\"group_by\":[]},\"modelName\":\"crm.lead\",\"views\":[[false,\"kanban\"],[false,\"list\"],[false,\"calendar\"],[false,\"pivot\"],[false,\"graph\"],[false,\"cohort\"],[false,\"dashboard\"],[false,\"map\"],[false,\"form\"],[false,\"activity\"]]},\"name\":\"Pipeline\"})"}}, "conditionalFormats": [{"rule": {"type": "ColorScaleRule", "minimum": {"type": "value", "color": 16777215}, "maximum": {"type": "value", "color": 9684093}}, "ranges": ["D42:D54"], "id": "b2b15f40-0d23-4ad7-8f44-6305685a47eb"}], "figures": [{"id": "877bde0f-16d0-48fb-abf7-6b3526a05d98", "x": 0, "y": 44, "height": 318, "width": 779, "tag": "chart", "data": {"title": "Actuals vs Target", "dataSets": ["B41:B53", "C41:C53"], "labelRange": "A42:A53", "type": "bar", "dataSetsHaveTitle": true, "background": "#FFFFFF", "verticalAxisPosition": "left", "legendPosition": "top", "stackedBar": false}}, {"id": "cf8cc197-6ad9-4ffc-981d-1bde264ffe7c", "x": 0, "y": 371, "height": 269, "width": 393, "tag": "chart", "data": {"title": "Expected Revenue by Stage", "dataSets": ["C32:C36"], "labelRange": "A33:A36", "type": "bar", "dataSetsHaveTitle": true, "background": "#FFFFFF", "verticalAxisPosition": "left", "legendPosition": "top", "stackedBar": false}}, {"id": "979e6431-5019-4403-88cb-fd616c90f079", "x": 401, "y": 371, "height": 269, "width": 378, "tag": "chart", "data": {"title": "Opportunities by Stage", "dataSets": ["B32:B36"], "labelRange": "A33:A36", "type": "bar", "dataSetsHaveTitle": true, "background": "#FFFFFF", "verticalAxisPosition": "left", "legendPosition": "top", "stackedBar": false}}, {"id": "297488ab-63ab-42bd-b99d-4b1003d5e23d", "x": 788, "y": 44, "height": 318, "width": 608, "tag": "chart", "data": {"title": "Loss Reasons", "dataSets": ["B58:B59"], "labelRange": "A59", "type": "pie", "dataSetsHaveTitle": true, "background": "#FFFFFF", "verticalAxisPosition": "left", "legendPosition": "top", "stackedBar": false}}, {"id": "1bdb22be-5f1b-4137-89e9-ae330047876a", "x": 788, "y": 372, "height": 268, "width": 609, "tag": "chart", "data": {"title": "Lost Opportunities by Stage", "dataSets": ["F32:F36"], "labelRange": "E33:E36", "type": "bar", "dataSetsHaveTitle": true, "background": "#FFFFFF", "verticalAxisPosition": "left", "legendPosition": "top", "stackedBar": false}}], "areGridLinesVisible": true}], "entities": {}, "styles": {"1": {"fillColor": "#cfe2f3", "fontSize": 12, "bold": true, "align": "center"}, "2": {"fillColor": "#f2f2f2", "textColor": "#756f6f"}, "3": {"fillColor": "#f2f2f2", "bold": true}, "4": {"fillColor": "#f2f2f2"}, "5": {"fillColor": "#f3f3f3", "textColor": "#666666"}, "6": {"fillColor": "#fff2cc"}, "7": {"fillColor": "#cfe2f3", "fontSize": 14, "bold": true, "align": "center"}, "8": {"fillColor": "#ffffff", "fontSize": 14, "bold": true, "align": "center"}, "9": {"textColor": "#00f", "underline": true}}, "borders": {}, "revisionId": "f345abe5-6fa8-48b7-9c11-cf85fdfe3e2f", "pivots": {"1": {"model": "crm.lead", "rowGroupBys": ["stage_id"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": [["type", "=", "opportunity"]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 1, "isLoaded": false, "promise": {}}, "2": {"model": "crm.lead", "rowGroupBys": ["date_deadline:month"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], ["date_deadline", "!=", false]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 2, "isLoaded": false, "promise": {}}, "3": {"model": "crm.lead", "rowGroupBys": ["lost_reason"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], "&", ["active", "=", false], ["probability", "=", 0]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 3, "isLoaded": false, "promise": {}}, "4": {"model": "crm.lead", "rowGroupBys": ["stage_id"], "colGroupBys": [], "measures": [{"field": "__count", "operator": "sum"}, {"field": "expected_revenue", "operator": "sum"}], "domain": ["&", ["type", "=", "opportunity"], "&", ["active", "=", false], ["probability", "=", 0]], "context": {"lang": "en_US", "tz": "Europe/London", "uid": 2, "allowed_company_ids": [1], "params": {"action": 517, "cids": 1, "menu_id": 399, "model": "crm.lead", "view_type": "pivot"}, "default_type": "opportunity", "search_default_assigned_to_me": 1}, "id": 4, "isLoaded": false, "promise": {}}}, "lists": {"1": {"model": "crm.lead", "domain": ["&", ["type", "=", "opportunity"], ["won_status", "=", "pending"]], "orderBy": [{"name": "expected_revenue", "asc": false}, {"name": "stage_id", "asc": false}], "context": {"lang": "en_US", "tz": "Asia/Kolkata", "uid": 2, "allowed_company_ids": [1], "default_type": "opportunity", "search_default_assigned_to_me": 1, "default_team_id": 1}, "columns": ["name", "contact_name", "email_from", "phone", "company_id", "user_id", "activity_ids", "my_activity_date_deadline", "expected_revenue", "recurring_revenue_monthly", "stage_id"], "id": "1"}}, "globalFilters": [{"id": "b481232d-a842-45c6-9686-184d290decce", "label": "Year", "type": "date", "rangeType": "year", "fields": {"1": {"field": "date_deadline", "type": "date"}, "2": {"field": "date_deadline", "type": "date"}, "3": {"field": "date_closed", "type": "datetime"}, "4": {"field": "date_closed", "type": "datetime"}}, "defaultValue": {"year": "this_year"}, "pivotFields": {"1": {"field": "date_deadline", "type": "date"}, "2": {"field": "date_deadline", "type": "date"}, "3": {"field": "date_closed", "type": "datetime"}, "4": {"field": "date_closed", "type": "datetime"}}, "listFields": {}}]}