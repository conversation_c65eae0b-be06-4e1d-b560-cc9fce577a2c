# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event_barcode
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:26
#, python-format
msgid "%s is already registered"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:24
#, python-format
msgid "%s is successfully registered"
msgstr ""

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_registration_badge
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:18
#, python-format
msgid "Attendee"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:33
#: model:ir.model.fields,field_description:event_barcode.field_event_registration__barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_registration_badge
#, python-format
msgid "Barcode"
msgstr "Barkod"

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
#: model_terms:ir.ui.view,arch_db:event_barcode.event_event_view_form_inherit_barcode
msgid "Barcode Interface"
msgstr "Barkod interfejs"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:32
#, python-format
msgid "Barcode Scanning"
msgstr ""

#. module: event_barcode
#: sql_constraint:event.registration:0
msgid "Barcode should be unique per event"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:20
#, python-format
msgid "Canceled registration"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:95
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:66
#, python-format
msgid "Error"
msgstr "Greška"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_event_registration
msgid "Event Registration"
msgstr "Registracija događaja"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:66
#, python-format
msgid "Invalid user input"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:96
#, python-format
msgid "Print"
msgstr "Ispis"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:42
#, python-format
msgid "Registration"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:87
#, python-format
msgid "Registration Summary"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:13
#, python-format
msgid "This ticket is not valid for this event"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:104
#, python-format
msgid "View"
msgstr "Pregled"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:34
#, python-format
msgid "Waiting for barcode scan..."
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:117
#, python-format
msgid "Warning"
msgstr "Upozorenje"
