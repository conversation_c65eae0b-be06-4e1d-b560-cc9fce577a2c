# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_barcode
# 
# Translators:
# <PERSON> <george_tarasid<PERSON>@yahoo.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <alexand<PERSON>@gnugr.org>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_registration_badge
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_event_template_badge
msgid ""
"<i class=\"fa-2x fa fa-barcode\" title=\"Barcode\" role=\"img\" aria-"
"label=\"Barcode\"/>"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#: model:ir.model.fields,field_description:event_barcode.field_event_registration__barcode
#, python-format
msgid "Barcode"
msgstr "Barcode"

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr ""

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Ονοματολογίες Barcode"

#. module: event_barcode
#: model:ir.model.constraint,message:event_barcode.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Canceled registration"
msgstr "Ακυρωμένη Συμμετοχή"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Close"
msgstr "Κλείσιμο"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company"
msgstr "Εταιρία"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company Logo"
msgstr ""

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Confirm"
msgstr "Επιβεβαίωση"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Confirm attendance for"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Event"
msgstr "Συμβάν"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_event_registration
msgid "Event Registration"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Events"
msgstr "Εκδηλώσεις"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Invalid ticket"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Name"
msgstr "Περιγραφή"

#. module: event_barcode
#: model:ir.model.fields,field_description:event_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Payment"
msgstr "Πληρωμή"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Print"
msgstr "Εκτύπωση"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Registration"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:0
#: model:ir.ui.menu,name:event_barcode.menu_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event_barcode.event_event_view_form
#, python-format
msgid "Registration Desk"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration Summary"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration confirmed"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Scan a badge"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Select Attendee"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "The registration must be paid"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is for another event"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is not for an ongoing event"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Ticket"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "View"
msgstr "Προβολή"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Warning"
msgstr "Προσοχή"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Welcome to"
msgstr "Καλώς Ήρθατε στο"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is already registered"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is successfully registered"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "or"
msgstr "ή"
