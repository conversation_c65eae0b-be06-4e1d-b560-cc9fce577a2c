# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_barcode
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 19:54+0000\n"
"PO-Revision-Date: 2024-08-13 19:54+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.event_report_template_foldable_badge_inherit_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.event_report_template_full_page_ticket_inherit_barcode
msgid ""
"<i class=\"fa-2x fa fa-barcode\" title=\"Barcode\" role=\"img\" aria-"
"label=\"Barcode\"/>"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#: model:ir.model.fields,field_description:event_barcode.field_event_registration__barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.event_report_template_full_page_ticket_inherit_barcode
#, python-format
msgid "Barcode"
msgstr ""

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr ""

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr ""

#. module: event_barcode
#: model:ir.model.constraint,message:event_barcode.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Canceled registration"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Close"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company Logo"
msgstr ""

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Confirm"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Confirm attendance for"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Event"
msgstr ""

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_event_registration
msgid "Event Registration"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Events"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Invalid ticket"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Name"
msgstr ""

#. module: event_barcode
#: model:ir.model.fields,field_description:event_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Payment"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Print"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Registration"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:0
#: model:ir.ui.menu,name:event_barcode.menu_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event_barcode.event_event_view_form
#, python-format
msgid "Registration Desk"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration Summary"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration confirmed"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Scan a badge"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Select Attendee"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "The registration must be paid"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is for another event"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is not for an ongoing event"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Ticket"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "View"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Warning"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Welcome to"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is already registered"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is successfully registered"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "or"
msgstr ""
