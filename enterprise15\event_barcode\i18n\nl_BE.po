# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event_barcode
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl_BE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:26
#, python-format
msgid "%s is already registered"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:24
#, python-format
msgid "%s is successfully registered"
msgstr ""

#. module: event_barcode
#: model:ir.ui.view,arch_db:event_barcode.report_registration_badge
msgid "&amp;nbsp;"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:18
#: model:ir.model,name:event_barcode.model_event_registration
#, python-format
msgid "Attendee"
msgstr ""

#. module: event_barcode
#: model:ir.model.fields,field_description:event_barcode.field_event_registration_barcode
msgid "Barcode"
msgstr ""

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
#: model:ir.ui.view,arch_db:event_barcode.event_event_view_form_inherit_barcode
msgid "Barcode Interface"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:32
#, python-format
msgid "Barcode Scanning"
msgstr ""

#. module: event_barcode
#: sql_constraint:event.registration:0
msgid "Barcode should be unique per event"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:20
#, python-format
msgid "Canceled registration"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:95
#, python-format
msgid "Close"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:66
#, python-format
msgid "Error"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:66
#, python-format
msgid "Invalid user input"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:96
#, python-format
msgid "Print"
msgstr "Afdrukken"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:87
#, python-format
msgid "Registration Summary"
msgstr ""

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:13
#, python-format
msgid "This ticket is not valid for this event"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:104
#, python-format
msgid "View"
msgstr ""

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:34
#, python-format
msgid "Waiting for barcode scan..."
msgstr ""
