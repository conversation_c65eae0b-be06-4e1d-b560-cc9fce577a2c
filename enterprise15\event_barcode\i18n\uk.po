# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_barcode
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://www.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_registration_badge
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_event_template_badge
msgid ""
"<i class=\"fa-2x fa fa-barcode\" title=\"Barcode\" role=\"img\" aria-"
"label=\"Barcode\"/>"
msgstr ""
"<i class=\"fa-2x fa fa-barcode\" title=\"Barcode\" role=\"img\" aria-"
"label=\"Barcode\"/>"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#: model:ir.model.fields,field_description:event_barcode.field_event_registration__barcode
#, python-format
msgid "Barcode"
msgstr "Штрих-код"

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr "Інтерфейс роботи зі штрих-кодами"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Номенклатура штрих-кодів"

#. module: event_barcode
#: model:ir.model.constraint,message:event_barcode.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr "Штрих-код повинен бути унікальним"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Canceled registration"
msgstr "Реєстрація завершена"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Close"
msgstr "Закрити"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company"
msgstr "Компанія"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company Logo"
msgstr "Логотип компанії"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Confirm"
msgstr "Підтвердити"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Confirm attendance for"
msgstr "Підтвердити участь для"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Event"
msgstr "Подія"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_event_registration
msgid "Event Registration"
msgstr "Реєстрація події"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Events"
msgstr "Події"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Invalid ticket"
msgstr "Недійсний квиток"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Name"
msgstr "Назва"

#. module: event_barcode
#: model:ir.model.fields,field_description:event_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Номенклатура"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Payment"
msgstr "Оплата"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Print"
msgstr "Друк"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Registration"
msgstr "Реєстрація"

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:0
#: model:ir.ui.menu,name:event_barcode.menu_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event_barcode.event_event_view_form
#, python-format
msgid "Registration Desk"
msgstr "Панель реєстрації"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration Summary"
msgstr "Резюме реєстрації"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration confirmed"
msgstr "Реєстрація підтверджена"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Scan a badge"
msgstr "Відскануйте значок"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Select Attendee"
msgstr "Виберіть учасника"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "The registration must be paid"
msgstr "Реєстрація має бути сплачена"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is for another event"
msgstr "Цей квиток для іншої події"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is not for an ongoing event"
msgstr "Цей квиток не на цю подію"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Ticket"
msgstr "Заявка"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "View"
msgstr "Перегляд"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Warning"
msgstr "Сповіщення"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Welcome to"
msgstr "Ласкаво просимо до"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is already registered"
msgstr "уже зареєстровано"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is successfully registered"
msgstr "успішно зареєстровано"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "or"
msgstr "або"
