# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_barcode
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_registration_badge
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.report_event_template_badge
msgid ""
"<i class=\"fa-2x fa fa-barcode\" title=\"Barcode\" role=\"img\" aria-"
"label=\"Barcode\"/>"
msgstr ""
"<i class=\"fa-2x fa fa-barcode\" title=\"Barcode\" role=\"img\" aria-"
"label=\"Barcode\"/>"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#: model:ir.model.fields,field_description:event_barcode.field_event_registration__barcode
#, python-format
msgid "Barcode"
msgstr "条码"

#. module: event_barcode
#: model:ir.actions.client,name:event_barcode.event_barcode_action_main_view
msgid "Barcode Interface"
msgstr "条码接口"

#. module: event_barcode
#: model_terms:ir.ui.view,arch_db:event_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "条码命名规则"

#. module: event_barcode
#: model:ir.model.constraint,message:event_barcode.constraint_event_registration_barcode_event_uniq
msgid "Barcode should be unique"
msgstr "条码应该是唯一的"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Canceled registration"
msgstr "取消登记记录"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Close"
msgstr "关闭"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company"
msgstr "公司"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Company Logo"
msgstr "公司 Logo"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Confirm"
msgstr "确认"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Confirm attendance for"
msgstr "确认出席"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Event"
msgstr "活动"

#. module: event_barcode
#: model:ir.model,name:event_barcode.model_event_registration
msgid "Event Registration"
msgstr "活动登记"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Events"
msgstr "活动"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Invalid ticket"
msgstr "无效门票"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Name"
msgstr "名称"

#. module: event_barcode
#: model:ir.model.fields,field_description:event_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "命名规则"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Payment"
msgstr "支付"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Print"
msgstr "打印"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Registration"
msgstr "登记"

#. module: event_barcode
#: code:addons/event_barcode/controllers/main.py:0
#: model:ir.ui.menu,name:event_barcode.menu_event_registration_desk
#: model_terms:ir.ui.view,arch_db:event_barcode.event_event_view_form
#, python-format
msgid "Registration Desk"
msgstr "登记处"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration Summary"
msgstr "登记摘要"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Registration confirmed"
msgstr "确认注册"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Scan a badge"
msgstr "扫描徽标"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Select Attendee"
msgstr "选择参会者"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "The registration must be paid"
msgstr "必须缴纳登记费。"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is for another event"
msgstr "这张票是另一个活动"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "This ticket is not for an ongoing event"
msgstr "该票不适用于正在进行的活动"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Ticket"
msgstr "门票"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "View"
msgstr "视图"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/js/event_barcode.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "Welcome to"
msgstr "欢迎"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is already registered"
msgstr " 已被注册"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "is successfully registered"
msgstr "已经成功注册"

#. module: event_barcode
#. openerp-web
#: code:addons/event_barcode/static/src/xml/event_barcode.xml:0
#, python-format
msgid "or"
msgstr "或"
