<?xml version="1.0" encoding="utf-8"?>

<templates xml:space="preserve">

    <t t-name="event_barcode_template">
        <div class="o_event_barcode_bg o_home_menu_background">
            <div class="o_event_barcode_main">
                <a href="#" class="o_event_previous_menu float-left"><i class="fa fa-chevron-left"></i></a>
                <div class="text-center">
                    <h1 t-esc="widget.data.name"/>
                    <p>
                        <t t-if="widget.data.city and widget.data.country">
                            <t t-esc="widget.data.city"/> - <t t-esc="widget.data.country"/>
                        </t>
                        <t t-if="widget.data.city and !widget.data.country" t-esc="widget.data.city"/>
                        <t t-if="widget.data.country and !widget.data.city" t-esc="widget.data.country"/>
                    </p>
                    <h2><small>Welcome to</small> <t t-esc="widget.data.company_name"/></h2>
                    <img t-if="widget.data.company_id" t-attf-src="/web/image/res.company/{{widget.data.company_id}}/logo_web" alt="Company Logo" class="o_event_barcode_company_image"/>
                </div>
                <div class="row">
                    <div class="col-sm-5 mt16">
                        <img class="o_event_barcode_image" src="/barcodes/static/img/barcode.png" alt="Barcode"/>
                        <h5 class="mt8 mb0 text-muted">Scan a badge</h5>
                    </div>
                    <div class="col-sm-2 mt32">
                        <h4 class="mt0 mb8"><i>or</i></h4>
                    </div>
                    <div class="col-sm-5 mt16">
                        <button class="o_event_select_attendee btn btn-primary mb16">
                            <div class="mb16 mt16">Select Attendee</div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <t t-name="event_registration_summary">
        <div class="row">
            <div class="col-lg-8 offset-lg-2">
                <div class="text-center mb-3" t-if="registration.partner_id">
                    <img t-attf-src="/web/image/res.partner/{{registration.partner_id}}/avatar_128" alt="Registration" class="o_image_64_cover"/>
                </div>
                <div t-if="registration.status == 'confirmed_registration'" class="alert alert-success text-center" role="alert">
                    <t t-esc="registration.name"/> is successfully registered
                </div>
                <div t-else="" class="alert alert-warning text-center" role="alert">
                    <t t-if="registration.status == 'need_manual_confirmation'">
                        <span>This ticket is for another event<br/>
                        Confirm attendance for <t t-esc="registration.name"/> ?</span>
                    </t>
                     <t t-elif="registration.status == 'not_ongoing_event'">
                        <span>This ticket is not for an ongoing event</span>
                    </t>
                    <t t-elif="registration.status == 'canceled_registration'">
                        <span>Canceled registration</span>
                    </t>
                    <t t-elif="registration.status == 'already_registered'">
                        <t t-esc="registration.name"/><span> is already registered</span>
                    </t>
                </div>
                <div t-if="registration.has_to_pay" class="alert alert-danger text-center" role="alert">
                    The registration must be paid
                </div>
            </div>
        </div>
        <div id="registration_information" class="row">
            <div class="col-lg-12">
                <table class="table table-striped">
                    <tr><td>Event</td><td><t t-esc="registration.event_display_name"/></td></tr>
                    <tr t-if="!registration.event_id and registration.company_name"><td>Company</td><td><t t-esc="registration.company_name"/></td></tr>
                    <tr><td>Name</td><td><t t-esc="registration.name"/></td></tr>
                    <tr t-if="registration.ticket_name"><td>Ticket</td><td><t t-esc="registration.ticket_name"/></td></tr>
                    <tr t-if="registration.payment_status_value"><td>Payment</td><td><t t-esc="registration.payment_status_value"/></td></tr>
                </table>
            </div>
        </div>
    </t>

</templates>
