<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.event.barcode</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="65"/>
            <field name="inherit_id" ref="event.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='event_barcode']" position="after">
                    <div class="content-group row mt16" attrs="{'invisible': [('module_event_barcode', '=', False)]}">
                        <label for="barcode_nomenclature_id" string="Barcode Nomenclature" class="col-lg-3 o_light_label"/>
                        <field name="barcode_nomenclature_id" attrs="{'required': [('module_event_barcode', '=', True)]}"/>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
