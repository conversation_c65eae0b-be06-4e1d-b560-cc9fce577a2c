<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="event_view_gantt" model="ir.ui.view">
        <field name="name">event.event.gantt</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <gantt
                plan="0"
                color="stage_id"
                date_start="date_begin"
                date_stop="date_end"
                default_group_by="address_id"
                default_scale="year">
                <field name="name"/>
                <field name="stage_id"/>
                <templates>
                    <div t-name="gantt-popover">
                        <div><strong>Stage — </strong><t t-esc="stage_id[1]"/></div>
                        <div class="col-2 px-0 text-nowrap"><t t-esc="userTimezoneStartDate.format('l LT ')"/><i class="fa fa-long-arrow-right" title="Arrow"/><t t-esc="userTimezoneStopDate.format(' l LT')"/></div>
                    </div>
                </templates>
            </gantt>
        </field>
    </record>

    <record id="event_event_view_map" model="ir.ui.view">
        <field name="name">event.event.view.map</field>
        <field name="model">event.event</field>
        <field name="arch" type="xml">
            <map res_partner="address_id" hide_name="true">
                <field name="name" string="Name"/>
                <field name="address_id" string="Venue"/>
            </map>
        </field>
    </record>

    <record id="event.action_event_view" model="ir.actions.act_window">
        <field name="view_mode">kanban,calendar,tree,gantt,form,pivot,graph,map</field>
    </record>

</odoo>
