# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_social
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 11:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: event_social
#: code:addons/event_social/models/event_mail.py:0
#: code:addons/event_social/models/event_type_mail.py:0
#, python-format
msgid ""
"As social posts have no recipients, they cannot be triggered by "
"registrations."
msgstr ""

#. module: event_social
#: model:ir.model,name:event_social.model_event_mail
msgid "Event Automated Mailing"
msgstr ""

#. module: event_social
#: model:ir.model,name:event_social.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr ""

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__notification_type
msgid "Send"
msgstr ""

#. module: event_social
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__notification_type__social_post
msgid "Social Post"
msgstr ""
