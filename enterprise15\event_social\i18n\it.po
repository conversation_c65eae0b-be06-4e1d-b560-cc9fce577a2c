# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_social
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Fasterling-Nesselbosch, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Friederike Fasterling-Nesselbosch, 2021\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_social
#: code:addons/event_social/models/event_mail.py:0
#: code:addons/event_social/models/event_type_mail.py:0
#, python-format
msgid ""
"As social posts have no recipients, they cannot be triggered by "
"registrations."
msgstr ""
"Poiché i post sociali non hanno destinatari, non possono essere attivati "
"dalle registrazioni."

#. module: event_social
#: model:ir.model,name:event_social.model_event_mail
msgid "Event Automated Mailing"
msgstr "Messaggi automatici dell'evento"

#. module: event_social
#: model:ir.model,name:event_social.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Pianificazione e-mail  su categoria evento"

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__notification_type
msgid "Send"
msgstr "Invia"

#. module: event_social
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__notification_type__social_post
msgid "Social Post"
msgstr "Post social"
