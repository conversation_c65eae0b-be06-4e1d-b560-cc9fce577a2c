# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_social
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://www.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: event_social
#: code:addons/event_social/models/event_mail.py:0
#: code:addons/event_social/models/event_type_mail.py:0
#, python-format
msgid ""
"As social posts have no recipients, they cannot be triggered by "
"registrations."
msgstr ""
"Оскільки повідомлення в соціальних мережах не мають одержувачів, вони не "
"можуть бути ініційовані реєстрацією."

#. module: event_social
#: model:ir.model,name:event_social.model_event_mail
msgid "Event Automated Mailing"
msgstr "Автоматична розсилка подій"

#. module: event_social
#: model:ir.model,name:event_social.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Планування електронних листів на категорію події"

#. module: event_social
#: model:ir.model.fields,field_description:event_social.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_social.field_event_type_mail__notification_type
msgid "Send"
msgstr "Надіслати"

#. module: event_social
#: model:ir.model.fields.selection,name:event_social.selection__event_mail__notification_type__social_post
#: model:ir.model.fields.selection,name:event_social.selection__event_type_mail__notification_type__social_post
msgid "Social Post"
msgstr "Публікація в соцмережах"
