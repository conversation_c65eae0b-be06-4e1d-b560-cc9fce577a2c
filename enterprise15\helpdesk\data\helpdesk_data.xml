<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="helpdesk_team1" model="helpdesk.team">
        <field name="name">Customer Care</field>
        <field name="alias_name">support</field>
        <field name="stage_ids" eval="False"/> <!-- eval=False to don't get the default stage. New stages are setted below-->
        <field name="use_sla" eval="True"/>
        <field name="member_ids" eval="[Command.link(ref('base.user_admin'))]"/>
    </record>

    <!-- stage "New" gets created by default with sequence 0-->
    <record id="stage_new" model="helpdesk.stage">
        <field name="name">New</field>
        <field name="sequence">0</field>
        <field name="team_ids" eval="[(4, ref('helpdesk_team1'))]"/>
        <field name="is_close" eval="False"/>
        <field name="template_id" ref="helpdesk.new_ticket_request_email_template"/>
    </record>
    <record id="stage_in_progress" model="helpdesk.stage">
        <field name="name">In Progress</field>
        <field name="sequence">1</field>
        <field name="team_ids" eval="[(4, ref('helpdesk_team1'))]"/>
        <field name="is_close" eval="False"/>
    </record>
    <record id="stage_solved" model="helpdesk.stage">
        <field name="name">Solved</field>
        <field name="team_ids" eval="[(4, ref('helpdesk_team1'))]"/>
        <field name="sequence">2</field>
        <field name="is_close" eval="True"/>
        <field name="fold" eval="True"/>
    </record>
    <record id="stage_cancelled" model="helpdesk.stage">
        <field name="name">Cancelled</field>
        <field name="sequence">3</field>
        <field name="team_ids" eval="[(4, ref('helpdesk_team1'))]"/>
        <field name="is_close" eval="True"/>
        <field name="fold" eval="True"/>
    </record>

    <record id="type_question" model="helpdesk.ticket.type">
        <field name="name">Question</field>
    </record>
    <record id="type_incident" model="helpdesk.ticket.type">
        <field name="name">Issue</field>
    </record>

    <!-- Share Button in action menu -->
    <record id="model_helpdesk_ticket_action_share" model="ir.actions.server">
        <field name="name">Share</field>
        <field name="model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="binding_model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">action = records.action_share()</field>
    </record>

    <!-- Report Filter -->
    <record id="helpdesk_sla_report_analysis_filter_status_per_deadline" model="ir.filters">
        <field name="name">Status Per Deadline</field>
        <field name="model_id">helpdesk.sla.report.analysis</field>
        <field name="context">{
            'pivot_column_groupby': ['sla_deadline:day'],
            'pivot_row_groupby': ['team_id', 'ticket_id', 'sla_id']
        }</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="active" eval="True"/>
    </record>

    <record id="helpdesk_sla_report_analysis_filter_stage_failed" model="ir.filters">
        <field name="name">Failed SLA Stage per Month</field>
        <field name="model_id">helpdesk.sla.report.analysis</field>
        <field name="context">{
            'pivot_measures': ['__count'],
            'pivot_column_groupby': ['create_date:month'],
            'pivot_row_groupby': ['team_id', 'sla_stage_id']
        }</field>
        <field name="domain">[]</field>
        <field name="user_id" eval="False"/>
        <field name="active" eval="True"/>
    </record>

</odoo>
