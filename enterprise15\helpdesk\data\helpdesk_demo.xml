<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tag_crm" model="helpdesk.tag">
        <field name="name">CRM</field>
    </record>
    <record id="tag_website" model="helpdesk.tag">
        <field name="name">Website</field>
    </record>
    <record id="tag_service" model="helpdesk.tag">
        <field name="name">Service</field>
    </record>
    <record id="tag_repair" model="helpdesk.tag">
        <field name="name">Repair</field>
    </record>

    <!-- Set the demo user as a helpdesk user -->
    <record id="group_helpdesk_user" model="res.groups">
        <field name="users" eval="[(4,ref('base.user_demo'))]"/>
    </record>

    <!-- helpdesk stages -->
    <record id="stage_done" model="helpdesk.stage">
        <field name="name">Done</field>
        <field name="sequence">2</field>
        <field name="template_id" ref="helpdesk.rating_ticket_request_email_template"/>
        <field name="is_close" eval="True"/>
        <field name="fold" eval="True"/>
    </record>

    <!-- helpdesk team -->
    <record id="helpdesk_team3" model="helpdesk.team">
        <field name="name">VIP Support</field>
        <field name="stage_ids" eval="[(6, 0, [ref('helpdesk.stage_new'), ref('helpdesk.stage_in_progress'), ref('helpdesk.stage_done'), ref('helpdesk.stage_cancelled')])]"/>
        <field name="use_sla" eval="True"/>
        <field name="use_rating" eval="True"/>
        <field name="member_ids" eval="[Command.link(ref('base.user_admin'))]"/>
    </record>

    <!-- SLA's -->
    <record id="helpdesk_sla_1" model="helpdesk.sla">
        <field name="name">2 days to start</field>
        <field name="team_id" ref="helpdesk_team1"/>
        <field name="stage_id" ref="stage_in_progress"/>
        <field name="time">16</field>
    </record>

    <record id="helpdesk_sla_2" model="helpdesk.sla">
        <field name="name">7 days to finish</field>
        <field name="team_id" ref="helpdesk_team1"/>
        <field name="stage_id" ref="stage_solved"/>
        <field name="time">56</field>
    </record>

    <!-- Tickets -->
    <record id="helpdesk_ticket_1" model="helpdesk.ticket">
        <field name="name">Kitchen collapsing</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="priority">3</field>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
    </record>
    <record id="helpdesk_ticket_2" model="helpdesk.ticket">
        <field name="name">Where can I download a catalog ?</field>
        <field name="priority">0</field>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
    </record>
    <record id="helpdesk_ticket_3" model="helpdesk.ticket">
        <field name="name">Warranty</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="priority">2</field>
        <field name="partner_id" ref="base.res_partner_main1" />
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="sla_reached_late" eval="True"/>
        <field name="description">
Hello,

I would like to know what kind of warranties you are offering for your products.

Here is my contact number: 123456789

Thank you,
Chester Reed
        </field>
    </record>
    <!-- fail the sla status -->
    <function model="helpdesk.sla.status" name="write">
        <value model="helpdesk.ticket" eval="obj().search([('id', '=', ref('helpdesk.helpdesk_ticket_3'))]).sla_status_ids.ids"/>
        <value eval="{'deadline': DateTime.now() - relativedelta(days=2)}"/>
    </function>

    <record id="helpdesk_ticket_4" model="helpdesk.ticket">
        <field name="name">Wood Treatment</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="partner_id" ref="base.res_partner_4" />
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="description">
Hello,

Is the wood from your furniture treated with a particular product? What would you recommend to maintain the quality of a dining table?

Your assistance would be greatly appreciated.

Thanks in Advance,
Azure Interior
        </field>
    </record>
    <record id="helpdesk_ticket_5" model="helpdesk.ticket">
        <field name="name">Chair dimensions</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="stage_id" ref="helpdesk.stage_solved"/>
        <field name="description">
Can you please tell me the dimensions of your “Office chair Black”? Also I am unable to find the information on your official site.

I look forward to your kind response.

Thank you!
        </field>
    </record>
    <record id="helpdesk_ticket_6" model="helpdesk.ticket">
        <field name="name">Lost key</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="partner_id" ref="base.res_partner_3"/>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="description">
Hello,

I bought a locker a few years ago and I, unfortunately, lost the key. I cannot retrieve the documents I had left in there without damaging the furniture item. What solution do you offer?

Thanks in advance for your help.
Kind regards,
Gemini Furniture
        </field>
    </record>
    <record id="helpdesk_ticket_7" model="helpdesk.ticket">
        <field name="name">Furniture delivery</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="partner_id" ref="base.res_partner_2"/>
        <field name="stage_id" ref="helpdesk.stage_cancelled"/>
        <field name="description">
Hi,

I was wondering if you were delivering the furniture or if we needed to pick it up at your warehouse?
If you do take care of the delivery, are there any extra costs?

Regards,
Deco Addict
        </field>
    </record>
    <record id="helpdesk_ticket_8" model="helpdesk.ticket">
        <field name="name">Cabinets in kit</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="partner_id" ref="base.res_partner_10"/>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="description">
Hello,

I would like to know if your cabinets come in a kit? They seem quite large and I am not sure they will fit through my front door.

Thank you for your help.
Best regards,
Jackson Group
        </field>
    </record>
    <record id="helpdesk_ticket_9" model="helpdesk.ticket">
        <field name="name">Missing user manual</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="description">
Hello,

I recently purchased one of your wardrobes in a kit. Unfortunately, I didn’t receive the user manual, so I cannot assemble the item. Could you send me this document?

Thank you.
Kind regards,
        </field>
    </record>
    <record id="helpdesk_ticket_10" model="helpdesk.ticket">
        <field name="name">Ugly Chair</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_id" ref="base.res_partner_2"/>
        <field name="stage_id" ref="helpdesk.stage_done"/>
        <field name="description">
Hello,

I purchased a chair from you last week. I now realize it doesn’t go well with the rest of my furniture, so I would like to return it and to get a refund.

Regards,
Deco Addict
        </field>
    </record>
    <record id="helpdesk_ticket_11" model="helpdesk.ticket">
        <field name="name">Couch</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="description">
Hello,

The couch I ordered was scratched during the delivery. Would it be possible to have a gesture of goodwill?

Thank you for considering my request.
Best regards,
        </field>
    </record>
    <record id="helpdesk_ticket_12" model="helpdesk.ticket">
        <field name="name">Chair wheels aren’t working</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="partner_id" ref="base.res_partner_main1"/>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="priority">3</field>
        <field name="tag_ids" eval="[(6,0,[ref('helpdesk.tag_repair'),ref('helpdesk.tag_service')])]"/>
        <field name="create_date" eval="DateTime.now()- relativedelta(days=1)"/>
        <field name="description">
The chair I bought last year isn't turning correctly anymore. Are you selling spare parts for the wheels?

Thank you in advance for your help.
Chester Reed
        </field>
    </record>
    <!-- Fail the sla on ticket -->
    <function model="helpdesk.sla.status" name="write">
        <value model="helpdesk.ticket" eval="obj().search([('id', '=', ref('helpdesk.helpdesk_ticket_12'))]).sla_status_ids.ids"/>
        <value eval="{'deadline': DateTime.now()}"/>
    </function>

    <record id="helpdesk_ticket_13" model="helpdesk.ticket">
        <field name="name">Cabinet Colour and Lock aren't proper</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_id" ref="base.res_partner_10"/>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="priority">3</field>
        <field name="tag_ids" eval="[(6,0,[ref('helpdesk.tag_repair'),ref('helpdesk.tag_service')])]"/>
        <field name="description">
Hi,

I purchased a "Cabinet With Doors" from your store a few days ago. The lock is not working properly and the color is wrong. This is unacceptable! I am asking for a product that corresponds to my order and that matches the quality you are advertising.

Regards,
The Jackson Group
        </field>
    </record>
    <record id="helpdesk_ticket_14" model="helpdesk.ticket">
        <field name="name">Lamp Stand is bent</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="partner_id" ref="base.res_partner_4"/>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="priority">2</field>
        <field name="description">
Hello,

Yesterday I purchased a lamp stand from your site but the product I received is bent.

Would it be possible to get a replacement?

Regards,
Ready Mat
        </field>
    </record>
    <record id="helpdesk_ticket_15" model="helpdesk.ticket">
        <field name="name">Table legs are unbalanced</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="priority">3</field>
        <field name="tag_ids" eval="[(6,0,[ref('helpdesk.tag_repair'),ref('helpdesk.tag_service')])]"/>
        <field name="description">
Hi,

A few days ago, I bought a Four Persons Desk. While assembling it in my office, I found that the legs of the table were not properly balanced. Could you please come and fix this?

Kindly do this as early as possible.

Best,
Azure Interior
        </field>
    </record>
    <record id="helpdesk_ticket_16" model="helpdesk.ticket">
        <field name="name">Drawer’s slides and handle have a defect</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_id" ref="base.res_partner_2"/>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="description">
Hi,

I have purchased a "Drawer" from your store but the slides and the handle seem to have a defect.

Would it be possible for you to fix it?

Regards,
Deco
        </field>
    </record>
    <record id="helpdesk_ticket_17" model="helpdesk.ticket">
        <field name="name">Want to change the place of the dining area</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_question"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="partner_id" ref="base.res_partner_3"/>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="description">
Hello,

I want to change the location of the dining area and would like your advice.

Hope to hear from you soon.

Best,
Gemini Furniture
        </field>
    </record>
    <record id="helpdesk_ticket_18" model="helpdesk.ticket">
        <field name="name">Received Product is damaged</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_demo"/>
        <field name="partner_id" ref="base.res_partner_12"/>
        <field name="stage_id" ref="helpdesk.stage_done"/>
        <field name="description">
Hi,

I ordered a "Table Kit" from your store but the delivered product is damaged. I demand a refund as soon as possible.

Regards,
        </field>
    </record>
    <record id="helpdesk_ticket_19" model="helpdesk.ticket">
        <field name="name">Delivered wood panel is not what I ordered</field>
        <field name="team_id" ref="helpdesk.helpdesk_team3"/>
        <field name="ticket_type_id" ref="helpdesk.type_incident"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="partner_id" ref="base.res_partner_1"/>
        <field name="stage_id" ref="helpdesk.stage_done"/>
        <field name="description">
Hello,

I ordered a wood panel from your online store, but the delivered product is not what I had ordered.

Could you please replace it with the right product?
Waiting for your response.

Best,
Wood Corner
        </field>
    </record>
    <function model="helpdesk.ticket" name="rating_apply" eval="([ref('helpdesk_ticket_18')], 3, None,'Good Service')"/>
    <function model="helpdesk.ticket" name="rating_apply" eval="([ref('helpdesk_ticket_19')], 5, None,'Awesome Service.\nLove to use your product')"/>

</odoo>
