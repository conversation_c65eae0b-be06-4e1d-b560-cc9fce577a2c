<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">

    <!-- Helpdesk-specific activities, for automatic generation mainly -->
    <record id="mail_act_helpdesk_handle" model="mail.activity.type">
        <field name="name">Handle Ticket</field>
        <field name="icon">fa-life-ring</field>
        <field name="res_model">helpdesk.ticket</field>
    </record>

    <!-- Ticket related subtypes for messaging / Chatter -->
    <record id="mt_ticket_new" model="mail.message.subtype">
        <field name="name">Ticket Created</field>
        <field name="sequence">0</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="hidden" eval="True"/>
        <field name="description">Ticket created</field>
    </record>
    <record id="mt_ticket_rated" model="mail.message.subtype">
        <field name="name">Ticket Rated</field>
        <field name="sequence">5</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="hidden" eval="False"/>
    </record>
    <record id="mt_ticket_stage" model="mail.message.subtype">
        <field name="name">Stage Changed</field>
        <field name="sequence">10</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="False"/>
        <field name="internal" eval="True"/>
        <field name="description">Stage Changed</field>
    </record>
    <record id="mt_ticket_refund_posted" model="mail.message.subtype">
        <field name="name">Refund Posted</field>
        <field name="sequence">11</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="description">Refund Posted</field>
    </record>
    <record id="mt_ticket_return_done" model="mail.message.subtype">
        <field name="name">Return Done</field>
        <field name="sequence">12</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="description">Return Done</field>
    </record>
    <record id="mt_ticket_repair_done" model="mail.message.subtype">
        <field name="name">Repair Done</field>
        <field name="sequence">13</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="description">Repair Done</field>
    </record>

    <!-- Team related subtypes for messaging / Chatter -->
    <record id="mt_team_ticket_new" model="mail.message.subtype">
        <field name="name">Ticket Created</field>
        <field name="sequence">0</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="parent_id" ref="mt_ticket_new"/>
        <field name="relation_field">team_id</field>
    </record>
    <record id="mt_team_ticket_rated" model="mail.message.subtype">
        <field name="name">Ticket Rated</field>
        <field name="sequence">5</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="parent_id" ref="mt_ticket_rated"/>
        <field name="relation_field">team_id</field>
    </record>
    <record id="mt_team_ticket_stage" model="mail.message.subtype">
        <field name="name">Ticket Stage Changed</field>
        <field name="sequence">10</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="False"/>
        <field name="internal" eval="True"/>
        <field name="parent_id" ref="mt_ticket_stage"/>
        <field name="relation_field">team_id</field>
    </record>
    <record id="mt_team_ticket_refund_posted" model="mail.message.subtype">
        <field name="name">Refund Posted</field>
        <field name="sequence">11</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="parent_id" ref="mt_ticket_refund_posted"/>
        <field name="description">Refund Posted</field>
        <field name="relation_field">team_id</field>
    </record>
    <record id="mt_team_ticket_return_done" model="mail.message.subtype">
        <field name="name">Return Done</field>
        <field name="sequence">12</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="parent_id" ref="mt_ticket_return_done"/>
        <field name="description">Return Done</field>
        <field name="relation_field">team_id</field>
    </record>
    <record id="mt_team_ticket_repair_done" model="mail.message.subtype">
        <field name="name">Repair Done</field>
        <field name="sequence">13</field>
        <field name="res_model">helpdesk.team</field>
        <field name="default" eval="True"/>
        <field name="internal" eval="True"/>
        <field name="parent_id" ref="mt_ticket_repair_done"/>
        <field name="description">Repair Done</field>
        <field name="relation_field">team_id</field>
    </record>
</data></odoo>
