<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">
    <record id="new_ticket_request_email_template" model="mail.template">
        <field name="name">Ticket: Reception Acknowledgment</field>
        <field name="model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="subject">{{ object.display_name }}</field>
        <field name="email_from">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
        <field name="email_to">{{ (object.partner_email if not object.sudo().partner_id.email or object.sudo().partner_id.email != object.partner_email else '') }}</field>
        <field name="partner_to">{{ object.partner_id.id if object.sudo().partner_id.email and object.sudo().partner_id.email == object.partner_email else '' }}</field>
        <field name="body_html" type="html">
<div>
    Dear <t t-out="object.sudo().partner_id.name or 'Madam/Sir'">Madam/Sir</t>,<br /><br />
    Your request
    <t t-if="object.get_portal_url()">
        <a t-attf-href="/my/ticket/{{ object.id }}/{{ object.access_token }}" t-out="object.name or ''"></a>
    </t>
    has been received and is being reviewed by our <t t-out="object.team_id.name or ''">Table legs are unbalanced</t> team.
    The reference of your ticket is <t t-out="object.id or ''">15</t>.<br />

    <div style="text-align: center; padding: 16px 0px 16px 0px;">
        <a style="background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;" t-att-href="object.get_portal_url()">View the ticket</a><br/>
    </div>

    To add additional comments, reply to this email.<br/><br/>

    Thank you,<br/><br/>
    <t t-out="object.team_id.name or 'Helpdesk'">Helpdesk</t> Team.
</div>
        </field>
        <field name="lang">{{ object.partner_id.lang or object.user_id.lang or user.lang }}</field>
        <field name="auto_delete" eval="False"/>
    </record>

    <record id="solved_ticket_request_email_template" model="mail.template">
        <field name="name">Ticket: Solved</field>
        <field name="model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="subject">{{ object.display_name }}</field>
        <field name="email_from">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
        <field name="email_to">{{ (object.partner_email if not object.sudo().partner_id.email or object.sudo().partner_id.email != object.partner_email else '') }}</field>
        <field name="partner_to">{{ object.partner_id.id if object.sudo().partner_id.email and object.sudo().partner_id.email == object.partner_email else '' }}</field>
        <field name="body_html" type="html">
<div>
    Dear <t t-out="object.sudo().partner_id.name or 'Madam/Sir'">Madam/Sir</t>,<br /><br />
    This automatic message informs you that we have closed your ticket (reference <t t-out="object.id or ''">15</t>).
    We hope that the services provided have met your expectations.
    If you have any more questions or comments, don't hesitate to reply to this e-mail to re-open your ticket.<br /><br />
    Thank you for your cooperation.<br />
    Kind regards,<br /><br />
    <t t-out="object.team_id.name or 'Helpdesk'">Helpdesk</t> Team.
</div>
        </field>
        <field name="lang">{{ object.partner_id.lang or object.user_id.lang or user.lang }}</field>
        <field name="auto_delete" eval="False"/>
    </record>

    <record id="rating_ticket_request_email_template" model="mail.template">
        <field name="name">Ticket: Rating Request (requires rating enabled on team)</field>
        <field name="model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="subject">{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' }}: Service Rating Request</field>
        <field name="email_from">{{ object.rating_get_rated_partner_id().email_formatted }}</field>
        <field name="email_to">{{ (object.partner_email if not object.sudo().partner_id.email or object.sudo().partner_id.email != object.partner_email else '') }}</field>
        <field name="partner_to">{{ object.partner_id.id if object.sudo().partner_id.email and object.sudo().partner_id.email == object.partner_email else '' }}</field>
        <field name="body_html" type="html">
<div>
    <t t-set="access_token" t-value="object.rating_get_access_token()"/>
    <t t-set="partner" t-value="object.rating_get_partner_id()"/>
    <table border="0" cellpadding="0" cellspacing="0" style="width:100%; margin:0;">
    <tbody>
        <tr><td valign="top" style="font-size: 14px;">
            <t t-if="partner.name">
                Hello <t t-out="partner.name or ''">Brandon Freeman</t>,<br/>
            </t>
            <t t-else="">
                Hello,<br/>
            </t>
            Please take a moment to rate our services related to the ticket "<strong t-out="object.name or ''">Table legs are unbalanced</strong>"
            <t t-if="object.rating_get_rated_partner_id().name">
                assigned to <strong t-out="object.rating_get_rated_partner_id().name or ''">Mitchell Admin</strong>.<br/>
            </t>
            <t t-else="">
                .<br/>
            </t>
        </td></tr>
        <tr><td style="text-align: center;">
            <table border="0" cellpadding="0" cellspacing="0" summary="o_mail_notification" style="width:100%; margin: 32px 0px 32px 0px;">
                <tr><td style="font-size: 14px;">
                    <strong>Tell us how you feel about our service</strong><br/>
                    <span style="text-color: #888888">(click on one of these smileys)</span>
                </td></tr>
                <tr><td style="font-size: 14px;">
                    <table style="width:100%;text-align:center;margin-top:2rem;">
                        <tr>
                            <td>
                                <a t-attf-href="/rate/{{ access_token }}/5">
                                    <img alt="Satisfied" src="/rating/static/src/img/rating_5.png" title="Satisfied"/>
                                </a>
                            </td>
                            <td>
                                <a t-attf-href="/rate/{{ access_token }}/3">
                                    <img alt="Okay" src="/rating/static/src/img/rating_3.png" title="Okay"/>
                                </a>
                            </td>
                            <td>
                                <a t-attf-href="/rate/{{ access_token }}/1">
                                    <img alt="Dissatisfied" src="/rating/static/src/img/rating_1.png" title="Dissatisfied"/>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td></tr>
            </table>
        </td></tr>
        <tr><td valign="top" style="font-size: 14px;">
            We appreciate your feedback. It helps us to improve continuously.
            <br/><span style="margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;">This customer survey has been sent because your ticket has been moved to the stage <b t-out="object.stage_id.name or ''">In Progress</b></span>
        </td></tr>
    </tbody>
    </table>
</div>
        </field>
        <field name="lang">{{ object.partner_id.lang or object.user_id.lang or user.lang }}</field>
        <field name="auto_delete" eval="True"/>
    </record>
</data></odoo>
