# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * helpdesk
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:31+0000\n"
"PO-Revision-Date: 2018-10-02 10:31+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"${object.company_id.name or object.user_id.company_id.name or 'Helpdesk'}: "
"Service Rating Request"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "${object.display_name}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__percentage_satisfaction
msgid "% Happy"
msgstr "% Srećan"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "% endif"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid ""
"% set email_alias = object.env['helpdesk.team'].search([('alias_name','!=', False)],limit=1).alias_id.display_name\n"
"    % if email_alias\n"
"    <strong style=\"font-size: 16px;\">Try the mail gateway</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"(Un)archiving a helpdesk team automatically (un)archives its tickets. Do you"
" want to proceed?"
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div>\n"
"    % set access_token = object.rating_get_access_token()\n"
"    % set partner = object.rating_get_partner_id()\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            Hello ${partner.name},<br/>\n"
"            Please take a moment to rate our services related to the ticket \"<strong>${object.name}</strong>\"\n"
"            % if object.rating_get_rated_partner_id().name:\n"
"                assigned to <strong>${object.rating_get_rated_partner_id().name}</strong>.<br/>\n"
"            % else:\n"
"                .<br/>\n"
"            % endif\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a href=\"/rating/${access_token}/10\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_10.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a href=\"/rating/${access_token}/5\">\n"
"                                    <img alt=\"Not satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Not satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a href=\"/rating/${access_token}/1\">\n"
"                                    <img alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Highly Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your ticket has been moved to the stage <b>${object.stage_id.name}</b></span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div>\n"
"    Dear ${object.sudo().partner_id.name or 'Madam/Sir'},<br/><br/>\n"
"    This automatic message informs you that we have closed your ticket (reference ${object.id}).\n"
"    We hope that the services provided have met your expectations.\n"
"    If you have any more questions or comments, don't hesitate to reply to this e-mail to re-open your ticket.<br/><br/>\n"
"    Thank you for your cooperation.<br/>\n"
"    Kind regards,<br/><br/>\n"
"    ${object.team_id.name or 'Helpdesk'} Team.\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div>\n"
"    Dear ${object.sudo().partner_id.name or 'Madam/Sir'},<br/><br/>\n"
"    Your request\n"
"    % if object.access_token:\n"
"    <a href=\"/helpdesk/ticket/${object.id}/${object.access_token}\">${object.name}</a>\n"
"    % endif\n"
"    has been received and is being reviewed by our ${object.team_id.name or ''} team.\n"
"    The reference of your ticket is ${object.id}.<br/><br/>\n"
"    To add additional comments, reply to this email.<br/>\n"
"    Thanks you,<br/><br/>\n"
"    ${object.team_id.name or 'Helpdesk'} Team.\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:58
#, python-format
msgid ""
"<b>Stars</b> mark the <b>ticket priority</b>. You can change it directly "
"from here!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<em class=\"text-muted\"><small>No description</small></em>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.index
msgid "<i class=\"fa fa-arrow-circle-right \"/> See the feedbacks"
msgstr "<i class=\"fa fa-arrow-circle-right \"/> Vidi odgovore"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Configure domain name"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-arrow-right\"/> View documentation"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.index
msgid "<i class=\"fa fa-envelope\"/> Email :"
msgstr "<i class=\"fa fa-envelope\"/> Email :"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-"
"label=\"Domain alias\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.index
msgid "<i/> Customer Satisfaction Ration:"
msgstr "<i/> Omjer zadovoljstva kupaca:"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i>No customer feedback yet.</i>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-right\">Status:</small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Assigned to</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Description</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Managed by</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Reported by</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Reported on</strong>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python dictionary koji će se provjeravati za zadane postave kada se kreira "
"novi zapis za ovaj alias."

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid ""
"A service level agreement is a contract between you and your\n"
"            customers that specifies performance measures for support\n"
"            by ticket priority."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__kanban_state
msgid ""
"A ticket's kanban state indicates special situations affecting it:\n"
"* Normal is the default situation\n"
"* Blocked indicates something is preventing the progress of this issue\n"
"* Ready for next stage indicates the issue is ready to be pulled to the next stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_api
msgid "API"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__description
msgid "About Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__active
msgid "Active"
msgstr "Aktivan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.mail_activity_type_action_config_helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:63
#, python-format
msgid ""
"Add columns to configure <b>stages for your tickets</b>.<br/><i>e.g. "
"Awaiting Customer Feedback, Customer Followup, ...</i>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_id
msgid "Alias"
msgstr "Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_contact
msgid "Alias Contact Security"
msgstr "Sigurnosni nadimak kontakta"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_name
msgid "Alias Name"
msgstr "Naziv nadimka"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain
msgid "Alias domain"
msgstr "Alias domena"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_model_id
msgid "Aliased Model"
msgstr "Zamjenski model"

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "All"
msgstr "Sve"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
msgid "All Tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Allow your customers to easily rate your services. Activate this option will"
" add a default email template on non folded closing stages"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Apply on"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Archive"
msgstr "Arhiviraj"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Assign To Me"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:39
#, python-format
msgid "Assign the ticket to someone."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:421
#, python-format
msgid "Assign to me"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__assign_method
msgid "Assignation Method"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__user_id
msgid "Assigned to"
msgstr "Dodjeljeno"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Assignee"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__template_id
msgid "Automated Answer Email Template"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__template_id
msgid ""
"Automated email sent to the ticket's customer when the ticket reaches this "
"stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__assign_method
msgid ""
"Automatic assignation method for new tickets:\n"
"\tManually: manual\n"
"\tRandomly: randomly but everyone gets the same amount\n"
"\tBalanced: to the person with the least amount of open tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr "Prosjek"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:104
#, python-format
msgid "Avg 7 days"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:35
#, python-format
msgid "Avg Open Hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr "Loše"

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Balanced"
msgstr "Izravnato"

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Blocked"
msgstr "Blokiran"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_cancelled
msgid "Cancelled"
msgstr "Otkazan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr "Kanali"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Choose an Email:"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:67
#, python-format
msgid "Click here and select \"Helpdesk Teams\" for further configuration."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:18
#, python-format
msgid "Click here to view this team's tickets."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:52
#, python-format
msgid ""
"Click these cards to open their form view, or <b>drag &amp; drop</b> them "
"through the different stages of this team."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:123
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:127
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:136
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:140
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:149
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:153
#, python-format
msgid "Click to set"
msgstr "Kliknite da postavite"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_date
msgid "Close date"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:81
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Closed Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_analysis
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__is_close
msgid "Closing Kanban Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__color
msgid "Color"
msgstr "Boja"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__color
msgid "Color Index"
msgstr "Indeks boje"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__company_id
msgid "Company"
msgstr "Kompanija"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr "Konfiguracija"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Configure SLA Policies"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Connect third party application and create tickets using web services"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Contact"
msgstr "Kontakt"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Create a new Service Level Agreement (SLA) policy"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid "Create a new stage in your helpdesk pipeline"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Create a new template"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_all_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_Archived
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_slafailed
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "Create a new ticket"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Create a new ticket tag"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "Create a new ticket team"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Create a new ticket type"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
msgid "Create your first ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr "Trenutno stanje ovog tiketa"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:332
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_id
#, python-format
msgid "Customer"
msgstr "Kupac"

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Customer Care"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:334
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_email
#, python-format
msgid "Customer Email"
msgstr "Email stranke"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_name
msgid "Customer Name"
msgstr "Naziv kupca"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:89
#, python-format
msgid "Customer Rating"
msgstr "Ocijena kupca"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Customer Satisfaction"
msgstr "Zadovoljstvo kupca"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:319
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#, python-format
msgid "Customer Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:85
#, python-format
msgid "Customer satisfaction analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:121
#, python-format
msgid "Daily Target"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:224
#, python-format
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time_days
msgid "Days"
msgstr "Dani"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time_days
msgid "Days to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__deadline
msgid "Deadline"
msgstr "Rok izvršenja"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_defaults
msgid "Default Values"
msgstr "Zadane vrijednosti"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Delete"
msgstr "Obriši"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__description
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Description"
msgstr "Opis"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Description for customer portal"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description of the ticket..."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_digest_digest
msgid "Digest"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__portal_show_rating
msgid "Display Rating on Customer Portal"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/digest.py:16
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Documents"
msgstr "Dokumenti"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Dropdown menu"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr "Uredi"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_alias
msgid "Email alias"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email
msgid "Email on Customer"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid ""
"Emails sent to <strong>${email_alias}</strong> generate new tickets in your "
"Helpdesk funnel.<br>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_slides
msgid "Enable eLearning"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:29
#, python-format
msgid ""
"Enter a subject or title for this ticket.<br/><i>(e.g. Problem with "
"installation, Wrong order, Can't understand bill, etc.)</i>"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:34
#, python-format
msgid "Enter the customer. Feel free to create it on the fly."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_fail
msgid "Failed SLA Policy"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Failed SLA: {{record.sla_name.raw_value}}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_date
msgid "First assignation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__fold
msgid "Folded"
msgstr "Skupljeno"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__fold
msgid "Folded in kanban view"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Follow this team to automatically track the events associated to tickets of "
"this team."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_channel_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid ""
"For example, we respond to urgent tickets related to bugs\n"
"            in two hours and resolve them within 36 hours."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get in touch with your website visitors"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Group By"
msgstr "Grupiši po"

#. module: helpdesk
#: model:mail.activity.type,name:helpdesk.mail_act_helpdesk_handle
msgid "Handle Ticket"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr "Srećan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_forum
msgid "Help Center"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
#: model_terms:ir.ui.view,arch_db:helpdesk.digest_digest_view_form
msgid "Helpdesk"
msgstr "Helpdesk"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
msgid "Helpdesk Overview"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
msgid "Helpdesk Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
msgid "Helpdesk Tags"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Helpdesk Team"
msgstr "Helpdesk tim"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.index
msgid "Helpdesk Team Satisfaction"
msgstr "Zadovoljstvo helpdesk timom"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Helpdesk Team..."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Helpdesk Teams"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Helpdesk Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_type
msgid "Helpdesk Ticket Type"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_sla_cron_ir_actions_server
#: model:ir.cron,cron_name:helpdesk.helpdesk_sla_cron
#: model:ir.cron,name:helpdesk.helpdesk_sla_cron
msgid "Helpdesk: compute SLAs"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:23
#, python-format
msgid "High Priority ("
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "High priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time_hours
msgid "Hours"
msgstr "Sati"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time_hours
msgid "Hours to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "How to assign newly created tickets to the right person"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__id
msgid "ID"
msgstr "ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_unread
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Important Messages"
msgstr "Važne poruke"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid ""
"In Channel: You can create a new ticket by typing /helpdesk [ticket title]. "
"You can search ticket by typing /helpdesk_search [Keyword1],[Keyword2],."
msgstr ""

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_in_progress
msgid "In Progress"
msgstr "U Toku"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Incoming emails create tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_is_follower
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_incident
msgid "Issue"
msgstr "Problem"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state
msgid "Kanban State"
msgstr "Kanban status"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Keep empty for everyone to see this team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed_value
msgid "Kpi Helpdesk Tickets Closed Value"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr "Posljednja 3 mjeseca"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr "Posljednjih 30 dana"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr "Posljednjih 7 dana"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Feedbacks"
msgstr "Zadnji odgovori"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:23
#, python-format
msgid "Let's create your first ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live chat"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "Low priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_main_attachment_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Manager"
msgstr "Upravitelj"

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Manually"
msgstr "Ručno"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__medium_id
msgid "Medium"
msgstr "Medijum"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Message and communication history"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__priority
msgid "Minimum Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "My Activities"
msgstr "Moje aktivnosti"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:74
#, python-format
msgid "My Performance"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:13
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "My Tickets"
msgstr "Moji tiketi"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:21
#, python-format
msgid "My high priority tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:15
#, python-format
msgid "My open tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:27
#, python-format
msgid "My urgent tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__name
msgid "Name"
msgstr "Naziv:"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_new
msgid "New"
msgstr "Novi"

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:35
#, python-format
msgid "Newest"
msgstr "Najnoviji"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_high_priorities
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla_high
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla_urgent
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_urgent
msgid "No tickets to display"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Normal"
msgstr "Normalan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__attachment_number
msgid "Number of Attachments"
msgstr "Broj zakački"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_tickets
msgid "Number of tickets from the same partner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_unread_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:189
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Samo cijeli broj bi trebao biti valjan."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__ticket_type_id
msgid ""
"Only apply the SLA to a specific ticket type. If left empty it will apply to"
" all types."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_hours
msgid "Open Time (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Opcionalni ID zapisa na kojeg će biti povezane sve dolazne poruke, čak i ako"
" oni nisu odgovorili na njega. Ako je postavljeno, to će onemogućiti "
"stvaranje novih zapisa u potpunosti."

#. module: helpdesk
#: selection:helpdesk.ticket,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
msgid "Overview"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_user_id
msgid "Owner"
msgstr "Vlasnik"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Roditeljski model"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Roditeljski zapis niti"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr "Performanse"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:77
#, python-format
msgid "Performance Report"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__priority
msgid "Priority"
msgstr "Prioritet"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Productivity"
msgstr "Produktivnost"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Publish this team's ratings on your website"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_question
msgid "Question"
msgstr "Pitanje"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Question and answer section on your website"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Randomly"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_ids
msgid "Rating"
msgstr "Ocijena"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Ocijenjivanje zadnjeg odgovora"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "Ocijenjivanje zadnje slike"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "Posljednja vrijednost ocijene"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "Broj ocijena"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_rating
msgid "Ratings"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Reach In"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Reach Stage"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Ready for next stage"
msgstr "Spremno za sljedeću fazu"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:170
#, python-format
msgid "Ready to boost your customer service?"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ID niti zapisa"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Record timesheets on your tickets and reinvoice time to you customer through"
" tasks."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Ref"
msgstr "Ref"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "Izvještavanje"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr "Oporavi"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:51
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "SLA Failed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "SLA Not Failed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_sla
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__description
msgid "SLA Policy Description"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__name
msgid "SLA Policy Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_name
msgid "SLA Policy name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_active
msgid "SLA active"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:18
#, python-format
msgid "Salesperson"
msgstr "Prodavač(ica)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:43
#, python-format
msgid "Save this ticket and the modifications you've made to it."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:39
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:42
#, python-format
msgid "Search in All"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:41
#, python-format
msgid "Search in Customer"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:40
#, python-format
msgid "Search in Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr "Sigurnosni token"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See Customer Satisfaction"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See SLAs"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Sell &amp; Track Hours"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:173
#, python-format
msgid "Send an email to"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send emails to"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set the calendar used to compute SLA target"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set up your Service Level Agreements to track performance"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr "Postavke"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.model_helpdesk_ticket_action_share
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Share"
msgstr "Podijeli"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Share presentation and videos, and organize into courses"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_solved
msgid "Solved"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__source_id
msgid "Source"
msgstr "Izvor"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__team_ids
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Stage"
msgstr "Faza"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr "Faza promjenjena"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr "Pretraga faza"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr "Faze"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid ""
"Stages will allow operators to easily track how a specific\n"
"            tickets are positioned in the process."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Status"
msgstr "Status"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
msgid "Status Changed"
msgstr "Promjenjen status"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:36
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__name
#, python-format
msgid "Subject"
msgstr "Tema"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Subject..."
msgstr "Naslov..."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Submit tickets with an online form"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:98
#, python-format
msgid "Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_tree
msgid "Tag"
msgstr "Oznaka"

#. module: helpdesk
#: sql_constraint:helpdesk.tag:0
msgid "Tag name already exists !"
msgstr "Naziv oznake već postoji!"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__tag_ids
msgid "Tags"
msgstr "Oznake"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags allows to organize tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr "Cilj"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_rating
msgid "Target Customer Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__stage_id
msgid "Target Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_success
msgid "Target Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_closed
msgid "Target Tickets to Close"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__team_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Team"
msgstr "Tim"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__member_ids
msgid "Team Members"
msgstr "Članovi tima"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Team Search"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.index
msgid "Teams"
msgstr "Timovi"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "Teams allows to organize tickets."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr "Predlošci"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Vlasnik zapisa kreiranih nakon primitka e-mailova na taj alias. Ako ovo "
"polje nije postavljeno sustav će pokušati pronaći pravog vlasnika na temelju"
" adrese pošiljatelja (od), ili će koristiti administratorski račun ako ne "
"pronađe sistemskog korisnika za tu adresu."

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:94
#, python-format
msgid ""
"The success rate is based on the tickets which passed the SLA policies "
"successfully."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr "Trenutno nema tiketa za Vaš nalog"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:171
#, python-format
msgid "This dashboard will activate once you have created your first ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "This required to have project module installed."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:340
#, python-format
msgid "This target does not exist."
msgstr "Cilj ne postoji."

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:30
#, python-format
msgid "Three stars, maximum score"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard_high_priority
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard_urgent
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_team_analysis_action
msgid "Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Ticket ID"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_rated
#: model:mail.message.subtype,name:helpdesk.mt_ticket_rated
msgid "Ticket Rated"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_stage
msgid "Ticket Stage Changed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
msgid "Ticket Tags"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_type_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_type_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_type_id
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_type_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Ticket Type"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_rated
msgid "Ticket rated"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:17
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_all_tickets
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_tickets
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_Archived
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_high_priorities
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla_high
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla_urgent
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_slafailed
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_urgent
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
#, python-format
msgid "Tickets"
msgstr "Tiketi"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed
msgid "Tickets Closed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Tickets Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__is_close
msgid ""
"Tickets in this stage are considered as done. This is used notably when "
"computing SLAs and KPIs on tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Tickets to Review"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__priority
msgid "Tickets under this priority will not be taken into account."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_hours
msgid "Time to first assignation (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "Timesheet on Ticket"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:74
#: selection:helpdesk.ticket,activity_state:0
#, python-format
msgid "Today"
msgstr "Danas"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Try Now"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:24
#, python-format
msgid "Two stars, with a maximum of three"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Type allows to organize tickets."
msgstr ""

#. module: helpdesk
#: sql_constraint:helpdesk.ticket.type:0
msgid "Type name already exists !"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "URL to Submit Issue"
msgstr "URL za prijavu problema"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__portal_rating_url
msgid "URL to Submit an Issue"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__unassigned_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unassigned Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_unread
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_unread
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_unread_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Upcoming SLA Fail"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__upcoming_sla_fail_tickets
msgid "Upcoming SLA Fail Tickets"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "Urgent"
msgstr "Hitno"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:29
#, python-format
msgid "Urgent ("
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:48
#, python-format
msgid "Use the breadcrumbs to go back to the Kanban view."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr "Korisnik"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:52
#, python-format
msgid "View failed tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:57
#, python-format
msgid "View high priority failed tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "View this teams ratings on this page:<br/>"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:62
#, python-format
msgid "View urgent failed tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:13
#, python-format
msgid ""
"Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to "
"start.</i>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_form
msgid "Website Form"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:167
#, python-format
msgid "Welcome"
msgstr "Dobrodošli"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"With random assignation, every user gets the same number of tickets. With "
"balanced assignation, tickets are assigned to the user with the least amount"
" of open tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working Hours"
msgstr "Radni sati"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:189
#, python-format
msgid "Wrong value entered!"
msgstr "Unesena je pogrešna vrijednost!"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:108
#, python-format
msgid "You must have team members assigned to change the assignation method."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "Your teams will appear here"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "days<br/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "eLearning"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "hours<br/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "oe_kanban_text_red"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:173
#, python-format
msgid "to create a ticket."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "to create tickets"
msgstr ""
