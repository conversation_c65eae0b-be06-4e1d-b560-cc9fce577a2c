# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * helpdesk
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Ecuador) (https://www.transifex.com/odoo/teams/41243/es_EC/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_EC\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"${object.company_id.name or object.user_id.company_id.name or 'Helpdesk'}: "
"Service Rating Request"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "${object.display_name}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_percentage_satisfaction
msgid "% Happy"
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"% set access_token = object.rating_get_access_token()\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <a href=\"/\"><img src=\"/web/binary/company_logo\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Satisfaction Survey\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Hello,</p>\n"
"                <p>Please take a moment to rate our services related to the ticket \"<strong>${object.name}</strong>\"\n"
"                   assigned to <strong>${object.rating_get_rated_partner_id().name}</strong>.</p>\n"
"                <p>We appreciate your feedback. It helps us to improve continuously.</p>\n"
"            </td></tr>\n"
"            <tr><td style=\"padding:10px 20px\">\n"
"                <table summary=\"o_mail_notification\" style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                    <tr>\n"
"                        <td style=\"text-align:center;\">\n"
"                            <h2 style=\"font-weight:300;font-size:18px;\">\n"
"                                Tell us how you feel about our service:\n"
"                            </h2>\n"
"                            <div style=\"text-color: #888888\">(click on one of these smileys)</div>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <tr>\n"
"                        <td style=\"padding:10px 10px;\">\n"
"                            <table style=\"width:100%;text-align:center;\">\n"
"                                <tr>\n"
"                                    <td>\n"
"                                        <a href=\"/rating/${access_token}/10\">\n"
"                                            <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_10.png\" title=\"Satisfied\"/>\n"
"                                        </a>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <a href=\"/rating/${access_token}/5\">\n"
"                                            <img alt=\"Not satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Not satisfied\"/>\n"
"                                        </a>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <a href=\"/rating/${access_token}/1\">\n"
"                                            <img alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Highly Dissatisfied\"/>\n"
"                                        </a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </td></tr>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.user_id.signature or '') | safe}</td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"                <p>This customer survey has been sent because your ticket has been moved to the stage <b>${object.stage_id.name}</b></p>\n"
"                <p>Email automatically sent by <a target=\"_blank\" href=\"https://www.odoo.com/page/helpdesk\" style=\"color:#875A7B;text-decoration:none;\">Odoo Helpdesk</a> for <a href=\"${object.team_id.company_id.website}\" style=\"color:#875A7B;text-decoration:none;\">${object.team_id.company_id.name}</a></p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"max-width:600px; height:auto; margin-left:30px;\">\n"
"    <div style=\"margin-left:30px;align=center;\">\n"
"        Dear ${object.sudo().partner_id.name or 'Madam, Sir'},\n"
"    </div>\n"
"    <div style=\"margin-left:30px;align=center;\" width=\"60%\">\n"
"        This automatic message informs you that we have closed your ticket (reference ${object.id}).\n"
"\n"
"        We hope that the services provided have met your expectations.\n"
"        If you have any more questions or comments, don't hesitate to reply to this e-mail to re-open your ticket.\n"
"\n"
"        Thank you for your cooperation.\n"
"    </div><br/>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">Kind regards, </span>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">${object.team_id.name or 'Helpdesk'} Team.</span>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"max-width:600px; height:auto; margin-left:30px;\">\n"
"    <div style=\"margin-left:30px;align=center;\">\n"
"        This is an automatic message:<br/>\n"
"        Dear ${object.sudo().partner_id.name or 'Madam, Sir'},\n"
"    </div>\n"
"    <div style=\"margin-left:30px;align=center;\" width=\"60%\">\n"
"        Your request\n"
"        % if object.access_token:\n"
"        <a href=\"/helpdesk/ticket/${object.id}/${object.access_token}\">${object.name}</a>\n"
"        % endif\n"
"        has been received and is being reviewed by our ${object.team_id.name or ''} team.\n"
"        The reference of your ticket is ${object.id}.\n"
"        <br/>To add additional comments, reply to this email.\n"
"    </div><br/>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">Thank you </span>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">${object.team_id.name or 'Helpdesk'} Team.</span>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:58
#, python-format
msgid ""
"<b>Stars</b> mark the <b>ticket priority</b>. You can change it directly "
"from here!"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.index
msgid "<i class=\"fa fa-arrow-circle-right \"/> See the feedbacks"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Configure domain name"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-arrow-right\"/> View documentation"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.index
msgid "<i class=\"fa fa-envelope\"/> Email :"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<i class=\"fa fa-envelope-o\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.index
msgid "<i/> Customer Satisfaction Ration:"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i>No customer feedback yet.</i>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_merge_view_form
msgid ""
"<span class=\"text-muted\">NB: This will archive the selected tickets "
"(Except the destination ticket)</span>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>View Tickets</span>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-md-2\">Description</strong>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-md-2\">Reported by</strong>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-md-2\">Reported on</strong>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong>Message and communication history</strong>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid ""
"A service level agreement is a contract between you and your\n"
"            customers that specifies performance measures for support\n"
"            by ticket priority."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket_kanban_state
msgid ""
"A ticket's kanban state indicates special situations affecting it:\n"
"* Normal is the default situation\n"
"* Blocked indicates something is preventing the progress of this issue\n"
"* Ready for next stage indicates the issue is ready to be pulled to the next stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_api
msgid "API"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_description
msgid "About Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_active
msgid "Active"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:64
#, python-format
msgid ""
"Add columns to configure <b>stages for your tickets</b>.<br/><i>e.g. "
"Awaiting Customer Feedback, Customer Followup, ...</i>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_name
msgid "Alias Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_domain
msgid "Alias domain"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "All"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
msgid "All Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to easily rate your services"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Apply on"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Archive"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Archived"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Assign To Me"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:39
#, python-format
msgid "Assign the ticket to someone."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:410
#, python-format
msgid "Assign to me"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_assign_method
msgid "Assignation Method"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_user_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_user_id
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Assigned to"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Assignee"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_template_id
msgid "Automated Answer Email Template"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_template_id
msgid ""
"Automated email sent to the ticket's customer when the ticket reaches this "
"stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_assign_method
msgid ""
"Automatic assignation method for new tickets:\n"
"\tManually: manual\n"
"\tRandomly: randomly but everyone gets the same amount\n"
"\tBalanced: to the person with the least amount of open tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:104
#, python-format
msgid "Avg 7 days"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:35
#, python-format
msgid "Avg Open Hours"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Balanced"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Blocked"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_merge_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Choose an Email:"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_all_tickets
#: model:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_tickets
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_Archived
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_slafailed
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "Click create a new ticket."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:68
#, python-format
msgid "Click here and select \"Helpdesk Teams\" for further configuration."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Click here to create a new template."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:18
#, python-format
msgid "Click here to view this team's tickets."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:52
#, python-format
msgid ""
"Click these cards to open their form view, or <b>drag &amp; drop</b> them "
"through the different stages of this team."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Click to create a new Service Level Agreement (SLA) policy."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Click to create a new ticket tag."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "Click to create a new ticket team."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Click to create a new ticket type."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid "Click to create a stage in your helpdesk pipeline."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:123
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:127
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:136
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:140
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:149
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:153
#, python-format
msgid "Click to set"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_close_date
msgid "Close date"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:81
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Closed Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_analysis
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_is_close
msgid "Closing Kanban Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_color
msgid "Color"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_color
msgid "Color Index"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_company_id
msgid "Company"
msgstr "Compañía"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Configure SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Connect third party application and create tickets using web services"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
msgid "Contact"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_create_new_ticket
msgid "Create a new ticket"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Create new Helpdesk Teams"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_create_uid
msgid "Created by"
msgstr "Creado por:"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_create_date
msgid "Created on"
msgstr "Creado"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:333
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_id
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:335
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_email
#, python-format
msgid "Customer Email"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_name
msgid "Customer Name"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:89
#, python-format
msgid "Customer Rating"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Customer Satisfaction"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:320
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#, python-format
msgid "Customer Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:85
#, python-format
msgid "Customer satisfaction analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:121
#, python-format
msgid "Daily Target"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:222
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
#, python-format
msgid "Dashboard"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_time_days
msgid "Days"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_time_days
msgid "Days to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_deadline
msgid "Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_defaults
msgid "Default Values"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Delete"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_description
#: model:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Description"
msgstr "Descripción"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Description for customer portal"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/wizard/helpdesk_ticket_merge.py:59
#, python-format
msgid "Description from ticket %s: %s"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description of the ticket..."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_portal_show_rating
msgid "Display Rating on Customer Portal"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Edit Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_alias
msgid "Email alias"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:29
#, python-format
msgid ""
"Enter a subject or title for this ticket.<br/><i>(e.g. Problem with "
"installation, Wrong order, Can't understand bill, etc.)</i>"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:34
#, python-format
msgid "Enter the customer. Feel free to create it on the fly."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_fail
msgid "Failed SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_assign_date
msgid "First assignation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_fold
msgid "Folded"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_fold
msgid "Folded in kanban view"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Follow this team to automatically track the events associated to tickets of "
"this team."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid ""
"For example, we respond to urgent tickets related to bugs\n"
"            in two hours and resolve them within 36 hours."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get in touch with you website visitors"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_forum
msgid "Help Center"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
msgid "Helpdesk"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_team_id
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.index
msgid "Helpdesk Team Satisfaction"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Helpdesk Team..."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:137
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action_form
#, python-format
msgid "Helpdesk Teams"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Helpdesk Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Helpdesk contracts are managed using tasks. Choose a default project for "
"these tasks."
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_team_action_settings
msgid "Helpdesk: Teams Settings"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_sla_cron_ir_actions_server
#: model:ir.cron,cron_name:helpdesk.helpdesk_sla_cron
#: model:ir.cron,name:helpdesk.helpdesk_sla_cron
msgid "Helpdesk: compute SLAs"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:23
#, python-format
msgid "High Priority ("
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "High priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_time_hours
msgid "Hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_time_hours
msgid "Hours to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "How to assign newly created tickets to the right person"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_id
msgid "ID"
msgstr "ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Important Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_use_website_helpdesk_livechat
msgid ""
"In Channel: You can create a new ticket by typing /helpdesk [ticket title]. "
"You can search ticket by typing /helpdesk_search [Keyword1],[Keyword2],."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Incoming emails create tickets"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_incident
msgid "Issue"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_kanban_state
msgid "Kanban State"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Keep empty for everyone to see this team"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_write_uid
msgid "Last Updated by"
msgstr "Ultima Actualización por"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_write_date
msgid "Last Updated on"
msgstr "Actualizado en"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Feedbacks"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:23
#, python-format
msgid "Let's create your first ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_livechat
msgid "Live chat"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "Low priority"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Managed by"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Manager"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Manually"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_actions_act_window_merge_tickets
msgid "Merge Selected Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_merge_view_form
msgid "Merge Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_target_ticket_id
msgid "Merge into an existing ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_priority
msgid "Minimum Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "My Activities"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:74
#, python-format
msgid "My Performance"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:13
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "My Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:21
#, python-format
msgid "My high priority tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:15
#, python-format
msgid "My open tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:27
#, python-format
msgid "My urgent tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_name
msgid "Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_target_ticket_name
msgid "New ticket name"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:27
#, python-format
msgid "Newest"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/wizard/helpdesk_ticket_merge.py:59
#, python-format
msgid "No description"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_high_priorities
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla_high
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla_urgent
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_urgent
msgid "No tickets to display."
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Normal"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Not Satisfied"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_tickets
msgid "Number of tickets from the same partner"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:187
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Solo un valor entero es válido."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_ticket_type_id
msgid ""
"Only apply the SLA to a specific ticket type. If left empty it will apply to"
" all types."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_close_hours
msgid "Open Time (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Opened Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_user_id
msgid "Owner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:77
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:94
#, python-format
msgid "Performance Report"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_priority
msgid "Priority"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Productivity"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Publish this team's ratings on your website"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_question
msgid "Question"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Question and answer section on your website"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Randomly"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_rating
msgid "Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Reach In"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Reach Stage"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Ready for next stage"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:170
#, python-format
msgid "Ready to boost your customer service?"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "Informe"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:51
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "SLA Failed"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "SLA Not Failed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_sla
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_id
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_description
msgid "SLA Policy Description"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_name
msgid "SLA Policy Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_name
msgid "SLA Policy name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_active
msgid "SLA active"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:43
#, python-format
msgid "Save this ticket and the modifications you've made to it."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:31
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:34
#, python-format
msgid "Search in All"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:33
#, python-format
msgid "Search in Customer"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:32
#, python-format
msgid "Search in Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_access_token
msgid "Security Token"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See Customer Satisfaction"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See SLAs"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Sell &amp; Track Hours"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:173
#, python-format
msgid "Send an email to"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send emails to"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set up your Service Level Agreements to track performance"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Share presentation and videos, and organize into courses"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_team_ids
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_stage_id
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Stage"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid ""
"Stages will allow operators to easily track how a specific\n"
"            tickets are positioned in the process."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Status"
msgstr "Estado"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
msgid "Status Changed"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:28
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_name
#, python-format
msgid "Subject"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Subject..."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Submit tickets with an online form"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:98
#, python-format
msgid "Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr ""

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Support"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_tree
msgid "Tag"
msgstr ""

#. module: helpdesk
#: sql_constraint:helpdesk.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_tag_ids
msgid "Tags"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags allows to organize tickets."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_helpdesk_target_rating
msgid "Target Customer Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_target_helpdesk_id
msgid "Target Helpdesk Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_stage_id
msgid "Target Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_helpdesk_target_success
msgid "Target Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_helpdesk_target_closed
msgid "Target Tickets to Close"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_team_ids
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_member_ids
msgid "Team Members"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.index
msgid "Teams"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "Teams allows to organize tickets."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.mail_template_ticket_merge
msgid "The following ticket has been merged into this one:"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.mail_template_ticket_merge
msgid "The following tickets have been merged into this one:"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:171
#, python-format
msgid "This dashboard will activate once you have created your first ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_use_helpdesk_timesheet
msgid "This required to have project module installed."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:345
#, python-format
msgid "This target does not exist."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.mail_template_ticket_merge
msgid "This ticket has been merged into the following ticket:"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
msgid "Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard_high_priority
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard_urgent
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_team_analysis_action
msgid "Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_assigned
#: model:mail.message.subtype,name:helpdesk.mt_ticket_assigned
msgid "Ticket Assigned"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Ticket Number"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_stage
msgid "Ticket Stage Changed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
msgid "Ticket Tags"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_type_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_type
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_ticket_type_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_ticket_type_id
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_type_menu
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Ticket Type"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_assigned
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:17
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_all_tickets
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_tickets
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_Archived
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_high_priorities
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla_high
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla_urgent
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_slafailed
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_urgent
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner_ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_res_users_ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
#, python-format
msgid "Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Tickets Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_is_close
msgid ""
"Tickets in this stage are considered as done. This is used notably when "
"computing SLAs and KPIs on tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_merge_ticket_ids
msgid "Tickets to Merge"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Tickets to Review"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_priority
msgid "Tickets under this priority will not be taken into account."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_assign_hours
msgid "Time to first assignation (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_helpdesk_timesheet
msgid "Timesheet on Ticket"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:74
#, python-format
msgid "Today"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_twitter
msgid "Twitter"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Type allows to organize tickets."
msgstr ""

#. module: helpdesk
#: sql_constraint:helpdesk.ticket.type:0
msgid "Type name already exists !"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_portal_rating_url
msgid "URL to Submit Issue"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_unassigned_tickets
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unassigned Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr "Mensajes no leídos"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Upcoming SLA Fail"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_upcoming_sla_fail_tickets
msgid "Upcoming SLA Fail Tickets"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "Urgent"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:29
#, python-format
msgid "Urgent ("
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:48
#, python-format
msgid "Use the breadcrumbs to go back to the Kanban view."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
msgid "Users"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:52
#, python-format
msgid "View failed tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:57
#, python-format
msgid "View high priority failed tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "View this teams ratings this page:<br/>"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:62
#, python-format
msgid "View urgent failed tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tour.js:13
#, python-format
msgid ""
"Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to "
"start.</i>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_form
msgid "Website Form"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"With random assignation, every user gets the same number of tickets. With "
"balanced assignation, tickets are assigned to the user with the least amount"
" of open tickets."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:187
#, python-format
msgid "Wrong value entered!"
msgstr "¡Valor entero no válido!"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:102
#, python-format
msgid "You must have team members assigned to change the assignation method."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "Your teams will appear here."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "days<br/>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_slides
msgid "eLearning"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_merge
msgid "helpdesk.ticket.merge"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "hours<br/>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "oe_kanban_text_red"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:173
#, python-format
msgid "to create a ticket."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "to create tickets"
msgstr ""
