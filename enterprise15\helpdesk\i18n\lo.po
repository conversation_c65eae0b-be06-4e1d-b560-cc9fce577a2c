# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk
# 
# Translators:
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON>vong <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>than<PERSON>vong <<EMAIL>>, 2022\n"
"Language-Team: Lao (https://www.transifex.com/odoo/teams/41243/lo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lo\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__open_ticket_count
msgid "# Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_count
msgid "# Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_policy_count
msgid "# SLA Policy"
msgstr ""

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_1
msgid "2 days to start"
msgstr ""

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_2
msgid "7 days to finish"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "<b class=\"tip_title\">Tip: Create tickets from incoming emails</b>"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change the stage of your ticket."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications (you will only notify the persons you specifically tag). \n"
"    Use <b>@ mentions</b> to ping a colleague \n"
"    or <b># mentions</b> to contact a group of people."
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the ticket \"<strong t-out=\"object.name or ''\">Table legs are unbalanced</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                assigned to <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your ticket has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In Progress</b></span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"    This automatic message informs you that we have closed your ticket (reference <t t-out=\"object.id or ''\">15</t>).\n"
"    We hope that the services provided have met your expectations.\n"
"    If you have any more questions or comments, don't hesitate to reply to this e-mail to re-open your ticket.<br/><br/>\n"
"    Thank you for your cooperation.<br/>\n"
"    Kind regards,<br/><br/>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team.\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"    Your request\n"
"    <t t-if=\"object.get_portal_url()\">\n"
"        <a t-attf-href=\"/my/ticket/{{ object.id }}/{{ object.access_token }}\" t-out=\"object.name or ''\"/>\n"
"    </t>\n"
"    has been received and is being reviewed by our <t t-out=\"object.team_id.name or ''\">Table legs are unbalanced</t> team.\n"
"    The reference of your ticket is <t t-out=\"object.id or ''\">15</t>.<br/>\n"
"\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.get_portal_url()\">View the ticket</a><br/>\n"
"    </div>\n"
"\n"
"    To add additional comments, reply to this email.<br/><br/>\n"
"\n"
"    Thank you,<br/><br/>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team.\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "<em class=\"font-weight-normal text-muted\">Tickets in stage:</em>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<em class=\"text-muted\"><small>No description</small></em>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-"
"label=\"Domain alias\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" attrs=\"{'invisible': [('rating_percentage_satisfaction', '&lt;', 66)]}\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" attrs=\"{'invisible': ['|', ('rating_percentage_satisfaction', '&lt;', 33), ('rating_percentage_satisfaction', '&gt;=', 65)]}\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" attrs=\"{'invisible': [('rating_percentage_satisfaction', '&gt;', 32)]}\" title=\"Dissatisfied\"/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\" role=\"img\"/> Enable the <i>Custom Email "
"Servers</i> feature in the <i>General Settings</i> and indicate an <i>alias "
"domain</i>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-right\">Status:</small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"<small>\n"
"                    If the issue has been solved, you can close the request.\n"
"                </small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "<small>#</small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small><b>Close this ticket</b></small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "<span class=\"o_field_widget o_readonly_modifier\">Working Hours</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Satisfaction\n"
"                                </span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span>days of inactivity</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Assigned to</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Description</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Managed by</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Reported by</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-2\">Reported on</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>After</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>Alias </strong>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"ພົດຈະນານຸກົມ ຂອງ Python ທີ່ຈະໄດ້ຮັບການປະເມີນເພື່ອໃຫ້ເປັນຄ່າເລີ່ມຕົ້ນ "
"ເມື່ອມີການສ້າງ ແຖວຂໍ້ມູນໃໝ໋ສໍາລັບສຳຮອງ."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__description
msgid "About Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__active
msgid "Active"
msgstr "ໃຊ້ຢູ່"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr "ກິດຈຳກຳ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr "ສະຖານະ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.mail_activity_type_action_config_helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid ""
"Adapt your <b>pipeline</b> to your workflow by adding <b>stages</b> <i>(e.g."
" Awaiting Customer Feedback, etc.).</i>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid ""
"Adapt your pipeline to your workflow and track the progress of your tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Add a description..."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Add team members..."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Add users..."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Administrator"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "After-Sales"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_id
msgid "Alias"
msgstr "ຊື່ ຫຼື ທີ່ຢູ່ສຳຮອງ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_contact
msgid "Alias Contact Security"
msgstr "ຄວາມປອດໄພຂອງທີ່ຢູ່ຕິດຕໍ່ສຳຮອງ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_name
msgid "Alias Name"
msgstr "ຊື່ສຳຮອງ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain
msgid "Alias domain"
msgstr "ໂດເມນສຳຮອງ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_alias_name
msgid "Alias email"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_model_id
msgid "Aliased Model"
msgstr "ແບບທີ່ສ້າງສຳຮອງ"

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__0
#, python-format
msgid "All"
msgstr "ທັງໝົດ"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_all
msgid "All Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy__user
msgid "All Users"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__allow_portal_ticket_closing
msgid "Allow customers to close their tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to close their own tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Archived"
msgstr "ສຳເນົາໄວ້ແລ້ວ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Are you sure you wish to proceed?"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Assign To Me"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Assign new tickets to the right persons"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "Assign the ticket to a <b>member of your team</b>."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#, python-format
msgid "Assign to me"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Assigned"
msgstr "ມອບໝາຍໃຫ້ແລ້ວ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__user_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__user_id
msgid "Assigned To"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__user_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Assigned to"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Assignee"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Assignment &amp; Visibility"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__assign_method
msgid "Assignment Method"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid ""
"At each stage, employees can block tickets or mark them as ready for the next step.\n"
"                                You can customize the meaning of each state."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__template_id
msgid ""
"Automated email sent to the ticket's customer when the ticket reaches this "
"stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_ticket
msgid "Automatic Closing"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__assign_method
msgid ""
"Automatic assignment method for new tickets:\n"
"\tManually: manual\n"
"\tRandomly: randomly but everyone gets the same amount\n"
"\tBalanced: to the person with the least amount of open tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Avg 7 Days Happy Rating"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Avg 7 days"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Avg Open Hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__balanced
msgid "Balanced"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Bill the time spent on your tickets to your customers"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:0
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_done
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_new
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_solved
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_1
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_10
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_11
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_12
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_13
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_14
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_15
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_16
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_17
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_18
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_19
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_2
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_3
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_4
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_5
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_6
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_7
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_8
#: model:helpdesk.ticket,legend_blocked:helpdesk.helpdesk_ticket_9
#, python-format
msgid "Blocked"
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_crm
msgid "CRM"
msgstr "CRM"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__campaign_id
msgid "Campaign"
msgstr "ການໂຄສະນາ"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_cancelled
msgid "Cancelled"
msgstr "ຖືກຍົກເລີກແລ້ວ"

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_team_not_portal_show_rating_if_not_use_rating
msgid "Cannot show ratings in portal if not using them"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Click to set"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close"
msgstr "ປິດອອກ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__close_date
msgid "Close date"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Close inactive tickets automatically"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close the ticket"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close ticket"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Closed"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#, python-format
msgid "Closed Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_analysis
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__closed_by_partner
msgid "Closed by Partner"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
msgid "Closing Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__is_close
msgid "Closing Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__allow_portal_ticket_closing
msgid "Closure by Customers"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__color
msgid "Color"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__color
msgid "Color Index"
msgstr "ດັດຊະນີສີ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state_label
msgid "Column Status"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Comment"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_forum
msgid "Community Forum"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__company_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Company"
msgstr "ບໍລິສັດ"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr "ການຕັ້ງຄ່າລະບົບ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Configure a custom domain"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_all_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "Congratulations!"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Contact"
msgstr "ຂໍ້ມູນຕິດຕໍ່ພົວພັນ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__use_fsm
msgid "Convert tickets into Field Service tasks"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_coupons
msgid "Coupons"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Create Date"
msgstr "ວັນທີສ້າງ"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Create a new template"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Create tickets by sending an email to an alias"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
msgid "Create tickets to get statistics on the performance of your teams."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "Create tickets to get statistics."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__create_date
msgid "Created On"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__create_uid
msgid "Created by"
msgstr "ສ້າງໂດຍ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__create_date
msgid "Created on"
msgstr "ສ້າງເມື່ອ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Creation Date"
msgstr "ວັນທີສ້າງ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Criteria"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Customer"
msgstr "ລູກຄ້າ"

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Customer Care"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_email
#, python-format
msgid "Customer Email"
msgstr "ອີເມວລ໌ລູກຄ້າ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_name
msgid "Customer Name"
msgstr "ຊື່ລູກຄ້າ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_phone
msgid "Customer Phone"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.rating_rating_action_helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_rating
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_ratings
msgid "Customer Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__partner_ids
msgid "Customers"
msgstr "ລູກຄ້າ"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Daily Target"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:0
#, python-format
msgid "Dashboard"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Datetime at which the SLA stage was reached for the first time"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_days
msgid ""
"Day to reach the stage of the SLA, without taking the working calendar into "
"account"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_days
msgid "Days to Reach SLA"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__deadline
msgid "Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_defaults
msgid "Default Values"
msgstr "ເລີ່ມຕົ້ນໂດຍຄ່າ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Delete"
msgstr "ລຶບ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__description
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Description"
msgstr "ຄຳອະທິບາຍ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Description for customer portal"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description of the ticket..."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_digest_digest
msgid "Digest"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Discard"
msgstr "ປະລະ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__display_name
msgid "Display Name"
msgstr "ຊື່ເຕັມ"

#. module: helpdesk
#: code:addons/helpdesk/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Documentation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__domain_user_ids
msgid "Domain User"
msgstr ""

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_done
msgid "Done"
msgstr "ສໍາເລັດແລ້ວ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Dropdown menu"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr "ແກ້ໄຂ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Email"
msgstr "ອີເມວລ໌"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_alias
msgid "Email Alias"
msgstr "ຊື່ສຳຮອງແີເມວລ໌"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__template_id
msgid "Email Template"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email_cc
msgid "Email cc"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email
msgid "Email on Customer"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Emails sent to"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid ""
"Emails sent to a Helpdesk Team alias generate tickets in your pipeline."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_slides
msgid "Enable eLearning"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid ""
"Enter the <b>subject</b> of your ticket <br/><i>(e.g. Problem with my "
"installation, Wrong order, etc.).</i>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__exceeded_days
msgid "Excedeed Working Days"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid "Excluding Stages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__failed
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__failed
msgid "Failed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_fail
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_fail
msgid "Failed SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.filters,name:helpdesk.helpdesk_sla_report_analysis_filter_stage_failed
msgid "Failed SLA Stage per Month"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_fsm
msgid "Field Service"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "First Assignment Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__assign_date
msgid "First assignment date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__fold
msgid "Folded in Kanban"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Follow this team to automatically track the events associated to tickets of "
"this team."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Followed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Followed Teams"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get in touch with your website visitors"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get tickets through an online form"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Grant discounts or free products"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__done
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__done
msgid "Green"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_done
msgid "Green Kanban Label"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__normal
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__normal
msgid "Grey"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_normal
msgid "Grey Kanban Label"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Group By"
msgstr "ຈັດຂໍ້ມູນຕາມ"

#. module: helpdesk
#: model:mail.activity.type,name:helpdesk.mail_act_helpdesk_handle
msgid "Handle Ticket"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Happy Rating"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Happy face"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_external_mail_server
msgid "Has External Mail Server"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_message
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached_late
msgid "Has SLA reached late"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
#: model_terms:ir.ui.view,arch_db:helpdesk.digest_digest_view_form
msgid "Helpdesk"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
msgid "Helpdesk Overview"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
msgid "Helpdesk Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
msgid "Helpdesk Tags"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__team_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Helpdesk Teams"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_type
msgid "Helpdesk Ticket Type"
msgstr ""

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.ir_cron_auto_close_ticket_ir_actions_server
#: model:ir.cron,cron_name:helpdesk.ir_cron_auto_close_ticket
#: model:ir.cron,name:helpdesk.ir_cron_auto_close_ticket
msgid "Helpdesk Ticket: Automatically close the tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "High Priority"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "High Priority ("
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__2
msgid "High priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_open_hours
msgid "Hours Open"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_assignation_hours
msgid "Hours to Assign"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_close_hours
msgid "Hours to Close"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__id
msgid "ID"
msgstr "ເລກລຳດັບ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ເລກກຳກັບຂອງແຖວຂໍ້ມູນຫຼັກ ທີ່ເກັບຊື່ແຝງໄວ້ (ຕົວຢ່າງ: ໂຄງການ "
"ທີ່ມີຊື່ແຝງການສ້າງວຽກ)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_unread
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__closed_by_partner
msgid ""
"If checked, this means the ticket was closed through the customer portal by "
"the customer."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "In"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid ""
"In Channel: You can create a new ticket by typing /helpdesk [ticket title]. "
"You can search ticket by typing /helpdesk_search [Keyword1],[Keyword2],."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:0
#: model:helpdesk.stage,legend_normal:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_normal:helpdesk.stage_done
#: model:helpdesk.stage,legend_normal:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_normal:helpdesk.stage_new
#: model:helpdesk.stage,legend_normal:helpdesk.stage_solved
#: model:helpdesk.stage,name:helpdesk.stage_in_progress
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_1
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_10
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_11
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_12
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_13
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_14
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_15
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_16
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_17
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_18
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_19
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_2
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_3
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_4
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_5
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_6
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_7
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_8
#: model:helpdesk.ticket,legend_normal:helpdesk.helpdesk_ticket_9
#, python-format
msgid "In Progress"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__from_stage_ids
msgid "In Stages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_day
msgid "Inactive Period(days)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__from_stage_ids
msgid ""
"Inactive tickets in these stages will be automatically closed. Leave empty "
"to take into account all the stages from the team."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy__invite
msgid "Invited Users"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_is_follower
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_incident
msgid "Issue"
msgstr "ຫົວຂໍ້ບັນຫາ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Issue credits notes"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__kanban_state
msgid "Kanban State"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_done
msgid "Kanban Valid Explanation"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed_value
msgid "Kpi Helpdesk Tickets Closed Value"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis____last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type____last_update
msgid "Last Modified on"
msgstr "ແກ້ໄຂລ້າສຸດເມື່ອ"

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr "ປັບປຸງຂັ້ນຕອນລ້າສຸດ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__write_uid
msgid "Last Updated by"
msgstr "ປັບປຸງລ້າສຸດໂດຍ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__write_date
msgid "Last Updated on"
msgstr "ປັບປຸງລ້າສຸດເມື່ອ"

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Last message is from customer"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Last message is from support"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Feedbacks"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Latest Rating: Dissatisfied"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Latest Rating: Okay"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Latest Rating: Satisfied"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Let your customers answer each other's questions on a forum"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "Let's create your first <b>ticket</b>."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tickets."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "Let's view your <b>team's tickets</b>."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__email_cc
msgid "List of cc from incoming emails."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Low Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__1
msgid "Low priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_main_attachment_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
msgid ""
"Make sure tickets are handled in a timely manner by using SLA Policies.<br>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Make sure tickets are handled on time"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Make sure tickets are handled on time by using SLA Policies.<br>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__manual
msgid "Manual"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__medium_id
msgid "Medium"
msgstr "ປານກາງ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Medium Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Message and communication history"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__priority
msgid "Minimum Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_module_module
msgid "Module"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__to_stage_id
msgid "Move to Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Failed Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My High Priority Failed Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My High Priority Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My High Priority Tickets Analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Open Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Open Tickets Analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Performance"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "My Teams"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "My Ticket"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_my
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "My Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Urgent Failed Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Urgent Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "My Urgent Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__name
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Name"
msgstr "ຊື່"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Neutral face"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:0
#: code:addons/helpdesk/models/helpdesk.py:0
#: model:helpdesk.stage,name:helpdesk.stage_new
#, python-format
msgid "New"
msgstr "ສ້າງໃໝ່"

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "ໃໝ່ສຸດ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr "ເນື້ອໃນກິດຈະກຳຕໍ່ໄປ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "No SLA policies found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
msgid "No data yet !"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
msgid "No data yet!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "No rating yet"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid "No stages found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "No tags found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "No teams found"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
msgid "No tickets found"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "No tickets found. Let's create one!"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "No types found. Let's create one!"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "None"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_count
msgid "Number of other tickets from the same partner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_unread_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__ongoing
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__ongoing
msgid "Ongoing"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__ticket_type_id
msgid ""
"Only apply the SLA to a specific ticket type. If left empty it will apply to"
" all types."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__tag_ids
msgid ""
"Only apply the SLA to tickets with specific tags. If left empty it will "
"apply to all tags."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Open"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_open_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__open_hours
msgid "Open Time (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ເລກກຳກັບທີ່ສາມາດເລືອກໄດ້ຂອງກະທູ້ (ແຖວຂໍ້ມູນ) "
"ຊຶ່ງຂໍ້ຄວາມເຂົ້າທັງໝົດຈະໄດ້ຄັດຕິດມາ, ເຖິງແມ່ນວ່າເຂົາເຈົ້າບໍ່ໄດ້ຕອບກັບກໍຕາມ. "
"ຖ້າຫາກຕັ້ງຕ່າໄວ້, ອັນນີ້ຈະປິດການສ້າງແຖວຂໍ້ມູນໃໝ່ ທັງໝົດ."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_page
msgid "Our Customer Satisfaction"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Our Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__legend_blocked
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__legend_done
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__legend_normal
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the task or issue is in that stage."
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
msgid "Overview"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_user_id
msgid "Owner"
msgstr "ເຈົ້າຂອງ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent Model"
msgstr "ແບບບໍລິສັດແມ່"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ເລກກຳກັບແຖວຂໍ້ມູນແມ່"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"ຮູບແບບຂອງແມ່ທີ່ຖືນາມແຝງ. ແບບທີ່ຖືການອ້າງອິງນາມແຝງ "
"ແມ່ນບໍ່ຈໍາເປັນຕ້ອງແມ່ນແບບທີ່ກໍານົດ ໂດຍ alias_model_id (ຕົວຢ່າງ: ໂຄງການ "
"(parent_model) ແລະ ໜ້າວຽກ (model))"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_ids
msgid "Partner Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Percentage of tickets that were closed without failing any SLAs."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__auto_close_day
msgid "Period of inactivity after which tickets will be automatically closed."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Phone"
msgstr "ໂທລະສັຍ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Plan onsite interventions"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:0
#, python-format
msgid "Please enter an integer value"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"ນະໂຍບາຍຂອງການສະແດງຂໍ້ຄວາມຢູ່ເທິງເອກະສານ ໃຫ້ນໍາໃຊ້ ປະຕູເມວລ໌ ນີ້.\n"
"- ທຸກຄົນ: ທຸກຄົນສາມາດສະແດງ\n"
"- ຄູ່ຮ່ວມງານ: ຄູ່ຮ່ວມງານທີ່ອະນຸຍາດໃຫ້ເທົ່ານັ້ນ\n"
"- ຜູ້ຕິດຕາມ: ພຽງແຕ່ ຜູ້ຕິດຕາມຂອງເອກະສານທີ່ກ່ຽວຂ້ອງ ຫຼື ສະມາຊິກຂອງຊ່ອງຕໍ່ໄປນີ້ເທົ່ານັ້ນ\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__priority
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Priority"
msgstr "ບຸລິມະສິດ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__portal_show_rating
msgid "Public Rating"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Publish this team's ratings on your website"
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_question
msgid "Question"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__randomly
msgid "Random"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Rated Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_ids
msgid "Rating"
msgstr "ຈັດລຳດັບ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_last_value
msgid "Rating (/5)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg
msgid "Rating Average"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_ids
msgid "Ratings"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "Reach Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__reached
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__reached
msgid "Reached"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Reached Date"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:0
#: model:helpdesk.stage,legend_done:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_done:helpdesk.stage_done
#: model:helpdesk.stage,legend_done:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_done:helpdesk.stage_new
#: model:helpdesk.stage,legend_done:helpdesk.stage_solved
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_1
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_10
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_11
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_12
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_13
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_14
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_15
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_16
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_17
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_18
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_19
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_2
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_3
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_4
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_5
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_6
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_7
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_8
#: model:helpdesk.ticket,legend_done:helpdesk.helpdesk_ticket_9
#, python-format
msgid "Ready"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "ເລກກຳກັບແຖວຂໍ້ມູນ"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__blocked
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__blocked
msgid "Red"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_blocked
msgid "Red Kanban Label"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Ref"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Reference"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_refund_posted
#: model:mail.message.subtype,description:helpdesk.mt_ticket_refund_posted
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_refund_posted
#: model:mail.message.subtype,name:helpdesk.mt_ticket_refund_posted
msgid "Refund Posted"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_credit_notes
msgid "Refunds"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__use_helpdesk_sale_timesheet
msgid "Reinvoice the time spent on ticket through tasks."
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_repair
msgid "Repair"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_repair_done
#: model:mail.message.subtype,description:helpdesk.mt_ticket_repair_done
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_repair_done
#: model:mail.message.subtype,name:helpdesk.mt_ticket_repair_done
msgid "Repair Done"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_repairs
msgid "Repairs"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "ບົດລາຍງານ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_team_ticket_return_done
#: model:mail.message.subtype,description:helpdesk.mt_ticket_return_done
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_return_done
#: model:mail.message.subtype,name:helpdesk.mt_ticket_return_done
msgid "Return Done"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Return faulty products"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_returns
msgid "Returns"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "SLA"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
msgid "SLA Deadline"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "SLA Failed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "SLA Issues"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_users__sla_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "SLA Policies"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__description
msgid "SLA Policy Description"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_reached_datetime
msgid "SLA Reached Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_stage_id
msgid "SLA Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_status_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "SLA Status"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_action
#: model:ir.model,name:helpdesk.model_helpdesk_sla_report_analysis
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_sla_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "SLA Status Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_failed
msgid "SLA Status Failed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "SLA Success"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "SLA in Progress"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "SLAs"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Sad face"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Salesperson"
msgstr "ພະນັກງານຂາຍ"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Sample"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "Save this ticket and the modifications you've made to it."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Search in Customer"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Search in Reference"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Search in Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See Customer Satisfaction"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "Select the <b>customer</b> of your ticket."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send broken products for repair"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type__sequence
msgid "Sequence"
msgstr "ລຳດັບ"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_service
msgid "Service"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set an Email Template on Stages"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr "ການກໍານົດຄ່າ"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.model_helpdesk_ticket_action_share
msgid "Share"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Share presentation and videos, and organize into courses"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_id
msgid "Sla"
msgstr ""

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_solved
msgid "Solved"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__source_id
msgid "Source"
msgstr "ແຫຼ່ງທີ່ມາ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__team_ids
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""
"ທີມງານສະເພາະກິດທີ່ໃຊ້ຂັ້ນຕອນນີ້. ທີມງານອື່ນໆຈະບໍ່ສາມາດເຫັນ ຫຼື "
"ໃຊ້ງານຂັ້ນນີ້."

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#, python-format
msgid "Stage"
msgstr "ຂັ້ນຕອນ"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr "ບາດກ້າວປ່ຽນໄປ"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Description and States Meaning"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr "ຊອກຫາບາດກ້າວ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__to_stage_id
msgid ""
"Stage to which inactive tickets will be automatically moved once the period "
"of inactivity is reached."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr "ບາດກ້າວ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__status
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Status"
msgstr "ສະຖານະພາບ"

#. module: helpdesk
#: model:ir.filters,name:helpdesk.helpdesk_sla_report_analysis_filter_status_per_deadline
msgid "Status Per Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__name
#, python-format
msgid "Subject"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Subject..."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_success
msgid "Success SLA Policy"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_tree
msgid "Tag"
msgstr "ເປົ້າໝາຍ"

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_tag_name_uniq
msgid "Tag name already exists !"
msgstr "ຊື່ເປົ້າໝາຍນີ້ມີແລ້ວ!"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__tag_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_form
msgid "Tags"
msgstr "ເປົ້າໝາຍ"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags are perfect for organizing your tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_rating
msgid "Target Customer Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Target Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_success
msgid "Target Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users__helpdesk_target_closed
msgid "Target Tickets to Close"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Team"
msgstr "ທີມງານ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__member_ids
msgid "Team Members"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__visibility_member_ids
msgid ""
"Team Members to whom this team will be visible. Keep empty for everyone to "
"see this team."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Team Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__visibility_member_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Team Visibility"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid ""
"Teams regroup tickets for people sharing the same expertise or from the same"
" area."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid ""
"The amount of time the ticket spends in this stage will not be taken into "
"account when evaluating the status of the SLA Policy."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__sla_deadline
msgid "The closest deadline of all SLA applied on this ticket"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"ນີ້ແມ່ນຮູບແບບ (ປະເພດເອກະສານ Odoo) ທີ່ ຊື່ແທນນີ້ກົງກັບ. ອີເມວຂາເຂົ້າ "
"ທີ່ບໍ່ໄດ້ຕອບກັບ ໄປຍັງ ແຖວຂໍ້ມູນທີ່ມີຢູ່ "
"ຈະເຮັດໃຫ້ເກີດການສ້າງແຖວຂໍ້ມູນໃໝ່ຂອງແບບນີ້ (ເຊັ່ນວຽກໂຄງການ)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"ຊື່ຂອງ ຊື່ແທນອີເມວລ໌, ຕ.ຢ. ໃສ່ \"jobs\" ຖ້າທ່ານຕ້ອງການ ເອົາເປັນຊື່ແທນ "
"ອີເມວລ໌ ຂອງ <<EMAIL>>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"ເຈົ້າຂອງແຖວຂໍ້ມູນໄດ້ສ້າງຂຶ້ນໃນຂະນະທີ່ໄດ້ຮັບອີເມວໃນນາມຊື່ແທນນີ້. "
"ຖ້າຊ່ອງຂໍ້ມູນນີ້ບໍ່ຖືກກໍານົດ "
"ລະບົບຈະພະຍາຍາມຊອກຫາເຈົ້າຂອງທີ່ຖືກຕ້ອງໂດຍອີງໃສ່ທີ່ຢູ່ຜູ້ສົ່ງ (ຈາກ), "
"ຫຼືຈະໃຊ້ບັນຊີ ຜູ້ບໍລິຫານ ຖ້າຫາກວ່າບໍ່ພົບຜູ້ໃຊ້ລະບົບສໍາລັບທີ່ຢູ່ນັ້ນ."

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "The team does not allow ticket closing through portal"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_page
msgid "There are no ratings yet."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "This"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__partner_ids
msgid ""
"This SLA Policy will apply to any tickets from the selected customers. Leave"
" empty to apply this SLA Policy to any ticket without distinction."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__assign_hours
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__close_hours
msgid "This duration is based on the working calendar of the team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__open_hours
msgid "This duration is not based on the working calendar of the team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "This requires to have project module installed."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"ຂັ້ນຕອນນີ້ ໄດ້ເອົາໄວ້ໃນໂຟນເດີ kanban ສຳລັບເບິ່ງ ເມື່ອບໍ່ມີແຖວຂໍ້ມູນໃດ "
"ທີ່ຈະສະແດງ ໃນຂັ້ນຕອນນັ້ນ."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Three stars, maximum score"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_report_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_closed
msgid "Ticket Closed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__ticket_count
msgid "Ticket Count"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__create_date
msgid "Ticket Create Date"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_deadline
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
msgid "Ticket Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_failed
msgid "Ticket Failed"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Ticket ID"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_rated
#: model:mail.message.subtype,name:helpdesk.mt_ticket_rated
msgid "Ticket Rated"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla_status
msgid "Ticket SLA Status"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_stage_id
msgid "Ticket Stage"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_stage
msgid "Ticket Stage Changed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_type_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_type_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Ticket Type"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#, python-format
msgid "Ticket closed by the customer"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr ""

#. module: helpdesk
#: model:mail.template,name:helpdesk.rating_ticket_request_email_template
msgid "Ticket: Rating Request (requires rating enabled on team)"
msgstr ""

#. module: helpdesk
#: model:mail.template,name:helpdesk.new_ticket_request_email_template
msgid "Ticket: Reception Acknowledgment"
msgstr ""

#. module: helpdesk
#: model:mail.template,name:helpdesk.solved_ticket_request_email_template
msgid "Ticket: Solved"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/models/helpdesk.py:0
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_all_tickets
#: model:ir.actions.act_window,name:helpdesk.helpdesk_my_ticket_action_no_create
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_my
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_res_users__ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
#, python-format
msgid "Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
msgid "Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed
msgid "Tickets Closed"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Tickets Closed Avg 7 Days"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Tickets Closed Today"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Tickets Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__is_close
msgid ""
"Tickets in this stage are considered as done. This is used notably when "
"computing SLAs and KPIs on tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__priority
msgid "Tickets under this priority will not be taken into account."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_close_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_hours
msgid "Time to close (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_assignation_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_hours
msgid "Time to first assignment (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time
msgid "Time to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "Timesheets"
msgstr ""

#. module: helpdesk
#: model:digest.tip,name:helpdesk.digest_tip_helpdesk_0
msgid "Tip: Create tickets from incoming emails"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid ""
"To get things done, use activities and statuses on tickets. <br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
msgid ""
"To get things done, use activities and statuses on tickets.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Today"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr "ກິດຈະກຳມື້ນີ້"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Today Happy Rating"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track &amp; Bill Time"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track customer satisfaction on tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track the time spent on tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_twitter
msgid "Twitter"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Two stars, with a maximum of three"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_type_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_type_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Type"
msgstr "ປະເພດ"

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_ticket_type_name_uniq
msgid "Type name already exists !"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_type_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_type_menu
msgid "Types"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Types are perfect for categorizing your tickets."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Unassigned"
msgstr "ບໍ່ໄດ້ມອບໝາຍໃຫ້"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__unassigned_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Unassigned Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_unread
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_unread
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr "ຂໍ້ຄວາມບໍ່ໄດ້ອ່ານ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_unread_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__upcoming_sla_fail_tickets
msgid "Upcoming SLA Fail Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__3
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Urgent"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:0
#, python-format
msgid "Urgent ("
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid "Use <b>activities</b> to organize your daily work."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_coupons
msgid "Use Coupons"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_credit_notes
msgid "Use Credit Notes"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_rating
msgid "Use Customer Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_product_repairs
msgid "Use Repairs"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_product_returns
msgid "Use Returns"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the progress of this ticket."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr "ຜູ້ໃຊ້"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
msgid "Users"
msgstr "ຜູ້ໃຊ້ງານ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__privacy
msgid "Users Assign"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Users to whom this team will be visible"
msgstr ""

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team3
msgid "VIP Support"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
#, python-format
msgid ""
"Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to "
"start.</i>"
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_website
msgid "Website"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_form
msgid "Website Form"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"With random assignation, every user gets the same number of tickets. With "
"balanced assignation, tickets are assigned to the user with the least amount"
" of open tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working Hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__exceeded_days
msgid ""
"Working days exceeded for reached SLAs compared with deadline. Positive "
"number means the SLA was eached after the deadline."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working hours used to determine the deadline of SLA Policies."
msgstr ""

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_closed_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_rating_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_success_not_zero
msgid "You cannot have negative targets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_all_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "You completed all your tickets on time."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. Customer Care"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "eLearning"
msgstr ""

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "generate tickets in your pipeline."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "has been created from ticket:"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' "
"}}: Service Rating Request"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "{{ object.display_name }}"
msgstr ""
