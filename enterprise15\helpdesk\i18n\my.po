# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * helpdesk
# 
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-03 13:22+0000\n"
"PO-Revision-Date: 2016-11-03 13:22+0000\n"
"Last-Translator: Pyaephone Kyaw <<EMAIL>>, 2016\n"
"Language-Team: Burmese (https://www.transifex.com/odoo/teams/41243/my/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: my\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"${object.company_id.name or object.user_id.company_id.name or 'Helpdesk'}: "
"Service Rating Request"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "${object.display_name}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_percentage_satisfaction
msgid "% Happy"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "--&gt; Configure domain name"
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"% set access_token = object.rating_get_access_token()\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <a href=\"/\"><img src=\"/web/binary/company_logo\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Satisfaction Survey\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Hello,</p>\n"
"                <p>Please take a moment to rate our services related to the ticket \"<strong>${object.name}</strong>\"\n"
"                   assigned to <strong>${object.rating_get_rated_partner_id().name}</strong>.</p>\n"
"                <p>We appreciate your feedback. It helps us to improve continuously.</p>\n"
"            </td></tr>\n"
"            <tr><td style=\"padding:10px 20px\">\n"
"                <table summary=\"o_mail_notification\" style=\"width:100%;border-top:1px solid #e1e1e1;\">\n"
"                    <tr>\n"
"                        <td style=\"text-align:center;\">\n"
"                            <h2 style=\"font-weight:300;font-size:18px;\">\n"
"                                Tell us how you feel about our service:\n"
"                            </h2>\n"
"                            <div style=\"text-color: #888888\">(click on one of these smileys)</div>\n"
"                        </td>\n"
"                    </tr>\n"
"                    <tr>\n"
"                        <td style=\"padding:10px 10px;\">\n"
"                            <table style=\"width:100%;text-align:center;\">\n"
"                                <tr>\n"
"                                    <td>\n"
"                                        <a href=\"/rating/${access_token}/10\">\n"
"                                            <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_10.png\" title=\"Satisfied\"/>\n"
"                                        </a>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <a href=\"/rating/${access_token}/5\">\n"
"                                            <img alt=\"Not satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Not satisfied\"/>\n"
"                                        </a>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <a href=\"/rating/${access_token}/1\">\n"
"                                            <img alt=\"Highly Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Highly Dissatisfied\"/>\n"
"                                        </a>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </td></tr>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">${(object.user_id.signature or '') | safe}</td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:auto;text-align:center;font-size:12px;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding-top:10px;color:#afafaf;\">\n"
"                <p>This customer survey has been sent because your ticket has been moved to the stage <b>${object.stage_id.name}</b></p>\n"
"                <p>Email automatically sent by <a href=\"https://www.odoo.com/page/helpdesk\" style=\"color:#875A7B;text-decoration:none;\">Odoo Helpdesk</a> for <a href=\"${object.team_id.company_id.website}\" style=\"color:#875A7B;text-decoration:none;\">${object.team_id.company_id.name}</a></p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"max-width:600px; height:auto; margin-left:30px;\">\n"
"    <div style=\"margin-left:30px;align=center;\">\n"
"        Dear ${object.partner_id.name or 'Madam, Sir'},\n"
"    </div>\n"
"    <div style=\"margin-left:30px;align=center;\" width=\"60%\">\n"
"        This automatic message informs you that we have closed your ticket (reference ${object.id}).\n"
"\n"
"        We hope that the services provided have met your expectations.\n"
"        If you have any more questions or comments, don't hesitate to reply to this e-mail to re-open your ticket.\n"
"\n"
"        Thank you for your cooperation.\n"
"    </div><br/>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">Kind regards, </span>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">${object.team_id.name or 'Helpdesk'} Team.</span>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"max-width:600px; height:auto; margin-left:30px;\">\n"
"    <div style=\"margin-left:30px;align=center;\">\n"
"        This is an automatic message:<br/>\n"
"        Dear ${object.partner_id.name or 'Madam, Sir'},\n"
"    </div>\n"
"    <div style=\"margin-left:30px;align=center;\" width=\"60%\">\n"
"        Your request\n"
"        % if object.access_token:\n"
"        <a href=\"/helpdesk/ticket/${object.id}/${object.access_token}\">${object.name}</a>\n"
"        % endif\n"
"        has been received and is being reviewed by our ${object.team_id.name or ''} team.\n"
"        The reference of your ticket is ${object.id}.\n"
"        <br/>To add additional comments, reply to this email.\n"
"    </div><br/>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">Thank you </span>\n"
"    <span style=\"margin-left:30px;font-weight:normal;\">${object.team_id.name or 'Helpdesk'} Team.</span>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<i class=\"fa fa-envelope-o\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i>No customer feedback yet.</i>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Reports</span>"
msgstr "<span>စစ်တမ်းများ</span>"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>View Tickets</span>"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid ""
"A service level agreement is a contract between you and your\n"
"            customers that specifies performance measures for support\n"
"            by ticket priority."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket_kanban_state
msgid ""
"A ticket's kanban state indicates special situations affecting it:\n"
"* Normal is the default situation\n"
"* Blocked indicates something is preventing the progress of this issue\n"
"* Ready for next stage indicates the issue is ready to be pulled to the next stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_api
msgid "API"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_description
msgid "About Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_active
msgid "Active"
msgstr "တက်ကြွသည်။"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_contact
msgid "Alias Contact Security"
msgstr "ဆက်သွယ်ရန် ချိတ်ဆက်ထားသော လုံခြုံရေး"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_name
msgid "Alias Name"
msgstr "ချိတ်ဆက်မှုအမည်"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_domain
msgid "Alias domain"
msgstr "ချိတ်ဆက်မှုဒိုမိန်း"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_model_id
msgid "Aliased Model"
msgstr "ချိတ်ဆက်မှု မော်ဒယ်"

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "All"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
msgid "All Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to easily rate your services."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Apply on"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Archive"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Archived"
msgstr "သိမ်းဆည်းထားသည်။"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Assign members to this team so that they will see it in their dashboard. If "
"no members are assigned, everyone will see this team."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_assign_method
msgid "Assignation Method"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_user_id
msgid "Assigned to"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Assignee"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_template_id
msgid "Automated Answer Email Template"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_template_id
msgid ""
"Automated email sent to the ticket's customer when the ticket reaches this "
"stage."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_assign_method
msgid ""
"Automatic assignation method for new tickets:\n"
"\tManually: manual\n"
"\tRandomly: randomly but everyone gets the same amount\n"
"\tBalanced: to the person with the least amount of open tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:104
#, python-format
msgid "Avg 7 days"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:35
#, python-format
msgid "Avg Open Hours"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Balanced"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_bank_account_count
msgid "Bank"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Blocked"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Choose an Email:"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_all_tickets
#: model:ir.actions.act_window,help:helpdesk.action_upcoming_sla_fail_tickets
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_Archived
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_slafailed
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "Click create a new ticket."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Click here to create a new template."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Click to create a new Service Level Agreement (SLA) policy."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Click to create a new ticket tag."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "Click to create a new ticket team."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Click to create a new ticket type."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid "Click to create a stage in your helpdesk pipeline."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:123
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:127
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:136
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:140
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:149
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:153
#, python-format
msgid "Click to set"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_close_date
msgid "Close date"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:81
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "Closed Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_analysis
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_is_close
msgid "Closing Kanban Stage"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_color
msgid "Color"
msgstr "အရောင်"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_color
msgid "Color Index"
msgstr "အရောင်ညွှန်းကိန်း"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_company_id
msgid "Company"
msgstr "ကုမ္ပဏီ"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr "ပြင်ဆင်ရန်"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Configure SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Connect third party application and create tickets using web services."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_contract_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_users_contracts_count
msgid "Contracts"
msgstr "စာချုပ်များ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_create_uid
msgid "Created by"
msgstr "ဖန်တီးသူ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_create_date
msgid "Created on"
msgstr "ဖန်တီးပုံ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_currency_id
msgid "Currency"
msgstr "ငွေကြေး"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:629
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_id
#, python-format
msgid "Customer"
msgstr "ဖောက်သည်"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:631
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_email
#, python-format
msgid "Customer Email"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_name
msgid "Customer Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_property_payment_term_id
msgid "Customer Payment Terms"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:89
#, python-format
msgid "Customer Rating"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:616
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#, python-format
msgid "Customer Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:85
#, python-format
msgid "Customer satisfaction analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:121
#, python-format
msgid "Daily Target"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:16
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
#, python-format
msgid "Dashboard"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_time_days
msgid "Days"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_time_days
msgid "Days to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_deadline
msgid "Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_defaults
msgid "Default Values"
msgstr "Default Values"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Delete"
msgstr "ဖျက်သည်။"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_description
msgid "Description"
msgstr "ဖော်ပြချက်"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description of the ticket..."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_display_name
msgid "Display Name"
msgstr "ပြချင်သော အမည်"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.action_helpdesk_email_template_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_templates_menu
msgid "Email Templates"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_alias
msgid "Email alias"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_fail
msgid "Failed SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_assign_date
msgid "First assignation date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_property_account_position_id
msgid "Fiscal Position"
msgstr "ဘဏ္ဍာရေးနှစ်အနေအထား"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_fold
msgid "Folded"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_fold
msgid "Folded in kanban view"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Follow this team to automatically track the events associated to tickets of "
"this team."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid ""
"For example, we respond to urgent tickets related to bugs\n"
"            in two hours and resolve them within 36 hours."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get in touch with you website visitors."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Group By"
msgstr "မူတည်၍"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_has_unreconciled_entries
msgid "Has unreconciled entries"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_forum
msgid "Help Center"
msgstr ""

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
msgid "Helpdesk"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_team_id
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Helpdesk Team..."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Helpdesk Teams"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Helpdesk Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:170
#, python-format
msgid "Hi there."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:23
#, python-format
msgid "High Priority ("
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "High priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_time_hours
msgid "Hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_time_hours
msgid "Hours to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "How to assign newly created tickets to the right person."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:670
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#, python-format
msgid "I take it"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_id
msgid "ID"
msgstr "နံပါတ်"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Important Messages"
msgstr "အရေးကြီးသော စာစောင်များ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_use_website_helpdesk_livechat
msgid ""
"In Channel: You can create a new ticket by typing /helpdesk [ticket title]. "
"You can search ticket by typing /helpdesk_search [Keyword1],[Keyword2],."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Incoming emails create tickets."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_invoice_warn
msgid "Invoice"
msgstr "ဝယ်ကုန်စာရင်း"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_invoice_ids
msgid "Invoices"
msgstr "ငွေတောင်းခံလွှာများ"

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_incident
msgid "Issue"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_issued_total
#: model:ir.model.fields,field_description:helpdesk.field_res_users_journal_item_count
msgid "Journal Items"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_kanban_state
msgid "Kanban State"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket___last_update
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type___last_update
msgid "Last Modified on"
msgstr "နောက်ဆုံးပြင်ဆင်ချိန်"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_write_uid
msgid "Last Updated by"
msgstr "နောက်ဆုံးပြင်ဆင်သူ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_write_date
msgid "Last Updated on"
msgstr "နောက်ဆုံးပြင်ဆင်ချိန်"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_livechat
msgid "Live chat"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "Low priority"
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Manager"
msgstr "မန်နေဂျာ"

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Manually"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_invoice_warn_msg
msgid "Message for Invoice"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_priority
msgid "Minimum Priority"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_time_minutes
msgid "Minutes"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_time_minutes
msgid "Minutes to reach given stage based on ticket creation date"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr "ပို၍ <i class=\"fa fa-caret-down\"/>"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:74
#, python-format
msgid "My Performance"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:13
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "My Tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:21
#, python-format
msgid "My high priority tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:15
#, python-format
msgid "My open tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:27
#, python-format
msgid "My urgent tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_name
msgid "Name"
msgstr "အမည်"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:674
#, python-format
msgid "New Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_high_priorities
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla_high
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla_urgent
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_urgent
msgid "No tickets to display."
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Normal"
msgstr "ပုံမှန်"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Not Satisfied"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_partner_tickets
msgid "Number of tickets from the same partner"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:85
#, python-format
msgid "Only Integer Value should be valid."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_ticket_type_id
msgid ""
"Only apply the SLA to a specific ticket type. If left empty it will apply to"
" all types."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Open Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_close_hours
msgid "Open Time (hours)"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Opened Tickets Analysis"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_user_id
msgid "Owner"
msgstr "ပိုင်ရှင်"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_parent_model_id
msgid "Parent Model"
msgstr "Parent Model"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Parent Record Thread ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_debit_limit
msgid "Payable Limit"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:77
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:94
#, python-format
msgid "Performance Report"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_priority
msgid "Priority"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Productivity"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Public Description..."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Publish this team's ratings on your website."
msgstr ""

#. module: helpdesk
#: model:helpdesk.ticket.type,name:helpdesk.type_question
msgid "Question"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Question and answer section on your website."
msgstr ""

#. module: helpdesk
#: selection:helpdesk.team,assign_method:0
msgid "Randomly"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_rating
msgid "Ratings"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Reach In"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Reach Stage"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.ticket,kanban_state:0
msgid "Ready for next stage"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:171
#, python-format
msgid "Ready to boost your customer service?"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_alias_force_thread_id
msgid "Record Thread ID"
msgstr "Record Thread ID"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reports"
msgstr "အစီရင်ခံစာများ"

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:51
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
#, python-format
msgid "SLA Failed"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "SLA Not Failed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_sla
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_id
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_description
msgid "SLA Policy Description"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_name
msgid "SLA Policy Name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_name
msgid "SLA Policy name"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_sla_active
msgid "SLA active"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Save this page and refresh to activate the feature"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See Customer Satisfaction"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "See SLAs"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_invoice_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send emails to"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_sequence
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_type_sequence
msgid "Sequence"
msgstr "Sequence"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set up your Service Level Agreements to track performance."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr "အပြင်အဆင်များ"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Share presentation and videos, and organize into courses."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_team_ids
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_stage_id
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Stage"
msgstr "အဆင့်"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr "အဆင့်ပြောင်းလဲခဲ့သည်"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr "အဆင့်များ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
msgid ""
"Stages will allow operators to easily track how a specific\n"
"            tickets are positioned in the process."
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
msgid "Status Changed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_name
msgid "Subject"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Subject..."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Submit tickets with an online form."
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:98
#, python-format
msgid "Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr ""

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Support"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_type_view_tree
msgid "Tag"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:376 sql_constraint:helpdesk.tag:0
#, python-format
msgid "Tag name already exists !"
msgstr "တက်(ဒ်) နာမည် ရှိပြီးသားဖြစ်နေသည်"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_tag_ids
msgid "Tags"
msgstr "တက်(ဒ်)များ"

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags allows to organize tickets."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_helpdesk_target_rating
msgid "Target Customer Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_helpdesk_target_success
msgid "Target Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_helpdesk_target_closed
msgid "Target Tickets to Close"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_team_ids
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Team"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_member_ids
msgid "Team Members"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "Teams allows to organize tickets."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:172
#, python-format
msgid "This dashboard will activate once you have created your first ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sale orders "
"and customer invoices"
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:311
#, python-format
msgid "This target does not exist."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
msgid "Ticket"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard_high_priority
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard_urgent
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_team_analysis_action
msgid "Ticket Analysis"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_assigned
#: model:mail.message.subtype,name:helpdesk.mt_ticket_assigned
msgid "Ticket Assigned"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_stage
msgid "Ticket Stage Changed"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
msgid "Ticket Tags"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_type_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_type
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_ticket_type_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_ticket_type_id
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_type_menu
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Ticket Type"
msgstr ""

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_assigned
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:17
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_all_tickets
#: model:ir.actions.act_window,name:helpdesk.action_upcoming_sla_fail_tickets
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_Archived
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_high_priorities
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla_high
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla_urgent
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_slafailed
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_urgent
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_ticket_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#, python-format
msgid "Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Tickets Search"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage_is_close
msgid ""
"Tickets in this stage are considered as done. This is used notably "
"whencomputing SLAs and KPIs on tickets."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Tickets to Review"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_priority
msgid "Tickets under this priority will not be taken into account."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_assign_hours
msgid "Time to first assignation (hours)"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:74
#, python-format
msgid "Today"
msgstr "ယနေ့"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_debit
msgid "Total Payable"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_credit
msgid "Total Receivable"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_twitter
msgid "Twitter"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_ticket_type_action
msgid "Type allows to organize tickets."
msgstr ""

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:363
#: sql_constraint:helpdesk.ticket.type:0
#, python-format
msgid "Type name already exists !"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Unarchive"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_unassigned_tickets
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unassigned Tickets"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Upcoming SLA Fail"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_upcoming_sla_fail_tickets
msgid "Upcoming SLA Fail Tickets"
msgstr ""

#. module: helpdesk
#: selection:helpdesk.sla,priority:0 selection:helpdesk.ticket,priority:0
msgid "Urgent"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:29
#, python-format
msgid "Urgent ("
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr "အသုံးပြုသူ"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
msgid "Users"
msgstr "အသုံးပြုသူများ"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:52
#, python-format
msgid "View failed tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:57
#, python-format
msgid "View high priority failed tickets"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/xml/helpdesk_team_templates.xml:62
#, python-format
msgid "View urgent failed tickets"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_form
msgid "Website Form"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_rating
msgid "Website Rating"
msgstr ""

#. module: helpdesk
#. openerp-web
#: code:addons/helpdesk/static/src/js/helpdesk_dashboard.js:85
#, python-format
msgid "Wrong value entered!"
msgstr "မှားယှငျးသောတနျဖိုးကိုထညျ့သှငျးခဲ့သညျ!"

#. module: helpdesk
#: code:addons/helpdesk/models/helpdesk.py:100
#, python-format
msgid "You must have team members assigned to change the assignation method."
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "Your teams will appear here."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "days<br/>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team_use_website_helpdesk_slides
msgid "eLearning"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "hours<br/>"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "minutes"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "oe_kanban_text_red"
msgstr "oe_kanban_text_red"

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "to create tickets."
msgstr ""

#. module: helpdesk
#: model:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "⇒ View documentation"
msgstr ""
