<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="helpdesk_ticket_view_pivot_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.pivot</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <pivot string="Ticket Analysis" disable_linking="1" sample="1">
                <field name="team_id" type="row"/>
                <field name="ticket_close_hours" widget="float_time" type="measure"/>
                <field name="ticket_open_hours" widget="float_time" type="measure"/>
                <field name="ticket_assignation_hours" widget="float_time" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="helpdesk_ticket_view_graph_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.graph</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <graph string="Tickets Analysis" sample="1" disable_linking="1">
                <field name="team_id"/>
                <field name="ticket_stage_id"/>
            </graph>
        </field>
    </record>

    <record id="helpdesk_ticket_view_cohort" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.cohort</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <cohort string="Tickets Analysis" date_start="create_date" date_stop="close_date" interval="week" sample="1"/>
        </field>
    </record>

    <record id="helpdesk_ticket_report_analysis_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.tree</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <tree string="Tickets Analysis">
                <field name="ticket_id"/>
                <field name="priority" optional="show" widget="priority"/>
                <field name="ticket_type_id" optional="hide"/>
                <field name="team_id" optional="show"/>
                <field name="partner_id" widget="res_partner_many2one" optional="show"/>
                <field name="ticket_stage_id" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="helpdesk_ticket_report_analysis_view_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.report.analysis.search</field>
        <field name="model">helpdesk.ticket.report.analysis</field>
        <field name="arch" type="xml">
            <search string="Tickets Analysis">
                <field name="ticket_id"/>
                <field name="user_id"/>
                <field name="partner_id"/>
                <field name="team_id"/>
                <field name="ticket_type_id"/>
                <field name="ticket_stage_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="ticket_deadline" groups="helpdesk.group_use_sla"/>
                <filter string="My Tickets" domain="[('user_id','=',uid)]" name="my_ticket"/>
                <filter string="Followed" domain="[('ticket_id.message_is_follower', '=', True)]" name="my_follow_ticket"/>
                <filter string="Unassigned" domain="[('user_id', '=', False)]" name="unassigned"/>
                <separator/>
                <filter string="Urgent" domain="[('priority', '=', 3)]" name="urgent_priority"/>
                <filter string="High Priority" domain="[('priority', '=', 2)]" name="high_priority"/>
                <filter string="Medium Priority" domain="[('priority', '=', 1)]" name="medium_priority"/>
                <filter string="Low Priority" domain="[('priority', '=', 0)]" name="low_priority"/>
                <separator groups="helpdesk.group_use_sla"/>
                <filter string="SLA Success" domain="[('ticket_id.sla_success', '=', True)]" name="sla_success" groups="helpdesk.group_use_sla"/>
                <filter string="SLA in Progress" domain="[('sla_fail', '=', False), ('ticket_id.sla_status_ids', '!=', False)]" name="sla_inprogress" groups="helpdesk.group_use_sla"/>
                <filter string="SLA Failed" domain="[('sla_fail', '=', True)]" name="sla_failed" groups="helpdesk.group_use_sla"/>
                <separator/>
                <filter string="Open" domain="[('ticket_stage_id.is_close', '=', False)]" name="is_open"/>
                <filter string="Closed" domain="[('ticket_stage_id.is_close', '=', True)]" name="is_close"/>
                <separator/>
                <filter string="Rated Tickets" domain="[('rating_last_value', '!=', 0.0)]" name="rated_ticket"/>
                <separator/>
                <filter string="Creation Date" date="create_date" name="creation_date"/>
                <separator/>
                <filter string="Archived" domain="[('active', '=', False)]" name="archive"/>
                <group expand="0" string="Group By">
                    <filter string="Assigned to" name="assignee" context="{'group_by':'user_id'}"/>
                    <filter string="Team" name="team" context="{'group_by':'team_id'}"/>
                    <filter string="Stage" name="stage" context="{'group_by':'ticket_stage_id'}"/>
                    <filter string="Type" name="ticket_type_id" context="{'group_by':'ticket_type_id'}"/>
                    <filter string="Priority" name="priority" context="{'group_by': 'priority'}"/>
                    <filter string="Status" name="state" context="{'group_by': 'kanban_state'}"/>
                    <filter string="Customer" name="partner" context="{'group_by': 'partner_id'}"/>
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    <filter string="Creation Date" name="created_by" context="{'group_by': 'create_date'}"/>
                    <filter string="SLA Deadline" name="ticket_deadline" context="{'group_by': 'ticket_deadline'}"/>
                    <filter string="Closing Date" name="close_date" context="{'group_by': 'close_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="helpdesk_ticket_analysis_action" model="ir.actions.act_window">
       <field name="name">Ticket Analysis</field>
       <field name="res_model">helpdesk.ticket.report.analysis</field>
       <field name="view_mode">pivot,graph</field>
        <field name="search_view_id" ref="helpdesk_ticket_report_analysis_view_search"/>
        <field name="context">{
            'search_default_group_by_create_date': 1,
            'pivot_measures': ['__count__', 'ticket_assignation_hours', 'ticket_close_hours'],
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet !
            </p><p>
                Create tickets to get statistics on the performance of your teams.
            </p>
        </field>
    </record>

    <record id="action_helpdesk_ticket_analysis_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="helpdesk_ticket_view_graph_analysis"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_action"/>
    </record>

    <record id="action_helpdesk_ticket_analysis_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="helpdesk_ticket_view_pivot_analysis"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_action"/>
    </record>

    <record id="action_helpdesk_ticket_analysis_cohort" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">cohort</field>
        <field name="view_id" ref="helpdesk_ticket_view_cohort"/>
        <field name="act_window_id" ref="helpdesk_ticket_analysis_action"/>
    </record>

</odoo>
