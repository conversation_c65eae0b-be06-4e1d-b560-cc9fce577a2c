<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="0">
    <template id="portal_my_home_menu_helpdesk" name="Portal layout : helpdesk tickets menu entries" inherit_id="portal.portal_breadcrumbs" priority="50">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <t t-set="rating_page" t-value="page_name == 'rating'"/>
            <li t-if="page_name == 'ticket' or ticket or rating_page" t-attf-class="breadcrumb-item #{'active ' if not ticket else ''}">
                <a t-if="ticket or rating_page" t-attf-href="/my/tickets?{{ keep_query() }}">Tickets</a>
                <t t-else="">Tickets</t>
            </li>
            <li t-if="ticket" class="breadcrumb-item active">
                <span t-field="ticket.name"/> (#<span t-field="ticket.id"/>)
            </li>
            <li t-if="rating_page" t-attf-class="breadcrumb-item active">
                Our Ratings
            </li>
        </xpath>
    </template>

    <template id="portal_my_home_helpdesk_ticket" name="Show Tickets" customize_show="True" inherit_id="portal.portal_my_home" priority="50">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t  t-call="portal.portal_docs_entry">
                <t t-set="title">Tickets</t>
                <t t-set="url" t-value="'/my/tickets'"/>
                <t t-set="placeholder_count" t-value="'ticket_count'"/>
            </t>
        </xpath>
    </template>

    <template id="portal_helpdesk_ticket" name="Helpdesk Ticket">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">Tickets</t>
            </t>
            <div t-if="not grouped_tickets" class="alert alert-info">
                There are currently no Ticket for your account.
            </div>
            <t t-else="">
                <t t-call="portal.portal_table">
                    <t t-foreach="grouped_tickets" t-as="tickets">
                        <thead>
                            <tr t-attf-class="{{'thead-light' if not groupby == 'none' else ''}}">
                                <th class="text-left">Ref</th>
                                <th t-if="groupby == 'none'" class="w-100">Description</th>
                                <th t-else="">
                                    <em class="font-weight-normal text-muted">Tickets in stage:</em>
                                    <span t-field="tickets[0].stage_id.name"/></th>
                                <th/>
                                <th t-if="groupby != 'stage'" class="text-center">Stage</th>
                            </tr>
                        </thead>
                        <t t-foreach="tickets" t-as="ticket">
                            <tr>
                                <td class="text-left"><a t-attf-href="/helpdesk/ticket/#{ticket.id}"><small>#</small><t t-esc="ticket.id"/></a></td>
                                <td><a t-attf-href="/helpdesk/ticket/#{ticket.id}"><span t-field="ticket.name"/></a></td><td/>
                                <td t-if="groupby != 'stage'" class="text-center"><span class="badge badge-pill badge-info" t-field="ticket.stage_id.name"/></td>
                            </tr>
                        </t>
                    </t>
                </t>
            </t>
        </t>
    </template>

    <template id="tickets_followup" name="Helpdesk Tickets">
        <t t-call="portal.portal_layout">
            <t t-set="wrapwrap_classes" t-value="'o_portal_bg_dark'"/>

            <t t-set="o_portal_fullwidth_alert" groups="helpdesk.group_helpdesk_user">
                <t t-call="portal.portal_back_in_edit_mode">
                    <t t-set="backend_url" t-value="'/web#model=helpdesk.ticket&amp;id=%s&amp;view_type=form' % (ticket.id)"/>
                </t>
            </t>

            <t t-call="portal.portal_record_layout">
                <t t-set="card_header">
                    <div class="row no-gutters">
                        <div class="col-md">
                            <h5 class="d-flex mb-1 mb-md-0">
                                <div class="col-9 text-truncate">
                                    <span t-field="ticket.name"/>
                                    <small class="text-muted "> (#<span t-field="ticket.id"/>)</small>
                                </div>
                                <div class="col-3 text-right">
                                    <small class="text-right">Status:</small>
                                    <span t-field="ticket.stage_id.name" class=" badge badge-pill badge-info" title="Current stage of this ticket"/>
                                </div>
                            </h5>
                        </div>
                    </div>
                </t>
                <t t-set="card_body">
                    <div class="row mb-4">
                        <strong class="col-lg-2">Reported on</strong>
                        <span class="col-lg-10" t-field="ticket.create_date" t-options='{"widget": "date"}'/>
                    </div>
                    <div class="row mb-4" t-if="ticket.team_id.portal_show_rating">
                        <strong class="col-lg-2">Managed by</strong>
                        <span class="col-lg-10">
                            <a t-attf-href="/helpdesk/rating/#{ticket.team_id.id}">
                                <span t-field="ticket.team_id.name"/>
                            </a>
                        </span>
                    </div>
                    <div class="row mb-4" t-if="ticket.partner_id">
                        <strong class="col-lg-2">Reported by</strong>
                        <div class="col-lg-10">
                            <div class="row">
                                <div class="col flex-grow-0 pr-3">
                                    <img class="rounded-circle o_portal_contact_img" t-attf-src="#{image_data_uri(ticket.partner_id.avatar_1024)}" alt="Contact"/>
                                </div>
                                <div class="col pl-sm-0">
                                    <div t-field="ticket.partner_id" t-options='{"widget": "contact", "fields": ["name", "email"], "no_marker": true}'/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4" t-if="ticket.user_id">
                        <strong class="col-lg-2">Assigned to</strong>
                        <div class="col-lg-10">
                            <div class="row">
                                <div class="col flex-grow-0 pr-3">
                                    <img class="rounded-circle o_portal_contact_img" t-attf-src="#{image_data_uri(ticket.user_id.avatar_1024)}" alt="Contact"/>
                                </div>
                                <div class="col pl-sm-0">
                                    <div t-field="ticket.user_id" t-options='{"widget": "contact", "fields": ["name", "email"], "no_marker": true}'/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4" name="description">
                        <strong class="col-lg-2">Description</strong>
                        <div t-if="not is_html_empty(ticket.description)" class="col-lg-10" t-field="ticket.description"/>
                        <div t-else="" class="col-lg-10">
                            <em class="text-muted"><small>No description</small></em>
                        </div>
                    </div>
                </t>
            </t>


            <div t-if="ticket.team_id.allow_portal_ticket_closing and not ticket.stage_id.is_close and not ticket.closed_by_partner" class="modal" tabindex="-1" role="dialog" id="helpdesk_ticket_close_modal">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Close ticket</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                        </div>
                        <div class="modal-body">
                            <p>Are you sure you wish to proceed?</p>
                        </div>
                        <div class="modal-footer">
                            <a role="button" class="btn btn-primary" t-att-href="'/my/ticket/close/%s/%s' % (ticket.id, ticket.access_token)">Close the ticket</a>
                            <button type="button" class="btn btn-light" data-dismiss="modal">Discard</button>
                        </div>
                    </div>
                </div>
            </div>

            <div t-if="ticket.team_id.allow_portal_ticket_closing and not ticket.stage_id.is_close and not ticket.closed_by_partner" class="text-center mt-5">
                <button class="btn btn-primary mb-1 pt-1" data-target="#helpdesk_ticket_close_modal" data-toggle="modal"><small><b>Close this ticket</b></small></button>
                <p><small>
                    If the issue has been solved, you can close the request.
                </small></p>
            </div>

            <div class="o_portal_messages_container mt32">
                <h4>Message and communication history</h4>
                <t t-call="portal.message_thread">
                    <t t-set="token" t-value="ticket.access_token"/>
                    <t t-set="object" t-value="ticket"/>
                    <t t-set="pid" t-value="pid"/>
                    <t t-set="hash" t-value="hash"/>
                    <t t-set="disable_composer" t-value="ticket.stage_id.is_close"/>
                </t>
            </div>
        </t>
    </template>

    <!-- Page : Rating of a particular team -->
    <template id="team_rating_progress_data" name="Ticket Rating Page">
        <div class="progress">
            <div class="progress-bar bg-success" t-attf-style="width: #{stats[duration][5]}%;" title="Happy" role="img" aria-label="Happy">
                <t t-esc="int(stats[duration][5])"/>%
            </div>
            <div class="progress-bar bg-warning" t-attf-style="width: #{stats[duration][3]}%;" title="Average" role="img" aria-label="Average">
                <t t-esc="int(stats[duration][3])"/>%
            </div>
            <div class="progress-bar bg-danger" t-attf-style="width: #{stats[duration][1]}%;" title="Bad" role="img" aria-label="Bad">
                <t t-esc="int(stats[duration][1])"/>%
            </div>
        </div>
    </template>

    <template id="team_rating_data" name="Helpdesk Ticket Rating Page">
        <div class="row">
            <div class="col-md-4">
                <h5>Last 7 days</h5>
                <t t-set="duration" t-value="7"/>
                <t t-set="stats" t-value="stats"/>
                <t t-call="helpdesk.team_rating_progress_data"/>
            </div>
            <div class="col-md-4">
                <h5>Last 30 days</h5>
                <t t-set="duration" t-value="30"/>
                <t t-set="stats" t-value="stats"/>
                <t t-call="helpdesk.team_rating_progress_data"/>
            </div>
            <div class="col-md-4">
                <h5>Last 3 months</h5>
                <t t-set="duration" t-value="90"/>
                <t t-set="stats" t-value="stats"/>
                <t t-call="helpdesk.team_rating_progress_data"/>
            </div>

        </div>
        <h5 class="o_page_header">Latest Feedbacks</h5>
        <t t-foreach="ratings" t-as="rating">
            <img t-attf-src='/rating/static/src/img/rating_#{int(rating.rating)}.png' t-att-alt="rating.res_name" t-att-title="rating.res_name"/>
        </t>
    </template>

    <template id="team_rating_page" name="Helpdesk Ticket Rating Page">
        <t t-set="no_breadcrumbs" t-value="True"/>
        <t t-call="portal.portal_layout">
            <h1 class="o_page_header p-4">Our Customer Satisfaction</h1>
            <t t-if="teams">
                <div id="wrap" class="pl-4">
                    <t t-foreach="teams" t-as="t">
                        <t t-set="team" t-value="t['team']"/>
                        <t t-set="ratings" t-value="t['ratings']"/>
                        <t t-set="stats" t-value="t['stats']"/>
                        <div class="oe_structure" id="oe_structure_helpdesk_team_rating_1"/>
                        <div class="container oe_website_rating_team">
                            <h2 t-esc="team.name" class="text-muted" />
                            <div class="row mb32">
                                <div class="col-lg-8">
                                    <t t-if="ratings" t-call="helpdesk.team_rating_data"/>
                                    <t t-else="">
                                        There are no ratings yet.
                                    </t>
                                </div>
                            </div>
                        </div>
                        <div class="oe_structure" id="oe_structure_helpdesk_team_rating_2"/>
                    </t>
                </div>
            </t>
    </t>
    </template>
</data>
</odoo>
