<?xml version="1.0"?>
<odoo>

    <!-- HELPDESK.TEAM -->
    <record id="helpdesk_team_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.team.tree</field>
        <field name="model">helpdesk.team</field>
        <field name="arch" type="xml">
            <tree string="Helpdesk Team" multi_edit="1" sample="1">
                <field name="sequence" widget="handle"/>
                <field name="name" class="field_name" string="Name"/>
                <field name="use_alias" invisible="1"/>
                <field name="display_alias_name" widget="email" attrs="{'invisible': [('use_alias', '=', False)]}" readonly="1"/>
                <field name="company_id" groups="base.group_multi_company" readonly="1" context="{'create': False}"/>
            </tree>
        </field>
    </record>

    <record id="helpdesk_sla_action" model="ir.actions.act_window">
        <field name="name">SLA Policies</field>
        <field name="res_model">helpdesk.sla</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="context">{'default_team_id': active_id, 'search_default_team_id': active_id}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No SLA policies found. Let's create one! 
            </p><p> 
                Make sure tickets are handled in a timely manner by using SLA Policies.<br/>
            </p>
        </field>
    </record>

    <record id="email_template_action_helpdesk" model="ir.actions.act_window" >
            <field name="name">Templates</field>
            <field name="res_model">mail.template</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('team_id', '=', active_id)]</field>
            <field name="context">{'default_team_id': active_id}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new template
                </p>
            </field>
    </record>

    <record id="helpdesk_team_view_form" model="ir.ui.view">
        <field name="name">helpdesk.team.form</field>
        <field name="model">helpdesk.team</field>
        <field name="arch" type="xml">
            <form string="team search" class="oe_form_configuration">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_open_ticket_view" type="object" icon="fa-life-ring" class="oe_stat_button">
                            <field name="open_ticket_count" string="Tickets" widget="statinfo"/>
                        </button>
                        <button name="action_view_team_rating" type="object" class="oe_stat_button" icon="" attrs="{'invisible': ['|', ('use_rating', '=', False), ('rating_percentage_satisfaction', '=', -1)]}">
                            <i class="fa fa-fw o_button_icon fa-smile-o text-success" attrs="{'invisible': [('rating_percentage_satisfaction', '&lt;', 66)]}" title="Satisfied"/>
                            <i class="fa fa-fw o_button_icon fa-meh-o text-warning" attrs="{'invisible': ['|', ('rating_percentage_satisfaction', '&lt;', 33), ('rating_percentage_satisfaction', '&gt;=', 65)]}" title="Okay"/>
                            <i class="fa fa-fw o_button_icon fa-frown-o text-danger" attrs="{'invisible': [('rating_percentage_satisfaction', '&gt;', 32)]}" title="Dissatisfied"/>
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="rating_percentage_satisfaction" nolabel="1"/>%
                                </span>
                                <span class="o_stat_text">
                                    Satisfaction
                                </span>
                            </div>
                        </button>
                        <button name="action_view_sla_policy" type="object" icon="fa-calendar-check-o" class="oe_stat_button" attrs="{'invisible': [('use_sla', '=', False)]}">
                            <field name="sla_policy_count" string="SLA Policies" widget="statinfo"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title" id='title'>
                        <label for="name" string="Name"/>
                        <h1 id="name"><field name="name" placeholder="e.g. Customer Care"/></h1>
                    </div>
                    <field name="active" invisible="1"/>
                    <field name="description" placeholder="Description for customer portal"/>
                    <group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" required="1" nolabel="1" context="{'create': False}"/>
                        </group>
                    </group>
                    <h2>Assignment &amp; Visibility</h2>
                    <div class="row mt16 o_settings_container" id="productivity">
                        <div class="col-lg-6 o_setting_box" title="With random assignation, every user gets the same number of tickets. With balanced assignation, tickets are assigned to the user with the least amount of open tickets.">
                            <div class="o_setting_right_pane">
                                <label for="assign_method"/>
                                <div class="text-muted">
                                    Assign new tickets to the right persons
                                </div>
                                <div>
                                    <field name="assign_method" class="mt16 o_light_label" widget="radio"/>
                                </div>
                                <div attrs="{'invisible': [('assign_method', '=', 'manual')]}">
                                    <field name="member_ids" widget="many2many_avatar_user"
                                    class="mt16" placeholder="Add team members..." options="{'no_quick_create': True'}"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_right_pane">
                                <label for="privacy" string="Team Visibility"/>
                                <div class="text-muted">
                                    Users to whom this team will be visible
                                </div>
                                <div>
                                    <field name="privacy" class="mt16 o_light_label" widget="radio"/>
                                </div>
                                <field name="visibility_member_ids" widget="many2many_avatar_user" options="{'color_field': 'color', 'no_quick_create': True}" attrs="{'invisible': [('privacy', '!=', 'invite')]}" class="mt16" placeholder="Add users..."/>
                            </div>
                        </div>
                    </div>
                    <h2>Channels</h2>
                    <div class="row mt16 o_settings_container" id="channels">
                        <div class="col-lg-6 o_setting_box" id="alias_channels">
                            <div class="o_setting_left_pane">
                                <field name="use_alias"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_alias"/>
                                <div class="text-muted">
                                    Create tickets by sending an email to an alias
                                </div>
                                <div attrs="{'invisible': [('use_alias','=',False)]}" class="mt16">
                                    <div class="oe_edit_only" attrs="{'invisible': [('alias_domain', '=', False)]}">
                                        <strong>Alias </strong>
                                        <field name="alias_name" class="w-25"/>@
                                        <field name="alias_domain" class="oe_inline" readonly="1"/>
                                    </div>
                                    <p class="oe_read_only" attrs="{'invisible': [('alias_domain', '=', False)]}">
                                    <strong>Alias </strong><field name="alias_id" class="oe_read_only oe_inline" required="False"/>
                                    </p>
                                    <field name="has_external_mail_server" invisible="1"/>
                                    <p class="text-muted" attrs="{'invisible': ['|', ('alias_domain', '!=', False), ('has_external_mail_server', '!=', False)]}">
                                        <i class="fa fa-lightbulb-o" role='img'/> Enable the <i>Custom Email Servers</i> feature in the <i>General Settings</i> and indicate an <i>alias domain</i>
                                    </p>
                                    <p attrs="{'invisible': [('alias_domain', '!=', False)]}">
                                        <button name="%(base_setup.action_general_configuration)d" type="action" string="Configure a custom domain" icon="fa-arrow-right" class="btn-link"/>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_website_helpdesk_livechat"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_website_helpdesk_livechat"/>
                                <div class="text-muted">
                                    Get in touch with your website visitors
                                </div>
                                <div id="im_livechat" attrs="{'invisible': [('use_website_helpdesk_livechat','=',False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt32 o_settings_container" id="website_form_channel">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_website_helpdesk_form"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_website_helpdesk_form"/>
                                <div class="text-muted">
                                    Get tickets through an online form
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2 class="mt32">Track &amp; Bill Time</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-lg-6 o_setting_box" id="timesheet">
                            <div class="o_setting_left_pane">
                                <field name="use_helpdesk_timesheet"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_helpdesk_timesheet"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/services/helpdesk/timesheet_and_invoice/invoice_time.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Track the time spent on tickets
                                </div>
                                <div id='helpdesk_timesheet' attrs="{'invisible': [('use_helpdesk_timesheet', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box" id="sale_timesheet">
                            <div class="o_setting_left_pane">
                                <field name="use_helpdesk_sale_timesheet"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_helpdesk_sale_timesheet"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/services/helpdesk/timesheet_and_invoice/reinvoice_from_project.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Bill the time spent on your tickets to your customers
                                </div>
                                <div id='helpdesk_sale_timesheet' attrs="{'invisible': [('use_helpdesk_sale_timesheet', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Performance</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-lg-6 o_setting_box" id="sla">
                            <div class="o_setting_left_pane">
                                <field name="use_sla"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_sla"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/services/helpdesk/overview/sla.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Make sure tickets are handled on time

                                </div>
                                <div class="mt16" attrs="{'invisible': [('use_sla', '=', False)]}">
                                    <label for="resource_calendar_id"/>
                                    <field name="resource_calendar_id" attrs="{'required': [('use_sla', '=', True)]}"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box" id="rating">
                            <div class="o_setting_left_pane">
                                <field name="use_rating"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_rating"/>
                                <div class="text-muted">
                                   Track customer satisfaction on tickets
                                </div>
                                <div id="use_rating" attrs="{'invisible': [('use_rating', '=', False)]}">
                                    <field name="portal_show_rating" class="oe_inline"/>
                                    <label for="portal_show_rating" string="Publish this team's ratings on your website"/>
                                    <div class="mt-3">
                                        <button name="%(helpdesk.helpdesk_stage_action)d" type="action" string="Set an Email Template on Stages" icon="fa-arrow-right" class="btn-link" context="{'search_default_team_ids': name}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Self-Service</h2>
                    <div class="row mt16 o_settings_container" id="self-Service">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_website_helpdesk_forum"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_website_helpdesk_forum"/>
                                <div class="text-muted">
                                    Let your customers answer each other's questions on a forum
                                </div>
                                <div id="use_website_helpdesk_forum" attrs="{'invisible': [('use_website_helpdesk_forum', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_website_helpdesk_slides"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_website_helpdesk_slides" string="eLearning"/>
                                <div class="text-muted">
                                    Share presentation and videos, and organize into courses
                                </div>
                                <div id="use_website_helpdesk_slides" attrs="{'invisible': [('use_website_helpdesk_slides', '=', False)]}">
                                    <div class="text-warning mb4 mt16" id="o_slide_option">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="auto_close_ticket"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="auto_close_ticket"/>
                                <div class="text-muted">
                                    Close inactive tickets automatically
                                </div>
                                <div class="content-group" attrs="{'invisible': [('auto_close_ticket', '=', False)]}">
                                    <field name="stage_ids" invisible="1"/>
                                    <div class="mt16">
                                        <label for="to_stage_id"/>
                                        <field name="to_stage_id" class="ml-2 oe_inline" attrs="{'required': [('auto_close_ticket', '=', True)]}" context="{'default_team_id': active_id}"/>
                                    </div>
                                    <div class="mt8">
                                        <strong>After</strong><field name="auto_close_day" class="mx-2 oe_inline text-center" required="1"/><span>days of inactivity</span>
                                    </div>
                                    <div class="mt8">
                                        <label for="from_stage_ids"/>
                                        <field name="from_stage_ids" widget="many2many_tags" class="ml-2 " context="{'default_team_id': active_id}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="allow_portal_ticket_closing"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="allow_portal_ticket_closing"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/services/helpdesk/advanced/close_tickets.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Allow your customers to close their own tickets
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>After-Sales <a href="https://www.odoo.com/documentation/15.0/applications/services/helpdesk/advanced/after_sales.html" title="Documentation" class="o_doc_link" target="_blank"></a></h2>
                    <div class="row mt32 o_settings_container" id="after-sales">
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_credit_notes"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_credit_notes"/>
                                <div class="text-muted">
                                    Issue credits notes
                                </div>
                                <div id="use_credit_notes" attrs="{'invisible': [('use_credit_notes', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_coupons"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_coupons"/>
                                <div class="text-muted">
                                    Grant discounts or free products
                                </div>
                                <div id="use_coupons" attrs="{'invisible': [('use_coupons', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_product_returns"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_product_returns"/>
                                <div class="text-muted">
                                    Return faulty products
                                </div>
                                <div id="use_product_returns" attrs="{'invisible': [('use_product_returns', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_product_repairs"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_product_repairs"/>
                                <div class="text-muted">
                                    Send broken products for repair
                                </div>
                                <div id="use_product_repairs" attrs="{'invisible': [('use_product_repairs', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="use_fsm"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="use_fsm"/>
                                <div class="text-muted">
                                    Plan onsite interventions
                                </div>
                                <div id="field_service" attrs="{'invisible': [('use_fsm', '=', False)]}">
                                    <div class="text-warning mb4 mt16">
                                        Save this page and refresh to activate the feature.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="oe_chatter" groups="base.group_user">
                        <field name="message_follower_ids" help="Follow this team to automatically track the events associated to tickets of this team."/>
                        <field name="message_ids"/>     <!-- To be able to see attachments linked to the team -->
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <record id="helpdesk_team_view_search" model="ir.ui.view">
        <field name="name">helpdesk.team.search</field>
        <field name="model">helpdesk.team</field>
        <field name="arch" type="xml">
            <search string="Team Search">
                <field name="name"/>
                <field name="member_ids"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="My Teams" domain="[('member_ids', 'in', [uid]), ('assign_method', '!=', 'manual')]" name="my_team"/>
                <filter string="Followed Teams" domain="[('message_is_follower', '=', True)]" name="my_follow_team"/>
                <separator/>
                <filter string="Archived" domain="[('active', '=', False)]" name="archived"/>
                <group expand="0" string="Group By">
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="helpdesk_team_view_kanban" model="ir.ui.view" >
        <field name="name">helpdesk.team.dashboard</field>
        <field name="model">helpdesk.team</field>
        <field name="priority">200</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard o_helpdesk_kanban" group_create="0" create="0" js_class="helpdesk_dashboard">
                <field name="name"/>
                <field name="sequence" widget="handle"/>
                <field name="color"/>
                <field name="use_alias"/>
                <field name="alias_name"/>
                <field name="alias_domain"/>
                <field name="alias_id"/>
                <field name="use_rating"/>
                <field name="use_sla"/>
                <field name="upcoming_sla_fail_tickets"/>
                <field name="unassigned_tickets"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''} oe_kanban_global_click">
                            <span class="oe_kanban_color_help" t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img" t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                            <div t-attf-class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary"><field name="name"/></div>
                                    <small t-if="record.use_alias.value and record.alias_name.value and record.alias_domain.value"><i class="fa fa-envelope-o" title="Domain alias" role="img" aria-label="Domain alias"></i>&amp;nbsp; <t t-esc="record.alias_id.value"/></small>
                                </div>
                                <div class="o_kanban_manage_button_section" t-if="!selection_mode" groups="helpdesk.group_helpdesk_manager">
                                    <a class="o_kanban_manage_toggle_button" href="#"><i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/></a>
                                </div>
                            </div>
                            <div class="container o_kanban_card_content" t-if="!selection_mode">
                                <div class="row">
                                    <div class="col-6 o_kanban_primary_left">
                                        <button class="btn btn-primary o_helpdesk_ticket_btn" name="action_view_ticket" type="object">Tickets</button>
                                    </div>
                                    <div class="col-6 o_kanban_primary_right">
                                        <div class="mb4" groups="helpdesk.group_use_sla" attrs="{'invisible': [('use_sla', '=', False)]}">
                                            <a name="%(helpdesk.action_upcoming_sla_fail_all_tickets)d" type="action"  context="{'search_default_team_id': active_id, 'default_team_id': active_id}"><t t-esc="record.upcoming_sla_fail_tickets.raw_value"/> SLA Issues</a>
                                        </div>
                                        <div class="mb4">
                                            <a name="%(helpdesk.helpdesk_ticket_action_unassigned)d" type="action" context="{'search_default_team_id': active_id, 'default_team_id': active_id}"><t t-esc="record.unassigned_tickets.raw_value"/> Unassigned Tickets</a>
                                        </div>
                                        <div class="mb4" t-if="record.use_rating.raw_value">
                                            <a name="action_view_all_rating" type="object">See Customer Satisfaction</a>
                                        </div>
                                    </div>
                                </div>
                            </div><div class="container o_kanban_card_manage_pane dropdown-menu" role="menu">

                                <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                                    <div role="menuitem" aria-haspopup="true" class="col-8">
                                        <ul class="oe_kanban_colorpicker" data-field="color" role="menu"/>
                                    </div>
                                    <div role="menuitem" class="col-4 text-right">
                                        <a type="edit">Settings</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="helpdesk_team_kanban_view" model="ir.ui.view">
        <field name="name">helpdesk.team.kanban.view</field>
        <field name="model">helpdesk.team</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_team_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes">
                <attribute name="class">oe_background_grey o_kanban_dashboard</attribute>
                <attribute name="js_class"></attribute>
                <attribute name="create">1</attribute>
             </xpath>
        </field>
    </record>

    <record id="helpdesk_team_action" model="ir.actions.act_window">
        <field name="name">Helpdesk Teams</field>
        <field name="res_model">helpdesk.team</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
                No teams found
            </p>
            <p>
                Teams regroup tickets for people sharing the same expertise or from the same area.
            </p>
        </field>
    </record>

    <record id="helpdesk_team_action_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="helpdesk.helpdesk_team_view_tree"/>
        <field name="act_window_id" ref="helpdesk_team_action"/>
    </record>

    <record id="helpdesk_team_action_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="helpdesk.helpdesk_team_kanban_view"/>
        <field name="act_window_id" ref="helpdesk_team_action"/>
    </record>

    <menuitem id="helpdesk_team_menu" name="Helpdesk Teams" action="helpdesk_team_action"
        sequence="0" parent="helpdesk.helpdesk_menu_config"
        groups="helpdesk.group_helpdesk_manager"/>

    <record id="helpdesk_team_dashboard_action_main" model="ir.actions.act_window">
        <field name="name">Helpdesk Overview</field>
        <field name="res_model">helpdesk.team</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{}</field>
        <field name="view_id" ref="helpdesk.helpdesk_team_view_kanban"/>
        <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
                No teams found
            </p>
            <p>
                Teams regroup tickets for people sharing the same expertise or from the same area.
            </p>
        </field>
    </record>

    <menuitem id="helpdesk_menu_team_dashboard" action="helpdesk.helpdesk_team_dashboard_action_main"
        sequence="5" parent="helpdesk.menu_helpdesk_root" name="Overview"
        groups="helpdesk.group_helpdesk_user"/>

</odoo>
