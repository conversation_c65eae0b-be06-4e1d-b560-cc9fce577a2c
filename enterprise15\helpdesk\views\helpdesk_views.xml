<?xml version="1.0"?>
<odoo>

    <menuitem id="menu_helpdesk_root" name="Helpdesk"
        sequence="90"
        web_icon="helpdesk,static/description/icon.png"
        groups="helpdesk.group_helpdesk_user"/>

    <menuitem id="helpdesk_menu_config" name="Configuration"
        sequence="100" parent="helpdesk.menu_helpdesk_root"
        groups="helpdesk.group_helpdesk_manager"/>

    <!-- HELPDESK.TAG -->
    <record id="helpdesk_tag_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.tags.tree</field>
        <field name="model">helpdesk.tag</field>
        <field name="arch" type="xml">
            <tree editable="top" string="Tag" sample="1" multi_edit="1">
                <field name="name"/>
                <field name="color" widget="color_picker"/>
            </tree>
        </field>
    </record>

    <record id="helpdesk_tag_view_form" model="ir.ui.view">
        <field name="name">helpdesk.tags.form</field>
        <field name="model">helpdesk.tag</field>
        <field name="arch" type="xml">
            <form string="Tags">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="color" widget="color_picker"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="helpdesk_tag_action" model="ir.actions.act_window">
        <field name="name">Tags</field>
        <field name="res_model">helpdesk.tag</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No tags found. Let's create one!
          </p><p>
            Tags are perfect for organizing your tickets.
          </p>
        </field>
    </record>

    <menuitem id="helpdesk_tag_menu" action="helpdesk_tag_action"
        sequence="1" parent="helpdesk.helpdesk_menu_config"
        groups="base.group_no_one"/>

    <!-- HELPDESK.TICKET.TYPE -->
     <record id="helpdesk_ticket_type_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.type.tree</field>
        <field name="model">helpdesk.ticket.type</field>
        <field name="arch" type="xml">
            <tree editable="top" string="Tag" sample="1">
                <field name="sequence" widget="handle" groups="base.group_no_one"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="helpdesk_ticket_type_action" model="ir.actions.act_window">
        <field name="name">Types</field>
        <field name="res_model">helpdesk.ticket.type</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No types found. Let's create one!
          </p><p>
            Types are perfect for categorizing your tickets.
          </p>
        </field>
    </record>

    <menuitem id="helpdesk_ticket_type_menu" action="helpdesk_ticket_type_action"
        sequence="1" parent="helpdesk.helpdesk_menu_config"/>

    <!-- HELPDESK.STAGE -->
    <record id="helpdesk_stage_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.stages.tree</field>
        <field name="model">helpdesk.stage</field>
        <field name="arch" type="xml">
            <tree string="Stage" multi_edit="1" sample="1">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="team_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="show"/>
                <field name="template_id" options="{'no_create': True}" optional="hide"/>
                <field name="is_close" optional="hide"/>
                <field name="fold" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="helpdesk_stage_view_search" model="ir.ui.view">
        <field name="name">helpdesk.stages.search</field>
        <field name="model">helpdesk.stage</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="team_ids"/>
                <field name="template_id" domain="[('model', '=', 'helpdesk.ticket')]"/>
                <filter string="Archived" name="active" domain="[('active', '=' ,False)]"/>
            </search>
        </field>
    </record>

    <record id="helpdesk_stage_view_kanban" model="ir.ui.view">
        <field name="name">helpdesk.stages.kanban</field>
        <field name="model">helpdesk.stage</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="sequence" widget="handle"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click oe_kanban_content">
                            <strong class="o_kanban_record_title">
                                <field name="name"/>
                            </strong>
                            <div>
                                <field name="team_ids" options="{'color_field': 'color'}" widget="many2many_tags"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="helpdesk_stage_view_form" model="ir.ui.view">
        <field name="name">helpdesk.stage.form</field>
        <field name="model">helpdesk.stage</field>
        <field name="arch" type="xml">
            <form string="Stage Search">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_open_helpdesk_ticket" icon="fa-life-ring" attrs="{'invisible': [('ticket_count', '=', 0)]}">
                            <field name="ticket_count" widget="statinfo" string="Tickets"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="template_id" context="{'default_model': 'helpdesk.ticket'}"/>
                            <field name="sequence" groups="base.group_no_one"/>
                        </group>
                        <group>
                            <field name="is_close"/>
                            <field name="fold"/>
                            <field name="team_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        </group>
                    </group>
                    <group string="Stage Description and States Meaning">
                        <group>
                            <p class="text-muted mt-2" colspan="2">
                                Add a description to help your coworkers understand the meaning and purpose of the stage.
                            </p>
                            <field name="description" placeholder="Add a description..." nolabel="1" colspan="2"/>
                            <p class="text-muted" colspan="2">
                                At each stage, employees can block tickets or mark them as ready for the next step.
                                You can customize the meaning of each state.
                            </p>
                            <div class="row ml-1" colspan="2">
                                <label for="legend_normal" string=" " class="o_status mt4"
                                    title="Task in progress. Click to block or set as done."
                                    aria-label="Task in progress. Click to block or set as done." role="img"/>
                                <div class="col-11 pl-0">
                                    <field name="legend_normal"/>
                                </div>
                            </div>
                            <div class="row ml-1" colspan="2">
                                <label for="legend_blocked" string=" " class="o_status o_status_red mt4"
                                    title="Task is blocked. Click to unblock or set as done."
                                    aria-label="Task is blocked. Click to unblock or set as done." role="img"/>
                                <div class="col-11 pl-0">
                                    <field name="legend_blocked"/>
                                </div>
                            </div>
                            <div class="row ml-1" colspan="2">
                                <label for="legend_done" string=" " class="o_status o_status_green mt4"
                                    title="This step is done. Click to block or set in progress."
                                    aria-label="This step is done. Click to block or set in progress." role="img"/>
                                <div class="col-11 pl-0">
                                    <field name="legend_done"/>
                                </div>
                            </div>

                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="helpdesk_stage_action" model="ir.actions.act_window">
        <field name="name">Stages</field>
        <field name="res_model">helpdesk.stage</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="search_view_id" ref="helpdesk_stage_view_search"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No stages found. Let's create one!
          </p><p>
            Adapt your pipeline to your workflow and track the progress of your tickets.
          </p>
        </field>
    </record>

    <menuitem id="helpdesk_stage_menu" action="helpdesk_stage_action"
        sequence="3" parent="helpdesk.helpdesk_menu_config"/>

    <!-- HELPDESK.SLA -->
    <record id="helpdesk_sla_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.sla.tree</field>
        <field name="model">helpdesk.sla</field>
        <field name="arch" type="xml">
            <tree string="SLA Policies" multi_edit="1" sample="1">
                <field name="company_id" invisible="1"/>
                <field name="name"/>
                <field name="team_id" optional="show" readonly="1"/>
                <field name="priority" optional="show" widget="priority"/>
                <field name="ticket_type_id" options="{'no_create_edit': True}" optional="show"/>
                <field name="tag_ids" widget="many2many_tags" optional="hide" options="{'color_field': 'color'}"/>
                <field name="partner_ids" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]" widget="many2many_tags" optional="hide"/>
                <field name="stage_id" optional="show" string="Reach Stage" readonly="1"/>
                <field name="time" optional="show" widget="float_time"/>
                <field name="exclude_stage_ids" widget="many2many_tags" optional="hide" readonly="1"/>
            </tree>
        </field>
    </record>

    <record id="helpdesk_sla_view_kanban" model="ir.ui.view">
        <field name="name">helpdesk.sla.kanban</field>
        <field name="model">helpdesk.sla</field>
        <field name="arch" type="xml">
            <kanban>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click oe_kanban_content">
                            <strong class="o_kanban_record_title">
                                <field name="name"/>
                            </strong>
                            <div>
                                <field name="team_id"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="helpdesk_sla_view_search" model="ir.ui.view">
        <field name="name">helpdesk.sla.search</field>
        <field name="model">helpdesk.sla</field>
        <field name="arch" type="xml">
            <search string="Search SLA Policies">
                <field name="name"/>
                <field name="team_id"/>
                <field name="ticket_type_id"/>
                <field name="tag_ids"/>
                <field name="partner_ids"/>
                <field name="stage_id" string="Reach Stage"/>
                <field name="exclude_stage_ids"/>
                <filter string="My Teams" domain="['|', ('team_id.member_ids', 'in', [uid]), ('team_id.member_ids', '=', False)]" name="my_team"/>
                <filter string="Followed Teams" domain="[('team_id.message_is_follower', '=', True)]" name="my_follow_team"/>
                <separator/>
                <filter string="Urgent" domain="[('priority', '=', 3)]" name="urgent_priority"/>
                <filter string="High Priority" domain="[('priority', '=', 2)]" name="high_priority"/>
                <filter string="Medium Priority" domain="[('priority', '=', 1)]" name="low_priority"/>
                <filter string="Low Priority" domain="[('priority', '=', 0)]" name="all_priority"/>
                <separator/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Team" name="team" context="{'group_by':'team_id'}"/>
                    <filter string="Priority" name="priority" context="{'group_by': 'priority'}"/>
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    <filter string="Reach Stage" name="stage" context="{'group_by': 'stage_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="helpdesk_sla_view_form" model="ir.ui.view">
        <field name="name">helpdesk.sla.form</field>
        <field name="model">helpdesk.sla</field>
        <field name="arch" type="xml">
            <form string="SLA Policy">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_open_helpdesk_ticket" icon="fa-life-ring" attrs="{'invisible': [('ticket_count', '=', 0)]}">
                            <field name="ticket_count" widget="statinfo" string="Tickets"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="e.g. Close urgent tickets within 36 hours"/>
                        </h1>
                    </div>
                    <field name="description" placeholder="Description of the policy..."/>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                            <separator colspan="2" string="Criteria"/>
                            <field name="team_id" domain="[('use_sla', '=', True)]"/>
                            <field name="priority" widget="priority"/>
                            <field name="ticket_type_id" options="{'no_open': True, 'no_create': True}"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            <field name="partner_ids" widget="many2many_tags"
                                domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]"/>

                            <separator colspan="2" string="Target" class="mt32"/>
                            <field name="stage_id" string="Reach Stage" domain="[('team_ids', '=', team_id), ('id', 'not in', exclude_stage_ids)]" required="1" context="{'default_team_ids': team_id and [(6, 0, [team_id])]}"/>
                            <label for="time" string="In"/>
                            <div class="o_row">
                                <field name="time" widget="float_time"/><span class="o_field_widget o_readonly_modifier">Working Hours</span>
                            </div>
                            <field name="exclude_stage_ids" widget="many2many_tags" domain="[('id', '!=', stage_id), ('team_ids', '=', team_id)]" context="{'default_team_ids': team_id and [(6, 0, [team_id])]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="helpdesk_sla_action_main" model="ir.actions.act_window">
        <field name="name">SLA Policies</field>
        <field name="res_model">helpdesk.sla</field>
        <field name="view_mode">tree,form,kanban</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
                No SLA policies found. Let's create one!
            </p><p>
                Make sure tickets are handled on time by using SLA Policies.<br/>
            </p>
        </field>
    </record>

    <menuitem id="helpdesk_sla_menu_main" action="helpdesk_sla_action_main"
        sequence="4" parent="helpdesk.helpdesk_menu_config"
        groups="helpdesk.group_use_sla"/>

    <!-- HELPDESK.TICKET -->
    <record id="helpdesk_ticket_view_activity" model="ir.ui.view">
        <field name="name">helpdesk.ticket.activity</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <activity string="Ticket">
                <field name="user_id"/>
                <templates>
                    <div t-name="activity-box">
                        <field name="user_id" widget="many2one_avatar_user"/>
                        <div class="w-100">
                            <div class="d-flex justify-content-between">
                                <div style="min-width: 0;">
                                    <field name="name" display="full"/>
                                </div>
                                <div class="flex-shrink-0">
                                    #<field name="id"/>
                                </div>
                            </div>
                            <field name="partner_id" muted="1" display="full"/>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record id="helpdesk_ticket_view_graph_main" model="ir.ui.view">
        <field name="name">helpdesk.ticket.graph</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <graph string="Helpdesk Tickets" type="line" sample="1">
                <field name="create_date" interval="day"/>
                <field name="stage_id"/>
            </graph>
        </field>
    </record>

    <record id="helpdesk_ticket_view_pivot_main" model="ir.ui.view">
        <field name="name">helpdesk.ticket.pivot</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <pivot string="Helpdesk Tickets" sample="1">
                <field name="create_date" type="row" interval="day"/>
                <field name="stage_id" type="col"/>
                <field name="color" invisible="1"/>
            </pivot>
        </field>
    </record>

    <record id="helpdesk_tickets_view_search" model="ir.ui.view">
        <field name="name">helpdesk.ticket.search</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <search string="Tickets Search">
                <field name="name" string="Ticket"/>
                <field name="id" string="Ticket ID"/>
                <field name="tag_ids"/>
                <field name="user_id"/>
                <field name="partner_id" filter_domain="['|', '|', '|', ('partner_id', 'ilike', self), ('partner_email', 'ilike', self), ('partner_phone', 'ilike', self), ('partner_name', 'ilike', self)]"/>
                <field name="team_id" invisible="context.get('default_team_id', False)"/>
                <field name="ticket_type_id"/>
                <field name="stage_id"/>
                <field name="sla_ids" groups="helpdesk.group_use_sla"/>
                <field name="company_id" groups="base.group_multi_company" invisible="context.get('default_team_id', False)"/>
                <field name="priority" invisible="1"/>
                <field name="sla_deadline" groups="helpdesk.group_use_sla"/>

                <filter string="My Tickets" domain="[('user_id','=',uid)]" name="my_ticket"/>
                <filter string="Followed" domain="[('message_is_follower', '=', True)]" name="my_follow_ticket"/>
                <filter string="Unassigned" domain="[('user_id','=',False)]" name="unassigned"/>
                <separator/>
                <filter string="Urgent" domain="[('priority', '=', 3)]" name="urgent_priority"/>
                <filter string="High Priority" domain="[('priority', '=', 2)]" name="high_priority"/>
                <filter string="Medium Priority" domain="[('priority', '=', 1)]" name="medium_priority"/>
                <filter string="Low Priority" domain="[('priority', '=', 0)]" name="low_priority"/>
                <separator groups="helpdesk.group_use_sla"/>
                <filter string="SLA Success" domain="[('sla_success', '=', True)]" name="sla_success" groups="helpdesk.group_use_sla"/>
                <filter string="SLA in Progress" domain="[('sla_status_ids.status', '=', 'ongoing')]" name="sla_inprogress" groups="helpdesk.group_use_sla"/>
                <filter string="SLA Failed" domain="[('sla_fail', '=', True)]" name="sla_failed" groups="helpdesk.group_use_sla"/>
                <separator/>
                <filter string="Unread Messages" domain="[('message_needaction','=',True)]" name="message_needaction"/>
                <separator/>
                <filter string="Open" domain="[('stage_id.is_close','=',False)]" name="is_open"/>
                <filter string="Closed" domain="[('stage_id.is_close','=',True)]" name="is_close"/>
                <separator/>
                <filter string="Rated Tickets" domain="[('rating_last_value', '!=', 0.0)]" name="rated_ticket"/>
                <separator/>
                <filter string="Creation Date" date="create_date" name="creation_date"/>
                <separator/>
                <filter string="Archived" domain="[('active', '=', False)]" name="archive"/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))
                        ]"/>
                <group expand="0" string="Group By">
                    <filter string="Assigned to" name="assignee" context="{'group_by':'user_id'}"/>
                    <filter string="Team" name="team" context="{'group_by':'team_id'}" invisible="context.get('default_team_id', False)"/>
                    <filter string="Stage" name="stage" context="{'group_by':'stage_id'}"/>
                    <filter string="Type" name="ticket_type_id" context="{'group_by':'ticket_type_id'}"/>
                    <filter string="Priority" name="priority" context="{'group_by': 'priority'}"/>
                    <filter string="Status" name="state" context="{'group_by': 'kanban_state'}"/>
                    <filter string="Customer" name="partner" context="{'group_by': 'partner_id'}"/>
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    <filter string="Create Date" name="created_by" context="{'group_by': 'create_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="helpdesk_ticket_view_search_analysis_closed" model="ir.ui.view">
        <field name="name">helpdesk.ticket.search</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <search string="Tickets Search">
                <field name="name"/>
                <field name="ticket_type_id"/>
                <field name="team_id"/>
                <field name="user_id"/>
                <filter string="My Tickets" domain="[('user_id','=',uid)]" name="my_ticket"/>
                <filter string="Unassigned Tickets" domain="[('user_id','=',False)]" name="unassigned"/>
                <separator/>
                <filter string="Archived" domain="[('active','=',False)]" name="archive"/>
                <separator/>
                <filter name="filter_create_date" date="create_date"/>
                <filter name="filter_assign_date" date="assign_date"/>
                <filter name="filter_sla_deadline" date="sla_deadline"/>
                <separator/>
                <filter string="SLA Failed" name="sla_failed" domain="[('sla_fail','!=',False)]" groups="helpdesk.group_use_sla"/>
                <group expand="0" string="Group By">
                  <filter string="Assignee" name="assignee" context="{'group_by':'user_id'}"/>
                  <filter string="Team" name="team" context="{'group_by':'team_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="helpdesk_tickets_view_tree" model="ir.ui.view">
        <field name="name">helpdesk.ticket.tree</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <tree string="Tickets" multi_edit="1" sample="1">
                <field name="use_sla" invisible="1"/>
                <field name="legend_normal" invisible="1"/>
                <field name="legend_blocked" invisible="1"/>
                <field name="legend_done" invisible="1"/>
                <field name="display_name" string="Name" readonly="1"/>
                <field name="team_id" optional="show" readonly="1" invisible="context.get('default_team_id', False)"/>
                <field name="team_id" optional="hide" readonly="1" invisible="not context.get('default_team_id', False)"/>
                <field name="user_id" optional="show" widget="many2one_avatar_user" options="{'no_quick_create': True}"/>
                <field name="partner_id" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]" widget="res_partner_many2one" optional="show"/>
                <field name="company_id" groups="base.group_multi_company" optional="show" readonly="1" invisible="context.get('default_team_id', False)"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide" readonly="1" invisible="not context.get('default_team_id', False)"/>
                <field name="activity_ids" widget="list_activity" optional="show"/>
                <field name="priority" optional="show" widget="priority"/>
                <field name="sla_status_ids" widget="many2many_tags" attrs="{'invisible': [('use_sla', '=', False)]}" options="{'color_field': 'color'}" string="SLAs" optional="hide" readonly="1"/>
                <field name="sla_deadline" attrs="{'invisible': [('use_sla', '=', False)]}" optional="show" widget="remaining_days"/>
                <field name="ticket_type_id" options="{'no_create_edit': True}" optional="hide"/>
                <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="kanban_state" optional="hide" readonly="1" widget="state_selection"/>
                <field name="stage_id" optional="show" readonly="not context.get('default_team_id', False)"/>
            </tree>
        </field>
    </record>

    <record id="quick_create_ticket_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.form.quick_create</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="stage_id" invisible="1"/>
                    <field name="team_id" invisible="1"/>
                    <field name="legend_normal" invisible="1"/>
                    <field name="legend_blocked" invisible="1"/>
                    <field name="legend_done" invisible="1"/>
                    <field name="name"/>
                    <field name="domain_user_ids" invisible="1"/>
                    <field name="team_id" required="1" invisible="context.get('default_team_id')"  options="{'no_open': True}"/>
                    <field name="user_id" domain="[('id', 'in', domain_user_ids)]" options="{'no_open': True}"/>
                    <field name="partner_id" options="{'no_open': True}" widget="res_partner_many2one"/>
                </group>
            </form>
        </field>
    </record>

    <record id="helpdesk_ticket_view_kanban" model="ir.ui.view">
        <field name="name">helpdesk.ticket.kanban</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <kanban default_group_by="stage_id" on_create="quick_create" class="o_kanban_small_column o_kanban_helpdesk_ticket" quick_create_view="helpdesk.quick_create_ticket_form" sample="1">
                <field name="stage_id" options='{"group_by_tooltip": {"description": "Stage Description"}}'/>
                <field name="user_id"/>
                <field name="color"/>
                <field name="priority"/>
                <field name="sla_fail"/>
                <field name="tag_ids"/>
                <field name="active"/>
                <field name="activity_ids"/>
                <field name="activity_state"/>
                <field name="team_id"/>
                <field name="legend_blocked"/>
                <field name="legend_normal"/>
                <field name="legend_done"/>
                <field name="use_rating"/>
                <field name="rating_last_value"/>
                <field name="rating_ids"/>
                <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''} oe_kanban_global_click">
                            <span class="oe_kanban_color_help" t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img" t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable"><a type="edit" class="dropdown-item" role="menuitem">Edit</a></t>
                                    <t t-if="widget.deletable"><a type="delete" class="dropdown-item" role="menuitem">Delete</a></t>
                                    <a name="toggle_active" type="object" class="dropdown-item" role="menuitem" t-if="! record.active.value">Restore</a>
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                            <div class="oe_kanban_content">
                                <div>
                                    <strong><field name="name"/> (#<field name="id"/>)</strong>
                                </div>
                                <div>
                                    <field name="commercial_partner_id"/>
                                </div>
                                <div>
                                    <field name="ticket_type_id"/>
                                </div>
                                <div>
                                    <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                </div>
                                <field name="use_sla" invisible="1"/>
                                <div t-if="record.sla_deadline.raw_value &amp;&amp; record.use_sla.raw_value">
                                    <t t-if="new Date(record.sla_deadline.raw_value) &lt; (new Date())" t-set="red" t-value="'oe_kanban_text_red'"/>
                                    <span t-attf-class="{{red}}">
                                        <field name="sla_deadline" widget="remaining_days"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="priority" widget="priority"/>
                                        <field name="activity_ids" widget="kanban_activity"/>
                                        <b t-if="record.use_rating.raw_value and record.rating_ids.raw_value.length">
                                            <span style="font-weight:bold;" class="fa fa-fw mt4 fa-smile-o text-success" t-if="record.rating_last_value.value == 5" title="Latest Rating: Satisfied" role="img" aria-label="Happy face"/>
                                            <span style="font-weight:bold;" class="fa fa-fw mt4 fa-meh-o text-warning" t-if="record.rating_last_value.value == 3" title="Latest Rating: Okay" role="img" aria-label="Neutral face"/>
                                            <span style="font-weight:bold;" class="fa fa-fw mt4 fa-frown-o text-danger" t-if="record.rating_last_value.value == 1" title="Latest Rating: Dissatisfied" role="img" aria-label="Sad face"/>
                                        </b>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <field name="kanban_state" widget="state_selection" groups="base.group_user"/>
                                        <field t-if="record.user_id.raw_value" name="user_id" widget="many2one_avatar_user"/>
                                    </div>
                                </div>
                            </div>
                            <div class="oe_clear"></div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>


    <record id="helpdesk_ticket_view_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.form</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <form string="Helpdesk Ticket">
                <header>
                    <button name="assign_ticket_to_self" string="Assign To Me" type="object" class="oe_highlight" groups="helpdesk.group_helpdesk_user" attrs="{'invisible': [('user_id', '!=', False)]}" data-hotkey="q"/>
                    <field name="stage_id" widget="statusbar"
                        options="{'clickable': '1', 'fold_field': 'fold'}"/>
                </header>
                <sheet>
                    <field name="legend_blocked" invisible="1"/>
                    <field name="legend_normal" invisible="1"/>
                    <field name="legend_done" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" type="object" name="action_open_helpdesk_ticket" icon="fa-life-ring" attrs="{'invisible': ['|', ('partner_id', '=', False), ('partner_ticket_count', '=', 0)]}">
                            <field name="partner_ticket_count" widget="statinfo" string="Tickets"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <field name="kanban_state" widget="state_selection"/>
                    <field name="use_sla" invisible="1"/>
                    <div class="oe_title">
                        <h1><field name="name" class="field_name" placeholder="Subject..."/></h1>
                        <field name="sla_status_ids" widget="many2many_tags" attrs="{'invisible': ['|', ('use_sla', '=', False), ('sla_status_ids', '=', [])]}" options="{'color_field': 'color', 'no_edit_color': True}" readonly="1" groups="helpdesk.group_use_sla"/>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="team_id" required="1"/>
                            <field name="user_id" class="field_user_id" domain="['&amp;', ('id', 'in', domain_user_ids), ('share', '=', False)]" widget="many2one_avatar_user"/>
                            <field name="domain_user_ids" invisible="1"/>
                            <field name="ticket_type_id"/>
                            <field name="priority" widget="priority"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                            <field name="sla_deadline" widget="remaining_days" attrs="{'invisible': ['|', ('sla_deadline', '=', False), ('use_sla', '=', False)]}" groups="helpdesk.group_use_sla"/>
                            <field name="company_id" groups="base.group_multi_company" context="{'create': False}"/>
                        </group>
                        <group>
                            <field name="partner_id" class="field_partner_id" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]" widget="res_partner_many2one" context="{'default_name': partner_name, 'default_email': partner_email, 'default_phone': partner_phone}"/>
                            <field name="partner_name" attrs="{'invisible': [('partner_id', '!=', False)]}"/>
                            <field name="partner_email" widget="email" string="Email"/>
                            <field name="partner_phone" widget="phone" string="Phone"/>
                            <field name="email_cc" groups="base.group_no_one"/>
                        </group>
                    </group>
                    <field name="description" class="oe-bordered-editor field_description" placeholder="Description of the ticket..."/>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids" options="{'post_refresh': 'recipients'}"/>
                </div>
            </form>
        </field>
    </record>

    <record id="helpdesk_ticket_action_main_my" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">list,kanban,form,activity</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="context">{'search_default_my_ticket': True, 'search_default_is_open': True, 'default_user_id': uid}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tickets found. Let's create one!
                </p><p>To get things done, use activities and statuses on tickets. <br/>
                Chat in real-time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_main_tree" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">list,kanban,form,activity</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="context">{'search_default_is_open': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tickets found. Let's create one!
                </p><p>To get things done, use activities and statuses on tickets. <br/>
                Chat in real-time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="action_upcoming_sla_fail_all_tickets" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">kanban,list,form,pivot,graph</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="context">{'search_default_sla_failed': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Congratulations! 
                </p><p>You completed all your tickets on time.
            </p>
        </field>
    </record>

    <!-- Action for dashboard button -->
    <record id="helpdesk_my_ticket_action_no_create" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">list,kanban,form,activity</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="context">{'create': False, 'search_default_is_open': True, 'search_default_my_ticket': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tickets found
                </p><p>To get things done, use activities and statuses on tickets.<br/>
                Chat in real-time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_sla" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">list,kanban,form,activity</field>
        <field name="domain">[]</field>
        <field name="context">{'create': False, 'search_default_is_open': True, 'search_default_my_ticket': True, 'search_default_sla_failed': True}</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Congratulations! 
                </p><p>You completed all your tickets on time.
            </p>
        </field>
    </record>

    <!-- Reporting action for dashboard -->
    <record id="helpdesk_ticket_action_close_analysis" model="ir.actions.act_window">
        <field name="name">Closed Tickets Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph</field>
        <field name="search_view_id" ref="helpdesk.helpdesk_ticket_report_analysis_view_search"/>
        <field name="domain" eval="[('close_date', '>=', (DateTime.today() - relativedelta(hours=12)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
        <field name="context">{
            'search_default_my_ticket': True,
            'pivot_measures': ['__count__'],
            'pivot_row_groupby': ['create_date:day'],
        }</field>
        <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'graph', 'view_id': ref('helpdesk.helpdesk_ticket_view_graph_analysis')}),
                          (0, 0, {'view_mode': 'pivot', 'view_id': ref('helpdesk.helpdesk_ticket_view_pivot_analysis')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet ! 
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_7days_analysis" model="ir.actions.act_window">
        <field name="name">Closed Tickets Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph</field>
        <field name="search_view_id" ref="helpdesk.helpdesk_ticket_report_analysis_view_search"/>
        <field name="domain" eval="[('close_date', '>=', (DateTime.today() - relativedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
        <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'graph', 'view_id': ref('helpdesk.helpdesk_ticket_view_graph_analysis')}),
                          (0, 0, {'view_mode': 'pivot', 'view_id': ref('helpdesk.helpdesk_ticket_view_pivot_analysis')})]"/>
        <field name="context">{
            'search_default_my_ticket': True,
            'search_default_is_close': True,
            'pivot_measures': ['__count__'],
            'pivot_row_groupby': ['create_date:day'],
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet ! 
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <!-- action for halpdask team -->
    <record id="helpdesk_ticket_action_team" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">kanban,list,form,activity</field>
        <field name="domain">[('team_id', '=', active_id)]</field>
        <field name="context">{'default_team_id': active_id}</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tickets found. Let's create one!
                </p><p>To get things done, use activities and statuses on tickets. <br/>
                Chat in real-time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_unassigned" model="ir.actions.act_window">
        <field name="name">Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">kanban,list,form,activity</field>
        <field name="context">{'search_default_team_id': active_id, 'search_default_unassigned': True}</field>
        <field name="search_view_id" ref="helpdesk_tickets_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tickets found. Let's create one!
                </p><p>To get things done, use activities and statuses on tickets. <br/>
                Chat in real-time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_view_search_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.search</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <search string="Tickets Search">
                <field name="name"/>
                <field name="ticket_type_id"/>
                <field name="priority" invisible="1"/>
                <field name="team_id"/>
                <field name="user_id"/>
                
                <filter string="My Tickets" domain="[('user_id','=',uid)]" name="my_ticket"/>
                <filter string="Unassigned Tickets" domain="[('user_id','=',False)]" name="unassigned"/>
                <separator/>
                <filter string="Open Tickets" domain="[('stage_id.is_close','=',False)]" name="is_open"/>
                <filter string="Closed Tickets" domain="[('stage_id.is_close','=',True)]" name="is_close"/>
                <separator/>
                <filter name="filter_create_date" date="create_date"/>
                <filter name="filter_sla_deadline" date="sla_deadline"/>
                <separator/>
                <filter string="SLA Failed" domain="[('sla_fail','!=',False)]" name="sla_failed" groups="helpdesk.group_use_sla"/>
                <filter string="SLA in Progress" domain="[('sla_fail','=',False)]" name="not_sla_failed" groups="helpdesk.group_use_sla"/>
                <filter string="SLA Success" name="sla_successed" domain="[('sla_success', '=', True)]" groups="helpdesk.group_use_sla"/>
                <separator/>
                <filter string="Archived" domain="[('active','=',False)]" name="archive"/>
                <group expand="0" string="Group By">
                  <filter string="Assignee" name="assignee" context="{'group_by':'user_id'}"/>
                  <filter string="Team" name="team" context="{'group_by':'team_id'}"/>
                  <filter string="Ticket Type" name="ticket_type_id" context="{'group_by':'ticket_type_id'}"/>
                  <filter string="Creation Date" context="{'group_by':'create_date:week'}" name="group_by_create_date"/>
                  <filter string="First Assignment Date" context="{'group_by': 'assign_date:month'}" name="group_by_assign_date"/>
                </group>
            </search>
        </field>
    </record>

    <menuitem id="helpdesk_ticket_report_menu_main" name="Reporting"
        sequence="20" parent="helpdesk.menu_helpdesk_root"
        groups="helpdesk.group_helpdesk_manager"/>

    <menuitem id="helpdesk_ticket_report_menu" name="Tickets Analysis" action="helpdesk.helpdesk_ticket_analysis_action"
        sequence="10" parent="helpdesk_ticket_report_menu_main"
        groups="helpdesk.group_helpdesk_manager"/>

    <!-- helpdesk team pivot View -->
    <record id="helpdesk_team_view_pivot_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.pivot</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <pivot string="Performance Analysis" sample="1">
                <field name="stage_id" type="col"/>
                <field name="name"/>
                <field name="close_hours" type="measure"/>
                <field name="color" invisible="1"/>
            </pivot>
        </field>
    </record>

    <record id="helpdesk_team_view_graph_analysis" model="ir.ui.view">
        <field name="name">helpdesk.ticket.graph</field>
        <field name="model">helpdesk.ticket</field>
        <field name="arch" type="xml">
            <graph string="Performance Analysis" sample="1">
                <field name="stage_id"/>
                <field name="team_id"/>
                <field name="close_hours" type="measure"/>
            </graph>
        </field>
    </record>

    <record id="helpdesk_ticket_action_team_performance" model="ir.actions.act_window">
        <field name="name">Performance Analysis</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">pivot,graph</field>
        <field name="search_view_id" ref="helpdesk_ticket_view_search_analysis"/>
        <field name="view_ids"
               eval="[(5, 0, 0),
                      (0, 0, {'view_mode': 'graph', 'view_id': ref('helpdesk_team_view_graph_analysis')}),
                      (0, 0, {'view_mode': 'pivot', 'view_id': ref('helpdesk_team_view_pivot_analysis')})]"/>
        <field name="search_view_id" ref="helpdesk_ticket_view_search_analysis"/>
        <field name="context">{'search_default_team_id': active_ids, 'pivot_measures': ['close_hours', '__count__']}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet ! 
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_success" model="ir.actions.act_window">
        <field name="name">Success Rate Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph</field>
        <field name="view_ids"
               eval="[(5, 0, 0),
                      (0, 0, {'view_mode': 'graph', 'view_id': ref('helpdesk.helpdesk_ticket_view_graph_analysis')}),
                      (0, 0, {'view_mode': 'pivot', 'view_id': ref('helpdesk.helpdesk_ticket_view_pivot_analysis')})]"/>
        <field name="domain" eval="[('close_date', '>=', (DateTime.today() - relativedelta(hours=12)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
        <field name="search_view_id" ref="helpdesk.helpdesk_ticket_report_analysis_view_search"/>
        <field name="context">{
            'search_default_is_close': True,
            'search_default_my_ticket': True,
            'search_default_not_sla_failed': True,
            'pivot_measures': ['__count__'],
            'pivot_row_groupby': ['create_date:day'],
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet ! 
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_7dayssuccess" model="ir.actions.act_window">
        <field name="name">Success Rate Analysis</field>
        <field name="res_model">helpdesk.ticket.report.analysis</field>
        <field name="view_mode">pivot,graph</field>
        <field name="view_ids"
               eval="[(5, 0, 0),
                      (0, 0, {'view_mode': 'graph', 'view_id': ref('helpdesk.helpdesk_ticket_view_graph_analysis')}),
                      (0, 0, {'view_mode': 'pivot', 'view_id': ref('helpdesk.helpdesk_ticket_view_pivot_analysis')})]"/>
        <field name="domain" eval="[('close_date', '>=', (DateTime.today() - relativedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S'))]"/>
        <field name="search_view_id" ref="helpdesk.helpdesk_ticket_report_analysis_view_search"/>
        <field name="context">{
            'search_default_is_close': True,
            'search_default_my_ticket': True,
            'search_default_not_sla_failed': True,
            'pivot_measures': ['__count__'],
            'pivot_row_groupby': ['create_date:day'],
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet! 
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <record id="helpdesk_ticket_action_dashboard" model="ir.actions.act_window">
        <field name="name">Ticket Analysis</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">pivot,graph</field>
        <field name="search_view_id" ref="helpdesk_ticket_view_search_analysis"/>
        <field name="domain">[('stage_id.is_close', '=', False)]</field>
        <field name="context">{'search_default_my_ticket': True, 'pivot_measures': ['close_hours', '__count__']}</field>
        <field name="view_ids"
               eval="[(5, 0, 0),
                      (0, 0, {'view_mode': 'graph', 'view_id': ref('helpdesk_team_view_graph_analysis')}),
                      (0, 0, {'view_mode': 'pivot', 'view_id': ref('helpdesk_team_view_pivot_analysis')})]"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No data yet! 
            </p><p>
                Create tickets to get statistics.
            </p>
        </field>
    </record>

    <menuitem id="helpdesk_ticket_menu_main" name="Tickets"
        sequence="10" parent="helpdesk.menu_helpdesk_root"/>

    <menuitem id="helpdesk_ticket_menu_my" name="My Tickets" action="helpdesk_ticket_action_main_my"
        sequence="10" parent="helpdesk.helpdesk_ticket_menu_main"/>

    <menuitem id="helpdesk_ticket_menu_all" name="All Tickets" action="helpdesk_ticket_action_main_tree"
        sequence="20" parent="helpdesk.helpdesk_ticket_menu_main"/>
</odoo>
