# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://www.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Inversare Notă Contabilă"

#. module: helpdesk_account
#: code:addons/helpdesk_account/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_account.field_helpdesk_ticket__invoice_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_account.helpdesk_ticket_view_form_inherit_helpdesk_invoicing
#, python-format
msgid "Credit Notes"
msgstr "Note credit"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_helpdesk_ticket__invoices_count
msgid "Credit Notes Count"
msgstr "Număr note credit"

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr "HelpDesk Bilet"

#. module: helpdesk_account
#: code:addons/helpdesk_account/wizard/account_move_reversal.py:0
#, python-format
msgid "Helpdesk Ticket #%s"
msgstr "Tichet helpdesk #%s"

#. module: helpdesk_account
#: model_terms:ir.ui.view,arch_db:helpdesk_account.view_account_move_reversal_inherit_helpdesk_account
msgid "Invoice to Refund"
msgstr ""

#. module: helpdesk_account
#: model:ir.model,name:helpdesk_account.model_account_move
msgid "Journal Entry"
msgstr "Notă contabilă"

#. module: helpdesk_account
#: model:ir.model.fields,help:helpdesk_account.field_account_move_reversal__helpdesk_sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""
"Referința comenzii de vânzare la care acest tichet se referă. Setarea "
"acestei informații are ca scop simplificarea procesului de After Sales și "
"servește doar scopuri informative."

#. module: helpdesk_account
#: model:ir.actions.act_window,name:helpdesk_account.helpdesk_ticket_action_refund
#: model_terms:ir.ui.view,arch_db:helpdesk_account.helpdesk_ticket_view_form_inherit_helpdesk_invoicing
msgid "Refund"
msgstr "Storare"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__helpdesk_sale_order_id
msgid "Sales Order"
msgstr "Comandă de vânzare"

#. module: helpdesk_account
#: model:ir.model.fields,field_description:helpdesk_account.field_account_move_reversal__suitable_move_ids
msgid "Suitable Move"
msgstr "Mișcare potrivită"
