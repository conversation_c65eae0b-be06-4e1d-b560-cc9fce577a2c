# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_fsm
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: helpdesk_fsm
#: model:project.task,legend_blocked:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_blocked:helpdesk_fsm.helpdesk_fsm_task_2
msgid "Blocked"
msgstr "Bloqueada"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__company_id
msgid "Company"
msgstr "Compañía"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Convert tickets into Field Service tasks"
msgstr "Convertir tickets en tareas del campo de servicio"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create & View Task"
msgstr "Crear y ver tarea"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create Task"
msgstr "Crear tarea"

#. module: helpdesk_fsm
#: code:addons/helpdesk_fsm/models/helpdesk.py:0
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_create_fsm_task
#, python-format
msgid "Create a Field Service task"
msgstr "Crear una tarea en el campo de servicio"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_date
msgid "Created on"
msgstr "Creado el"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Discard"
msgstr "Descartar"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_team__fsm_project_id
msgid "FSM Project"
msgstr "FSM del proyecto"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Field Service"
msgstr "Servicio externo"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_count
msgid "Fsm Task Count"
msgstr "Número de tareas del campo de servicio"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Equipo de servicio de asistencia"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket de servicio de asistencia"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_fsm
#: model:project.task,legend_normal:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_normal:helpdesk_fsm.helpdesk_fsm_task_2
msgid "In Progress"
msgstr "En proceso"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Plan Intervention"
msgstr "Planear intervención"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_team_view_form
msgid "Project"
msgstr "Proyecto"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
msgid "Project in which to create the task"
msgstr "Proyecto en el que se crea la tarea"

#. module: helpdesk_fsm
#: model:project.task,legend_done:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_done:helpdesk_fsm.helpdesk_fsm_task_2
msgid "Ready"
msgstr "Listo"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__helpdesk_ticket_id
msgid "Related ticket"
msgstr "Ticket relacionado"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_project_task
msgid "Task"
msgstr "Tarea"

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_done
msgid "Task Done"
msgstr "Tarea hecha"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Tasks"
msgstr "Tareas"

#. module: helpdesk_fsm
#: code:addons/helpdesk_fsm/models/helpdesk.py:0
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
#, python-format
msgid "Tasks from Tickets"
msgstr "Tareas desde Tickets"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
msgid "Tasks generated from this ticket"
msgstr "Tareas generadas a partir de este ticket"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_project_task__helpdesk_ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket from this task"
msgstr "Ticket de esta tarea"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_project_task__helpdesk_ticket_id
msgid "Ticket this task was generated from"
msgstr "Ticket a partir del cual se generó esta tarea"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Ticket's customer, will be linked to the task"
msgstr "El ticket del cliente, estará vinculado a la tarea"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Tickets"
msgstr "Tickets"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__name
msgid "Title"
msgstr "Título"
