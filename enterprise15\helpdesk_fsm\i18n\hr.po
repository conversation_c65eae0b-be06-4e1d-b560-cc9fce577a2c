# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_fsm
# 
# Translators:
# <PERSON><PERSON><PERSON> <stjepan.lo<PERSON><PERSON>@gmail.com>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <davor.b<PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: Croatian (https://www.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: helpdesk_fsm
#: model:project.task,legend_blocked:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_blocked:helpdesk_fsm.helpdesk_fsm_task_2
msgid "Blocked"
msgstr "Blokirano"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__company_id
msgid "Company"
msgstr "Tvrtka"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Convert tickets into Field Service tasks"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create & View Task"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create Task"
msgstr "Kreiraj zadatak"

#. module: helpdesk_fsm
#: code:addons/helpdesk_fsm/models/helpdesk.py:0
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_create_fsm_task
#, python-format
msgid "Create a Field Service task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Customer"
msgstr "Kupac"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Discard"
msgstr "Odbaci"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_team__fsm_project_id
msgid "FSM Project"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Field Service"
msgstr "Terenski servis"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_count
msgid "Fsm Task Count"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Tim podrške"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Prijava podrške"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_fsm
#: model:project.task,legend_normal:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_normal:helpdesk_fsm.helpdesk_fsm_task_2
msgid "In Progress"
msgstr "U tijeku"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Plan Intervention"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_team_view_form
msgid "Project"
msgstr "Projekt"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
msgid "Project in which to create the task"
msgstr ""

#. module: helpdesk_fsm
#: model:project.task,legend_done:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_done:helpdesk_fsm.helpdesk_fsm_task_2
msgid "Ready"
msgstr "Spremno"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__helpdesk_ticket_id
msgid "Related ticket"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_project_task
msgid "Task"
msgstr "Zadatak"

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_done
msgid "Task Done"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Tasks"
msgstr "Zadaci"

#. module: helpdesk_fsm
#: code:addons/helpdesk_fsm/models/helpdesk.py:0
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
#, python-format
msgid "Tasks from Tickets"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
msgid "Tasks generated from this ticket"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_project_task__helpdesk_ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket"
msgstr "Prijava"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket from this task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_project_task__helpdesk_ticket_id
msgid "Ticket this task was generated from"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Ticket's customer, will be linked to the task"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Tickets"
msgstr "Prijave"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__name
msgid "Title"
msgstr "Naslov"
