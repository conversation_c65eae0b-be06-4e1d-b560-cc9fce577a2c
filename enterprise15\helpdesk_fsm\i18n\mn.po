# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_fsm
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Uuganbayar Batbaatar <<EMAIL>>, 2021\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_fsm
#: model:project.task,legend_blocked:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_blocked:helpdesk_fsm.helpdesk_fsm_task_2
msgid "Blocked"
msgstr "Түгжигдсэн"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__company_id
msgid "Company"
msgstr "Компани"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Convert tickets into Field Service tasks"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create & View Task"
msgstr "Даалгавар үүсгэх болон нээх"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Create Task"
msgstr "Даалгавар үүсгэх"

#. module: helpdesk_fsm
#: code:addons/helpdesk_fsm/models/helpdesk.py:0
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_create_fsm_task
#, python-format
msgid "Create a Field Service task"
msgstr "Даалгаварын үйлчилгээний талбарыг үүсгэх"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Customer"
msgstr "Үйлчлүүлэгч"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.create_fsm_task_view_form
msgid "Discard"
msgstr "Үл хэрэгсэх"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_team__fsm_project_id
msgid "FSM Project"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__use_fsm
msgid "Field Service"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_count
msgid "Fsm Task Count"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Тусламж дэмжлэгийн баг"

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Тусламжийн төвийн тасалбар"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_fsm
#: model:project.task,legend_normal:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_normal:helpdesk_fsm.helpdesk_fsm_task_2
msgid "In Progress"
msgstr "Явагдаж буй"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Plan Intervention"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_team_view_form
msgid "Project"
msgstr "Төсөл"

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__project_id
msgid "Project in which to create the task"
msgstr ""

#. module: helpdesk_fsm
#: model:project.task,legend_done:helpdesk_fsm.helpdesk_fsm_task_1
#: model:project.task,legend_done:helpdesk_fsm.helpdesk_fsm_task_2
msgid "Ready"
msgstr "Бэлэн"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__helpdesk_ticket_id
msgid "Related ticket"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model,name:helpdesk_fsm.model_project_task
msgid "Task"
msgstr "Даалгавар"

#. module: helpdesk_fsm
#: model:mail.message.subtype,description:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,description:helpdesk_fsm.mt_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_team_ticket_task_done
#: model:mail.message.subtype,name:helpdesk_fsm.mt_ticket_task_done
msgid "Task Done"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.helpdesk_ticket_view_form
msgid "Tasks"
msgstr "Даалгаврууд"

#. module: helpdesk_fsm
#: code:addons/helpdesk_fsm/models/helpdesk.py:0
#: code:addons/helpdesk_fsm/wizard/create_task.py:0
#, python-format
msgid "Tasks from Tickets"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_ticket__fsm_task_ids
msgid "Tasks generated from this ticket"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_project_task__helpdesk_ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket"
msgstr "Тасалбар"

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Ticket from this task"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_project_task__helpdesk_ticket_id
msgid "Ticket this task was generated from"
msgstr ""

#. module: helpdesk_fsm
#: model:ir.model.fields,help:helpdesk_fsm.field_helpdesk_create_fsm_task__partner_id
msgid "Ticket's customer, will be linked to the task"
msgstr ""

#. module: helpdesk_fsm
#: model_terms:ir.ui.view,arch_db:helpdesk_fsm.project_sharing_inherit_project_task_view_form
msgid "Tickets"
msgstr "Тасалбарууд"

#. module: helpdesk_fsm
#: model:ir.model.fields,field_description:helpdesk_fsm.field_helpdesk_create_fsm_task__name
msgid "Title"
msgstr "Гарчиг"
