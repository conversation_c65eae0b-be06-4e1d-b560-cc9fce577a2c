# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_mail_plugin
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Closed"
msgstr "已关闭"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Could not create the ticket"
msgstr "无法创建工票"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Email already logged on the ticket"
msgstr "已经登录在工票上的电子邮件"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Log Email Into Ticket"
msgstr "将电子邮件记录到工票中"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Log the email on the ticket"
msgstr "将电子邮件记录在工票上"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "No tickets found for this contact."
msgstr "没有找到该联系人的工票。"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Save Contact to create new Tickets."
msgstr "保存联系人以创建新的工票。"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Save the contact to create new tickets."
msgstr "保存联系人以创建新工票。"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "The Contact needs to exist to create Ticket."
msgstr "需要存在联系人才能创建工单."

#. module: helpdesk_mail_plugin
#: model:ir.actions.act_window,name:helpdesk_mail_plugin.helpdesk_ticket_action_form_edit
msgid "Ticket: redirect to form in edit mode"
msgstr "工票：在编辑模式下重定向到表单"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Tickets"
msgstr "服务单"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_outlook.xml:0
#, python-format
msgid "Tickets (%(count)s)"
msgstr "工票 (%(count)s)"

#. module: helpdesk_mail_plugin
#. openerp-web
#: code:addons/helpdesk_mail_plugin/static/src/to_translate/translations_gmail.xml:0
#, python-format
msgid "Tickets (%s)"
msgstr "工票 (%s)"
