# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_repair
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: helpdesk_repair
#: model:helpdesk.ticket,legend_blocked:helpdesk_repair.helpdesk_repair_ticket_1
msgid "Blocked"
msgstr "Blokováno"

#. module: helpdesk_repair
#: model:ir.actions.act_window,name:helpdesk_repair.action_repair_order_form
msgid "Create a Repair Order"
msgstr "Vytvořit příkaz opravy"

#. module: helpdesk_repair
#: model:ir.model,name:helpdesk_repair.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Tým Helpdesku"

#. module: helpdesk_repair
#: model:ir.model,name:helpdesk_repair.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Helpdesk požadavek"

#. module: helpdesk_repair
#: model:helpdesk.ticket,legend_normal:helpdesk_repair.helpdesk_repair_ticket_1
msgid "In Progress"
msgstr "Probíhá"

#. module: helpdesk_repair
#: model:helpdesk.ticket,legend_done:helpdesk_repair.helpdesk_repair_ticket_1
msgid "Ready"
msgstr "Připraveno"

#. module: helpdesk_repair
#: model:ir.model.fields,help:helpdesk_repair.field_repair_order__ticket_id
msgid "Related Helpdesk Ticket"
msgstr "Související tiket na helpdesku"

#. module: helpdesk_repair
#: model_terms:ir.ui.view,arch_db:helpdesk_repair.helpdesk_ticket_view_form_inherit_stock_user
msgid "Repair"
msgstr "Oprava"

#. module: helpdesk_repair
#: model:ir.model,name:helpdesk_repair.model_repair_order
msgid "Repair Order"
msgstr "Příkaz opravy"

#. module: helpdesk_repair
#: code:addons/helpdesk_repair/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_repair.field_helpdesk_ticket__repair_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_repair.helpdesk_team_view_form_inherit_helpdesk_repair
#: model_terms:ir.ui.view,arch_db:helpdesk_repair.helpdesk_ticket_view_form_inherit_helpdesk_repair
#, python-format
msgid "Repairs"
msgstr "Opravy"

#. module: helpdesk_repair
#: model:ir.model.fields,field_description:helpdesk_repair.field_helpdesk_team__repairs_count
#: model:ir.model.fields,field_description:helpdesk_repair.field_helpdesk_ticket__repairs_count
msgid "Repairs Count"
msgstr "Počet oprav"

#. module: helpdesk_repair
#: model:ir.model.fields,field_description:helpdesk_repair.field_repair_order__ticket_id
msgid "Ticket"
msgstr "Požadavek"
