<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="helpdesk_ticket_view_form_inherit_helpdesk_invoicing" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.invoicing</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_form"/>
        <field name="arch" type="xml">
            <field name='email_cc' position="after">
                <field name="commercial_partner_id" invisible="1"/>
                <field name="sale_order_id" options='{"no_open": True}' readonly="1" invisible="1"/>
            </field>
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="options">{'always_reload': True}</attribute>
                <attribute name="context">{'res_partner_search_mode': 'customer',
                                            'default_name': partner_name,
                                            'default_email': partner_email,
                                            'default_phone': partner_phone}</attribute>
            </xpath>
        </field>
    </record>

    <record id="quick_create_ticket_form" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.quick_create</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.quick_create_ticket_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="options">{'always_reload': True}</attribute>
                <attribute name="context">{'res_partner_search_mode': 'customer'}</attribute>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_form_inherit_sale_user" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.invoicing</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_ticket_view_form_inherit_helpdesk_invoicing"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='sale_order_id']" position="attributes">
                <attribute name="options">{"no_create": True}</attribute>
                <attribute name="readonly">0</attribute>
            </xpath>
        </field>
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="helpdesk_ticket_view_search_inherit_helpdesk_sale" model="ir.ui.view">
        <field name="name">helpdesk.ticket.search.inherit.sale</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_search"/>
        <field name="arch" type="xml">
            <field name="stage_id" position="after">
                <field name="sale_order_id"/>
            </field>
        </field>
    </record>

    <record id="helpdesk_tickets_view_tree_inherit_helpdesk_sale" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.sale</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="options">{'always_reload': True}</attribute>
                <attribute name="context">{'res_partner_search_mode': 'customer'}</attribute>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_sla_view_form_inherit_helpdesk_sale" model="ir.ui.view">
        <field name="name">helpdesk.sla.view.form.inherit.helpdesk.sale</field>
        <field name="model">helpdesk.sla</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_sla_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_ids']" position="attributes">
                <attribute name="context">{'res_partner_search_mode': 'customer'}</attribute>
            </xpath>
        </field>
    </record>
</odoo>
