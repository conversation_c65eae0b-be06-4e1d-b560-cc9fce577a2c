# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_coupon
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-13 08:45+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__company_id
msgid "Company"
msgstr "Empresa"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
msgid "Coupon"
msgstr "Cupón"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__program
msgid "Coupon Program"
msgstr "Programa de cupones"

#. module: helpdesk_sale_coupon
#: code:addons/helpdesk_sale_coupon/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
#, python-format
msgid "Coupons"
msgstr "Cupones"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_ticket__coupons_count
msgid "Coupons Count"
msgstr "Número de cupones"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__create_date
msgid "Created on"
msgstr "Creado el"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Generate"
msgstr "Generar"

#. module: helpdesk_sale_coupon
#: model:ir.model,name:helpdesk_sale_coupon.model_helpdesk_sale_coupon_generate
msgid "Generate Sales Coupon from Helpdesk"
msgstr "Generar cupón de ventas desde el soporte al cliente"

#. module: helpdesk_sale_coupon
#: model:ir.actions.act_window,name:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_action
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Generate a Coupon"
msgstr "Generar un cupón"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_ticket__coupon_ids
msgid "Generated Coupons"
msgstr "Cupones generados"

#. module: helpdesk_sale_coupon
#: model:ir.model,name:helpdesk_sale_coupon.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket de soporte al cliente"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__ticket_id
msgid "Ticket"
msgstr "Ticket"
