# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_coupon
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <tadeusz<PERSON><EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-13 08:45+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Cancel"
msgstr "Anuluj"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__company_id
msgid "Company"
msgstr "Firma"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
msgid "Coupon"
msgstr "Kupon"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__program
msgid "Coupon Program"
msgstr "Program kuponowy"

#. module: helpdesk_sale_coupon
#: code:addons/helpdesk_sale_coupon/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
#, python-format
msgid "Coupons"
msgstr "Kupony"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_ticket__coupons_count
msgid "Coupons Count"
msgstr ""

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Generate"
msgstr "Generuj"

#. module: helpdesk_sale_coupon
#: model:ir.model,name:helpdesk_sale_coupon.model_helpdesk_sale_coupon_generate
msgid "Generate Sales Coupon from Helpdesk"
msgstr "Wygeneruj kupon sprzedaży z helpdesku"

#. module: helpdesk_sale_coupon
#: model:ir.actions.act_window,name:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_action
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Generate a Coupon"
msgstr "Generuj kupon"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_ticket__coupon_ids
msgid "Generated Coupons"
msgstr "Wygenerowane kupony"

#. module: helpdesk_sale_coupon
#: model:ir.model,name:helpdesk_sale_coupon.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Zgłoszenia Punktu Pomocy"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__ticket_id
msgid "Ticket"
msgstr "Zgłoszenie"
