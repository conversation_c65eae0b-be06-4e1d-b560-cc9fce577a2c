# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_coupon
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-13 08:45+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Swedish (https://www.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Cancel"
msgstr "Avbryt"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__company_id
msgid "Company"
msgstr "Företag"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
msgid "Coupon"
msgstr "Kupong"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__program
msgid "Coupon Program"
msgstr "Kupong Program"

#. module: helpdesk_sale_coupon
#: code:addons/helpdesk_sale_coupon/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
#, python-format
msgid "Coupons"
msgstr "Kuponger"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_ticket__coupons_count
msgid "Coupons Count"
msgstr "Kupong Antal"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: helpdesk_sale_coupon
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Generate"
msgstr "Skapa"

#. module: helpdesk_sale_coupon
#: model:ir.model,name:helpdesk_sale_coupon.model_helpdesk_sale_coupon_generate
msgid "Generate Sales Coupon from Helpdesk"
msgstr "Skapa en Försäljningskupong från Kundtjänst"

#. module: helpdesk_sale_coupon
#: model:ir.actions.act_window,name:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_action
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_coupon.helpdesk_sale_coupon_generate_view_form
msgid "Generate a Coupon"
msgstr "Skapa en Kupong"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_ticket__coupon_ids
msgid "Generated Coupons"
msgstr "Genererade Kuponger"

#. module: helpdesk_sale_coupon
#: model:ir.model,name:helpdesk_sale_coupon.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Kundtjänstärende"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate____last_update
msgid "Last Modified on"
msgstr "Senast redigerad"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: helpdesk_sale_coupon
#: model:ir.model.fields,field_description:helpdesk_sale_coupon.field_helpdesk_sale_coupon_generate__ticket_id
msgid "Ticket"
msgstr "Ärende"
