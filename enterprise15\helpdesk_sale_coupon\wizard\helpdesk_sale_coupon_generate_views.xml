<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="helpdesk_sale_coupon_generate_view_form" model="ir.ui.view">
            <field name="name">helpdesk.sale.coupon.generate.form</field>
            <field name="model">helpdesk.sale.coupon.generate</field>
            <field name="arch" type="xml">
                <form string="Generate a Coupon">
                    <group>
                        <field name="ticket_id" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="program" required="1" options="{'no_create': True}" domain="[('program_type', '=', 'coupon_program'), '|', ('company_id', '=', False), ('company_id', '=', company_id)]"/>
                    </group>
                    <footer>
                        <button name="generate_coupon" type="object" string="Generate" class="oe_highlight" data-hotkey="q"/>
                        <button special="cancel" data-hotkey="z" string="Cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="helpdesk_sale_coupon_generate_action" model="ir.actions.act_window">
            <field name="name">Generate a Coupon</field>
            <field name="res_model">helpdesk.sale.coupon.generate</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="helpdesk_sale_coupon_generate_view_form"/>
        </record>
    </data>
</odoo>
