# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>le Collart <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Cécile Collart <<EMAIL>>, 2022\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Ligne analytique"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Date"
msgstr "Date"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Days:"
msgstr "Jours : "

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Description"
msgstr "Description"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Duration (Days)"
msgstr "Durée (Jours)"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Duration (Hours)"
msgstr "Durée (Heures)"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Employee"
msgstr "Employé"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "Stratégie SLA Assistance"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Équipe d'assistance"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket d'Assistance"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Hours:"
msgstr "Heures : "

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Modèle d'article"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid "Project"
msgstr "Projet"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid ""
"Project to which the tickets (and the timesheets) will be linked by default."
msgstr ""
"Projet auquel les tickets (et les feuilles de temps) seront liés par défaut."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__project_sale_order_id
msgid "Project's sale order"
msgstr "Bon de commande du projet"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid "Ref. Sales Order"
msgstr "Ref. Bon de Commande"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""
"Référence du bon de commande auquel ce ticket fait référence. Configurer "
"cette information a pour but de faciliter votre procédure d'après-vente et "
"ne sert qu'à des fins d'indication."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoice the time spent on ticket through tasks."
msgstr "Refacturer le temps passé sur des tickets via des tâches"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoicing Timesheet activated on Team"
msgstr "Refacturation du Temps activé sur l'Equipe"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet_editable
msgid "Remaining Days on SO"
msgstr "Jours restants sur le bon de commande"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Heures restantes disponibles"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_so
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet_editable
msgid "Remaining Hours on SO"
msgstr "Heures restantes sur le bon de commande"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_product_product__sla_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_product_template__sla_id
msgid "SLA Policy"
msgstr "Stratégie SLA"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Sales Order"
msgstr "Commande"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
msgid "Sales Order Item"
msgstr "Ligne de vente"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__sale_line_ids
msgid "Sales Order Items"
msgstr "Lignes de vente"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Bon de commande auquel le projet est lié."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_sla__sale_line_ids
msgid ""
"This SLA Policy will apply to any tickets with the selected Sales Order Item"
" as reference. Leave empty to apply this SLA Policy to any ticket without "
"distinction."
msgstr ""
"Cette stratégie SLA sera appliquée à n'importe quel ticket faisant référence"
" à cette ligne de vente. Laissez vide pour appliquer cette stratégie SLA à "
"n'importe quel ticket sans distinction."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order__ticket_count
msgid "Ticket Count"
msgstr "Nombre de tickets"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.sale_order_form_inherit_helpdesk_sale
msgid "Tickets"
msgstr "Tickets"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Timesheets"
msgstr "Feuilles de temps"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Total"
msgstr "Total"
