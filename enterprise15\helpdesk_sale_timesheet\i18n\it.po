# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Riga analitica"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Date"
msgstr "Data"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Days:"
msgstr "Giorni:"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Description"
msgstr "Descrizione"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Duration (Days)"
msgstr "Durata (giorni)"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Duration (Hours)"
msgstr "Durata (ore)"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Employee"
msgstr "Dipendente"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "Politiche SLA assistenza clienti"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Team assistenza clienti"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket assistenza clienti"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Hours:"
msgstr "Ore:"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Modello prodotto"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid "Project"
msgstr "Progetto"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_team__project_id
msgid ""
"Project to which the tickets (and the timesheets) will be linked by default."
msgstr ""
"Progetto a cui i ticket (e i fogli di lavoro) saranno collegati per default."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__project_sale_order_id
msgid "Project's sale order"
msgstr "Ordine di vendita progetto"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid "Ref. Sales Order"
msgstr "Rif. ordine di vendita"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""
"Riferimento all'ordine di vendita per questo ticket. Impostare questa "
"informazione mira a semplificare il processo post vendita ed è a solo scopo "
"indicativo."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoice the time spent on ticket through tasks."
msgstr "Rifattura il tempo impiegato nel ticket utilizzando i lavori."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__use_helpdesk_sale_timesheet
msgid "Reinvoicing Timesheet activated on Team"
msgstr "Rifatturazione foglio ore attivato sul team"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet_editable
msgid "Remaining Days on SO"
msgstr "Giorni rimanenti sull'ordine di vendita"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Ore rimanenti disponibili"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__remaining_hours_so
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet_editable
msgid "Remaining Hours on SO"
msgstr "Ore rimanenti sull'ordine di vendita"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_product_product__sla_id
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_product_template__sla_id
msgid "SLA Policy"
msgstr "Politica SLA"

#. module: helpdesk_sale_timesheet
#: model:ir.model,name:helpdesk_sale_timesheet.model_sale_order
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_ticket__sale_line_id
msgid "Sales Order Item"
msgstr "Voce ordine di vendita"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_helpdesk_sla__sale_line_ids
msgid "Sales Order Items"
msgstr "Voci ordine di vendita"

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_ticket__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Ordine di vendita al quale è collegato il progetto."

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,help:helpdesk_sale_timesheet.field_helpdesk_sla__sale_line_ids
msgid ""
"This SLA Policy will apply to any tickets with the selected Sales Order Item"
" as reference. Leave empty to apply this SLA Policy to any ticket without "
"distinction."
msgstr ""

#. module: helpdesk_sale_timesheet
#: model:ir.model.fields,field_description:helpdesk_sale_timesheet.field_sale_order__ticket_count
msgid "Ticket Count"
msgstr "Conteggio Ticket"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.sale_order_form_inherit_helpdesk_sale
msgid "Tickets"
msgstr "Ticket"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Timesheets"
msgstr "Fogli ore"

#. module: helpdesk_sale_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_timesheet.tickets_followup_timesheet
msgid "Total"
msgstr "Totale"
