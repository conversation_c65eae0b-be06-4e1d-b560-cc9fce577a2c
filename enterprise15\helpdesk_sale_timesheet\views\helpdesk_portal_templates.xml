<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="0">
    <template id="tickets_followup_timesheet" name="Helpdesk tickets timesheet" inherit_id="helpdesk.tickets_followup" priority="50">
        <xpath expr="//div[@name='description']" position="after">
            <t t-set='timesheet_lines' t-value='ticket.timesheet_ids._get_portal_helpdesk_timesheet()'/>
            <t t-set='is_uom_day' t-value='ticket.timesheet_ids._is_timesheet_encode_uom_day()'/>
            <section id="details" style="page-break-inside: auto;" class="mt32" t-if="ticket.use_helpdesk_timesheet and ticket.use_helpdesk_sale_timesheet and timesheet_lines and ticket.project_id.privacy_visibility == 'portal'">
                <h3 id="details">Timesheets</h3>

                <table class="table table-sm" id="timesheet_table">
                    <thead class="bg-100">
                        <tr>
                            <th class="text-left">Date</th>
                            <th class="text-left">Description</th>
                            <th class="text-left">Employee</th>
                            <th t-if="is_uom_day" class="text-right">Duration (Days)</th>
                            <th t-else="" class="text-right">Duration (Hours)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="timesheet_lines" t-as="line">
                            <tr>
                                <td><span t-field="line.date"/></td>
                                <td><span t-field="line.name"/></td>
                                <td><span t-field="line.employee_id"/></td>
                                <td class="text-right">
                                    <span t-if="is_uom_day" t-esc="line._get_timesheet_time_day()" t-options='{"widget": "timesheet_uom"}'/>
                                    <span t-else="" t-field="line.unit_amount" t-options='{"widget": "float_time"}'/>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3"></th>
                            <th class="text-right">
                                Total
                                <t t-set="total_timesheet_amount" t-value="sum(timesheet_lines.mapped('unit_amount'))"/>
                                <t t-if="is_uom_day"> Days:
                                    <span t-esc="timesheet_lines._convert_hours_to_days(total_timesheet_amount)" t-options='{"widget": "timesheet_uom"}'/>
                                </t>
                                <t t-else=""> Hours:
                                    <span t-esc="round(total_timesheet_amount, 2)" t-options='{"widget": "float_time"}'/>
                                </t>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </section>
        </xpath>
    </template>

</data>
</odoo>
