<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="helpdesk_team_view_form_inherit" model="ir.ui.view">
        <field name="name">helpdesk.team.form.inherit.timesheet</field>
        <field name="model">helpdesk.team</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_team_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='helpdesk_sale_timesheet']" position='attributes'>
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='project_id']" position='attributes'>
                <attribute name="context">{'default_allow_timesheets': 1, 'default_allow_billable': use_helpdesk_sale_timesheet}</attribute>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.sale.timesheet</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='timesheet_ids']" position="attributes">
                <attribute name="widget">so_line_one2many</attribute>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/tree" position="attributes">
                <attribute name="decoration-muted">timesheet_invoice_id != False</attribute>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/tree" position="inside">
                <field name="timesheet_invoice_id" invisible="1"/>
                <field name="is_so_line_edited" invisible="1"/>
                <field name="helpdesk_ticket_id" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/tree/field[@name='unit_amount']" position="before">
                <field name="so_line"
                    attrs="{'column_invisible': ['|', ('parent.use_helpdesk_sale_timesheet', '=', False), ('parent.partner_id', '=', False)]}"
                    domain="[('is_service', '=', True), ('order_partner_id', 'child_of', parent.commercial_partner_id), ('is_expense', '=', False), ('state', 'in', ['sale', 'done'])]"
                    optional="hide" options="{'no_create': True, 'no_open': True}"/>
            </xpath>
            <xpath expr="//field[@name='project_id']" position="before">
                <field name="use_helpdesk_sale_timesheet" invisible="1"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <button class="d-none d-md-inline oe_stat_button"
                        type="object" name="action_view_so" icon="fa-dollar"
                        attrs="{'invisible': ['|', ('use_helpdesk_sale_timesheet', '=', False), '&amp;', ('sale_order_id', '=', False), ('sale_line_id', '=', False)]}"
                        string="Sales Order"
                        groups="sales_team.group_sale_salesman"/>
            </xpath>
            <xpath expr="//field[@name='project_id']" position="after">
                <field name="sale_line_id" attrs="{'invisible': ['|', ('use_helpdesk_sale_timesheet', '=', False), ('partner_id', '=', False)]}" options="{'no_create': True, 'no_open': True}" context="{'create': False}"/>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet_editable" model="ir.ui.view">
        <field name="name">helpdesk.ticket.form.inherit.sale.timesheet.editable</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk_ticket_view_form_inherit_helpdesk_sale_timesheet"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='sale_line_id']" position="attributes">
                <attribute name="context">{'with_remaining_hours': True, 'with_price_unit': True, 'create': False}</attribute>
                <attribute name="options">{"no_create": True}</attribute>
            </xpath>
            <xpath expr="//field[@name='total_hours_spent']" position="after">
                <field name="remaining_hours_available" invisible="1"/>
                <span id="remaining_hours_so_label" attrs="{'invisible': ['|', '|', '|', '|', ('sale_order_id', '=', False), ('use_helpdesk_sale_timesheet', '=', False), ('partner_id', '=', False), ('sale_line_id', '=', False), ('remaining_hours_available', '=', False)]}">
                    <label class="font-weight-bold" for="remaining_hours_so" string="Remaining Hours on SO"
                           attrs="{'invisible': ['|', ('encode_uom_in_days', '=', True), ('remaining_hours_so', '&lt;', 0)]}"/>
                    <label class="font-weight-bold" for="remaining_hours_so" string="Remaining Days on SO"
                           attrs="{'invisible': ['|', ('encode_uom_in_days', '=', False), ('remaining_hours_so', '&lt;', 0)]}"/>
                    <label class="font-weight-bold text-danger" for="remaining_hours_so" string="Remaining Hours on SO"
                           attrs="{'invisible': ['|', ('encode_uom_in_days', '=', True), ('remaining_hours_so', '&gt;=', 0)]}"/>
                    <label class="font-weight-bold text-danger" for="remaining_hours_so" string="Remaining Days on SO"
                           attrs="{'invisible': ['|', ('encode_uom_in_days', '=', False), ('remaining_hours_so', '&gt;=', 0)]}"/>
                </span>
                <field name="remaining_hours_so" nolabel="1" widget="timesheet_uom" attrs="{'invisible': ['|', '|', '|', '|', ('sale_order_id', '=', False), ('use_helpdesk_sale_timesheet', '=', False), ('partner_id', '=', False), ('sale_line_id', '=', False), ('remaining_hours_available', '=', False)]}"/>
            </xpath>
            <xpath expr="//field[@name='timesheet_ids']/tree/field[@name='so_line']" position="attributes">
                <attribute name="context">{'with_remaining_hours': True, 'with_price_unit': True}</attribute>
                <attribute name="options">{"no_create": True}</attribute>
            </xpath>
        </field>
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="helpdesk_sla_view_form_inherit_sale_timesheet" model="ir.ui.view">
        <field name='name'>helpdesk.sla.form.inherit.sale.timesheet</field>
        <field name="model">helpdesk.sla</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_sla_view_form"/>
        <field name="arch" type="xml">
            <field name='partner_ids' position="after">
                <field name="sale_line_ids" widget="many2many_tags" options="{'no_create_edit': True, 'no_create': True}" groups="sales_team.group_sale_salesman"/>
            </field>
        </field>
    </record>

    <record id="helpdesk_ticket_view_tree_inherit_sale_timesheet" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.tree.inherit.sale.timesheet</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">90</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_tree"/>
        <field name="arch" type="xml">
            <field name='partner_id' position="after">
                <field name="sale_line_id" readonly="1" optional="hide"/>
            </field>
        </field>
    </record>

    <record id="helpdesk_sla_view_tree_inherit_sale_timesheet" model="ir.ui.view">
        <field name="name">helpdesk.sla.tree.inherit.sale.timesheet</field>
        <field name="model">helpdesk.sla</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_sla_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_ids']" position="after">
                <field name="sale_line_ids" widget="many2many_tags" optional="hide"
                    options="{'no_create_edit': True, 'no_create': True}" groups="sales_team.group_sale_salesman"/>
            </xpath>
        </field>
    </record>

    <record id="helpdesk_sla_view_search_inherit_sale_timesheet" model="ir.ui.view">
        <field name='name'>helpdesk.sla.search.inherit.sale.timesheet</field>
        <field name="model">helpdesk.sla</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_sla_view_search"/>
        <field name="arch" type="xml">
            <field name='partner_ids' position="after">
                <field name="sale_line_ids" groups="sales_team.group_sale_salesman"/>
            </field>
        </field>
    </record>
</odoo>
