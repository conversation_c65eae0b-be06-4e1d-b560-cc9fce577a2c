# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_stock
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# Noma <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Noma <PERSON>, 2022\n"
"Language-Team: Japanese (https://www.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__partner_id
msgid "Customer"
msgstr "顧客"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.view_stock_return_picking_form_inherit_helpdesk_stock
msgid "Delivery to Return"
msgstr "返送"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "倉庫に置いておける商品を追跡できるようにする"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "ヘルプデスクチケット"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial Number"
msgstr "ロット/シリアル番号"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial number concerned by the ticket"
msgstr "チケットに関わるロット/シリアル番号"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__picking_id
msgid "Picking"
msgstr "ピッキング"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_sla_report_analysis__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket_report_analysis__product_id
msgid "Product"
msgstr "プロダクト"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__product_id
msgid "Product concerned by the ticket"
msgstr "チケットに関わる製品"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""
"このチケットが参照する販売注文を参照します。この情報を設定することで、アフターセールスプロセスが容易になり、指標作成系の目的のみに使われます。"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_stock_user
msgid "Return"
msgstr "返却"

#. module: helpdesk_stock
#: code:addons/helpdesk_stock/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__picking_ids
#, python-format
msgid "Return Orders"
msgstr "返品要求"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__pickings_count
msgid "Return Orders Count"
msgstr "返品要求数"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "ピッキングの戻し"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_helpdesk_stock
msgid "Returns"
msgstr "返品"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "SLAステータス分析"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid "Sales Order"
msgstr "販売オーダ"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__suitable_picking_ids
msgid "Suitable Picking"
msgstr "適切なピッキング"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__ticket_id
msgid "Ticket"
msgstr "チケット"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "チケット分析"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Tracking"
msgstr "追跡"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_picking
msgid "Transfer"
msgstr "運送"
