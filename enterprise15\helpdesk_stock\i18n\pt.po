# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.view_stock_return_picking_form_inherit_helpdesk_stock
msgid "Delivery to Return"
msgstr "Entrega para Devolução"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Garante o rastreamento de um artigo armazenável no teu armazém."

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket de Apoio ao Cliente"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial Number"
msgstr "Lote/Número de Série"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial number concerned by the ticket"
msgstr ""

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__picking_id
msgid "Picking"
msgstr "Operação de Stock"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_sla_report_analysis__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket_report_analysis__product_id
msgid "Product"
msgstr "Artigo"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__product_id
msgid "Product concerned by the ticket"
msgstr ""

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_stock_user
msgid "Return"
msgstr "Devolução"

#. module: helpdesk_stock
#: code:addons/helpdesk_stock/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__picking_ids
#, python-format
msgid "Return Orders"
msgstr "Ordens de Devolução"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__pickings_count
msgid "Return Orders Count"
msgstr "Contagem de Ordens de Devolução"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Operação de Devolução de Stock"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_helpdesk_stock
msgid "Returns"
msgstr "Devoluções"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "Análise de Estados de SLA"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid "Sales Order"
msgstr "Ordem de Vendas"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__suitable_picking_ids
msgid "Suitable Picking"
msgstr "Picking Apropriado"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__ticket_id
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Análise de Tickets"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Tracking"
msgstr "Rastrear"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_picking
msgid "Transfer"
msgstr "Transferência"
