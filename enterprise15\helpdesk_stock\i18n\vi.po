# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Vo Than<PERSON> Thuy, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://www.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__partner_id
msgid "Customer"
msgstr "Khách hàng"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.view_stock_return_picking_form_inherit_helpdesk_stock
msgid "Delivery to Return"
msgstr "Giao hàng cần trả lại"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Đảm bảo tính truy xuất một sản phẩm có thể lưu kho trong nhà kho. "

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Vé hỗ trợ"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial Number"
msgstr "Số lô/sê-ri"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial number concerned by the ticket"
msgstr "Số lô/sê-ri liên quan tới vé"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__picking_id
msgid "Picking"
msgstr "Lấy hàng"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_sla_report_analysis__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket_report_analysis__product_id
msgid "Product"
msgstr "Sản phẩm"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__product_id
msgid "Product concerned by the ticket"
msgstr "Sản phẩm liên quan tới vé"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""
"Mã tham chiếu của đơn hàng liên quan tới vé này. Đặt thông tin này nhằm giúp"
" quá trình hậu mãi dễ dàng hơn và chỉ phục vụ mục đích chỉ định."

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_stock_user
msgid "Return"
msgstr "Trả hàng"

#. module: helpdesk_stock
#: code:addons/helpdesk_stock/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__picking_ids
#, python-format
msgid "Return Orders"
msgstr "Đơn trả hàng"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__pickings_count
msgid "Return Orders Count"
msgstr "Số đơn trả hàng"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Lấy hàng trả lại"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_helpdesk_stock
msgid "Returns"
msgstr "Trả hàng"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "Phân tích trạng thái SLA"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid "Sales Order"
msgstr "Đơn bán hàng"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__suitable_picking_ids
msgid "Suitable Picking"
msgstr "Lấy hàng phù hợp"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__ticket_id
msgid "Ticket"
msgstr "Vé"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Phân tích vé"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Tracking"
msgstr "Theo dõi"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_picking
msgid "Transfer"
msgstr "Chuyển"
