# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_stock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__partner_id
msgid "Customer"
msgstr "客戶"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.view_stock_return_picking_form_inherit_helpdesk_stock
msgid "Delivery to Return"
msgstr "送貨退貨"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "確保倉庫內可儲存產品的可追蹤性。"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "技術支援請求"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial Number"
msgstr "批次/序列號碼"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial number concerned by the ticket"
msgstr ""

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__picking_id
msgid "Picking"
msgstr "揀貨"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_sla_report_analysis__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket_report_analysis__product_id
msgid "Product"
msgstr "商品"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__product_id
msgid "Product concerned by the ticket"
msgstr ""

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid ""
"Reference of the Sales Order to which this ticket refers. Setting this "
"information aims at easing your After Sales process and only serves "
"indicative purposes."
msgstr ""

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_stock_user
msgid "Return"
msgstr "退回"

#. module: helpdesk_stock
#: code:addons/helpdesk_stock/models/helpdesk.py:0
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__picking_ids
#, python-format
msgid "Return Orders"
msgstr "退貨單"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__pickings_count
msgid "Return Orders Count"
msgstr "退貨單數目"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "退回揀貨"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_helpdesk_stock
msgid "Returns"
msgstr "退回"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "SLA狀態分析"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid "Sales Order"
msgstr "銷售訂單"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__suitable_picking_ids
msgid "Suitable Picking"
msgstr "合適的揀貨"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__ticket_id
msgid "Ticket"
msgstr "門票"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "支援請求分析"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Tracking"
msgstr "追蹤"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_picking
msgid "Transfer"
msgstr "調撥"
