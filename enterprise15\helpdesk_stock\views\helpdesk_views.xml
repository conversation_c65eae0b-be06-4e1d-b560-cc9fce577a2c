<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="helpdesk_ticket_view_form_inherit_helpdesk_stock" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.stock</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_form"/>
        <field name="arch" type="xml">
            <div class="oe_button_box" position="inside">
                <button class="oe_stat_button" name="action_view_pickings" icon="fa-truck"
                    type="object" attrs="{'invisible': [('pickings_count', '=', 0)]}">
                    <field name="pickings_count" string="Returns" widget="statinfo" />
                </button>
            </div>
            <field name='sale_order_id' position="after">
                <field name="use_credit_notes" invisible="1"/>
                <field name="use_product_returns" invisible="1"/>
                <field name="use_product_repairs" invisible="1"/>
                <field name="product_id"
                    attrs="{'invisible': [('use_credit_notes', '=', False), ('use_product_returns', '=', False), ('use_product_repairs', '=', False)]}"/>
                <field name="tracking" invisible="1"/>
                <field name="lot_id" groups="stock.group_production_lot" context="{'default_product_id': product_id, 'default_company_id': company_id}"
                    attrs="{'invisible': ['|', ('tracking', 'in', ['none', False]), '&amp;', '&amp;', ('use_credit_notes', '=', False), ('use_product_returns', '=', False), ('use_product_repairs', '=', False)]}" />
            </field>
        </field>
    </record>

    <record id="helpdesk_ticket_view_form_inherit_stock_user" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.form.inherit.return.stock.user</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">45</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_form"/>
        <field name="arch" type="xml">
            <field name="stage_id" position="before">
                <field name="use_product_returns" invisible="1"/>
                <button type="action" name="%(stock.act_stock_return_picking)d"
                    groups="stock.group_stock_user"
                    string="Return" attrs="{'invisible': [('use_product_returns', '=', False)]}"
                    context="{'default_ticket_id': id, 'default_company_id': company_id}"/>
            </field>
        </field>
        <field name="groups_id" eval="[(4, ref('stock.group_stock_user'))]"/>
    </record>

    <record id="helpdesk_team_view_form_inherit_helpdesk_account" model="ir.ui.view">
        <field name='name'>helpdesk.team.form.inherit.helpdesk.account</field>
        <field name="model">helpdesk.team</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_team_view_form"/>
        <field name="arch" type="xml">
            <div id="use_product_returns" position="replace"/>
        </field>
    </record>

    <record id="helpdesk_ticket_view_search_inherit_helpdesk_stock" model="ir.ui.view">
        <field name="name">helpdesk.ticket.search.inherit.stock</field>
        <field name="model">helpdesk.ticket</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_search"/>
        <field name="arch" type="xml">
            <field name="sla_ids" position="before">
                <field name="product_id"/>
            </field>
        </field>
    </record>

    <record id="helpdesk_ticket_view_tree_inherit_helpdesk_stock" model="ir.ui.view">
        <field name='name'>helpdesk.ticket.tree.inherit.stock</field>
        <field name="model">helpdesk.ticket</field>
        <field name="priority">40</field>
        <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_tree"/>
        <field name="arch" type="xml">
            <field name='partner_id' position="after">
                <field name="product_id" optional="hide"/>
            </field>
        </field>
    </record>
</odoo>
