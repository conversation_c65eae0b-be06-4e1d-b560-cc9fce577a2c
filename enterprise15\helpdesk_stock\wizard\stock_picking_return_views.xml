<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_stock_return_picking_form_inherit_helpdesk_stock" model="ir.ui.view">
            <field name="name">Return</field>
            <field name="model">stock.return.picking</field>
            <field name="inherit_id" ref="stock.view_stock_return_picking_form"/>
            <field name="arch" type="xml">
                <group position="before">
                    <field name="partner_id" invisible="1"/>
                    <field name="suitable_picking_ids" invisible="1"/>
                    <field name="ticket_id" invisible="1"/>
                    <group attrs="{'invisible': [('ticket_id', '=', False)]}">
                        <field name="sale_order_id" readonly="1" attrs="{'invisible': [('sale_order_id', '=', False)]}"/>
                        <field name="picking_id" options="{'no_create': True}" string="Delivery to Return"
                            attrs="{'required': [('ticket_id', '!=', False)]}"/>
                    </group>
                </group>
            </field>
        </record>
    </data>
</odoo>
