# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_count
msgid "# Tickets"
msgstr "Nbr de tickets"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_team_view_form_inherit_helpdesk_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Enregistré</span>"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/analytic.py:0
#, python-format
msgid "A timesheet cannot be linked to a task and a ticket at the same time."
msgstr ""
"Une feuille de temps ne peut pas être liée à une tâche et à un ticket en "
"même temps."

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Ligne analytique"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Closed"
msgstr "Fermé"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Confirm Time Spent"
msgstr "Confirmer le Temps Passé"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_create_timesheet
msgid "Create Timesheet from ticket"
msgstr "Créer une feuille de temps depuis un ticket"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_date
msgid "Created on"
msgstr "Créé le"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Days Spent"
msgstr "Jours passés"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Delete"
msgstr "Supprimer"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "Décrivez votre activité..."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description"
msgstr "Description"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description of the ticket..."
msgstr "Description du ticket..."

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Discard"
msgstr "Ignorer"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer
msgid "Display Timer"
msgstr "Afficher le chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_pause
msgid "Display Timer Pause"
msgstr "Afficher la pause du chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_resume
msgid "Display Timer Resume"
msgstr "Afficher la reprise du chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr "Afficher le démarrage du chronomètre en primaire"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_secondary
msgid "Display Timer Start Secondary"
msgstr "Afficher le démarrage du chronomètre en secondaire"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_stop
msgid "Display Timer Stop"
msgstr "Afficher le stop du chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timesheet_timer
msgid "Display Timesheet Time"
msgstr "Afficher le Temps de la Feuille de Temps"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Duration"
msgstr "Durée"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "Encoder l'unité de mesure en jours"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__has_helpdesk_team
msgid "Has Helpdesk Teams"
msgstr "A des équipes d'assistance"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__helpdesk_team
msgid "Helpdesk Team"
msgstr "Équipe d'assistance"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr "Ticket d'Assistance"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__total_hours_spent
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Hours Spent"
msgstr "Heures passés"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "In Progress"
msgstr "En cours"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__is_timer_running
msgid "Is Timer Running"
msgstr "Le chronomètre tourne?"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Team's Ratings"
msgstr "Evaluations de mon équipe"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
msgid "My Team's Tickets"
msgstr "Tickets de mon équipe"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause"
msgstr "Pause"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause timer"
msgstr "Mettre en pause le chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_project
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid "Project"
msgstr "Projet"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.project_project_action_view_helpdesk_tickets
msgid "Project Tickets"
msgstr "Tickets du projet"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid ""
"Project to which the tickets (and the timesheets) will be linked by default."
msgstr ""
"Projet auquel les tickets (et les feuilles de temps) seront liés par défaut."

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid "Record a new activity"
msgstr "Enregistrer une nouvelle activité"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume"
msgstr "Reprendre"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume timer"
msgstr "Relancer le chronomètre"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save"
msgstr "Sauvegarder"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save time"
msgstr "Gagner du temps"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__total_hours_spent
msgid "Spent Hours"
msgstr "Heures consommées"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start"
msgstr "Démarrer"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start timer"
msgstr "Lancer le chronomètre"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop"
msgstr "Arrêter"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop timer"
msgstr "Arrêter le chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model.constraint,message:helpdesk_timesheet.constraint_helpdesk_ticket_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "Le temps de la feuille de temps doit être positif"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "This requires to have project module installed."
msgstr "Ceci requiet d'avoir le module Projet installé."

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"Cela définira l'unité de mesure utilisée pour encoder les feuilles de temps. Cela fournira simplement des outils\n"
"        et des widgets pour faciliter l'encodage. Tous les rapports seront toujours exprimés en heures (valeur par défaut)."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_search_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_tree_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_view_form_helpdesk
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Analyse de Ticket"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
msgid "Ticket for which we are creating a sales order"
msgstr "Ticket pour lequel nous créons un bon de commande"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/project.py:0
#: code:addons/helpdesk_timesheet/models/project.py:0
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.project_project_view_form_inherit_helpdesk_timesheet
#, python-format
msgid "Tickets"
msgstr "Tickets"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__time_spent
msgid "Time"
msgstr "Heure"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_pause
msgid "Timer Last Pause"
msgstr "Dernière pause du chronomètre"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_start
msgid "Timer Start"
msgstr "Démarrage du chronomètre"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheet Activities"
msgstr "Activités de la feuille de temps"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Unité d'encodage des feuilles de temps"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "Timesheet activated on Team"
msgstr "Feuille de temps activée sur l'équipe"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheets"
msgstr "Feuilles de temps"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "Temps total de la feuille de temps"

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Suivez vos heures de travail par projets chaque jour et facturez ce temps à "
"vos clients."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__user_timer_id
msgid "User Timer"
msgstr "Chronomètre de l'utilisateur"
