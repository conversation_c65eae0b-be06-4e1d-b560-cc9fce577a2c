# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_count
msgid "# Tickets"
msgstr "# Tickets"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_team_view_form_inherit_helpdesk_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/analytic.py:0
#, python-format
msgid "A timesheet cannot be linked to a task and a ticket at the same time."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha Analítica"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Closed"
msgstr "Encerrado"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Confirm Time Spent"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_create_timesheet
msgid "Create Timesheet from ticket"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_date
msgid "Created on"
msgstr "Criado em"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Days Spent"
msgstr "Dias gastos"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Delete"
msgstr "Eliminar"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Describe your activity..."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description"
msgstr "Descrição"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description of the ticket..."
msgstr "Descrição do ticket..."

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Discard"
msgstr "Descartar"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__display_name
msgid "Display Name"
msgstr "Nome"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer
msgid "Display Timer"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_pause
msgid "Display Timer Pause"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_resume
msgid "Display Timer Resume"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_secondary
msgid "Display Timer Start Secondary"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_stop
msgid "Display Timer Stop"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timesheet_timer
msgid "Display Timesheet Time"
msgstr "Exibir Horas no Registo de Horas"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Duration"
msgstr "Duração"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__has_helpdesk_team
msgid "Has Helpdesk Teams"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__helpdesk_team
msgid "Helpdesk Team"
msgstr "Equipa de Apoio ao Cliente"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr "Ticket de Apoio ao Cliente"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__total_hours_spent
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Hours Spent"
msgstr "Horas Gastas"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "In Progress"
msgstr "Em Progresso"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__is_timer_running
msgid "Is Timer Running"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Team's Ratings"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
msgid "My Team's Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause"
msgstr "Pausa"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause timer"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_project
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid "Project"
msgstr "Projeto"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.project_project_action_view_helpdesk_tickets
msgid "Project Tickets"
msgstr "Tickets do Projeto"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid ""
"Project to which the tickets (and the timesheets) will be linked by default."
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid "Record a new activity"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume"
msgstr "Resumo"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume timer"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save"
msgstr "Guardar"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save time"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__total_hours_spent
msgid "Spent Hours"
msgstr "Horas Gastas"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start"
msgstr "Início"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start timer"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop"
msgstr "Fim"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop timer"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.constraint,message:helpdesk_timesheet.constraint_helpdesk_ticket_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "This requires to have project module installed."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_search_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_tree_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_view_form_helpdesk
msgid "Ticket"
msgstr "Ticket"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Análise de Tickets"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
msgid "Ticket for which we are creating a sales order"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/project.py:0
#: code:addons/helpdesk_timesheet/models/project.py:0
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.project_project_view_form_inherit_helpdesk_timesheet
#, python-format
msgid "Tickets"
msgstr "Tickets"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__time_spent
msgid "Time"
msgstr "Tempo"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_pause
msgid "Timer Last Pause"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_start
msgid "Timer Start"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheet Activities"
msgstr "Atividades do Registo de Horas"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "Timesheet activated on Team"
msgstr "Registo de horas ativado na Equipa"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheets"
msgstr "Registos de Horas"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__total_timesheet_time
msgid "Total Timesheet Time"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__user_timer_id
msgid "User Timer"
msgstr ""
