# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_timesheet
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Nadir <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_count
msgid "# Tickets"
msgstr "Talep Sayısı"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_team_view_form_inherit_helpdesk_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Kaydedilmiş</span>"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/analytic.py:0
#, python-format
msgid "A timesheet cannot be linked to a task and a ticket at the same time."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitik Satırı"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Closed"
msgstr "Kapanmış"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "Confirm Time Spent"
msgstr "Harcanan Zamanı Onayla"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_create_timesheet
msgid "Create Timesheet from ticket"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Days Spent"
msgstr "Harcanan Günler"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Delete"
msgstr "Sil"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "Aktivitenizi tanımlayın... "

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description"
msgstr "Açıklama"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Description of the ticket..."
msgstr "Talep tanımı..."

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Discard"
msgstr "Vazgeç"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer
msgid "Display Timer"
msgstr "Zamanlayıcıyı Görüntüle"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_pause
msgid "Display Timer Pause"
msgstr "Gösterge Zamanlayıcı Duraklat"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_resume
msgid "Display Timer Resume"
msgstr "Zamanlayıcıyı Devam Ettirmeyi Göster"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr "Zamanlayıcı Başlangıç ​​Birincil Gösterimi"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_secondary
msgid "Display Timer Start Secondary"
msgstr "Ekran Zamanlayıcı İkincil Başlat"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_stop
msgid "Display Timer Stop"
msgstr "Zamanlayıcı Durdurmayı Göster"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timesheet_timer
msgid "Display Timesheet Time"
msgstr "Çalışma Çizelgesi Süresini Görüntüle"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Duration"
msgstr "Süre"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "Encode Uom In Days"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__has_helpdesk_team
msgid "Has Helpdesk Teams"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__helpdesk_team
msgid "Helpdesk Team"
msgstr "Destek Ekibi"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr "Yardım Masası Talebi"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__total_hours_spent
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Hours Spent"
msgstr "Harcanan Saatler"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/helpdesk.py:0
#, python-format
msgid "In Progress"
msgstr "Devam Eden"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__is_timer_running
msgid "Is Timer Running"
msgstr "Zamanlayıcı Çalışıyor"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Team's Ratings"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
msgid "My Team's Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause"
msgstr "Duraklat"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause timer"
msgstr "Zamanlayıcıyı duraklat"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_project
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid "Project"
msgstr "Proje"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.project_project_action_view_helpdesk_tickets
msgid "Project Tickets"
msgstr "Proje Talepleri"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid ""
"Project to which the tickets (and the timesheets) will be linked by default."
msgstr ""
"Destek taleplerinin (ve çalışma çizelgelerinin) varsayılan olarak "
"bağlanacağı proje."

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid "Record a new activity"
msgstr "Yeni bir aktivite kaydedin"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume"
msgstr "Devam Et"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume timer"
msgstr "Zamanlayıcıyı devam ettir"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save"
msgstr "Kaydet"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save time"
msgstr "Zamanı kaydet"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__total_hours_spent
msgid "Spent Hours"
msgstr "Harcanan Saatleri"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start"
msgstr "Başla"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start timer"
msgstr "Zamanlayıcıyı başlat"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop"
msgstr "Durdur"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop timer"
msgstr "Zamanlayıcıyı durdur"

#. module: helpdesk_timesheet
#: model:ir.model.constraint,message:helpdesk_timesheet.constraint_helpdesk_ticket_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "Çalışma çizelgesinin zamanı pozitif olmalıdır"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "This requires to have project module installed."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"Bu, çalışma çizelgesini kodlamak için kullanılan ölçü birimini ayarlar. Bu sadece araçlar sağlayacaktır\n"
"        ve kodlamaya yardımcı olacak araçlar. Tüm raporlar yine de saat cinsinden ifade edilir (varsayılan değer)."

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_search_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_tree_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_view_form_helpdesk
msgid "Ticket"
msgstr "Talep"

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "Talep Analizi"

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
msgid "Ticket for which we are creating a sales order"
msgstr ""

#. module: helpdesk_timesheet
#: code:addons/helpdesk_timesheet/models/project.py:0
#: code:addons/helpdesk_timesheet/models/project.py:0
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.project_project_view_form_inherit_helpdesk_timesheet
#, python-format
msgid "Tickets"
msgstr "Talepler"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__time_spent
msgid "Time"
msgstr "Zaman"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_pause
msgid "Timer Last Pause"
msgstr "Zamanlayıcı Son Duraklatma"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_start
msgid "Timer Start"
msgstr "Zamanlayıcı Başlangıcı"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheet Activities"
msgstr "Çalışma Çizelgesi Aktiviteleri"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Çalışma Çizelgesi Kodlama Birimi"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "Timesheet activated on Team"
msgstr "Ekipte çalışma çizelgesi aktifleştirildi"

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheets"
msgstr "Çalışma Çizelgeleri"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "Toplam Çalışma Çizelgesi Süresi"

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Çalışma saatlerinizi her gün projelere göre takip edin ve bu zamanı "
"müşterilerinize fatura edin. "

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__user_timer_id
msgid "User Timer"
msgstr "Kullanıcı Zamanlayıcısı"
