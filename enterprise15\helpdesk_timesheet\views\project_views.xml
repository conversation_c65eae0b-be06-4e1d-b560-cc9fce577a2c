<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_project_action_view_helpdesk_tickets" model="ir.actions.act_window">
        <field name="name">Project Tickets</field>
        <field name="res_model">helpdesk.ticket</field>
        <field name="view_mode">kanban,tree,form,pivot,graph</field>
        <field name="search_view_id" ref="helpdesk_ticket_view_search_inherit_helpdesk_timesheet"/>
        <field name="context">{'search_default_project_id': active_id, 'default_project_id': active_id}</field>
        <field name="domain">[('project_id', '=', active_id)]</field>
    </record>

   <record id="project_project_view_form_inherit_helpdesk_timesheet" model="ir.ui.view">
        <field name="name">project.form.inherit.helpdesk.timesheet</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.edit_project"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <!-- Button to be removed in master -->
                <button
                    class="oe_stat_button d-none"
                    type="action"
                    name="%(project_project_action_view_helpdesk_tickets)d"
                    context="{'search_default_project_id': [active_id], 'default_project_id': active_id}"
                    icon="fa-life-ring"
                    attrs="{'invisible': [('ticket_count', '=', 0)]}">
                </button>
                <button
                    class="oe_stat_button"
                    type="object"
                    name="action_open_project_tickets"
                    context="{'search_default_project_id': [active_id], 'default_project_id': active_id}"
                    icon="fa-life-ring"
                    attrs="{'invisible': [('ticket_count', '=', 0)]}">
                    <field name="ticket_count" string="Tickets" widget="statinfo"/>
                </button>
            </div>
        </field>
        <field name="groups_id" eval="[(4, ref('helpdesk.group_helpdesk_user'))]"/>
   </record>

</odoo>
