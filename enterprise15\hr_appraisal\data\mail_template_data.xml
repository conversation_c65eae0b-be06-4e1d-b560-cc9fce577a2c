<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_appraisal_confirm" model="mail.template">
            <field name="name">Hr Appraisal: Confirm Appraisal</field>
            <field name="model_id" ref="hr_appraisal.model_hr_appraisal"/>
            <field name="subject">{{ object.employee_id.name }}: Appraisal Confirmed</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        An appraisal of <t t-out="ctx.get('employee_to_name', 'employee')">employee</t> has been confirmed.
                        <br/><br/>
                        Please schedule an appraisal date together.
                        <br/><br/>
                        <t t-if="ctx.get('recipient_users')">
                            <p style="margin: 16px 0px 16px 0px; text-align: center;">
                                <a t-att-href="ctx.get('url')"
                                    style="background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
                                    View Appraisal
                                </a>
                            </p>
                        </t>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_template_appraisal_request" model="mail.template">
            <field name="name">Hr Appraisal: Request an Appraisal from Manager</field>
            <field name="model_id" ref="hr_appraisal.model_hr_appraisal"/>
            <field name="subject">Appraisal Requested</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        Dear <t t-out="ctx.get('employee_to_name', 'employee')">employee</t>,
                        <br/>
                        An appraisal has been requested by <t t-out="ctx.get('author_name', '')">Addison Olson</t>
                        <br/>
                        (<t t-out="ctx.get('author_mail', '')"><EMAIL></t>).
                        <t t-if="ctx.get('user_body')">
                            <div style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;">
                                <t t-out="ctx.get('user_body')">Annual appraisal.</t>
                            </div>
                        </t>
                        <t t-if="ctx.get('recipient_users')">
                            <p style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;">
                                <a t-att-href="ctx.get('url')"
                                    style=" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
                                    View Appraisal
                                </a>
                            </p>
                        </t>
                        <br/>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="mail_template_appraisal_request_from_employee" model="mail.template">
            <field name="name">Hr Appraisal: Request an Appraisal From Employee</field>
            <field name="model_id" ref="hr_appraisal.model_hr_appraisal"/>
            <field name="subject">{{ hasattr(object, 'name') and object.name or '' }} requests an Appraisal</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        Dear <t t-out="ctx.get('employee_to_name', 'employee')">employee</t>,
                        <br/>
                        <t t-out="ctx.get('author_name', '')">Addison Olson</t> (<t t-out="ctx.get('author_mail', '')"><EMAIL></t>) wishes an appraisal.
                        <t t-if="ctx.get('user_body')">
                            <div style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;">
                                <t t-out="ctx.get('user_body')">Annual appraisal.</t>
                            </div>
                        </t>
                        <t t-if="ctx.get('recipient_users')">
                            <p style="margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;">
                                <a t-att-href="ctx.get('url')"
                                    style=" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
                                    View Appraisal
                                </a>
                            </p>
                        </t>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
