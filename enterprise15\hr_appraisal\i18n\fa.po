# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# سید محمد آ<PERSON>ربرا <moham<PERSON><PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON> <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "%s Goals"
msgstr "%s اهداف"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "1 Meeting"
msgstr "جلسه 1"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__100
msgid "100 %"
msgstr "100%"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__25
msgid "25 %"
msgstr "25%"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__module_hr_appraisal_survey
msgid "360 Feedback"
msgstr "بازخورد 360"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__50
msgid "50 %"
msgstr "50%"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__75
msgid "75 %"
msgstr "75%"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        An appraisal of <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t> has been confirmed.\n"
"                        <br/><br/>\n"
"                        Please schedule an appraisal date together.\n"
"                        <br/><br/>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) wishes an appraisal.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        An appraisal has been requested by <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t>\n"
"                        <br/>\n"
"                        (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>).\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <br/>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Schedule an appraisal\n"
"                        </p><p>\n"
"                            Plan appraisals with your colleagues, collect and discuss feedback.\n"
"                        </p>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-info\">Ready</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-secondary\">Canceled</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"bg-success\">Done</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"col-4 text-right\" attrs=\"{'invisible': ['|', '|', ('manager_feedback_published', '=', True), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}\">Not Visible to Employee</span>\n"
"                                            <span class=\"col-4 text-right\" attrs=\"{'invisible': ['|', '|', ('manager_feedback_published', '=', False), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}\">Visible to Employee</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">360 Feedback</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">Appraisals Plans</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"<span class=\"o_form_label\">Feedback Templates</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" role=\"img\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Appraisal\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Goals</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_value\">Prev. Appraisal</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-right\" attrs=\"{'invisible': ['|', '|', ('employee_feedback_published', '=', True), ('can_see_employee_publish', '=', False), ('state', '=', 'done')]}\">Not Visible to Manager</span>\n"
"                                            <span class=\"text-right\" attrs=\"{'invisible': ['|', '|', ('employee_feedback_published', '=', False), ('can_see_employee_publish', '=', False), ('state', '=', 'done')]}\">Visible to Manager</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-right\" attrs=\"{'invisible': ['|', '|', ('manager_feedback_published', '=', True), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}\">Not Visible to Employee</span>\n"
"                                        <span class=\"text-right\" attrs=\"{'invisible': ['|', '|', ('manager_feedback_published', '=', False), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}\">Visible to Employee</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', True), ('can_see_employee_publish', '=', False)]}\">Not Visible to Manager</span>\n"
"                                        <span class=\"text-right\" attrs=\"{'invisible': ['|', ('employee_feedback_published', '=', False), ('can_see_employee_publish', '=', False)]}\">Visible to Manager</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<strong><span>Meeting: </span></strong>"
msgstr "<strong><span>جلسه: </span></strong>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_gantt
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_gantt
msgid "<strong>Date — </strong>"
msgstr ""

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "Access all appraisals"
msgstr "کاربر"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "Action Needed"
msgstr "اقدام مورد نیاز است"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "فعال"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_ids
msgid "Activities"
msgstr "فعالیت‌ها"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "دکوراسیون استثنایی فعالیت"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid "Activity State"
msgstr "وضعیت فعالیت"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Activity Type Icon"
msgstr "آیکون نوع فعالیت"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Add existing contacts..."
msgstr "افزودن مخاطبین موجود..."

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Administrator"
msgstr "مدیر"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
msgid "Application Settings"
msgstr "تنظیمات برنامه"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__appraisal_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Appraisal"
msgstr "ارزیابی"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr "تحلیل ارزیابی"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_note
msgid "Appraisal Assessment Note"
msgstr "یادداشت ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_child_ids
msgid "Appraisal Child"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_mail_template
msgid "Appraisal Confirm Mail Template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_count
msgid "Appraisal Count"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
msgid "Appraisal Date"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__employee_id
msgid "Appraisal Employee"
msgstr "کارمند ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_employee_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_employee_feedback_template
msgid "Appraisal Employee Feedback Template"
msgstr "قالب بازخورد کارمندان ارزیابی"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Appraisal Form to Fill"
msgstr "فرم ارزیابی برای پر کردن"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal
msgid "Appraisal Goal"
msgstr "هدف ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_manager_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_manager_feedback_template
msgid "Appraisal Manager Feedback Template"
msgstr "قالب بازخورد مدیر ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_plan_posted
msgid "Appraisal Plan Posted"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Appraisal Request"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request
msgid "Appraisal Requested"
msgstr "ارزیابی درخواست شده است"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__pending
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Sent"
msgstr "ارزیابی ارسال شده"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr "آمار ارزیابی"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_form
msgid "Appraisal Templates"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Appraisal of %s"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Appraisal to Confirm and Send"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr "ارزیابی در حال شروع"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
#: model:ir.cron,cron_name:hr_appraisal.ir_cron_scheduler_appraisal
#: model:ir.cron,name:hr_appraisal.ir_cron_scheduler_appraisal
msgid "Appraisal: Run employee appraisal"
msgstr "ارزیابی: ارزیابی کارکنان را اجرا کنید"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals"
msgstr "ارزیابی‌ها"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr "ارزیابی برای پردازش"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Archived"
msgstr "بایگانی شده"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Ask to fill a survey to other employees"
msgstr "از سایر کارمندان بخواهید نظرسنجی را پر کنید"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Assessment"
msgstr "ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__assessment_note_ids
msgid "Assessment Note"
msgstr "یادداشت ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__author_id
msgid "Author"
msgstr "نویسنده"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__author_id
msgid "Author of the message."
msgstr "نویسنده پیام."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_plan
msgid "Automatically Generate Appraisals"
msgstr "ایجاد ارزیابی خودکار"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Automatically generate appraisals"
msgstr "ایجاد ارزیابی خودکار"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_1920
msgid "Avatar"
msgstr "آواتار"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_128
msgid "Avatar 128"
msgstr "آواتار 128"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_base
msgid "Basic Employee"
msgstr "کارمند پایه"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Calendar Event"
msgstr "رخداد گاهشمار"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__can_edit_body
msgid "Can Edit Body"
msgstr "امکان ویرایش محتوا"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_employee_publish
msgid "Can See Employee Publish"
msgstr "می توانید انتشار کارمند را ببینید"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_manager_publish
msgid "Can See Manager Publish"
msgstr "می توانید انتشار مدیر را ببینید"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "لغو"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel Future Appraisals"
msgstr "لغو ارزیابی های آینده"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel all appraisal after contract end date."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__cancel
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__cancel
msgid "Cancelled"
msgstr "لغو شد"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__color
msgid "Color"
msgstr "رنگ"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__company_id
msgid "Company"
msgstr "شرکت"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Compose Email"
msgstr "نگارش ایمیل"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "پیکربندی"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Configure General Feedback Template"
msgstr "پیکربندی قالب بازخورد عمومی"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Confirm"
msgstr "تایید"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__pending
msgid "Confirmed"
msgstr "تایید شده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body
msgid "Contents"
msgstr "محتویات"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "ایجاد تاریخ"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_first_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_first_appraisal
msgid "Create a first Appraisal after"
msgstr "ایجاد اولین ارزیابی بعد از"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_next_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_next_appraisal
msgid "Create a second Appraisal after"
msgstr "ایجاد دومین ارزیابی بعد از"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_after_recruitment
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_after_recruitment
msgid "Create an Appraisal after recruitment"
msgstr "ایجاد ارزیابی بعد از استخدام"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_date
msgid "Created on"
msgstr "ایجاد شده در"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "تاریخ ایجاد"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__custom_appraisal_templates
msgid "Custom Appraisal Templates"
msgstr "قالب های ارزیابی سفارشی"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Date"
msgstr "تاریخ"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__date_close
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid ""
"Date of the appraisal, automatically updated when the appraisal is Done or "
"Cancelled."
msgstr ""
"تاریخ ارزیابی، زمانی که ارزیابی انجام شد یا لغو شد، به طور خودکار به روز می "
"شود."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Deadline"
msgstr "آخرین مهلت"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "Deadline:"
msgstr "ضرب الاجل:"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__null_value
msgid "Default Value"
msgstr "مقدار پیش‌فرض"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "Delete"
msgstr "حذف"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Department"
msgstr "دپارتمان"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "ویزارد خروج"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__description
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "Description"
msgstr "توضیحات"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__done
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Done"
msgstr "انجام شد"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "Dropdown menu"
msgstr "منوی کشویی"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__email_from
msgid "Email address of the sender"
msgstr "آدرس ایمیل فرستنده"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Employee"
msgstr "کارمند"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "ارزیابی کارمند"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_autocomplete_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_autocomplete_ids
msgid "Employee Autocomplete"
msgstr "تکمیل خودکار کارکنان"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback
msgid "Employee Feedback"
msgstr "بازخورد کارمندان"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_published
msgid "Employee Feedback Published"
msgstr "بازخورد کارمندان منتشر شده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__employee_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Employee Feedback Template"
msgstr "قالب بازخورد کارکنان"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__name
msgid "Employee Name"
msgstr "نام کارمند"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_user_id
msgid "Employee User"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Feedback"
msgstr "بازخورد کارمندان"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__assessment_note_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_note
msgid "Evaluation Scale"
msgstr "مقیاس ارزیابی"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Exceeds expectations"
msgstr "فراتر از انتظارات"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "فیلتر های بسط یافته..."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Feedback Templates"
msgstr "قالب بازخورد"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__model_object_field
msgid "Field"
msgstr "فیلدفیلد"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Fill appraisal for <a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"
msgstr "تکمیل ارزیابی برای <a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr "مصاحبه نهایی"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr "تاریخ مصاحبه نهایی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__assessment_note
msgid "Final Rating"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_follower_ids
msgid "Followers"
msgstr "دنبال‌کنندگان"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "آیکون فونت عالی مثلا fa-tasks"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__email_from
msgid "From"
msgstr "از"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "فعالیتهای آینده"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "Goal"
msgstr "هدف"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_goal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_goal
msgid "Goals"
msgstr "اهداف"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "گروه‌بندی برمبنای"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "گروه‌بندی بر اساس..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__has_message
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__has_message
msgid "Has Message"
msgstr "دارای پیام"

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_confirm
msgid "Hr Appraisal: Confirm Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "Hr Appraisal: Request an Appraisal From Employee"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request
msgid "Hr Appraisal: Request an Appraisal from Manager"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__id
msgid "ID"
msgstr "شناسه"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon"
msgstr "آیکون"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "آیکون برای نشان دادن یک فعالیت استثنایی."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "در صورت بررسی ، برخی پیام ها خطای تحویل دارند."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_1920
msgid "Image"
msgstr "تصویر"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_128
msgid "Image 128"
msgstr "تصویر 128"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr "در حال انجام ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr "مصاحبه"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__is_appraisal_manager
msgid "Is Appraisal Manager"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__is_mail_template_editor
msgid "Is Editor"
msgstr "ویرایشگر است"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_is_follower
msgid "Is Follower"
msgstr "دنبال می کند"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__is_implicit_manager
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_implicit_manager
msgid "Is Implicit Manager"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_manager
msgid "Is Manager"
msgstr "آیا مدیر است"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__lang
msgid "Language"
msgstr "زبان"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_id
msgid "Last Appraisal"
msgstr "آخرین ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_date
msgid "Last Appraisal Date"
msgstr "آخرین تاریخ ارزیابی"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Last Meeting"
msgstr "آخرین جلسه"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report____last_update
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_date
msgid "Last Updated on"
msgstr "آخرین به روز رسانی در"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late"
msgstr "دیر"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "فعالیتهای اخیر"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__template_id
msgid "Mail Template"
msgstr "قالب ایمیل"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_main_attachment_id
msgid "Main Attachment"
msgstr "پیوست اصلی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_form
msgid "Manager"
msgstr "مدیر"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager Assessment will show here"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback
msgid "Manager Feedback"
msgstr "بازخورد مدیر"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_published
msgid "Manager Feedback Published"
msgstr "بازخورد مدیر منتشر شده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_template
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__manager_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Manager Feedback Template"
msgstr "قالب بازخورد مدیر"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_user_ids
msgid "Manager Users"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager's Feedback"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Mark as Done"
msgstr "علامت به عنوان انجام شده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_count_display
msgid "Meeting Count"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_ids
msgid "Meetings"
msgstr "جلسات"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Meets expectations"
msgstr "مطابق انتظارات"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_ids
msgid "Messages"
msgstr "پیام‌ها"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "موعد فعالیت من"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_my
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Appraisals"
msgstr "ارزیابی های من"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "My Goals"
msgstr "اهداف من"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__name
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Name"
msgstr "نام"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Needs improvement"
msgstr "نیازمند بهبود"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "رویداد تقویم فعالیت بعدی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "موعد فعالیت بعدی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_summary
msgid "Next Activity Summary"
msgstr "خلاصه فعالیت بعدی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_id
msgid "Next Activity Type"
msgstr "نوع فعالیت بعدی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__next_appraisal_date
msgid "Next Appraisal Date"
msgstr "تاریخ ارزیابی بعدی"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Next Meeting"
msgstr "جلسه بعدی"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "No Meeting"
msgstr "بدون جلسه"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Note"
msgstr "یادداشت"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "تعداد پیام ها که نیاز به عمل"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_unread_counter
msgid "Number of unread messages"
msgstr "تعداد پیام‌های خوانده نشده"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"زبان ترجمه اختیاری (کد ISO) برای انتخاب هنگام ارسال ایمیل. اگر تنظیم نشود، "
"از نسخه انگلیسی استفاده خواهد شد. این معمولاً باید یک عبارت نگهدارنده باشد "
"که زبان مناسب را ارائه می دهد، به عنوان مثال. {{ object.partner_id.lang }}."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__null_value
msgid "Optional value to use if the target field is empty"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__parent_user_id
msgid "Parent User"
msgstr "کاربر والد"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "People I Manage"
msgstr "افرادی که من مدیریت میکنم"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__copyvalue
msgid "Placeholder Expression"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid "Plan appraisals with your colleagues, collect and discuss feedback."
msgstr ""
"ارزیابی ها را با همکاران خود برنامه ریزی کنید، بازخوردها را جمع آوری و بحث "
"کنید."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__previous_appraisal_id
msgid "Previous Appraisal"
msgstr "ارزیابی قبلی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Previous Appraisal Date"
msgstr "تاریخ ارزیابی قبلی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__note
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private Note"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__progression
msgid "Progress"
msgstr "پیشرفت"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_public
msgid "Public Employee"
msgstr "کارمند عمومی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__recipient_ids
msgid "Recipients"
msgstr "گیرندگان"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "شریک تجاری مرتبط"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__employee_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__manager_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee_base__parent_user_id
msgid "Related user name for the resource to manage its access."
msgstr "نام کاربری مرتبط برای منبع برای مدیریت حق دسترسی آن."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__render_model
msgid "Rendering Model"
msgstr "مدل رندرینگ"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Reopen"
msgstr "بازگشایی"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "گزارش"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr "درخواست ارزیابی"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_create_multi_appraisals
msgid "Request Appraisals"
msgstr "درخواست ارزیابی"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_request_appraisal
msgid "Request an Appraisal"
msgstr "درخواست یک ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_user_id
msgid "Responsible User"
msgstr "کاربر مسئول"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.open_view_hr_appraisal_tree
msgid "Schedule an appraisal"
msgstr "یک ارزیابی را برنامه ریزی کنید"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr "جستجوی ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Self Assessment will show here"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Send"
msgstr "ارسال"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Send by email"
msgstr "ارسال با ایمیل"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "تنظیمات"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_employee_feedback_full
msgid "Show Employee Feedback Full"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_manager_feedback_full
msgid "Show Manager Feedback Full"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr "تمام رکوردهایی که تاریخ اعمال بعدی قبل از امروز است را نشان بده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "وضعیت"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"وضعیت بر اساس فعالیت ها\n"
"به سر رسیده : موعد مقرر گذشته است\n"
"امروز: تاریخ فعالیت امروز است\n"
"برنامه ریزی شده: فعالیت های آینده."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid "Strongly Exceed Expectations"
msgstr "به شدت فراتر از انتظارات"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__sub_model_object_field
msgid "Sub-field"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__sub_object
msgid "Sub-model"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__subject
msgid "Subject"
msgstr "موضوع"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Subject..."
msgstr "موضوع..."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "چنین گروه بندی مجاز نیست."

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid ""
"Thanks to your Appraisal Plan, without any new manual Appraisal, the new "
"Appraisal will be automatically created on %s."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__note
msgid "The content of this note is not visible by the Employee."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__last_appraisal_date
msgid "The date of the last appraisal"
msgstr "تاریخ آخرین ارزیابی"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__next_appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr ""
"تاریخ ارزیابی بعدی با تاریخ های طرح ارزیابی (ارزیابی اول + دوره تکرار) "
"محاسبه می شود."

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_res_company_positif_number_months
msgid "The duration time must be bigger or equal to 1 month."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid ""
"The employee %s arrived %s months ago. An appraisal for %s is created. You "
"can assess %s & determinate the date for '1to1' meeting before %s."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "The employee feedback cannot be changed by managers."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "The manager feedback cannot be changed by an employee."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__assessment_note
msgid "This field is not visible to the Employee."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__new
msgid "To Confirm"
msgstr "برای تایید"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "To Do"
msgstr "جهت اقدام"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__new
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "To Start"
msgstr "برای شروع"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "فعالیتها امروز"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع فعالیت استثنایی روی رکورد."

#. module: hr_appraisal
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "ارسال پیام ممکن نیست، لطفا آدرس ایمیل فرستنده را پیکربندی کنید."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__uncomplete_goals_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__uncomplete_goals_count
msgid "Uncomplete Goals Count"
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "Unpublished"
msgstr "منتشر نشده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_unread
msgid "Unread Messages"
msgstr "پیام های ناخوانده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_unread_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_unread_counter
msgid "Unread Messages Counter"
msgstr "شمارنده پیام‌های خوانده‌نشده"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_user_id
msgid "User"
msgstr "کاربر"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__user_body
msgid "User Contents"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
msgid "Users"
msgstr "کاربران"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__waiting_feedback
msgid "Waiting Feedback from Employee/Managers"
msgstr "در انتظار بازخورد از کارمند/مدیران"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid ""
"You arrived %s months ago. Your appraisal is created you can assess yourself"
" here. Your manager will determinate the date for your '1to1' meeting."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#, python-format
msgid "You cannot delete appraisal which is not in draft or canceled state"
msgstr ""
"نمی توانید ارزیابی هایی را که در حالت پیش نویس یا لغو شده نیستند حذف کنید"

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid ""
"Your employee's last appraisal was %s months ago. An appraisal for %s is "
"created. You can assess %s & determinate the date for '1to1' meeting before "
"%s."
msgstr ""

#. module: hr_appraisal
#: code:addons/hr_appraisal/models/res_company.py:0
#, python-format
msgid ""
"Your last appraisal was %s months ago. Your appraisal is created you can "
"assess yourself here. Your manager will determinate the date for your '1to1'"
" meeting."
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "e.g. John Doe"
msgstr "برای مثال محمد احمدی"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "e.g. Present yourself to your new team"
msgstr "به عنوان مثال، خود را به تیم جدید خود معرفی کنید"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months after recruitment,"
msgstr "ماه پس از استخدام"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months,"
msgstr "ماه"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months."
msgstr "ماه"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "oe_kanban_text_red"
msgstr "oe_kanban_text_red"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "once published"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "text-danger"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "then after"
msgstr "پس از"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "then every"
msgstr "سپس هر"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"{{ hasattr(object, 'name') and object.name or '' }} requests an Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm
msgid "{{ object.employee_id.name }}: Appraisal Confirmed"
msgstr ""
