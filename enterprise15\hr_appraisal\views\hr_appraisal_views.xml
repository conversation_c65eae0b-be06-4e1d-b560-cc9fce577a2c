<?xml version="1.0" ?>
<odoo>

    <menuitem name="Appraisals" id="menu_hr_appraisal_root" web_icon="hr_appraisal,static/description/icon.png" sequence="200"/>
    <menuitem name="Configuration"
        id="menu_hr_appraisal_configuration"
        parent="menu_hr_appraisal_root"
        sequence="99"/>

    <record model="ir.ui.view" id="view_hr_appraisal_form">
        <field name="name">hr.appraisal.form</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <form string="Appraisal" class="o_appraisal_form">
            <header>
                <button name="action_confirm" string="Confirm" type="object" class="oe_highlight" attrs="{'invisible': &quot;['|',('state','!=','new'), '&amp;', ('manager_user_ids', 'not in', uid), '&amp;', ('employee_user_id', '!=', uid), ('is_appraisal_manager', '=', False)]&quot;}" data-hotkey="v"/>
                <button name="action_done" string="Mark as Done" type="object" class="oe_highlight" attrs="{'invisible': &quot;['|', ('state','!=','pending'), '&amp;', ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}" data-hotkey="x"/>
                <button name="action_send_appraisal_request" string="Send by email" states="new" type="object" data-hotkey="g"/>
                <button name="action_cancel" string="Cancel" type="object" attrs="{'invisible': &quot;['|', ('state','!=','pending'), '&amp;', ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}" data-hotkey="z"/>
                <button name="action_back" string="Reopen" type="object" attrs="{'invisible': &quot;['|', '&amp;', ('state','!=','done'), ('state','!=','cancel'), '&amp;', ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}" data-hotkey="w"/>
                <field name="state" widget="statusbar" statusbar_visible="new,pending,done" options="{'fold_field': 'fold'}"/>
            </header>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button class="oe_stat_button" name="action_open_goals" icon="fa-bullseye" type="object">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text">
                                <field name="uncomplete_goals_count"/> Employee's
                            </span>
                            <span class="o_stat_text">Goals</span>
                        </div>
                    </button>
                    <button class="oe_stat_button" name="action_open_last_appraisal" icon="fa-star-half-o" type="object" attrs="{'invisible': &quot;['|', ('previous_appraisal_id', '=', False), ('previous_appraisal_id', '=', id)]&quot;}">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value">Prev. Appraisal</span>
                            <span class="o_stat_text"><field name="previous_appraisal_date"/></span>
                        </div>
                    </button>
                    <button class="oe_stat_button" name="action_calendar_event" icon="fa-calendar" type="object">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value"><field name="meeting_count_display" /></span>
                            <span class="o_stat_text"><field name="date_final_interview" readonly="1"/></span>
                        </div>
                    </button>
                </div>
                <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <div class="oe_title">
                    <label for="employee_id"/>
                    <h1>
                        <field name="employee_id" domain="[('id', 'in', employee_autocomplete_ids)]" attrs="{'readonly': ['|', ('state', '!=', 'new'), '&amp;', ('is_implicit_manager', '=', False), ('is_appraisal_manager', '=', False)]}" placeholder="e.g. John Doe"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="manager_ids" widget="many2many_tags" options="{'color_field': 'color'}" required="1" attrs="{
                            'readonly': &quot;[
                            '|',
                            ('state', '=', ('done')),
                            '&amp;',
                            ('state', '=', ('pending')),
                            '&amp;',
                            ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}"/>
                        <field name="date_close" attrs="{'readonly':[('state','in', ('pending','done'))]}"/>
                    </group>
                    <group>
                        <field name="department_id"/>
                        <field name="previous_appraisal_id" invisible="1"/>
                        <field name="employee_user_id" invisible="1"/>
                        <field name="manager_user_ids" invisible="1"/>
                        <field name="id" invisible="1"/>
                        <field name="company_id" groups="base.group_multi_company" required="1"/>
                        <field name="assessment_note" options="{'no_create': True, 'no_open': True}" attrs="{
                            'invisible': &quot;['|', ('state', '=', 'new'), ('employee_user_id', '=', uid)]&quot;,
                            'readonly': &quot;[
                            '|',
                            ('state', '=', 'done'),
                            '&amp;',
                            ('state', '=', ('pending')),
                            '&amp;',
                            ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}"/>
                    </group>
                </group>
                <notebook>
                    <page string="Appraisal" name="appraisal">
                        <group attrs="{'invisible': [('show_employee_feedback_full', '=', False)]}">
                            <div class="row">
                                <div class="col-12 d-flex justify-content-between">
                                    <h3>Employee's Feedback</h3>
                                    <div>
                                        <span class="text-right" attrs="{'invisible': ['|', ('employee_feedback_published', '=', True), ('can_see_employee_publish', '=', False)]}">Not Visible to Manager</span>
                                        <span class="text-right" attrs="{'invisible': ['|', ('employee_feedback_published', '=', False), ('can_see_employee_publish', '=', False)]}">Visible to Manager</span>
                                        <field name="employee_feedback_published" nolabel="1"  class="ml-2 mr-0"  widget="boolean_toggle" attrs="{'invisible': [('can_see_employee_publish', '=', False)]}"/>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <field name="employee_feedback" class="o_appraisal_template" nolabel="1" attrs="{
                                        'invisible': [
                                            '&amp;',
                                            ('employee_feedback_published', '=', False),
                                            ('can_see_employee_publish', '=', False)
                                        ],
                                        'readonly': [
                                            '|',
                                            ('state', 'not in', ['new', 'pending']),
                                            ('can_see_employee_publish', '=', False)]}"/>
                                </div>
                            </div>
                        </group>

                        <group attrs="{'invisible': [('show_manager_feedback_full', '=', False)]}">
                            <div class="row">
                                <div class="col-12 d-flex justify-content-between">
                                    <h3>Manager's Feedback</h3>
                                    <div>
                                        <span class="text-right" attrs="{'invisible': ['|', '|', ('manager_feedback_published', '=', True), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}">Not Visible to Employee</span>
                                        <span class="text-right" attrs="{'invisible': ['|', '|', ('manager_feedback_published', '=', False), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}">Visible to Employee</span>
                                        <field name="manager_feedback_published" nolabel="1" widget="boolean_toggle" class="ml-2 mr-0" attrs="{'invisible': ['|', ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}"/>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <field name="manager_feedback" class="o_appraisal_template" nolabel="1" attrs="{
                                        'invisible': [
                                            '&amp;',
                                            ('manager_feedback_published', '=', False),
                                            ('can_see_manager_publish', '=', False)
                                        ],
                                        'readonly': [
                                            '|',
                                            ('can_see_manager_publish', '=', False),
                                            ('state', 'not in', ['new', 'pending'])]}"/>
                                </div>
                            </div>
                        </group>

                        <group attrs="{'invisible':['|', ('show_employee_feedback_full', '=', True), ('show_manager_feedback_full', '=', True)]}">
                            <group >
                                <div class="row">
                                    <div class="col-12 d-flex justify-content-between">
                                        <h3>Employee's Feedback</h3>
                                        <div>
                                            <span class="text-right" attrs="{'invisible': ['|', '|', ('employee_feedback_published', '=', True), ('can_see_employee_publish', '=', False), ('state', '=', 'done')]}">Not Visible to Manager</span>
                                            <span class="text-right" attrs="{'invisible': ['|', '|', ('employee_feedback_published', '=', False), ('can_see_employee_publish', '=', False), ('state', '=', 'done')]}">Visible to Manager</span>
                                            <field name="employee_feedback_published" nolabel="1" widget="boolean_toggle" class="ml-2 mr-0" attrs="{'invisible': ['|', ('can_see_employee_publish', '=', False), ('state', '=', 'done')]}"/>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <span class="o_appraisal_overlay o_highlight" attrs="{'invisible': ['|', ('employee_feedback_published', '=', True), ('employee_feedback_template', '=', False)]}">
                                            <div>Self Assessment will show here</div>
                                            <div>once published</div>
                                        </span>
                                        <field name="employee_feedback" nolabel="1" class="o_appraisal_template" attrs="{
                                            'invisible': [('employee_feedback_published', '=', False)],
                                            'readonly': [
                                            '|',
                                            ('can_see_employee_publish', '=', False),
                                            ('state', 'not in', ['new', 'pending'])]}"/>
                                        <field name="employee_feedback_template" attrs="{
                                            'invisible': [('employee_feedback_published', '=', True)]}"
                                            class="o_appraisal_blur"/>
                                    </div>
                                </div>
                            </group>
                            <group>
                                <div class="row">
                                    <div class="col-12 d-flex justify-content-between">
                                        <h3>Manager's Feedback</h3>
                                        <div>
                                            <span class="col-4 text-right" attrs="{'invisible': ['|', '|', ('manager_feedback_published', '=', True), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}">Not Visible to Employee</span>
                                            <span class="col-4 text-right" attrs="{'invisible': ['|', '|', ('manager_feedback_published', '=', False), ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}">Visible to Employee</span>
                                            <field name="manager_feedback_published" nolabel="1" widget="boolean_toggle" class="ml-2 mr-0" attrs="{'invisible': ['|', ('can_see_manager_publish', '=', False), ('state', '=', 'done')]}"/>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <span class="o_appraisal_overlay" attrs="{'invisible': ['|', ('manager_feedback_published', '=', True), ('manager_feedback_template', '=', False)]}">
                                            <div>Manager Assessment will show here</div>
                                            <div>once published</div>
                                        </span>
                                        <field name="manager_feedback" nolabel="1" class="o_appraisal_template" attrs="{
                                            'invisible': [('manager_feedback_published', '=', False)],
                                            'readonly': [
                                            '|',
                                            ('can_see_manager_publish', '=', False),
                                            ('state', 'not in', ['new', 'pending'])]}"/>
                                        <field name="manager_feedback_template" attrs="{
                                            'invisible': [('manager_feedback_published', '=', True)]}"
                                            class="o_appraisal_blur"/>
                                    </div>
                                </div>
                            </group>
                        </group>
                    </page>
                    <page string="Private Note" attrs="{'invisible': &quot;[('employee_user_id', '=', uid)]&quot;}">
                        <field name="note" attrs="{'readonly': &quot;['|', ('state', '=', 'done'), '&amp;', ('manager_user_ids', 'not in', uid), ('is_appraisal_manager', '=', False)]&quot;}"/>
                    </page>
                </notebook>
                <field name="active" invisible="1"/>
                <field name="meeting_ids" invisible="1"/>
                <field name="can_see_employee_publish" invisible="1"/>
                <field name="can_see_manager_publish" invisible="1"/>
                <field name="waiting_feedback" invisible="1"/>
                <field name="show_employee_feedback_full" invisible="1"/>
                <field name="show_manager_feedback_full" invisible="1"/>
                <field name="is_appraisal_manager" invisible="1"/>
                <field name="is_implicit_manager" invisible="1"/>
                <field name="employee_autocomplete_ids" invisible="1"/>
          </sheet>
          <div class="oe_chatter">
              <field name="message_follower_ids" groups="base.group_user"/>
              <field name="activity_ids"/>
              <field name="message_ids"/>
          </div>
          </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_hr_appraisal_tree">
        <field name="name">hr.appraisal.tree</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <tree string="Appraisal" sample="1">
                <field name="active" invisible="1"/>
                <field name="employee_id" string="Name" widget="many2one_avatar_employee"/>
                <field name="date_close" widget="remaining_days" attrs="{'invisible': [('state', 'in', ['done', 'cancel'])]}"/>
                <field name="date_final_interview" widget="remaining_days"/>
                <field name="state" widget="badge" decoration-info="state in ('new','pending')" decoration-success="state == 'done'"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </tree>
        </field>
    </record>

   <record id="hr_appraisal_search" model="ir.ui.view">
        <field name="name">hr.appraisal.search</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <search string="Search Appraisal">
                <field name="employee_id"/>
                <filter string="To Do" name="my_appraisals" domain="['|', ('state', '=', 'new'), ('state', '=', 'pending')]"/>
                <filter string="Done" name="filter_done" domain="[('state', '=', 'done')]"/>
                <separator/>
                <filter string="My Appraisals" name="my_appraisals" domain="[('employee_id.user_id', '=', uid)]"/>
                <filter string="People I Manage" name="people_i_manage" domain="[('employee_id.parent_id.user_id', '=', uid)]" groups="hr_appraisal.group_hr_appraisal_user"/>
                <separator/>
                <filter string="Late" name="late_appraisals" domain="[('state', 'not in', ['cancel', 'done']), ('date_close', '&lt;', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <separator/>
                <group expand='0' string='Group by...'>
                    <filter string='Employee' name="employee" icon="fa-user" domain="[]" context="{'group_by': 'employee_id'}"/>
                    <filter name="groupby_state" context="{'group_by': 'state'}"/>
               </group>
                <searchpanel>
                    <field name="company_id" groups="base.group_multi_company" icon="fa-building"/>
                    <field name="department_id" icon="fa-users"/>
                    <field name="state"/>
                </searchpanel>
           </search>
        </field>
    </record>

    <record id="hr_appraisal_kanban" model="ir.ui.view">
        <field name="name">hr.appraisal.kanban</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <kanban quick_create="false" default_order="state desc, date_close" class="o_hr_employee_kanban" js_class="appraisal_kanban" sample="1">
                <field name="state"/>
                <field name="activity_ids" />
                <field name="activity_state" />
                <field name="manager_ids" />
                <field name="waiting_feedback"/>
                <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click o_kanban_record_has_image_fill o_kanban_card_appraisal">
                            <field name="avatar_128" widget="image" class="o_kanban_image_fill_left" options="{'background': true, 'preventClicks': false}"/>
                            <div class="ribbon ribbon-top-right" attrs="{'invisible': [('state', '!=', 'done')]}">
                                <span class="bg-success">Done</span>
                            </div>
                            <div class="ribbon ribbon-top-right" attrs="{'invisible': ['|', ('state', '!=', 'pending'), ('waiting_feedback', '=', True)]}">
                                <span class="bg-info">Ready</span>
                            </div>
                            <div class="ribbon ribbon-top-right" attrs="{'invisible': [('state', '!=', 'cancel')]}">
                                <span class="bg-secondary">Canceled</span>
                            </div>
                            <div class="oe_kanban_details d-flex justify-content-between flex-column">
                                <div>
                                    <div class="o_kanban_record_top align-items-start">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="employee_id"/>
                                            </strong>
                                        </div>
                                    </div>
                                    <div>
                                        <field name="department_id"/>
                                    </div>
                                    <div class="o_kanban_inline_block">
                                        <div attrs="{'invisible': [('state', 'in', ['done', 'cancel'])]}">
                                        <t t-if="record.date_close.raw_value and record.date_close.raw_value &lt; (new Date())" t-set="red">oe_kanban_text_red</t>
                                            <span t-attf-class="#{red}"><field name="date_close"/></span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div>
                                        <t t-if="record.date_final_interview.raw_value and record.state.raw_value != 'new'"><strong><span>Meeting: </span></strong>
                                        <span><i><field name="date_final_interview"/></i></span></t>
                                    </div>
                                    <div class="o_kanban_record_bottom d-flex justify-content-between">
                                        <div class="oe_kanban_bottom_left">
                                            <field name="activity_ids" widget="kanban_activity"/>
                                        </div>
                                        <div class="oe_kanban_bottom_right align-items-end">
                                            <t t-foreach="record.manager_ids.raw_value" t-as="employee">
                                                <img class="oe_kanban_avatar o_appraisal_manager" t-att-src="kanban_image('hr.employee', 'avatar_128', employee)" t-att-title="employee.value" t-att-alt="employee.value" t-att-data-id="employee"/>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="oe_clear"></div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_appraisal_view_activity" model="ir.ui.view">
        <field name="name">hr.appraisal.activity</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <activity string="Appraisal">
                <field name="employee_id"/>
                <templates>
                    <div t-name="activity-box">
                        <img t-att-src="activity_image('hr.employee', 'avatar_128', record.employee_id.raw_value)" t-att-title="record.employee_id.value" t-att-alt="record.employee_id.value"/>
                        <div>
                            <field name="employee_id"/>
                        </div>
                        <div class="text-muted">
                            <t t-if="record.date_close.raw_value and record.date_close.raw_value &lt; (new Date())" t-set="red">text-danger</t>
                            Deadline: <span t-attf-class="#{red}"><i><field name="date_close"/></i></span>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record id="hr_appraisal_view_gantt" model="ir.ui.view">
        <field name="name">hr.appraisal.gantt</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <gantt
                js_class="hr_gantt"
                color="employee_id"
                date_start="date_close"
                date_stop="date_close"
                default_group_by='department_id'>
                <templates>
                    <div t-name="gantt-popover">
                        <div><strong>Date — </strong><t t-esc="userTimezoneStartDate.format('l LT')"/></div>
                    </div>
                </templates>
            </gantt>
        </field>
    </record>

    <record id="hr_appraisal_view_calendar" model="ir.ui.view">
        <field name="name">hr.appraisal.calendar</field>
        <field name="model">hr.appraisal</field>
        <field name="arch" type="xml">
            <calendar date_start="date_close" date_stop="date_close" mode="month" color="employee_id" quick_add="False">
                <field name="display_name"/>
                <field name="employee_id" filters="1" invisible="1"/>
            </calendar>
        </field>
    </record>

    <record model="ir.actions.act_window" id="open_view_hr_appraisal_tree">
        <field name="name">Appraisals</field>
        <field name="res_model">hr.appraisal</field>
        <field name="view_mode">kanban,tree,gantt,calendar,form,activity</field>
        <field name="context">{"search_default_next_month":1}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Schedule an appraisal
          </p><p>
            Plan appraisals with your colleagues, collect and discuss feedback.
          </p>
        </field>
    </record>

    <menuitem name="Appraisals"
        parent="menu_hr_appraisal_root"
        id="menu_open_view_hr_appraisal_tree"
        action="open_view_hr_appraisal_tree"
        sequence="1"
        groups="base.group_user"/>

    <record model="ir.actions.act_window" id="open_view_hr_appraisal_tree2">
        <field name="name">Appraisal</field>
        <field name="res_model">hr.appraisal</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="context">{"search_default_employee_id": [active_id], "default_employee_id": active_id}}</field>
    </record>

    <record model="ir.actions.act_window" id="hr_appraisal_action">
        <field name="name">Appraisal</field>
        <field name="res_model">hr.appraisal</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'search_default_my_appraisals': 1}</field>
    </record>

    <record model="ir.actions.act_window" id="hr_appraisal_action_my">
        <field name="name">My Appraisals</field>
        <field name="res_model">hr.appraisal</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="context">{'search_default_my_appraisals': 1}</field>
    </record>

    <record id="hr_appraisal_action_from_department" model="ir.actions.act_window">
            <field name="name">Appraisal to start</field>
            <field name="res_model">hr.appraisal</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="hr_appraisal_search"/>
            <field name="domain">[('department_id', '=', active_id), ('state', 'in', ['new', 'pending'])]</field>
    </record>
</odoo>
