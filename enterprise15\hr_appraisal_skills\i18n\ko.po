# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal_skills
# 
# Translators:
# <PERSON>, 2021
# <PERSON> Up링크업 <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:44+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_view_form
msgid ""
"<span class=\"o_appraisal_overlay\" attrs=\"{'invisible': [('state', '!=', 'new')]}\">\n"
"                            Skills tab will be active once the appraisal is confirmed.\n"
"                        </span>"
msgstr ""
"<span class=\"o_appraisal_overlay\" attrs=\"{'invisible': [('state', '!=', 'new')]}\">\n"
"                            업무 평가가 완료되면 기술 탭이 활성화됩니다.\n"
"                        </span>"

#. module: hr_appraisal_skills
#. openerp-web
#: code:addons/hr_appraisal_skills/static/src/xml/templates.xml:0
#, python-format
msgid "ADD"
msgstr "추가하기"

#. module: hr_appraisal_skills
#. openerp-web
#: code:addons/hr_appraisal_skills/static/src/xml/templates.xml:0
#, python-format
msgid "ADD NEW SKILLS"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__appraisal_id
msgid "Appraisal"
msgstr "업무 평가 관리"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_uid
msgid "Created by"
msgstr "작성자"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_date
msgid "Created on"
msgstr "작성일자"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__display_name
msgid "Display Name"
msgstr "표시명"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_id
msgid "Employee"
msgstr "임직원"

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "직원 업무 평가"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_skill_id
msgid "Employee Skill"
msgstr "직원 역량"

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal_skill
msgid "Employee Skills"
msgstr "직원 역량"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__justification
msgid "Justification"
msgstr "근거"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__manager_ids
msgid "Manager"
msgstr "관리자"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress"
msgstr "진행"

#. module: hr_appraisal_skills
#: model:ir.model.fields,help:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "아무 것도 모름(0 %)에서 완전히 마스터(100 %)함으로 진행."

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_id
msgid "Skill"
msgstr "업무 능력"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_level_id
msgid "Skill Level"
msgstr "능력 수준"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_type_id
msgid "Skill Type"
msgstr "능력 유형"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal__skill_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_view_form
msgid "Skills"
msgstr "업무 능력"

#. module: hr_appraisal_skills
#: model:ir.model.constraint,message:hr_appraisal_skills.constraint_hr_appraisal_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "동일 능력에 대해 두 가지 수준이 함께 허용되지 않습니다."

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been added."
msgstr "추가되었습니다."

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been deleted."
msgstr "삭제되었습니다."
