# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal_skills
# 
# Translators:
# <PERSON><PERSON><PERSON> V. <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:44+0000\n"
"PO-Revision-Date: 2021-09-14 12:39+0000\n"
"Last-Translator: UAB \"Draugiški sprendimai\" <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_view_form
msgid ""
"<span class=\"o_appraisal_overlay\" attrs=\"{'invisible': [('state', '!=', 'new')]}\">\n"
"                            Skills tab will be active once the appraisal is confirmed.\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal_skills
#. openerp-web
#: code:addons/hr_appraisal_skills/static/src/xml/templates.xml:0
#, python-format
msgid "ADD"
msgstr ""

#. module: hr_appraisal_skills
#. openerp-web
#: code:addons/hr_appraisal_skills/static/src/xml/templates.xml:0
#, python-format
msgid "ADD NEW SKILLS"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__appraisal_id
msgid "Appraisal"
msgstr "Įvertinimas"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_id
msgid "Employee"
msgstr "Darbuotojas"

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "Darbuotojo įvertinimas"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__employee_skill_id
msgid "Employee Skill"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model,name:hr_appraisal_skills.model_hr_appraisal_skill
msgid "Employee Skills"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__justification
msgid "Justification"
msgstr "Pateisinimas"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__manager_ids
msgid "Manager"
msgstr "Vadovas"

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress"
msgstr "Eiga"

#. module: hr_appraisal_skills
#: model:ir.model.fields,help:hr_appraisal_skills.field_hr_appraisal_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_id
msgid "Skill"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_level_id
msgid "Skill Level"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal_skill__skill_type_id
msgid "Skill Type"
msgstr ""

#. module: hr_appraisal_skills
#: model:ir.model.fields,field_description:hr_appraisal_skills.field_hr_appraisal__skill_ids
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.hr_appraisal_skill_view_form
msgid "Skills"
msgstr "Įgūdžiai"

#. module: hr_appraisal_skills
#: model:ir.model.constraint,message:hr_appraisal_skills.constraint_hr_appraisal_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been added."
msgstr ""

#. module: hr_appraisal_skills
#: model_terms:ir.ui.view,arch_db:hr_appraisal_skills.appraisal_skills_update_template
msgid "has been deleted."
msgstr ""
