<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="survey_survey_view_form" model="ir.ui.view">
        <field name="name">survey.survey.view.form</field>
        <field name="model">survey.survey</field>
        <field name="inherit_id" ref="survey.survey_form"/>
        <field name="arch" type="xml">
            <field name="access_mode" position="after">
                <field name="is_appraisal" groups="base.group_no_one"/>
            </field>
        </field>
    </record>

    <record id="survey_user_input_view_tree" model="ir.ui.view">
        <field name="name">survey.user.input.view.tree.inherit.appraisal</field>
        <field name="model">survey.user_input</field>
        <field name="inherit_id" ref="survey.survey_user_input_view_tree"/>
        <field name="priority">32</field>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="scoring_percentage" position="after">
                <button class="oe_stat_button" name="action_open_survey_inputs" icon="fa-external-link" type="object"/>
            </field>
        </field>
    </record>

    <record id="survey_survey_action_appraisal" model="ir.actions.act_window">
        <field name="name">Surveys</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">survey.survey</field>
        <field name="domain">[('is_appraisal', '=', True)]</field>
        <field name="view_mode">kanban,tree,activity,form</field>
        <field name="context">{'default_is_appraisal': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new survey
            </p><p>
                You can create surveys used for appraisals. Design easily your appraisal,
                send invitations and analyze answers.
            </p>
        </field>
    </record>

    <!--Hide appraisal surveys from survey app-->
    <record id="survey.action_survey_form" model="ir.actions.act_window">
        <field name="domain">[('is_appraisal', '=', False)]</field>
    </record>

    <record id="survey.action_survey_question_form" model="ir.actions.act_window">
        <field name="domain">[('is_page', '=', False), ('survey_id.is_appraisal', '=', False)]</field>
    </record>

    <record id="survey.survey_question_answer_action" model="ir.actions.act_window">
        <field name="domain">[('survey_id.is_appraisal', '=', False)]</field>
    </record>

    <record id="survey.action_survey_user_input" model="ir.actions.act_window">
        <field name="domain">[('survey_id.is_appraisal', '=', False)]</field>
    </record>

    <record id="survey.survey_user_input_line_action" model="ir.actions.act_window">
        <field name="domain">[('survey_id.is_appraisal', '=', False)]</field>
    </record>

    <menuitem
        id="menu_hr_appraisal_surveys"
        name="Surveys"
        action="survey_survey_action_appraisal"
        parent="hr_appraisal.menu_hr_appraisal_configuration"
        sequence="6"
        groups="hr_appraisal.group_hr_appraisal_manager"/>
</odoo>
