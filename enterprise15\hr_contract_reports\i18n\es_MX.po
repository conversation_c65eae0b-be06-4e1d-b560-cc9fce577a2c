# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_reports
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.3+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 09:31+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_employee_exit
msgid "# Departure Employee"
msgstr "# Salida de empleado"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_new_employee
msgid "# New Employees"
msgstr "# Nuevos empleados"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Company"
msgstr "Empresa"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_id
msgid "Contract"
msgstr "Contrato"

#. module: hr_contract_reports
#: model:ir.model,name:hr_contract_reports.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Reporte de análisis de contratos y empleados"

#. module: hr_contract_reports
#: model:ir.ui.menu,name:hr_contract_reports.menu_report_contract_employee_all
msgid "Contracts"
msgstr "Contratos"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date
msgid "Date"
msgstr "Fecha"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_start
msgid "Date First Contract Started"
msgstr "Fecha en la que empezó el primer contrato"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date_end_contract
msgid "Date Last Contract Ended"
msgstr "Fecha de finalización del último contrato"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Department"
msgstr "Departamento"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Departure Employees"
msgstr "Salida de empleados"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__departure_reason_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Departure Reason"
msgstr "Motivo de salida"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__age_sum
msgid "Duration Contract"
msgstr "Duración del contrato"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Employee"
msgstr "Empleado"

#. module: hr_contract_reports
#: model:ir.actions.act_window,name:hr_contract_reports.contract_employee_report_action
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Employees Analysis"
msgstr "Análisis de empleados"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Last 365 Days"
msgstr "Últimos 365 días"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Last Contract Ended"
msgstr "Último contrato terminado"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Max Date in Months"
msgstr "Fecha máxima en meses"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Mean Contract Duration"
msgstr "Duración media del contrato"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Min Date in Months"
msgstr "Fecha mínima en meses"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__start_date_months
msgid "Months of first date of this month since 01/01/1970"
msgstr "Meses de la primera fecha de este mes desde 01/01/1970"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__end_date_months
msgid "Months of last date of this month since 01/01/1970"
msgstr "Meses de la última fecha de este mes desde 01/01/1970"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "New Employees"
msgstr "Nuevos empleados"

#. module: hr_contract_reports
#: model_terms:ir.actions.act_window,help:hr_contract_reports.contract_employee_report_action
msgid "This report performs analysis on your contracts."
msgstr "Este reporte realiza un análisis de sus contratos."

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Total Contract Length"
msgstr "Duración total del contrato"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Total Contracts"
msgstr "Números de contrato"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Total Employees"
msgstr "Número de empleados"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Turnover Rate"
msgstr "Tasa de rotación"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__wage
msgid "Wage"
msgstr "Salario"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "months"
msgstr "meses"
