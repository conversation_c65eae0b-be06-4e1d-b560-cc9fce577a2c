# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_reports
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <fold<PERSON><PERSON>@nexterp.ro>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.3+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 09:31+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_employee_exit
msgid "# Departure Employee"
msgstr "# Plecare Angajat"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__count_new_employee
msgid "# New Employees"
msgstr "# Angajati Noi"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Company"
msgstr "Companie"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_id
msgid "Contract"
msgstr "Contract"

#. module: hr_contract_reports
#: model:ir.model,name:hr_contract_reports.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Raport privind contractele și analiza angajaților"

#. module: hr_contract_reports
#: model:ir.ui.menu,name:hr_contract_reports.menu_report_contract_employee_all
msgid "Contracts"
msgstr "Contracte"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date
msgid "Date"
msgstr "Dată"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__contract_start
msgid "Date First Contract Started"
msgstr "Data începerii primului contract"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__date_end_contract
msgid "Date Last Contract Ended"
msgstr "Data încheierii ultimului contract"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Department"
msgstr "Departament"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Departure Employees"
msgstr "Plecări Angajați"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__departure_reason_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Departure Reason"
msgstr "Motiv Plecare"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__age_sum
msgid "Duration Contract"
msgstr "Durată Contract"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Employee"
msgstr "Angajat"

#. module: hr_contract_reports
#: model:ir.actions.act_window,name:hr_contract_reports.contract_employee_report_action
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Employees Analysis"
msgstr "Analiză Angajați"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_search
msgid "Last 365 Days"
msgstr "Ultimile 365 de zile"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Last Contract Ended"
msgstr "Ultimul contract încheiat"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Max Date in Months"
msgstr "Data maximă în luni"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Mean Contract Duration"
msgstr "Durata medie a contractului"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Min Date in Months"
msgstr "Data minimă în luni"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__start_date_months
msgid "Months of first date of this month since 01/01/1970"
msgstr "Lunile primei date din această lună de la 01/01/1970"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__end_date_months
msgid "Months of last date of this month since 01/01/1970"
msgstr "Lunile ultimei date din această lună de la 01/01/1970"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "New Employees"
msgstr "Angajati Noi"

#. module: hr_contract_reports
#: model_terms:ir.actions.act_window,help:hr_contract_reports.contract_employee_report_action
msgid "This report performs analysis on your contracts."
msgstr "Acest raport efectuează o analiză a contractelor dvs."

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Total Contract Length"
msgstr "Lungime Totală Contract"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Total Contracts"
msgstr "Contracte Totale"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Total Employees"
msgstr "Angajați Totali"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "Turnover Rate"
msgstr "Rată cifră afaceri"

#. module: hr_contract_reports
#: model:ir.model.fields,field_description:hr_contract_reports.field_hr_contract_employee_report__wage
msgid "Wage"
msgstr "Salariu"

#. module: hr_contract_reports
#: model_terms:ir.ui.view,arch_db:hr_contract_reports.contract_employee_report_view_dashboard
msgid "months"
msgstr "luni"
