# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_contract_salary
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:50+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer
msgid "${object.company_id.name} : Job Offer - ${object.name}"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_data_notification_email_send_offer
msgid "${object.subject}"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "+ International Communication"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "+32 2 290 34 90"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "00.00.00-000.00"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "110 €"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "150 €"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div>\n"
"                    <h2>Congratulations!</h2>\n"
"                    <p>You can configure your salary package by clicking on the link below.</p>\n"
"                    <br/><br/>\n"
"                    <center>\n"
"                        <a href=\"${ctx.get('salary_package_url')}\" target=\"_blank\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">Configure your package</a>\n"
"                        <br/><br/><br/>\n"
"                    </center>\n"
"\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_data_notification_email_send_offer
msgid ""
"<?xml version=\"1.0\"?>\n"
"<html>\n"
"                <head/>\n"
"                % set record = ctx.get('record')\n"
"                % set company = record and record.company_id or user.company_id\n"
"                <body style=\"margin: 0; padding: 0;\">\n"
"                <table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed\" summary=\"o_mail_notification\">\n"
"                    <tbody>\n"
"\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\">\n"
"                                  <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                                      ${object.record_name}\n"
"                                  </span>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                  <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\"/>\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                ${object.body | safe}\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                ${company.name}<br/>\n"
"                                ${company.phone or ''}\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                % if company.email:\n"
"                                <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                                % endif\n"
"                                % if company.website:\n"
"                                    <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                                        ${company.website}\n"
"                                    </a>\n"
"                                % endif\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <tr>\n"
"                        <td align=\"center\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-car fa-fw\"/> Transport"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-crosshairs fa-fw fa-lg\"/> Commissions"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-cutlery fa-fw\"/> Meal Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-gift fa-fw fa-lg\"/> 13<sup>th</sup> Month"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-globe fa-fw fa-lg\"/> Internet Subscription"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-leaf fa-fw\"/> Eco Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-male fa-fw fa-lg\"/> Representation Fees"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> ATN Company Car"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> ATN Internet"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> ATN Phone Subscription"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Meal Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Social contribution"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Special social contribution"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Withholding Tax"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-mobile fa-fw fa-lg\"/> Phone Subscription"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> ATN Company Car"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> ATN Internet"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> ATN Phone Subscription"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Employment Bonus"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Representation Fees"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Withholding Tax Reductions"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-suitcase fa-fw\"/> Double Holidays"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-suitcase fa-fw\"/> Legal Leaves"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-tint fa-fw\"/> Fuel Card"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<i class=\"fa fa-check-circle-o mr8\"/>Congratulations"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block mt8\" id=\"internet_tooltip\">Your internet "
"subscription will be paid up to this amount.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block mt8\" id=\"mobile_tooltip\">Your mobile phone "
"subscription will be paid up to this amount.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block mt8\" name=\"holidays_20_days\" style=\"display: inline;\"> You will receive 20 days of paid leaves. However, if you only use </span>\n"
"                                                        <span class=\"help-block\" style=\"display: inline;\" name=\"holidays_amount\">X</span>\n"
"                                                        <span class=\"help-block\" style=\"display: inline;\"> days by the end of the year, you will receive a compensation of approximately </span>\n"
"                                                        <span class=\"help-block\" style=\"display: inline;\" name=\"compensation_amount\">x</span>\n"
"                                                        <span class=\"help-block\" style=\"display: inline;\"> €.</span><br/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block mt8\">Enter the monthly amount you spend on public "
"transportation to go to work. The approximative amount reimbursed by the "
"employer is calculated accordingly.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block mt8\">The cost of the car is depreciated over 5 "
"years. For example, a new car may cost 500€/month the first year but its "
"cost will decrease at each yearly appraisal to reach 100€/month after 5 "
"years.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block\">Every month, you'll get 7.45€ per worked day on your meal voucher card.\n"
"</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block\">This commission is only effective if the target "
"is reached. The commissions are paid using warrants that you receive every "
"three months.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<span class=\"help-block\">This is a monthly net amount.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block\">You'll receive this amount around December. This "
"amount may be different based on your working schedule and the number of "
"worked days during the year.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block\">You'll receive this amount around June. This "
"amount may be different based on your working schedule and the number of "
"worked days during the year.</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"help-block\">You'll receive this amount in the form of Eco "
"Vouchers around June. This amount may be different based on your working "
"schedule and the number of worked days during the year. </span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "<span class=\"o_stat_text\">Contracts</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Accept Request"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_access_token_end_date
msgid "Access Token Validity Date"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_active_employee
msgid "Active"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_employee_additional_note
msgid "Additional Note"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Address"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_applicant
msgid "Applicant"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "BASIC SALARY"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "BE10 3631 0709 4104"
msgstr ""

#. module: hr_contract_salary
#: selection:hr.employee,certificate:0
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Bachelor"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Bank account"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Batman"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Birthdate"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Cash"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Certificate"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_employee_certificate
msgid "Certificate Level"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "City"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "Click here to create new contracts."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Company Car"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Computer Science"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_access_token_consumed
msgid "Consumed Access Token"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Contact Information"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.action_hr_contract_templates
#: model:ir.ui.menu,name:hr_contract_salary.hr_menu_contract_templates
msgid "Contract Templates"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job_contract_update_template_id
msgid "Contract Update Document Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_signature_request_template_id
msgid "Contract template that the employee will have to sign."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Country"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Create Offer"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Customize your salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings_access_token_validity
msgid "Default Access Token Validity Duration"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant_default_contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job_default_contract_id
msgid "Default Contract for New Employees"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_applicant_default_contract_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job_default_contract_id
msgid "Default contract used when making an offer to an applicant."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job_signature_request_template_id
msgid ""
"Default document that the applicant will have to sign to accept a contract "
"offer."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job_contract_update_template_id
msgid ""
"Default document that the employee will have to sign to update his contract."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Details"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled Children"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled Spouse"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled people <span class=\"help-block\">&amp;le; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled seniors <span class=\"help-block\">&amp;ge; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Divorced"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signature_request_template_id
msgid "Document Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_employee_view_form
msgid "Education"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Email"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_employee_emergency_contact
msgid "Emergency Contact"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_employee_emergency_phone
msgid "Emergency Phone"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_employee
msgid "Employee"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Employee Total Cost"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Female"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_employee_study_field
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Field of Study"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "GROSS SALARY"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Gross"
msgstr ""

#. module: hr_contract_salary
#: model:signature.item.party,name:hr_contract_salary.signature_item_party_job_responsible
msgid "HR Responsible"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid ""
"He/She will review your contract.<br/> Feel free to contact him/her if you "
"have further questions."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Hide <i class=\"fa fa-chevron-up\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"If you want to choose another car model, please contact your HR manager."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "In case of emergency"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_job
msgid "Job Position"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Legal Leaves"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Male"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Marital Status"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Married / Legal Cohabitant"
msgstr ""

#. module: hr_contract_salary
#: selection:hr.employee,certificate:0
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Master"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Advantages"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Advantages in Cash"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Benefits"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Equivalent"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "NET SALARY"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Name"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "National Identification Number"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Nationality"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Nature"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net calculation"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job_signature_request_template_id
msgid "New Contract Document Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "No"
msgstr ""

#. module: hr_contract_salary
#: code:addons/hr_contract_salary/controllers/main.py:293
#, python-format
msgid ""
"No HR responsible defined on the job position. Please contact an "
"administrator."
msgstr ""

#. module: hr_contract_salary
#: code:addons/hr_contract_salary/models/hr_contract.py:110
#, python-format
msgid "No home address defined on the employee!"
msgstr ""

#. module: hr_contract_salary
#: code:addons/hr_contract_salary/controllers/main.py:290
#, python-format
msgid ""
"No signature template defined on the job position. Please contact the HR "
"responsible."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Number of Dependent Children"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Number of Disabled Children"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_origin_contract_id
msgid "Origin Contract"
msgstr ""

#. module: hr_contract_salary
#: selection:hr.employee,certificate:0
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Other"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Other Dependent People"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "People <span class=\"help-block\">&amp;le; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Person to call"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Phone number"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"Please note that this information may be inaccurate and should be used for "
"reference only.<br/> The amounts are calculated  based on a full time "
"permanent contract."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_job_view_form
msgid "Preview Contract"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant_proposed_contracts
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Proposed Contracts"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant_proposed_contracts_count
msgid "Proposed Contracts Count"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Public Transportation"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Refuse Request"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signature_request_ids
msgid "Requested Signatures"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Review Contract &amp; Sign"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Salary Package Configurator"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_employee_study_school
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "School"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_access_token
msgid "Security Token"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Send Contract Link"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Send Offer"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Seniors <span class=\"help-block\">&amp;ge; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Show <i class=\"fa fa-chevron-down\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract_signature_request_count
msgid "Signature Request Count"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Single"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Net Revenue"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Other Net Revenue"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "State"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Street"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Street 2"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "TAXABLE INCOME"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Tax status for spouse"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract_origin_contract_id
msgid "The contract from which this contract has been duplicated."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"The number of days you get depends on your working schedule, the number of "
"days used in your previous job and the number of months worked previously."
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Total"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Validity duration for salary package requests for new applicants"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Widower"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "With Income"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Without Income"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Yearly Advantages"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Yearly Advantages in Cash"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Yes"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Your Personal Information"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "Your contract has been sent to:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Zip Code"
msgstr ""

#. module: hr_contract_salary
#: model:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "day(s)"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_res_config_settings
msgid "res.config.settings"
msgstr ""
