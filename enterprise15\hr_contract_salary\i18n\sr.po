# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_contract_salary
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__signatures_count
msgid "# Signatures"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "# disabled people (&lt;65):"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "# disabled seniors (&gt;=65):"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "# people (&lt;65):"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "# seniors (&gt;=65):"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer
#: model:mail.template,subject:hr_contract_salary.mail_template_send_offer_applicant
msgid "${object.company_id.name} : Job Offer - ${object.name}"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "+ International Communication"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "+32 2 290 34 90"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "/year"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "00.00.00-000.00"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "110 €"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "13th Month:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "150 €"
msgstr ""

#. module: hr_contract_salary
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer
#: model:mail.template,body_html:hr_contract_salary.mail_template_send_offer_applicant
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <h2>Congratulations!</h2>\n"
"    You can configure your salary package by clicking on the link below.\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a href=\"${ctx.get('salary_package_url')}\" target=\"_blank\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Configure your package</a>\n"
"    </div>\n"
"</div>\n"
"        "
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-crosshairs fa-fw fa-lg\"/> Commissions"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-cutlery fa-fw\"/> Meal Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-gift fa-fw fa-lg\"/> 13<sup>th</sup> Month"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-globe fa-fw fa-lg\"/> Internet Subscription"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-leaf fa-fw\"/> Eco Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Benefit in Kind (Company Car)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Benefit in Kind (Internet)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<i class=\"fa fa-1x fa-minus fa-fw\"/> Benefit in Kind (Phone Subscription)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Intellectual Property"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<i class=\"fa fa-1x fa-minus fa-fw\"/> Intellectual Property Income "
"Deduction"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Meal Vouchers"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Social contribution"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Special social contribution"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-minus fa-fw\"/> Withholding Tax"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Benefit in Kind (Company Car)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Benefit in Kind (Internet)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Benefit in Kind Phone (Subscription)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Employment Bonus"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Intellectual Property"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Representation Fees"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-plus fa-fw\"/> Withholding Tax Reductions"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-suitcase fa-fw\"/> Holiday Bonus"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-suitcase fa-fw\"/> Legal Leaves"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "<i class=\"fa fa-1x fa-tint fa-fw\"/> Fuel Card"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "<i class=\"fa fa-check-circle-o mr8\"/>Congratulations"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"col-md-3 o_form_label col-form-label\">\n"
"                                                    <i class=\"fa fa-1x fa-male fa-fw fa-lg\"/> Representation Fees\n"
"                                                </span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"col-md-3 o_form_label col-form-label\">\n"
"                                                    <i class=\"fa fa-1x fa-puzzle-piece fa-fw fa-lg\"/> Intellectual Property\n"
"                                                </span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"col-md-3 o_form_label col-form-label\">\n"
"                                                <i class=\"fa fa-1x fa-car fa-fw\"/> Transport\n"
"                                            </span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"col-md-3 o_form_label col-form-label\">\n"
"                                                <i class=\"fa fa-1x fa-mobile fa-fw fa-lg\"/> Phone Subscription\n"
"                                            </span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\" id=\"internet_tooltip\">Enter the amount of "
"your internet subscription invoice. If you have a pack only the internet "
"part of your operator subscription will be paid by the employer.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\" id=\"ip_tooltip\">As Odoo invests massively in"
" developing and writing Intellectual Property content, we benefit from "
"fiscal ruling allowing us to pay a part of employee's salary as an "
"intellectual property income instead of a regular salary. This amount will "
"be paid in cash every month and is less taxed.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\" id=\"mobile_tooltip\">Your mobile phone "
"subscription will be paid up to this amount.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\" name=\"holidays_20_days\" style=\"display: inline;\"> You will receive 20 days of paid leaves. However, if you only use </span>\n"
"                                                        <span class=\"form-text\" style=\"display: inline;\" name=\"holidays_amount\">X</span>\n"
"                                                        <span class=\"form-text\" style=\"display: inline;\"> days by the end of the year, you will receive a compensation of approximately </span>\n"
"                                                        <span class=\"form-text\" style=\"display: inline;\" name=\"compensation_amount\">x</span>\n"
"                                                        <span class=\"form-text\" style=\"display: inline;\"> €.</span><br/>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\">Enter the monthly amount that should be "
"reimbursed by your employer on transportation (other than public) to go to "
"work.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\">Enter the monthly amount you spend on public "
"transportation to go to work. The approximative amount reimbursed by the "
"employer is calculated accordingly.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text mt8\">The cost of a car is depreciated by 8% per "
"year, for 7 years. So, your gross salary increases automatically to keep the"
" same employee budget. Once a car has 7 years, the car is fully depreciated "
"and the only remaining costs are the maintenance, insurances and "
"taxes.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text\">Every month, you'll get 7.45€ per worked day on your meal voucher card.\n"
"</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text\">This is a monthly net amount, taken into account "
"in your payslip.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text\">You'll receive this amount around December. This "
"amount may be different based on your working schedule and the number of "
"worked days during the year.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text\">You'll receive this amount around June. This "
"amount may be different based on your working schedule and the number of "
"worked days during the year.</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"<span class=\"form-text\">You'll receive this amount in the form of Eco "
"Vouchers around June. This amount may be different based on your working "
"schedule and the number of worked days during the year. </span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Salary Package Configurator</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "<span class=\"o_stat_text\">Contracts</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Reviews</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Accept Request"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__access_token_end_date
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__access_token_end_date
msgid "Access Token Validity Date"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__active_employee
msgid "Active Employee"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Address"
msgstr "Адреса"

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_applicant
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__applicant_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__applicant_id
msgid "Applicant"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Apply Now"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.server,name:hr_contract_salary.ir_cron_clean_redundant_salary_data_ir_actions_server
#: model:ir.cron,cron_name:hr_contract_salary.ir_cron_clean_redundant_salary_data
#: model:ir.cron,name:hr_contract_salary.ir_cron_clean_redundant_salary_data
msgid "Archive/Delete redundant generated salary data"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "BASIC SALARY"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "BE10 3631 0709 4104"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Bachelor"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Bank Account Number:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Bank account"
msgstr "Банковни рачун"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Batman"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Birth"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Birthdate"
msgstr "Датум рођења"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Birthdate:"
msgstr ""

#. module: hr_contract_salary
#: selection:generate.simulation.link,contract_type:0
#: selection:hr.contract,contract_type:0
msgid "CDD"
msgstr ""

#. module: hr_contract_salary
#: selection:generate.simulation.link,contract_type:0
#: selection:hr.contract,contract_type:0
msgid "CDI"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "CO2 Emission: <span name=\"co2\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__new_car
msgid "Can request a new car"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Cancel"
msgstr "Otkaži"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Car:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Cash"
msgstr "Gotovina"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Certificate"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Certificate Level:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Citizenship and Other Information"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "City"
msgstr "Grad"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "City:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.actions.act_window,help:hr_contract_salary.action_hr_contract_templates
msgid "Click here to create new contracts."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Commission on Target:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Company Car"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Computer Science"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Configure the simulation"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__access_token_consumed
msgid "Consumed Access Token"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Contact Information"
msgstr "Kontakt Info"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Contract Information:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__contract_id
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__default_contract_id
msgid "Contract Template"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.action_hr_contract_templates
#: model:ir.ui.menu,name:hr_contract_salary.hr_menu_contract_templates
msgid "Contract Templates"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__contract_type
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_type
msgid "Contract Type"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Contract Type:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_update_template_id
msgid "Contract Update Document Template"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Contracts Reviews"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Country"
msgstr "Zemlja"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Country of Birth:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Country of birth"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Country:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__create_uid
msgid "Created by"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Customize your salary"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_res_config_settings__access_token_validity
msgid "Default Access Token Validity Duration"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_job__default_contract_id
msgid "Default Contract for New Employees"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__default_contract_id
#: model:ir.model.fields,help:hr_contract_salary.field_hr_job__default_contract_id
msgid "Default contract used when making an offer to an applicant."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__sign_template_id
msgid ""
"Default document that the applicant will have to sign to accept a contract "
"offer."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__contract_update_template_id
msgid ""
"Default document that the employee will have to sign to update his contract."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Details"
msgstr "Detalji"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled Children"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled Spouse"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Disabled Spouse: No"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Disabled Spouse: Yes"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled people <span class=\"form-text\">&amp;le; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Disabled seniors <span class=\"form-text\">&amp;ge; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Disabled: No"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Disabled: Yes"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__display_name
msgid "Display Name"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Divorced"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Doors Number: <span name=\"door_number\"/>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Eco Vouchers:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Education"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Email"
msgstr "E-mail"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__email_to
msgid "Email To"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Email:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Emergency Contact:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Emergency Phone:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_id
msgid "Employee"
msgstr "Zapošljeni"

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__final_yearly_costs
msgid "Employee Budget"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__employee_contract_id
msgid "Employee Contract"
msgstr "Ugovor Zapošljenog"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Employee Name:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Employer Cost"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Female"
msgstr "Žensko"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Field of Study"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Field of Study:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Fuel Card:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Fuel Type: <span name=\"fuel_type\"/>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Full time equivalent employer cost"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "GROSS SALARY"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_generate_simulation_link
msgid "Gamification Simulation Link"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Gender:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Generate Offer Link"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Generate Simulation Link"
msgstr ""

#. module: hr_contract_salary
#: model:ir.actions.act_window,name:hr_contract_salary.generate_offer_link_action
#: model:ir.actions.act_window,name:hr_contract_salary.generate_simulation_link_action
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Generate a Simulation Link"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Gross"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__hr_responsible_id
#: model:sign.item.role,name:hr_contract_salary.sign_item_role_job_responsible
msgid "HR Responsible"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Has Another Transport Mode: Yes"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Has Company Car: Yes"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Has Public Transportation: Yes"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid ""
"He/She will review your contract.<br/> Feel free to contact him/her if you "
"have further questions."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Hide <i class=\"fa fa-chevron-up\"/>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Holiday Bonus:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Holidays:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "IP percentage:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "IP: No"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Identification No:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Immatriculation Date: <span name=\"immatriculation\"/>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "In case of emergency"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__customer_relation
msgid "In relations with customers"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "International Communication: No"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "International Communication: Yes"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Internet:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Internet: No"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model,name:hr_contract_salary.model_hr_job
msgid "Job Position"
msgstr "Pozicija"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Job Position:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__job_title
msgid "Job Title"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Job Title:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Km Home to Work"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Km Home-Work:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Last Odometer: <span name=\"odometer\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Legal Cohabitant"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Legal Leaves"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Male"
msgstr "Muškarac"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Marital Status"
msgstr "Bračno Stanje"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Marital Status:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Married"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Master"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Meal Vouchers:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Mobile: No"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Mobile: Yes"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__new_car_model_id
msgid "Model"
msgstr "Model"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Model:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Advantages"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Advantages in Cash"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Advantages in Net"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Benefit in Kind"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Equivalent"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Monthly Salary"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "NET SALARY"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Name"
msgstr "Ime"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "National Identification Number"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Nationality"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Nationality:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Nature"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Net calculation"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_template_id
msgid "New Contract Document Template"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "No"
msgstr ""

#. module: hr_contract_salary
#: code:addons/hr_contract_salary/controllers/main.py:592
#, python-format
msgid ""
"No HR responsible defined on the job position. Please contact an "
"administrator."
msgstr ""

#. module: hr_contract_salary
#: code:addons/hr_contract_salary/models/hr_contract.py:192
#: code:addons/hr_contract_salary/wizard/generate_simulation_link.py:131
#, python-format
msgid "No private address defined on the employee!"
msgstr ""

#. module: hr_contract_salary
#: code:addons/hr_contract_salary/controllers/main.py:589
#, python-format
msgid ""
"No signature template defined on the contract. Please contact the HR "
"responsible."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Number of Children:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Number of Dependent Children"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Number of Disabled Children"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "Origin Contract"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Original Link:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Other"
msgstr "Ostalo"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Other Dependent People"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Other Transport Mode"
msgstr ""

#. module: hr_contract_salary
#: selection:generate.simulation.link,contract_type:0
#: selection:hr.contract,contract_type:0
msgid "PFI"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Paid by the employee (Monthly):"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "People <span class=\"form-text\">&amp;le; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__hr_responsible_id
msgid "Person responsible of validating the employee's contracts."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Person to call"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Personal Information"
msgstr "Lične informacije"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Phone number"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Phone:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Place of Birth:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Place of birth"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"Please note that this information may be inaccurate and should be used for "
"reference only.<br/> The amounts are calculated  based on a full time "
"permanent contract."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_applicant_view_form
msgid "Proposed Contracts"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__proposed_contracts_count
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__contract_reviews_count
msgid "Proposed Contracts Count"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Public Transportation"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Recompute"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_view_form
msgid "Refuse Request"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Reimbursed amount:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Representation Fees:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_request_ids
msgid "Requested Signatures"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Review Contract &amp; Sign"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "School"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "School:"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_applicant__access_token
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__access_token
msgid "Security Token"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.generate_simulation_link_view_form
msgid "Send Offer"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Send to Responsible"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Seniors <span class=\"form-text\">&amp;ge; 65 years old</span>"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Show <i class=\"fa fa-chevron-down\"/>"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_hr_contract__sign_request_count
msgid "Sign Request Count"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__url
msgid "Simulation link"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Single"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Birthdate"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Birthdate:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Complete Name:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Name and First Name"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Net Revenue"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Other Net Revenue"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Spouse Professional Situation"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Professional Situation: With Income greater than 225"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Professional Situation: With Income greater than 450"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Professional Situation: With Income less than 225"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Professional Situation: With Pensions less than 450"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Spouse Professional Situation: Without Income"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "State"
msgstr "Stanje"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "State:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Status"
msgstr "Status"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Street"
msgstr "Ulica"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Street 2"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Street2:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Street:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "TAXABLE INCOME"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "TAXED SALARY"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Tax status for spouse"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__origin_contract_id
msgid "The contract from which this contract has been duplicated."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"The number of days you get depends on your working schedule, the number of "
"days used in your previous job and the number of months worked previously."
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,help:hr_contract_salary.field_hr_contract__signatures_count
msgid "The number of signatures on the pdf contract with the most signatures."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"This commission is only effective if the target is reached. This results to "
"a net commission value of"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"This cost simulation is only for information. If you want to choose another "
"car model, please contact your HR manager."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"This is the <b>average salary package</b> for your experience. Top performers get <b>up to 50% more</b>\n"
"                                        and people below expectations get 20% less."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Total"
msgstr "Ukupno"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.res_config_settings_view_form
msgid "Validity duration for salary package requests for new applicants"
msgstr ""

#. module: hr_contract_salary
#: model:ir.model.fields,field_description:hr_contract_salary.field_generate_simulation_link__vehicle_id
msgid "Vehicle"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Wage:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Widower"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "With Income"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "With Income &gt;= 225 €/month"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "With Income &lt; 225 €/month"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "With Pensions &gt;= 450 €/month"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "With Pensions &lt; 450 €/month"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Without Income"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Yearly Advantages"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Yearly Advantages in Cash"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Yes"
msgstr "Da"

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Your Personal Information"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package_thank_you
msgid "Your contract has been sent to:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid ""
"Your gross salary is below the scale. You can change values of the "
"configurator to increase the gross according to the CP 200 scales."
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "Zip Code"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "Zip:"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "add me to the waiting list for a new car"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "close"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.salary_package
msgid "day(s)"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "€/Worked Day"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "€/month"
msgstr ""

#. module: hr_contract_salary
#: model_terms:ir.ui.view,arch_db:hr_contract_salary.hr_contract_salary_email_template
msgid "€/year"
msgstr ""
