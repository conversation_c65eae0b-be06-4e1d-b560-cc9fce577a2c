<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="hr_contract_salary_resume_rule_multi_company" model="ir.rule">
        <field name="name">HR Contract Salary Resume: Multi Company</field>
        <field name="model_id" ref="model_hr_contract_salary_resume"/>
        <field name="domain_force">['|',('structure_type_id','=',False), ('structure_type_id.country_id', 'in', user.env.companies.mapped('country_id').ids)]</field>
    </record>

</odoo>
