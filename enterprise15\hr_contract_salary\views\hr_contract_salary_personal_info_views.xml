<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_salary_personal_info_action" model="ir.actions.act_window">
        <field name="name">Personal Info</field>
        <field name="res_model">hr.contract.salary.personal.info</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="hr_contract_salary_personal_info_view_tree" model="ir.ui.view">
        <field name="name">hr.contract.salary.personal.info.view.tree</field>
        <field name="model">hr.contract.salary.personal.info</field>
        <field name="arch" type="xml">
            <tree string="Salary Package Personal Info">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="res_field_id"/>
                <field name="field" invisible="1"/>
                <field name="structure_type_id"/>
            </tree>
        </field>
    </record>

    <record id="hr_contract_salary_personal_info_view_form" model="ir.ui.view">
        <field name="name">hr.contract.salary.personal.info.view.form</field>
        <field name="model">hr.contract.salary.personal.info</field>
        <field name="arch" type="xml">
            <form string="Salary Package Personal Info">
                <sheet>
                    <div class="oe_title">
                        <label for="name" string="Information"/>
                        <h1><field name="name" placeholder="e.g. Birthdate"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="applies_on"/>
                            <field name="res_field_id" options="{'no_create': True, 'no_edit': True, 'no_open': True}"/>
                            <field name="res_model" invisible="1"/>
                            <field name="field" invisible="1"/>
                            <field name="info_type_id"/>
                            <field name="structure_type_id"/>
                            <field name="placeholder"/>
                            <field name="helper"/>
                            <field name="is_required"/>
                            <field name="impacts_net_salary"/>
                        </group>
                        <group>
                        	<field name="position"/>
                            <field name="display_type"/>
                            <field name="dropdown_selection" attrs="{'invisible': [('display_type', '!=', 'dropdown')]}"/>
                            <field name="value_ids" attrs="{'invisible': [('display_type', 'not in', ['dropdown', 'radio'])]}">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="value"/>
                                </tree>
                            </field>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <menuitem
        id="salary_package_personal_info"
        action="hr_contract_salary_personal_info_action"
        parent="hr_contract_salary.salary_package_menu"
        sequence="2"/>

</odoo>
