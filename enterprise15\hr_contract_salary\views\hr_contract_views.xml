<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_view_form" model="ir.ui.view">
        <field name="name">hr.contract.form</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
        <field name="priority">50</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='wage']" position="after">
                <label name="label_wage_with_holidays" for="wage_with_holidays" attrs="{'invisible': [('holidays', '=', 0.0)]}"/>
                <div class="o_row" name="div_wage_with_holidays" attrs="{'invisible': [('holidays', '=', 0.0)]}">
                    <field name="wage_with_holidays" nolabel="1"/>
                    <span>/ month</span>
                </div>
            </xpath>
            <xpath expr="//button[@name='open_sign_requests']" position="before">
                <button name="action_view_origin_contract" type="object" class="oe_stat_button" icon="fa-book" attrs="{'invisible': ['|', ('origin_contract_id', '=', False), ('is_origin_contract_template', '=', True)]}" help="Previous Contract">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text ml4">Previous Contract</span>
                    </div>
                    <field name="origin_contract_id" invisible="1"/>
                    <field name="is_origin_contract_template" invisible="1"/>
                </button>
            </xpath>
            <div name="button_box" position="inside">
                <button name="action_show_contract_reviews" class="oe_stat_button" icon="fa-book" type="object" help="Contracts Reviews" attrs="{'invisible': ['|', ('employee_id', '=', False), ('contract_reviews_count', '=', 0)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="contract_reviews_count"/></span>
                        <span class="o_stat_text">Reviews</span>
                     </div>
                </button>
            </div>
            <xpath expr="//field[@name='state']" position="before">
                <button name="%(hr_contract_salary.generate_simulation_link_action)d" type="action" string="Generate Simulation Link" attrs="{'invisible': [('employee_id', '=', False)]}"/>
            </xpath>
            <field name="job_id" position="after">
                <label for="wage_on_signature"/>
                <div class="o_row" name="wage">
                    <field name="wage_on_signature" nolabel="1"/>
                    <span>/ month</span>
                </div>
            </field>
             <xpath expr="//group[@name='contract_details']" position="inside">
                <field name="default_contract_id"/>
                <field name="sign_template_id" options="{'no_create': True}" kanban_view_ref="%(sign.sign_template_view_kanban_mobile)s" required="1"/>
                <field name="contract_update_template_id" options="{'no_create': True}" required="1"/>
                <field name="signatures_count" invisible="1"/>
             </xpath>
            <page name="information" position="after">
                <page string="Personal Documents" name="personal_documents" groups="hr.group_hr_manager" attrs="{'invisible': [('employee_id', '=', False)]}">
                    <group>
                        <field name="image_1920"/>
                    </group>
                </page>
            </page>
            <xpath expr="//group[@name='salary']" position="after">
                <group groups="hr.group_hr_manager" name="employee_costs">
                    <label for="final_yearly_costs"/>
                    <div class="o_row">
                        <field name="final_yearly_costs" nolabel="1"/>
                        <span>/ year</span>
                    </div>
                    <label for="monthly_yearly_costs"/>
                    <div class="o_row">
                        <field name="monthly_yearly_costs" nolabel="1"/>
                        <span>/ month</span>
                    </div>
                </group>
            </xpath>
            <xpath expr="//group[@name='yearly_advantages']" position="inside">
                <label for="holidays" attrs="{'invisible': [('country_code', '!=', 'BE')]}"/>
                <div class="o_row" attrs="{'invisible': [('country_code', '!=', 'BE')]}">
                    <field name="holidays" nolabel="1"/>
                    <span>days / year</span>
                </div>
            </xpath>
        </field>
    </record>

    <record id="hr_contract_view_form_contract_templates" model="ir.ui.view">
        <field name="name">hr.contract.form</field>
        <field name="model">hr.contract</field>
        <field name="mode">primary</field>
        <field name="priority">60</field>
        <field name="inherit_id" ref="hr_contract_view_form"/>
        <field name="arch" type="xml">
            <page name="other" position="replace"/>
            <field name="hr_responsible_id" position="after">
                <field name="sign_template_id" kanban_view_ref="%(sign.sign_template_view_kanban_mobile)s"/>
                <field name="contract_update_template_id"/>
            </field>
            <field name="date_start" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="date_end" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="employee_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="state" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
        </field>
    </record>

    <record id="hr_contract_view_tree_contract_templates" model="ir.ui.view">
        <field name="name">hr.contract.tree</field>
        <field name="model">hr.contract</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_tree"/>
        <field name="arch" type="xml">
            <field name="employee_id" position="replace">
                <field name="department_id" readonly="1"/>
            </field>
            <field name="date_start" position="replace"/>
            <field name="date_end" position="replace"/>                
        </field>
    </record>

    <record id="hr_contract_view_kanban" model="ir.ui.view">
        <field name="name">hr.contract.kanban</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_kanban"/>
        <field name="arch" type="xml">
            <field name="employee_id" position="before">
                <field name="sign_request_ids" groups="sign.group_sign_user"/>
            </field>
            <xpath expr="//div[hasclass('oe_kanban_content')]" position="inside">
                <t groups="sign.group_sign_user">
                    <t t-if="record.sign_request_ids.raw_value.length">
                        <div class="oe_kanban_bottom_left">
                            <i class="fa fa-pencil"> <field name="signatures_count"/></i>
                        </div>
                    </t>
                </t>
            </xpath>
        </field>
    </record>

    <record id="action_hr_contract_templates" model="ir.actions.act_window">
        <field name="name">Contract Templates</field>
        <field name="res_model">hr.contract</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('employee_id', '=', False)]</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'sequence': 1, 'view_id': ref('hr_contract_view_tree_contract_templates')}),
            (0, 0, {'view_mode': 'form', 'sequence': 2, 'view_id': ref('hr_contract_view_form_contract_templates')})]"/>
        <field name="search_view_id" ref="hr_contract.hr_contract_view_search"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
              No Template found</p><p>
                Let's create one
          </p>
        </field>
    </record>

    <menuitem
        id="hr_menu_contract_templates"
        name="Templates"
        action="action_hr_contract_templates"
        parent="hr_contract.menu_human_resources_configuration_contract"
        sequence="10"
        groups="hr.group_hr_manager"/>
</odoo>
