<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.contract.salary</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="hr_recruitment.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div name="recruitment_process_div" position="inside">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_left_pane"/>
                    <div class="o_setting_right_pane">
                        <span class="o_form_label">Salary Package Configurator</span>
                        <div class="text-muted">
                            Validity duration for salary package requests for new applicants 
                        </div>
                        <div class="content-group">
                            <field name="access_token_validity"/>
                        </div>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo> 
