<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="generate_simulation_link_view_form" model="ir.ui.view">
          <field name="name">generate.simulation.link.form</field>
          <field name="model">generate.simulation.link</field>
          <field name="arch" type="xml">
            <form string="Generate a Simulation Link">
                <group>
                    <field name="contract_id"/>
                    <field name="job_title"/>
                    <field name="contract_start_date"/>
                    <field name="employee_contract_id" invisible="1"/>
                    <field name="employee_contract_employee_id" invisible="1"/>
                    <field name="employee_id" invisible="1"/>
                    <field name="applicant_id" invisible="1"/>
                    <field name="currency_id" invisible="1"/>
                    <label for="final_yearly_costs"/>
                    <div class="o_row">
                        <field name="final_yearly_costs" nolabel="1"/>
                        <span> /year</span>
                    </div>
                </group>
                <group>
                    <field name="email_to" invisible="True"/>
                    <field name="url" widget="CopyClipboardChar"/>
                </group>
                <footer>
                    <button name="send_offer" type="object" string="Send" class="oe_highlight" data-hotkey="q"/>
                    <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
          </field>
    </record>

    <record id="generate_simulation_link_action" model="ir.actions.act_window">
        <field name="name">Generate a Simulation Link</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">generate.simulation.link</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="generate_simulation_link_view_form"/>
        <field name="context">{'active_id' : active_id, 'active_model': 'hr.contract'}</field>
        <field name="target">new</field>
    </record>

    <record id="generate_offer_link_action" model="ir.actions.act_window">
        <field name="name">Generate a Simulation Link</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">generate.simulation.link</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="generate_simulation_link_view_form"/>
        <field name="context">{'active_id' : active_id, 'active_model': 'hr.applicant'}</field>
        <field name="target">new</field>
    </record>

</odoo>
