# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary_holidays
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.hr_contract_view_form
msgid "<span>Allocations</span>"
msgstr "<span>할당</span>"

#. module: hr_contract_salary_holidays
#: model:ir.model.constraint,message:hr_contract_salary_holidays.constraint_res_company_auto_allocation
msgid ""
"A Time Off Type is required once the Extra Time Off automatic allocation is "
"set."
msgstr "추가 휴가를 자동 할당되도록 설정하려면 휴가 유형이 필요합니다."

#. module: hr_contract_salary_holidays
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_hr_contract__leave_allocation_id
msgid "Allocation"
msgstr "할당"

#. module: hr_contract_salary_holidays
#: code:addons/hr_contract_salary_holidays/controllers/main.py:0
#, python-format
msgid "Allocation automatically created from Contract Signature."
msgstr "계약 서명에서 자동으로 생성된 할당입니다."

#. module: hr_contract_salary_holidays
#: model:ir.model,name:hr_contract_salary_holidays.model_res_company
msgid "Companies"
msgstr "회사"

#. module: hr_contract_salary_holidays
#: model:ir.model,name:hr_contract_salary_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: hr_contract_salary_holidays
#: code:addons/hr_contract_salary_holidays/models/hr_contract.py:0
#, python-format
msgid "Contract has been cancelled."
msgstr "계약이 취소되었습니다."

#. module: hr_contract_salary_holidays
#: model:ir.model,name:hr_contract_salary_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "근로 계약서"

#. module: hr_contract_salary_holidays
#: model:hr.leave.type,name:hr_contract_salary_holidays.holiday_status_eto
msgid "Extra Time Off"
msgstr "추가 휴가"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.res_config_settings_view_form
msgid "Extra Time Off Allocation"
msgstr "추가 휴가 할당"

#. module: hr_contract_salary_holidays
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_company__hr_contract_timeoff_auto_allocation
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_config_settings__hr_contract_timeoff_auto_allocation
msgid "Extra Time Off Allocation on contract signature"
msgstr "계약서 서명에 따른 추가 휴가 할당"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.res_config_settings_view_form
msgid ""
"If the employee requested extra time off in his salary configurator, create "
"automatically the allocation request"
msgstr "직원이 급여 설정에서 추가 연차를 요청한 경우 자동으로 할당 요청을 생성합니다."

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.res_config_settings_view_form
msgid ""
"If the employee requested extra time off in his salary configurator, create "
"automatically the allocation request."
msgstr "직원이 급여 설정에서 추가 연차를 요청한 경우 자동으로 할당 요청을 생성합니다."

#. module: hr_contract_salary_holidays
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_company__hr_contract_timeoff_auto_allocation_type_id
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_config_settings__hr_contract_timeoff_auto_allocation_type_id
msgid "Time Off Type"
msgstr "휴가 유형"
