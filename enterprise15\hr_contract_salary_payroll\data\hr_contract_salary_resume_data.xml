<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_salary.hr_contract_salary_resume_gross" model="hr.contract.salary.resume">
        <field name="impacts_monthly_total" eval="False"/>
    </record>

    <record id="hr_contract_salary_resume_net" model="hr.contract.salary.resume">
        <field name="name">Net</field>
        <field name="value_type">payslip</field>
        <field name="code">NET</field>
        <field name="category_id" ref="hr_contract_salary.hr_contract_salary_resume_category_monthly_salary"/>
        <field name="impacts_monthly_total" eval="True"/>
    </record>
</odoo>
