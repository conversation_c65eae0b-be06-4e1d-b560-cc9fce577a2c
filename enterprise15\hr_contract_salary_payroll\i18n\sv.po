# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary_payroll
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,field_description:hr_contract_salary_payroll.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "Kod"

#. module: hr_contract_salary_payroll
#: model:ir.model.fields.selection,name:hr_contract_salary_payroll.selection__hr_contract_salary_resume__value_type__payslip
msgid "Payslip Value"
msgstr "Belopp på lönebesked"

#. module: hr_contract_salary_payroll
#. openerp-web
#: code:addons/hr_contract_salary_payroll/static/src/xml/brut2net_modal.xml:0
#, python-format
msgid ""
"Please note that this information may be inaccurate and should be used for "
"reference only."
msgstr ""
"Observera att denna information kan vara felaktig och endast bör användas "
"som referens."

#. module: hr_contract_salary_payroll
#: model:ir.model,name:hr_contract_salary_payroll.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr "Lönepaket meritförteckning"

#. module: hr_contract_salary_payroll
#. openerp-web
#: code:addons/hr_contract_salary_payroll/static/src/xml/brut2net_modal.xml:0
#, python-format
msgid "The amounts are calculated  based on a full time permanent contract."
msgstr "Beloppen är beräknade utifrån en tillsvidareanställning på heltid."

#. module: hr_contract_salary_payroll
#. openerp-web
#: code:addons/hr_contract_salary_payroll/static/src/xml/brut2net_modal.xml:0
#, python-format
msgid ""
"There is no defined payroll structure for your contract. Please contact a "
"responsible for more information."
msgstr ""
"Det finns ingen definierad lönestruktur för ditt kontrakt. Vänligen kontakta"
" en ansvarig för mer information."

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,field_description:hr_contract_salary_payroll.field_hr_contract_salary_resume__value_type
msgid "Value Type"
msgstr "Värde Typ"
