# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_sign
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "%s and %s are the signatories."
msgstr "%s ja %s ovat allekirjoittajia."

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"%s requested a new signature on the following documents:<br/><ul>%s</ul>%s"
msgstr ""
"%s pyytänyt uutta allekirjoitusta seuraaviin "
"asiakirjoihin:<br/><ul>%s</ul>%s"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_employee_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.res_users_request_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">Allekirjoituspyynnöt</span>"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__contract_id
msgid "Contract"
msgstr "Sopimus"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_history
msgid "Contract history"
msgstr "Sopimushistoria"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__follower_ids
msgid "Copy to"
msgstr "Kopioi osoitteeseen"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_date
msgid "Created on"
msgstr "Luotu"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Discard"
msgstr "Hylkää"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: hr_contract_sign
#: model:ir.actions.act_window,name:hr_contract_sign.sign_contract_wizard_action
msgid "Document Signature"
msgstr "Asiakirjan allekirjoitus"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "Allekirjoitettavat asiakirjat"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"Allekirjoitettavat asiakirjat. Vain asiakirjat, joissa on 1 tai 2 eri vastuuhenkilöä, ovat valittavissa.\n"
"        Asiakirjat, joissa on yksi vastuuhenkilö, on vain työntekijän allekirjoitettava, kun taas asiakirjat, joissa on kaksi eri vastuuhenkilöä, on sekä työntekijän että vastuuhenkilön allekirjoitettava.\n"
"        "

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_id
msgid "Employee"
msgstr "Työntekijä"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract
msgid "Employee Contract"
msgstr "Työsopimus"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid "Employee Role"
msgstr "Työntekijän rooli"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Employee must be linked to a user and a partner."
msgstr ""

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid ""
"Employee's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr ""
"Työntekijän rooli allekirjoitettavien mallien osalta. Saman roolin on oltava"
" kaikissa malleissa"

#. module: hr_contract_sign
#: model:sign.item.role,name:hr_contract_sign.sign_item_role_job_responsible
msgid "HR Responsible"
msgstr "HR-vastaava"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "On molemmat mallit"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__message
msgid "Message"
msgstr "Viesti"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr ""
"Sopivaa mallia ei löytynyt, varmista, että olet määrittänyt ne oikein."

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "No template available"
msgstr "Mallia ei ole saatavilla"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Only %s has to sign."
msgstr "Vain %s on allekirjoitettava."

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Optional Message..."
msgstr "Valinnainen viesti..."

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_ids
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_history__sign_request_ids
msgid "Requested Signatures"
msgstr "Pyydetyt allekirjoitukset"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Send"
msgstr "Lähetä"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_employee__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_res_users__sign_request_count
msgid "Sign Request Count"
msgstr "Allekirjoituspyyntöjen määrä"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "Allekirjoitusmallin vastuutaho"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "Allekirjoita sopimusasiakirja"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Signature Request"
msgstr "Allekirjoituspyyntö"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "Allekirjoituspyyntö - %s"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__subject
msgid "Subject"
msgstr "Aihe"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "Mallivaroitus"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_res_users
msgid "Users"
msgstr "Käyttäjät"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Write email or search contact..."
msgstr "Kirjoita sähköpostia tai etsi yhteystiedot..."

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/models/hr_contract.py:0
#, python-format
msgid ""
"You can't delete a contract linked to a signed document, archive it instead."
msgstr ""
"Allekirjoitettuun asiakirjaan linkitettyä sopimusta ei voi poistaa, vaan se "
"on arkistoitava."

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "document"
msgstr "dokumentti"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "documents"
msgstr "asiakirjat"
