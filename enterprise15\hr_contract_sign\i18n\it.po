# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_sign
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "%s and %s are the signatories."
msgstr "%s e %s sono firmatari."

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"%s requested a new signature on the following documents:<br/><ul>%s</ul>%s"
msgstr ""
"%s ha richiesto una nuova firma sul documento seguente:<br/><ul>%s</ul>%s"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_employee_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.res_users_request_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">Richieste di firma</span>"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__contract_id
msgid "Contract"
msgstr "Contratto"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_history
msgid "Contract history"
msgstr "Cronologia contratto"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__follower_ids
msgid "Copy to"
msgstr "Copia a"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Discard"
msgstr "Abbandona"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: hr_contract_sign
#: model:ir.actions.act_window,name:hr_contract_sign.sign_contract_wizard_action
msgid "Document Signature"
msgstr "Firma documento"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "Documenti da firmare"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"Documenti da firmare. Sono selezionabili solo i documenti con 1 o 2 responsabili diversi. \n"
"I documenti con 1 responsabile dovranno essere firmati solo dal dipendente, mentre i documenti con 2 responsabili diversi dovranno essere firmati sia dal dipendente che dal responsabile."

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_id
msgid "Employee"
msgstr "Dipendente"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract
msgid "Employee Contract"
msgstr "Contratto dipendente"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid "Employee Role"
msgstr "Ruolo dipendente"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Employee must be linked to a user and a partner."
msgstr "Il dipendente deve essere collegato a un utente o a un partner."

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid ""
"Employee's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr ""
"Ruolo del dipendente sui template da firmare. Lo stesso ruolo deve essere "
"presente in tutti i template"

#. module: hr_contract_sign
#: model:sign.item.role,name:hr_contract_sign.sign_item_role_job_responsible
msgid "HR Responsible"
msgstr "Responsabile RU"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "Ha entrambi i modelli"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__message
msgid "Message"
msgstr "Messaggio"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr ""
"Non è stato trovato alcun modello appropriato, assicurati di averli "
"configurati correttamente."

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "No template available"
msgstr "Nessun modello disponibile"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Only %s has to sign."
msgstr "Solo %s deve firmare."

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Optional Message..."
msgstr "Messaggio facoltativo... "

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_ids
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_history__sign_request_ids
msgid "Requested Signatures"
msgstr "Firme richieste"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "Responsabile"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Send"
msgstr "Invia"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_employee__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_res_users__sign_request_count
msgid "Sign Request Count"
msgstr "N. richieste di firma"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "Responsabile modello di firma"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "Firma di un documento in un contratto"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Signature Request"
msgstr "Richiesta di firma"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "Richiesta firma - %s"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__subject
msgid "Subject"
msgstr "Oggetto"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "Avviso Template"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_res_users
msgid "Users"
msgstr "Utenti"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Write email or search contact..."
msgstr "Scrivi e-mail o cerca contatto..."

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/models/hr_contract.py:0
#, python-format
msgid ""
"You can't delete a contract linked to a signed document, archive it instead."
msgstr ""
"Impossibile eliminare un contratto collegato a un documento firmato, "
"procedere invece all'archiviazione."

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "document"
msgstr "documenti"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "documents"
msgstr "documenti"
