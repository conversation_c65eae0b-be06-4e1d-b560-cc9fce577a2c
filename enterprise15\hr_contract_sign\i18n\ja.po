# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_sign
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "%s and %s are the signatories."
msgstr "%s と %sが署名者です。 "

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"%s requested a new signature on the following documents:<br/><ul>%s</ul>%s"
msgstr ""

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_employee_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.res_users_request_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">署名要求</span>"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__contract_id
msgid "Contract"
msgstr "契約"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_history
msgid "Contract history"
msgstr "契約履歴"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__follower_ids
msgid "Copy to"
msgstr "以下へコピー"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Discard"
msgstr "破棄"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_contract_sign
#: model:ir.actions.act_window,name:hr_contract_sign.sign_contract_wizard_action
msgid "Document Signature"
msgstr "ドキュメントサイン"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "未署名ドキュメント"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"未署名ドキュメント。選択できるのは、1つまたは2つの異なる責任者を持つドキュメントのみです。\n"
"        責任者が1名のドキュメントには従業員のみが署名する必要がありますが、責任者が2名のドキュメントには従業員と責任者の両方が署名する必要があります。\n"
"        "

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_id
msgid "Employee"
msgstr "従業員"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract
msgid "Employee Contract"
msgstr "従業員契約"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid "Employee Role"
msgstr "従業員役割"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Employee must be linked to a user and a partner."
msgstr ""

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid ""
"Employee's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr "署名するテンプレート上の従業員の役割。全てのテンプレートに同じ役割が存在する必要があります。"

#. module: hr_contract_sign
#: model:sign.item.role,name:hr_contract_sign.sign_item_role_job_responsible
msgid "HR Responsible"
msgstr "担当者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "両方のテンプレートあり"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__message
msgid "Message"
msgstr "メッセージ"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr "適切なテンプレートが見つかりません。正確に設定したか確認して下さい。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "No template available"
msgstr "利用可能なテンプレートなし"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Only %s has to sign."
msgstr " %s のみ署名が必要です。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Optional Message..."
msgstr "任意のメッセージ..."

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_ids
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_history__sign_request_ids
msgid "Requested Signatures"
msgstr "要求された署名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "担当者"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Send"
msgstr "送信"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_employee__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_res_users__sign_request_count
msgid "Sign Request Count"
msgstr "署名要求数"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "署名テンプレート担当者"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "契約書でドキュメントに署名"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Signature Request"
msgstr "署名依頼"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "署名依頼 - %s"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__subject
msgid "Subject"
msgstr "件名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "テンプレート警告"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_res_users
msgid "Users"
msgstr "ユーザ"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Write email or search contact..."
msgstr "Eメールを登録もしくは連絡先を検索..."

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/models/hr_contract.py:0
#, python-format
msgid ""
"You can't delete a contract linked to a signed document, archive it instead."
msgstr "署名済の文書にリンクされた契約書を削除することはできません。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "document"
msgstr "ドキュメント"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "documents"
msgstr "ドキュメント"
