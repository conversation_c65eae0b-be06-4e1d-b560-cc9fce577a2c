# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_sign
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "%s and %s are the signatories."
msgstr "%s 及 %s 是簽署人。"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"%s requested a new signature on the following documents:<br/><ul>%s</ul>%s"
msgstr ""

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_employee_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.res_users_request_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">簽名請求</span>"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__contract_id
msgid "Contract"
msgstr "合約"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_history
msgid "Contract history"
msgstr "契約歷程"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__follower_ids
msgid "Copy to"
msgstr "Copy to"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_uid
msgid "Created by"
msgstr "創立者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_date
msgid "Created on"
msgstr "建立於"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Discard"
msgstr "取消"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: hr_contract_sign
#: model:ir.actions.act_window,name:hr_contract_sign.sign_contract_wizard_action
msgid "Document Signature"
msgstr "文件簽名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "待簽名文件"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"需要簽署的文件。只限擁有 1 名或 2 名不同負責人的文件可供選取。\n"
"        涉及 1 名負責人的文件，只需由員工簽署；涉及 2 名不同負責人的文件，員工及負責人都必須簽署。\n"
"        "

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_id
msgid "Employee"
msgstr "員工"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract
msgid "Employee Contract"
msgstr "員工契約"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid "Employee Role"
msgstr "員工角色"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Employee must be linked to a user and a partner."
msgstr ""

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
msgid ""
"Employee's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr "員工在需要簽名的範本上的角色。相同的角色必須在所有範本中存在"

#. module: hr_contract_sign
#: model:sign.item.role,name:hr_contract_sign.sign_item_role_job_responsible
msgid "HR Responsible"
msgstr "人力資源負責人"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "兩個範本皆有"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__message
msgid "Message"
msgstr "消息"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr "找不到合適的範本，請確保你已正確配置它們。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "No template available"
msgstr "沒有可用範本"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Only %s has to sign."
msgstr "只有 %s 必須簽名。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Optional Message..."
msgstr "Optional Message..."

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_ids
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_history__sign_request_ids
msgid "Requested Signatures"
msgstr "請求的簽名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "負責人"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Send"
msgstr "發送"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_employee__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_res_users__sign_request_count
msgid "Sign Request Count"
msgstr "簽名請求數目"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "簽名範本負責人"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "簽署合約內的文件"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Signature Request"
msgstr "簽名請求"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
#, python-format
msgid "Signature Request - %s"
msgstr "簽名請求 － %s"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__subject
msgid "Subject"
msgstr "來源"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "範本警告"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_res_users
msgid "Users"
msgstr "使用者"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Write email or search contact..."
msgstr "輸入電郵地址或搜尋聯絡人⋯"

#. module: hr_contract_sign
#: code:addons/hr_contract_sign/models/hr_contract.py:0
#, python-format
msgid ""
"You can't delete a contract linked to a signed document, archive it instead."
msgstr "不可刪除連結至已簽署文件的合約。請改為將其封存。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "document"
msgstr "單據"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "documents"
msgstr "文件"
