<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_contract_sign_view_form" model="ir.ui.view">
        <field name="name">hr.contract.form</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="open_sign_requests" type="object" groups="sign.group_sign_user" class="oe_stat_button" icon="fa-pencil" attrs="{'invisible': [('sign_request_count', '=', 0)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="sign_request_count"/></span>
                        <span class="o_stat_text">Signature Requests</span>
                    </div>
                </button>
            </div>
            <xpath expr="//header" position="inside">
                <button name="%(sign_contract_wizard_action)d" string="Signature Request" attrs="{'invisible': [('employee_id', '=', False)]}" type="action" groups="sign.group_sign_user"/>
            </xpath>
            <xpath expr="//field[@name='hr_responsible_id']" position="after">
                <field name="sign_request_ids" invisible="1" groups='sign.group_sign_user'/>
            </xpath>
        </field>
    </record>
</odoo>
