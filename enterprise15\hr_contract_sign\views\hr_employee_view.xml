<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_employee_sign_view_form" model="ir.ui.view">
        <field name="name">hr.employee.form</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="groups_id" eval="[(4, ref('hr_contract.group_hr_contract_manager'))]"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button name="open_employee_sign_requests" type="object" class="oe_stat_button" icon="fa-pencil" attrs="{'invisible': [('sign_request_count', '=', 0)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="sign_request_count"/></span>
                        <span class="o_stat_text">Signature Requests</span>
                    </div>
                </button>
            </div>
        </field>
    </record>
</odoo>
