# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_extract
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__amount
msgid "Amount"
msgstr "Betrag"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__error_status
#, python-format
msgid "An error occurred"
msgstr "ein Fehler ist aufgetreten"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_ir_attachment
msgid "Attachment"
msgstr "Dateianhang"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__available_payment_method_line_ids
msgid "Available Payment Method Line"
msgstr "Verfügbare Zahlungsmethodenzeile"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Buy credits"
msgstr "Guthaben kaufen"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_resend_button
msgid "Can show the ocr resend button"
msgstr "Kann den ocr erneut senden Button anzeigen"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Kann Schaltfläche zum OCR-Versand anzeigen"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Choose a receipt."
msgstr "Wählen Sie einen Beleg aus."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Choose a receipt:"
msgstr "Einen Beleg auswählen:"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__done
msgid "Completed flow"
msgstr "Vollständiger Flow"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Create Payment"
msgstr "Zahlung erstellen"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__currency_id
msgid "Currency"
msgstr "Währung"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__auto_send
msgid "Digitalize automatically"
msgstr "Automatisch digitalisieren"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__manual_send
msgid "Digitalize on demand only"
msgstr "Nur auf Anfrage digitalisieren"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__no_send
msgid "Do not digitalize"
msgstr "Nicht digitalisieren"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_error_message
msgid "Error message"
msgstr "Fehlermeldung"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__sheet_id
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__expense_id
msgid "Expense"
msgstr "Ausgabe"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_update_ocr_status_ir_actions_server
#: model:ir.cron,cron_name:hr_expense_extract.ir_cron_update_ocr_status
#: model:ir.cron,name:hr_expense_extract.ir_cron_update_ocr_status
msgid "Expense OCR: Update All Status"
msgstr "Spesen OCR: Alle Status aktualisieren"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Spesenabrechnung"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__extract_remote_id
msgid "Expense extract id"
msgstr "ID der Ausgabenextraktion"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_config_settings__expense_extract_show_ocr_option_selection
msgid "Expense processing option"
msgstr "Verarbeitungsoptionen für Spesen"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Expenses sent"
msgstr "Spesen gesendet"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_word_ids
msgid "Extract Word"
msgstr "Wort entnehmen"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state
msgid "Extract state"
msgstr "Auszugstatus"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_extract_words
msgid "Extracted words from expense scan"
msgstr "Aus Auslagenscan extrahierte Wörter"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Generated Expense"
msgstr "Generierte Auslage"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_partial
msgid "Hide Partial"
msgstr "Teilweise verbergen"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_payment_method_line
msgid "Hide Payment Method Line"
msgstr "Zahlungsmethodenzeile ausblenden"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__id
msgid "ID"
msgstr "ID"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_remote_id
msgid "Id of the request to IAP-OCR"
msgstr "ID der IAP-OCR Anfrage"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__journal_id
msgid "Journal"
msgstr "Journal"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__open
msgid "Keep open"
msgstr "Offen halten"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt____last_update
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register____last_update
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid ""
"Let's try a sample receipt to test the automated processing of expenses with"
" Artificial Intelligence."
msgstr ""
"Testen Sie die automatische Verarbeitung von Spesen mit künstlicher "
"Intelligenz mit einem Beispielbeleg."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Acquirers: Each payment acquirer has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"Manuell: Bezahlen oder bezahlt werden mit einer beliebigen Methode außerhalb von Odoo.\n"
"Zahlungsanbieter: Jeder Zahlungsanbieter hat seine eigene Zahlungsmethode. Beantragen Sie eine Transaktion mithilfe eines Zahlungstokens, das der Partner beim Online-Kauf oder -Abonnement gespeichert hat.\n"
"Scheck: Bezahlen Sie Rechnungen per Scheck und drucken Sie ihn aus Odoo aus.\n"
"Sammeleinzahlung: Sammeln Sie mehrere Kundenschecks auf einmal, indem Sie eine Sammeleinzahlung erstellen und an Ihre Bank übermitteln. Das Modul account_batch_payment ist erforderlich.\n"
"SEPA-Überweisung: Zahlen Sie im SEPA-Raum, indem Sie eine SEPA-Überweisungsdatei an Ihre Bank übermitteln. Das Modul account_sepa ist erforderlich.\n"
"SEPA-Lastschrift: Bezahlen Sie in der SEPA-Zone dank eines Mandats, das Ihnen Ihr Partner erteilt hat. Das Modul account_sepa ist erforderlich.\n"
"\n"
"\n"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__paid
msgid "Mark as fully paid"
msgstr "Als vollständig bezahlt markieren"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__memo
msgid "Memo"
msgstr "Memo"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "No document name provided"
msgstr "Kein Dokumentname angegeben"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__no_extract_requested
msgid "No extract requested"
msgstr "Kein Auszug erforderlich"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__not_enough_credit
msgid "Not enough credit"
msgstr "Nicht genug Guthaben"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__date
msgid "Payment Date"
msgstr "Zahlungsdatum"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__partial_mode
msgid "Payment Difference"
msgstr "Zahlungsdifferenz"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid "Payment Method"
msgstr "Zahlungsmethode"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Register Payment"
msgstr "Zahlung registrieren"

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_register
msgid "Register Sample Payment"
msgstr "Beispielzahlung registrieren"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_register
msgid "Register Sample Payments"
msgstr "Beispielzahlungen registrieren"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Report this expense to your manager for validation."
msgstr "Reichen Sie diese Auslage zur Validierung bei Ihrem Manager ein."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Resend For Digitalization"
msgstr "Für Digitalisierung erneut versenden"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
#, python-format
msgid "Sample Employee"
msgstr "Beispielmitarbeiter"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
#, python-format
msgid "Sample Receipt: %s"
msgstr "Beispielbeleg: %s"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Send For Digitalization"
msgstr "Für Digitalisierung versenden"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.hr_expense_parse_action_server
msgid "Send for digitalization"
msgstr "Für Digitalisierung versenden"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_company__expense_extract_show_ocr_option_selection
msgid "Send mode on expense attachments"
msgstr "Sendemodus für Spesenabrechnungsanhänge"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Server is currently under maintenance. Please retry later"
msgstr ""
"Am Server finden Wartungsarbeiten statt. Bitte versuchen Sie es später "
"erneut."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Server not available. Please retry later"
msgstr "Server ist nicht verfügbar. Bitte versuchen Sie es später"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_status_code
msgid "Status code"
msgstr "Statuscode"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 60 seconds."
msgstr ""
"Die Datenextraktion ist noch nicht abgeschlossen. Die Extraktion dauert "
"normalerweise zwischen 5 und 60 Sekunden."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "The document could not be found"
msgstr "Das Dokument wurde nicht gefunden"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid ""
"The file has been sent and is being processed. It usually takes between 5 "
"and 60 seconds."
msgstr ""
"Die Datei wurde versandt und befindet sich in Verarbeitung. Das dauert in "
"der Regel zwischen 5 und 60 Sekunden."

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_receipt
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Try Sample Receipt"
msgstr "Beispielbeleg ausprobieren"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_receipt
msgid "Try Sample Receipts"
msgstr "Beispielbelege ausprobieren"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Try the AI with a sample receipt."
msgstr "Testen Sie die Künstliche Intelligenz mit einem Beispielbeleg."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Unsupported image format"
msgstr "Nicht unterstütztes Bildformat"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Update status"
msgstr "Aktualisieren Sie ihren Status"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.res_config_settings_view_form
msgid "View My Services"
msgstr "Meine Dienste anzeigen"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr "Wartende Extraktion"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "Warten auf Bestätigung"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Wasting time recording your receipts? Let’s try a better way."
msgstr ""
"Sie verlieren Zeit mit der Erfassung von Belegen? Wir haben die Lösung."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__word_page
msgid "Word Page"
msgstr "Wortseite"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__word_text
msgid "Word Text"
msgstr "Worttext"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "You cannot send a expense that is not in draft state!"
msgstr "Sie können Spesen nur im Entwurf einreichen!"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "You don't have enough credit to extract data from your expense."
msgstr ""
"Sie haben nicht genügend Guthaben, um Daten aus Ihren Ausgaben zu "
"extrahieren."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "You must send the same quantity of documents and file names"
msgstr "Sie müssen die gleiche Anzahl von Dokumenten und Dateinamen senden"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Your manager will have to approve (or refuse) your expense reports."
msgstr "Ihr Manager muss Ihre Spesenabrechnungen genehmigen (oder ablehnen)."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense_extract_words__expense_id
msgid "expense id"
msgstr "expense id"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__extract_not_ready
msgid "waiting extraction, but it is not ready"
msgstr "Warten Extraktion, aber es ist nicht fertig"
