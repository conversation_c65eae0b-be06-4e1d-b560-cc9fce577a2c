# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_extract
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Spanish (https://www.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__amount
msgid "Amount"
msgstr "Importe"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__error_status
#, python-format
msgid "An error occurred"
msgstr "Se ha producido un error"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_ir_attachment
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__available_payment_method_line_ids
msgid "Available Payment Method Line"
msgstr "Línea de método de pago disponible"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Buy credits"
msgstr "Comprar créditos"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_resend_button
msgid "Can show the ocr resend button"
msgstr "Puede mostrar el botón de reenvío ocr."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Puede mostrar el botón de envío de ocr"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Choose a receipt."
msgstr "Elige un recibo."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Choose a receipt:"
msgstr "Elige un recibo:"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__company_id
msgid "Company"
msgstr "Compañía"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__done
msgid "Completed flow"
msgstr "Flujo completado"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Create Payment"
msgstr "Crear pago"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__auto_send
msgid "Digitalize automatically"
msgstr "Digitalizar automáticamente"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__manual_send
msgid "Digitalize on demand only"
msgstr "Digitalizar solo bajo demanda"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__no_send
msgid "Do not digitalize"
msgstr "No digitalizar"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_error_message
msgid "Error message"
msgstr "Mensaje de error"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__sheet_id
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__expense_id
msgid "Expense"
msgstr "Gasto"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_update_ocr_status_ir_actions_server
#: model:ir.cron,cron_name:hr_expense_extract.ir_cron_update_ocr_status
#: model:ir.cron,name:hr_expense_extract.ir_cron_update_ocr_status
msgid "Expense OCR: Update All Status"
msgstr "OCR de gastos: Actualizar todos los estados"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Informe de gastos"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__extract_remote_id
msgid "Expense extract id"
msgstr "ID del extracto de gastos"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_config_settings__expense_extract_show_ocr_option_selection
msgid "Expense processing option"
msgstr "Opción de procesamiento de gastos"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Expenses sent"
msgstr "Gastos enviados "

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_word_ids
msgid "Extract Word"
msgstr "Extraer palabra"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state
msgid "Extract state"
msgstr "Estado del extracto"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_extract_words
msgid "Extracted words from expense scan"
msgstr "Palabras extraídas del escaneo de gastos"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Generated Expense"
msgstr "Gasto generado"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_partial
msgid "Hide Partial"
msgstr "Ocultar parcial"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_payment_method_line
msgid "Hide Payment Method Line"
msgstr "Ocultar línea de método de pago"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__id
msgid "ID"
msgstr "ID"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_remote_id
msgid "Id of the request to IAP-OCR"
msgstr "ID de la solicitud al IAP-OCR"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__journal_id
msgid "Journal"
msgstr "Diario"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__open
msgid "Keep open"
msgstr "Mantener abierto"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt____last_update
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register____last_update
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid ""
"Let's try a sample receipt to test the automated processing of expenses with"
" Artificial Intelligence."
msgstr ""
"Hagamos un recibo de prueba par probar el procesamiento automático de gastos"
" con Inteligencia Artificial"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Acquirers: Each payment acquirer has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""
"Manual: Pague u obtenga su pago con cualquier método fuera de Odoo.\n"
"Métodos de pago: Cada sistema de pago en línea tiene su propio método de pago. Pida una transacción en/a una tarjeta gracias al token de pago que el partner guarda al hacer una compra o subscribirse en línea.\n"
"Cheque: Pague las facturas con un cheque e imprímalo desde Odoo.\n"
"Depósito en lotes: Genere y envíe un depósito en lotes a su banco para recolectar varios cheques de clientes a la vez. Se necesita el módulo account_batch_payment.\n"
"Domiciliación bancaria SEPA: podrá obtener pagos en la zona SEPA gracias a un mandato que su partner le otorgará. Se necesita el módulo account_sepa.\n"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__paid
msgid "Mark as fully paid"
msgstr "Marcar como totalmente pagado"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__memo
msgid "Memo"
msgstr "Memo"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "No document name provided"
msgstr "No se proporcionó el nombre del documento"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__no_extract_requested
msgid "No extract requested"
msgstr "No se solicitó el extracto"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__not_enough_credit
msgid "Not enough credit"
msgstr "No hay suficiente crédito"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__date
msgid "Payment Date"
msgstr "Fecha de pago"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__partial_mode
msgid "Payment Difference"
msgstr "Diferencia en pago"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid "Payment Method"
msgstr "Método de pago"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Register Payment"
msgstr "Registrar pago"

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_register
msgid "Register Sample Payment"
msgstr "Registrar pago de muestra"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_register
msgid "Register Sample Payments"
msgstr "Registrar pagos de muestra"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Report this expense to your manager for validation."
msgstr "Reporta este gasto a tu gerente para que lo valide."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Resend For Digitalization"
msgstr "Reenviar para digitalización"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
#, python-format
msgid "Sample Employee"
msgstr "Empleado de muestra"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
#, python-format
msgid "Sample Receipt: %s"
msgstr "Recibo de muestra: %s"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Send For Digitalization"
msgstr "Enviar para digitalización"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.hr_expense_parse_action_server
msgid "Send for digitalization"
msgstr "Enviar a digitalización"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_company__expense_extract_show_ocr_option_selection
msgid "Send mode on expense attachments"
msgstr "Modo de envío en archivos adjuntos de gastos"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Server is currently under maintenance. Please retry later"
msgstr ""
"El servidor se encuentra actualmente en mantenimiento. Por favor, inténtalo "
"más tarde."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Server not available. Please retry later"
msgstr "El servidor no está disponible. Por favor, inténtalo más tarde."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_status_code
msgid "Status code"
msgstr "Código del estado"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 60 seconds."
msgstr ""
"La extracción de datos no se ha completado. La extracción suele tomar entre "
"5 y 60 segundos. "

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "The document could not be found"
msgstr "No se pudo encontrar el documento."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid ""
"The file has been sent and is being processed. It usually takes between 5 "
"and 60 seconds."
msgstr ""
"El archivo ha sido enviado y se está procesando. Por lo general, toma entre "
"5 y 60 segundos."

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_receipt
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Try Sample Receipt"
msgstr "Prueba el recibo de muestra"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_receipt
msgid "Try Sample Receipts"
msgstr "Prueba los recibos de muestra"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Try the AI with a sample receipt."
msgstr "Prueba la IA con un recibo de muestra."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Unsupported image format"
msgstr "Formato de imagen no compatible"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Update status"
msgstr "Actualizar estado"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.res_config_settings_view_form
msgid "View My Services"
msgstr "Ver mis servicios"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr "En espera de extracción"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "En espera de validación"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Wasting time recording your receipts? Let’s try a better way."
msgstr ""
"¿Perdiendo el tiempo registrando tus recibos? Probemos una forma mejor."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__word_page
msgid "Word Page"
msgstr "Página de Word"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__word_text
msgid "Word Text"
msgstr "Texto de Word"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "You cannot send a expense that is not in draft state!"
msgstr "¡No puedes enviar un gasto que no esté en estado de borrador!"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "You don't have enough credit to extract data from your expense."
msgstr "No tienes crédito suficiente para extraer datos de tu gasto."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "You must send the same quantity of documents and file names"
msgstr "Debes enviar la misma cantidad de documentos y nombres de archivo."

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Your manager will have to approve (or refuse) your expense reports."
msgstr "Su gerente tendrá que aprobar (o rechazar) sus reportes de gastos."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense_extract_words__expense_id
msgid "expense id"
msgstr "id de gasto"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__extract_not_ready
msgid "waiting extraction, but it is not ready"
msgstr "En espera de extracción, pero no está lista"
