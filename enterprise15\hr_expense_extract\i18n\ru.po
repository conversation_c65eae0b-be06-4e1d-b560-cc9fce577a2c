# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_extract
# 
# Translators:
# <PERSON>, 2021
# Collex100, 2021
# Андре<PERSON>ев <<EMAIL>>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON><PERSON><PERSON><PERSON> <koro<PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# E<PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__amount
msgid "Amount"
msgstr "Сумма"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__error_status
#, python-format
msgid "An error occurred"
msgstr "Произошла ошибка"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_ir_attachment
msgid "Attachment"
msgstr "Вложение"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__available_payment_method_line_ids
msgid "Available Payment Method Line"
msgstr "Строка доступных способов оплаты"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Buy credits"
msgstr "получить кредит"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_resend_button
msgid "Can show the ocr resend button"
msgstr "Может показать пересылки кнопки ocr"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Может показывать присланную кнопку ocr"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Cancel"
msgstr "Отмена"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Choose a receipt."
msgstr "Выберите квитанцию."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Choose a receipt:"
msgstr "Выберите квитанцию:"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__company_id
msgid "Company"
msgstr "Компания"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__done
msgid "Completed flow"
msgstr "завершен процесс"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Create Payment"
msgstr "Создать платеж"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__create_uid
msgid "Created by"
msgstr "Создан"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__create_date
msgid "Created on"
msgstr "Создан"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__auto_send
msgid "Digitalize automatically"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__manual_send
msgid "Digitalize on demand only"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__no_send
msgid "Do not digitalize"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_error_message
msgid "Error message"
msgstr "Сообщение об ошибке"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__sheet_id
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__expense_id
msgid "Expense"
msgstr "Расход"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_update_ocr_status_ir_actions_server
#: model:ir.cron,cron_name:hr_expense_extract.ir_cron_update_ocr_status
#: model:ir.cron,name:hr_expense_extract.ir_cron_update_ocr_status
msgid "Expense OCR: Update All Status"
msgstr "Расходы OCR: обновление всех статусов"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Отчет о расходах"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__extract_remote_id
msgid "Expense extract id"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_config_settings__expense_extract_show_ocr_option_selection
msgid "Expense processing option"
msgstr "Возможность обработки расходов"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Expenses sent"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_word_ids
msgid "Extract Word"
msgstr "удалите слово"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state
msgid "Extract state"
msgstr "состояние извлечения"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_extract_words
msgid "Extracted words from expense scan"
msgstr ""

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Generated Expense"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_partial
msgid "Hide Partial"
msgstr "Скрыть частично"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_payment_method_line
msgid "Hide Payment Method Line"
msgstr "Скрыть строку способа оплаты"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__id
msgid "ID"
msgstr "Идентификатор"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_remote_id
msgid "Id of the request to IAP-OCR"
msgstr "Id запроса в IAP-OCR"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__journal_id
msgid "Journal"
msgstr "Журнал"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__open
msgid "Keep open"
msgstr "Оставить открытым"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt____last_update
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register____last_update
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid ""
"Let's try a sample receipt to test the automated processing of expenses with"
" Artificial Intelligence."
msgstr ""
"Давайте попробуем создать образец квитанции, чтобы проверить автоматическую "
"обработку расходов с помощью искусственного интеллекта."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Acquirers: Each payment acquirer has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_sepa is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_sepa is necessary.\n"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__paid
msgid "Mark as fully paid"
msgstr "Отметить как полностью оплаченный"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__memo
msgid "Memo"
msgstr "Заметки"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "No document name provided"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__no_extract_requested
msgid "No extract requested"
msgstr "Нет запроса на удаление"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__not_enough_credit
msgid "Not enough credit"
msgstr "недостаточно кредитов"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__date
msgid "Payment Date"
msgstr "Дата платежа"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__partial_mode
msgid "Payment Difference"
msgstr "Оплата Разницы"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid "Payment Method"
msgstr "Метод оплаты"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Register Payment"
msgstr "Регистрация Платежа"

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_register
msgid "Register Sample Payment"
msgstr "Регистрация Образец оплаты"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_register
msgid "Register Sample Payments"
msgstr "Регистрация образцов платежей"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Report this expense to your manager for validation."
msgstr "Сообщите об этом расходе своему руководителю для подтверждения."

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Resend For Digitalization"
msgstr "Переслать на распознавание текста"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
#, python-format
msgid "Sample Employee"
msgstr "Образец сотрудника"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
#, python-format
msgid "Sample Receipt: %s"
msgstr "Образец квитанции: %s"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Send For Digitalization"
msgstr "Отправить на оцифровку"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.hr_expense_parse_action_server
msgid "Send for digitalization"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_company__expense_extract_show_ocr_option_selection
msgid "Send mode on expense attachments"
msgstr "Режим отправки для вложений расходов"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Server is currently under maintenance. Please retry later"
msgstr ""
"Сервер в настоящее время находится на техническом обслуживании. Пожалуйста, "
"повторите попытку позже"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Server not available. Please retry later"
msgstr "Сервер недоступен. Пожалуйста, повторите попытку позже"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_status_code
msgid "Status code"
msgstr "Код состояния"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid ""
"The data extraction is not finished yet. The extraction usually takes "
"between 5 and 60 seconds."
msgstr ""
"Извлечение данных еще не завершено. Изъятие обычно длится от 5 до 60 секунд."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "The document could not be found"
msgstr "Документ не найден"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid ""
"The file has been sent and is being processed. It usually takes between 5 "
"and 60 seconds."
msgstr ""
"Файл отправлен и обрабатывается. Обычно это занимает от 5 до 60 секунд."

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_receipt
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Try Sample Receipt"
msgstr "Попробуйте образец квитанции"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_receipt
msgid "Try Sample Receipts"
msgstr "Попробуйте использовать образцы квитанций"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Try the AI with a sample receipt."
msgstr "Попробуйте использовать ИИ с образцом квитанции."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "Unsupported image format"
msgstr "Неподдерживаемый формат изображения"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Update status"
msgstr "обновить статус"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.res_config_settings_view_form
msgid "View My Services"
msgstr "Посмотреть мои услуги"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__waiting_extraction
msgid "Waiting extraction"
msgstr "ожидания изъятия"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__waiting_validation
msgid "Waiting validation"
msgstr "ожидание подтверждения"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Wasting time recording your receipts? Let’s try a better way."
msgstr "Тратите время на запись квитанций? Давайте попробуем лучший способ."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__word_page
msgid "Word Page"
msgstr "Word Page"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense_extract_words__word_text
msgid "Word Text"
msgstr "Word Text"

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "You cannot send a expense that is not in draft state!"
msgstr ""
"Вы не можете отправить расход, который не находится в состоянии черновика!"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "You don't have enough credit to extract data from your expense."
msgstr "У вас недостаточно кредитов, чтобы извлечь данные из расходов."

#. module: hr_expense_extract
#: code:addons/hr_expense_extract/models/hr_expense.py:0
#, python-format
msgid "You must send the same quantity of documents and file names"
msgstr "Вы должны отправить одинаковое количество документов и имен файлов"

#. module: hr_expense_extract
#. openerp-web
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
#, python-format
msgid "Your manager will have to approve (or refuse) your expense reports."
msgstr ""
"Ваш руководитель должен будет утверждать (или отклонять) ваши отчеты о "
"расходах."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense_extract_words__expense_id
msgid "expense id"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__hr_expense__extract_state__extract_not_ready
msgid "waiting extraction, but it is not ready"
msgstr "ожидания изъятия, но оно еще не готово"
