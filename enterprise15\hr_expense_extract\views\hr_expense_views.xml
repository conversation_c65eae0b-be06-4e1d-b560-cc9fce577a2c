<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_expense_extract_view_form" model="ir.ui.view">
        <field name="name">hr.expense.extract.view.form</field>
        <field name="model">hr.expense</field>
        <field name="inherit_id" ref="hr_expense.hr_expense_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="retry_ocr" class="oe_highlight" string="Send For Digitalization" type="object"
                attrs="{'invisible': [('extract_can_show_send_button', '=', False)]}" data-hotkey="q"/>
            </xpath>
            <xpath expr="//header" position='after'>
                <field name="extract_state" attrs="{'invisible':True}"/>
                <field name="extract_word_ids" attrs="{'invisible':True}"/>
                <field name="extract_can_show_resend_button" attrs="{'invisible':True}"/>
                <field name="extract_can_show_send_button" attrs="{'invisible':True}"/>
                <div role="status" class="o_success_ocr"
                     attrs="{'invisible':['|',
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['waiting_validation', 'validation_to_send'])]}" >
                    <!-- Extraction processed with success. Don't forget to validate the different fields. -->
                </div>
                <div role="status" class="alert alert-danger mb8"
                     attrs="{'invisible':['|',
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['not_enough_credit'])]}">
                    <button string="Buy credits" type="object" name="buy_credits" class="btn btn-sm btn-primary pull-right mb0"/>
                    <button name="retry_ocr" class="btn btn-sm btn-primary pull-right mb0 mr-1" string="Resend For Digitalization" type="object"
                        attrs="{'invisible': [('extract_can_show_resend_button', '=', False)]}" data-hotkey="q"/>
                    You don't have enough credit to extract data from your expense.
                    <div class="clearfix"/>
                </div>
                <div role="status" class="alert alert-info mb8"
                     attrs="{'invisible':['|',
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['waiting_extraction'])]}">
                    <button string="Update status" type="object" name="check_status" class="btn btn-sm btn-primary pull-right mb0"/>
                    The file has been sent and is being processed. It usually takes between 5 and 60 seconds.
                    <div class="clearfix"/>
                </div>
                <field name="extract_status_code" invisible="1"/>
                <div role="status" class="alert alert-info mb8"
                     attrs="{'invisible':['|',
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['extract_not_ready'])]}">
                    <button string="Update status" type="object" name="check_status" class="btn btn-sm btn-primary pull-right mb0"/>
                    The data extraction is not finished yet. The extraction usually takes between 5 and 60 seconds.
                    <div class="clearfix"/>
                </div>
                <div role="status" class="alert alert-danger mb8"
                     attrs="{'invisible':['|', '|',
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['error_status']),
                            '&amp;',
                            ('extract_status_code', '&gt;', 99),
                            ('extract_status_code', '&lt;', 200)]}">
                    <field name="extract_error_message"/>
                </div>
                <div role="status" class="alert alert-warning mb8"
                     attrs="{'invisible':['|', '|', '|',
                            ('state', 'not in', ['draft']),
                            ('extract_state', 'not in', ['error_status', 'waiting_validation']),
                            ('extract_status_code', '&lt;', 100),
                            ('extract_status_code', '&gt;', 199)]}">
                    <field name="extract_error_message"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='account_id']" position='after'>
                <field name="extract_remote_id" groups="base.group_no_one"/>
            </xpath>
        </field>
    </record>

    <record id="hr_expense_extract_view_list" model="ir.ui.view">
        <field name="name">hr.expense.extract.view.list</field>
        <field name="model">hr.expense</field>
        <field name="inherit_id" ref="hr_expense.hr_expense_view_expenses_analysis_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="decoration-muted">extract_state == 'waiting_extraction'</attribute>
            </xpath>
            <xpath expr="//tree" position="inside">
                <field name="extract_state" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="hr_expense_parse_action_server" model="ir.actions.server">
            <field name="name">Send for digitalization</field>
            <field name="type">ir.actions.server</field>
            <field name="model_id" ref="model_hr_expense"/>
            <field name="binding_model_id" ref="model_hr_expense"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
if records:
    action = records.action_send_for_digitalization()
            </field>
    </record>
</odoo>
