<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.hr.expense</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="85"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <div id="expense_extract_settings" position="inside">
                    <div class="mt16" attrs="{'invisible': [('module_hr_expense_extract', '=', False)]}">
                        <field name="expense_extract_show_ocr_option_selection" class="o_light_label" widget="radio" required="True"/>
                        <widget name="iap_buy_more_credits" service_name="invoice_ocr" class="pl-1"/>
                        <!-- TODO: [XBO] Remove me in master -->
                        <button name="%(iap.open_iap_account)d" icon="fa-arrow-right" type="action" string="View My Services" class="btn-link pl-0" invisible="1"/>
                    </div>
                </div>
            </field>
        </record>
    </data>
</odoo>
