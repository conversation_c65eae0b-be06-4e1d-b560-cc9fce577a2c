<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="expense_sample_register_view_form" model="ir.ui.view">
        <field name="name">expense.sample.register.view.form</field>
        <field name="model">expense.sample.register</field>
        <field name="arch" type="xml">
            <form string="Register Payment">
                <field name="sheet_id" invisible="1" /> 
                <field name="currency_id" invisible="1"/>
                <field name="company_id" invisible="1"/>
                <field name="available_payment_method_line_ids" invisible="1"/>
                <field name="hide_payment_method_line" invisible="1"/>
                <field name="hide_partial" invisible="1"/>
                <group> 
                    <group> 
                        <field name="journal_id" options="{'no_open': True, 'no_create': True}"/>
                        <field name="payment_method_line_id" widget="radio"
                            attrs="{'invisible': [('hide_payment_method_line', '=', True)]}" />
                        <field name="partial_mode" widget="radio"
                            attrs="{'invisible': [('hide_partial', '=', True)]}" />
                    </group>
                    <group> 
                        <field name="amount" widget="monetary" options="{'currency_field': 'currency_id'}" />
                        <field name="date" />
                        <field name="memo" />
                    </group>
                </group>
                <footer>
                    <button string="Create Payment" name="action_create_payments" type="object" class="oe_highlight" close="1" data-hotkey="q"/>
                    <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_expense_sample_register" model="ir.actions.act_window">
        <field name="name">Register Sample Payment</field>
        <field name="res_model">expense.sample.register</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
