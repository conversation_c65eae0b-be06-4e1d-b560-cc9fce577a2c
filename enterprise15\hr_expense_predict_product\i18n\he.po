# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_predict_product
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2022
# yael terner, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: yael terner, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__accommodation
msgid "Accommodation"
msgstr ""

#. module: hr_expense_predict_product
#: model:ir.model,name:hr_expense_predict_product.model_hr_expense
msgid "Expense"
msgstr "מאשר הוצאות"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__food
msgid "Food"
msgstr "מזון"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__gasoline
msgid "Gasoline"
msgstr "בנזין"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__miscellaneous
msgid "Miscellaneous"
msgstr "שונות"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__parking
msgid "Parking"
msgstr ""

#. module: hr_expense_predict_product
#: model:ir.model.fields,field_description:hr_expense_predict_product.field_hr_expense__predicted_category
msgid "Predicted Category"
msgstr "קטגוריה חזויה"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__toll
msgid "Toll"
msgstr ""

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__transport
msgid "Transport"
msgstr "הובלה"
