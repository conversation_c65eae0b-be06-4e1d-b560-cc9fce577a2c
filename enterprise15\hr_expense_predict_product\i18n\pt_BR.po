# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_predict_product
# 
# Translators:
# <PERSON>, 2021
# graz<PERSON>no <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: Ke<PERSON>yn Rosa, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__accommodation
msgid "Accommodation"
msgstr "Acomodação"

#. module: hr_expense_predict_product
#: model:ir.model,name:hr_expense_predict_product.model_hr_expense
msgid "Expense"
msgstr "Despesa"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__food
msgid "Food"
msgstr "Comida"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__gasoline
msgid "Gasoline"
msgstr "Gasolina"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__miscellaneous
msgid "Miscellaneous"
msgstr "Diversos"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__parking
msgid "Parking"
msgstr "estacionamento"

#. module: hr_expense_predict_product
#: model:ir.model.fields,field_description:hr_expense_predict_product.field_hr_expense__predicted_category
msgid "Predicted Category"
msgstr "Categoria prevista"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__toll
msgid "Toll"
msgstr "Pedágio"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__transport
msgid "Transport"
msgstr "Transporte"
