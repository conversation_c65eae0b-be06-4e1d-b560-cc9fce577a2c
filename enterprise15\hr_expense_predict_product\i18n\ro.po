# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_predict_product
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__accommodation
msgid "Accommodation"
msgstr "Cazare"

#. module: hr_expense_predict_product
#: model:ir.model,name:hr_expense_predict_product.model_hr_expense
msgid "Expense"
msgstr "Cheltuieli"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__food
msgid "Food"
msgstr "Mâncare"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__gasoline
msgid "Gasoline"
msgstr "Benzina"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__miscellaneous
msgid "Miscellaneous"
msgstr "Diverse"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__parking
msgid "Parking"
msgstr "Parcare"

#. module: hr_expense_predict_product
#: model:ir.model.fields,field_description:hr_expense_predict_product.field_hr_expense__predicted_category
msgid "Predicted Category"
msgstr "Categorie prevăzută"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__toll
msgid "Toll"
msgstr "Taxe de drum"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__transport
msgid "Transport"
msgstr "Transport"
