# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_predict_product
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovenian (https://www.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__accommodation
msgid "Accommodation"
msgstr "Nastanitev"

#. module: hr_expense_predict_product
#: model:ir.model,name:hr_expense_predict_product.model_hr_expense
msgid "Expense"
msgstr "Strošek"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__food
msgid "Food"
msgstr "Prehrambena"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__gasoline
msgid "Gasoline"
msgstr "Bencin"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__miscellaneous
msgid "Miscellaneous"
msgstr "Razno"

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__parking
msgid "Parking"
msgstr ""

#. module: hr_expense_predict_product
#: model:ir.model.fields,field_description:hr_expense_predict_product.field_hr_expense__predicted_category
msgid "Predicted Category"
msgstr ""

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__toll
msgid "Toll"
msgstr ""

#. module: hr_expense_predict_product
#: model:ir.model.fields.selection,name:hr_expense_predict_product.selection__hr_expense__predicted_category__transport
msgid "Transport"
msgstr "Transport"
