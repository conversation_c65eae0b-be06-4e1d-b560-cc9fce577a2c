# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_gantt
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Hungarian (https://www.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid " and"
msgstr "és"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "%(employee)s %(time_off_type)s%(period_leaves)s. \n"
msgstr ""

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "%(prefix)s from the %(dfrom)s to the %(dto)s"
msgstr ""

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid ""
"%(prefix)s from the %(dfrom_date)s at %(dfrom)s to the %(dto_date)s at "
"%(dto)s"
msgstr ""

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr ""

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_allocation_gantt_view
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_gantt_view
msgid "Days"
msgstr "nap"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Time Off"
msgstr "Szabadság"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Szabadságnaptár"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "has requested time off"
msgstr ""

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "is on time off"
msgstr ""
