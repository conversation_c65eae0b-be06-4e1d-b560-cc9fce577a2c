# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_gantt
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <luka<PERSON>.<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid " and"
msgstr "i"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "%(employee)s %(time_off_type)s%(period_leaves)s. \n"
msgstr "%(employee)s%(time_off_type)s%(period_leaves)s.\n"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "%(prefix)s from the %(dfrom)s to the %(dto)s"
msgstr "%(prefix)sz%(dfrom)sdo%(dto)s"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid ""
"%(prefix)s from the %(dfrom_date)s at %(dfrom)s to the %(dto_date)s at "
"%(dto)s"
msgstr "%(prefix)sz%(dfrom_date)sna%(dfrom)sdo%(dto_date)sna%(dto)s"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_allocation_gantt_view
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_gantt_view
msgid "Days"
msgstr "Dni"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Time Off"
msgstr "Dni wolne"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Kalendarz dni wolnych"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "has requested time off"
msgstr "zażądał urlopu"

#. module: hr_holidays_gantt
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
#, python-format
msgid "is on time off"
msgstr "jest na urlopie"
