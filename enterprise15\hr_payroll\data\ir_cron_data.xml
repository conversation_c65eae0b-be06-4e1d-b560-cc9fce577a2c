<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="ir_cron_update_payroll_data" model="ir.cron">
            <field name="name">Payroll: Update data</field>
            <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
            <field name="state">code</field>
            <field name="code">model._update_payroll_data()</field>
            <field name="active" eval="True"/>
            <field name="interval_number">1</field>
            <field name="interval_type">months</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="nextcall" eval="DateTime.now().replace(day=20, hour=3, minute=0)"/>
        </record>

        <record id="ir_cron_generate_payslip_pdfs" model="ir.cron">
            <field name="name">Payroll: Generate pdfs</field>
            <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
            <field name="state">code</field>
            <field name="code">model._cron_generate_pdf()</field>
            <field name="active" eval="True"/>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=1))"/>
        </record>
    </data>
</odoo>
