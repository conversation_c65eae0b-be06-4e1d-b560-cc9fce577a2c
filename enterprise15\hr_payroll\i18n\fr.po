# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Wil <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-19 10:26+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count
msgid "# Payslip"
msgstr "# Fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_count
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "# Payslips"
msgstr "# Fiches de paie"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid ""
"%s:\n"
"- Employee: %s\n"
"- Contract: %s\n"
"- Payslip: %s\n"
"- Salary rule: %s (%s)\n"
"- Error: %s"
msgstr ""
"%s:\n"
"- Employé: %s\n"
"- Contrat: %s\n"
"- Fiche de Paie: %s\n"
"- Règle de Salaire: %s (%s)\n"
"- Erreur: %s"

#. module: hr_payroll
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr "'Fiche de paie - %s' % (object.employee_id.name)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When user cancel payslip the status is 'Rejected'."
msgstr ""
"* Quand la fiche de paie est créée, le statut est 'Brouillon'.\n"
"* Si la fiche de paie est à contrôler, le statut est 'En Attente'.\n"
"* Si la fiche de paie est confirmée, le statut est 'Terminée'.\n"
"* Quand l'utilisateur annule la fiche de paie, le statut est 'Rejetée'."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "-> Report"
msgstr "-> Rapport"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Hour"
msgstr "/ Heure"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Month"
msgstr "/ Mois"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_13th_month_salary
msgid "13th pay salary"
msgstr "13ème mois"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span class=\"ml-2\"> hours/week</span>"
msgstr "<span class=\"ml-2\">heures/semaine</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Payroll Rules</span>"
msgstr "<span class=\"o_form_label\">Règles de Paie</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "<span class=\"o_stat_text\">Payslips</span>"
msgstr "<span class=\"o_stat_text\">Fiches de paie</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span class=\"text-muted\">Set a specific department if you wish to select "
"all the employees from this department (and subdepartments) at once.</span>"
msgstr ""
"<span class=\"text-muted\">Définissez un département spécifique si vous "
"souhaitez sélectionner tous les employés de ce département (et sous-"
"départements) à la fois.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span class=\"text-muted\">Set a specific structure if you wish to make an "
"extra payslip (eg: End of the year bonus). If you leave this field empty, a "
"regular payslip will be generated for all the selected employees, based on "
"their contracts configuration.</span>"
msgstr ""
"<span class=\"text-muted\">Définissez une structure spécifique si vous "
"souhaitez faire une fiche de paie supplémentaire (ex: bonus de fin d'année)."
" Si vous laissez ce champ vide, une fiche de paie régulière sera générée "
"pour tous les employés sélectionnés, en fonction de la configuration de "
"leurs contrats.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all"
" selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span colspan=\"4\" nolabel=\"1\">Cet assistant va générer les fiches de "
"paie de(s) l'employé(s) sélectionné(s) sur base des dates et des avoirs "
"figurant sur les bulletins de paie.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid ""
"<span role=\"status\" class=\"alert alert-warning\" attrs=\"{'invisible': "
"[('display_warning', '=', False)]}\">You have selected contracts that are "
"not running, this wizard can only index running contracts.</span>"
msgstr ""
"<span role=\"status\" class=\"alert alert-warning\" attrs=\"{'invisible': "
"[('display_warning', '=', False)]}\">Vous avez sélectionné des contrats qui "
"ne sont pas en cours, cet assistant peut uniquement indexer des contrats qui"
" sont en cours.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> hours/week</span>"
msgstr "<span>heures/semaine</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "<span>/ hour</span>"
msgstr "<span>/heure</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid ""
"<span><strong>Tip:</strong> Each time you edit the quantity or the amount on"
" a line, we recompute the following lines. We recommend that you edit from "
"top to bottom to prevent your edition from being overwritten by the "
"automatic recalculation. Be careful that reordering the lines doesn't "
"recompute them.</span>"
msgstr ""
"<span><strong>Conseil:</strong> Chaque fois que vous modifier la quantité ou"
" le montant d'une ligne, les lignes suivants sont recalculées. Nous vous "
"conseillons d'modifier les lignes de haut en bas pour éviter que vos "
"changements soient écrasés par le recalcul automatique. Soyez conscient que "
"réordonner les lignes ne les recalcule pas.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Basic Salary</strong>"
msgstr "<strong>Salaire de base</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Computed on </strong>"
msgstr "<strong>Calculé le </strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Contract Start Date</strong>"
msgstr "<strong>Date de début du contrat</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Contract Type </strong>"
msgstr "<strong>Type de Contrat </strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Designation</strong>"
msgstr "<strong>Désignation</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Employee</strong>"
msgstr "<strong>Employé</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Identification</strong>"
msgstr "<strong>Identification</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Marital Status</strong>"
msgstr "<strong>Status Civil</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Pay Period</strong>"
msgstr "<strong>Période de paie</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Person in charge</strong>"
msgstr "<strong>Personnes à charge</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Register Name:</strong>"
msgstr "<strong>Nom de dossier:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Working Schedule</strong>"
msgstr "<strong>Horaire de travail</strong>"

#. module: hr_payroll
#: model:mail.template,body_html:hr_payroll.mail_template_new_payslip
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Dear <t t-esc=\"object.employee_id.name\"/>, a new payslip is available for you.<br/><br/>\n"
"        Please find the PDF in your employee portal.<br/><br/>\n"
"        Have a nice day,<br/>\n"
"        The HR Team\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Bonjour <t t-esc=\"object.employee_id.name\"/>, une nouvelle fiche de paie est disponible pour vous.<br/><br/>\n"
"        Vous trouverez le PDF dans votre portail employés.<br/><br/>\n"
"        Belle journée,<br/>\n"
"        L'équipe RH\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_work_entry_type_is_unforeseen_is_leave
msgid "A unforeseen absence must be a leave."
msgstr "Une absence imprévue doit être un congé."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Absenteeism Rate"
msgstr "Taux d'absentéisme"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "Comptabilité"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__active
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Active"
msgstr "Actif"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__active_amount
msgid "Active Amount"
msgstr "Montant Actif"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_state
msgid "Activity State"
msgstr "État de l'Activité"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_from_type
msgid "Add a new salary structure"
msgstr "Ajoutez une nouvelle structure salariale"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr "Ajouter une note interne…"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips
msgid "All Payslips"
msgstr "Toutes les fiches de paie"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr "Allocation"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "Toujours vrai"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Amount"
msgstr "Montant"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_select
msgid "Amount Type"
msgstr "Type de montant"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Amount already paid."
msgstr "Montant déjà payé"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Amount to pay each month."
msgstr "Montant à payer chaque mois."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__active_amount
msgid ""
"Amount to pay for this month, Monthly Amount or less depending on the "
"Remaining Amount."
msgstr ""
"Montant à payer pour ce mois, montant mensuel ou moins selon le montant "
"restant."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__annually
msgid "Annually"
msgstr "Annuel"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"Another refund payslip with the same amount has been found. Do you want to "
"create a new one ?"
msgstr ""
"Une autre fiche de paie de remboursement avec le même montant a été trouvée."
" Voulez-vous en créer un nouveau ?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "Apparaît sur le bulletin de paie"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"Appliquer cette règle pour le calcul si la condition est vraie. Vous pouvez "
"spécifier une condition comme base> 1000."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Approximated end date."
msgstr "Date de fin approximative."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Archived"
msgstr "Archivé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__assignment_amount
msgid "Assigment of Salary"
msgstr "Cession de salaire"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__deduction_type__assignment
#, python-format
msgid "Assignment of Salary"
msgstr "Cession de salaire"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"At least one previous negative net could be reported on this payslip for <a "
"href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"
msgstr ""
"Au moins un montant net négatif précédent peut être déclaré sur cette fiche "
"de paie pour <a href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment_name
msgid "Attachment Name"
msgstr "Nom de la saisie"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__attachment_amount
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__deduction_type__attachment
#, python-format
msgid "Attachment of Salary"
msgstr "Saisie sur salaire"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Attendance Rate"
msgstr "Taux de présence"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid "Availability in Structure"
msgstr "Disponible dans la structure"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Basic Wage"
msgstr "Salaire de base moyen"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Gross Wage"
msgstr "Salaire brut moyen"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Hours per Day of Work"
msgstr "Heures moyennes par jour de travail"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Net Wage"
msgstr "Salaire net moyen"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Basic Wage"
msgstr "Moyenne du salaire de base"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Net Wage"
msgstr "Moyenne du salaire net"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_worker_basic
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr "Basique"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Basic Salary"
msgstr "Salaire de Base"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__basic_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__basic_wage
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Basic Wage"
msgstr "Salaire minimum"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__leave_basic_wage
msgid "Basic Wage for Time Off"
msgstr "Salaire de base pour les congés"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Batch"
msgstr "Mode groupé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Batch Name"
msgstr "Nom du lot"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
msgid "Batches"
msgstr "Lots"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr "Règles de paie belge"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__bi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "Bi-mensuel"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__bi-weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "Bi-hebdomadaire"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr "Calculs"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__calendar_changed
msgid "Calendar Changed"
msgstr "Calendrier modifié"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_cancel_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel"
msgstr "Annuler"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__cancel
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__cancelled
msgid "Cancelled"
msgstr "Annulé"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Cannot cancel a payslip that is done."
msgstr "Impossible d'annuler un bulletin de paie terminé."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Cannot mark payslip as paid if not confirmed."
msgstr ""
"Impossible de marquer la fiche de paie comme payée si elle n'est pas "
"confirmée."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__code
msgid ""
"Carefull, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Attention, le Code est utilisé dans de nombreuses références, sa "
"modification pourrait entraîner des changements indésirables."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr "Catégorie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
msgid "Change of Occupation"
msgstr "Changement d'occupation"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__child_support_amount
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__deduction_type__child_support
#, python-format
msgid "Child Support"
msgstr "Pension alimentaire"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "Enfants"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr "Choisir une localisation pour la paie"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__half-up
msgid "Closest"
msgstr "Le plus proche"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Code"
msgstr "Code"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "Code :"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Company"
msgstr "Société"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr "Contribution de la société"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__close
msgid "Completed"
msgstr "Achevé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr "Calcul"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_compute_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr "Calculer la feuille"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__compute_date
msgid "Computed On"
msgstr "Calculé le"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "Condition basée sur"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr "Conditions"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr "Confirmer"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__verify
msgid "Confirmed"
msgstr "Confirmé"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__conflict
msgid "Conflict"
msgstr "Conflit"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "Contract"
msgstr "Contrat"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Contract Type"
msgstr "Type de Contrat"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Historique des Contrats"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid "Contract indexing"
msgstr "Indexation des contrats"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_contract_repository
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__contract_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_all_contracts
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employees_root
msgid "Contracts"
msgstr "Contrats"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr "Registre de contribution"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_registers
#: model:ir.actions.report,name:hr_payroll.action_report_register
msgid "Contribution Registers"
msgstr "Registres des contributions"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "Indemnité de transport"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr "Indemnité de transport pour Gravie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__amount
msgid "Count"
msgstr "Comptage"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__country_id
msgid "Country"
msgstr "Pays"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_code
msgid "Country Code"
msgstr "Code du pays"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_confirm_payroll
msgid "Create Draft Entry"
msgstr "Créer un brouillon d'entrée"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Create SEPA payment"
msgstr "Créer un paiement SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_new_salary_attachment
msgid "Create Salary Attachment"
msgstr "Créer une saisie sur salaire"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_hr_contract_repository
msgid "Create a new contract"
msgstr "Créez un nouveau contrat"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_date
msgid "Created on"
msgstr "Créé le"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__credit_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__credit_note
msgid "Credit Note"
msgstr "Avoir"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Credit Notes"
msgstr "Avoirs"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__currency_id
msgid "Currency"
msgstr "Devise"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Current month"
msgstr "Mois actuel"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Date"
msgstr "Date"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_start
msgid "Date From"
msgstr "Date début"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__date_start
msgid "Date Start"
msgstr "Date de début"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_end
msgid "Date To"
msgstr "Date de fin"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_end
msgid "Date at which this assignment has been set as completed or cancelled."
msgstr ""
"Date à laquelle cette mission a été définie comme terminée ou annulée."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__full
msgid "Day"
msgstr "Jour"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Days"
msgstr "Jours"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave
msgid "Days of Paid Time Off"
msgstr "Jours de congés payés"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_unforeseen_absence
msgid "Days of Unforeseen Absence"
msgstr "Jours d'absence imprévue"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave_unpaid
msgid "Days of Unpaid Time Off"
msgstr "Jours de congés non payés"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule.category,name:hr_payroll.DED
#, python-format
msgid "Deduction"
msgstr "Déduction"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Default Scheduled Pay"
msgstr "Paiement planifié par défaut"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Type de prestation par défaut"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "Définit la fréquence de paiement du salaire."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, according to the "
"contract chosen. If the contract is empty, this field isn't mandatory "
"anymore and all the valid rules of the structures of the employee's "
"contracts will be applied."
msgstr ""
"Définit les règles qui doivent être appliquées à cette fiche de paie, selon "
"le contrat choisi. Si le contrat est vide, ce champ n'est plus obligatoire "
"et toutes les règles valides des structures des contrats du salarié seront "
"appliquées."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Department"
msgstr "Département"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "Description"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Discard"
msgstr "Ignorer"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Display in Payslip"
msgstr "Afficher dans la fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment
msgid "Document"
msgstr "Document"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__done
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr "Fait"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr "Lots terminés de bulletins de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr "Bulletin de paie terminé"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Double Holiday Pay"
msgstr "Double Pécule"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__down
msgid "Down"
msgstr "Bas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__draft
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft"
msgstr "Brouillon"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr "Lots brouillons de bulletins de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr "Bulletin de paie brouillon"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.server,name:hr_payroll.action_edit_payslip_lines
#, python-format
msgid "Edit Payslip Lines"
msgstr "Modifier les lignes de fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__edit_payslip_lines_wizard_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__edit_payslip_lines_wizard_id
msgid "Edit Payslip Lines Wizard"
msgstr "Assistant d'édition de lignes de fiche de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_worked_days_line
msgid "Edit payslip line wizard worked days"
msgstr "Assistant d'édition de ligne de fiche de paie jour ouvré"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_lines_wizard
msgid "Edit payslip lines wizard"
msgstr "Assistant d'édition de lignes de fiche de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_line
msgid "Edit payslip lines wizard line"
msgstr "Ligne d'assistant d'édition de lignes de fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__edited
msgid "Edited"
msgstr "Modifié"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Edition of Payslip Lines in the Payslip"
msgstr "Edition de lignes de fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr "Employé"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Contrat de l'employé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr "Fonction de l'employé"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_month_form
msgid "Employee Payslips"
msgstr "Bulletins de l'employé"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr "Salaire horaire brut de l'employé."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__resource_calendar_id
msgid "Employee's working schedule."
msgstr "Emploi du temps de l'employé."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__employee_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_contracts_configuration
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr "Employés"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_end
msgid "End Date"
msgstr "Date de fin"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_dates
msgid "End date may not be before the starting date."
msgstr "La date de fin ne peut pas être antérieure à la date de début."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_warning
msgid "Error"
msgstr "Erreur"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule_category.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr ""
"Erreur ! Vous ne pouvez pas créer de hiérarchie récursive de Categorie de "
"Règles de Salaire."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Estimated End Date"
msgstr "Date de fin estimée"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__partner_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr ""
"Éventuellement partie tierce impliquée dans le paiement des salaires des "
"employés."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Export Payslip"
msgstr "Exporter la fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "Montant fixe"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome e.g. fa-tasks"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "Par exemple, saisir 50.0 pour appliquer un pourcentage de 50%"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr "Paie française"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__date_from
msgid "From"
msgstr "De"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "From %s to %s"
msgstr "De %s à %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Full Time"
msgstr "À plein temps"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__full_time_required_hours
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__full_time_required_hours
msgid "Fulltime Hours"
msgstr "Heures à plein temps"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr "Général"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "Générer"

#. module: hr_payroll
#. openerp-web
#: code:addons/hr_payroll/static/src/js/work_entries_controller_mixin.js:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#, python-format
msgid "Generate Payslips"
msgstr "Générer les bulletins de paie"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_generate_payslips_from_work_entries
msgid "Generate payslips"
msgstr "Générer des fiches de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Génère les bulletins de paie pour tous les employés sélectionnés"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generated Payslips"
msgstr "Fiches de paie générées"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
#, python-format
msgid "Gross"
msgstr "Brut"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__gross_wage
msgid "Gross Wage"
msgstr "Salaire brut"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Regrouper par"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Type de prestations RH"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__half
msgid "Half Day"
msgstr "Demi-journée"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_message
msgid "Has Message"
msgstr "A un message"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_negative_net_to_report
msgid "Has Negative Net To Report"
msgstr "A un net négatif à reporter"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_refund_slip
msgid "Has Refund Slip"
msgstr "A une fiche de paie de retour"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment
msgid "Has Similar Attachment"
msgstr "A une saisie similaire"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment_warning
msgid "Has Similar Attachment Warning"
msgstr "A l'avertissement de saisie similaire"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_total_amount
msgid "Has Total Amount"
msgstr "A un montant total"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__hourly
msgid "Hourly Wage"
msgstr "Salaire horaire"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Hours of Work"
msgstr "Heures de travail"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hours_per_week
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__hours_per_week
msgid "Hours per Week"
msgstr "Heures par semaine"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "Allocation logement"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_unread
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__credit_note
msgid ""
"If its checked, indicates that all payslips generated from here are refund "
"payslips."
msgstr ""
"Si coché, indique que tous les bulletins de paie générés à partir d'ici sont"
" des bulletins de paie de remboursement."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"Si le champ actif est défini sur faux, la règle salariale sera masquée sans "
"être supprimée."

#. module: hr_payroll
#. openerp-web
#: code:addons/hr_payroll/static/src/xml/hr_contract_tree_views.xml:0
#, python-format
msgid "Index Contracts"
msgstr "Indexer les contrats"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_index
#: model:ir.actions.server,name:hr_payroll.action_index_contracts
msgid "Index contract(s)"
msgstr "Indexer contrat(s)"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_index
msgid "Index contracts"
msgstr "Indexer les contrats"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr "Paie Indienne"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "Indique que ce bulletin de paie est le remboursement d'un autre"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr "Données d'entrée"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "Note interne"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__internet_invoice
msgid "Internet Subscription Invoice"
msgstr "Facture d'abbonement internet"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__is_fulltime
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__is_fulltime
msgid "Is Full Time"
msgstr "Est à temps plein"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_paid
msgid "Is Paid"
msgstr "Est payé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Is Register"
msgstr "Est un registre"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_regular
msgid "Is Regular"
msgstr "Est régulière"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_superuser
msgid "Is Superuser"
msgstr "Est un super-utilisateur"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_view_kanban
msgid "Is a Blocking Reason?"
msgstr "Est-ce un motif de blocage ?"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. E.g. a rule for "
"Meal Voucher having fixed amount of 1€ per worked day can have its quantity "
"defined in expression like worked_days.WORK100.number_of_days."
msgstr ""
"Il est utilisé dans le calcul du pourcentage et du montant fixe. Par "
"exemple. une règle pour un chèque-repas ayant un montant fixe de 1 € par "
"jour ouvré peut avoir sa quantité définie dans l'expression comme "
"working_days.WORK100.number_of_days."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. E.g. a rule for salesmen having 1%% commission of"
" basic salary per product can defined in expression like: result = "
"inputs.SALEURO.amount * contract.wage * 0.01."
msgstr ""
"Il est utilisé dans le calcul. Par exemple. une règle pour les vendeurs "
"ayant 1%% commission de salaire de base par produit peut être définie dans "
"une expression comme : résultat = intrants.SALEURO.montant * contrat.salaire"
" * 0,01."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__job_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Job Position"
msgstr "Poste occupé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Last 365 Days Payslip"
msgstr "Fiche de paie des 365 derniers jours"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Month"
msgstr "Le mois dernier"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Quarter"
msgstr "Dernier trimestre"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Line Name"
msgstr "Ligne de nom"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"La liaison d'une catégorie salariale à son parent est utilisée uniquement "
"dans le but de rapports."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__email_cc
msgid "List of cc from incoming emails."
msgstr "Liste de cc des emails entrants."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid
msgid "Made Payment Order ? "
msgstr "Établir l'ordre de paiement "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Made by Odoo with ❤️"
msgstr "Réalisé par Odoo avec ❤️"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Mark as Completed"
msgstr "Marquer comme terminé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Mark as paid"
msgstr "Marquer comme payé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "Plage maximale"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "Chèque Repas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_ids
msgid "Messages"
msgstr "Messages"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "Plage minimum"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr "Facture d'abbonement de téléphone"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_contribution_register
msgid "Model for Printing hr.payslip.line grouped by register"
msgstr "Modèle pour imprimer hr.payslip.line groupé par registre"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__monthly
msgid "Monthly"
msgstr "Mensuel"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__monthly_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Monthly Amount"
msgstr "Montant mensuel"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__monthly
msgid "Monthly Fixed Wage"
msgstr "Salaire fixe mensuel"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_monthly_amount
msgid "Monthly amount must be strictly positive."
msgstr "Le montant mensuel doit être strictement positif."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Name"
msgstr "Nom"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__payslip_name
msgid ""
"Name to be set on a payslip. Example: 'End of the year bonus'. If not set, "
"the default value is 'Salary Slip'"
msgstr ""
"Nom à indiquer sur une fiche de paie. Exemple: 'Bonus de fin d'année'. S'il "
"n'est pas défini, la valeur par défaut est 'Salaire'"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_amount
msgid "Negative Net To Report Amount"
msgstr "Montant de net négatif à reporter"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_display
msgid "Negative Net To Report Display"
msgstr "Affichache du net négatif à reporter"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_message
msgid "Negative Net To Report Message"
msgstr "Message de net négatif à reporter"

#. module: hr_payroll
#: model:mail.activity.type,name:hr_payroll.mail_activity_data_hr_payslip_negative_net
msgid "Negative Net to Report"
msgstr "Net négatif à reporter"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr "Net"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_view_kanban
msgid "Net -"
msgstr "Net -"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Net Salary"
msgstr "Salaire Net"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__net_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__net_wage
msgid "Net Wage"
msgstr "Salaire net"

#. module: hr_payroll
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__draft
#, python-format
msgid "New"
msgstr "Nouveau"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "New Payslip"
msgstr "Nouvelle fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'Activité à Venir"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé d'activité suivante"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__no
msgid "No Rounding"
msgstr "Aucun arrondi"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_employee_unique_registration_number
msgid "No duplication of registration numbers is allowed"
msgstr "Aucune duplication des numéros d'enregistrement n'est autorisée"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
#, python-format
msgid "No rule parameter with code '%s' was found for %s "
msgstr "Aucun paramètre de règle avec le code '%s' n'a été trouvé pour %s "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__normal_wage
msgid "Normal Wage"
msgstr "Salaire Normal"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"Note: There are previous payslips with a negative amount for a total of %s "
"to report."
msgstr ""
"Note: Il y a des fiches de paie précédentes avec un montant négatif pour un "
"total de %s à reporter "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr "Notes"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "Nothing to show"
msgstr "Rien à montrer"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Number of Days"
msgstr "Nombre de jours"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Number of Hours"
msgstr "Nombre d'Heures"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__full_time_required_hours
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__full_time_required_hours
msgid "Number of hours to work to be considered as fulltime."
msgstr "Nombre d'heures de travail pour être considéré comme temps plein."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_unread_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer"
msgstr "Fonctionnaire"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Info"
msgstr "Autres informations"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Other Input"
msgstr "Autre entrée"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__input_line_type_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___allowed_input_type_ids
msgid "Other Input Line"
msgstr "Autre ligne d'entrée"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_entry_type_view
msgid "Other Input Types"
msgstr "Autres types d'entrée"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr "Autres entrées"

#. module: hr_payroll
#: model:hr.work.entry.type,name:hr_payroll.hr_work_entry_type_out_of_contract
msgid "Out of Contract"
msgstr "Hors Contrat"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__paid
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__paid
msgid "Paid"
msgstr "Payé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Paid Amount"
msgstr "Montant payé"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__2
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Paid Time Off"
msgstr "Congés payés"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Paid Time Off (Basic Wage)"
msgstr "Congés payés (salaire de base)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Parameter Value"
msgstr "Valeur du paramètre"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "Parent"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__partner_id
msgid "Partner"
msgstr "Partenaire"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__payslip_id
msgid "Pay Slip"
msgstr "Feuille de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__slip_id
msgid "PaySlip"
msgstr "Fiche de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Lines by Contribution Register"
msgstr "Lignes du bulletin par registre de contribution"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Name"
msgstr "Nom du bulletin"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_root
#: model:ir.ui.menu,name:hr_payroll.menu_report_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "Paie"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_reset_work_entries
msgid "Payroll - Technical: Reset Work Entries"
msgstr "Paie - Technique : Réinitialiser les prestations"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.payroll_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Payroll Analysis"
msgstr "Analyse de la paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Rapport d'analyse de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "Entrées de règles de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll SEPA"
msgstr "Paie SEPA"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr "Structures des bulletins"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr "Règles de paie qui s'appliquent dans votre pays"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account
msgid "Payroll with Accounting"
msgstr "Paie avec Comptabilité"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account_sepa
msgid "Payroll with SEPA payment"
msgstr "Paie avec paiement SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_generate_payslip_pdfs_ir_actions_server
#: model:ir.cron,cron_name:hr_payroll.ir_cron_generate_payslip_pdfs
#: model:ir.cron,name:hr_payroll.ir_cron_generate_payslip_pdfs
msgid "Payroll: Generate pdfs"
msgstr "Paie : Générer des pdf"

#. module: hr_payroll
#: model:mail.template,name:hr_payroll.mail_template_new_payslip
msgid "Payroll: New Payslip"
msgstr "Paie : Nouvelle fiche de paie"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_update_payroll_data_ir_actions_server
#: model:ir.cron,cron_name:hr_payroll.ir_cron_update_payroll_data
#: model:ir.cron,name:hr_payroll.ir_cron_update_payroll_data
msgid "Payroll: Update data"
msgstr "Paie : mettre à jour les données"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__payslip_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#, python-format
msgid "Payslip"
msgstr "Feuille de paie"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Payslip 'Date From' must be earlier 'Date To'."
msgstr "La date de début de la fiche de paie doit précéder la date de fin."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batch"
msgstr "Lot de bulletins de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lots de bulletins de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payslip_count
msgid "Payslip Count"
msgstr "Décompte de la fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__payslip_end_date
msgid "Payslip End Date"
msgstr "Date de fin de la fiche de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input"
msgstr "Entrée du bulletin"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input Name"
msgstr "Nom de l'entrée de la fiche de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input_type
msgid "Payslip Input Type"
msgstr "Type d'entrée de la fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "Entrées du bulletin de salaire"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr "Ligne de bulletin de salaire"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__line_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr "Lignes du bulletin de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__payslip_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__payslip_id
msgid "Payslip Name"
msgstr "Nom de bulletin de paie"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_input_type
msgid "Payslip Other Input Types"
msgstr "Autres types d'entrée de la fiche de paie"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__payslip_start_date
msgid "Payslip Start Date"
msgstr "Date de début de la fiche de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "Bulletin de paie jours travaillés"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_payslips
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
#, python-format
msgid "Payslips"
msgstr "Feuilles de paie"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr "Lots de bulletins de paie"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payslip_action_view_to_pay
msgid "Payslips To Pay"
msgstr "Fiches de paie à payer"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "Bulletins de paie par employé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__percentage
msgid "Percentage"
msgstr "Pourcentage"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "Pourcentage (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "Pourcentage basé sur"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr "Période"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "Transmettre les bulletins de paie au service comptabilité"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Previous Negative Payslip to Report"
msgstr "Fiches de paie précédentes négatives à reporter"

#. module: hr_payroll
#. openerp-web
#: code:addons/hr_payroll/static/src/xml/payslip_tree_views.xml:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#, python-format
msgid "Print"
msgstr "Imprimer"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "Taxe professionnelle"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Prorated end-of-year bonus"
msgstr "Bonus de fin d'année au prorata"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "Caisse de prévoyance"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__code
msgid "Python Code"
msgstr "Code Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "Condition Python"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "Expression Python"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Python data structure"
msgstr "Structure de données Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__quantity
msgid "Quantity"
msgstr "Quantité"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Quantity/Rate"
msgstr "Quantité/taux"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__quarterly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__quarterly
msgid "Quarterly"
msgstr "Trimestriel"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__queued_for_pdf
msgid "Queued For Pdf"
msgstr "En file d'attente pour le PDF"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "Plage"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "Plage basée sur"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "Taux (%)"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_recompute_whole_sheet
msgid "Recompute Whole Sheet"
msgstr "Recalculer la feuille entière"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Recompute the payslip lines only, not the worked days / input lines"
msgstr ""
"Recalculer uniquement les lignes de fiche de paie, pas les jours "
"travaillés/lignes de saisie"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#, python-format
msgid "Recorded a new payment of %s."
msgstr "Enregistré un nouveau paiement de %s."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "Référence"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr "Avoir"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Refund: %(payslip)s"
msgstr "Remboursement: %(payslip)s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__registration_number
msgid "Registration Number of the Employee"
msgstr "Numéro d'enregistrement de l'employé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_struct_id
msgid "Regular Pay Structure"
msgstr "Structure de paie régulière"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__1
msgid "Regular Working Day"
msgstr "Journée de travail régulière"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Reimbursement"
msgstr "Remboursement"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__cancel
msgid "Rejected"
msgstr "Rejeté"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining Amount"
msgstr "Montant restant"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_remaining_amount
msgid "Remaining amount must be positive."
msgstr "Le montant restant doit être positif."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining amount to be paid."
msgstr "Le montant restant doit être payé."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__report_id
msgid "Report"
msgstr "Rapport"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit_contract
msgid "Reporting"
msgstr "Analyse"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_resource_calendar
msgid "Resource Working Time"
msgstr "Temps de travail de la ressource"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur Responsable"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Round Type"
msgstr "Type d'arrondi"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days
msgid "Rounding"
msgstr "Arrondi"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "Règle"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
msgid "Rule Categories"
msgstr "Catégories de règles"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Rule Name"
msgstr "Nom de la règle"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_id
msgid "Rule Parameter"
msgstr "Paramètre de règle"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_parameter
msgid "Rule Parameters"
msgstr "Paramètres de la règle"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
msgid "Rules"
msgstr "Règles"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__open
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Running"
msgstr "En cours"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__sim_card
msgid "SIM Card Copy"
msgstr "Copie de la carte SIM"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_configuration
msgid "Salary"
msgstr "Salaire"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action_view_employee
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#, python-format
msgid "Salary Attachment"
msgstr "Saisie sur salaire"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment_report
msgid "Salary Attachment / Report"
msgstr "Saisie sur salaire / Rapport"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__salary_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_count
msgid "Salary Attachment Count"
msgstr "Nombre de saisies sur salaire"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_report_action
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_attachment_salary_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_report_view_pivot
msgid "Salary Attachment Report"
msgstr "Rapport de saisies sur salaire"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_count
msgid "Salary Attachment count"
msgstr "Nombre de saisies sur salaire"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_salary_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#, python-format
msgid "Salary Attachments"
msgstr "Saisies sur salaire"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr "Catégories de salaires"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr "Calcul de salaire"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Règles salariales"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr "Catégories de règles pour le salaire"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr "Catégorie de règle salariale"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Salary Rule Parameter"
msgstr "Paramètre de la règle de salariale"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter_value
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_value_view_form
msgid "Salary Rule Parameter Value"
msgstr "Valeur du paramètre de la règle salariale"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_rule_parameter_action
msgid "Salary Rule Parameters"
msgstr "Paramètres de la règle salariale"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__rule_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr "Règles salariales"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Salary Slip"
msgstr "Fiche de paie"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Structure"
msgstr "Structure salariale"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure_type
msgid "Salary Structure Type"
msgstr "Type de structure salariale"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_from_type
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Salary Structures"
msgstr "Structure des salaires"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__schedule_pay
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__schedule_pay
msgid "Scheduled Pay"
msgstr "Paie planifiée"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr "Rechercher des lots de bulletin de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr "Rechercher des lignes de bulletins"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr "Rechercher dans les feuilles de paie"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Search Salary Attachment"
msgstr "Rechercher des saisies sur salaire"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr "Rechercher une règle salariale"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Search Structure Type"
msgstr "Rechercher le type de structure"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__semi-annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "Semestriel"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr "Marquer comme brouillon"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr "Paramètres"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Slips to Confirm"
msgstr "Fiches à confirmer"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid ""
"Some part of %s's calendar is not covered by any work entry. Please complete"
" the schedule. Time intervals to look for:%s"
msgstr ""
"Certaines portions du calendrier de %sne sont pas couvertes par des "
"prestations. Veuillez compléter l'horaire. Intervales de temps à verifier: "
"%s"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "Some work entries could not be validated."
msgstr "Certaines entrées de travail n'ont pas pu être validées."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_start
msgid "Start Date"
msgstr "Date de début"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__state
msgid "State"
msgstr "État"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Status"
msgstr "Statut"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_id
msgid "Structure"
msgstr "Structure"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Name"
msgstr "Nom de la structure"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Type"
msgstr "Type de structure"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_type_count
msgid "Structure Type Count"
msgstr "Décompte du type de structure"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_structure_type
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_type
msgid "Structure Types"
msgstr "Types de structure"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "Structures"
msgstr "Structures"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Sum Worked Hours"
msgstr "Somme des heures travaillées"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sum_alw_category
msgid "Sum of Allowance category"
msgstr "Somme pour la catégorie d'indemnité"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Sum of Days"
msgstr "Somme des jours"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Code de pays ISO en deux caractères. \n"
"Vous pouvez utiliser ce champ pour une recherche rapide."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid ""
"The Work Entry checked as Unforeseen Absence will be counted in absenteeism "
"at work report."
msgstr ""
"La prestation marquée comme absence imprévue sera comptée dans le rapport "
"d'absentéisme au travail."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"Le code des règles salariales peuvent être utilisés comme référence dans le "
"calcul d'autres règles. Dans ce cas, il est sensible à la casse."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
msgid "The code that can be used in the salary rules"
msgstr "Code qui peut être utilisé dans les règles salariales"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "La méthode de calcul pour la règle de montant."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__contract_id
msgid "The contract this input should be applied to"
msgstr "Le contrat sur lequel cette entrée doit être appliquée"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract this worked days should be applied to"
msgstr "Le contrat sur lequel cette prestation doit être appliquée"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"The following employees have a contract outside of the payslip period:\n"
"%s"
msgstr ""
"Les salariés suivants ont un contrat hors période de fiche de paie :\n"
"%s"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"The following values are not valid:\n"
"%s"
msgstr ""
"Les valeurs suivantes ne sont pas valides :\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "Montant maximum, appliqué pour cette règle."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "Le montant minimum appliqué pour cette règle."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid ""
"The net amount will be recovered from the first positive remuneration "
"established after this."
msgstr ""
"Le montant net sera récupéré sur la première rémunération positive établie "
"ensuite."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "The payslips should be in Draft or Waiting state."
msgstr "La fiche de paie doit être en Brouillon ou en Attente."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "The work entry won’t grant any money to employee in payslip."
msgstr ""
"La prestation n'accordera aucune somme d'argent à l'employé sur la fiche de "
"paie."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This action is forbidden on validated payslips."
msgstr "Cette action est interdite sur les fiches de paie validées."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This action is restricted to payroll managers only."
msgstr "Cette action est réservée aux gestionnaires de paie uniquement."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__code
msgid "This code is used in salary rules to refer to this parameter."
msgstr ""
"Ce code est utilisé dans les règles de salaire pour faire référence à ce "
"paramètre."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid ""
"This input will be only available in those structure. If empty, it will be "
"available in all payslip."
msgstr ""
"Cette entrée sera uniquement disponible pour ces structures. Si ce champ est"
" vide, elle sera disponible dans toutes les fiches de paies. "

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"This payslip can be erroneous! Work entries may not be generated for the "
"period from %(start)s to %(end)s."
msgstr ""
"Cette fiche de paie pourrait être érronnée! Les prestations pourraient ne "
"pas avoir été générées du %(start)s au %(end)s. "

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_edit_payslip_lines_wizard.py:0
#, python-format
msgid "This payslip has been manually edited by %s."
msgstr "Cette fiche de paie a été modifiée manuellement par %s."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This payslip is not validated. This is not a legal document."
msgstr ""
"Cette fiche de paie n'est pas validée. Ce n'est pas un document légal."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.payroll_report_action
msgid "This report performs analysis on your payslip."
msgstr "Ce rapport effectue une analyse de votre fiche de paie."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_work_entry_report_action
msgid "This report performs analysis on your work entries."
msgstr "Ce rapport effectue une analyse de vos prestations."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""
"Sera utilisé pour calculer le % des valeurs des champs, en général "
"s'applique à la base, mais vous pouvez également utiliser les catégories de "
"champs de code en minuscules  en tant que nom de variables (hra, ma, Ita, "
"etc.) et la base variable."

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "Time intervals to look for:%s"
msgstr "Intervalles de temps à vérifier: %s "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_to
msgid "To"
msgstr "Vers"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Compute"
msgstr "À Calculer"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Confirm"
msgstr "À Confirmer"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips_to_pay
msgid "To Pay"
msgstr "A payer"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "To pay on"
msgstr "A payer le"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "To see something in this report, compute a payslip."
msgstr "Pour voir quelque chose dans ce rapport, calculez une fiche de paie."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__total
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Total"
msgstr "Total"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__total_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Amount"
msgstr "Montant total"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Total Gross Wage"
msgstr "Salaire brut total"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Total Net Wage"
msgstr "Salaire net total"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr "Nb. jours travaillés"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Hours"
msgstr "Heures de travail totales"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_total_amount
msgid ""
"Total amount must be strictly positive and greater than or equal to the "
"monthly amount."
msgstr ""
"Le montant total doit être strictement positif et supérieur ou égal au "
"montant mensuel."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__total_amount
msgid "Total amount to be paid."
msgstr "Montant total à payer."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Total hours of attendance and time off (paid or not)"
msgstr "Nombre total d'heures de présence et de congés (payés ou non)"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter__unique
msgid "Two rule parameters cannot have the same code."
msgstr "Deux paramètres de règle ne peuvent pas avoir le même code."

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter_value__unique
msgid "Two rules with the same code cannot start the same day"
msgstr "Deux règles avec le même code ne peuvent pas commencer le même jour"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__input_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__deduction_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Type"
msgstr "Type"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid "Unforeseen Absence"
msgstr "Absence imprévue"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Unpaid"
msgstr "Impayé"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__3
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Unpaid Time Off"
msgstr "Congés non payés"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__unpaid_work_entry_type_ids
msgid "Unpaid Work Entry Type"
msgstr "Type de prestation non rémunérée"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Unpaid Work Entry Types"
msgstr "Types de prestations non rémunérés"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "Unpaid in Structures Types"
msgstr "Non rémunéré dans les types de structure"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_unread
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_unread_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__up
msgid "Up"
msgstr "Haut"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
msgid "Use Worked Day Lines"
msgstr "Utiliser les lignes de jours travaillés"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "À utiliser pour définir les séquences de calcul"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "Utilisé pour montrer la règle de salaire sur la fiche de paie."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Validate"
msgstr "Valider"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Validate Edition"
msgstr "Valider l'édition"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__validated
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Validated"
msgstr "Validé"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__parameter_version_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Versions"
msgstr "Versions"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__wage_type
msgid "Wage Type"
msgstr "Type de salaire"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
#, python-format
msgid "Wage indexed by %.2f%% on %s"
msgstr "Salaire indexé par %.2f%% le %s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "En attente"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__warning_message
msgid "Warning Message"
msgstr "Message d'avertissement"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#, python-format
msgid "Warning, a similar attachment has been found."
msgstr "Attention, une saisie similaire a été trouvée."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Way of rounding the work entry type."
msgstr "Manière d'arrondir le type de prestation."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__weekly
msgid "Weekly"
msgstr "Hebdomadaire"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days
msgid ""
"When the work entry is displayed in the payslip, the value is rounded "
"accordingly."
msgstr ""
"Quand la prestation est affichée sur la fiche de paie, la valeur est "
"arrondie en conséquence."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__calendar_changed
msgid "Whether the previous or next contract has a different schedule or not"
msgstr "Si le contrat précédent ou suivant a un calendrier différent ou non"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_index__description
msgid ""
"Will be used as the message specifying why the wage on the contract has been"
" modified"
msgstr ""
"Sera utilisé comme message précisant pourquoi le salaire du contrat a été "
"modifié"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Work Days"
msgstr "Jours Travaillés"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_work_entries_root
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Work Entries"
msgstr "Prestations"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_work_entry_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Work Entries Analysis"
msgstr "Analyse des Prestations"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_report
msgid "Work Entries Analysis Report"
msgstr "Rapport d'analyse des prestations"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_work_entry_report
msgid "Work Entry Analysis"
msgstr "Analyse des Prestations"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Work Entry Type"
msgstr "Type de prestation"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work_hours
msgid "Work Hours"
msgstr "Heures de travail"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__work_time_rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Work Time Rate"
msgstr "Taux de temps de travail"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Type de prestation pour les présences régulières."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"Le ratio du taux de temps de travail par rapport à l'horaire complet à temps"
" plein devrait se situer entre 0 et 100 %."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_code
msgid "Work type"
msgstr "Type de travail"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_type
msgid "Work, (un)paid Time Off"
msgstr "Travail, congés (non) payés"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr "Jour travaillé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr "Jours travaillés"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr "Jours travaillés et entrées"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__worked_days_line_ids
msgid "Worked Days Lines"
msgstr "Ligne de prestation"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
msgid "Worked days won't be computed/displayed in payslips."
msgstr ""
"Les jours travaillés ne seront pas calculés/affichés sur les fiches de paie."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__resource_calendar_id
msgid "Working Schedule"
msgstr "Heures de travail"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong percentage base or quantity defined for:"
msgstr "Mauvais pourcentage de base ou quantité définie pour:"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python code defined for:"
msgstr "Mauvais code Python défini pour:"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python condition defined for:"
msgstr "Mauvaise condition Python définie pour:"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong quantity defined for:"
msgstr "Mauvaise quantité définie pour:"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong range condition defined for:"
msgstr "Mauvais intervalle conditionnel défini pour:"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "You can't validate a cancelled payslip."
msgstr "Vous ne pouvez pas valider une fiche de paie annulée."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip_input_type.py:0
#, python-format
msgid ""
"You cannot delete %s as it is used in another module but you can archive it "
"instead."
msgstr ""
"Vous ne pouvez pas supprimer %s, car c'est utilisé dans un autre module. "
"Veuillez plutôt l'archiver."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
#, python-format
msgid "You cannot delete a payslip batch which is not draft!"
msgstr ""
"Vous ne pouvez pas supprimer un lot de fiches de paie qui n'est pas en "
"brouillon !"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
#, python-format
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr ""
"Vous ne pouvez supprimer une fiche de paie qui ne soit ni brouillon ni "
"annulée !"

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#, python-format
msgid "You cannot delete a running salary attachment!"
msgstr "Vous ne pouvez pas supprimer une saisie sur salaire en cours."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "You cannot valide a payslip on which the contract is cancelled"
msgstr ""
"Vous ne pouvez pas valider une fiche de paie pour laquelle le contrat est "
"résilié"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
#, python-format
msgid ""
"You have selected non running contracts, if you really need to index them, "
"please do it by hand"
msgstr ""
"Vous avez sélectionné des contrats qui ne sont pas en cours, si vous avez "
"vraiment besoin de les indexer, veuillez le faire manuellement"

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "You must select employee(s) to generate payslip(s)."
msgstr ""
"Vous devez sélectionnner un (des) employé(s) pour générer une (des) fiche(s)"
" de paie."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip_line.py:0
#, python-format
msgid "You must set a contract to create a payslip line."
msgstr ""
"Vous devez définir un contrat pour créer une ligne de bulletin de paie."

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure_type.py:0
#, python-format
msgid "You should also be logged into a company in %s to set this country."
msgstr ""
"Vous devez également être connecté à une entreprise en/au %spour définir ce "
"pays."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "days"
msgstr "jours"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "e.g. April 2021"
msgstr "par exemple : Avril 2021"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "e.g. Employee"
msgstr "par exemple : Employé"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net"
msgstr "par exemple : Net"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net Salary"
msgstr "par exemple : Salaire Net"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "e.g. Regular Pay"
msgstr "par exemple : Salaire régulier"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "of"
msgstr "de"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "le résultat sera affecté à une variable"

#. module: hr_payroll
#: model:mail.template,subject:hr_payroll.mail_template_new_payslip
msgid "{{ object.employee_id.name }}, a new payslip is available for you"
msgstr ""
"{{ object.employee_id.name }}, une nouvelle fiche de paie est disponible "
"pour vous"
