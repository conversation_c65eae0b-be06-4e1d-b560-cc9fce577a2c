# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:11+0000\n"
"PO-Revision-Date: 2017-11-30 13:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: French (Canada) (https://www.transifex.com/odoo/teams/41243/"
"fr_CA/)\n"
"Language: fr_CA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Grouper par"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_report_hr_payroll_contribution_register__id
msgid "ID"
msgstr "Identifiant"
