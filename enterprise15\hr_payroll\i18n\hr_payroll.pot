# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 19:53+0000\n"
"PO-Revision-Date: 2024-08-13 19:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count
msgid "# Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_count
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "# Payslips"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid ""
"%s:\n"
"- Employee: %s\n"
"- Contract: %s\n"
"- Payslip: %s\n"
"- Salary rule: %s (%s)\n"
"- Error: %s"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When user cancel payslip the status is 'Rejected'."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "-> Report"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Hour"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Month"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_13th_month_salary
msgid "13th pay salary"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span class=\"ml-2\"> hours/week</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Payroll Rules</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "<span class=\"o_stat_text\">Payslips</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span class=\"text-muted\">Set a specific department if you wish to select "
"all the employees from this department (and subdepartments) at once.</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span class=\"text-muted\">Set a specific structure if you wish to make an "
"extra payslip (eg: End of the year bonus). If you leave this field empty, a "
"regular payslip will be generated for all the selected employees, based on "
"their contracts configuration.</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all"
" selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid ""
"<span role=\"status\" class=\"alert alert-warning\" attrs=\"{'invisible': "
"[('display_warning', '=', False)]}\">You have selected contracts that are "
"not running, this wizard can only index running contracts.</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> %</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> hours/week</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "<span>/ hour</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid ""
"<span><strong>Tip:</strong> Each time you edit the quantity or the amount on"
" a line, we recompute the following lines. We recommend that you edit from "
"top to bottom to prevent your edition from being overwritten by the "
"automatic recalculation. Be careful that reordering the lines doesn't "
"recompute them.</span>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Basic Salary</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Computed on </strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Contract Start Date</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Contract Type </strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Designation</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Employee</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Identification</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Marital Status</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Pay Period</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Person in charge</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Register Name:</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Total</strong>"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong>Working Schedule</strong>"
msgstr ""

#. module: hr_payroll
#: model:mail.template,body_html:hr_payroll.mail_template_new_payslip
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Dear <t t-esc=\"object.employee_id.name\"/>, a new payslip is available for you.<br/><br/>\n"
"        Please find the PDF in your employee portal.<br/><br/>\n"
"        Have a nice day,<br/>\n"
"        The HR Team\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_work_entry_type_is_unforeseen_is_leave
msgid "A unforeseen absence must be a leave."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Absenteeism Rate"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__active
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Active"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__active_amount
msgid "Active Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_ids
msgid "Activities"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_state
msgid "Activity State"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_from_type
msgid "Add a new salary structure"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr ""

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Administrator"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips
msgid "All Payslips"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_select
msgid "Amount Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Amount already paid."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Amount to pay each month."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__active_amount
msgid ""
"Amount to pay for this month, Monthly Amount or less depending on the "
"Remaining Amount."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__annually
msgid "Annually"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"Another refund payslip with the same amount has been found. Do you want to "
"create a new one ?"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Approximated end date."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Archived"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__assignment_amount
msgid "Assigment of Salary"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__deduction_type__assignment
#, python-format
msgid "Assignment of Salary"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"At least one previous negative net could be reported on this payslip for <a "
"href=\"#\" data-oe-model=\"%s\" data-oe-id=\"%s\">%s</a>"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment_name
msgid "Attachment Name"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__attachment_amount
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__deduction_type__attachment
#, python-format
msgid "Attachment of Salary"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Attendance Rate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid "Availability in Structure"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Basic Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Gross Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Hours per Day of Work"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Average Net Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Basic Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Net Wage"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_worker_basic
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Basic Salary"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__basic_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__basic_wage
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Basic Wage"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__leave_basic_wage
msgid "Basic Wage for Time Off"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Batch"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Batch Name"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
msgid "Batches"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__bi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__bi-weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__calendar_changed
msgid "Calendar Changed"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_cancel_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__cancel
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__cancelled
msgid "Cancelled"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Cannot cancel a payslip that is done."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Cannot mark payslip as paid if not confirmed."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__code
msgid ""
"Carefull, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
msgid "Change of Occupation"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__child_support_amount
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__deduction_type__child_support
#, python-format
msgid "Child Support"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__half-up
msgid "Closest"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Code"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Company"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__close
msgid "Completed"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_compute_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__compute_date
msgid "Computed On"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__verify
msgid "Confirmed"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__conflict
msgid "Conflict"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "Contract"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Contract Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid "Contract indexing"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_contract_repository
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__contract_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_all_contracts
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employees_root
msgid "Contracts"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_registers
#: model:ir.actions.report,name:hr_payroll.action_report_register
msgid "Contribution Registers"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__amount
msgid "Count"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__country_id
msgid "Country"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_code
msgid "Country Code"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_confirm_payroll
msgid "Create Draft Entry"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Create SEPA payment"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_new_salary_attachment
msgid "Create Salary Attachment"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_hr_contract_repository
msgid "Create a new contract"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_uid
msgid "Created by"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_date
msgid "Created on"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__credit_note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__credit_note
msgid "Credit Note"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Credit Notes"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__currency_id
msgid "Currency"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Current month"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_start
msgid "Date From"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__date_start
msgid "Date Start"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_end
msgid "Date To"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_end
msgid "Date at which this assignment has been set as completed or cancelled."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__full
msgid "Day"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Days"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave
msgid "Days of Paid Time Off"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_unforeseen_absence
msgid "Days of Unforeseen Absence"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave_unpaid
msgid "Days of Unpaid Time Off"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule.category,name:hr_payroll.DED
#, python-format
msgid "Deduction"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Default Scheduled Pay"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Default Work Entry Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, according to the "
"contract chosen. If the contract is empty, this field isn't mandatory "
"anymore and all the valid rules of the structures of the employee's "
"contracts will be applied."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Department"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Discard"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__display_name
msgid "Display Name"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Display in Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment
msgid "Document"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__done
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Double Holiday Pay"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__down
msgid "Down"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__draft
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.server,name:hr_payroll.action_edit_payslip_lines
#, python-format
msgid "Edit Payslip Lines"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__edit_payslip_lines_wizard_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__edit_payslip_lines_wizard_id
msgid "Edit Payslip Lines Wizard"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_worked_days_line
msgid "Edit payslip line wizard worked days"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_lines_wizard
msgid "Edit payslip lines wizard"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_line
msgid "Edit payslip lines wizard line"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__edited
msgid "Edited"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Edition of Payslip Lines in the Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__email_cc
msgid "Email cc"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_month_form
msgid "Employee Payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__resource_calendar_id
msgid "Employee's working schedule."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__employee_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_contracts_configuration
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_end
msgid "End Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_dates
msgid "End date may not be before the starting date."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_warning
msgid "Error"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule_category.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Estimated End Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__partner_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Export Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__date_from
msgid "From"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "From %s to %s"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Full Time"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__full_time_required_hours
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__full_time_required_hours
msgid "Fulltime Hours"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr ""

#. module: hr_payroll
#. openerp-web
#: code:addons/hr_payroll/static/src/js/work_entries_controller_mixin.js:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#, python-format
msgid "Generate Payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_generate_payslips_from_work_entries
msgid "Generate payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generated Payslips"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
#, python-format
msgid "Gross"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__gross_wage
msgid "Gross Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__half
msgid "Half Day"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_message
msgid "Has Message"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_negative_net_to_report
msgid "Has Negative Net To Report"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_refund_slip
msgid "Has Refund Slip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment
msgid "Has Similar Attachment"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment_warning
msgid "Has Similar Attachment Warning"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_total_amount
msgid "Has Total Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__hourly
msgid "Hourly Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Hours of Work"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hours_per_week
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__hours_per_week
msgid "Hours per Week"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__id
msgid "ID"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_unread
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__credit_note
msgid ""
"If its checked, indicates that all payslips generated from here are refund "
"payslips."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""

#. module: hr_payroll
#. openerp-web
#: code:addons/hr_payroll/static/src/xml/hr_contract_tree_views.xml:0
#, python-format
msgid "Index Contracts"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_index
#: model:ir.actions.server,name:hr_payroll.action_index_contracts
msgid "Index contract(s)"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_index
msgid "Index contracts"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__internet_invoice
msgid "Internet Subscription Invoice"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__is_fulltime
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__is_fulltime
msgid "Is Full Time"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_paid
msgid "Is Paid"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Is Register"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_regular
msgid "Is Regular"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_superuser
msgid "Is Superuser"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_view_kanban
msgid "Is a Blocking Reason?"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. E.g. a rule for "
"Meal Voucher having fixed amount of 1€ per worked day can have its quantity "
"defined in expression like worked_days.WORK100.number_of_days."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. E.g. a rule for salesmen having 1%% commission of"
" basic salary per product can defined in expression like: result = "
"inputs.SALEURO.amount * contract.wage * 0.01."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__job_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Job Position"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Last 365 Days Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category____last_update
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Month"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Quarter"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Line Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__email_cc
msgid "List of cc from incoming emails."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid
msgid "Made Payment Order ? "
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Mark as Completed"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Mark as paid"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_ids
msgid "Messages"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_contribution_register
msgid "Model for Printing hr.payslip.line grouped by register"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__monthly
msgid "Monthly"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__monthly_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Monthly Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__monthly
msgid "Monthly Fixed Wage"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_monthly_amount
msgid "Monthly amount must be strictly positive."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__payslip_name
msgid ""
"Name to be set on a payslip. Example: 'End of the year bonus'. If not set, "
"the default value is 'Salary Slip'"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_amount
msgid "Negative Net To Report Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_display
msgid "Negative Net To Report Display"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_message
msgid "Negative Net To Report Message"
msgstr ""

#. module: hr_payroll
#: model:mail.activity.type,name:hr_payroll.mail_activity_data_hr_payslip_negative_net
msgid "Negative Net to Report"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_view_kanban
msgid "Net -"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Net Salary"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__net_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__net_wage
msgid "Net Wage"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__draft
#, python-format
msgid "New"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "New Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__no
msgid "No Rounding"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_employee_unique_registration_number
msgid "No duplication of registration numbers is allowed"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
#, python-format
msgid "No rule parameter with code '%s' was found for %s "
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__normal_wage
msgid "Normal Wage"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"Note: There are previous payslips with a negative amount for a total of %s "
"to report."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "Nothing to show"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Number of Days"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_hours
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Number of Hours"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__full_time_required_hours
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__full_time_required_hours
msgid "Number of hours to work to be considered as fulltime."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_unread_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Info"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Other Input"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__input_line_type_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___allowed_input_type_ids
msgid "Other Input Line"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_entry_type_view
msgid "Other Input Types"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr ""

#. module: hr_payroll
#: model:hr.work.entry.type,name:hr_payroll.hr_work_entry_type_out_of_contract
msgid "Out of Contract"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__paid
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__paid
msgid "Paid"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Paid Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__2
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Paid Time Off"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Paid Time Off (Basic Wage)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Parameter Value"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__partner_id
msgid "Partner"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__payslip_id
msgid "Pay Slip"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__slip_id
msgid "PaySlip"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Lines by Contribution Register"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Name"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_root
#: model:ir.ui.menu,name:hr_payroll.menu_report_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_reset_work_entries
msgid "Payroll - Technical: Reset Work Entries"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.payroll_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Payroll Analysis"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll SEPA"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account
msgid "Payroll with Accounting"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account_sepa
msgid "Payroll with SEPA payment"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_generate_payslip_pdfs_ir_actions_server
#: model:ir.cron,cron_name:hr_payroll.ir_cron_generate_payslip_pdfs
#: model:ir.cron,name:hr_payroll.ir_cron_generate_payslip_pdfs
msgid "Payroll: Generate pdfs"
msgstr ""

#. module: hr_payroll
#: model:mail.template,name:hr_payroll.mail_template_new_payslip
msgid "Payroll: New Payslip"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_update_payroll_data_ir_actions_server
#: model:ir.cron,cron_name:hr_payroll.ir_cron_update_payroll_data
#: model:ir.cron,name:hr_payroll.ir_cron_update_payroll_data
msgid "Payroll: Update data"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__payslip_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#, python-format
msgid "Payslip"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Payslip 'Date From' must be earlier 'Date To'."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batch"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payslip_count
msgid "Payslip Count"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__payslip_end_date
msgid "Payslip End Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input_type
msgid "Payslip Input Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__line_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__payslip_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__payslip_id
msgid "Payslip Name"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_input_type
msgid "Payslip Other Input Types"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment_report__payslip_start_date
msgid "Payslip Start Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_payslips
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
#, python-format
msgid "Payslips"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payslip_action_view_to_pay
msgid "Payslips To Pay"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__percentage
msgid "Percentage"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Previous Negative Payslip to Report"
msgstr ""

#. module: hr_payroll
#. openerp-web
#: code:addons/hr_payroll/static/src/xml/payslip_tree_views.xml:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#, python-format
msgid "Print"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Prorated end-of-year bonus"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__code
msgid "Python Code"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Python data structure"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__quantity
msgid "Quantity"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Quantity/Rate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__quarterly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__quarterly
msgid "Quarterly"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__queued_for_pdf
msgid "Queued For Pdf"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_recompute_whole_sheet
msgid "Recompute Whole Sheet"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Recompute the payslip lines only, not the worked days / input lines"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#, python-format
msgid "Recorded a new payment of %s."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__number
msgid "Reference"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Refund: %(payslip)s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__registration_number
msgid "Registration Number of the Employee"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_struct_id
msgid "Regular Pay Structure"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__1
msgid "Regular Working Day"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Reimbursement"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__cancel
msgid "Rejected"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining Amount"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_remaining_amount
msgid "Remaining amount must be positive."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining amount to be paid."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__report_id
msgid "Report"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit_contract
msgid "Reporting"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_resource_calendar
msgid "Resource Working Time"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Round Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days
msgid "Rounding"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
msgid "Rule Categories"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Rule Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_id
msgid "Rule Parameter"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_parameter
msgid "Rule Parameters"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
msgid "Rules"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__open
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Running"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__sim_card
msgid "SIM Card Copy"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_configuration
msgid "Salary"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action_view_employee
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#, python-format
msgid "Salary Attachment"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment_report
msgid "Salary Attachment / Report"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__salary_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_count
msgid "Salary Attachment Count"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_report_action
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_attachment_salary_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_report_view_pivot
msgid "Salary Attachment Report"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_count
msgid "Salary Attachment count"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_salary_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#, python-format
msgid "Salary Attachments"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Salary Rule Parameter"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter_value
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_value_view_form
msgid "Salary Rule Parameter Value"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_rule_parameter_action
msgid "Salary Rule Parameters"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__rule_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "Salary Slip"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Structure"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure_type
msgid "Salary Structure Type"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_from_type
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Salary Structures"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__schedule_pay
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__schedule_pay
msgid "Scheduled Pay"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Search Salary Attachment"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Search Structure Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__semi-annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-annually
msgid "Semi-annually"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Slips to Confirm"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid ""
"Some part of %s's calendar is not covered by any work entry. Please complete"
" the schedule. Time intervals to look for:%s"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "Some work entries could not be validated."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_start
msgid "Start Date"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__state
msgid "State"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Status"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_id
msgid "Structure"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Name"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_type_count
msgid "Structure Type Count"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_structure_type
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_type
msgid "Structure Types"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "Structures"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Sum Worked Hours"
msgstr ""

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sum_alw_category
msgid "Sum of Allowance category"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Sum of Days"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid ""
"The Work Entry checked as Unforeseen Absence will be counted in absenteeism "
"at work report."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
msgid "The code that can be used in the salary rules"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__contract_id
msgid "The contract this input should be applied to"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract this worked days should be applied to"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"The following employees have a contract outside of the payslip period:\n"
"%s"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"The following values are not valid:\n"
"%s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid ""
"The net amount will be recovered from the first positive remuneration "
"established after this."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "The payslips should be in Draft or Waiting state."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "The work entry won’t grant any money to employee in payslip."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This action is forbidden on validated payslips."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This action is restricted to payroll managers only."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__code
msgid "This code is used in salary rules to refer to this parameter."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid ""
"This input will be only available in those structure. If empty, it will be "
"available in all payslip."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid ""
"This payslip can be erroneous! Work entries may not be generated for the "
"period from %(start)s to %(end)s."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_edit_payslip_lines_wizard.py:0
#, python-format
msgid "This payslip has been manually edited by %s."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "This payslip is not validated. This is not a legal document."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.payroll_report_action
msgid "This report performs analysis on your payslip."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_work_entry_report_action
msgid "This report performs analysis on your work entries."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "Time intervals to look for:%s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_to
msgid "To"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Compute"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Confirm"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips_to_pay
msgid "To Pay"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "To pay on"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "To see something in this report, compute a payslip."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__total
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Total"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__total_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Amount"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Total Gross Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Total Net Wage"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Hours"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_total_amount
msgid ""
"Total amount must be strictly positive and greater than or equal to the "
"monthly amount."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__total_amount
msgid "Total amount to be paid."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Total hours of attendance and time off (paid or not)"
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter__unique
msgid "Two rule parameters cannot have the same code."
msgstr ""

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter_value__unique
msgid "Two rules with the same code cannot start the same day"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__input_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__deduction_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid "Unforeseen Absence"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Unpaid"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__3
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Unpaid Time Off"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__unpaid_work_entry_type_ids
msgid "Unpaid Work Entry Type"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Unpaid Work Entry Types"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "Unpaid in Structures Types"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_unread
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_unread
msgid "Unread Messages"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_unread_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__up
msgid "Up"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
msgid "Use Worked Day Lines"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Validate"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Validate Edition"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__validated
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Validated"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__parameter_version_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Versions"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__wage_type
msgid "Wage Type"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
#, python-format
msgid "Wage indexed by %.2f%% on %s"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__warning_message
msgid "Warning Message"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#, python-format
msgid "Warning, a similar attachment has been found."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Way of rounding the work entry type."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure__schedule_pay__weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__weekly
msgid "Weekly"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days
msgid ""
"When the work entry is displayed in the payslip, the value is rounded "
"accordingly."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__calendar_changed
msgid "Whether the previous or next contract has a different schedule or not"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_index__description
msgid ""
"Will be used as the message specifying why the wage on the contract has been"
" modified"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "Work Days"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_work_entries_root
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Work Entries"
msgstr ""

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_work_entry_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Work Entries Analysis"
msgstr ""

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_report
msgid "Work Entries Analysis Report"
msgstr ""

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_work_entry_report
msgid "Work Entry Analysis"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Work Entry Type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work_hours
msgid "Work Hours"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__work_time_rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Work Time Rate"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_code
msgid "Work type"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_type
msgid "Work, (un)paid Time Off"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__worked_days_line_ids
msgid "Worked Days Lines"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
msgid "Worked days won't be computed/displayed in payslips."
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__resource_calendar_id
msgid "Working Schedule"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong percentage base or quantity defined for:"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python code defined for:"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python condition defined for:"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong quantity defined for:"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong range condition defined for:"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "You can't validate a cancelled payslip."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip_input_type.py:0
#, python-format
msgid ""
"You cannot delete %s as it is used in another module but you can archive it "
"instead."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
#, python-format
msgid "You cannot delete a payslip batch which is not draft!"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
#, python-format
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#, python-format
msgid "You cannot delete a running salary attachment!"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip.py:0
#, python-format
msgid "You cannot valide a payslip on which the contract is cancelled"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
#, python-format
msgid ""
"You have selected non running contracts, if you really need to index them, "
"please do it by hand"
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "You must select employee(s) to generate payslip(s)."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payslip_line.py:0
#, python-format
msgid "You must set a contract to create a payslip line."
msgstr ""

#. module: hr_payroll
#: code:addons/hr_payroll/models/hr_payroll_structure_type.py:0
#, python-format
msgid "You should also be logged into a company in %s to set this country."
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_dashboard
msgid "days"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "e.g. April 2021"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "e.g. Employee"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net Salary"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "e.g. Regular Pay"
msgstr ""

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "of"
msgstr ""

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr ""

#. module: hr_payroll
#: model:mail.template,subject:hr_payroll.mail_template_new_payslip
msgid "{{ object.employee_id.name }}, a new payslip is available for you"
msgstr ""
