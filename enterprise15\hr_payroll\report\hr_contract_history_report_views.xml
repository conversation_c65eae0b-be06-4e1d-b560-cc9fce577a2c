<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="hr_contract_history_view_form" model="ir.ui.view">
        <field name="name">hr.contract.history.form</field>
        <field name="model">hr.contract.history</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_history_view_form"/>
        <field name="arch" type="xml">
            <field name="contract_id" position="after">
                <field name="wage_type" invisible="1"/>
            </field>
            <div name="button_box" position="inside">
                <button class="oe_stat_button" name="action_open_payslips"
                        type="object" icon="fa-dollar" groups="hr_payroll.group_hr_payroll_user">
                    <field name="payslips_count" string="Payslips" widget="statinfo"/>
                </button>
                <button name="action_open_salary_attachments"
                    class="oe_stat_button"
                    icon="fa-files-o"
                    type="object"
                    groups="hr_payroll.group_hr_payroll_user">
                    <field name="salary_attachment_count" string="Salary Attachments" widget="statinfo"/>
                </button>
            </div>
        </field>
    </record>
</odoo>
