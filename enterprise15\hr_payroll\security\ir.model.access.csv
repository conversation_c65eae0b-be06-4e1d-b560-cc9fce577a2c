id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_hr_payroll_structure,hr.payroll.structure,model_hr_payroll_structure,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_structure_hr_user,hr.payroll.structure.hr.user,model_hr_payroll_structure,hr.group_hr_user,1,0,0,0
access_hr_payroll_structure_hr_contract_manager,hr.payroll.structure.hr.contract.manager,model_hr_payroll_structure,hr_contract.group_hr_contract_manager,1,0,0,0
access_hr_salary_rule_category,hr.salary.rule.category,model_hr_salary_rule_category,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip,hr.payslip,model_hr_payslip,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_line,hr.payslip.line,model_hr_payslip_line,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_input_user,hr.payslip.input.user,model_hr_payslip_input,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_input_type_user,hr.payslip.input.type.user,model_hr_payslip_input_type,hr_payroll.group_hr_payroll_user,1,0,0,0
access_hr_payslip_input_type_manager,hr.payslip.input.type.manager,model_hr_payslip_input_type,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_payslip_worked_days_officer,hr.payslip.worked_days.officer,model_hr_payslip_worked_days,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payslip_run,hr.payslip.run,model_hr_payslip_run,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_salary_rule_user,hr.salary.rule.user,model_hr_salary_rule,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_work_entry_type_manager,access_hr_work_entry_type_manager,model_hr_work_entry_type,group_hr_payroll_manager,1,1,1,1
access_hr_rule_parameter_manager,access_hr_rule_parameter_manager,model_hr_rule_parameter,group_hr_payroll_manager,1,1,1,1
access_hr_rule_parameter_user,access_hr_rule_parameter_user,model_hr_rule_parameter,hr.group_hr_user,1,0,0,0
access_hr_rule_parameter_value_manager,access_hr_rule_parameter_value_manager,model_hr_rule_parameter_value,group_hr_payroll_manager,1,1,1,1
access_hr_rule_parameter_value_user,access_hr_rule_parameter_value_user,model_hr_rule_parameter_value,hr.group_hr_user,1,0,0,0
access_hr_payroll_structure_type,hr.payroll.structure.type,model_hr_payroll_structure_type,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_payroll_report_manager,hr.payroll.report,model_hr_payroll_report,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_payslip_employees,access.hr.payslip.employees,model_hr_payslip_employees,hr_payroll.group_hr_payroll_manager,1,1,1,0
access_hr_payroll_index,access.hr.payroll.index,model_hr_payroll_index,hr_contract.group_hr_contract_manager,1,1,1,0
access_hr_work_entry_report_manager,hr.work.entry.report,model_hr_work_entry_report,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_payroll_edit_payslip_lines_wizard,access.hr.payroll.edit.payslip.lines.wizard,model_hr_payroll_edit_payslip_lines_wizard,hr_payroll.group_hr_payroll_manager,1,1,1,0
access_hr_payroll_edit_payslip_line,access.hr.payroll.edit.payslip.line,model_hr_payroll_edit_payslip_line,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_payroll_edit_payslip_worked_days_line,access.hr.payroll.edit.payslip.worked.days.line,model_hr_payroll_edit_payslip_worked_days_line,hr_payroll.group_hr_payroll_manager,1,1,1,1
access_hr_salary_assignment,hr.salary.attachment,model_hr_salary_attachment,hr_payroll.group_hr_payroll_user,1,1,1,1
access_hr_salary_assignment_report,hr.salary.attachment.reoprt,model_hr_salary_attachment_report,hr_payroll.group_hr_payroll_user,1,1,1,1
hr_work_entry.access_hr_work_entry_system,access_hr_work_entry_system,hr_work_entry.model_hr_work_entry,hr_payroll.group_hr_payroll_user,1,1,1,1
