<?xml version='1.0' encoding='UTF-8' ?>
<odoo>

     <!-- Root Menus -->
    <menuitem id="menu_hr_payroll_root" name="Payroll" sequence="195" web_icon="hr_payroll,static/description/icon.png" groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_employees_root"
        name="Contracts"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="50"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_work_entries_root"
        name="Work Entries"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="60"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_payslips"
        name="Payslips"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="70"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_report"
        name="Reporting"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
        sequence="70"
        groups="hr_payroll.group_hr_payroll_manager"/>

    <!-- **** Employees **** -->
    <menuitem
        id="menu_hr_payroll_contracts_configuration"
        name="Employees"
        action="hr_contract.hr_contract_history_view_list_action"
        parent="menu_hr_payroll_employees_root"
        sequence="20"/>

    <menuitem
        id="hr_menu_all_contracts"
        name="Contracts"
        action="hr_contract.action_hr_contract"
        parent="menu_hr_payroll_employees_root"
        sequence="30"/>

    <menuitem
        id="hr_menu_salary_attachments"
        name="Salary Attachments"
        action="hr_payroll.hr_salary_attachment_action"
        parent="menu_hr_payroll_employees_root"
        sequence="35"/>

    <!-- **** Payslips **** -->
    <menuitem
        id="menu_hr_payroll_employee_payslips"
        name="All Payslips"
        parent="menu_hr_payroll_payslips"
        sequence="60"
        action="action_view_hr_payslip_month_form"
        groups="hr_payroll.group_hr_payroll_user"/>

    <menuitem
        id="menu_hr_payroll_employee_payslips_to_pay"
        name="To Pay"
        parent="menu_hr_payroll_payslips"
        sequence="20"
        action="hr_payslip_action_view_to_pay"
        groups="hr_payroll.group_hr_payroll_user"/>

      <menuitem
        id="menu_hr_payslip_run"
        action="action_hr_payslip_run_tree"
        name="Batches"
        sequence="80"
        parent="menu_hr_payroll_payslips"/>

    <!-- **** Reporting **** -->
    <menuitem id="menu_report_payroll"
        name="Payroll"
        action="payroll_report_action"
        sequence="10"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_work_entry_report"
        name="Work Entry Analysis"
        action="hr_work_entry_report_action"
        sequence="20"
        parent="menu_hr_payroll_report"/>

    <menuitem
        id="menu_hr_payroll_attachment_salary_report"
        action="hr_salary_attachment_report_action"
        parent="menu_hr_payroll_report"
        sequence="25"/>

    <!-- **** Configuration **** -->

    <menuitem
        id="menu_hr_payroll_global_settings"
        name="Settings"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_configuration"
        sequence="0"
        action="action_hr_payroll_configuration"
        groups="base.group_system"/>

    <!-- Salary Configuration -->
    <menuitem
        id="menu_hr_salary_configuration"
        name="Salary"
        parent="hr_work_entry_contract_enterprise.menu_hr_payroll_configuration"
        sequence="80"
        groups="hr_payroll.group_hr_payroll_user"
    />

    <menuitem
        id="menu_action_hr_salary_rule_form"
        action="action_salary_rule_form"
        name="Rules"
        parent="menu_hr_salary_configuration"
        sequence="30"/>

    <menuitem
        id="menu_action_hr_salary_rule_parameter"
        action="hr_rule_parameter_action"
        name="Rule Parameters"
        parent="menu_hr_salary_configuration"
        sequence="35"/>

    <menuitem
        id="menu_hr_salary_rule_category"
        action="action_hr_salary_rule_category"
        name="Rule Categories"
        parent="menu_hr_salary_configuration"
        sequence="35"
        groups="base.group_no_one"
    />

    <menuitem
        id="menu_hr_payroll_structure_view"
        action="action_view_hr_payroll_structure_list_form"
        name="Structures"
        parent="menu_hr_salary_configuration"
        sequence="20"
    />

    <menuitem
        id="menu_hr_payroll_structure_type"
        name="Structure Types"
        action="action_hr_payroll_structure_type"
        parent="menu_hr_salary_configuration"
        sequence="10"/>

    <menuitem
        id="menu_hr_payslip_entry_type_view"
        action="action_view_hr_payslip_input_type"
        parent="menu_hr_salary_configuration"
        name="Other Input Types"
        sequence="40"/>

</odoo>
