<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="hr_payslip_input_type_view_form" model="ir.ui.view">
        <field name="name">hr.payslip.input.type.view.form</field>
        <field name="model">hr.payslip.input.type</field>
        <field name="arch" type="xml">
            <form string="Payslip Input" >
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="country_id" options="{'no_create': True, 'no_open': True}" groups="base.group_no_one"/>
                        </group>
                        <group>
                            <field name="code"/>
                        </group>
                        <group>
                            <field name="struct_ids" widget="many2many_tags" colspan="4"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_payslip_input_type_view_tree" model="ir.ui.view">
        <field name="name">hr.payslip.input.type.view.tree</field>
        <field name="model">hr.payslip.input.type</field>
        <field name="arch" type="xml">
            <tree string="Payslip Input" >
                <field name="name" placeholder="Payslip Input Name"/>
                <field name="code"/>
                <field name="struct_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>

    <record id="hr_payslip_input_type_view_kanban" model="ir.ui.view">
        <field name="name">hr.payslip.input.type.kanban.view</field>
        <field name="model">hr.payslip.input.type</field>
        <field name="arch" type="xml">
            <kanban>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div>
                                    <strong class="o_kanban_record_title"><span><field name="name"/></span></strong>
                                </div>
                                <div>
                                    <span class="text-muted o_kanban_record_subtitle"><field name="code"/></span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="action_view_hr_payslip_input_type" model="ir.actions.act_window">
        <field name="name">Payslip Other Input Types</field>
        <field name="res_model">hr.payslip.input.type</field>
        <field name="view_mode">tree,form,kanban</field>
    </record>

</odoo>
