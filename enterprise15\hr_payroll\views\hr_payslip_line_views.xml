<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Payslip Line -->
    <record id="view_hr_payslip_line_tree" model="ir.ui.view">
        <field name="name">hr.payslip.line.tree</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <tree string="Salary Structure" editable="bottom" decoration-info="total == 0">
                <field name="category_id"/>
                <field name="employee_id" invisible="1"/>
                <field name="sequence"/>
                <field name="name"/>
                <field name="code"/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                <field name="quantity"/>
                <field name="rate"/>
                <field name="amount"/>
                <field name="total"/>
                <field name="amount_select" invisible="1"/>
                <field name="partner_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="view_hr_payslip_line_tree_register" model="ir.ui.view">
        <field name="name">hr.payslip.line.tree.register</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <tree string="Salary Structure" edit="0" create="0" decoration-info="total == 0">
                <field name="employee_id" invisible="1"/>
                <field name="amount_select" invisible="1"/>
                <field name="code"/>
                <field name="partner_id"/>
                <field name="slip_id"/>
                <field name="total"/>
            </tree>
        </field>
    </record>

    <record id="view_hr_payslip_line_form" model="ir.ui.view">
        <field name="name">hr.payslip.line.form</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <form string="Payslip Line">
                <group>
                    <group>
                        <field name="partner_id"/>
                        <field name="name" string="Line Name"/>
                        <field name="code"/>
                        <field name="slip_id"/>
                        <field name="employee_id"/>
                    </group>
                    <group string="Calculations">
                        <field name="category_id"/>
                        <field name="amount_select"/>
                        <field name="amount_fix"  attrs="{'readonly':[('amount_select','!=','fix')]}"/>
                        <field name="amount_percentage"  attrs="{'readonly':[('amount_select','!=','percentage')]}"/>
                        <field name="sequence"/>
                    </group>
                    <field name="note"/>
                </group>
            </form>
        </field>
    </record>

    <record id="view_hr_payslip_line_filter" model="ir.ui.view">
        <field name="name">hr.payslip.line.select</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <search string="Search Payslip Lines">
                <field name="name" string="Payslip Lines" filter_domain="['|',('name','ilike',self),('code','ilike',self)]"/>
                <field name="amount_select"/>
                <field name="slip_id"/>
                <group col="8" colspan="4" expand="0" string="Group By">
                    <filter string="Salary Rule Category" name="category_id" context="{'group_by':'category_id'}"/>
                    <filter string="Contribution Register" name="partner_id" context="{'group_by':'partner_id'}"/>
                    <filter string="Employees" name="employee_id" context="{'group_by':'employee_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_payslip_line_view_search_register" model="ir.ui.view">
        <field name="name">hr.payslip.line.search.view</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <search string="Search Payslip Lines">
                <field name="name" string="Payslip Lines" filter_domain="['|',('name','ilike',self),('code','ilike',self)]"/>
                <field name="partner_id"/>
                <field name="amount_select"/>
                <field name="slip_id"/>
                <field name="date_from"/>
                <filter string="Is Register" name="has_partner" domain="[('partner_id', '!=', False)]"/>
                <separator/>
                <filter string="Last Month" name="last_month" domain="[('date_from', '&gt;=', (context_today() + relativedelta(months=-1)).strftime('%Y-%m-%d'))]"/>
                <filter string="Last Quarter" name="last_quarter" domain="[('date_from', '&gt;=', (context_today() + relativedelta(months=-3)).strftime('%Y-%m-%d'))]"/>
                <group col="8" colspan="4" expand="0" string="Group By">
                    <filter string="Salary Rule Category" name="category_id" context="{'group_by':'category_id'}"/>
                    <filter string="Contribution Register" name="group_by_partner_id" context="{'group_by':'partner_id'}"/>
                    <filter string="Employees" name="employee_id" context="{'group_by':'employee_id'}"/>
                    <filter string="Date" name="group_by_date_from" context="{'group_by': 'date_from'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_contribution_registers" model="ir.actions.act_window">
        <field name="name">Contribution Registers</field>
        <field name="res_model">hr.payslip.line</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_hr_payslip_line_tree_register"/>
        <field name="search_view_id" ref="hr_payslip_line_view_search_register"/>
        <field name="context">{'search_default_group_by_partner_id': 1, 'search_default_has_partner': 1}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_empty_folder">
            Nothing to show
          </p><p>
            To see something in this report, compute a payslip.
          </p>
        </field>
    </record>

    <record id="action_contribution_registers_view_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_hr_payslip_line_tree_register"/>
        <field name="act_window_id" ref="action_contribution_registers"/>
    </record>

    <record id="action_contribution_registers_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_hr_payslip_line_form"/>
        <field name="act_window_id" ref="action_contribution_registers"/>
    </record>

    <record id="act_contribution_reg_payslip_lines" model="ir.actions.act_window">
        <field name="name">Payslip Lines</field>
        <field name="res_model">hr.payslip.line</field>
        <field name="domain">[('partner_id', '=', active_id)]</field>
        <field name="context">{'default_partner_id': active_id, 'search_default_partner_id': 1}</field>
        <field name="binding_model_id" ref="base.model_res_partner"/>
        <field name="binding_view_types">form</field>
    </record>
</odoo>
