<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="hr_payslip_run_filter" model="ir.ui.view">
        <field name="name">hr.payslip.run.search</field>
        <field name="model">hr.payslip.run</field>
        <field name="arch" type="xml">
            <search string="Search Payslip Batches">
                <field name="name" string="Payslip Batch"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]" help="Draft Payslip Batches"/>
                <filter string="Done" name="done_filter" domain="[('state', '=', 'close')]" help="Done Payslip Batches"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_payslip_run_tree" model="ir.ui.view">
        <field name="name">hr.payslip.run.tree</field>
        <field name="model">hr.payslip.run</field>
        <field name="arch" type="xml">
            <tree string="Payslips Batches" sample="1">
                <field name="name"/>
                <field name="date_start"/>
                <field name="date_end"/>
                <field name="credit_note"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="hr_payslip_run_view_kanban" model="ir.ui.view">
        <field name="name">hr.payslip.run.kanban</field>
        <field name="model">hr.payslip.run</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" sample="1">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-6">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-6">
                                    <span class="float-right badge badge-secondary">
                                        <field name="state"/>
                                    </span>
                                </div>
                                <div class="col-12">
                                    <span>
                                        <field name="date_start"/> - <field name="date_end"/>
                                    </span>
                                    <span class="float-right" title="Is a Blocking Reason?">
                                        <field name="credit_note" widget="boolean"/>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_payslip_run_form" model="ir.ui.view">
        <field name="name">hr.payslip.run.form</field>
        <field name="model">hr.payslip.run</field>
        <field name="arch" type="xml">
            <form string="Payslips Batches">
            <header>
                <button name="%(action_hr_payslip_by_employees)d" type="action" states="draft" string="Generate Payslips" class="oe_highlight"/>
                <button name="action_validate" type="object" string="Validate" states="verify" class="oe_highlight" context="{'payslip_generate_pdf': True}"/>
                <button string="Mark as paid" name="action_paid" type="object" states="close" class="oe_highlight"/>
                <button string="Set to Draft" name="action_draft" type="object" states="verify,close"/>
                <field name="state" widget="statusbar"/>
            </header>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button name="action_open_payslips" class="oe_stat_button" icon="fa-book" type="object" help="Generated Payslips" attrs="{'invisible': [('payslip_count', '=', 0)]}">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value"><field name="payslip_count"/></span>
                            <span class="o_stat_text">Payslips</span>
                         </div>
                    </button>
                </div>
                <label for="name" string="Batch Name"/>
                <h1>
                    <field name="name" placeholder="e.g. April 2021"/>
                </h1>
                <group>
                    <group>
                        <label for="date_start" string="Period"/>
                         <div class="o_row">
                               <field name="date_start" class="oe_inline"/> - <field name="date_end" class="oe_inline"/>
                         </div>
                     </group>
                     <group>
                        <field name="credit_note"/>
                        <field name="company_id" groups="base.group_multi_company"/>
                        <field name="country_id" invisible="1"/>
                        <field name="country_code" invisible="1"/>
                    </group>
                </group>
            </sheet>
            </form>
        </field>
    </record>

    <record id="action_hr_payslip_run_tree" model="ir.actions.act_window">
        <field name="name">Payslips Batches</field>
        <field name="res_model">hr.payslip.run</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="hr_payslip_run_filter"/>
    </record>

</odoo>
