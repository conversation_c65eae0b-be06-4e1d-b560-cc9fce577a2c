<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_hr_payslip_tree" model="ir.ui.view">
        <field name="name">hr.payslip.tree</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <tree string="Payslips" js_class="hr_payroll_payslip_tree" sample="1" multi_edit="1">
                <field name="currency_id" invisible="1"/>
                <field name="number" decoration-bf="1"/>
                <field name="employee_id" widget="many2one_avatar_employee"/>
                <field name="payslip_run_id"/>
                <field name="struct_id" optional="hide"/>
                <field name="date_from" optional="hide"/>
                <field name="date_to" optional="hide"/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                <field name="basic_wage" widget="monetary" options="{'currency_field': 'currency_id'}" optional="show"/>
                <field name="net_wage" widget="monetary" options="{'currency_field': 'currency_id'}" decoration-bf="1" optional="show"/>
                <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-warning="state == 'verify'" decoration-success="state in ('done','paid')"/>
            </tree>
        </field>
    </record>

    <record id="hr_payslip_view_kanban" model="ir.ui.view">
        <field name="name">hr.payslip.kanban</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile" sample="1">
                <field name="currency_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-6">
                                    <strong><field name="employee_id"/></strong>
                                </div>
                                <div class="col-6">
                                    <span class="float-right badge badge-secondary">
                                        <field name="state"/>
                                    </span>
                                </div>
                                <div class="col-12">
                                    <span>
                                        <field name="date_from"/> - <field name="date_to"/>
                                    </span>
                                </div>
                                <div class="col-12">
                                    <span><field name="name"/></span>
                                </div>
                                <div class="col-12">
                                    <span>Net - <field name="net_wage"/></span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_hr_payslip_form" model="ir.ui.view">
        <field name="name">hr.payslip.form</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <form string="Payslip">
            <header>
                <button string="Confirm" name="action_payslip_done" type="object" states="verify" class="oe_highlight" context="{'payslip_generate_pdf': True}"/>
                <button string="Mark as paid" name="action_payslip_paid" type="object" states="done" class="oe_highlight"/>
                <button string="Refund" name="refund_sheet" type='object' attrs="{'invisible': ['|', '|', ('has_refund_slip', '=', True), ('credit_note', '=', True), ('state', 'not in', ('done', 'paid'))]}"/>
                <button string="Refund" name="refund_sheet" type='object' attrs="{'invisible': ['|', '|', ('has_refund_slip', '=', False), ('credit_note', '=', True), ('state', 'not in', ('done', 'paid'))]}"
                    confirm="Another refund payslip with the same amount has been found. Do you want to create a new one ?"/>
                <button string="Set to Draft" name="action_payslip_draft" type="object" states="cancel"/>
                <button string="Compute Sheet" name="compute_sheet" type="object" attrs="{'invisible': ['|', ('state', '!=', 'draft'), ('credit_note', '=', True)]}" class="oe_highlight" help="Recompute the payslip lines only, not the worked days / input lines"/>
                <button string="Compute Sheet" name="compute_sheet" type="object" attrs="{'invisible': ['|', ('state', '!=', 'verify'), ('credit_note', '=', True)]}" help="Recompute the payslip lines only, not the worked days / input lines"/>
                <button string="Cancel" name="action_payslip_cancel" type="object" states="draft,done,verify"/>
                <button string="Print" name="action_print_payslip" type="object"/>
                <button string="Export Payslip" name="action_export_payslip" type="object" attrs="{'invisible': [('is_superuser', '=', False)]}"/>
                <field name="state" widget="statusbar" statusbar_visible="draft,done,paid"/>
            </header>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button type="object" class="oe_stat_button" id="open_work_entries"
                        icon="fa-calendar" name="action_open_work_entries" string="Work Entries">
                    </button>
                    <button type="object" class="oe_stat_button" name="action_open_salary_attachments" id="open_salary_attachments"
                        icon="fa-files-o" attrs="{'invisible': [('salary_attachment_count', '=', 0)]}">
                        <field name="salary_attachment_count" widget="statinfo" string="Salary Attachments"/>
                    </button>
                </div>
                <div class="oe_title" name="title">
                    <h1><field name="employee_id" placeholder="Employee"/></h1>
                    <h2><field name="number" attrs="{'invisible': [('state', '=', 'draft')]}"/></h2>
                </div>
                 <div class="alert alert-warning" role="alert" attrs="{'invisible': [('warning_message','=',False)]}">
                    <field name="warning_message"/>
                </div>
                <group col="4">
                    <label for="date_from" string="Period"/>
                    <div>
                        <field name="date_from" class="oe_inline"/> - <field name="date_to" class="oe_inline"/>
                    </div>
                    <field name="contract_id" domain="[('employee_id','=',employee_id),('state','!=','cancel'),('date_start','&lt;=',date_to),'|',('date_end','&gt;=',date_from),('date_end','=',False)]" context="{'default_employee_id': employee_id}" required="1"/>
                    <field name="country_id" invisible="1"/>
                    <field name="country_code" invisible="1"/>
                    <field name="payslip_run_id" string="Batch"/>
                    <field name="struct_id" required="1"/>
                    <field name="salary_attachment_ids" invisible="1" force_save="1"/>
                    <field name="wage_type" invisible="1"/>
                    <field name="sum_worked_hours" invisible="1"/>
                    <field name="normal_wage" invisible="1"/>
                    <field name="credit_note" invisible="1"/>
                    <field name="is_superuser" invisible="1"/>
                    <field name="has_refund_slip" invisible="1"/>
                </group>
                <notebook>
                    <page string="Worked Days &amp; Inputs" name="worked_days_input">
                        <separator string="Worked Days"/>
                        <field name="worked_days_line_ids">
                            <tree string="Worked Days" editable="bottom" create="0" delete="0">
                                <field name="work_entry_type_id" readonly="1" force_save="1"/>
                                <field name="name"/>
                                <field name="number_of_days" sum="Total Working Days" readonly="1" force_save="1"/>
                                <field name="number_of_hours" sum="Total Working Hours" readonly="1" force_save="1"/>
                                <field name="amount" readonly="1" sum="Total Amount" force_save="1"/>
                                <field name="is_paid" invisible="True" readonly="1" force_save="1"/>
                                <field name="sequence" invisible="True" readonly="1" force_save="1"/>
                                <field name="currency_id" invisible="1"/>
                            </tree>
                            <form string="Worked Day">
                                <group col="4">
                                    <field name="work_entry_type_id"/>
                                    <field name="name"/>
                                    <field name="sequence"/>
                                    <field name="number_of_days"/>
                                    <field name="number_of_hours" widget="float_time"/>
                                    <field name="amount"/>
                                    <field name="is_paid"/>
                                    <field name="contract_id"/>
                                    <field name="currency_id" invisible="1"/>
                                </group>
                            </form>
                        </field>
                        <separator string="Other Inputs"/>
                        <div class="alert alert-warning" role="alert" attrs="{'invisible': [('negative_net_to_report_display', '=', False)]}">
                            <field name="negative_net_to_report_message"/>
                            <button name="action_report_negative_amount" class="oe_link" type="object" string=" -> Report"/>
                        </div>
                        <field name="input_line_ids" colspan="4" nolabel="1">
                            <tree string="Input Data" editable="bottom">
                                <!--
                                    Required 0 to force client to send on change request even when not defined
                                    Salary Attachments would otherwise not properly be computed and payslip_id is
                                    required for _allowed_input_type_ids to work..
                                -->
                                <field name="payslip_id" invisible="1" required="0"/>
                                <field name="_allowed_input_type_ids" invisible="1"/>
                                <field name="input_type_id"/>
                                <field name="name"/>
                                <field name="amount"/>
                                <field name="contract_id" invisible="True"/>
                                <field name="sequence" invisible="True"/>
                            </tree>
                            <form string="Payslip Line">
                                <group col="4">
                                    <field name="payslip_id" invisible="1"/>
                                    <field name="_allowed_input_type_ids" invisible="1"/>
                                    <field name="input_type_id"/>
                                    <field name="name"/>
                                    <field name="sequence"/>
                                    <field name="amount"/>
                                    <field name="contract_id"/>
                                </group>
                            </form>
                        </field>
                    </page>
                    <page string="Salary Computation" name="salary_computation">
                        <field name="line_ids" colspan="4" nolabel="1">
                            <tree string="Salary Structure" editable="bottom" decoration-info="total == 0" create="0" delete="0">
                                <field name="name"/>
                                <field name="code" readonly="1" force_save="1" optional="hide"/>
                                <field name="category_id" readonly="1" force_save="1"/>
                                <field name="sequence" invisible="1" readonly="1" force_save="1"/>
                                <field name="quantity" readonly="1" force_save="1"/>
                                <field name="rate" readonly="1" force_save="1"/>
                                <field name="salary_rule_id" groups="base.group_no_one" readonly="1" force_save="1"/>
                                <field name="amount" readonly="1" force_save="1"/>
                                <field name="total" readonly="1" force_save="1"/>
                                <field name="currency_id" invisible="1"/>
                            </tree>
                            <form string="Payslip Line">
                                <group col="4">
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="category_id"/>
                                    <field name="sequence"/>
                                    <field name="quantity"/>
                                    <field name="rate"/>
                                    <field name="amount"/>
                                    <field name="total"/>
                                    <field name="salary_rule_id"/>
                                    <field name="currency_id" invisible="1"/>
                                </group>
                            </form>
                        </field>
                    </page>
                    <page string="Other Info" name="account_info">
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                            <group name="accounting">
                                <field name="has_negative_net_to_report"/>
                                <field name="negative_net_to_report_display" invisible="1"/>
                                <field name="paid"/>
                            </group>
                        </group>
                        <div colspan="4">
                            <field name="note" placeholder="Add an internal note..."/>
                        </div>
                    </page>
                </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_hr_payslip_filter" model="ir.ui.view">
        <field name="name">hr.payslip.select</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <search string="Search Payslips">
                <field name="employee_id"/>
                <field name="name" string="Payslips" filter_domain="['|', ('name', 'ilike', self), ('number', 'ilike', self)]"/>
                <field name="date_from"/>
                <field name="contract_id"/>
                <field name="payslip_run_id"/>
                <filter string="To Compute" name="draft" domain="[('state', '=', 'draft')]" help="Draft Slip"/>
                <filter string="To Confirm" name="draft" domain="[('state', '=', 'verify')]" help="Slips to Confirm"/>
                <filter string="Done" name="done" domain="[('state', '=', 'done')]" help="Done Slip"/>
                <separator/>
                <filter string="Date" name="date_filter" date="date_to" default_period="last_month"/>
                <separator/>
                <filter string="Credit Notes" name="credit_note" domain="[('credit_note', '=', True)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee_id" context="{'group_by': 'employee_id'}"/>
                    <filter string="Department" name="department_id" context="{'group_by':'department_id'}"/>
                    <filter string="Job Position" name="job_id" context="{'group_by':'job_id'}"/>
                    <filter string="Status" name="state" context="{'group_by': 'state'}"/>
                    <filter string="Batch" name="group_by_batch" context="{'group_by': 'payslip_run_id'}"/>
                    <filter string="Company" name="company_id" groups="base.group_multi_company" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="hr_payslip_action_view_to_pay" model="ir.actions.act_window">
        <field name="name">Payslips To Pay</field>
        <field name="res_model">hr.payslip</field>
        <field name="view_mode">tree,kanban,form,pivot,activity</field>
        <field name="search_view_id" ref="view_hr_payslip_filter"/>
        <field name="domain">[('state', 'in', ['draft', 'verify'])]</field>
    </record>
    <record id="action_view_hr_payslip_form" model="ir.actions.act_window">
        <field name="name">Employee Payslips</field>
        <field name="res_model">hr.payslip</field>
        <field name="view_mode">tree,kanban,form,pivot</field>
        <field name="search_view_id" ref="view_hr_payslip_filter"/>
        <field name="context">{'search_default_group_by_batch': 1}</field>
    </record>
    <record id="action_view_hr_payslip_month_form" model="ir.actions.act_window">
        <field name="name">Employee Payslips</field>
        <field name="res_model">hr.payslip</field>
        <field name="view_mode">tree,kanban,form,activity</field>
        <field name="search_view_id" ref="view_hr_payslip_filter"/>
        <field name="context">{"search_default_group_by_batch":1}</field>
    </record>

    <record id="act_hr_employee_payslip_list" model="ir.actions.act_window">
        <field name="res_model">hr.payslip</field>
        <field name="name">Payslips</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_employee_id': [active_id], 'default_employee_id': active_id}</field>
    </record>

    <record model="ir.actions.server" id="action_hr_payroll_compute_payroll">
        <field name="name">Compute Sheet</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            records.compute_sheet()
        </field>
    </record>

    <record model="ir.actions.server" id="action_hr_payroll_recompute_whole_sheet">
        <field name="name">Recompute Whole Sheet</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_view_types">list,form</field>
        <field name="state">code</field>
        <field name="code">
            records.action_refresh_from_work_entries()
        </field>
    </record>

    <record model="ir.actions.server" id="action_hr_payroll_confirm_payroll">
        <field name="name">Confirm</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                records.action_payslip_done()
        </field>
    </record>

    <record model="ir.actions.server" id="action_hr_payroll_cancel_payroll">
        <field name="name">Cancel</field>
        <field name="model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="binding_model_id" ref="hr_payroll.model_hr_payslip"/>
        <field name="state">code</field>
        <field name="code">
            if records:
                records.action_payslip_cancel()
        </field>
    </record>

</odoo>
