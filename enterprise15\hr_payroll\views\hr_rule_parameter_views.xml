<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_rule_parameter_view_form" model="ir.ui.view">
        <field name="name">hr.rule.parameter.form</field>
        <field name="model">hr.rule.parameter</field>
        <field name="arch" type="xml">
            <form string="Salary Rule Parameter">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="code"/>
                    </group>
                    <group string="Versions">
                        <field name="parameter_version_ids" nolabel="1" context="{'default_rule_parameter_id': active_id}">
                            <tree>
                                <field name="date_from"/>
                                <field name="parameter_value"/>
                            </tree>
                        </field>
                    </group>
                    <field name="description" class="oe-bordered-editor" placeholder="Description"/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_rule_parameter_value_view_form" model="ir.ui.view">
        <field name="name">hr.rule.parameter.value.form</field>
        <field name="model">hr.rule.parameter.value</field>
        <field name="arch" type="xml">
            <form string="Salary Rule Parameter Value">
                <group>
                    <group>
                        <field name="rule_parameter_id"/>
                        <field name="code"/>
                    </group>
                    <group>
                        <field name="date_from"/>
                    </group>
                </group>
                <field name="parameter_value"/>
            </form>
        </field>
    </record>

    <record id="hr_rule_parameter_view_tree" model="ir.ui.view">
        <field name="name">hr.rule.parameter.tree</field>
        <field name="model">hr.rule.parameter</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="code"/>
            </tree>
        </field>
    </record>

    <record id="hr_rule_parameter_view_search" model="ir.ui.view">
        <field name="name">hr.rule.parameter.search</field>
        <field name="model">hr.rule.parameter</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
            </search>
        </field>
    </record>

    <record id="hr_rule_parameter_action" model="ir.actions.act_window">
        <field name="name">Salary Rule Parameters</field>
        <field name="res_model">hr.rule.parameter</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>
