<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_salary_attachment_view_tree" model="ir.ui.view">
        <field name="name">hr.salary.attachment.tree</field>
        <field name="model">hr.salary.attachment</field>
        <field name="arch" type="xml">
            <tree string="Salary Attachments">
                <field name="employee_id" widget="many2one_avatar_employee"/>
                <field name="description"/>
                <field name="deduction_type"/>
                <field name="monthly_amount" sum="Monthly Amount" widget="monetary"/>
                <field name="total_amount" optional="hide" widget="monetary"/>
                <field name="paid_amount" optional="hide" widget="monetary"/>
                <field name="date_start"/>
                <field name="state" widget="badge" decoration-success="state == 'open'" decoration-info="state == 'close'" decoration-danger="state == 'cancel'"/>
            </tree>
        </field>
    </record>

    <record id="hr_salary_attachment_view_form" model="ir.ui.view">
        <field name="name">hr.salary.attachment.form</field>
        <field name="model">hr.salary.attachment</field>
        <field name="arch" type="xml">
            <form string="Salary Attachment">
                <header>
                    <button name="action_done" type="object" string="Mark as Completed"
                        states="open,cancel"/>
                    <button name="action_open" type="object" string="Running"
                        states="close,cancel"/>
                    <button name="action_cancel" type="object" string="Cancel"
                        states="open"/>
                    <field name="state" widget="statusbar"
                        statusbar_visible="open,close"/>
                </header>
                <div role="alert" class="alert alert-warning text-center" attrs="{'invisible': [('has_similar_attachment', '=', False)]}">
                    <field name="has_similar_attachment_warning"/>
                    <field name="has_similar_attachment" invisible="1"/>
                </div>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object" name="action_open_payslips" class="oe_stat_button"
                            icon="fa-money" id="open_payslips" attrs="{'invisible': [('payslip_count', '=', 0)]}">
                            <field name="payslip_count" widget="statinfo" string="Payslips" />
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="employee_id"/>
                        <h1><field name="employee_id"/></h1>
                    </div>
                    <field name="company_id" invisible="1"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="has_total_amount" invisible="1"/>
                    <field name="attachment_name" invisible="1"/>
                    <group>
                        <group>
                            <field name="description"/>
                            <field name="deduction_type"/>
                            <field name="date_start"/>
                            <field name="date_estimated_end" attrs="{'invisible': [('date_estimated_end', '=', False)]}"/>
                            <field name="date_end" attrs="{'invisible': ['|', ('date_end', '=', False), ('state', '=', 'open')]}"/>
                            <field name="attachment" filename="attachment_name"/>
                        </group>
                        <group>
                            <field name="monthly_amount"/>
                            <field name="total_amount" attrs="{'invisible': [('has_total_amount', '=', False)], 'required': [('has_total_amount', '=', True)]}"/>
                            <field name="paid_amount" attrs="{'invisible': [('paid_amount', '=', 0)]}"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="hr_salary_attachment_view_search" model="ir.ui.view">
        <field name="name">hr.salary.attachment.search</field>
        <field name="model">hr.salary.attachment</field>
        <field name="arch" type="xml">
            <search string="Search Salary Attachment">
                <field name="employee_id"/>
                <field name="description"/>
                <field name="deduction_type"/>
                <field name="state"/>
                <filter string="Active" name="active_this_year"
                    domain="[
                        '&amp;', ('state', '=', 'open'),
                                 ('date_start', '&lt;=', (context_today().strftime('%Y-%m-%d')))
                    ]"/>
                <filter string="Employee" name="employee_id_group" context="{'group_by': 'employee_id'}"/>
                <filter string="Type" name="deduction_type" context="{'group_by': 'deduction_type'}"/>
                <filter string="Status" name="state" context="{'group_by': 'state'}"/>
            </search>
        </field>
    </record>

    <record id="hr_salary_attachment_action" model="ir.actions.act_window">
        <field name="name">Salary Attachment</field>
        <field name="res_model">hr.salary.attachment</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="hr_salary_attachment_action_view_employee" model="ir.actions.act_window">
        <field name="name">Salary Attachment</field>
        <field name="res_model">hr.salary.attachment</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{"search_default_employee_id": [active_id]}</field>
    </record>
</odoo>
