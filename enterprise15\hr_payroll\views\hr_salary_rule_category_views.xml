<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Salary Category-->
    <record id="hr_salary_rule_category_form" model="ir.ui.view">
        <field name="name">hr.salary.rule.category.form</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="arch" type="xml">
            <form string="Salary Categories">
            <sheet>
                <group name="category_details" col="4">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="parent_id"/>
                </group>
                <group name="notes" string="Notes">
                    <field name="note" nolabel="1"/>
                </group>
            </sheet>
            </form>
        </field>
    </record>

    <record id="hr_salary_rule_category_tree" model="ir.ui.view">
        <field name="name">hr.salary.rule.category.tree</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="arch" type="xml">
            <tree string="Salary Rule Categories">
                <field name="name"/>
                <field name="code"/>
                <field name="parent_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="view_hr_salary_rule_category_filter" model="ir.ui.view">
        <field name="name">hr.salary.rule.category.select</field>
        <field name="model">hr.salary.rule.category</field>
        <field name="arch" type="xml">
            <search string="Salary Rule Categories">
                <field name="name" string="Salary Rule Categories" filter_domain="['|',('name','ilike',self),('code','ilike',self)]"/>
           </search>
        </field>
    </record>

    <record id="action_hr_salary_rule_category" model="ir.actions.act_window">
        <field name="name">Salary Rule Categories</field>
        <field name="res_model">hr.salary.rule.category</field>
        <field name="view_id" ref="hr_salary_rule_category_tree"/>
       <field name="search_view_id" ref="view_hr_salary_rule_category_filter"/>
    </record>
</odoo>
