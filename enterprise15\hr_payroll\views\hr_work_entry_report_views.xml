<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_work_entry_report_view_search" model="ir.ui.view">
        <field name="name">hr.work.entry.report.view.search</field>
        <field name="model">hr.work.entry.report</field>
        <field name="arch" type="xml">
            <search string="Work Entries Analysis">
                <field name="work_entry_type_id" filter_domain="[('work_entry_type_id', 'ilike', self)]" />
                <field name="company_id" filter_domain="[('company_id', 'ilike', self)]"/>
                <field name="employee_id" filter_domain="[('employee_id', 'ilike', self)]"/>
                <field name="department_id" filter_domain="[('department_id', 'ilike', self)]"/>
                <field name="state" filter_domain="[('state', 'ilike', self)]" />
                <field name="date_start" />
                <separator/>
                <filter string="Current month" name="filter_date_start" date="date_start" default_period="this_month"/>
                <separator/>
                <filter string="Validated" name="filter_work_entry_validated" domain="[('state', '=', 'validated')]"/>
                <group expand="1" string="Group By">
                    <filter string="Work Entry Type" name="work_entry_type_id" context="{'group_by': 'work_entry_type_id'}"/>
                    <filter string="Employee" name="employee_id" context="{'group_by': 'employee_id'}"/>
                    <filter string="Department" name="department_id" context="{'group_by': 'department_id'}"/>
                    <filter string="Company" name="company_id" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_work_entry_report_view_pivot" model="ir.ui.view">
        <field name="name">hr.work.entry.report.view.pivot</field>
        <field name="model">hr.work.entry.report</field>
        <field name="arch" type="xml">
            <pivot string="Work Entries Analysis" sample="1">
                <field name="work_entry_type_id" type="row" />
                <field name="number_of_days" type="measure" />
            </pivot>
        </field>
    </record>

    <record id="hr_work_entry_report_view_tree" model="ir.ui.view">
        <field name="name">hr.work.entry.report.view.tree</field>
        <field name="model">hr.work.entry.report</field>
        <field name="arch" type="xml">
            <tree string="Work Entries Analysis">
                <field name="employee_id" widget="many2one_avatar_employee"/>
                <field name="department_id" optional="show"/>
                <field name="work_entry_type_id" optional="hide"/>
                <field name="date_start" optional="show"/>
                <field name="number_of_days" optional="show" sum="Sum of Days"/>
                <field name="state" optional="hide"/>
                <field name="company_id" optional="show" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="hr_work_entry_report_action" model="ir.actions.act_window">
        <field name="name">Work Entries Analysis</field>
        <field name="res_model">hr.work.entry.report</field>
        <field name="view_mode">pivot</field>
        <field name="search_view_id" ref="hr_work_entry_report_view_search"/>
        <field name="context">{
            'search_default_filter_date_start': True,
            'search_default_filter_work_entry_validated': True,
            'group_default_work_entry_type_id': True
        }</field>
        <field name="help">This report performs analysis on your work entries.</field>
    </record>
</odoo>
