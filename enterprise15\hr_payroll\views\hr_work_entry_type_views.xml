<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payroll_hr_work_entry_type_view_form_inherit" model="ir.ui.view">
        <field name="name">payroll.hr.work.entry.type.view.form.inherit</field>
        <field name="model">hr.work.entry.type</field>
        <field name="inherit_id" ref="hr_work_entry.hr_work_entry_type_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='main_group']" position="after">
                <group>
                    <group string="Display in Payslip">
                        <field name="round_days"/>
                        <field name="round_days_type" attrs="{'invisible': [('round_days', '=', 'NO')]}"/>
                    </group>
                    <group string="Unpaid">
                        <field name="unpaid_structure_ids" widget="many2many_tags"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>
    <record id="payroll_hr_work_entry_type_view_form_inherit_contract" model="ir.ui.view">
      <field name="name">payroll.hr.work.entry.type.view.form.inherit.contract</field>
      <field name="model">hr.work.entry.type</field>
      <field name="inherit_id" ref="hr_work_entry_contract.hr_work_entry_contract_type_view_form_inherit"/>
      <field name="arch" type="xml">
        <group name="time_off" position="after">
            <group string="Reporting">
                <field name="is_unforeseen"/>
            </group>
        </group>
      </field>
    </record>
</odoo>
