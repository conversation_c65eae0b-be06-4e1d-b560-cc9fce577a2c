<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_payslip">
    <t t-call="web.external_layout">
        <div class="page">
            <h2 t-field="o.name"/>
            <t t-set="is_invalid" t-value="o._is_invalid()"/>
            <t t-if="is_invalid">
                <strong><t t-esc="is_invalid"/></strong>
            </t>
            <table name="employee-infos" class="table table-sm table-bordered">
                <tr>
                    <td class="w-25"><strong>Employee</strong></td>
                    <td class="w-25"><strong>Marital Status</strong></td>
                    <td class="w-25"><strong>Designation</strong></td>
                    <td class="w-25"><strong>Pay Period</strong></td>
                </tr>
                <tr>
                    <td><span t-field="o.employee_id"/></td>
                    <td><span t-field="o.employee_id.marital"/></td>
                    <td><span t-field="o.employee_id.job_title"/></td>
                    <td>
                        <t t-if="o.date_from &lt; o.contract_id.date_start">
                            <span t-field="o.contract_id.date_start"/>
                        </t>
                        <t t-else="">
                            <span t-field="o.date_from"/>
                        </t>
                         - 
                        <t t-if="o.contract_id.date_end and o.date_to &gt; o.contract_id.date_end">
                            <span t-field="o.contract_id.date_end"/>
                        </t>
                        <t t-else="">
                            <span t-field="o.date_to"/>
                        </t>
                    </td>
                </tr>
                <tr>
                    <td><span t-field="o.employee_id.address_home_id.street"/></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td><span t-field="o.employee_id.address_home_id.city"/> <span t-field="o.employee_id.address_home_id.zip"/></td>
                    <td><strong>Person in charge</strong></td>
                    <td><strong>Identification</strong></td>
                    <td><strong>Computed on </strong></td>
                </tr>
                <tr>
                    <td><span t-field="o.employee_id.work_email"/></td>
                    <td name="personne-in-charge"><t t-esc="o.employee_id.children"/></td>
                    <td><span t-field="o.employee_id.identification_id"/></td>
                    <td><span t-field="o.compute_date"/></td>
                </tr>
                <tr>
                    <td class="w-25"><strong>Contract Start Date</strong></td>
                    <td class="w-25"><strong>Contract Type </strong></td>
                    <td class="w-25"><strong>Working Schedule</strong></td>
                    <td class="w-25" name="company-car-title"></td>
                </tr>
                <tr>
                    <td><span t-field="o.employee_id.first_contract_date"/></td>
                    <td><span t-field="o.employee_id.contract_id.contract_type_id"/></td>
                    <td><span t-field="o.employee_id.contract_id.hours_per_week"/></td>
                    <td class="w-25" name="company-car-model"></td>
                </tr>
            </table>

            <!-- YTI TODO master: Remove this horrible bidouille and add a field on the structure to display the basic
                 salary on the payslip report -->
            <t t-set="holiday_attest_n1" t-value="o.env.ref('l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n1_holidays', raise_if_not_found=False)"/>
            <t t-set="holiday_attest_n" t-value="o.env.ref('l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n_holidays', raise_if_not_found=False)"/>
            <table class="table table-sm" t-if="o.struct_id not in [holiday_attest_n, holiday_attest_n1]">
                <tr>
                    <td class="w-25"><strong>Basic Salary</strong></td>
                    <td style="color:#875A7B" class="w-25">
                        <span t-esc="o.contract_id._get_contract_wage()" t-options="{'widget': 'monetary', 'display_currency': o.company_id.currency_id}"/>
                    </td>
                    <td class="w-25"></td>
                    <td class="w-25"></td>
                </tr>
            </table>
            <div id="total">
                <table class="table table-sm">
                    <thead class="o_black_border">
                        <tr>
                            <th>Name</th>
                            <th>Number of Hours</th>
                            <th>Number of Days</th>
                            <th class="text-right">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <span t-foreach="o.worked_days_line_ids" t-as="worked_days">
                            <t t-if="worked_days.code != 'OUT'">
                                <tr style="color:none">
                                    <td>
                                        <span t-field="worked_days.name"/>
                                    </td>
                                    <td><span t-field="worked_days.number_of_hours"/></td>
                                    <td><span t-field="worked_days.number_of_days"/></td>
                                    <td class="text-right"><span t-esc="worked_days.amount" digits="[42, 2]"
                                            t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'/></td>
                                </tr>
                            </t>
                        </span>
                        <span t-foreach="o.line_ids.filtered(lambda line: line.appears_on_payslip)" t-as="line">
                            <t t-set="line_style"/>
                            <t t-set="line_class"/>
                            <t t-if="line.code == 'NET'">
                                <t t-set="line_style" t-value="'color:#875A7B;'"/>
                                <t t-set="line_class" t-value="'o_total o_border_bottom font-weight-bold'"/>
                            </t>
                            <t t-if="(line.code == 'BASIC') or (line.code == 'GROSS')">
                                <t t-set="line_style" t-value="'color:#00A09D;'"/>
                                <t t-set="line_class" t-value="'o_subtotal o_border_bottom'"/>
                            </t>
                            <tr t-att-class="line_class" t-att-style="line_style">
                                <td><span t-field="line.name"/></td>
                                <td></td>
                                <td><span t-if="line.quantity > 1" t-esc="line.quantity"/></td>
                                <td class="text-right"><span t-esc="line.total"
                                        t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'
                                        t-att-style="'color:#875A7B;' if line.total &lt; 0 else ''"/></td>
                            </tr>
                        </span>
                    </tbody>
                </table>
            </div>
            <p t-if="o.net_wage &gt;= 0">To pay on <b><span t-field="o.employee_id.bank_account_id"/></b> of <i><span t-field="o.employee_id"/></i>: <span t-field="o.net_wage"/></p>
            <p t-if="o.net_wage &lt; 0">The net amount will be recovered from the first positive remuneration established after this.</p>
        </div>
    </t>
</template>

<template id="report_payslip_lang">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-set="o" t-value="o.with_context(lang=o.employee_id.sudo().address_home_id.lang or o.env.lang)"/>
            <t t-call="hr_payroll.report_payslip" t-lang="o.env.lang"/>
        </t>
    </t>
</template>
</odoo>
