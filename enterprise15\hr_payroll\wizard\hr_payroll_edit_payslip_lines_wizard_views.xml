<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_payroll_edit_payslip_lines_form_wizard" model="ir.ui.view">
        <field name="name">hr.payroll.edit.payslip.lines.wizard.view.form</field>
        <field name="model">hr.payroll.edit.payslip.lines.wizard</field>
        <field name="arch" type="xml">
            <form string="Edition of Payslip Lines in the Payslip" js_class="payslip_edit_lines_form">
                <sheet>
                    <field name="payslip_id" invisible="1"/>
                    <span><strong>Tip:</strong> Each time you edit the quantity or the amount on a line, we recompute the following lines. We recommend that you edit from top to bottom to prevent your edition from being overwritten by the automatic recalculation. Be careful that reordering the lines doesn't recompute them.</span>
                    <group>
                        <field name="worked_days_line_ids" widget="worked_days_line_one2many" nolabel="1" context="{'default_slip_id': payslip_id}">
                            <tree string="Worked Days" editable="bottom" delete="0">
                                <field name="work_entry_type_id"/>
                                <field name="name" required="1"/>
                                <field name="number_of_days"/>
                                <field name="number_of_hours"/>
                                <field name="code" optional="hide"/>
                                <field name="amount"/>
                            </tree>
                        </field>
                    </group>
                    <group>
                        <field name="line_ids" widget="payslip_line_one2many" nolabel="1" context="{'default_slip_id': payslip_id}">
                            <tree string="Salary Structure" editable="bottom" decoration-info="total == 0" delete="0">
                                <field name="sequence" widget="handle"/>
                                <field name="slip_id" invisible="1"/>
                                <field name="struct_id" invisible="1"/>
                                <field name="name" required="True"/>
                                <field name="code" readonly="1" force_save="1" optional="hide"/>
                                <field name="category_id" invisible="1" readonly="1" force_save="1"/>
                                <field name="quantity"/>
                                <field name="rate" readonly="1" force_save="1"/>
                                <field name="salary_rule_id" options="{'no_create_edit': True}" required="1"/>
                                <field name="amount"/>
                                <field name="total" readonly="1" force_save="1"/>
                            </tree>
                        </field>
                    </group>
                </sheet>
                <footer>
                    <button name="action_validate_edition" string="Validate Edition" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button string="Discard" special="cancel" data-hotkey="z" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
