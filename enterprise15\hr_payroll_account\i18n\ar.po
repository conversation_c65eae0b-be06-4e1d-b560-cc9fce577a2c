# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "المحاسبة "

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "القيد المحاسبي"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "قيد التعديل "

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed ?"
msgstr "هل أنت متأكد من أنك ترغب بالمتابعة؟ "

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr "سِجل العقد "

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr "إنشاء مسودة قيد "

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "الحساب الدائن"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "حساب التاريخ "

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "الحساب المدين"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "عقد الموظف"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr "اليومية غير صحيحة: يجب أن يكون للحساب نفس عملة الشركة "

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "اتركه فارغاً لاستخدام تاريخ تصديق (إيصال الدفع). "

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr "لم يتم احتسابه في الصافي بشكل خاضع للمساءلة  "

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the contract for these payslips has no structure type."
msgstr "لا يحتوي أحد عقود إيصالات الدفع هذه على نوع هيكل. "

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the payroll structures has no account journal defined on it."
msgstr "لا يحتوي أحد هياكل إيصالات الدفع على دفتر يومية للحساب معين فيه. "

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "إيصال دفع "

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "دفتر يومية المرتبات "

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr "قاعدة الراتب "

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "هيكل الراتب "

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
msgid "Set to Draft"
msgstr "التعيين كمسودة "

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "لم يقم دفتر يومية النفقات \"%s\" بتهيئة الحساب الدائن بشكل صحيح! "

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "لم يقم دفتر يومية النفقات \"%s\" بتهيئة حساب الخصم بشكل صحيح! "

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""
"يتيح لك هذا الحقل حذف قيمة هذه القاعدة في قاعدة \"صافي الراتب\" على المستوى "
"المحاسبي لعرض قيمة هذه القاعدة في المحاسبة بشكل صريح. على سبيل المثال، إذا "
"أردت عرض قيمة رسوف التمثيل، يمكنك تحديد هذا الحقل. "
