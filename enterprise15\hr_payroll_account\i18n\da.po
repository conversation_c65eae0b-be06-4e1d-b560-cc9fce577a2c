# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2021
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "Regnskab"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "Regnskabsposteringer"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "Justerings indtastning"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Analysekonto"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed ?"
msgstr "Er du sikker på, du vil fortsætte?"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr "Opret kladde postering"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Kreditkonto"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "Konto dato"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Debit konto"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "Ansættelseskontrakt"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr "Ukorrekt journal: Journalen skal bruge samme valuta som virksomheden"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "Hold tom for at bruge perioden fra valideringsdatoen (Lønseddel)."

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr "Beregnes ikke i net regnskabsmæssigt"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the contract for these payslips has no structure type."
msgstr "En af kontrakterne for disse lønsedler har ingen struktur type."

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the payroll structures has no account journal defined on it."
msgstr "En af lønseddel strukturerne har ingen konto journal defineret."

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "Lønseddel"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "Løn journal"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Lønregel"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "Løn struktur"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
msgid "Set to Draft"
msgstr "Sæt til kladde"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "Udgifts journalen \"%s\" har ikke konfigureret Kreditkontoen korrekt!"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "Udgifts journalen \"%s\" har ikke konfigureret debetkontoen korrekt!"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""
"Dette felt lader dig slette værdien for denne regel i \"Samlet løn\" reglen "
"på regnskabs niveau, for eksplicit at vise værdien af denne regel i "
"regnskabet. For eksempel, hvis du vil vise værdien på dine "
"repræsentationsgebyrer, kan du markere dette felt."
