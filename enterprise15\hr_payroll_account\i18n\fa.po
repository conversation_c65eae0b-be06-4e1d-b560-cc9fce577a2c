# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: Hanna Kheradroosta, 2022\n"
"Language-Team: Persian (https://www.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "حسابداری"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "سند حسابداری"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "سند تعدیل"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "حساب تحلیلی"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed ?"
msgstr "آیا مطمئن هستید که می خواهید ادامه دهید ؟"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr "تاریخچه قرارداد"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr "ایجاد سند پیش‌نویس"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "حساب بستانکاری"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "تاریخ حساب"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "حساب بدهی"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "قرارداد کارمند"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr "دفترروزنامه نادرست: دفترروزنامه باید با ارز شرکت یکسان باشد"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "برای استفاده از دوره تاریخ معتبرسازی (فیش حقوقی) خالی نگه دارید."

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr "در حساب خالص محاسبه نشده است"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the contract for these payslips has no structure type."
msgstr "یکی از قراردادهای این فیش‌حقوقی فاقد نوع ساختار است."

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the payroll structures has no account journal defined on it."
msgstr "یکی از ساختارهای حقوق‌ودستمزد فاقد دفترروزنامه حساب در آن است."

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "فیش پرداخت"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "دفترروزنامه‌ حقوق و دستمزد"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr "قاعده حقوق و دستمزد"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "ساختار حقوق و دستمزد"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
msgid "Set to Draft"
msgstr "تبدیل به پیش‌نویس"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "دفترروزنامه هزینه \"%s\" به‌درستی حساب بستانکاری پیکربندی نشده است!"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "دفترروزنامه هزینه \"%s\" به‌درستی حساب بدهکاری پیکربندی نشده است!"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""
"این فیلد به شما امکان می دهد ارزش این قاعده را در قاعده \"حقوق خالص\" در سطح"
" حسابداری حذف کنید تا ارزش این قاعده در حسابداری به صراحت نمایش داده شود. به"
" عنوان مثال ، اگر می خواهید ارزش هزینه نمایندگی خود را نشان دهید، می توانید "
"این فیلد را بررسی کنید."
