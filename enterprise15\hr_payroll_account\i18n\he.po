# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# yaco<PERSON> mosbacher <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2022
# yael terner, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: yael terner, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "הנהלת חשבונות"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "רשומת התאמה"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "חשבון אנליטי"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed ?"
msgstr "האם אתה בטוח שברצונך להמשיך?"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "תאריך תלוש שכר"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "חוזה עובד"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the contract for these payslips has no structure type."
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the payroll structures has no account journal defined on it."
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "תלוש שכר"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "יומן שכר"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
msgid "Set to Draft"
msgstr "הגדר כטיוטה"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr ""

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""
