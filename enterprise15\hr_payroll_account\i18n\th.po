# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# <PERSON>, 2021
# K<PERSON>wu<PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>hip<PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:57+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "บัญชี"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "บัญทึกทางบัญชี"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "บันทึกปรับปรุง"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed ?"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr "ประวัติสัญญา"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr "สร้างรายการร่าง"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "บัญชีเครดิต"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "วันที่บัญชี"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "บัญชีเดบิต"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "สัญญาของบุคลากร"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr "สมุดรายวันไม่ถูกต้อง: สมุดรายวันต้องเป็นสกุลเงินเดียวกันกับบริษัท"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "ว่างไว้เพื่อใช้งวดบัญชีของวันที่ตรวจสอบสลิปเงินเดือน"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr "ไม่นับสุทธิตามบัญชี"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the contract for these payslips has no structure type."
msgstr "หนึ่งในสัญญาสำหรับสลิปเงินเดือนเหล่านี้ไม่มีโครงสร้างประเภท"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "One of the payroll structures has no account journal defined on it."
msgstr ""
"โครงสร้างบัญชีเงินเดือนอย่างใดอย่างหนึ่งไม่มีสมุดรายวันทางบัญชีที่กำหนดไว้อยู่"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "สมุดรายวันเงินเดือน"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr "กฎเงินเดือน"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "โครงสร้างเงินเดือน"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
msgid "Set to Draft"
msgstr "กำหนดให้เป็นฉบับร่าง"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "สมุดรายวันรายจ่าย \"%s\" ไม่ได้กำหนดค่าบัญชีเครดิตที่เหมาะสม"

#. module: hr_payroll_account
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "สมุดรายวันรายจ่าย \"%s\" ไม่ได้กำหนดค่าบัญชีเดบิตที่เหมาะสม"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""
"ฟิลด์นี้ช่วยให้คุณสามารถลบค่าของกฎนี้ในกฎ \"เงินเดือนสุทธิ\" ในระดับบัญชี "
"เพื่อแสดงมูลค่าของกฎนี้ในการบัญชีอย่างชัดเจน ตัวอย่างเช่น "
"หากคุณต้องการแสดงมูลค่าค่าธรรมเนียมการเป็นตัวแทน "
"คุณสามารถทำเครื่องหมายในช่องนี้ได้"
