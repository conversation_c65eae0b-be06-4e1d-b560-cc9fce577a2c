# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_sepa
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Spanish (Mexico) (https://www.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__journal_id
msgid "Bank Journal"
msgstr "Diario bancario"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Create Payment Report"
msgstr "Crear reporte de pago"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Creation date of the payment file."
msgstr "Fecha de creación del archivo de pago."

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export
msgid "Export file related to this payslip"
msgstr "Exportar archivo relacionado con este recibo de nómina"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Exported File"
msgstr "Archivo exportado"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "Exported SEPA .xml file"
msgstr "Archivo SEPA .xml exportado"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "Exported SEPA .xml file name"
msgstr "Nombre de archivo SEPA .xml exportado"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "File Name"
msgstr "Nombre del archivo"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__file_name
msgid "File name"
msgstr "Nombre del archivo"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Generation Date"
msgstr "Fecha de generación"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run_sepa_wizard
msgid "HR Payslip Run SEPA Wizard"
msgstr "Recibo de nómina de RR. HH. ejecutar asistente de SEPA"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_sepa_wizard
msgid "HR Payslip SEPA Wizard"
msgstr "Asistente de SEPA de recibo de nómina de RR. HH."

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#, python-format
msgid ""
"Invalid bank account for the following employees:\n"
"%s"
msgstr ""
"Cuenta bancaria inválida para los siguientes empleados:\n"
"%s"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard____last_update
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
msgid "Name of the export file generated for this payslip"
msgstr "Nombre del archivo de exportación generado para este recibo de nómina"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip
msgid "Pay Slip"
msgstr "Recibo de nómina"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lotes de recibos de nómina"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "SEPA File"
msgstr "Archivo SEPA"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Select a bank journal."
msgstr "Seleccione un diario bancario."

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a bank account."
msgstr "Algunos empleados (%s) no tienen una cuenta bancaria."

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a private address."
msgstr "Algunos empleados (%s) no tienen una dirección privada."

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a valid name on the private address."
msgstr ""
"Algunos empleados (%s) no tienen un nombre válido en la dirección privada."

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"El diario '%s' requiere una cuenta IBAN para pagar a través de SEPA. "
"Configúrela primero."
