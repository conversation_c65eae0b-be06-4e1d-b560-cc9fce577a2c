# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_sepa
# 
# Translators:
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__journal_id
msgid "Bank Journal"
msgstr "銀行仕訳帳"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Cancel"
msgstr "取消"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Confirm"
msgstr "確認"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Create Payment Report"
msgstr "支払報告を作成"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Creation date of the payment file."
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export
msgid "Export file related to this payslip"
msgstr "この給与明細に関連したファイルをエクスポート"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Exported File"
msgstr "エクスポート済ファイル"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "Exported SEPA .xml file"
msgstr "エクスポート済SEPA .xml ファイル"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "Exported SEPA .xml file name"
msgstr "エクスポート済SEPA .xml ファイル名"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "File Name"
msgstr "ファイル名"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__file_name
msgid "File name"
msgstr "ファイル名"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Generation Date"
msgstr "生成日"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run_sepa_wizard
msgid "HR Payslip Run SEPA Wizard"
msgstr "人事給与明細実施SEPAウィザード"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_sepa_wizard
msgid "HR Payslip SEPA Wizard"
msgstr "人事給与明細SEPAウィザード"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#, python-format
msgid ""
"Invalid bank account for the following employees:\n"
"%s"
msgstr ""
"以下の従業員用の銀行口座が無効です:\n"
"%s"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard____last_update
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
msgid "Name of the export file generated for this payslip"
msgstr "この給与明細用に生成されたエキスポートファイル名"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip
msgid "Pay Slip"
msgstr "給与明細"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "給与明細書のバッチ"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "SEPA File"
msgstr "SEPAファイル"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Select a bank journal."
msgstr "銀行仕訳帳を選択"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a bank account."
msgstr "銀行口座を持っていない従業員(%s)がいます。"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a private address."
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a valid name on the private address."
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr "仕訳'%s'はSEPA経由での支払いに適切なIBAN口座を必要としています。まずIBAN口座を設定して下さい。"
