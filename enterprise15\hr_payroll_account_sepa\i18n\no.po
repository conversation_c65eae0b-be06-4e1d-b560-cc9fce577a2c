# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_sepa
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Language-Team: Norwegian (https://www.transifex.com/odoo/teams/41243/no/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: no\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__journal_id
msgid "Bank Journal"
msgstr ""

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Cancel"
msgstr ""

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Confirm"
msgstr ""

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Create Payment Report"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_date
msgid "Created on"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Creation date of the payment file."
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export
msgid "Export file related to this payslip"
msgstr ""

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Exported File"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "Exported SEPA .xml file"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "Exported SEPA .xml file name"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "File Name"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__file_name
msgid "File name"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Generation Date"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run_sepa_wizard
msgid "HR Payslip Run SEPA Wizard"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_sepa_wizard
msgid "HR Payslip SEPA Wizard"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__id
msgid "ID"
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#, python-format
msgid ""
"Invalid bank account for the following employees:\n"
"%s"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard____last_update
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
msgid "Name of the export file generated for this payslip"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip
msgid "Pay Slip"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run
msgid "Payslip Batches"
msgstr ""

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "SEPA File"
msgstr ""

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Select a bank journal."
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a bank account."
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a private address."
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a valid name on the private address."
msgstr ""

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
