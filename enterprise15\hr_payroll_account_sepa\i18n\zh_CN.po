# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_sepa
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__journal_id
msgid "Bank Journal"
msgstr "银行日记账"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Cancel"
msgstr "取消"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Confirm"
msgstr "确认"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Create Payment Report"
msgstr "创建付款报告"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_uid
msgid "Created by"
msgstr "创建人"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__create_date
msgid "Created on"
msgstr "创建时间"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Creation date of the payment file."
msgstr "付款文件的创建日期。"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export
msgid "Export file related to this payslip"
msgstr "导出这个工资单的相关文件"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_view_form
msgid "Exported File"
msgstr "文件导出"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "Exported SEPA .xml file"
msgstr "导出SEPA xml文件"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "Exported SEPA .xml file name"
msgstr "导出SEPA xml文件名称"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_filename
msgid "File Name"
msgstr "文件名称"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__file_name
msgid "File name"
msgstr "文件名"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export_date
msgid "Generation Date"
msgstr "生成日期"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run_sepa_wizard
msgid "HR Payslip Run SEPA Wizard"
msgstr "HR工资单运行SEPA向导"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_sepa_wizard
msgid "HR Payslip SEPA Wizard"
msgstr "HR工资单SPA向导"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__id
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#: code:addons/hr_payroll_account_sepa/wizard/hr_payroll_account_sepa_wizard.py:0
#, python-format
msgid ""
"Invalid bank account for the following employees:\n"
"%s"
msgstr ""
"以下雇员的银行账户无效。\n"
"%s"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard____last_update
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run_sepa_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_sepa_wizard__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,help:hr_payroll_account_sepa.field_hr_payslip__sepa_export_filename
msgid "Name of the export file generated for this payslip"
msgstr "这个工资单生成的导出文件名称"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip
msgid "Pay Slip"
msgstr "工资单"

#. module: hr_payroll_account_sepa
#: model:ir.model,name:hr_payroll_account_sepa.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "工资单批处理"

#. module: hr_payroll_account_sepa
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip__sepa_export
#: model:ir.model.fields,field_description:hr_payroll_account_sepa.field_hr_payslip_run__sepa_export
msgid "SEPA File"
msgstr "SEPA文件"

#. module: hr_payroll_account_sepa
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_run_sepa_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_sepa.hr_payslip_sepa_view_form
msgid "Select a bank journal."
msgstr "选择一个银行日记账"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a bank account."
msgstr "一些员工（%s）没有银行账户信息。"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a private address."
msgstr "一些员工（%s）没有私人地址信息。"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid "Some employees (%s) don't have a valid name on the private address."
msgstr "一些雇员（%s）在私人地址上没有有效的名字。"

#. module: hr_payroll_account_sepa
#: code:addons/hr_payroll_account_sepa/models/hr_payslip.py:0
#, python-format
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr "日记账 '%s' 需要一个正确的IBAN 账号来通过SEPA付费。请先配置它。"
