<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add Payment button on the payslip run form. -->
    <record id="hr_payslip_run_view_form" model="ir.ui.view">
        <field name="name">hr.payslip.run.inherit.form</field>
        <field name="model">hr.payslip.run</field>
        <field name="inherit_id" ref="hr_payroll.hr_payslip_run_form"/>
        <field name="arch" type="xml">
            <button name="action_paid" position="before">
                <button name="action_open_sepa_wizard" type="object" string="Create Payment Report" class="oe_highlight" attrs="{'invisible': ['|', ('state', 'not in', ['close', 'paid']), ('sepa_export', '!=', False)]}"/>
            </button>
            <button name="action_paid" position="after">
                <button name="action_open_sepa_wizard" type="object" string="Create Payment Report" attrs="{'invisible': ['|', ('state', 'not in', ['close', 'paid']), ('sepa_export', '=', False)]}"/>
            </button>
            <field name="credit_note" position="after">
                <field name="sepa_export_date" attrs="{'invisible': [('sepa_export','=', False)]}" readonly="1"/>
                <field name="sepa_export_filename" invisible="1"/>
                <field name="sepa_export" filename="sepa_export_filename" attrs="{'invisible': [('sepa_export', '=', False)]}"/>
            </field>
        </field>
    </record>

    <!-- Add Payment button on the payslip form if the payslip hasn't got a payslip run. -->
    <record id="hr_payslip_view_form" model="ir.ui.view">
        <field name="name">hr.payslip.inherit.form</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
        <field name="arch" type="xml">
            <button name="action_payslip_paid" position="replace">
                <button name="action_open_sepa_wizard" type="object" string="Create Payment Report" class="oe_highlight" 
                    attrs="{'invisible': ['|', ('payslip_run_id', '!=', False), ('state', '!=', 'done')]}"/>
            </button>
            <notebook position="inside">
                <page string="Exported File" name="exported_file" attrs="{'invisible': [('sepa_export','=', False)]}">
                    <group>
                        <field name="sepa_export_date"/>
                        <field name="sepa_export_filename" invisible="1"/>
                        <field name="sepa_export" filename="sepa_export_filename"/>
                    </group>
                </page>
            </notebook>
        </field>
    </record>
</odoo>
