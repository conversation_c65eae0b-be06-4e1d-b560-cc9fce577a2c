# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_expense
# 
# Translators:
# <PERSON>ied<PERSON><PERSON>-Nesselbosch, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Spesenabrechnung"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_payslip_view_form_inherit_expense
msgid "Expenses"
msgstr "Spesen"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expenses_count
msgid "Expenses Count"
msgstr "Anzahl Ausgaben"

#. module: hr_payroll_expense
#: model:hr.salary.rule,name:hr_payroll_expense.hr_salary_rule_expense_refund
msgid "Expenses Reimbursement"
msgstr "Kostenerstattung"

#. module: hr_payroll_expense
#: model:ir.model.fields,help:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
msgid "Expenses to reimburse to employee."
msgstr "Dem Mitarbeiter zu erstattende Ausgaben."

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payslip
msgid "Pay Slip"
msgstr "Gehaltsabrechnung"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__payslip_id
msgid "Payslip"
msgstr "Gehaltsabrechnung"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__refund_in_payslip
msgid "Reimburse In Next Payslip"
msgstr "Mit nächster Gehaltszahlung erstatten"

#. module: hr_payroll_expense
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
#, python-format
msgid "Reimbursed Expenses"
msgstr "Erstattete Spesen"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Report in Next Payslip"
msgstr "In nächster Gehaltsabrechnung verrechnen"

#. module: hr_payroll_expense
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Your expense (%s) will be added to your next payslip."
msgstr "Ihre Auslage (%s) wird mit der nächsten Gehaltsabrechnung verrechnet."
