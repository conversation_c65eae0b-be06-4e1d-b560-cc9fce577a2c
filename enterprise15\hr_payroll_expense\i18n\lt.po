# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_expense
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Išlaidų ataskaita"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_payslip_view_form_inherit_expense
msgid "Expenses"
msgstr "Išlaidos"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expenses_count
msgid "Expenses Count"
msgstr ""

#. module: hr_payroll_expense
#: model:hr.salary.rule,name:hr_payroll_expense.hr_salary_rule_expense_refund
msgid "Expenses Reimbursement"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model.fields,help:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
msgid "Expenses to reimburse to employee."
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payslip
msgid "Pay Slip"
msgstr "Algalapis"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__payslip_id
msgid "Payslip"
msgstr "Algalapis"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__refund_in_payslip
msgid "Reimburse In Next Payslip"
msgstr ""

#. module: hr_payroll_expense
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
#, python-format
msgid "Reimbursed Expenses"
msgstr ""

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Report in Next Payslip"
msgstr ""

#. module: hr_payroll_expense
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Your expense (%s) will be added to your next payslip."
msgstr ""
