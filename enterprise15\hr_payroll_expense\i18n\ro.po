# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_expense
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Raport de cheltuieli"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_payslip_view_form_inherit_expense
msgid "Expenses"
msgstr "Cheltuieli"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expenses_count
msgid "Expenses Count"
msgstr "Număr de cheltuieli"

#. module: hr_payroll_expense
#: model:hr.salary.rule,name:hr_payroll_expense.hr_salary_rule_expense_refund
msgid "Expenses Reimbursement"
msgstr "Rambursarea cheltuielilor"

#. module: hr_payroll_expense
#: model:ir.model.fields,help:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
msgid "Expenses to reimburse to employee."
msgstr "Cheltuieli de rambursat angajatului."

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payslip
msgid "Pay Slip"
msgstr "Fluturas de salariu"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__payslip_id
msgid "Payslip"
msgstr "Fluturas de salariu"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__refund_in_payslip
msgid "Reimburse In Next Payslip"
msgstr "Rambursați în următorul fluturaș de salariu"

#. module: hr_payroll_expense
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
#, python-format
msgid "Reimbursed Expenses"
msgstr "Cheltuieli rambursate"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Report in Next Payslip"
msgstr "Raportați în următoarul filuturaș de salariu"

#. module: hr_payroll_expense
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Your expense (%s) will be added to your next payslip."
msgstr "Cheltuiala ta  (%s) va fi adăugată la următorul fluturaș de salariu."
