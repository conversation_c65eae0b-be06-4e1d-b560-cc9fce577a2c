<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_expense_sheet_view_form_inherit_payroll" model="ir.ui.view">
        <field name="name">hr.expense.sheet.view.form.payroll</field>
        <field name="model">hr.expense.sheet</field>
        <field name="inherit_id" ref="hr_expense.view_hr_expense_sheet_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="refund_in_payslip" invisible="1"/>
                <field name="payslip_id" attrs="{'invisible':[('payslip_id','=',False)]}"/>
            </xpath>
            <button name="action_sheet_move_create" position="after">
                <button name="action_report_in_next_payslip" string="Report in Next Payslip" type="object" groups="account.group_account_manager" attrs="{'invisible': ['|', ('refund_in_payslip', '=', True), ('state', '!=', 'approve')]}" class="oe_highlight" data-hotkey="g"/>
            </button>
            <button name="action_sheet_move_create" position="attributes">
                <attribute name="attrs">{'invisible': ['|', ('refund_in_payslip', '=', True), ('state', '!=', 'approve')]}</attribute>
            </button>
        </field>
    </record>
</odoo>
