<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_actions_server_report_to_next_month" model="ir.actions.server">
            <field name="name">Defer to Next Month</field>
            <field name="model_id" ref="hr_holidays.model_hr_leave"/>
            <field name="binding_model_id" ref="hr_holidays.model_hr_leave"/>
            <field name="binding_view_types">list</field>
            <field name="groups_id" eval="[(4, ref('hr_payroll.group_hr_payroll_user'))]"/>
            <field name="state">code</field>
            <field name="code">
                records.action_report_to_next_month()
            </field>
        </record>

        <record id="ir_actions_server_mark_as_reported" model="ir.actions.server">
            <field name="name">Mark as defered</field>
            <field name="model_id" ref="hr_holidays.model_hr_leave"/>
            <field name="binding_model_id" ref="hr_holidays.model_hr_leave"/>
            <field name="binding_view_types">list,form</field>
            <field name="groups_id" eval="[(4, ref('hr_payroll.group_hr_payroll_user'))]"/>
            <field name="state">code</field>
            <field name="code">
                records.activity_feedback(['hr_payroll_holidays.mail_activity_data_hr_leave_to_defer'])
            </field>
        </record>
    </data>
</odoo>
