# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_holidays
# 
# Translators:
# <PERSON>, 2021
# krn<PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <juhasz.<PERSON><PERSON><PERSON><PERSON>@josafar.hu>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Hungarian (https://www.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_mail_activity
msgid "Activity"
msgstr "Tevékenység"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_company
msgid "Companies"
msgstr "Vállalatok"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__done
msgid "Computed in current payslip"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások módosítása"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Deferred Time Off"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_company__deferred_time_off_manager
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_config_settings__deferred_time_off_manager
msgid "Deferred Time Off Manager"
msgstr ""

#. module: hr_payroll_holidays
#: model:mail.activity.type,name:hr_payroll_holidays.mail_activity_data_hr_leave_to_defer
msgid "Leave to Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_payslip
msgid "Pay Slip"
msgstr "Bérjegyzék"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_hr_leave__payslip_state
msgid "Payslip State"
msgstr "Bérjegyzék állapot"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Please create manually the work entry for <a href=\"#\" data-oe-model=\"%s\""
" data-oe-id=\"%s\">%s</a>"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Postpone time off after payslip validation"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Responsible"
msgstr "Felelős"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_payslip.py:0
#, python-format
msgid ""
"There is some remaining time off to defer for these employees: \n"
"\n"
" %s"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_work_entry_action
#: model:ir.model,name:hr_payroll_holidays.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Time Off"
msgstr "Szabadság"

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_action_open_to_defer
msgid "Time Off to Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.ui.menu,name:hr_payroll_holidays.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr "Szabadságok riportolása"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Report in Payslip"
msgstr "Bérjegyzéken feltüntetendő"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__normal
msgid "To compute in next payslip"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__blocked
msgid "To defer to next payslip"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "Validated Time Off to Defer"
msgstr ""

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "You have some"
msgstr ""

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "time off"
msgstr ""

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "to defer to the next month."
msgstr ""
