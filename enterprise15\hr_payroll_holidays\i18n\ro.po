# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_holidays
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <fold<PERSON><EMAIL>>, 2021
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"O modalitate excelentă de a urmări PTO-urile angajaților, zilele de concediu"
" medical și statutul de aprobare."

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_mail_activity
msgid "Activity"
msgstr "Activitate"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__done
msgid "Computed in current payslip"
msgstr "Calculat în fluturașul de salariu curent"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_report_to_next_month
msgid "Defer to Next Month"
msgstr "Amânare pentru luna următoare"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Deferred Time Off"
msgstr "Concediu amânat"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_company__deferred_time_off_manager
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_config_settings__deferred_time_off_manager
msgid "Deferred Time Off Manager"
msgstr "Manager concediu amânat"

#. module: hr_payroll_holidays
#: model:mail.activity.type,name:hr_payroll_holidays.mail_activity_data_hr_leave_to_defer
msgid "Leave to Defer"
msgstr "Autorizare pentru amânare"

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_mark_as_reported
msgid "Mark as defered"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr "Vizualizare timp liber de pe tabloul de bord."

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Not enough attendance work entries to report the time off %s. Plase make the"
" operation manually"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "Only an employee time off to defer can be reported to next month"
msgstr ""
"Doar timpul liber de amânat al unui singur angajat poate fi raportat la luna"
" următoare"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_payslip
msgid "Pay Slip"
msgstr "Fluturas de salariu"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_hr_leave__payslip_state
msgid "Payslip State"
msgstr "Stare fluturaș de salariu"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Please create manually the work entry for <a href=\"#\" data-oe-model=\"%s\""
" data-oe-id=\"%s\">%s</a>"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Postpone time off after payslip validation"
msgstr "Amânați concediul după validarea fluturașului de salariu"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_form_inherit
msgid "Report to Next Month"
msgstr "Raportați luna viitoare"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Responsible"
msgstr "Responsabil"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The next month work entries are not generated yet or are validated already "
"for time off %s"
msgstr ""
"Înrtrările de lucru pentru luna următoare nu sunt încă generate sau sunt "
"deja validate pentru concediu %s"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The selected period is covered by a validated payslip. You can't create a "
"time off for that period."
msgstr ""
"Perioada selectată este acoperită de un fluturaș de salariu validat. Nu "
"puteți crea un concediu pentru perioada respectivă."

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The time off %s can not be reported because it is defined over more than 2 "
"months"
msgstr ""
"Concediul %s nu poate fi raportat deoarece este definit peste mai mult de 2 "
"luni"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "There is no work entries linked to this time off to report"
msgstr ""
"Nu există intrări de muncă asociate acestui concediu care să fie raportate"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_payslip.py:0
#, python-format
msgid ""
"There is some remaining time off to defer for these employees: \n"
"\n"
" %s"
msgstr ""
"Mai există concediu de amânat pentru acești angajați: \n"
"\n"
" %s"

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_work_entry_action
#: model:ir.model,name:hr_payroll_holidays.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Time Off"
msgstr "Concediu"

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_action_open_to_defer
msgid "Time Off to Defer"
msgstr "Concediu de amânat"

#. module: hr_payroll_holidays
#: model:ir.ui.menu,name:hr_payroll_holidays.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr "Concediu de Raportat"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Defer"
msgstr "De amânat"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Report in Payslip"
msgstr "Pentru raportare în Payslip"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__normal
msgid "To compute in next payslip"
msgstr "De calculat în următoarul fluturaș de salariu"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__blocked
msgid "To defer to next payslip"
msgstr "De amânat în următorul fluturaș de salariu"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "Validated Time Off to Defer"
msgstr "Concediu validat pentru amânare"

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "You have some"
msgstr "Aveți niște"

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "time off"
msgstr "Concediu"

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "to defer to the next month."
msgstr "de amânat pentru luna viitoare."
