# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_holidays
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <brencic<PERSON><EMAIL>>, 2022
# ka<PERSON><PERSON><PERSON>chus<PERSON> <karolina.schus<PERSON><PERSON>@vdp.sk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Skvelý spôsob, ako sledovať PTO zamestnancov, dni práceneschopnosti a stav "
"schválenia."

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_mail_activity
msgid "Activity"
msgstr "Aktivita"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__done
msgid "Computed in current payslip"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_report_to_next_month
msgid "Defer to Next Month"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Deferred Time Off"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_company__deferred_time_off_manager
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_config_settings__deferred_time_off_manager
msgid "Deferred Time Off Manager"
msgstr ""

#. module: hr_payroll_holidays
#: model:mail.activity.type,name:hr_payroll_holidays.mail_activity_data_hr_leave_to_defer
msgid "Leave to Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_mark_as_reported
msgid "Mark as defered"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr "Zoznámte sa s riadiacim panelom."

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Not enough attendance work entries to report the time off %s. Plase make the"
" operation manually"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "Only an employee time off to defer can be reported to next month"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_payslip
msgid "Pay Slip"
msgstr "Výplatná páska"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_hr_leave__payslip_state
msgid "Payslip State"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Please create manually the work entry for <a href=\"#\" data-oe-model=\"%s\""
" data-oe-id=\"%s\">%s</a>"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Postpone time off after payslip validation"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_form_inherit
msgid "Report to Next Month"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Responsible"
msgstr "Zodpovedný"

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The next month work entries are not generated yet or are validated already "
"for time off %s"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The selected period is covered by a validated payslip. You can't create a "
"time off for that period."
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The time off %s can not be reported because it is defined over more than 2 "
"months"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "There is no work entries linked to this time off to report"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_payslip.py:0
#, python-format
msgid ""
"There is some remaining time off to defer for these employees: \n"
"\n"
" %s"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_work_entry_action
#: model:ir.model,name:hr_payroll_holidays.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Time Off"
msgstr "Voľné dni"

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_action_open_to_defer
msgid "Time Off to Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.ui.menu,name:hr_payroll_holidays.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr "Voľné dni report"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Defer"
msgstr ""

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Report in Payslip"
msgstr "K vykázaniu na výplatnú pásku"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__normal
msgid "To compute in next payslip"
msgstr ""

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__blocked
msgid "To defer to next payslip"
msgstr ""

#. module: hr_payroll_holidays
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
#, python-format
msgid "Validated Time Off to Defer"
msgstr ""

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "You have some"
msgstr ""

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "time off"
msgstr ""

#. module: hr_payroll_holidays
#. openerp-web
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
#, python-format
msgid "to defer to the next month."
msgstr ""
