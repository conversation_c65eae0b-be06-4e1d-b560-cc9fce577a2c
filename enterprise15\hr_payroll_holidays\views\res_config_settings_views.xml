<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.payroll.holidays</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="45"/>
        <field name="inherit_id" ref="hr_payroll.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='hr_payroll_accountant']" position="after">
                <h2>Time Off</h2>
                <div class="row mt16 o_settings_container">
                    <div class="col-lg-6 col-12 o_setting_box">
                        <div class="o_setting_left_pane"/>
                        <div class="o_setting_right_pane">
                            <label for="deferred_time_off_manager" string="Deferred Time Off"/>
                            <div class="text-muted">
                                Postpone time off after payslip validation
                            </div>
                            <div class="content-group">
                                <div class="row mt16">
                                    <label string="Responsible" for="deferred_time_off_manager" class="col-lg-3 o_light_label"/>
                                    <field name="deferred_time_off_manager"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
