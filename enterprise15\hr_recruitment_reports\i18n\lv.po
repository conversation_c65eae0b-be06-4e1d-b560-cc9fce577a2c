# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_reports
# 
# Translators:
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.3+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 09:32+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: ievaputnina <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hired
msgid "# Hired"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__count
msgid "# New Applicant"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "# Payslips"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refused
msgid "# Refused"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__name
msgid "Applicant Name"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Company"
msgstr "Uzņēmums"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__date_closed
msgid "End Date"
msgstr "Beigu datums"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "From creation till hire"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Group By"
msgstr "Grupēt pēc"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "Hired People"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__job_id
msgid "Job"
msgstr "Amats"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Job Position"
msgstr "Amats"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Last 365 Days Applicant"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__medium_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "Medium"
msgstr "Vidējs"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__process_duration
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "Process Duration"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "Process Duration in Days"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Recruitment Analysis"
msgstr "Atlases analīze"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "Recruitment Rate"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refuse_reason_id
msgid "Refuse Reason"
msgstr ""

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__stage_id
msgid "Stage"
msgstr "Stadija"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_date
msgid "Start Date"
msgstr "Sākuma datums"

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
msgid "This report performs analysis on your recruitment."
msgstr ""

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_dashboard
msgid "weeks"
msgstr "nedēļas"
