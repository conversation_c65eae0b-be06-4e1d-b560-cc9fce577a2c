<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_recruitment_report_view_dashboard" model="ir.ui.view">
            <field name="name">hr.recruitment.report.view.dashboard</field>
            <field name="model">hr.recruitment.report</field>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <dashboard sample="1">
                    <view type="graph" ref="hr_recruitment.recruitment_report_view_graph"/>
                    <group>
                        <group col="4">
                            <group>
                                <aggregate name="count" string="Hired People" field="count" invisible="1"/>
                                <aggregate name="refused" string="# Payslips" field="refused" invisible="1"/>
                                <aggregate name="hired" string="Hired People" field="hired"/>
                                <formula name="recruitment_rate" string="Recruitment Rate" value="100 * record.hired / record.count" value_label="%"/>
                            </group>
                            <group>
                                <aggregate name="process_duration" string="Process Duration in Days" field="process_duration" invisible="1"/>
                                <formula name="process_duration_week" string="Process Duration" help="From creation till hire" value="record.process_duration / 7" value_label="weeks"/>
                            </group>
                        </group>
                        <group col="1">
                            <widget name="pie_chart" title="Medium" attrs="{'groupby': 'medium_id', 'measure': 'count'}"/>
                        </group>
                    </group>
                    <view type="pivot" ref="hr_recruitment.recruitment_report_view_pivot"/>
                </dashboard>
            </field>
        </record>

        <record id="recruitment_report_view_pivot" model="ir.ui.view">
            <field name="name">hr.recruitment.report.pivot</field>
            <field name="model">hr.recruitment.report</field>
            <field name="arch" type="xml">
                <pivot string="Recruitment Analysis" sample="1">
                    <field name="job_id" type="row"/>
                    <field name="count" type="measure"/>
                    <field name="hired" type="measure"/>
                    <field name="refused" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="recruitment_report_view_graph" model="ir.ui.view">
            <field name="name">hr.recruitment.report.graph</field>
            <field name="model">hr.recruitment.report</field>
            <field name="arch" type="xml">
                <graph string="Recruitment Analysis" type="line" sample="1">
                    <field name="create_date"/>
                    <field name="count" type="measure"/>
                </graph>
            </field>
        </record>

        <record id="hr_recruitment_report_view_tree" model="ir.ui.view">
            <field name="name">hr.recruitment.report.view.tree</field>
            <field name="model">hr.recruitment.report</field>
            <field name="arch" type="xml">
                <tree string="Recruitment Analysis">
                    <field name="name"/>
                    <field name="job_id" optional="show"/>
                    <field name="medium_id" optional="show"/>
                    <field name="create_date" optional="show"/>
                    <field name="stage_id" optional="show"/>
                </tree>
            </field>
        </record>

        <record id="recruitment_report_view_search" model="ir.ui.view">
            <field name="name">hr.recruitment.report.search</field>
            <field name="model">hr.recruitment.report</field>
            <field name="arch" type="xml">
                <search string="Recruitment Analysis">
                    <field name="company_id" filter_domain="[('company_id', 'ilike', self)]"/>
                    <field name="job_id" filter_domain="[('job_id', 'ilike', self)]"/>
                    <field name="name" filter_domain="[('name', 'ilike', self)]"/>
                    <field name="refuse_reason_id" filter_domain="[('refuse_reason_id', 'ilike', self)]"/>
                    <field name="create_date"/>
                    <field name="date_closed"/>
                    <filter string="Last 365 Days Applicant" name="year" domain="[
                        ('create_date', '>=', (context_today() + relativedelta(days=-365)).strftime('%Y-%m-%d')),
                        ('create_date', '&lt;', (context_today() + relativedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter name="filter_create_date" date="create_date"/>
                    <group expand="1" string="Group By">
                        <filter string="Job Position" name="job_id" context="{'group_by':'job_id'}"/>
                        <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="recruitment_report_action" model="ir.actions.act_window">
            <field name="name">Recruitment Analysis</field>
            <field name="res_model">hr.recruitment.report</field>
            <field name="view_mode">dashboard,pivot,graph</field>
            <field name="search_view_id" ref="recruitment_report_view_search"/>
            <field name="context">{
                'search_default_year': True
            }</field>
            <field name="help">This report performs analysis on your recruitment.</field>
        </record>

        <menuitem id="hr_recruitment.hr_applicant_report_menu"
            parent="hr_recruitment.report_hr_recruitment"
            action="recruitment_report_action"/>

    </data>
</odoo>
