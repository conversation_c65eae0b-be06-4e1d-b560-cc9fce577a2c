<?xml version="1.0"?>
<odoo>
    <record id="view_hr_job_employee_referral_kanban" model="ir.ui.view">
        <field name="name">hr.job.employee.referral.kanban</field>
        <field name="model">hr.job</field>
        <field name="arch" type="xml">
            <kanban create="false" class="oe_background_grey o_kanban_dashboard o_kanban_referral o_kanban_referral_job" js_class="employee_referral_dashboard">
                <field name="name"/>
                <field name="department_id"/>
                <field name="state"/>
                <field name="website_url"/>
                <field name="max_points"/>
                <field name="description"/>
                <field name="direct_clicks"/>
                <field name="facebook_clicks"/>
                <field name="twitter_clicks"/>
                <field name="linkedin_clicks"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="o_kanban_record">
                            <div t-attf-class="o_kanban_card_header justify-content-between border-bottom pb-2 ">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <field name="name"/>
                                    </div>
                                </div>
                                <div class="d-flex align-items-start flex-row">
                                    <div class="mt-2 h6 flex-grow-1">
                                        <t t-if="record.no_of_recruitment.raw_value > 1">
                                            <t t-set="pos">Positions</t>
                                        </t>
                                        <t t-else="">
                                            <t t-set="pos">Position</t>
                                        </t>
                                        <field name="no_of_recruitment"/> Open <t t-esc="pos"/>
                                    </div>
                                    <div class="d-flex align-items-center flex-grow-0 mt-1">
                                        <img src="/hr_referral/static/src/img/points.svg" class="mr-2" height="20px" alt="Points icon"/>
                                        <span class="text-nowrap"><field name="max_points"/> Points</span>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_card_content container">
                                <div class="pb-2">
                                    <t t-out="record.description.value" class="text-muted"/>
                                </div>
                                <div>
                                    <div class="o_button_referral">
                                        <a role="button" name="%(hr_referral_send_mail_action)d" type="action" class="btn btn-secondary border ">
                                            <span title='Send by Mail'>Refer Friend</span>
                                        </a>
                                        <a role="button" t-attf-href="#{record.website_url.raw_value}" target="_blank" class="btn btn-secondary border ">
                                            <span title='More info'>More info</span>
                                        </a>
                                    </div>
                                    <div class="o_to_share">
                                        <a role="button" name="%(hr_referral_link_to_share_action)d" type="action" class="btn btn-sm p-2 btn-secondary">
                                            <span title='My Link to Share'><i class='fa fa-lg fa-link text-secondary' role="img" aria-label="My Link to Share"/></span>
                                            <span class="d-block mt-2 text-primary">
                                                <t t-if="record.direct_clicks.raw_value &gt; 0"><t t-esc="record.direct_clicks.raw_value"/> click(s)</t>
                                                <t t-else="">Share Now</t>
                                            </span>
                                        </a>
                                        <t t-foreach="[{'lower': 'facebook', 'name': 'Facebook', 'clicks': record.facebook_clicks.raw_value}, {'lower': 'twitter', 'name': 'Twitter', 'clicks': record.twitter_clicks.raw_value}, {'lower': 'linkedin', 'name': 'Linkedin', 'clicks': record.linkedin_clicks.raw_value}]" t-as="source">
                                            <a role="button" name="action_share_external" type="object" class="btn btn-sm p-2 btn-secondary" t-attf-data-context="{'default_channel': '{{source.lower}}'}">
                                                <span t-attf-title="Share on {{ source.name }}"><i t-attf-class="fa fa-lg fa-{{source.lower}} text-secondary" role="img" t-attf-aria-label="Share on {{source.name}}"/></span>
                                                <span class="d-block mt-2 text-primary">
                                                    <t t-if="source.clicks &gt; 0"><t t-esc="source['clicks']"/> click(s)</t>
                                                    <t t-else="">Share Now</t>
                                                </span>
                                            </a>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_job_employee_referral">
        <field name="name">Job Positions</field>
        <field name="res_model">hr.job</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="hr_referral.view_hr_job_employee_referral_kanban"/>
        <field name="domain">[('is_published', '=', True), ('state', '=', 'recruit')]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_referral_job_kanban">
                No job positions are available to share.
            </p>
        </field>
    </record>
</odoo>
