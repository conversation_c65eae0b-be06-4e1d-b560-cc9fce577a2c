<?xml version="1.0"?>
<odoo>

    <record model="ir.ui.view" id="view_hr_referral_friend_form">
        <field name="name">hr.referral.friend.form</field>
        <field name="model">hr.referral.friend</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group name="name_details">
                            <field name="name"/>
                            <field name="position" widget="radio"/>
                        </group>
                        <group name="image_header">
                            <field name="image_head" widget='image' class="oe_avatar referral-rounded-circle" nolabel="1"/>
                        </group>
                        <group name="image">
                            <field name="image" widget='image' nolabel="0"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_hr_referral_friend_tree">
        <field name="name">hr.referral.friend.tree</field>
        <field name="model">hr.referral.friend</field>
        <field name="arch" type="xml">
            <tree>
                <field name="image" widget='image' class="oe_avatar o_referral_img"/>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_referral_friend_configuration">
        <field name="name">Friends</field>
        <field name="res_model">hr.referral.friend</field>
        <field name="view_mode">tree,form</field>
        <field name="groups_id" eval="[(4, ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
    </record>

    <menuitem parent="menu_hr_referral_configuration" id="menu_hr_referral_friend_configuration" action="action_hr_referral_friend_configuration" sequence="3" groups="hr_recruitment.group_hr_recruitment_manager"/>

</odoo>
