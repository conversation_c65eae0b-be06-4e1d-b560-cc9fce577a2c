# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays_enterprise
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.3+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 09:31+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Language-Team: Lithuanian (https://www.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.actions.act_window,help:hr_work_entry_holidays_enterprise.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model:ir.actions.act_window,name:hr_work_entry_holidays_enterprise.hr_leave_work_entry_action
msgid "All Time Off"
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit
msgid "Approve Time Off"
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.actions.act_window,help:hr_work_entry_holidays_enterprise.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit
msgid "Refuse Time Off"
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit_contract
msgid ""
"This work entry cannot be validated. It is conflicting with at least one work entry. <br/>\n"
"                  Two work entries of the same employee cannot overlap at the same time."
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit_contract
msgid ""
"This work entry cannot be validated. There is a leave to approve (or refuse)"
" at the same time."
msgstr ""

#. module: hr_work_entry_holidays_enterprise
#: model:ir.ui.menu,name:hr_work_entry_holidays_enterprise.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr ""
