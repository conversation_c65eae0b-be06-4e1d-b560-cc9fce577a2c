# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays_enterprise
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.3+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 09:31+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.actions.act_window,help:hr_work_entry_holidays_enterprise.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr "追踪员工的PTO、病假和批准状态的一个好方法。"

#. module: hr_work_entry_holidays_enterprise
#: model:ir.actions.act_window,name:hr_work_entry_holidays_enterprise.hr_leave_work_entry_action
msgid "All Time Off"
msgstr "所有休假"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit
msgid "Approve Time Off"
msgstr "批准休假"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.actions.act_window,help:hr_work_entry_holidays_enterprise.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr "满足休息时间仪表盘的要求。"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit
msgid "Refuse Time Off"
msgstr "拒绝休息时间"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit_contract
msgid ""
"This work entry cannot be validated. It is conflicting with at least one work entry. <br/>\n"
"                  Two work entries of the same employee cannot overlap at the same time."
msgstr ""
"此工作条目无法验证。它与至少一个工作条目相冲突。<br/>\n"
"同一员工的两个工作条目不能同时重叠。"

#. module: hr_work_entry_holidays_enterprise
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays_enterprise.payroll_hr_work_entry_view_form_inherit_contract
msgid ""
"This work entry cannot be validated. There is a leave to approve (or refuse)"
" at the same time."
msgstr ""
"此工作条目无法验证。休假是可以同时批准（或拒绝）。\n"
" "

#. module: hr_work_entry_holidays_enterprise
#: model:ir.ui.menu,name:hr_work_entry_holidays_enterprise.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr "待报告的休假"
