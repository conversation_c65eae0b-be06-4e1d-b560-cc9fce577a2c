# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr "عدد المهام"

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_intervention_details
msgid ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.planned_date_end, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">customer</t>,<br/><br/>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled from the <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> to the <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled.\n"
"    </t>\n"
"    <br/><br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.planned_date_end, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    عزيزنا <t t-out=\"object.partner_id.name or 'customer'\">العميل</t>،<br/><br/>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        تمت جدولة <t t-out=\"object.name or ''\">صيانة الغلاية</t> من <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> إلى <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        تمت جدولة <t t-out=\"object.name or ''\">صيانة الغلاية</t>.\n"
"    </t>\n"
"    <br/><br/>\n"
"    مع تحيات،\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_inherit_project_task_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                        <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('planned_date_begin', '=', False), ('planned_date_end', '=', False)]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"سهم \"/>\n"
"                        <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"سهم \" attrs=\"{'invisible': [('planned_date_begin', '=', False), ('planned_date_end', '=', False)]}\"/>"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Add a <b>title</b> <i>(e.g. Boiler maintenance, Air-conditioning "
"installation, etc.).</i>"
msgstr ""
"أضف <b>عنواناً</b> <i>(مثال: صيانة الغلاية، تركيب نظام التكييف، وما إلى "
"ذلك.).</i> "

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr "مدير"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr "كافة المهام"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "Analyze the performance of your tasks and your workers."
msgstr "قم بتحليل أداء مهامك وعمّالك. "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Archived"
msgstr "مؤرشف"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__user_ids
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_ids
msgid "Assignees"
msgstr "المسند إليهم "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignment Date"
msgstr "تاريخ التعيين "

#. module: industry_fsm
#: model:project.task,legend_blocked:industry_fsm.planning_task_0
#: model:project.task,legend_blocked:industry_fsm.planning_task_1
#: model:project.task,legend_blocked:industry_fsm.planning_task_10
#: model:project.task,legend_blocked:industry_fsm.planning_task_11
#: model:project.task,legend_blocked:industry_fsm.planning_task_12
#: model:project.task,legend_blocked:industry_fsm.planning_task_13
#: model:project.task,legend_blocked:industry_fsm.planning_task_14
#: model:project.task,legend_blocked:industry_fsm.planning_task_15
#: model:project.task,legend_blocked:industry_fsm.planning_task_16
#: model:project.task,legend_blocked:industry_fsm.planning_task_17
#: model:project.task,legend_blocked:industry_fsm.planning_task_18
#: model:project.task,legend_blocked:industry_fsm.planning_task_19
#: model:project.task,legend_blocked:industry_fsm.planning_task_2
#: model:project.task,legend_blocked:industry_fsm.planning_task_20
#: model:project.task,legend_blocked:industry_fsm.planning_task_3
#: model:project.task,legend_blocked:industry_fsm.planning_task_4
#: model:project.task,legend_blocked:industry_fsm.planning_task_5
#: model:project.task,legend_blocked:industry_fsm.planning_task_6
#: model:project.task,legend_blocked:industry_fsm.planning_task_7
#: model:project.task,legend_blocked:industry_fsm.planning_task_8
#: model:project.task,legend_blocked:industry_fsm.planning_task_9
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_1
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_2
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_3
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_4
msgid "Blocked"
msgstr "محجوب"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr "حسب المشروع"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr "حسب المستخدم "

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_4
msgid "Cancelled"
msgstr "ملغي"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Company"
msgstr "شركة"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "التهيئة "

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Confirm the <b>time spent</b> on your task. <i>Tip: a rounding of 15min has "
"automatically been applied.</i>"
msgstr ""
"قم بتأكيد <b>الوقت المقضي</b> على مهامك. <i>نصيحة: لقت تم تطبيق التقريب إلى "
"15 دقيقة بالفعل.</i> "

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr "أنشئ عروض أسعار جديدة مباشرة من المهام "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create new quotations directly from your tasks"
msgstr "أنشئ عروض أسعار جديدة مباشرة من مهامك "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
msgid "Customer"
msgstr "العميل"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "Days to Deadline"
msgstr "الأيام حتى الموعد النهائي "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr "الموعد النهائي"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_enabled_conditions_count
msgid "Display Enabled Conditions Count"
msgstr "عدد شروط العرض المُمَكّنة "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_primary
msgid "Display Mark As Done Primary"
msgstr "عرض التعيين كمنتهي الأساسي "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_secondary
msgid "Display Mark As Done Secondary"
msgstr "عرض التعيين كمنتهي الثانوي "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_satisfied_conditions_count
msgid "Display Satisfied Conditions Count"
msgstr "عدد شروط العرض المستوفاة "

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr ""
"قم بعرض المهام في تطبيق الخدمة الميدانية واسمح بالتخطيط مع تواريخ "
"البدء/الانتهاء. "

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_3
msgid "Done"
msgstr "تم"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_effective
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Effective Hours"
msgstr "ساعات العمل الفعلية"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr "تاريخ الانتهاء"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr "عروض الأسعار الإضافية "

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "تحليل مهام إدارة الخدمات الميدانية "

#. module: industry_fsm
#: code:addons/industry_fsm/models/company.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model:project.project,name:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#, python-format
msgid "Field Service"
msgstr "الخدمة الميدانية"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
msgid "Find here your itinerary for today's tasks."
msgstr "ستجد هنا مخطط عملك لمهام اليوم. "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "Find here your tasks planned for the following days."
msgstr "ستجد هنا مهامك المخططة للأيام القادمة. "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future"
msgstr "المستقبلية "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Group By"
msgstr "التجميع حسب "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__has_complete_partner_address
msgid "Has Complete Partner Address"
msgstr "يحتوي على عنوان الشريك الكامل "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr "المُعرف"

#. module: industry_fsm
#: model:project.task,legend_normal:industry_fsm.planning_task_0
#: model:project.task,legend_normal:industry_fsm.planning_task_1
#: model:project.task,legend_normal:industry_fsm.planning_task_10
#: model:project.task,legend_normal:industry_fsm.planning_task_11
#: model:project.task,legend_normal:industry_fsm.planning_task_12
#: model:project.task,legend_normal:industry_fsm.planning_task_13
#: model:project.task,legend_normal:industry_fsm.planning_task_14
#: model:project.task,legend_normal:industry_fsm.planning_task_15
#: model:project.task,legend_normal:industry_fsm.planning_task_16
#: model:project.task,legend_normal:industry_fsm.planning_task_17
#: model:project.task,legend_normal:industry_fsm.planning_task_18
#: model:project.task,legend_normal:industry_fsm.planning_task_19
#: model:project.task,legend_normal:industry_fsm.planning_task_2
#: model:project.task,legend_normal:industry_fsm.planning_task_20
#: model:project.task,legend_normal:industry_fsm.planning_task_3
#: model:project.task,legend_normal:industry_fsm.planning_task_4
#: model:project.task,legend_normal:industry_fsm.planning_task_5
#: model:project.task,legend_normal:industry_fsm.planning_task_6
#: model:project.task,legend_normal:industry_fsm.planning_task_7
#: model:project.task,legend_normal:industry_fsm.planning_task_8
#: model:project.task,legend_normal:industry_fsm.planning_task_9
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_1
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_2
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_3
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_4
#: model:project.task.type,name:industry_fsm.planning_project_stage_2
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Initially Planned Hours"
msgstr "ساعات العمل المبدئية المخطط لها"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "Kanban State"
msgstr "حالة كانبان"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Keep track of the products used for your tasks, and invoice your time and "
"material to your customers"
msgstr "تتبع المنتجات المستخدمة لمهامك، وقم بفوترة وقتك والمواد لعملائك "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr "آخر تحديث للمرحلة"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Launch the timer to <b>track the time spent</b> on your task."
msgstr "قم بتشغيل المؤقت <b>لتعقب الوقت الذي تقضيه</b> في مهامك. "

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Let's <b>mark your task as done!</b> <i>Tip: when doing so, your stock will "
"automatically be updated, and your task will move to the next stage.</i>"
msgstr ""
"فلنقم <b>بتعيين مهمتك كمنتهية!</b> <i>نصيحة: عند قيامك بذلك، سوف يتم تحديث "
"مخزونك تلقائياً، وسوف تنتقل مهمتك إلى المرحلة التالية.</i> "

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "فلنقم بإنشاء <b>مهمتك</b> الأولى. "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Manage tasks in the Field Service module"
msgstr "إدارة المهام في تطبيق الخدمة الميدانية "

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr "الخريطة "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr "التعيين كمنتهي "

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "My Tasks"
msgstr "مهامي"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Navigate To"
msgstr "التنقل إلى "

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_0
msgid "New"
msgstr "جديد"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "No projects found. Let's create one!"
msgstr "لم يتم العثور على أي مشروع. فلننشئ واحداً! "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "No stages found. Let's create one!"
msgstr "لم يتم العثور على أي مراحل. فلنقم بإنشائها! "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "No tasks found. Let's create one!"
msgstr "لم يتم العثور على أي مهام. فلنقم بإنشائها! "

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_days_close
msgid "Number of Working Days to close the task"
msgstr "عدد أيام العمل حتى إغلاق المهمة"

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_days_open
msgid "Number of Working Days to open the task"
msgstr "عدد أيام العمل حتى فتح المهمة "

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Number of Working Hours to close the task"
msgstr "عدد ساعات العمل حتى إغلاق المهمة "

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Number of Working Hours to open the task"
msgstr "عدد ساعات العمل حتى فتح المهمة "

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Planned"
msgstr "مخطط"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_inherit_project_task_view_form
msgid "Planned Date"
msgstr "التاريخ المخطط "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_planned
msgid "Planned Hours"
msgstr "الساعات المخطط لها"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Planned for Today"
msgstr "المخطط لها اليوم "

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr "التخطيط"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
msgid "Planning by Project"
msgstr "التخطيط حسب المشروع "

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Planning by User"
msgstr "التخطيط حسب المستخدم "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr "الأولوية"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr "مدى التقدم "

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Project"
msgstr "المشروع"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr "المشاريع "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "Projects regroup tasks on the same topic, and each has its dashboard."
msgstr ""
"تقوم المشاريع بإعادة تجميع المهام من نفس الموضوع، ولكل منها لوحة بيانات "
"خاصة. "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Provide custom worksheet reports to be signed off by customers"
msgstr "قم بتقديم تقارير ورقة عمل مخصصة ليتم التوقيع عليها من قِبَل العملاء "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_value
msgid "Rating Value (/5)"
msgstr "قيمة التقييم (/5) "

#. module: industry_fsm
#: model:project.task,legend_done:industry_fsm.planning_task_0
#: model:project.task,legend_done:industry_fsm.planning_task_1
#: model:project.task,legend_done:industry_fsm.planning_task_10
#: model:project.task,legend_done:industry_fsm.planning_task_11
#: model:project.task,legend_done:industry_fsm.planning_task_12
#: model:project.task,legend_done:industry_fsm.planning_task_13
#: model:project.task,legend_done:industry_fsm.planning_task_14
#: model:project.task,legend_done:industry_fsm.planning_task_15
#: model:project.task,legend_done:industry_fsm.planning_task_16
#: model:project.task,legend_done:industry_fsm.planning_task_17
#: model:project.task,legend_done:industry_fsm.planning_task_18
#: model:project.task,legend_done:industry_fsm.planning_task_19
#: model:project.task,legend_done:industry_fsm.planning_task_2
#: model:project.task,legend_done:industry_fsm.planning_task_20
#: model:project.task,legend_done:industry_fsm.planning_task_3
#: model:project.task,legend_done:industry_fsm.planning_task_4
#: model:project.task,legend_done:industry_fsm.planning_task_5
#: model:project.task,legend_done:industry_fsm.planning_task_6
#: model:project.task,legend_done:industry_fsm.planning_task_7
#: model:project.task,legend_done:industry_fsm.planning_task_8
#: model:project.task,legend_done:industry_fsm.planning_task_9
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_1
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_2
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_3
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_4
msgid "Ready"
msgstr "جاهز"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Ready to <b>manage your onsite interventions</b>? <i>Click Field Service to "
"start.</i>"
msgstr ""
"أأنت جاهز <b>لإدارة عملياتك في مواقع العمل</b>؟ <i>اضغط على الخدمة الميدانية"
" للبدء.</i> "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Remaining Hours"
msgstr "الساعات المتبقية"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Responsible"
msgstr "المسؤول "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "Schedule tasks and assign them to your workers."
msgstr "قم بجدولة المهام ثم إسنادها إلى عمالك. "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Schedule your tasks and assign them to your workers."
msgstr "قم بجدولة مهامك ثم إسنادها إلى عمالك. "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Search planning"
msgstr "البحث في التخطيط "

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Select the <b>customer</b> of your task."
msgstr "اختر <b>العميل</b> لمهمتك. "

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr "الإعدادات"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Stage"
msgstr "المرحلة"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_type_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_stage
msgid "Stages"
msgstr "المراحل"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_project_task_inherit_view_kanban
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Start Date"
msgstr "تاريخ البداية"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Stop the <b>timer</b> when you are done."
msgstr "قم بإيقاف <b>المؤقت</b> عند انتهائك. "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_subtasks
msgid "Sub-tasks"
msgstr "المهام الفرعية"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Sum of Effective Hours"
msgstr "إجمالي الساعات الفعلية "

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.menu_project_tags_act
msgid "Tags"
msgstr "علامات التصنيف "

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
msgid "Task"
msgstr "المهمة"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_task_dependencies
msgid "Task Dependencies"
msgstr "تبعيات المهام "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
msgid "Task Done"
msgstr "انتهت المهمة "

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_intervention_details
msgid "Task: Intervention Scheduled"
msgstr "المهمة: تمت جدولة العمل الميداني "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__task_id
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model:project.project,label_tasks:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks"
msgstr "المهام"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Tasks Analysis"
msgstr "تحليل المهام"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks in Conflict"
msgstr "المهام المتعارضة "

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Time"
msgstr "الوقت"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr "الوقت المستغرق"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_sale
msgid "Time and Material"
msgstr "الوقت والمواد "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Time recorded on this task"
msgstr "الوقت المسجل في هذه المهمة "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "الجداول الزمنية "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Do"
msgstr "المهام المراد تنفيذها"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Schedule"
msgstr "لجدولتها "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""
"لإنجاز المهام، استخدم الأنشطة والحالات في المهام.<br>\n"
"                دردش في الوقت الفعلي أو تواصل عبر البريد الإلكتروني للتواصل بشكل أكثر فعالية. "

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "Track the progress of your tasks from their creation to their closing."
msgstr "تتبع تقدم مهامك منذ إنشائها وحتى انتهائها. "

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr "المستخدم"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
msgid "Working Days to Assign"
msgstr "أيام العمل لتعيينها "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "Working Days to Close"
msgstr "أيام العمل لإغلاقها "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Working Hours to Assign"
msgstr "ساعات العمل لتعيينها "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Working Hours to Close"
msgstr "ساعات العمل لإغلاقها "

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Worksheets"
msgstr "أوراق العمل "

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_intervention_details
msgid ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.planned_date_end and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.planned_date_end, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"
msgstr ""
"تمت جدولة عملك الميداني {{ object.planned_date_begin and "
"object.planned_date_end and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.planned_date_end, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }} "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm
msgid "e.g. Boiler replacement"
msgstr "مثال: استبدال الغلاية "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "is FSM ?"
msgstr "إدارة الخدمات الميدانية؟ "

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "working days to assign"
msgstr "أيام العمل لإسنادها "
