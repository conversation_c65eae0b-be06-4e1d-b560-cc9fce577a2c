# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON> <ma<PERSON>@stedjan.com>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:16+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON>, 2022\n"
"Language-Team: <PERSON> (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__nbr
msgid "# of Tasks"
msgstr "# oppgaver"

#. module: industry_fsm
#: model:mail.template,body_html:industry_fsm.mail_template_data_intervention_details
msgid ""
"<div>\n"
"    <t t-set=\"date_begin\" t-value=\"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    <t t-set=\"date_end\" t-value=\"format_datetime(object.planned_date_end, tz=object.partner_id.tz, lang_code=object.partner_id.lang)\"/>\n"
"\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">customer</t>,<br/><br/>\n"
"    <t t-if=\"date_begin and date_end\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled from the <t t-out=\"date_begin or ''\">05/31/2021 12:30:00</t> to the <t t-out=\"date_end or ''\">05/31/2021 14:30:00</t>.\n"
"    </t>\n"
"    <t t-else=\"\">\n"
"        Your <t t-out=\"object.name or ''\">Boiler maintenance</t> intervention is scheduled.\n"
"    </t>\n"
"    <br/><br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_inherit_project_task_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                        <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('planned_date_begin', '=', False), ('planned_date_end', '=', False)]}\"/>"
msgstr ""

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Add a <b>title</b> <i>(e.g. Boiler maintenance, Air-conditioning "
"installation, etc.).</i>"
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_manager
msgid "Administrator"
msgstr "Administrator"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_all_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_root
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_todo
msgid "All Tasks"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "Analyze the performance of your tasks and your workers."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Archived"
msgstr "Arkivert"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__user_ids
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__user_ids
msgid "Assignees"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_assign
msgid "Assignment Date"
msgstr ""

#. module: industry_fsm
#: model:project.task,legend_blocked:industry_fsm.planning_task_0
#: model:project.task,legend_blocked:industry_fsm.planning_task_1
#: model:project.task,legend_blocked:industry_fsm.planning_task_10
#: model:project.task,legend_blocked:industry_fsm.planning_task_11
#: model:project.task,legend_blocked:industry_fsm.planning_task_12
#: model:project.task,legend_blocked:industry_fsm.planning_task_13
#: model:project.task,legend_blocked:industry_fsm.planning_task_14
#: model:project.task,legend_blocked:industry_fsm.planning_task_15
#: model:project.task,legend_blocked:industry_fsm.planning_task_16
#: model:project.task,legend_blocked:industry_fsm.planning_task_17
#: model:project.task,legend_blocked:industry_fsm.planning_task_18
#: model:project.task,legend_blocked:industry_fsm.planning_task_19
#: model:project.task,legend_blocked:industry_fsm.planning_task_2
#: model:project.task,legend_blocked:industry_fsm.planning_task_20
#: model:project.task,legend_blocked:industry_fsm.planning_task_3
#: model:project.task,legend_blocked:industry_fsm.planning_task_4
#: model:project.task,legend_blocked:industry_fsm.planning_task_5
#: model:project.task,legend_blocked:industry_fsm.planning_task_6
#: model:project.task,legend_blocked:industry_fsm.planning_task_7
#: model:project.task,legend_blocked:industry_fsm.planning_task_8
#: model:project.task,legend_blocked:industry_fsm.planning_task_9
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_1
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_2
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_3
#: model:project.task.type,legend_blocked:industry_fsm.planning_project_stage_4
msgid "Blocked"
msgstr "Blokkert"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_project_fsm
msgid "By Project"
msgstr "Etter prosjekt"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.project_task_menu_planning_by_user_fsm
msgid "By User"
msgstr "Etter bruker"

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_4
msgid "Cancelled"
msgstr "Kansellert"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__company_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Company"
msgstr "Firma"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Confirm the <b>time spent</b> on your task. <i>Tip: a rounding of 15min has "
"automatically been applied.</i>"
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_quotation_from_task
msgid "Create new quotations directly from the tasks"
msgstr "Opprett nytt tilbud direkte fra oppgaven"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Create new quotations directly from your tasks"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__delay_endings_days
msgid "Days to Deadline"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_deadline
msgid "Deadline"
msgstr "Frist"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_enabled_conditions_count
msgid "Display Enabled Conditions Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_primary
msgid "Display Mark As Done Primary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_mark_as_done_secondary
msgid "Display Mark As Done Secondary"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__display_satisfied_conditions_count
msgid "Display Satisfied Conditions Count"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,help:industry_fsm.field_project_task__is_fsm
msgid ""
"Display tasks in the Field Service module and allow planning with start/end "
"dates."
msgstr ""

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_3
msgid "Done"
msgstr "Fullført"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_effective
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Effective Hours"
msgstr "Effektive timer"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_end
msgid "Ending Date"
msgstr "Sluttdato"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__group_industry_fsm_quotations
msgid "Extra Quotations"
msgstr "Ekstra tilbuder"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/company.py:0
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__is_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__is_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_root
#: model:project.project,name:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
#, python-format
msgid "Field Service"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
msgid "Find here your itinerary for today's tasks."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
msgid "Find here your tasks planned for the following days."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Future"
msgstr "Fremtid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Group By"
msgstr "Grupper etter"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__has_complete_partner_address
msgid "Has Complete Partner Address"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__id
msgid "ID"
msgstr "ID"

#. module: industry_fsm
#: model:project.task,legend_normal:industry_fsm.planning_task_0
#: model:project.task,legend_normal:industry_fsm.planning_task_1
#: model:project.task,legend_normal:industry_fsm.planning_task_10
#: model:project.task,legend_normal:industry_fsm.planning_task_11
#: model:project.task,legend_normal:industry_fsm.planning_task_12
#: model:project.task,legend_normal:industry_fsm.planning_task_13
#: model:project.task,legend_normal:industry_fsm.planning_task_14
#: model:project.task,legend_normal:industry_fsm.planning_task_15
#: model:project.task,legend_normal:industry_fsm.planning_task_16
#: model:project.task,legend_normal:industry_fsm.planning_task_17
#: model:project.task,legend_normal:industry_fsm.planning_task_18
#: model:project.task,legend_normal:industry_fsm.planning_task_19
#: model:project.task,legend_normal:industry_fsm.planning_task_2
#: model:project.task,legend_normal:industry_fsm.planning_task_20
#: model:project.task,legend_normal:industry_fsm.planning_task_3
#: model:project.task,legend_normal:industry_fsm.planning_task_4
#: model:project.task,legend_normal:industry_fsm.planning_task_5
#: model:project.task,legend_normal:industry_fsm.planning_task_6
#: model:project.task,legend_normal:industry_fsm.planning_task_7
#: model:project.task,legend_normal:industry_fsm.planning_task_8
#: model:project.task,legend_normal:industry_fsm.planning_task_9
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_1
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_2
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_3
#: model:project.task.type,legend_normal:industry_fsm.planning_project_stage_4
#: model:project.task.type,name:industry_fsm.planning_project_stage_2
msgid "In Progress"
msgstr "Pågår"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Initially Planned Hours"
msgstr "Opprinnelig planlagte timer"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__state
msgid "Kanban State"
msgstr "Kanban-status"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid ""
"Keep track of the products used for your tasks, and invoice your time and "
"material to your customers"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__date_last_stage_update
msgid "Last Stage Update"
msgstr "Siste oppdatering av stadium"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Launch the timer to <b>track the time spent</b> on your task."
msgstr ""

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Let's <b>mark your task as done!</b> <i>Tip: when doing so, your stock will "
"automatically be updated, and your task will move to the next stage.</i>"
msgstr ""

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_view_form_inherit
msgid "Manage tasks in the Field Service module"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_map
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_map
msgid "Map"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Mark as done"
msgstr "Marker som fullført"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_tasks_menu
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "My Tasks"
msgstr "Oppgavene mine"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Navigate To"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_0
msgid "New"
msgstr "Ny"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_user_action_report_fsm
msgid "No data yet!"
msgstr "Ingen data ennå"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "No projects found. Let's create one!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "No stages found. Let's create one!"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_map
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "No tasks found. Let's create one!"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_days_close
msgid "Number of Working Days to close the task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_days_open
msgid "Number of Working Days to open the task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Number of Working Hours to close the task"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,help:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Number of Working Hours to open the task"
msgstr ""

#. module: industry_fsm
#: model:project.task.type,name:industry_fsm.planning_project_stage_1
msgid "Planned"
msgstr "Planlagt"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_inherit_project_task_view_form
msgid "Planned Date"
msgstr "Planlagt dato"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__hours_planned
msgid "Planned Hours"
msgstr "Planlaget timer"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Planned for Today"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_planning
msgid "Planning"
msgstr "Planlegging"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_project
msgid "Planning by Project"
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Planning by User"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__priority
msgid "Priority"
msgstr "Prioritet"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__progress
msgid "Progress"
msgstr "Fremdrift"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_project
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__project_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Project"
msgstr "Prosjekt"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_project_action_only_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_project
msgid "Projects"
msgstr "Prosjekter"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_project_action_only_fsm
msgid "Projects regroup tasks on the same topic, and each has its dashboard."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.res_config_settings_view_form
msgid "Provide custom worksheet reports to be signed off by customers"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__rating_last_value
msgid "Rating Value (/5)"
msgstr ""

#. module: industry_fsm
#: model:project.task,legend_done:industry_fsm.planning_task_0
#: model:project.task,legend_done:industry_fsm.planning_task_1
#: model:project.task,legend_done:industry_fsm.planning_task_10
#: model:project.task,legend_done:industry_fsm.planning_task_11
#: model:project.task,legend_done:industry_fsm.planning_task_12
#: model:project.task,legend_done:industry_fsm.planning_task_13
#: model:project.task,legend_done:industry_fsm.planning_task_14
#: model:project.task,legend_done:industry_fsm.planning_task_15
#: model:project.task,legend_done:industry_fsm.planning_task_16
#: model:project.task,legend_done:industry_fsm.planning_task_17
#: model:project.task,legend_done:industry_fsm.planning_task_18
#: model:project.task,legend_done:industry_fsm.planning_task_19
#: model:project.task,legend_done:industry_fsm.planning_task_2
#: model:project.task,legend_done:industry_fsm.planning_task_20
#: model:project.task,legend_done:industry_fsm.planning_task_3
#: model:project.task,legend_done:industry_fsm.planning_task_4
#: model:project.task,legend_done:industry_fsm.planning_task_5
#: model:project.task,legend_done:industry_fsm.planning_task_6
#: model:project.task,legend_done:industry_fsm.planning_task_7
#: model:project.task,legend_done:industry_fsm.planning_task_8
#: model:project.task,legend_done:industry_fsm.planning_task_9
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_0
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_1
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_2
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_3
#: model:project.task.type,legend_done:industry_fsm.planning_project_stage_4
msgid "Ready"
msgstr "Klar"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid ""
"Ready to <b>manage your onsite interventions</b>? <i>Click Field Service to "
"start.</i>"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__remaining_hours
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
msgid "Remaining Hours"
msgstr "Gjenstående timer"

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting
msgid "Reporting"
msgstr "Rapportering"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Responsible"
msgstr "Ansvarlig"

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_to_schedule_fsm
msgid "Schedule tasks and assign them to your workers."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_project
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_fsm_planning_groupby_user
msgid "Schedule your tasks and assign them to your workers."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Search planning"
msgstr ""

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Select the <b>customer</b> of your task."
msgstr ""

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.res_config_settings_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_res_config
msgid "Settings"
msgstr "Innstillinger"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__stage_id
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Stage"
msgstr "Stadium"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_type_action_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_settings_stage
msgid "Stages"
msgstr "Stadier"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_sharing_project_task_inherit_view_kanban
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Start Date"
msgstr "Startdato"

#. module: industry_fsm
#. openerp-web
#: code:addons/industry_fsm/static/src/js/tours/industry_fsm_tour.js:0
#, python-format
msgid "Stop the <b>timer</b> when you are done."
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_subtasks
msgid "Sub-tasks"
msgstr "Deloppgaver"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Sum of Effective Hours"
msgstr ""

#. module: industry_fsm
#: model:ir.ui.menu,name:industry_fsm.menu_project_tags_act
msgid "Tags"
msgstr "Etiketter"

#. module: industry_fsm
#: model:ir.model,name:industry_fsm.model_project_task
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__name
msgid "Task"
msgstr "Oppgave"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_project__allow_task_dependencies
msgid "Task Dependencies"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_project_task__fsm_done
msgid "Task Done"
msgstr ""

#. module: industry_fsm
#: model:mail.template,name:industry_fsm.mail_template_data_intervention_details
msgid "Task: Intervention Scheduled"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__task_id
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_tasks_kanban
#: model:project.project,label_tasks:industry_fsm.fsm_project
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_calendar_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_list_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks"
msgstr "Oppgaver"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_user_action_report_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_reporting_task_analysis
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
#: model_terms:ir.ui.view,arch_db:industry_fsm.report_project_task_user_fsm_view_tree
msgid "Tasks Analysis"
msgstr "Oppgaveanalyse"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "Tasks in Conflict"
msgstr ""

#. module: industry_fsm
#: code:addons/industry_fsm/models/project.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
#, python-format
msgid "Time"
msgstr "Tid"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Time Spent"
msgstr "Tid brukt"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_sale
msgid "Time and Material"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "Time recorded on this task"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.timesheet_view_form
msgid "Timesheet"
msgstr "Timeliste"

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Do"
msgstr "Å gjøre"

#. module: industry_fsm
#: model:ir.actions.act_window,name:industry_fsm.project_task_action_to_schedule_fsm
#: model:ir.ui.menu,name:industry_fsm.fsm_menu_all_tasks_schedule
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_view_search_fsm
msgid "To Schedule"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_action_all_fsm
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real-time or by email to collaborate efficiently."
msgstr ""

#. module: industry_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm.project_task_type_action_fsm
msgid "Track the progress of your tasks from their creation to their closing."
msgstr ""

#. module: industry_fsm
#: model:res.groups,name:industry_fsm.group_fsm_user
msgid "User"
msgstr "Bruker"

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_open
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
msgid "Working Days to Assign"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_days_close
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_graph
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "Working Days to Close"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_open
msgid "Working Hours to Assign"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_report_project_task_user_fsm__working_hours_close
msgid "Working Hours to Close"
msgstr ""

#. module: industry_fsm
#: model:ir.model.fields,field_description:industry_fsm.field_res_config_settings__module_industry_fsm_report
msgid "Worksheets"
msgstr ""

#. module: industry_fsm
#: model:mail.template,subject:industry_fsm.mail_template_data_intervention_details
msgid ""
"Your intervention is scheduled {{ object.planned_date_begin and "
"object.planned_date_end and 'from the ' + "
"format_datetime(object.planned_date_begin, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) + ' to the ' + "
"format_datetime(object.planned_date_end, tz=object.partner_id.tz, "
"lang_code=object.partner_id.lang) or '' }}"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.quick_create_task_form_fsm
msgid "e.g. Boiler replacement"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.view_task_form2_inherit
msgid "is FSM ?"
msgstr ""

#. module: industry_fsm
#: model_terms:ir.ui.view,arch_db:industry_fsm.project_task_user_view_pivot
msgid "working days to assign"
msgstr ""
