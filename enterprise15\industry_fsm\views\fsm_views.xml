<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_task_view_list_fsm" model="ir.ui.view">
        <field name="name">project.task.tree.fsm</field>
        <field name="model">project.task</field>
        <field name="arch" type="xml">
            <tree string="Tasks" multi_edit="1" sample="1" default_order="planned_date_begin">
                <field name="child_text" invisible="1"/>
                <field name="priority" widget="priority" optional="hide" nolabel="1"/>
                <field name="name" widget="name_with_subtask_count"/>
                <field name="project_id" readonly="1" optional="show"/>
                <field name="partner_id" optional="show"/>
                <field name="user_ids" widget="many2many_avatar_user" optional="show" invisible="context.get('user_invisible', False)" domain="[('share', '=', False), ('active', '=', True)]"/>
                <field name="company_id" optional="show" groups="base.group_multi_company" readonly="1"/>
                <field name="planned_date_begin" optional="show"/>
                <field name="planned_date_end" optional="show"/>
                <field name="planned_hours" widget="timesheet_uom_no_toggle" sum="Initially Planned Hours" optional="hide"/>
                <field name="effective_hours" widget="timesheet_uom" sum="Effective Hours" optional="hide"/>
                <field name="remaining_hours" widget="timesheet_uom" sum="Remaining Hours" optional="hide" decoration-danger="progress &gt;= 100" decoration-warning="progress &gt;= 80 and progress &lt; 100"/>
                <field name="subtask_effective_hours" widget="timesheet_uom" optional="hide"/>
                <field name="total_hours_spent" widget="timesheet_uom" optional="hide"/>
                <field name="progress" widget="progressbar" optional="hide" groups="hr_timesheet.group_hr_timesheet_user"/>
                <field name="activity_ids" widget="list_activity" invisible="context.get('set_visible',False)" optional="show" readonly="1"/>
                <field name="tag_ids" optional="show" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="kanban_state" widget="state_selection" optional="hide" readonly="1"/>
                <field name="stage_id" optional="hide"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </tree>
        </field>
    </record>

    <record id="project_task_view_search_fsm" model="ir.ui.view">
        <field name="name">project.task.search.fsm</field>
        <field name="model">project.task</field>
        <field name="arch" type="xml">
            <search string="Search planning">
                <field name="name" string="Tasks"/>
                <field name="partner_id" operator="child_of"/>
                <field name="user_ids" filter_domain="[('user_ids.name', 'ilike', self), ('user_ids.active', 'in', [True, False])]"/>
                <field name="active"/>
                <filter string="My Tasks" name="my_tasks" domain="[('user_ids', 'in', uid)]"/>
                <separator />
                <filter string="To Schedule" name="schedule" domain="[
                    '|',
                    ('user_ids', '=', False),
                    '&amp;',
                        ('planned_date_begin', '=', False),
                        ('planned_date_end', '=', False)
                ]" groups="industry_fsm.group_fsm_manager"/>
                <filter string="To Do" name="todo" domain="[('fsm_done', '=', False), ('user_ids', '!=', False), ('planned_date_begin', '!=', False), ('planned_date_end', '!=', False)]"/>
                <separator />
                <filter name="planned_today" string="Planned for Today" domain="[
                    ('planned_date_begin','&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59))),
                    ('planned_date_end','&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter name="planned_future" string="Future" domain="[('planned_date_begin', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <separator/>
                <filter name="conflict_task" string="Tasks in Conflict" domain="[('planning_overlap', '&gt;', 0)]"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Responsible" name="groupby_user" context="{'group_by':'user_ids'}"/>
                    <filter string="Stage" name="groupby_stage" context="{'group_by':'stage_id'}"/>
                    <filter string="Project" name="groupby_project" context="{'group_by':'project_id'}"/>
                    <filter string="Company" name="groupby_company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    <filter string="Start Date" name="groupby_planned_date_begin" context="{'group_by': 'planned_date_begin:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="project_task_view_calendar_fsm" model="ir.ui.view">
        <field name="name">project.task.calendar.fsm</field>
        <field name="model">project.task</field>
        <field name="arch" type="xml">
            <calendar date_start="planned_date_begin" date_stop="planned_date_end" string="Tasks" mode="month" color="user_ids" event_open_popup="1" form_view_id="%(project.view_task_form2)d" quick_add="0" show_unusual_days="True">
                <field name="name"/>
                <field name="project_id" filters="1"/>
                <field name="user_ids" widget="many2many_avatar_user" attrs="{'invisible': [('user_ids', '=', [])]}"/>
                <field name="partner_id" widget="res_partner_many2one" context="{'show_address': 1}"/>
                <field name="partner_phone" widget="phone" attrs="{'invisible': [('partner_phone', '=', False)]}"/>
                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}" attrs="{'invisible': [('tag_ids', '=', [])]}"/>
            </calendar>
        </field>
    </record>

    <record id="project_task_view_gantt_fsm" model="ir.ui.view">
        <field name="name">project.task.view.gantt</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project_enterprise.project_task_view_gantt"/>
        <field name="arch" type="xml">
            <xpath expr="//gantt" position="attributes">
                <attribute name="total_row">True</attribute>
            </xpath>
        </field>
    </record>

    <!-- Non primary kanban inherit (fsm tasks must display start date instead of deadline in all application) -->
    <record id="project_task_view_kanban" model="ir.ui.view">
        <field name="name">project.task.kanban.fsm.nonprimary</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.view_task_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="inside">
                <field name="is_fsm"/>
                <field name="planned_date_begin"/>
                <field name="planned_date_begin_formatted"/>
                <field name="fsm_done" />
            </xpath>
            <xpath expr="//templates/descendant::field[@name='partner_id']" position="after">
                <t t-if="record.is_fsm.raw_value and record.partner_city.value"> • <field name="partner_city" /></t>
            </xpath>
            <div name="date_deadline" position="attributes">
                <attribute name="attrs">{'invisible': ['|', ('is_fsm', '=', True), ('is_closed', '=', True)]}</attribute>
            </div>
            <div name="date_deadline" position="after">
                <!-- <field name="planned_date_begin" widget="remaining_days"/> -->
                <t t-if="record.planned_date_begin.raw_value">
                    <t t-if="context.fsm_task_kanban_whole_date || !('fsm_task_kanban_whole_date' in context)">
                        <t t-if="record.is_fsm.raw_value" t-set="deadline_class" t-value="" />
                        <t t-if="record.is_fsm.raw_value and !record.fsm_done.raw_value and record.planned_date_begin.raw_value and moment(record.planned_date_begin.raw_value.toISOString()).startOf('day') lt moment().startOf('day')"
                            t-set="deadline_class" t-value="'oe_kanban_text_red'" />
                        <t t-elif="record.is_fsm.raw_value and !record.fsm_done.raw_value and record.planned_date_begin.raw_value and moment(record.planned_date_begin.raw_value.toISOString()).startOf('day') lt moment().endOf('day')"
                            t-set="deadline_class" t-value="'text-warning font-weight-bold'" />
                        <t t-if="record.fsm_done.raw_value" t-value="'font-weight-bold'"></t>
                    </t>
                    <t t-if="'fsm_task_kanban_whole_date' in context &amp;&amp; !context.fsm_task_kanban_whole_date">
                        <t t-set="deadline_class"></t>
                        <t t-if="!record.fsm_done.raw_value and record.planned_date_begin.raw_value and moment(record.planned_date_begin.raw_value.toISOString()) lt moment()"
                            t-set="deadline_class" t-value="'oe_kanban_text_red'" />
                    </t>
                    <span name="date" title="Start Date" t-attf-class="#{deadline_class || ''}"><t t-esc="record.planned_date_begin_formatted.raw_value"/></span>
                </t>
            </div>
        </field>
    </record>

    <!-- Primary kanban inherit all tasks in fsm must display their date in hours format -->
    <record id="project_task_view_kanban_fsm" model="ir.ui.view">
        <field name="name">project.task.kanban.fsm</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.view_task_kanban"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes" t-translation="off">
                <attribute name="group_create">0</attribute>
                <attribute name="default_order">planned_date_begin</attribute>
                <attribute name="class">o_fsm_kanban o_kanban_project_tasks</attribute>
                <attribute name="quick_create_view">industry_fsm.quick_create_task_form_fsm</attribute>
                <attribute name="on_create">quick_create</attribute>
            </xpath>
        </field>
    </record>

    <!--
        FSM Actions
    -->

    <!-- My Tasks: kanban action -->
    <record id="project_task_action_fsm" model="ir.actions.act_window">
        <field name="name">My Tasks</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">map,kanban,gantt,calendar,tree,form,activity</field>
        <field name="search_view_id" ref="project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context">{
            'fsm_mode': True,
            'show_address': True,
            'search_default_my_tasks': True,
            'search_default_planned_future': True,
            'search_default_planned_today': True,
            'fsm_task_kanban_whole_date': False,
            'search_default_groupby_planned_date_begin': True,
            'default_mode': 'day',
            'default_scale': 'day',
        }</field>
        <field name="help" type="html">
            <p class='o_view_nocontent_smiling_face'>No tasks found. Let's create one!</p>
            <p>Find here your tasks planned for the following days.</p>
        </field>
    </record>

    <record id="project_task_action_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project_task_view_kanban_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm"/>
    </record>

    <record id="project_task_action_fsm_view_map" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">map</field>
        <field name="view_id" ref="project_enterprise.project_task_map_view"/>
        <field name="act_window_id" ref="project_task_action_fsm"/>
    </record>

    <record id="project_task_action_fsm_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="20"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="industry_fsm.project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm"/>
    </record>

     <record id="project_task_action_fsm_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm"/>
    </record>

    <record id="project_task_action_fsm_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="30"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_fsm"/>
    </record>

    <!-- My Tasks: map action -->
    <record id="project_task_action_fsm_map" model="ir.actions.act_window">
        <field name="name">Map</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">map,kanban,gantt,calendar,tree,form,activity</field>
        <field name="search_view_id" ref="project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context">{
            'fsm_mode': True,
            'search_default_my_tasks': True,
            'search_default_planned_today': True,
            'fsm_task_kanban_whole_date':False,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tasks found. Let's create one!
            </p><p>
                Find here your itinerary for today's tasks.
            </p>
        </field>
    </record>

    <record id="project_task_action_fsm_map_view_map" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">map</field>
        <field name="view_id" ref="project_enterprise.project_task_map_view"/>
        <field name="act_window_id" ref="project_task_action_fsm_map"/>
    </record>

    <record id="project_task_action_fsm_map_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project_task_view_kanban_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_map"/>
    </record>

    <record id="project_task_action_fsm_map_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="20"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="industry_fsm.project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_map"/>
    </record>

    <record id="project_task_action_fsm_map_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_map"/>
    </record>

    <record id="project_task_action_fsm_map_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="30"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_fsm_map"/>
    </record>

    <!-- All Tasks default group by stage -->
    <record id="project_task_view_kanban_fsm_all" model="ir.ui.view">
        <field name="name">project.task.kanban.fsm.all</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.project_task_view_kanban_fsm"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="attributes" t-translation="off">
                <attribute name="default_group_by">stage_id</attribute>
            </xpath>
        </field>
    </record>
    <!-- All Tasks: main action -->
    <record id="project_task_action_all_fsm" model="ir.actions.act_window">
        <field name="name">All Tasks</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">tree,kanban,gantt,calendar,map,pivot,graph,form,activity</field>
        <field name="search_view_id" ref="project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context">{
            'fsm_mode': True,
            'default_user_ids': False,
            'fsm_task_kanban_whole_date': True,
            'graph_measure': '__count__',
            'graph_groupbys': ['project_id'],
            'pivot_measures': ['__count__'],
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tasks found. Let's create one!
            </p><p>
                To get things done, use activities and status on tasks.<br/>
                Chat in real-time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="project_task_action_all_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project_task_view_kanban_fsm_all"/>
        <field name="act_window_id" ref="project_task_action_all_fsm"/>
    </record>

    <record id="project_task_action_all_fsm_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_all_fsm"/>
    </record>

    <record id="project_task_action_all_fsm_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_all_fsm"/>
    </record>

    <record id="project_task_action_all_fsm_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="40"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_all_fsm"/>
    </record>

    <!-- All Tasks: to schedule action -->
    <record id="project_task_action_to_schedule_fsm" model="ir.actions.act_window">
        <field name="name">To Schedule</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">tree,kanban,gantt,calendar,map,pivot,graph,form,activity</field>
        <field name="search_view_id" ref="project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context">{
            'fsm_mode': True,
            'search_default_schedule': True,
            'default_user_ids': False,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tasks found. Let's create one!
            </p><p>Schedule tasks and assign them to your workers.</p>
        </field>
    </record>

    <record id="project_task_action_to_schedule_fsm_view_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_to_schedule_fsm"/>
    </record>

    <record id="project_task_action_to_schedule_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project_task_view_kanban_fsm"/>
        <field name="act_window_id" ref="project_task_action_to_schedule_fsm"/>
    </record>

    <record id="project_task_action_to_schedule_fsm_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="20"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_to_schedule_fsm"/>
    </record>

    <record id="project_task_action_to_schedule_fsm_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="45"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_to_schedule_fsm"/>
    </record>

    <!-- Planning : by users -->
    <record id="project_task_action_fsm_planning_groupby_user" model="ir.actions.act_window">
        <field name="name">Planning by User</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">gantt,kanban,tree,calendar,map,pivot,graph,form,activity</field>
        <field name="search_view_id" ref="project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context" eval="{'search_default_groupby_user': 1, 'fsm_mode': 1, 'task_nameget_with_hours': 1}"/>
        <field name="help" type="html">
            <p class='o_view_nocontent_smiling_face'>
                No tasks found. Let's create one!
            </p><p>
                Schedule your tasks and assign them to your workers.
            </p>
        </field>
    </record>

    <record id="project_task_action_planning_groupby_user_gantt" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">gantt</field>
        <field name="view_id" ref="project_enterprise.project_task_view_gantt"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project_task_view_kanban_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_fsm_view_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="20"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_fsm_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_fsm_view_map" model="ir.actions.act_window.view">
        <field name="sequence" eval="30"/>
        <field name="view_mode">map</field>
        <field name="view_id" ref="project_enterprise.project_task_map_view"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_fsm_view_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="33"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="project.view_project_task_pivot"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_fsm_view_graph" model="ir.actions.act_window.view">
        <field name="sequence" eval="34"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="project.view_project_task_graph"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <record id="project_task_action_planning_groupby_user_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="35"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_user"/>
    </record>

    <!-- Planning : by project -->
    <record id="project_task_action_fsm_planning_groupby_project" model="ir.actions.act_window">
        <field name="name">Planning by Project</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">kanban,tree,calendar,map,pivot,graph,activity</field>
        <field name="search_view_id" ref="project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context" eval="{'search_default_groupby_project': 1, 'search_default_groupby_user': 2, 'fsm_mode': 1, 'task_nameget_with_hours': 1}"/>
        <field name="help" type="html">
            <p class='o_view_nocontent_smiling_face'>
                No tasks found. Let's create one!
            </p><p>
                Schedule your tasks and assign them to your workers.
            </p>
        </field>
    </record>

    <record id="project_task_action_planning_groupby_project_gantt" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">gantt</field>
        <field name="view_id" ref="project_enterprise.project_task_view_gantt"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_project"/>
    </record>

    <record id="project_task_action_planning_groupby_project_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project_task_view_kanban_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_project"/>
    </record>

    <record id="project_task_action_planning_groupby_project_fsm_view_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="20"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_project"/>
    </record>

    <record id="project_task_action_planning_groupby_project_fsm_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_project"/>
    </record>

    <record id="project_task_action_planning_groupby_project_fsm_view_map" model="ir.actions.act_window.view">
        <field name="sequence" eval="30"/>
        <field name="view_mode">map</field>
        <field name="view_id" ref="project_enterprise.project_task_map_view"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_project"/>
    </record>

    <record id="project_task_action_planning_groupby_project_fsm_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="35"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_fsm_planning_groupby_project"/>
    </record>

    <!-- Settings actions -->
    <record id="res_config_settings_action_fsm" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'industry_fsm', 'bin_size': False}</field>
    </record>

    <record id="project_view_tree_primary" model="ir.ui.view">
        <field name="name">project.view.tree.primary</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.view_project"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <field name="partner_id" position="attributes">
                <attribute name="optional">hide</attribute>
            </field>
        </field>
    </record>

    <record id="project_project_action_only_fsm" model="ir.actions.act_window" >
        <field name="name">Projects</field>
        <field name="res_model">project.project</field>
        <field name="domain">[('is_fsm', '=', True)]</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="view_id" ref="project.view_project"/>
        <field name="search_view_id" ref="project.view_project_project_filter"/>
        <field name="target">main</field>
        <field name="context">{
            'fsm_mode': True,
            'default_is_fsm': True,
            'default_allow_timesheets': True,
        }</field>
        <field name="help" type="html">
            <p class='o_view_nocontent_smiling_face'>No projects found. Let's create one!</p>
            <p>Projects regroup tasks on the same topic, and each has its dashboard.</p>
        </field>
    </record>
    <record id="project_project_action_only_fsm_view_tree" model="ir.actions.act_window.view">
        <field name="sequence" eval="15"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="industry_fsm.project_view_tree_primary"/>
        <field name="act_window_id" ref="project_project_action_only_fsm"/>
    </record>
    <record id="project_project_action_only_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="project.view_project_kanban"/>
        <field name="act_window_id" ref="project_project_action_only_fsm"/>
    </record>


    <record id="project_project_view_form_simplified_inherit" model="ir.ui.view">
        <field name="name">project.project.view.form.simplified.inherit</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.project_project_view_form_simplified"/>
        <field name="arch" type="xml">
            <field name="user_id" position="after">
                <field name="is_fsm" invisible="1"/>
            </field>
        </field>
    </record>

    <record id="project_task_type_action_fsm" model="ir.actions.act_window">
        <field name="name">Stages</field>
        <field name="res_model">project.task.type</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="domain">[('project_ids.is_fsm', '=', True)]</field>
        <field name="view_id" ref="project.task_type_tree"/>
        <field name="context">{
            'fsm_mode': True,
        }</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No stages found. Let's create one!
          </p><p>
            Track the progress of your tasks from their creation to their closing.
          </p>
        </field>
    </record>

    <!--
        FSM Menus
    -->

    <menuitem id="fsm_menu_root"
        name="Field Service"
        sequence="80"
        web_icon="industry_fsm,static/description/icon.png"
        groups="industry_fsm.group_fsm_user"/>

    <menuitem id="fsm_tasks_menu"
        name="My Tasks"
        sequence="10"
        parent="fsm_menu_root"
        groups="industry_fsm.group_fsm_user"/>

        <menuitem id="fsm_menu_tasks_kanban"
            name="Tasks"
            action="project_task_action_fsm"
            sequence="10"
            parent="fsm_tasks_menu"
            groups="industry_fsm.group_fsm_user"/>

        <menuitem id="fsm_menu_tasks_map"
            name="Map"
            action="project_task_action_fsm_map"
            sequence="20"
            parent="fsm_tasks_menu"
            groups="industry_fsm.group_fsm_user"/>

    <menuitem id="fsm_menu_all_tasks_root"
        name="All Tasks"
        sequence="15"
        parent="fsm_menu_root"
        groups="industry_fsm.group_fsm_manager" />

        <menuitem id="fsm_menu_all_tasks_todo"
            name="All Tasks"
            action="project_task_action_all_fsm"
            sequence="10"
            parent="industry_fsm.fsm_menu_all_tasks_root"
            groups="industry_fsm.group_fsm_manager" />

        <menuitem id="fsm_menu_all_tasks_schedule"
            name="To Schedule"
            action="project_task_action_to_schedule_fsm"
            sequence="20"
            parent="industry_fsm.fsm_menu_all_tasks_root"
            groups="industry_fsm.group_fsm_manager" />

    <menuitem id="fsm_menu_planning"
        name="Planning"
        sequence="20"
        parent="fsm_menu_root"
        groups="industry_fsm.group_fsm_manager"/>

        <menuitem id="project_task_menu_planning_by_user_fsm"
            name="By User"
            sequence="10"
            action="industry_fsm.project_task_action_fsm_planning_groupby_user"
            parent="fsm_menu_planning"
            groups="industry_fsm.group_fsm_manager"/>

        <menuitem id="project_task_menu_planning_by_project_fsm"
            name="By Project"
            sequence="15"
            action="industry_fsm.project_task_action_fsm_planning_groupby_project"
            parent="fsm_menu_planning"
            groups="industry_fsm.group_fsm_manager"/>

    <menuitem id="fsm_menu_reporting"
        name="Reporting"
        sequence="40"
        parent="fsm_menu_root"
        groups="industry_fsm.group_fsm_manager"/>

        <menuitem id="fsm_menu_reporting_task_analysis"
            name="Tasks Analysis"
            sequence="10"
            action="project_task_user_action_report_fsm"
            parent="industry_fsm.fsm_menu_reporting"
            groups="industry_fsm.group_fsm_manager"/>

    <menuitem id="fsm_menu_settings"
        name="Configuration"
        sequence="50"
        parent="industry_fsm.fsm_menu_root"
        groups="industry_fsm.group_fsm_manager"/>

        <menuitem id="fsm_menu_settings_res_config"
            name="Settings"
            parent="fsm_menu_settings"
            sequence="0"
            action="industry_fsm.res_config_settings_action_fsm"
            groups="base.group_system"/>

        <menuitem id="fsm_menu_settings_project"
            name="Projects"
            sequence="10"
            action="project_project_action_only_fsm"
            parent="fsm_menu_settings"
            groups="industry_fsm.group_fsm_manager"/>

        <menuitem id="menu_project_tags_act"
            name="Tags"
            sequence="40"
            action="project.project_tags_action"
            parent="fsm_menu_settings"
            groups="industry_fsm.group_fsm_manager"/>

        <menuitem id="fsm_menu_settings_stage"
            name="Stages"
            sequence="10"
            action="industry_fsm.project_task_type_action_fsm"
            parent="fsm_menu_settings"
            groups="industry_fsm.group_fsm_manager"/>

</odoo>
