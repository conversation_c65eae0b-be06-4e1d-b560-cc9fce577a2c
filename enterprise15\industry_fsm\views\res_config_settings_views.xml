<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.industry.fsm</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="priority" eval="75" />
        <field name="arch" type="xml">
            <xpath expr="//div[@data-key='project']" position="after">
                <div class="app_settings_block" data-string="industry_fsm" string="Field Service" data-key="industry_fsm" groups="base.group_system">
                    <h2>Field Service</h2>
                    <div class="row mt16 o_settings_container" id="fsm_tasks_management">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_industry_fsm_sale"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="module_industry_fsm_sale"/>
                                <div class="text-muted">
                                    Keep track of the products used for your tasks, and invoice your time and material to your customers
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="group_industry_fsm_quotations"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_industry_fsm_quotations"/>
                                <div class="text-muted">
                                    Create new quotations directly from your tasks
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" name="module_industry_fsm_report">
                            <div class="o_setting_left_pane">
                                <field name="module_industry_fsm_report"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="module_industry_fsm_report"/>
                                <div class="text-muted">
                                    Provide custom worksheet reports to be signed off by customers
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
