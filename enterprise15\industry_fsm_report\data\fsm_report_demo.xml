<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="fsm_worksheet_template2" model="worksheet.template">
            <field name="name">Device Installation and Maintenance</field>
            <field name="res_model">project.task</field>
            <field name="color">3</field>
        </record>

        <record id="fsm_template_field1" model="ir.model.fields">
            <field name="name">x_intervention_type</field>
            <field name="ttype">selection</field>
            <field name="field_description">Intervention Type</field>
            <field name="selection">[('first_installation','First installation'),('technical_maintenance','Technical maintenance')]</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_template_field2" model="ir.model.fields">
            <field name="name">x_description</field>
            <field name="ttype">text</field>
            <field name="field_description">Description of the Intervention</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_template_field3" model="ir.model.fields">
            <field name="name">x_manufacturer</field>
            <field name="ttype">many2one</field>
            <field name="relation">res.partner</field>
            <field name="field_description">Manufacturer</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_template_field5" model="ir.model.fields">
            <field name="name">x_checkbox</field>
            <field name="ttype">boolean</field>
            <field name="field_description">I hereby certify that this device meets the requirements of an acceptable device at the time of testing.</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_template_field6" model="ir.model.fields">
            <field name="name">x_serial_number</field>
            <field name="ttype">char</field>
            <field name="field_description">Serial Number</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_template_field7" model="ir.model.fields">
            <field name="name">x_date</field>
            <field name="ttype">date</field>
            <field name="field_description">Date</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_template_field8" model="ir.model.fields">
            <field name="name">x_worker_signature</field>
            <field name="ttype">binary</field>
            <field name="field_description">Worker Signature</field>
            <field name="model_id" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.id"/>
        </record>

        <record id="fsm_worksheet_template2_form_inherit" model="ir.ui.view">
            <field name="name">fsm.worksheet.template2.form.inherit</field>
            <field name="type">form</field>
            <field name="model" model="ir.model" eval="obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.model"/>
            <field name="inherit_id" model="ir.ui.view" search="[('name','ilike', obj().env.ref('industry_fsm_report.fsm_worksheet_template2').model_id.model),('type', '=', 'form')]"/>
            <field name="arch" type="xml">
                <xpath expr="//form/sheet" position="replace">
                    <sheet>
                        <group invisible="context.get('studio') or context.get('default_x_project_task_id')">
                            <field name="x_project_task_id" domain="[('is_fsm', '=', True)]"/>
                        </group>
                        <group class="o_fsm_worksheet_form">
                            <field name="x_name"/>
                            <field name="x_manufacturer" options="{'no_create':true,'no_open':true}"/>
                            <field name="x_serial_number"/>
                            <field name="x_intervention_type" widget="radio"/>
                            <field name="x_description"/>
                            <field name="x_checkbox"/>
                            <field name="x_date"/>
                            <field name="x_worker_signature" widget="signature"/>
                        </group>
                    </sheet>
                </xpath>
            </field>
        </record>

        <function model="worksheet.template" name="_generate_qweb_report_template" eval="[[ref('fsm_worksheet_template2')]]"/>

        <!-- The worksheet_template_id is set on the project in the post_init_hook -->

        <record id="industry_fsm.planning_task_0" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_1" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_2" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_3" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_4" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_5" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_6" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_7" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_8" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_9" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_10" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_11" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_12" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_13" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_14" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_15" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_16" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_17" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_18" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_19" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

        <record id="industry_fsm.planning_task_20" model="project.task">
            <field name="worksheet_template_id" ref="industry_fsm_report.fsm_worksheet_template2"/>
        </record>

    </data>
</odoo>
