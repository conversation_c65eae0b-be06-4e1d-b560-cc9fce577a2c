# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_report
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# martioodo hola, 2023
# jabe<PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: jabel<PERSON>, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_report
#: model:ir.actions.report,print_report_name:industry_fsm_report.task_custom_report
msgid "'Worksheet %s - %s' % (object.name, object.partner_id.name)"
msgstr "'Full de treball %s - %s' % (object.name, object.partnerid.name)"

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"<b>Review and sign</b> your <b>worksheet report</b> with your customer."
msgstr ""
"<b>Revisar i signar</b>  <b>l'informe de full de treball</b> amb el client."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "<b>Send your worksheet report</b> to your customer."
msgstr "<b>Envia l'informe del vostre full de treball</b> al teu client."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-1\"/>Sign"
msgstr "<i class=\"fa fa-check mr-1\"/>Signatura"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-2\"/>Sign"
msgstr "<i class=\"fa fa-check mr-2\"/>Signatura"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Descarregar"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Imprimir"

#. module: industry_fsm_report
#: model:mail.template,body_html:industry_fsm_report.mail_template_data_send_report
msgid ""
"<p>\n"
"                    Dear <t t-out=\"object.partner_id.name or 'Customer'\">Customer</t>,<br/><br/>\n"
"                    Please find attached the worksheet of our onsite operation. <br/><br/>\n"
"                    Feel free to contact us if you have any questions.<br/><br/>\n"
"                    Best regards,<br/><br/>\n"
"                </p>\n"
"            "
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_project_kanban_inherit_industry_fsm_report
msgid ""
"<span class=\"fa fa-pencil mr-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"
msgstr ""
"<span class=\"fa fa-pencil mr-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "<span class=\"o_label\">Worksheets</span>"
msgstr "<span class=\"olabel\">Fulls de treball</span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Signed\n"
"                            </span>"
msgstr ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Signat\n"
"                            </span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr "<strong>Client: </strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_gantt_fsm_worksheet
msgid "<strong>Template — </strong>"
msgstr "<strong>Plantilla —</strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Workers: </strong>"
msgstr "<strong>Treballadors:</strong>"

#. module: industry_fsm_report
#: model:ir.ui.menu,name:industry_fsm_report.project_task_menu_planning_by_project_fsm
msgid "By Worksheet"
msgstr "Per full de treball"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__worksheet_template_id
msgid ""
"Choose a default worksheet template for this project (you can change it "
"individually on each task)."
msgstr ""
"Escolliu una plantilla de full de treball per defecte per aquest projecte "
"(podeu canviar-la individualment a cada tasca)."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Close"
msgstr "Tancar"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_color
msgid "Color"
msgstr "Color"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_2
msgid "Comments"
msgstr "Comentaris"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "Create and fill custom reports on tasks"
msgstr "Crea i emplena informes personalitzats sobre les tasques"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Customer Preview"
msgstr "Vista prèvia del client"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "Customize worksheet templates for each type of intervention.<br>"
msgstr ""
"Personalitza les plantilles de full de treball per a cada tipus "
"d'intervenció.<br>"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field7
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Date"
msgstr "Data"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Days Spent"
msgstr "Dies invertits"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_task_worksheet_template_2
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__worksheet_template_id
msgid "Default Worksheet"
msgstr "Full de treball per defecte"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Description"
msgstr "Descripció"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field2
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Description of the Intervention"
msgstr "Descripció de la intervenció"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_task_worksheet_template_3
msgid "Device Installation and Maintenance"
msgstr "Instal·lació i manteniment del dispositiu"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_send_report_primary
msgid "Display Send Report Primary"
msgstr "Mostra l'informe primari"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_send_report_secondary
msgid "Display Send Report Secondary"
msgstr "Mostra l'informe secundari"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_sign_report_primary
msgid "Display Sign Report Primary"
msgstr "Mostra l'informe de signes primari"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_sign_report_secondary
msgid "Display Sign Report Secondary"
msgstr "Mostra l'informe de signes secundari"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Download"
msgstr "Descarregar"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__allow_worksheets
msgid "Enables customizable worksheets on tasks."
msgstr "Habilita fulls de treball personalitzables a les tasques."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Fill in your <b>worksheet</b> with the details of your intervention."
msgstr ""
"Ompliu el vostre <b>full de treball</b> amb els detalls de la vostra "
"intervenció."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Go back to your Field Service <b>task</b>."
msgstr "Torna al vostre servei de camp <b>tasca</b>."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Hours Spent"
msgstr "Hores dedicades"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field5
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid ""
"I hereby certify that this device meets the requirements of an acceptable "
"device at the time of testing."
msgstr ""
"Admeto que aquest dispositiu compleix els requisits d'un dispositiu "
"acceptable en el moment de les proves."

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field1
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Intervention Type"
msgstr "Tipus d'intervenció"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid Task."
msgstr "Tasca no vàlida."

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "Data de signatura no vàlida."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"Invite your customer to <b>validate and sign your worksheet report</b>."
msgstr ""
"Convida al teu client a <b>validar i signar l'informe del vostre full de "
"treball</b>."

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__fsm_is_sent
msgid "Is Worksheet sent"
msgstr "S'ha enviat el full de treball"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Manufacturer"
msgstr "Fabricant"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Name of the person that signed the task."
msgstr "Nom de la persona que signa la tasca."

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "No tasks found. Let's create one!"
msgstr "No s'ha trobat cap tasca. ¡Creem-ne un!"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "No worksheet templates found. Let's create one!"
msgstr "No s'ha trobat cap plantilla de full de treball. Creem-ne un!"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Planning by Worksheet"
msgstr "Planning per full de treball"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Print"
msgstr "Imprimir"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_project
msgid "Project"
msgstr "Projecte"

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Save time by generating a <b>signature</b> automatically."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"Save your <b>worksheet</b> once it is complete.<br/><i>Tip: customize this "
"form to your needs and create as many templates as you want.</i>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Schedule tasks and assign them to your workers."
msgstr "Planifica les tasques i les assigna als treballadors."

#. module: industry_fsm_report
#: model:ir.actions.server,name:industry_fsm_report.action_fsm_task_send_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Send Report"
msgstr "Enviar informe"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "Send report"
msgstr "Envia l'informe"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field6
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Serial Number"
msgstr "Núm. de sèrie"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign"
msgstr "Signar"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Sign Report"
msgstr "Signa l'informe"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign Task"
msgstr "Signa la tasca"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Signature"
msgstr "Signatura"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Manca Signatura"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signature
msgid "Signature received through the portal."
msgstr "Signatura rebuda per mitjà del portal."

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr "Signat per"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task
msgid "Task"
msgstr "Tasca"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Recurrència de la tasca"

#. module: industry_fsm_report
#: model:mail.template,name:industry_fsm_report.mail_template_data_send_report
msgid "Task Report"
msgstr "Informe de tasques"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Task Report:"
msgstr "Informe de tasques:"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_industry_fsm_report_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr "Informe personalitzat de full de treball de tasca"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet has been signed"
msgstr "S'ha signat el full de treball"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet is not in a state requiring customer signature."
msgstr ""
"El full de treball no està en un estat que requereixi signatura del client."

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "There are no reports to send."
msgstr "No hi ha informes per enviar."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Timesheets"
msgstr "Fulls d'hores"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "To send the report, you need to select a worksheet template."
msgstr ""
"Per a enviar l'informe cal que establiu una plantilla de full de treball."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr "Feu servir el fil d'Ariadna per a tornar a la  <b>tasca</b>."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Validate the <b>signature</b>."
msgstr "Validar la <b>signatura</b>."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_task
msgid "View Worksheet"
msgstr "Veure full de treball"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worker"
msgstr "Treballador"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field8
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Worker Signature"
msgstr "Signatura del treballador"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worksheet"
msgstr "Full de treball"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Worksheet Completed"
msgstr "Full de treball completat"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_count
msgid "Worksheet Count"
msgstr "Nombre de fulls de treball"

#. module: industry_fsm_report
#: model:ir.actions.report,name:industry_fsm_report.task_custom_report
msgid "Worksheet Report (PDF)"
msgstr "Informe de full de treball (PDF)"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_worksheet_template
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_template_id
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_map_view_inherit_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_search_fsm_report
msgid "Worksheet Template"
msgstr "Plantilla de full de treball"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.action_fsm_worksheets
#: model:ir.actions.act_window,name:industry_fsm_report.fsm_worksheets_action_settings
#: model:ir.ui.menu,name:industry_fsm_report.fsm_settings_worksheets
msgid "Worksheet Templates"
msgstr "Plantilles full de treball"

#. module: industry_fsm_report
#: model:mail.template,report_name:industry_fsm_report.mail_template_data_send_report
msgid ""
"Worksheet {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id.name else '' }}.pdf"
msgstr ""
"Full de Treball {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id.name else '' }}.pdf"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Default_Worksheet
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Device_Installation_and_Maintenance
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__allow_worksheets
msgid "Worksheets"
msgstr "Fulls de treball"

#. module: industry_fsm_report
#: model:mail.template,subject:industry_fsm_report.mail_template_data_send_report
msgid "{{ object.name }} Report"
msgstr "{{ object.name }} Informe"
