# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_report
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Greek (https://www.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_report
#: model:ir.actions.report,print_report_name:industry_fsm_report.task_custom_report
msgid "'Worksheet %s - %s' % (object.name, object.partner_id.name)"
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"<b>Review and sign</b> your <b>worksheet report</b> with your customer."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "<b>Send your worksheet report</b> to your customer."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-1\"/>Sign"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-2\"/>Sign"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Λήψη"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Εκτύπωση"

#. module: industry_fsm_report
#: model:mail.template,body_html:industry_fsm_report.mail_template_data_send_report
msgid ""
"<p>\n"
"                    Dear <t t-out=\"object.partner_id.name or 'Customer'\">Customer</t>,<br/><br/>\n"
"                    Please find attached the worksheet of our onsite operation. <br/><br/>\n"
"                    Feel free to contact us if you have any questions.<br/><br/>\n"
"                    Best regards,<br/><br/>\n"
"                </p>\n"
"            "
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_project_kanban_inherit_industry_fsm_report
msgid ""
"<span class=\"fa fa-pencil mr-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "<span class=\"o_label\">Worksheets</span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Signed\n"
"                            </span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr "<strong>Πελάτης: </strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_gantt_fsm_worksheet
msgid "<strong>Template — </strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Total</strong>"
msgstr "<strong>Σύνολο</strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Workers: </strong>"
msgstr ""

#. module: industry_fsm_report
#: model:ir.ui.menu,name:industry_fsm_report.project_task_menu_planning_by_project_fsm
msgid "By Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__worksheet_template_id
msgid ""
"Choose a default worksheet template for this project (you can change it "
"individually on each task)."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Close"
msgstr "Κλείσιμο"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_color
msgid "Color"
msgstr "Χρώμα"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_2
msgid "Comments"
msgstr "Σχόλια"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "Create and fill custom reports on tasks"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Customer Preview"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "Customize worksheet templates for each type of intervention.<br>"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field7
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Date"
msgstr "Ημερομηνία"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Days Spent"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_task_worksheet_template_2
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__worksheet_template_id
msgid "Default Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Description"
msgstr "Περιγραφή"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field2
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Description of the Intervention"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_task_worksheet_template_3
msgid "Device Installation and Maintenance"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_send_report_primary
msgid "Display Send Report Primary"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_send_report_secondary
msgid "Display Send Report Secondary"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_sign_report_primary
msgid "Display Sign Report Primary"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_sign_report_secondary
msgid "Display Sign Report Secondary"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Download"
msgstr "Λήψη"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "Dropdown menu"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__allow_worksheets
msgid "Enables customizable worksheets on tasks."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Fill in your <b>worksheet</b> with the details of your intervention."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Go back to your Field Service <b>task</b>."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Hours Spent"
msgstr "Πραγματοποιημένες Ώρες"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field5
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid ""
"I hereby certify that this device meets the requirements of an acceptable "
"device at the time of testing."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field1
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Intervention Type"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid Task."
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"Invite your customer to <b>validate and sign your worksheet report</b>."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__fsm_is_sent
msgid "Is Worksheet sent"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Manufacturer"
msgstr "Κατασκευαστής"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Name of the person that signed the task."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "No tasks found. Let's create one!"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "No worksheet templates found. Let's create one!"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Planning by Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Print"
msgstr "Εκτύπωση"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_project
msgid "Project"
msgstr "Έργο"

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Save time by generating a <b>signature</b> automatically."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"Save your <b>worksheet</b> once it is complete.<br/><i>Tip: customize this "
"form to your needs and create as many templates as you want.</i>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Schedule tasks and assign them to your workers."
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.server,name:industry_fsm_report.action_fsm_task_send_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Send Report"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "Send report"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field6
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Serial Number"
msgstr "Σειριακός Αριθμός"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Sign Report"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign Task"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Signature"
msgstr "Υπογραφή"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Δεν υπάρχει υπογραφή."

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signature
msgid "Signature received through the portal."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task
msgid "Task"
msgstr "Εργασία"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: industry_fsm_report
#: model:mail.template,name:industry_fsm_report.mail_template_data_send_report
msgid "Task Report"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Task Report:"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_industry_fsm_report_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet has been signed"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet is not in a state requiring customer signature."
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "There are no reports to send."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Timesheets"
msgstr "Κάρτες Χρόνου"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "To send the report, you need to select a worksheet template."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr ""

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Validate the <b>signature</b>."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_task
msgid "View Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worker"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field8
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Worker Signature"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worksheet"
msgstr "Φύλλο εργασίας"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Worksheet Completed"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_count
msgid "Worksheet Count"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.report,name:industry_fsm_report.task_custom_report
msgid "Worksheet Report (PDF)"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_worksheet_template
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_template_id
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_map_view_inherit_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_search_fsm_report
msgid "Worksheet Template"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.action_fsm_worksheets
#: model:ir.actions.act_window,name:industry_fsm_report.fsm_worksheets_action_settings
#: model:ir.ui.menu,name:industry_fsm_report.fsm_settings_worksheets
msgid "Worksheet Templates"
msgstr ""

#. module: industry_fsm_report
#: model:mail.template,report_name:industry_fsm_report.mail_template_data_send_report
msgid ""
"Worksheet {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id else '' }}.pdf"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Default_Worksheet
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Device_Installation_and_Maintenance
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__allow_worksheets
msgid "Worksheets"
msgstr ""

#. module: industry_fsm_report
#: model:mail.template,subject:industry_fsm_report.mail_template_data_send_report
msgid "{{ object.name }} Report"
msgstr ""
