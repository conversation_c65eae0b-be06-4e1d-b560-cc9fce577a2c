# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_report
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: industry_fsm_report
#: model:ir.actions.report,print_report_name:industry_fsm_report.task_custom_report
msgid "'Worksheet %s - %s' % (object.name, object.partner_id.name)"
msgstr "\"Foglio di lavoro %s - %s\" % (object.name, object.partner_id.name)"

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"<b>Review and sign</b> your <b>worksheet report</b> with your customer."
msgstr "<b>Rivedi e firma</b> il <b>foglio di lavoro</b> con il tuo cliente."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "<b>Send your worksheet report</b> to your customer."
msgstr "<b>Invia il tuo rapporto del foglio avoro</b> al tuo cliente."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-1\"/>Sign"
msgstr "<i class=\"fa fa-check mr-1\"/>Firma"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-2\"/>Sign"
msgstr "<i class=\"fa fa-check mr-2\"/>Firma"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/> Scarica"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Stampa"

#. module: industry_fsm_report
#: model:mail.template,body_html:industry_fsm_report.mail_template_data_send_report
msgid ""
"<p>\n"
"                    Dear <t t-out=\"object.partner_id.name or 'Customer'\">Customer</t>,<br/><br/>\n"
"                    Please find attached the worksheet of our onsite operation. <br/><br/>\n"
"                    Feel free to contact us if you have any questions.<br/><br/>\n"
"                    Best regards,<br/><br/>\n"
"                </p>\n"
"            "
msgstr ""
"<p>\n"
"                    Gentile <t t-out=\"object.partner_id.name or 'Customer'\">cliente</t>,<br/><br/>\n"
"                    di seguito, in allegato, il foglio di lavoro per le nostre operazioni in loco. <br/><br/>\n"
"                    Non esitare a contattarci se hai domande.<br/><br/>\n"
"                    Cordiali saluti,<br/><br/>\n"
"                </p>\n"
"            "

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_project_kanban_inherit_industry_fsm_report
msgid ""
"<span class=\"fa fa-pencil mr-1\" aria-label=\"Worksheet Template\" "
"title=\"Worksheet Template\"/>"
msgstr ""
"<span class=\"fa fa-pencil mr-1\" aria-label=\"Worksheet Template\" "
"title=\"Modello foglio di lavoro\"/>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "<span class=\"o_label\">Worksheets</span>"
msgstr "<span class=\"o_label\">Rapportini di lavoro</span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Signed\n"
"                            </span>"
msgstr ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; height: auto; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px; left: auto;                                 padding: 0;\">\n"
"                                Firmato\n"
"                            </span>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr "<strong>Cliente: </strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_gantt_fsm_worksheet
msgid "<strong>Template — </strong>"
msgstr "<strong>Modello — </strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Total</strong>"
msgstr "<strong>Totale</strong>"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Workers: </strong>"
msgstr "<strong>Dipendenti: </strong>"

#. module: industry_fsm_report
#: model:ir.ui.menu,name:industry_fsm_report.project_task_menu_planning_by_project_fsm
msgid "By Worksheet"
msgstr "Per foglio di lavoro"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__worksheet_template_id
msgid ""
"Choose a default worksheet template for this project (you can change it "
"individually on each task)."
msgstr ""
"Scegliere un modello di foglio di lavoro predefinito per il progetto (è "
"possibile cambiarlo in modo individuale per ciascun lavoro). "

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Close"
msgstr "Chiudi"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_color
msgid "Color"
msgstr "Colore"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_2
msgid "Comments"
msgstr "Commenti"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "Create and fill custom reports on tasks"
msgstr "Creazione e compilazione rapportini personalizzati dei lavori"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Customer Preview"
msgstr "Anteprima cliente"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "Customize worksheet templates for each type of intervention.<br>"
msgstr ""
"Personalizza i modelli di fogli di lavoro per ogni tipo di intervento.<br>"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field7
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Date"
msgstr "Data"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Days Spent"
msgstr "Giorni impiegati"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_task_worksheet_template_2
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__worksheet_template_id
msgid "Default Worksheet"
msgstr "Foglio di lavoro predefinito"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Description"
msgstr "Descrizione"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field2
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Description of the Intervention"
msgstr "Descrizione dell'intervento"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_task_worksheet_template_3
msgid "Device Installation and Maintenance"
msgstr "Installazione e manutenzione del dispositivo"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_send_report_primary
msgid "Display Send Report Primary"
msgstr "Visualizza invia rapportino primario"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_send_report_secondary
msgid "Display Send Report Secondary"
msgstr "Visualizza invia rapportino secondario"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_sign_report_primary
msgid "Display Sign Report Primary"
msgstr "Visualizza firma rapportino primario"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__display_sign_report_secondary
msgid "Display Sign Report Secondary"
msgstr "Visualizza firma rapportino secondario"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Download"
msgstr "Scarica"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_worksheet_template_kanban
msgid "Dropdown menu"
msgstr "Menù a discesa"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__allow_worksheets
msgid "Enables customizable worksheets on tasks."
msgstr "Abilita fogli di lavoro personalizzabili sui lavori."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Fill in your <b>worksheet</b> with the details of your intervention."
msgstr ""
"Compila il tuo <b>foglio di lavoro</b> con i dettagli del tuo intervento."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Go back to your Field Service <b>task</b>."
msgstr "Ritorna al tuo <b>compito</b> di Assistenza sul campo."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Hours Spent"
msgstr "Ore impiegate"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field5
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid ""
"I hereby certify that this device meets the requirements of an acceptable "
"device at the time of testing."
msgstr ""
"Con la presente dichiaro che questo dispositivo soddisfa i requisiti minimi "
"al momento della prova."

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field1
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Intervention Type"
msgstr "Tipologia intervento"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid Task."
msgstr "Lavoro non valido."

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "Dati firma non validi."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"Invite your customer to <b>validate and sign your worksheet report</b>."
msgstr ""
"Invita i clienti a <b>convalidare e firmare il rapportino di lavoro</b>."

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__fsm_is_sent
msgid "Is Worksheet sent"
msgstr "Foglio di lavoro è inviato"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field3
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Manufacturer"
msgstr "Produttore"

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Name of the person that signed the task."
msgstr "Nome della persona che ha firmato il lavoro."

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "No tasks found. Let's create one!"
msgstr "Nessun lavoro trovato. Creane uno!"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.fsm_worksheets_action_settings
msgid "No worksheet templates found. Let's create one!"
msgstr "Nessun modello di foglio di lavoro trovato. Creane uno!"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Planning by Worksheet"
msgstr "Pianificazione per foglio di lavoro"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Print"
msgstr "Stampa"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_project
msgid "Project"
msgstr "Progetto"

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Save time by generating a <b>signature</b> automatically."
msgstr "Risparmia tempo generando una <b>firma</b> automaticamente."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid ""
"Save your <b>worksheet</b> once it is complete.<br/><i>Tip: customize this "
"form to your needs and create as many templates as you want.</i>"
msgstr ""
"Salva il tuo <b>foglio di lavoro</b> una volta completato. "
"<br/><i>Suggerimento: personalizza questo modulo secondo le tue esigenze e "
"crea tutti i modelli che vuoi.</i>"

#. module: industry_fsm_report
#: model_terms:ir.actions.act_window,help:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Schedule tasks and assign them to your workers."
msgstr "Programma i lavori e assegnali agli addetti."

#. module: industry_fsm_report
#: model:ir.actions.server,name:industry_fsm_report.action_fsm_task_send_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Send Report"
msgstr "Invia rapportino"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "Send report"
msgstr "Invia rapporto"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field6
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Serial Number"
msgstr "Numero di serie"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign"
msgstr "Firma"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Sign Report"
msgstr "Firma rapportino"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign Task"
msgstr "Firma lavoro"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Signature"
msgstr "Firma"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Firma mancante."

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signature
msgid "Signature received through the portal."
msgstr "Firma ricevuta tramite il portale"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr "Firmato da"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task
msgid "Task"
msgstr "Lavoro"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Ricorrenza lavoro"

#. module: industry_fsm_report
#: model:mail.template,name:industry_fsm_report.mail_template_data_send_report
msgid "Task Report"
msgstr "Resoconto attività"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Task Report:"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_industry_fsm_report_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr "Rapportino di lavoro personalizzato"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet has been signed"
msgstr "Il foglio di lavoro è stato firmato"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet is not in a state requiring customer signature."
msgstr "Lo stato del foglio di lavoro non richiede la firma del cliente."

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "There are no reports to send."
msgstr "Non ci sono rapporti da inviare."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Timesheets"
msgstr "Fogli ore"

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "To send the report, you need to select a worksheet template."
msgstr ""
"Per inviare il rapportino è necessario selezionare un modello per il foglio "
"di lavoro."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr "Usa i percorsi di navigazione per tornare ai tuoi <b>compiti</b>."

#. module: industry_fsm_report
#. openerp-web
#: code:addons/industry_fsm_report/static/src/js/tours/industry_fsm_report.js:0
#, python-format
msgid "Validate the <b>signature</b>."
msgstr "Convalida la <b>firma</b>."

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_task
msgid "View Worksheet"
msgstr "Vedi foglio di lavoro"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worker"
msgstr "Dipendente"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.fsm_template_field8
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.report_custom_x_project_task_worksheet_template_3
msgid "Worker Signature"
msgstr "Firma operatore"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worksheet"
msgstr "Foglio di lavoro"

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Worksheet Completed"
msgstr "Foglio di lavoro completato"

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_count
msgid "Worksheet Count"
msgstr "Numero fogli di lavoro"

#. module: industry_fsm_report
#: model:ir.actions.report,name:industry_fsm_report.task_custom_report
msgid "Worksheet Report (PDF)"
msgstr "Rapportino di lavoro (PDF)"

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_worksheet_template
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_template_id
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_map_view_inherit_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_search_fsm_report
msgid "Worksheet Template"
msgstr "Modello foglio di lavoro"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.action_fsm_worksheets
#: model:ir.actions.act_window,name:industry_fsm_report.fsm_worksheets_action_settings
#: model:ir.ui.menu,name:industry_fsm_report.fsm_settings_worksheets
msgid "Worksheet Templates"
msgstr "Modelli foglio di lavoro"

#. module: industry_fsm_report
#: model:mail.template,report_name:industry_fsm_report.mail_template_data_send_report
msgid ""
"Worksheet {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id.name else '' }}.pdf"
msgstr ""
"Foglio di lavoro {{ object.name }}{{ (' - ' + object.partner_id.name) if "
"object.partner_id.name else '' }}.pdf"

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Default_Worksheet
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Device_Installation_and_Maintenance
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__allow_worksheets
msgid "Worksheets"
msgstr "Fogli di lavoro"

#. module: industry_fsm_report
#: model:mail.template,subject:industry_fsm_report.mail_template_data_send_report
msgid "{{ object.name }} Report"
msgstr "{{ object.name }} Rapporto"
