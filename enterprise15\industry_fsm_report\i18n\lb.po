# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_report
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-10-01 12:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:36+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_report
#: model:mail.template,subject:industry_fsm_report.mail_template_data_send_report
msgid "${object.name} Report"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project_worksheet_template.py:0
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "&amp;times;"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.report,print_report_name:industry_fsm_report.task_custom_report
msgid "'Worksheet %s - %s' % (object.name, object.partner_id.name)"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-1\"/>Sign"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-check mr-2\"/>Sign"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-download\"/> Download"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: industry_fsm_report
#: model:mail.template,body_html:industry_fsm_report.mail_template_data_send_report
msgid ""
"<p>\n"
"                    Dear ${object.partner_id.name or 'Customer'},<br/><br/>\n"
"                    Please find attached the worksheet of our onsite operation. <br/><br/>\n"
"                    Feel free to contact us if you have any questions.<br/><br/>\n"
"                    Best regards,<br/><br/>\n"
"                </p>\n"
"            "
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_worksheet_template_view_form
msgid "<span class=\"o_stat_text\"> Worksheets</span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid ""
"<span style=\"                                 font-size: 10px;                                 color: #fff;                                 text-transform: uppercase;                                 text-align: center;                                 font-weight: bold; line-height: 20px;                                 transform: rotate(45deg);                                 width: 100px; display: block;                                 background: green;                                 position: absolute;                                 top: 19px; right: -21px;\">\n"
"                                Signed\n"
"                            </span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<span>Accepted on the behalf of:</span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<span>Disc.%</span>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "<strong class=\"text-muted\">Contact person</strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Contact: </strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Customer: </strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "<strong>Subtotal</strong>"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid ""
"<strong>Thank You!</strong><br/>\n"
"                        Your Worksheet Report is now signed."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__action_id
msgid "Action"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__active
msgid "Active"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Amount"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project_worksheet_template.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_worksheet_template_view_form
#, python-format
msgid "Analysis"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_worksheet_template_view_search
msgid "Archived"
msgstr ""

#. module: industry_fsm_report
#: model:ir.ui.menu,name:industry_fsm_report.project_task_menu_planning_by_project_fsm
msgid "By Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__worksheet_template_id
msgid ""
"Choose a default worksheet template for this project (you can change it "
"individually on each task)."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Close"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_color
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__color
msgid "Color"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.industry_fsm_report._custom_x_project_worksheet_template_1_1569932110
msgid "Comments"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_project_form_inherit_industry_fsm_report
msgid "Create and fill report on tasks"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__create_uid
msgid "Created by"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__create_date
msgid "Created on"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Date"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_x_custom_worksheet_x_project_worksheet_template_1
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__worksheet_template_id
msgid "Default Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Description"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__display_name
#: model:ir.model.fields,field_description:industry_fsm_report.field_report_industry_fsm_report_worksheet_custom__display_name
msgid "Display Name"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Download"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Employee"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__allow_worksheets
msgid "Enables customizable worksheets on tasks."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__id
#: model:ir.model.fields,field_description:industry_fsm_report.field_report_industry_fsm_report_worksheet_custom__id
msgid "ID"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid Task."
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__fsm_is_sent
msgid "Is Worksheet sent"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template____last_update
#: model:ir.model.fields,field_description:industry_fsm_report.field_report_industry_fsm_report_worksheet_custom____last_update
msgid "Last Modified on"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__write_uid
msgid "Last Updated by"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__write_date
msgid "Last Updated on"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__model_id
msgid "Model"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__name
msgid "Name"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Name of the person that signed the task."
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.project_task_action_fsm_planning_groupby_worksheet
msgid "Planning by Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Print"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_product_template
msgid "Product Template"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_project
msgid "Project"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_worksheet_template
msgid "Project Worksheet Template"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_worksheet_template_view_search
msgid "Project Worksheet Template Search"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Quantity"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__report_view_id
msgid "Report View"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Send Report"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__sequence
msgid "Sequence"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Sign Report"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Sign Task"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signature
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Signature"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,help:industry_fsm_report.field_project_task__worksheet_signature
msgid "Signature received through the portal."
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_signed_by
msgid "Signed By"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_project_task
msgid "Task"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model,name:industry_fsm_report.model_report_industry_fsm_report_worksheet_custom
msgid "Task Worksheet Custom Report"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "Task signed by %s"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Task:"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_worksheet_template_view_list
msgid "Template name"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project_worksheet_template.py:0
#, python-format
msgid ""
"The template to print this worksheet template should be a QWeb template."
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/controllers/portal.py:0
#, python-format
msgid "The worksheet is not in a state requiring customer signature."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Time &amp; Material"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Time Spent"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Timesheets"
msgstr ""

#. module: industry_fsm_report
#: code:addons/industry_fsm_report/models/project.py:0
#: code:addons/industry_fsm_report/models/project.py:0
#, python-format
msgid "To send the report, you need to select a worksheet template."
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Total Price"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Unit Price"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_task
msgid "View Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "Worksheet"
msgstr ""

#. module: industry_fsm_report
#: model:mail.template,report_name:industry_fsm_report.mail_template_data_send_report
msgid ""
"Worksheet ${object.name}${(' - ' + object.partner_id.name) if "
"object.partner_id else ''}.pdf"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_form_inherit
msgid "Worksheet Completed"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_count
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_worksheet_template__worksheet_count
msgid "Worksheet Count"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "Worksheet Report"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.report,name:industry_fsm_report.task_custom_report
msgid "Worksheet Report (PDF)"
msgstr ""

#. module: industry_fsm_report
#: model:ir.model.fields,field_description:industry_fsm_report.field_product_product__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_product_template__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__worksheet_template_id
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_search_fsm_report
msgid "Worksheet Template"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.action_fsm_worksheets
#: model:ir.ui.menu,name:industry_fsm_report.fsm_settings_worksheets
msgid "Worksheet Templates"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_task_view_form_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.view_task_form2_inherit
msgid "Worksheet from this task"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.template_action_Default_Worksheet
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_project__allow_worksheets
#: model:ir.model.fields,field_description:industry_fsm_report.field_project_task__allow_worksheets
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.project_worksheet_template_view_list
msgid "Worksheets"
msgstr ""

#. module: industry_fsm_report
#: model:ir.actions.act_window,name:industry_fsm_report.fsm_worksheets_action_settings
msgid "Worksheets Templates"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.portal_my_worksheet
msgid "contact avatar"
msgstr ""

#. module: industry_fsm_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_report.worksheet_custom_page
msgid "hour(s)"
msgstr ""
