<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_sharing_project_task_inherit_view_kanban" model="ir.ui.view">
        <field name="name">project.sharing.project.task.view.kanban.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.project_sharing_project_task_inherit_view_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//kanban" position="inside">
                <field name="worksheet_template_id" />
            </xpath>
            <xpath expr="//strong[hasclass('o_kanban_record_title')]" position="after">
                <t t-if="record.worksheet_template_id.raw_value">
                    <br />
                    <span>
                        <field name="worksheet_template_id"/>
                    </span>
                </t>
            </xpath>
        </field>
    </record>

    <record id="project_sharing_quick_create_task_form_inherit" model="ir.ui.view">
        <field name="name">project.sharing.form.quick_create.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.project_sharing_quick_create_task_form_inherit"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='is_fsm']" position="before">
                <field name="allow_worksheets" invisible="1"/>
                <field name="worksheet_template_id" attrs="{'invisible': [('allow_worksheets', '=', False)]}" options="{'no_open': True}"/>
            </xpath>
        </field>
    </record>

    <record id="project_sharing_project_task_inherit_view_tree" model="ir.ui.view">
        <field name="name">project.sharing.project.task.view.tree.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.project_sharing_project_task_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='child_text']" position="after">
                <field name="allow_worksheets" invisible="1"/>
                <field name="worksheet_template_id" optional="hide" attrs="{'column_invisible': [('allow_worksheets', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <record id="project_sharing_project_task_inherit_view_form" model="ir.ui.view">
        <field name="name">project.sharing.project.task.view.form.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.project_sharing_inherit_project_task_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='display_project_id']" position="after">
                <field name="allow_worksheets" invisible="1"/>
                <field name="worksheet_template_id" attrs="{'invisible': [('allow_worksheets', '=', False)]}" options="{'no_open': True, 'no_create': True, 'no_edit': True}"/>
            </xpath>
            <!-- [XBO] TODO: Remove me in master -->
            <xpath expr="//div[@name='button_box']" position="inside">
                <field name="worksheet_count" invisible="1"/>
                <field name="worksheet_signature" invisible="1"/>
                <button
                    string="Customer Preview"
                    class="oe_stat_button o_debounce_disabled"
                    name="action_preview_worksheet"
                    icon="fa-globe" type="object" special="cancel"
                    invisible="1">
                </button>
                <button
                    string="Worksheet"
                    class="oe_stat_button o_debounce_disabled"
                    name="action_fsm_worksheet"
                    icon="fa-pencil" type="object" special="cancel"
                    invisible="1">
                </button>
                <button
                    string="Worksheet Completed"
                    class="oe_stat_button text-success o_debounce_disabled"
                    name="action_fsm_worksheet"
                    icon="fa-check" type="object" special="cancel"
                    invisible="1">
                </button>
            </xpath>
            <xpath expr="//field[@name='child_ids']/tree/field[@name='stage_id']" position="after">
                <field name="worksheet_template_id" optional="hide"/>
            </xpath>
        </field>
    </record>

</odoo>
