<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="field_service_project_stage_0" model="project.task.type">
            <field name="sequence">15</field>
            <field name="name">To Invoice</field>
            <field name="legend_blocked">Blocked</field>
        </record>

        <function model="project.project" name="write">
            <value model="project.project" search="[('id', '=', ref('industry_fsm.fsm_project', raise_if_not_found=False))]"/>
            <value eval="{'allow_billable': 'True',
            'allow_material': 'True',
            'timesheet_product_id' : ref('sale_timesheet.time_product'),
            'type_ids': [(4, ref('field_service_project_stage_0'))]}"/>
        </function>

        <record id="field_service_product" model="product.product">
            <field name="name">Field Service</field>
            <field name="project_id" search="[('id', '=?', ref('industry_fsm.fsm_project', raise_if_not_found=False)), ('is_fsm', '=', True)]"/>
            <field name="service_tracking">task_global_project</field>
            <field name="type">service</field>
            <field name="list_price">100</field>
            <field name="uom_id" ref="uom.product_uom_hour"/>
            <field name="uom_po_id" ref="uom.product_uom_hour"/>
            <field name="service_policy">delivered_timesheet</field>
            <field name="image_1920" type="base64" file="sale_timesheet/static/img/product_product_time_product.png"/>
        </record>
	</data>
</odoo>
