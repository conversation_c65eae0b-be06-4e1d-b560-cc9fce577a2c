# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# ka<PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "<b>Invoice your time and material</b> to your customer."
msgstr "<b>Fakturujte svůj čas a materiál</b> vašemu zákazníkovi."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Create a new product\n"
"                        </p><p>\n"
"                            You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Vytvořte nový produkt\n"
"                        </p><p>\n"
"                            Musíte definovat produkt pro vše, co prodáváte nebo kupujete,\n"
"                            ať už se jedná o skladovatelný produkt, spotřební materiál nebo službu.\n"
"                        </p>"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "A customer should be set on the task to generate a worksheet."
msgstr "Zákazník by měl být nastaven na úkol vytvořit pracovní výkaz."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Added Products"
msgstr "Přidané produkty"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr "Projekt FSM musí být účtován podle úkolové nebo zaměstnanecké sazby."

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytická řádka"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Fakturovatelné"

#. module: industry_fsm_sale
#: model:project.task.type,legend_blocked:industry_fsm_sale.field_service_project_stage_0
msgid "Blocked"
msgstr "Blokováno"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""
"Uložením této změny se všechny položky timesheetu bez rozdílu propojí s "
"vybranou položkou prodejní objednávky."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Choose Products"
msgstr "Vyberte produkty"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""
"Vyberte <b>název</b> pro svůj produkt <i>(např. šrouby, vruty, kotel "
"atd.).</i>"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Click on your product to add it to your <b>list of materials</b>. <i>Tip: "
"for large quantities, click on the number to edit it directly.</i>"
msgstr ""
"Kliknutím na svůj produkt jej přidáte do <b>seznamu materiálů</b>. <i>Tip: V"
" případě velkého množství klikněte na číslo a přímo jej upravte.</i>"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "Potvrďte vytvoření vaší <b>faktury</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Vytvořit fakturu"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create new quotation"
msgstr "Vytvořit novou nabídku"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "Vytvořit nové nabídky přímo z úkolů"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Měna"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_primary
msgid "Display Create Invoice Primary"
msgstr "Zobrazit Vytvoření faktury Primární"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_secondary
msgid "Display Create Invoice Secondary"
msgstr "Zobrazit Vytvoření faktury jako sekundární"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Dropdown menu"
msgstr "Rozbalovací nabídka"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Edit"
msgstr "Upravit"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_quotations
msgid "Extra Quotations"
msgstr "Extra nabidky"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "FSM Product Quantity"
msgstr "Množství produktů FSM"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Favorites"
msgstr "Oblíbené"

#. module: industry_fsm_sale
#: model:product.product,name:industry_fsm_sale.field_service_product
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Řízení pracovníků v terénu"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Group By"
msgstr "Seskupit podle"

#. module: industry_fsm_sale
#: model:product.product,uom_name:industry_fsm_sale.field_service_product
#: model:product.template,uom_name:industry_fsm_sale.field_service_product_product_template
msgid "Hours"
msgstr "Hodiny"

#. module: industry_fsm_sale
#: model:project.task.type,legend_normal:industry_fsm_sale.field_service_project_stage_0
msgid "In Progress"
msgstr "Probíhá"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_status
msgid "Invoice Status"
msgstr "Stav faktury"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__allow_billable
msgid "Invoice your time and material from tasks."
msgstr "Fakturujte svůj čas a materiál z úkolů."

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""
"Jakmile své úkoly dokončíte, fakturujte svůj čas a materiál svým zákazníkům."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
#, python-format
msgid "Invoices"
msgstr "Faktury"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's <b>track the material</b> you use for your task."
msgstr "Pojďme <b>sledovat materiál</b>, který používáte pro svůj úkol."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's create a new <b>product</b>."
msgstr "Vytvořme nový <b>produkt</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr "Počet položek materiálu produktu"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "Celková cena materiálu produktu"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "Množství materiálu"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Nová nabídka"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "Nebyly nalezeny žádné produkty. Vytvořme si jeden!"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid "No tasks found. Let's create one!"
msgstr "Nebyly nalezeny žádné úkoly. Vytvořme si jeden!"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Price:"
msgstr "Cena:"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Product"
msgstr "Produkt"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Category"
msgstr "Kategorie výrobku"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Template"
msgstr "Šablona produktu"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Type"
msgstr "Typ výrobku"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Produkty"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_material
msgid "Products on Tasks"
msgstr "Produkty v úkolech"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Projekt"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Linka prodeje projektu, mapování zaměstnanců"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Počet nabídek"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
msgid "Quotations"
msgstr "Nabídky"

#. module: industry_fsm_sale
#: model:project.task.type,legend_done:industry_fsm_sale.field_service_project_stage_0
msgid "Ready"
msgstr "Připraveno"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "Související úkol"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Položka objednávky"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Řádek zakázky"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"Položka prodejní objednávky, ke které je projekt propojen. Propojte položku "
"časového rozvrhu s položkou prodejní objednávky definovanou v projektu. "
"Platí pouze pro úkoly bez definované položky prodejní objednávky a pokud "
"zaměstnanec není v projektu „Mapování položky zaměstnance / prodejní "
"objednávky“."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Save your <b>product</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,help:industry_fsm_sale.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"Vyberte zúčtovatelný projekt, na kterém lze vytvářet úkoly. Toto nastavení "
"musí být nastaveno pro každou společnost."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Služba"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Services"
msgstr "Služby"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Úloha"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Opakování úkolu"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr "Úkol, ze kterého byla vytvořena nabídka"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr "Materiál lze povolit pouze tehdy, když lze úkol vyúčtovat."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "The set quantity is invalid"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_timesheets_and_fsm_projects
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""
"Produkt časový rozvrh je vyžadován, pokud lze projekt fsm účtovat a jsou "
"povoleny časové rozvrhy."

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model:project.task.type,name:industry_fsm_sale.field_service_project_stage_0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "K fakturaci"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr "Sledujte a fakturujte materiál použitý k dokončení vašich úkolů."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "Sledovat materiál použitý k dokončení úkolu"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Jednotková cena"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>list of products</b>."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr "Pomocí navigace se vraťte zpět do vašeho <b>úkolu</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__warning_message
msgid "Warning Message"
msgstr "Varovná zpráva"
