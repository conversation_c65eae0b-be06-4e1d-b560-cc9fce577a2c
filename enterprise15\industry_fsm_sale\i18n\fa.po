# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "<b>Invoice your time and material</b> to your customer."
msgstr ""
"<b>صدور صورت‌حساب برای زمان و مطالبی</b> که در اختیار مشتریانتان قرار "
"داده‌اید."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Create a new product\n"
"                        </p><p>\n"
"                            You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service.\n"
"                        </p>"
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "A customer should be set on the task to generate a worksheet."
msgstr ""
"مشتری باید ترغیب شود  که در وظیفه‌ی مربوطه یک برگه‌ی ساعت کارکرد ایجاد کند."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Added Products"
msgstr "محصولات اضافه شده"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""
"فاکتور یک پروژه‌ی FSM (مدیریت خدمات میدانی) باید طبق سرعت انجام وظیفه یا "
"سرعت کار کارمندان صادر شود. "

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "سطرتحلیلی"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "قابل فاکتور"

#. module: industry_fsm_sale
#: model:project.task.type,legend_blocked:industry_fsm_sale.field_service_project_stage_0
msgid "Blocked"
msgstr "مسدود شد"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Choose Products"
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Click on your product to add it to your <b>list of materials</b>. <i>Tip: "
"for large quantities, click on the number to edit it directly.</i>"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Confirm the creation of your <b>invoice</b>."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "ایجاد فاکتور"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create new quotation"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "ارز"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_primary
msgid "Display Create Invoice Primary"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_secondary
msgid "Display Create Invoice Secondary"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Dropdown menu"
msgstr "منوی پایین‌افتادنی"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Edit"
msgstr "ویرایش"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_quotations
msgid "Extra Quotations"
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "FSM Product Quantity"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Favorites"
msgstr "علاقه مندی ها"

#. module: industry_fsm_sale
#: model:product.product,name:industry_fsm_sale.field_service_product
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "سرویس در محل"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Group By"
msgstr "گروه بندی بر مبنای"

#. module: industry_fsm_sale
#: model:product.product,uom_name:industry_fsm_sale.field_service_product
#: model:product.template,uom_name:industry_fsm_sale.field_service_product_product_template
msgid "Hours"
msgstr "ساعت"

#. module: industry_fsm_sale
#: model:project.task.type,legend_normal:industry_fsm_sale.field_service_project_stage_0
msgid "In Progress"
msgstr "در جریان"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/product_template.py:0
#, python-format
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Invoice"
msgstr "فاکتور"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_status
msgid "Invoice Status"
msgstr "وضعیت فاکتور"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__allow_billable
msgid "Invoice your time and material from tasks."
msgstr "زمان و اجناس خود را از وظایف فاکتور کنید"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
#, python-format
msgid "Invoices"
msgstr "فاکتورها"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's <b>track the material</b> you use for your task."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's create a new <b>product</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "پیشفاکتور جدید"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid "No tasks found. Let's create one!"
msgstr "فعالیتی یافت نشد. فعالیت جدید بسازید!"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Price:"
msgstr "قیمت:"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Product"
msgstr "محصول"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Category"
msgstr "دسته بندی محصول"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Template"
msgstr "قالب محصول"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Type"
msgstr "نوع محصول"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "محصولات"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_material
msgid "Products on Tasks"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "پروژه"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "تعداد پیشفاکتور"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
msgid "Quotations"
msgstr "پیش فاکتورها"

#. module: industry_fsm_sale
#: model:project.task.type,legend_done:industry_fsm_sale.field_service_project_stage_0
msgid "Ready"
msgstr "آماده"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "سفارش فروش"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "آیتم سفارش فروش"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "سطر سفارش‌فروش"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"آیتم سفارش فروش که پروژه به آن وصل است. ورودی برگه ساعت کارکرد را به آیتم "
"سفارش فروش تعریف شده در پروژه پیوند دهید. فقط برای وظایفی اعمال می شود که "
"آیتم سفارش فروش تعریف نشده باشد، و اگر کارمند در «نقشه کارمند/اقلام سفارش "
"فروش» پروژه نباشد."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Save your <b>product</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,help:industry_fsm_sale.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"یک پروژه قابل پرداخت را انتخاب کنید که در آن می توان وظایف ایجاد کرد. این "
"تنظیمات باید برای هر شرکت تنظیم شود."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "خدمت"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Services"
msgstr "خدمات"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "وظیفه"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "تکرار وظیفه"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "The set quantity is invalid"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_timesheets_and_fsm_projects
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model:project.task.type,name:industry_fsm_sale.field_service_project_stage_0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "قابل فاکتور"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "قیمت واحد"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>list of products</b>."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__warning_message
msgid "Warning Message"
msgstr "پیام هشدار"
