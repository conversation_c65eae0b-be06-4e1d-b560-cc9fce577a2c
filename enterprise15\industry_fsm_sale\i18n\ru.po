# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# Константи<PERSON> <<EMAIL>>, 2021
# Сергей <PERSON>н <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# alenafairy, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "<b>Invoice your time and material</b> to your customer."
msgstr ""
"<b>Выставьте</b> заказчику <b>счет-фактуру за потраченное время и "
"материалы</b>."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Create a new product\n"
"                        </p><p>\n"
"                            You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service.\n"
"                        </p>"
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "A customer should be set on the task to generate a worksheet."
msgstr "На задании должен быть установлен клиент для создания рабочего листа."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Added Products"
msgstr "Добавленные позиции"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""
"Счет за проект FSM должен быть выставлен по тарифу задачи или по тарифу "
"сотрудника."

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Позиция аналитики"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Подлежит оплате"

#. module: industry_fsm_sale
#: model:project.task.type,legend_blocked:industry_fsm_sale.field_service_project_stage_0
msgid "Blocked"
msgstr "Заблокировано"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""
"При сохранении этого изменения все записи в табеле учета рабочего времени "
"будут связаны с выбранным пунктом заказа на продажу без различий."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Choose Products"
msgstr "Выберите позиции"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""
"Выберите <b>название</b> для своего продукта <i>(например, \"Болты\", "
"\"Винты\", \"Котел\" и т. д.)</i>."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Click on your product to add it to your <b>list of materials</b>. <i>Tip: "
"for large quantities, click on the number to edit it directly.</i>"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "Подтвердите создание <b>счета-фактуры</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Создать акт"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create new quotation"
msgstr "Создать новое предложение"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "Создавайте новые предложения непосредственно из задач"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_primary
msgid "Display Create Invoice Primary"
msgstr "Отображение Создать первичный счет-фактуру"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_secondary
msgid "Display Create Invoice Secondary"
msgstr "Отображение Создать счет-фактуру вторично"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Dropdown menu"
msgstr "Выпадающее меню"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Edit"
msgstr "Редактировать"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_quotations
msgid "Extra Quotations"
msgstr "Дополнительные цитаты"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "FSM Product Quantity"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Favorites"
msgstr "Избранное"

#. module: industry_fsm_sale
#: model:product.product,name:industry_fsm_sale.field_service_product
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Выездные сотрудники"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Group By"
msgstr "Группировка"

#. module: industry_fsm_sale
#: model:product.product,uom_name:industry_fsm_sale.field_service_product
#: model:product.template,uom_name:industry_fsm_sale.field_service_product_product_template
msgid "Hours"
msgstr "Часы"

#. module: industry_fsm_sale
#: model:project.task.type,legend_normal:industry_fsm_sale.field_service_project_stage_0
msgid "In Progress"
msgstr "В работе"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/product_template.py:0
#, python-format
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Invoice"
msgstr "Акт"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_status
msgid "Invoice Status"
msgstr "Статус акта"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__allow_billable
msgid "Invoice your time and material from tasks."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""
"Выставляйте счета за потраченное время и материалы своим клиентам после "
"выполнения заданий."

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
#, python-format
msgid "Invoices"
msgstr "Акты"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's <b>track the material</b> you use for your task."
msgstr ""
"Давайте <b>проследим, какой материал</b> вы используете для выполнения "
"задания."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's create a new <b>product</b>."
msgstr "Давайте создадим новый <b>продукт</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr "Линия материалов Количество продуктов"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "Линия материалов Общая цена"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "Количество материала"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Новое коммерческое предложение"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "Продукты не найдены. Давайте создадим один!"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid "No tasks found. Let's create one!"
msgstr "Нет задач. Создайте их."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Price:"
msgstr "Цена:"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Product"
msgstr "Продукт"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Category"
msgstr "Категория продукта"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Type"
msgstr "Тип продукта"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Продукты"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_material
msgid "Products on Tasks"
msgstr "Продукты в задачах"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Управление проектами"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Строка продаж проекта, сопоставление сотрудников"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Количество коммерческих предложений"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
msgid "Quotations"
msgstr "Коммерческие предложения"

#. module: industry_fsm_sale
#: model:project.task.type,legend_done:industry_fsm_sale.field_service_project_stage_0
msgid "Ready"
msgstr "Готово"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "Связанная задача"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Элемент заказа на продажу"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Save your <b>product</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,help:industry_fsm_sale.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Услуга"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Services"
msgstr "Услуги"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Задача"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Повтор задачи"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr ""
"Материал может быть разрешен только тогда, когда за задание можно выставить "
"счет."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "The set quantity is invalid"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_timesheets_and_fsm_projects
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""
"Продукт \"Табель учета рабочего времени\" необходим, если проект fsm может "
"быть тарифицирован и разрешены табели учета рабочего времени."

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model:project.task.type,name:industry_fsm_sale.field_service_project_stage_0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "Выставить акт"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr ""
"Отслеживайте и выставляйте счета за материалы, использованные для выполнения"
" заданий."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "Отслеживайте материалы, использованные для выполнения заданий"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Цена за ед."

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>list of products</b>."
msgstr ""

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__warning_message
msgid "Warning Message"
msgstr "Сообщение о предупреждении"
