# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 11:17+0000\n"
"PO-Revision-Date: 2021-09-14 12:40+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "<b>Invoice your time and material</b> to your customer."
msgstr "向客户<b>开具您的时间和材料</b>的结算单。"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Create a new product\n"
"                        </p><p>\n"
"                            You must define a product for everything you sell or purchase,\n"
"                            whether it's a storable product, a consumable or a service.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            创建新产品\n"
"                            </p><p>\n"
"                             您必须定义一个产品用于您的销售或采购，\n"
"\n"
"                             无论是可库存的产品、可消耗品还是服务。\n"
"                             </p>"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "A customer should be set on the task to generate a worksheet."
msgstr "应在任务上设置一个客户，以生成一个工作表。"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Added Products"
msgstr "添加的产品"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr "一个FSM项目必须按任务费率或雇员费率计费。"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析明细"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "计费"

#. module: industry_fsm_sale
#: model:project.task.type,legend_blocked:industry_fsm_sale.field_service_project_stage_0
msgid "Blocked"
msgstr "阻塞"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr "通过保存这一更改，所有的考勤表条目将不加区分地与选定的销售订单项目相联系。"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Choose Products"
msgstr "选择产品"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr "为您的产品选择一个<b>名称</b> <i>（例如，螺栓、螺钉、锅炉等）</i>。"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid ""
"Click on your product to add it to your <b>list of materials</b>. <i>Tip: "
"for large quantities, click on the number to edit it directly.</i>"
msgstr "点击您的产品，将其添加到您的<b>材料清单</b>中。<i>提示：对于大批量的产品，点击数字可以直接编辑。</i>"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "确认创建您的<b>结算单</b>。"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "创建结算单"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create new quotation"
msgstr "创建新报价"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "直接从任务创建新报价"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "币种"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_primary
msgid "Display Create Invoice Primary"
msgstr "显示 \"创建初级结算单\"。"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__display_create_invoice_secondary
msgid "Display Create Invoice Secondary"
msgstr "显示创建二级结算单"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Edit"
msgstr "编辑"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_quotations
msgid "Extra Quotations"
msgstr "额外引言"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "FSM Product Quantity"
msgstr "FSM Product Quantity"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Favorites"
msgstr "收藏"

#. module: industry_fsm_sale
#: model:product.product,name:industry_fsm_sale.field_service_product
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "现场服务"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Group By"
msgstr "分组"

#. module: industry_fsm_sale
#: model:product.product,uom_name:industry_fsm_sale.field_service_product
#: model:product.template,uom_name:industry_fsm_sale.field_service_product_product_template
msgid "Hours"
msgstr "小时"

#. module: industry_fsm_sale
#: model:project.task.type,legend_normal:industry_fsm_sale.field_service_project_stage_0
msgid "In Progress"
msgstr "进行中"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/product_template.py:0
#, python-format
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr "以下产品当前与现场服务项目相关联，不能更改其开票策略或类型：%s"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#, python-format
msgid "Invoice"
msgstr "结算单"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_status
msgid "Invoice Status"
msgstr "结算单状态"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__allow_billable
msgid "Invoice your time and material from tasks."
msgstr "从任务中开出您的时间和材料的结算单。"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr "一旦您的任务完成，就向您的客户开具您的时间和材料的结算单。"

#. module: industry_fsm_sale
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
#, python-format
msgid "Invoices"
msgstr "结算单"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's <b>track the material</b> you use for your task."
msgstr "让我们来<b>跟踪</b>您用于任务的<b>材料</b>。"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Let's create a new <b>product</b>."
msgstr "让我们创建一个新<b>产品</b>。"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_product_count
msgid "Material Line Product Count"
msgstr "材料系列 产品数量"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "材料行总价"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "物料数量"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "新的报价"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "没有找到产品。让我们创造一个!"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
msgid "No tasks found. Let's create one!"
msgstr "找不到任务。 让我们创建一个！"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Price:"
msgstr "价格:"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_product_product_kanban_material
msgid "Product"
msgstr "产品"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Category"
msgstr "产品类别"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Template"
msgstr "产品模板"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Product Type"
msgstr "产品类型"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "产品"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__allow_material
msgid "Products on Tasks"
msgstr "任务上的产品"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "项目"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "销售明细行/员工地图"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "报价数量"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_form_quotation
msgid "Quotations"
msgstr "报价"

#. module: industry_fsm_sale
#: model:project.task.type,legend_done:industry_fsm_sale.field_service_project_stage_0
msgid "Ready"
msgstr "就绪"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "关联人物"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "销售订单"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "销售订单项目"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "销售订单行"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"链接到项目的销售订单明细。将时间表条目链接到项目中定义的销售订单项。仅适用于未定义销售订单项目的任务，并且员工不在项目的“员工/销售订单项目映射”中。"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Save your <b>product</b>."
msgstr "保存您的<b>产品</b>。"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,help:industry_fsm_sale.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr "选择可以在其上创建任务的可计费项目。必须为每个公司进行此设置。"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "服务"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.product_search_form_view_inherit_fsm_sale
msgid "Services"
msgstr "服务"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "任务"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "重新发起"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which quotation have been created"
msgstr "创建报价的任务"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr "只有在任务可以开票的情况下，才能允许使用该材料。"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/fsm_product_quantity.js:0
#, python-format
msgid "The set quantity is invalid"
msgstr "设定的数量是无效的"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_timesheets_and_fsm_projects
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr "当FSM项目可以计费且允许计时表时，需要计时表产品。"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model:project.task.type,name:industry_fsm_sale.field_service_project_stage_0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "待开票"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr "对完成任务所用的材料进行跟踪和结算。"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "追踪用于完成任务的材料"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "单价"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>list of products</b>."
msgstr "使用面包屑回到您的<b>产品列表</b>。"

#. module: industry_fsm_sale
#. openerp-web
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
#, python-format
msgid "Use the breadcrumbs to go back to your <b>task</b>."
msgstr "使用面包屑来回到您的<b>任务</b>。"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__warning_message
msgid "Warning Message"
msgstr "警告消息"
