<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="project_view_form_inherit" model="ir.ui.view">
        <field name="name">project.view.form.inherit</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="sale_timesheet.project_project_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='billing_employee_rate']//group/field[@name='timesheet_product_id']" position="attributes">
                <attribute name="invisible">0</attribute>
                <attribute name="attrs">{'invisible': ['|', '|', ('allow_timesheets', '=', False), ('pricing_type', '=', False), ('is_fsm', '=', False)], 'required': [('is_fsm', '=', True), ('allow_billable', '=', True), ('allow_timesheets', '=', True)]}</attribute>
            </xpath>
            <xpath expr="//page[@name='billing_employee_rate']//group/field[@name='sale_line_id']" position="attributes">
                <attribute name="attrs">{'invisible': [('is_fsm', '=', True)]}</attribute>
            </xpath>

            <xpath expr="//field[@name='sale_line_employee_ids']/tree/field[@name='sale_line_id']" position="attributes">
                <attribute name="attrs">{'column_invisible': [('parent.is_fsm', '=', True)], 'required': [('parent.is_fsm', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//field[@name='sale_line_employee_ids']/tree/field[@name='sale_line_id']" position="after">
                <field name="timesheet_product_id" attrs="{'column_invisible': [('parent.is_fsm', '=', False)], 'required': [('parent.is_fsm', '=', True)]}" context="{'default_detailed_type': 'service', 'default_service_policy': 'delivered_timesheet', 'default_service_type': 'timesheet'}"/>
            </xpath>
            <xpath expr="//field[@name='sale_line_employee_ids']/tree/field[@name='price_unit']" position="attributes">
                <attribute name="attrs">{'readonly': ['|', ('parent.is_fsm', '=', False), ('timesheet_product_id', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//div[@id='allow_billable_container']" position="after">
                <field name="is_fsm" invisible="1"/>
                <div class="col-lg-6 o_setting_box" attrs="{'invisible': [('allow_billable', '=', False)]}">
                    <div class="o_setting_left_pane">
                        <field name="allow_material"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="allow_material"/>
                        <div class="text-muted" id="allow_billable_setting">
                            Track the material used to complete tasks
                        </div>
                    </div>
                </div>
            </xpath>
            <xpath expr="//div[@id='rating_settings']" position="after">
                <div class="col-lg-6 o_setting_box"  groups="industry_fsm.group_fsm_quotation_from_task">
                    <div class="o_setting_left_pane">
                        <field name="allow_quotations"/>
                    </div>
                    <div class="o_setting_right_pane">
                        <label for="allow_quotations"/>
                        <div class="text-muted" id="allow_billable_setting">
                            Create new quotations directly from tasks
                        </div>
                    </div>
                </div>
            </xpath>
            <xpath expr="//page[@name='billing_employee_rate']" position="attributes">
                <attribute name="attrs">{'invisible': ['|', '|', '&amp;', ('allow_timesheets', '=', False), ('is_fsm', '=', True), ('allow_billable', '=', False), '&amp;', ('partner_id', '=', False), ('is_fsm', '=', False)]}</attribute>
            </xpath>
        </field>
    </record>

    <record id="project_project_view_form_simplified_inherit" model="ir.ui.view">
        <field name="name">project.project.view.form.simplified.inherit</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="sale_timesheet.project_project_view_form_simplified_inherit"/>
        <field name="arch" type="xml">
            <field name="allow_billable" position="after">
                <field name="allow_material" invisible="1"/>
            </field>
        </field>
    </record>

</odoo>
