<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_sharing_project_task_inherit_view_form" model="ir.ui.view">
        <field name="name">project.sharing.project.task.view.form.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.project_sharing_project_task_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="after">
                <div class="text-center alert alert-primary" role="alert" attrs="{'invisible': ['|', ('warning_message', '=', False), ('warning_message', '=', '')]}">
                    <field name="warning_message"/>
                </div>
            </xpath>
            <field name="is_fsm" position="after">
                <field name="allow_material" invisible="1"/>
            </field>
            <xpath expr="//button[@name='action_open_parent_task']" position="before">
                <button class="oe_stat_button o_debounce_disabled" name="action_fsm_view_material" type="object" icon="fa-cart-plus" special="cancel"
                        attrs="{'invisible': ['|', '|', ('partner_id', '=', False), ('allow_material', '=', False), ('material_line_product_count', '=', 0)]}">
                    <div class="o_stat_info">
                        <span class="o_stat_value">
                            <field name="material_line_product_count" widget="statinfo" string="Products" class="mr-1"/>
                        </span>
                        <field name="material_line_total_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        <field name="currency_id" invisible="True"/>
                    </div>
                </button>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <!-- [XBO] TODO: Remove me in master -->
                <button name="action_view_invoices" type="object" class="oe_stat_button o_debounce_disabled" icon="fa-pencil-square-o" special="cancel"
                        invisible="1">
                    <field name="invoice_count" widget="statinfo" string="Invoices"/>
                </button>
                <field name="allow_quotations" invisible="1" />
                <button name="action_fsm_view_quotations"
                    type="object" special="cancel"
                    class="oe_stat_button o_debounce_disabled"
                    icon="fa-dollar"
                    attrs="{'invisible': ['|', ('allow_quotations', '=', False), ('quotation_count', '=', 0)]}">
                    <field string="Quotations" name="quotation_count" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>

</odoo>
