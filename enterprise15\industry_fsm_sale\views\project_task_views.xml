<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_task_view_list_fsm_sale_inherit" model="ir.ui.view">
        <field name="name">project.task.tree.fsm.sale.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.project_task_view_list_fsm"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
                <header>
                    <button name="action_create_invoice" string="Create Invoice" type="object"
                            groups="industry_fsm.group_fsm_manager"
                            invisible="not context.get('search_default_to_invoice',0)"/>
                </header>
            </xpath>
            <field name="remaining_hours" position="after">
                <field name="remaining_hours_so" widget="timesheet_uom" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- All Tasks: to invoice action -->
    <record id="project_task_action_to_invoice_fsm" model="ir.actions.act_window">
        <field name="name">To Invoice</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">tree,kanban,gantt,calendar,map,pivot,graph,form,activity</field>
        <field name="search_view_id" ref="industry_fsm.project_task_view_search_fsm"/>
        <field name="domain">[('is_fsm', '=', True), ('display_project_id', '!=', False)]</field>
        <field name="context">{
            'fsm_mode': True,
            'search_default_to_invoice': True,
            'default_user_id': False,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tasks found. Let's create one!
            </p><p>Invoice your time and material to your customers once your tasks are done.</p>
        </field>
    </record>

    <record id="project_task_view_search_fsm_inherit_sale" model="ir.ui.view">
        <field name="name">project.task.view.search</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.project_task_view_search_fsm"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='todo']" position='after'>
                <filter string="To Invoice" domain="[('invoice_status', '=', 'to invoice')]" name="to_invoice"/>
                <field name="invoice_status"/>
            </xpath>
        </field>
    </record>

    <record id="view_project_task_pivot_fsm_inherit_sale" model="ir.ui.view">
        <field name="name">project.task.view.pivot</field>
        <field name="model">project.task</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="project.view_project_task_pivot"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='stage_id']" position='replace'/>
        </field>
    </record>

    <record id="project_task_action_to_invoice_fsm_view_list" model="ir.actions.act_window.view">
        <field name="sequence" eval="5"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="industry_fsm.project_task_view_list_fsm"/>
        <field name="act_window_id" ref="project_task_action_to_invoice_fsm"/>
    </record>

    <record id="project_task_action_to_invoice_fsm_view_kanban" model="ir.actions.act_window.view">
        <field name="sequence" eval="10"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="industry_fsm.project_task_view_kanban_fsm"/>
        <field name="act_window_id" ref="project_task_action_to_invoice_fsm"/>
    </record>

    <record id="project_task_action_to_invoice_fsm_view_calendar" model="ir.actions.act_window.view">
        <field name="sequence" eval="20"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="industry_fsm.project_task_view_calendar_fsm"/>
        <field name="act_window_id" ref="project_task_action_to_invoice_fsm"/>
    </record>

    <record id="project_task_action_to_invoice_fsm_view_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="25"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="industry_fsm_sale.view_project_task_pivot_fsm_inherit_sale"/>
        <field name="act_window_id" ref="project_task_action_to_invoice_fsm"/>
    </record>

    <record id="project_task_action_to_invoice_fsm_view_form" model="ir.actions.act_window.view">
        <field name="sequence" eval="45"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project.view_task_form2"/>
        <field name="act_window_id" ref="project_task_action_to_invoice_fsm"/>
    </record>

    <record id="industry_fsm.project_project_action_only_fsm" model="ir.actions.act_window">
        <field name="context">{
            'fsm_mode': True,
            'default_is_fsm': True,
            'default_allow_timesheets': True,
            'default_allow_material': True,
            'default_allow_billable': True,
        }</field>
    </record>

    <record id="product_action_fsm" model="ir.actions.act_window" >
        <field name="name">Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,tree,activity,form</field>
        <field name="domain">[('sale_ok', '=', True)]</field>
        <field name="context">{
            'fsm_mode': True,
            'search_default_filter_to_sell': True,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No products found. Let's create one!
            </p><p>
                Track and bill the material used to complete your tasks.
            </p>
        </field>
    </record>

    <menuitem id="fsm_menu_settings_product"
        name="Products"
        sequence="30"
        action="product_action_fsm"
        parent="industry_fsm.fsm_menu_settings"
        groups="sales_team.group_sale_salesman"/>

        <menuitem id="fsm_menu_all_tasks_invoice"
            name="To Invoice"
            action="project_task_action_to_invoice_fsm"
            sequence="30"
            parent="industry_fsm.fsm_menu_all_tasks_root"
            groups="sales_team.group_sale_salesman"/>

    <record id="view_task_form2_inherit" model="ir.ui.view">
        <field name="name">view.task.form2.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="industry_fsm.view_task_form2_inherit"/>
        <field name="arch" type="xml">
            <field name="is_fsm" position="after">
                <field name="allow_material" invisible="1"/>
                <field name="allow_quotations" invisible="1"/>
                <field name="display_create_invoice_primary" invisible="1"/>
                <field name="display_create_invoice_secondary" invisible="1"/>
            </field>
            <xpath expr="//button[@name='action_fsm_validate'][hasclass('btn-primary')]" position="before">
                <button class="btn-primary" name="action_create_invoice" type="object" string="Create Invoice" data-hotkey="l"
                    attrs="{'invisible': [('display_create_invoice_primary', '=', False)]}" groups="industry_fsm.group_fsm_manager"/>
            </xpath>
            <xpath expr="//button[@name='action_fsm_validate'][hasclass('btn-secondary')]" position="before">
                <button class="btn-secondary" name="action_create_invoice" type="object" string="Create Invoice" data-hotkey="l"
                        attrs="{'invisible': [('display_create_invoice_secondary', '=', False)]}" groups="industry_fsm.group_fsm_manager"/>
            </xpath>
            <xpath expr="//button[@name='action_timer_resume']" position='after'>
                <button class="btn-secondary" name="action_fsm_create_quotation" type="object" string="New Quotation"
                        attrs="{'invisible': [('allow_quotations', '=', False)]}" groups="sales_team.group_sale_salesman"
                        title="Create new quotation" data-hotkey="x"/>
            </xpath>
            <xpath expr="//button[@name='action_open_parent_task']" position="before">
                <button class="oe_stat_button" name="action_fsm_view_material"
                    type="object" icon="fa-cart-plus" attrs="{'invisible': ['|', ('partner_id', '=', False), ('allow_material', '=', False)]}">
                    <div class="o_stat_info">
                        <span class="o_stat_value">
                            <field name="material_line_product_count" widget="statinfo" string="Products" class="mr-1"/>
                        </span>
                        <field name="material_line_total_price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        <field name="currency_id" invisible="True"/>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

    <!-- FIXME: normally the attributes in partner_id field does not change because same definition set in industry_fsm module -->
        <record id="view_task_form2_inherit_sale_timesheet" model="ir.ui.view">
        <field name="name">view.task.form2.inherit</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.view_task_form2"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="attributes">
                <attribute name="options">{'always_reload': True}</attribute>
                <attribute name="context">{'show_address': is_fsm, 'res_partner_search_mode': 'customer'}</attribute>
                <attribute name="attrs">{'required': [('is_fsm', '=', True)]}</attribute>
            </field>
        </field>
    </record>

    <!-- Quotation button in Project task form (SALESMAN ONLY)-->
    <record id="project_task_view_form_quotation" model="ir.ui.view">
        <field name="name">view.form.fsm.inherit.quotation</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="sale_project.view_sale_project_inherit_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="after">
                <div class="text-center alert alert-primary" role="alert" attrs="{'invisible': ['|', ('warning_message', '=', False), ('warning_message', '=', '')]}">
                    <field name="warning_message"/>
                </div>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_fsm_view_quotations"
                    type="object"
                    class="oe_stat_button"
                    icon="fa-dollar"
                    groups="industry_fsm.group_fsm_manager"
                    attrs="{'invisible': ['|', ('allow_quotations', '=', False), ('quotation_count', '=', 0)]}">
                    <field string="Quotations" name="quotation_count" widget="statinfo"/>
                </button>
            </xpath>
            <xpath expr="//button[@name='action_view_so']" position="after">
                <button name="action_view_invoices" type="object" class="oe_stat_button" icon="fa-pencil-square-o" attrs="{'invisible': ['|', ('allow_billable', '=', False), ('invoice_count', '=', 0)]}" groups="industry_fsm.group_fsm_manager">
                    <field name="invoice_count" widget="statinfo" string="Invoices"/>
                </button>
            </xpath>
        </field>
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman')), (4, ref('industry_fsm.group_fsm_quotation_from_task'))]"/>
    </record>


</odoo>
