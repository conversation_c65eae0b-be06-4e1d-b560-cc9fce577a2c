# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale_report
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 11:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2023\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "<span>Disc.%</span>"
msgstr "<span>Endirim%</span>"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Yekun</strong>"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "<strong>Taxes</strong>"
msgstr "<strong>Vergilər</strong>"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "<strong>Total</strong>"
msgstr "<strong>Cəm</strong>"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "<strong>Untaxed amount</strong>"
msgstr ""

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Amount"
msgstr "Məbləğ"

#. module: industry_fsm_sale_report
#: model:ir.model.fields,field_description:industry_fsm_sale_report.field_sale_order_line__delivered_price_subtotal
msgid "Delivered Subtotal"
msgstr ""

#. module: industry_fsm_sale_report
#: model:ir.model.fields,field_description:industry_fsm_sale_report.field_sale_order_line__delivered_price_total
msgid "Delivered Total"
msgstr ""

#. module: industry_fsm_sale_report
#: model:ir.model.fields,field_description:industry_fsm_sale_report.field_sale_order_line__delivered_price_tax
msgid "Delivered Total Tax"
msgstr ""

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Description"
msgstr "Təsvir"

#. module: industry_fsm_sale_report
#: model:ir.model.fields,field_description:industry_fsm_sale_report.fsm_template_field4
msgid "Model"
msgstr ""

#. module: industry_fsm_sale_report
#: model:ir.model,name:industry_fsm_sale_report.model_product_template
msgid "Product Template"
msgstr "Məhsul Şablonu"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Quantity"
msgstr "Miqdar"

#. module: industry_fsm_sale_report
#: model:ir.model,name:industry_fsm_sale_report.model_sale_order_line
msgid "Sales Order Line"
msgstr "Satış Sifarişi Sətri"

#. module: industry_fsm_sale_report
#: model:ir.model,name:industry_fsm_sale_report.model_project_task
msgid "Task"
msgstr "Tapşırıq"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Taxes"
msgstr "Vergilər"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Time &amp; Material"
msgstr ""

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Total Price"
msgstr "Ümumi Qiymət"

#. module: industry_fsm_sale_report
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale_report.worksheet_custom_page
msgid "Unit Price"
msgstr "Vahid Qiymət"

#. module: industry_fsm_sale_report
#: model:ir.model.fields,field_description:industry_fsm_sale_report.field_product_product__worksheet_template_id
#: model:ir.model.fields,field_description:industry_fsm_sale_report.field_product_template__worksheet_template_id
msgid "Worksheet Template"
msgstr ""
