<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_product_timesheet_form_inherit" model="ir.ui.view">
        <field name="name">product.template.timesheet.form</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="sale_project.product_template_form_view_invoice_policy_inherit_sale_project"/>
        <field name="arch" type="xml">
            <field name="project_template_id" position="after">
                <field name="worksheet_template_id" attrs="{'invisible': ['|', ('worksheet_template_id', '=', False), ('service_tracking', 'not in', ['task_global_project', 'task_new_project'])]}" context="{'default_res_model': 'project.task'}"/>
            </field>
        </field>
    </record>
</odoo>
