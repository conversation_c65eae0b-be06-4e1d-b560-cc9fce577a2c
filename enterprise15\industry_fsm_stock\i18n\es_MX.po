# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Already Delivered"
msgstr "Entregado"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Close"
msgstr "Cerrar"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__company_id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__company_id
msgid "Company"
msgstr "Empresa"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__create_uid
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__create_date
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Discard"
msgstr "Descartar"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__display_name
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/wizard/fsm_stock_tracking.py:0
#, python-format
msgid "Each line needs a Lot/Serial Number"
msgstr "Cada línea debe ser un número de serie o de lote."

#. module: industry_fsm_stock
#: model:ir.model.fields,help:industry_fsm_stock.field_fsm_stock_tracking__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Asegure la trazabilidad de los productos almacenables en su almacén."

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_sale_order_line__fsm_lot_id
msgid "Fsm Lot"
msgstr "Lote de Servicio externo"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__id
msgid "ID"
msgstr "ID"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking____last_update
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__write_uid
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__write_date
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_fsm_stock_tracking_line
msgid "Lines for FSM Stock Tracking"
msgstr "Líneas para el seguimiento de existencias de Servicio externo"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__lot_id
msgid "Lot/Serial Number"
msgstr "Número de lote/de serie"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_product_product
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__product_id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__product_id
msgid "Product"
msgstr "Producto"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__quantity
msgid "Quantity"
msgstr "Cantidad"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_product_product__quantity_decreasable
msgid "Quantity Decreasable"
msgstr "Cantidad disminuible"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__sale_order_line_id
msgid "Sale Order Line"
msgstr "Línea de orden de venta"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de orden de venta"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.stock_product_product_kanban_material
msgid "Serial"
msgstr "De serie"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_product_product__serial_missing
msgid "Serial Missing"
msgstr "De serie faltante"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__task_id
msgid "Task"
msgstr "Tarea"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__fsm_done
msgid "Task Done"
msgstr "Tarea hecha"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_fsm_stock_tracking
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_line_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Track Stock"
msgstr "Seguimiento de existencias"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking
msgid "Tracking"
msgstr "Seguimiento"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking_line_ids
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__wizard_tracking_line
msgid "Tracking Line"
msgstr "Línea de seguimiento"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking_validated_line_ids
msgid "Tracking Validated Line"
msgstr "Línea de seguimiento validada"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Validate"
msgstr "Validar"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid "Validate Lot/Serial Number"
msgstr "Validar número de serie o de lote"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__wizard_tracking_line_valided
msgid "Validated Tracking Line"
msgstr "Línea de seguimiento validada"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid ""
"You can no longer decrease the delivered quantity of a product once the task"
" is marked as done. Please, create a return in your Inventory instead."
msgstr ""
"Ya no puede disminuir la cantidad entregada de un producto una vez que la "
"tarea se marca como realizada. Por favor, cree una devolución en su "
"inventario en su lugar."
