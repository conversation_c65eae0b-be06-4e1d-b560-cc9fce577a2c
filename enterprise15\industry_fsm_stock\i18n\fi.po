# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Already Delivered"
msgstr "Jo toimitettu"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Close"
msgstr "Sulje"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__company_id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__company_id
msgid "Company"
msgstr "Yritys"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__create_uid
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__create_date
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__create_date
msgid "Created on"
msgstr "Luotu"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Discard"
msgstr "Hylkää"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__display_name
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/wizard/fsm_stock_tracking.py:0
#, python-format
msgid "Each line needs a Lot/Serial Number"
msgstr "Jokaisella rivillä on oltava erä- tai sarjanumero"

#. module: industry_fsm_stock
#: model:ir.model.fields,help:industry_fsm_stock.field_fsm_stock_tracking__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Varmista varastoitavan tuotteen jäljitettävyys varastossasi."

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_sale_order_line__fsm_lot_id
msgid "Fsm Lot"
msgstr "Kenttätyön erä"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking____last_update
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__write_uid
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__write_date
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_fsm_stock_tracking_line
msgid "Lines for FSM Stock Tracking"
msgstr "Kenttätyön varastoseurantaa koskevat rivit"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__lot_id
msgid "Lot/Serial Number"
msgstr "Erä/sarjanumero"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_product_product
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__product_id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__product_id
msgid "Product"
msgstr "Tuote"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__quantity
msgid "Quantity"
msgstr "Määrä"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_product_product__quantity_decreasable
msgid "Quantity Decreasable"
msgstr "Vähennettävä määrä"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__sale_order_line_id
msgid "Sale Order Line"
msgstr "Myyntitilausrivi"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Myyntitilausrivi"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.stock_product_product_kanban_material
msgid "Serial"
msgstr "Sarjanumero"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_product_product__serial_missing
msgid "Serial Missing"
msgstr "Sarjanumero puuttuu"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_stock_move
msgid "Stock Move"
msgstr "Varastosiirto"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__task_id
msgid "Task"
msgstr "Tehtävä"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__fsm_done
msgid "Task Done"
msgstr "Tehtävä valmis"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_fsm_stock_tracking
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_line_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Track Stock"
msgstr "Seuraa varastoa"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking
msgid "Tracking"
msgstr "Seuranta"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking_line_ids
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__wizard_tracking_line
msgid "Tracking Line"
msgstr "Seurantalinja"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking_validated_line_ids
msgid "Tracking Validated Line"
msgstr "Varmistetun rivin seuranta"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Validate"
msgstr "Vahvista"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid "Validate Lot/Serial Number"
msgstr "Vahvista erä-/sarjanumero"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__wizard_tracking_line_valided
msgid "Validated Tracking Line"
msgstr "Vahvista seurattava rivi"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid ""
"You can no longer decrease the delivered quantity of a product once the task"
" is marked as done. Please, create a return in your Inventory instead."
msgstr ""
