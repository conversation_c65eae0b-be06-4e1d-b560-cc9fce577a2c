# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_stock
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# דוד<PERSON> מלכ<PERSON> <<EMAIL>>, 2022
# NoaFarkash, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 09:00+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Already Delivered"
msgstr "כבר נשלח"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Close"
msgstr "סגור"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__company_id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__company_id
msgid "Company"
msgstr "חברה"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__create_uid
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__create_date
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Discard"
msgstr "בטל"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__display_name
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/wizard/fsm_stock_tracking.py:0
#, python-format
msgid "Each line needs a Lot/Serial Number"
msgstr ""

#. module: industry_fsm_stock
#: model:ir.model.fields,help:industry_fsm_stock.field_fsm_stock_tracking__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "וודא מעקב אחר מוצר מנוהל מלאי במחסן שלך."

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_sale_order_line__fsm_lot_id
msgid "Fsm Lot"
msgstr ""

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking____last_update
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__write_uid
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__write_date
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_fsm_stock_tracking_line
msgid "Lines for FSM Stock Tracking"
msgstr ""

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__lot_id
msgid "Lot/Serial Number"
msgstr "מס' אצווה/ סידורי"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_product_product
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__product_id
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__product_id
msgid "Product"
msgstr "מוצר"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__quantity
msgid "Quantity"
msgstr "כמות"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_product_product__quantity_decreasable
msgid "Quantity Decreasable"
msgstr ""

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__sale_order_line_id
msgid "Sale Order Line"
msgstr "שורת פריט בהזמנה"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.stock_product_product_kanban_material
msgid "Serial"
msgstr "מספר סידורי"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_product_product__serial_missing
msgid "Serial Missing"
msgstr ""

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_stock_move
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__task_id
msgid "Task"
msgstr "משימה"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__fsm_done
msgid "Task Done"
msgstr "משימה בוצעה"

#. module: industry_fsm_stock
#: model:ir.model,name:industry_fsm_stock.model_fsm_stock_tracking
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_line_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Track Stock"
msgstr "עקוב אחר מלאי"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking
msgid "Tracking"
msgstr "מעקב"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking_line_ids
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__wizard_tracking_line
msgid "Tracking Line"
msgstr "שורת מעקב"

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking__tracking_validated_line_ids
msgid "Tracking Validated Line"
msgstr ""

#. module: industry_fsm_stock
#: model_terms:ir.ui.view,arch_db:industry_fsm_stock.fsm_stock_tracking_line_view_form
msgid "Validate"
msgstr "אשר"

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid "Validate Lot/Serial Number"
msgstr ""

#. module: industry_fsm_stock
#: model:ir.model.fields,field_description:industry_fsm_stock.field_fsm_stock_tracking_line__wizard_tracking_line_valided
msgid "Validated Tracking Line"
msgstr "שורת מעקב מאומתת "

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid ""
"You can no longer decrease the delivered quantity of a product once the task"
" is marked as done. Please, create a return in your Inventory instead."
msgstr ""

#. module: industry_fsm_stock
#: code:addons/industry_fsm_stock/models/product.py:0
#, python-format
msgid ""
"You cannot have a quantity inferior to the number of items which have "
"already been delivered."
msgstr ""
