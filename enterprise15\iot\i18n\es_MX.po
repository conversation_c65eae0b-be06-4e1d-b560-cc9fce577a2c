# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                    <strong>A. Ethernet Connection</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box.<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>"
msgstr ""
"0. Encienda la caja IoT<br/><br/>\n"
"\n"
"                    <strong>A. Conexión ethernet</strong><br/>\n"
"                    1. Lea el código de emparejamiento desde una pantalla o impresora térmica conectada a la caja IoT.<br/>\n"
"                    2. Ingrese el código a continuación y haga clic en \"Emparejar\".<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-secondary\">Disconnected</span>"
msgstr "<span class=\"badge badge-secondary\">Desconectado</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-success\">Connected</span>"
msgstr "<span class=\"badge badge-success\">Conectado</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                    1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                    2. Copy the token that is below<br/>\n"
"                    3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                    4. You will be redirected to the IoT Box Homepage<br/>\n"
"                    5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""
"<strong>B. Conexión WiFi (o si la conexión ethernet no funciona)</strong><br/>\n"
"                    1. Asegúrese de que no hay ningún cable ethernet conectado a la caja IoT<br/>\n"
"                    2. Copie el siguiente token<br/>\n"
"                    3. Conéctese a la red WiFi de la caja IoT (debería verla en sus redes WiFi disponibles)<br/>\n"
"                    4. Se le redirigirá a la página de inicio de la caja IoT<br/>\n"
"                    5. Pegue el token en el campo correspondiente y siga los pasos descritos en la página de inicio de la caja IoT<br/>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "Agregar asistente de la caja IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "Actualización automática de controladores"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr ""
"Actualice los controladores de forma automática cuando la caja IoT cargue"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "Lector de códigos de barras"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Bluetooth"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_box_controllers.js:0
#, python-format
msgid "CONNECT"
msgstr "CONECTAR"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "Cámara"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#, python-format
msgid "Check if the device is still connected"
msgstr "Compruebe si el dispositivo sigue conectado"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Check if the printer is still connected"
msgstr "Compruebe si la impresora sigue conectada"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click here to open your IoT Homepage"
msgstr "Haga clic aquí para abrir su página de inicio de IoT"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "Haga clic en Avanzado / Mostrar detalles / Detalles / Más información"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Haga clic en Proceder al.../Agregar excepción/Visitar este sitio web/Ir a la"
" página web"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "Haga clic en el"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Close this window and try again"
msgstr "Cierre esta ventana e inténtelo de nuevo"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "Empresa"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr "Conecte una caja IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "Conexión"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "Falló la conexión a la caja IoT"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Connection to device failed"
msgstr "Falló la conexión al dispositivo"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Connection to printer failed"
msgstr "Falló la conexión con la impresora"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
msgid "Created on"
msgstr "Creado el"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "Dispositivo"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "Número de dispositivos"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "Tipo de dispositivo"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "El tipo de dispositivo es #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "Dispositivos"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "Pantalla"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "Mostrar URL"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "Dirección de dominio"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Download Logs"
msgstr "Descargar registros"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr "Solo Firefox: haga clic en confirmar excepción de seguridad"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "Módulo de datos fiscales"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "Agrupar por"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "Hdmi"
msgstr "Hdmi"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "Dispositivo IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "Identificador"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "Identificador (dirección Mac)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "Si el dispositivo está conectado a la caja IoT "

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Si está en un servidor seguro (HTTPS), verifique si aceptó el certificado:"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "Versión de imagen"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "Caja IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "Página de inicio de la caja IoT"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "Cajas IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "Dispositivo IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "¿Es un escáner?"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "Teclado"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "Distribución del teclado"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_device____last_update
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "Último valor enviado"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "Diseño"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "Medida manual"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Lea las medidas manualmente desde el dispositivo"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr ""
"Cambie de forma manual el tipo de dispositivo entre teclado y escáner."

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "Fabricante"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "Nombre"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "Red"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found !"
msgstr "¡No se encontró ninguna caja IoT!"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo no puede conectar con la caja IoT."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "Emparejar"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "Código de emparejamiento"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "Terminal de pago"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser) :"
msgstr ""
"Acepte el certificado de su caja IoT (el procedimiento depende de su "
"navegador):"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Verifique si la caja IoT sigue conectada."

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Please check if the device is still connected."
msgstr "Compruebe si el dispositivo todavía está conectado."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "Impresora"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Printer "
msgstr "Impresora"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "Reportes de la impresora"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "Contrato de garantía del editor para la caja IoT"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "Reportar la acción"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "Reporte xml"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr "Reportes"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "Báscula"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "De serie"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "Estado"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Successfully sent to printer!"
msgstr "¡Enviado a la impresora con éxito!"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"No encontramos el código de emparejamiento que ingresó en nuestro sistema, "
"verifique que sea el código correcto."

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "No hay ningún dispositivo conectado a esta caja IoT "

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "No hay ningún dispositivo conectado a sus cajas IoT "

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "Token"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "Tipo"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "Tipo de conexión."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "Tipo de dispositivo."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"URL de la página que mostrará el dispositivo, déjela en blanco para usar la "
"pantalla de PdV del cliente."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "Variante"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr "Tuvimos problemas emparejando su caja IoT. Inténtelo más tarde."

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""
"Al configurar un dispositivo aquí, el reporte se imprimirá a través de este "
"dispositivo en la caja IoT "

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "conectar"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "para agregar una caja IoT."
