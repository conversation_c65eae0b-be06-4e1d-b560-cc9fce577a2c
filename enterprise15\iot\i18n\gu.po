# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * iot
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 07:14+0000\n"
"PO-Revision-Date: 2018-10-08 07:14+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                            <strong>A. Ethernet Connection</strong><br/>\n"
"                            1. Click on the \"Scan\" button below<br/><br/>\n"
"\n"
"                            <strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                            1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                            2. Copy the token that is below<br/>\n"
"                            3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                            4. You will be redirected to the IoT Box Homepage<br/>\n"
"                            5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_trigger__action
msgid "Action"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr ""

#. module: iot
#: selection:iot.device,connection:0
msgid "Bluetooth"
msgstr ""

#. module: iot
#: selection:iot.device,type:0
msgid "Camera"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Cancel"
msgstr "રદ કરો"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Close MO"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Close WO"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
msgid "Connection"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:291
#, python-format
msgid "Connection failed"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:94
#, python-format
msgid "Connection with the IoT Box failed!"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_trigger__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_trigger__create_date
msgid "Created on"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Detect and Add IoT devices in the network"
msgstr ""

#. module: iot
#: selection:iot.device,type:0
#: model:ir.model.fields,field_description:iot.field_iot_trigger__device_id
msgid "Device"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_trigger__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: iot
#: selection:iot.trigger,action:0
msgid "Fail"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Finish"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Found IoT Box(s)"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_trigger__id
msgid "ID"
msgstr "ઓળખ"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_trigger
msgid "IOT Trigger"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
msgid "IP Address"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr ""

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_trigger__key
msgid "Key"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_device____last_update
#: model:ir.model.fields,field_description:iot.field_iot_trigger____last_update
msgid "Last Modified on"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_trigger__write_uid
msgid "Last Updated by"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_trigger__write_date
msgid "Last Updated on"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
msgid "Name"
msgstr "નામ"

#. module: iot
#: selection:iot.device,connection:0
msgid "Network"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Next"
msgstr "આગલું"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found !"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "OK"
msgstr "બરાબર"

#. module: iot
#: selection:iot.trigger,action:0
msgid "Pack"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Pass"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Pause"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Previous"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Print Delivery Slip"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Print Labels"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Print Operation"
msgstr ""

#. module: iot
#: selection:iot.device,type:0
msgid "Printer"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Record Production"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr "અહેવાલો"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:120
#, python-format
msgid "SCAN"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Scanning range(s)"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Scrap"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__screen_url
msgid "Screen URL"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_trigger__sequence
msgid "Sequence"
msgstr "ક્રમ"

#. module: iot
#: selection:iot.trigger,action:0
msgid "Skip"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:89
#, python-format
msgid "Successfully sent to printer!"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:325 selection:iot.trigger,action:0
#, python-format
msgid "Take Measure"
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Take Picture"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "પ્રકાર"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr ""

#. module: iot
#: selection:iot.device,connection:0
msgid "USB"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__screen_url
msgid "Url of the page that will be displayed by hdmi port of the box."
msgstr ""

#. module: iot
#: selection:iot.trigger,action:0
msgid "Validate"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr ""
