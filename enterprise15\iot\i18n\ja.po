# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> (Quartile) <tashi<PERSON>@roomsfor.hk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: Ryo<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                    <strong>A. Ethernet Connection</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box.<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>"
msgstr ""
"0. IoT ボックスの電源を入れる<br/><br/>\n"
"\n"
"                    <strong>A. イーサネット接続</strong><br/>\n"
"                    1. IoT Boxに接続されたディスプレイやサーマルプリンタから、ペアリングコードを読み取ります。<br/>\n"
"                    2. 下記のコードを入力し、\"ペア\"をクリックしてください。<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-secondary\">Disconnected</span>"
msgstr "<span class=\"badge badge-secondary\">未接続</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-success\">Connected</span>"
msgstr "<span class=\"badge badge-success\">接続済</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                    1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                    2. Copy the token that is below<br/>\n"
"                    3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                    4. You will be redirected to the IoT Box Homepage<br/>\n"
"                    5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""
"<strong>B. WiFi 接続 (または、イーサネットがつながらない場合)</strong><br/>\n"
"                    1. IoT Boxにイーサネットケーブルが接続されていないことを確認します。<br/>\n"
"                    2. 下記のトークンをコピーしてください。<br/>\n"
"                    3. IoT BoxのWiFiネットワークに接続します（利用可能なWiFiネットワーク内に表示されているはずです）。<br/>\n"
"                    4. IoT Boxのホームページが表示されます。<br/>\n"
"                    5. トークン欄にトークンを貼り付け、IoT Boxホームページに記載されている手順に従ってください。<br/>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "IoT Box ウィザードを追加"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "ドライバーの自動更新"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "IoT Boxの起動時にドライバーを自動でアップデート"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "バーコードスキャナ"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Bluetooth"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_box_controllers.js:0
#, python-format
msgid "CONNECT"
msgstr "接続"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "カメラ"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#, python-format
msgid "Check if the device is still connected"
msgstr "デバイスがまだ接続されているか確認して下さい、"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Check if the printer is still connected"
msgstr "プリンタがまだ接続されているか確認して下さい。、"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click here to open your IoT Homepage"
msgstr "IoTページを開くにはここをクリック"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "詳細設定/詳細を表示/詳細/詳細情報をクリック"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr "に進む/例外を追加する/このウェブサイトを見る/ウェブサイトに進むをクリック"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "以下をクリック"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
#, python-format
msgid "Close"
msgstr "閉じる"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Close this window and try again"
msgstr "このウィンドウを閉じて、もう一度試してください。"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "会社"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr "IoTボックスの接続"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "接続"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "IoTボックスへの接続に失敗しました"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Connection to device failed"
msgstr "機器との接続に失敗しました"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Connection to printer failed"
msgstr "プリンタとの接続に失敗しました"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
msgid "Created by"
msgstr "作成者"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
msgid "Created on"
msgstr "作成日"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "デバイス"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "デバイス数"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "デバイスタイプ"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "デバイスタイプは #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "デバイス"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "表示"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
msgid "Display Name"
msgstr "表示名"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "URLを表示"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "ドメインIPアドレス"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Download Logs"
msgstr "ログのダウンロード"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr "Firefoxのみ：「セキュリティ例外の確認」をクリックします。"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "会計データモジュール"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "グループ化"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "Hdmi"
msgstr "HDMI"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "IOT デバイス"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "保管ロット"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "識別子(Macアドレス)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "デバイスがIoT Boxに接続されているか"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr "セキュアサーバー（HTTPS）を使用している場合は、証明書を受け入れたかどうかを確認してください。"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "イメージバージョン"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "IoT Box"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "IoT ボックスホームページ"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "IoT ボックス"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "IoT 機器"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "スキャナー"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "キーボード"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "キーボード・レイアウト"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_device____last_update
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "最後に送信された値"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "レイアウト"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "手動計測"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "デバイスから測定値を手動で読み込む"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "キーボードとスキャナの間のデバイスタイプを手動で切替"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "製造業"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "名称"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "ネットワーク"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found !"
msgstr "IoTボックスが見つかりません！"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Odoo cannot reach the IoT Box."
msgstr "OdooがIoT Boxに到達できません。"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "ペア"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "ペアリングコード"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "支払ターミナル"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser) :"
msgstr "IoT Boxの証明書を承認してください（手順はブラウザによって異なります）。"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "IoTボックスがまだ接続されているかどうかを確認してください。"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Please check if the device is still connected."
msgstr "機器が接続されたままになっているか確認してください。"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "プリンタ"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Printer "
msgstr "プリンタ"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "プリンタレポート"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "IoTボックスの発行者保証契約"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "レポートアクション"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "レポートXML"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr "レポート"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "スケール"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "シリアル"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "ステータス"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Successfully sent to printer!"
msgstr "プリンタへの送信に成功しました！"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr "入力されたペアリングコードは、システム内で見つかりませんでした。正しく入力されているかどうか、ご確認ください。"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "このIoT ボックスに接続されたデバイスはありません。"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "IoT ボックスに接続されたデバイスはありません。"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "トークン"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "タイプ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "接続タイプ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "機器タイプ"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr "機器で表示されるページのURL。POSの顧客向けディスプレイとして使用する場合は空欄にします。"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "製品バリアント"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr "IoTボックスのペアリングに問題が発生しました。後ほどもう一度お試しください。"

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr "ここで機器を設定すると、この機器を通してIoT Boxでレポートが印刷されます"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "接続"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "IoTボックスのIPアドレス"
