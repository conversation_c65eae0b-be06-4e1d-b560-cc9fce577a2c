# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                    <strong>A. Ethernet Connection</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box.<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>"
msgstr ""
"0. IoT 박스 전원을 켭니다.<br/><br/>\n"
"\n"
"                    <strong>A. 이더넷에 연결합니다.</strong><br/>\n"
"                    1. IoT Box에 연결된 디스플레이 또는 감열식 프린에서 페어링 코드를 불러옵니다.<br/>\n"
"                    2. 아래의 코드를 입력하고 \"페어링\"을 클릭합니다.<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-secondary\">Disconnected</span>"
msgstr "<span class=\"badge badge-secondary\">연결 해제</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-success\">Connected</span>"
msgstr "<span class=\"badge badge-success\">연결됨</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                    1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                    2. Copy the token that is below<br/>\n"
"                    3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                    4. You will be redirected to the IoT Box Homepage<br/>\n"
"                    5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""
"<strong>B. WiFi 연결 (또는 이더넷 연결이 작동하지 않음)</strong><br/>\n"
"                    1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                    2. Copy the token that is below<br/>\n"
"                    3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                    4. You will be redirected to the IoT Box Homepage<br/>\n"
"                    5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "IOT 박스 마법사 추가"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "자동 드라이버 업데이트"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "IoT Box 부팅시 자동으로 드라이버 업데이트"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "바코드 스캐너"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "블루투스"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_box_controllers.js:0
#, python-format
msgid "CONNECT"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "카메라"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#, python-format
msgid "Check if the device is still connected"
msgstr "디바이스가 아직 연결되어 있는지 확인"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Check if the printer is still connected"
msgstr "프린터가 아직 연결되어 있는지 확인"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click here to open your IoT Homepage"
msgstr ""
"13\n"
"IoT 홈페이지를 열려면 여기를 클릭하십시오\n"
" "

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr "고급/세부사항 보기/세부사항/추가 정보를 클릭하세요"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr "웹페이지에서 .../예외 추가/웹사이트 방문/진행 으로 진행을 클릭하십시오."

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "다음을 클릭하세요"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
#, python-format
msgid "Close"
msgstr "닫기"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Close this window and try again"
msgstr "이 창을 닫고 다시 시도하십시오"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "회사"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr "IoT 박스 연결"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "연결"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "IoT Box에 연결하지 못했습니다"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Connection to device failed"
msgstr "장치에 연결하지 못했습니다"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Connection to printer failed"
msgstr "프린터 연결 실패"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
msgid "Created by"
msgstr "작성자"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
msgid "Created on"
msgstr "작성일자"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "장치"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "장치 수"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "장치 유형"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "디바이스 유형은 #{record.type.raw_value}입니다"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "장치"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "표시"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
msgid "Display Name"
msgstr "표시명"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr " URL 표시"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "도메인 주소"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Download Logs"
msgstr "다운로드 기록"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr "Firefox 만 해당 : 보안 예외 확인을 클릭하십시오."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "회계 데이터 모듈"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "그룹별"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "Hdmi"
msgstr "Hdmi"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "IOT 장치"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "식별자"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "식별자 (맥 주소)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "기기가 IoT Box에 연결된 경우"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr "보안 서버 (HTTPS)에 있는 경우 인증서를 수락했는지 확인하십시오."

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "이미지 버전"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "IoT 박스"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "IoT Box 홈페이지"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "IoT 박스"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "IOT 장치"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "스캐너"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "키보드"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "키보드 레이아웃"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_device____last_update
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "마지막으로 보낸 값"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "배치"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "수동 측정"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "장치에서 수동으로 측정값 읽기"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "키보드와 스캐너 간에 수동으로 디바이스 유형 전환하기"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "제조사"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "이름"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "네트워크"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found !"
msgstr "IoT 박스가 없습니다!"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo는 IoT Box에 연결할 수 없습니다."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "페어링"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "페어링 코드"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "결제 단말기"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser) :"
msgstr "IoT Box의 인증서를 수락하십시오 (절차는 브라우저에 따라 다름)."

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "IoT Box가 여전히 연결되어 있는지 확인하십시오."

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Please check if the device is still connected."
msgstr "장치가 여전히 연결되어 있는지 확인하십시오."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "프린터"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Printer "
msgstr "프린터"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "프린터 보고서"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "IoT 박스에 대한 게시자 보증 계약서"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "보고서 작업"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "xml 보고서"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr "보고서"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "크기"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "시리얼"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "상태"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Successfully sent to printer!"
msgstr "프린터로 성공적으로 전송되었습니다!"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr "입력하신 페어링 코드는 시스템에서 찾을 수 없습니다. 올바르게 입력했는지 다시 확인해 주세요."

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "이 IoT Box에 연결된 장치가 없습니다"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "IoT 박스에 연결된 장치가 없음"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "토큰"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "유형"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "연결 유형"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "장치 유형"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr "장치에서 표시 할 페이지의 URL입니다. 고객이 POS를 표시하도록 하려면 비워 두십시오."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "파생 상품"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr "IoT Box를 페어링하는 데 문제가 발생했습니다. 나중에 다시 시도하세요."

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr "장치를 설정할면 보고서는 IoT 상자에 있는 이 장치를 통해 인쇄됩니다"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "연결"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "IoT 박스에 추가하기."
