# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 11:38+0000\n"
"PO-Revision-Date: 2019-08-26 09:36+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                            <strong>A. Ethernet Connection</strong><br/>\n"
"                            1. Click on the \"Scan\" button below<br/><br/>\n"
"\n"
"                            <strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                            1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                            2. Copy the token that is below<br/>\n"
"                            3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                            4. You will be redirected to the IoT Box Homepage<br/>\n"
"                            5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-secondary\">Disconnected</span>"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-success\">Connected</span>"
msgstr ""

#. module: iot
#: code:addons/iot/drivers/SixDriver.py:0
#, python-format
msgid ""
"A card is still inserted in the Payment Terminal, please remove it then try "
"again."
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/xml/iot_scan_progress_template.xml:0
#, python-format
msgid "Add"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/tours/iot.js:0
#: code:addons/iot/static/src/js/tours/iot.js:0
#, python-format
msgid "Add range to scan."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Click here to open your IoT Homepage"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/tours/iot.js:0
#, python-format
msgid "Click on iot App."
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Close"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Close this window and try again"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_scan.js:0
#, python-format
msgid "Connection failed"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controller.js:0
#, python-format
msgid "Connection to Device failed"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Connection to Printer failed"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Connection to device failed"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/tours/iot.js:0
#, python-format
msgid "Copy token to the clipboard."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
msgid "Created by"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
msgid "Created on"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Detect and Add IoT devices in the network"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
msgid "Display Name"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Done"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Found IoT Box(s)"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "Hdmi"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
msgid "ID"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr ""

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr ""

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_scan.js:0
#, python-format
msgid "IoTBox connected"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_device____last_update
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout____last_update
msgid "Last Modified on"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
msgid "Last Updated by"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
msgid "Last Updated on"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found !"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "No IoT Box(s) found"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "OK"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Odoo cannot reach the IoT Box."
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/tours/iot.js:0
#, python-format
msgid "Open wizard to scan range."
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser) :"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#: code:addons/iot/static/src/js/iot_device_controller.js:0
#, python-format
msgid "Please check if the device is still connected."
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/xml/iot_scan_progress_template.xml:0
#, python-format
msgid "Range(s) to scan"
msgstr ""

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "SCAN"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/xml/iot_scan_progress_template.xml:0
#, python-format
msgid "Scan another range, e.g.: 10.1.1.*"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Scanning Network"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__screen_url
#: model:ir.model.fields,field_description:iot.field_iot_device__screen_url
msgid "Screen URL"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot.js:0
#, python-format
msgid "Successfully sent to printer!"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/tours/iot.js:0
#, python-format
msgid "The range can not be empty."
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr ""

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_scan.js:0
#, python-format
msgid "This IoTBox is already connected"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__screen_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__screen_url
msgid "Url of the page that will be displayed by hdmi port of the box."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""

#. module: iot
#: code:addons/iot/drivers/SixDriver.py:0
#, python-format
msgid "You cannot reverse this payment anymore."
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr ""
