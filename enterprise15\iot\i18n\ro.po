# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:56+0000\n"
"PO-Revision-Date: 2021-09-14 12:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box<br/><br/>\n"
"\n"
"                    <strong>A. Ethernet Connection</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box.<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>"
msgstr ""
"0. Porniți caseta IoT<br/><br/>\n"
"\n"
"                    <strong>A. Conexiune Ethernet</strong><br/>\n"
"                    1. Citiți codul de asociere de pe un afișaj sau imprimantă termică conectată la IoT Box.<br/>\n"
"                    2. Introduceți codul de mai jos și faceți clic pe „Pair”.<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-secondary\">Disconnected</span>"
msgstr "<span class=\"badge badge-secondary\">Deconectat</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge badge-success\">Connected</span>"
msgstr "<span class=\"badge badge-success\">Conectat</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<strong>B. WiFi Connection (or Ethernet Connection doesn't work)</strong><br/>\n"
"                    1. Make sure no ethernet cable is connected to the IoT Box<br/>\n"
"                    2. Copy the token that is below<br/>\n"
"                    3. Connect to the IoT Box WiFi network (you should see it in your available WiFi networks)<br/>\n"
"                    4. You will be redirected to the IoT Box Homepage<br/>\n"
"                    5. Paste the token in token field and follow the steps described on the IoT Box Homepage<br/>"
msgstr ""
"<strong>B. Conexiune WiFi (sau conexiunea Ethernet nu funcționează)</strong><br/>\n"
"                    1. Asigurați-vă că nu este conectat niciun cablu Ethernet la caseta IoT <br/>\n"
"                    2. Copiați jetonul de mai jos<br/>\n"
"                    3. Conectați-vă la rețeaua WiFi IoT Box (ar trebui să o vedeți în rețelele WiFi disponibile)<br/>\n"
"                    4. Veți fi redirecționat către pagina de pornire a casetei IoT<br/>\n"
"                    5. Lipiți jetonul în câmpul jeton și urmați pașii descriși pe pagina de pornire a casetei IoT<br/>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "Adăugați expertul IoT Box"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "Actualizarea automată a driverelor"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "Actualizați automat driverele la pornirea IoT Box"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "Scanner de Coduri de bare"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Bluetooth"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_box_controllers.js:0
#, python-format
msgid "CONNECT"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "Aparat Foto"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#, python-format
msgid "Check if the device is still connected"
msgstr "Verificați dacă dispozitivul este încă conectat"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Check if the printer is still connected"
msgstr "Verificați dacă imprimanta este încă conectată"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click here to open your IoT Homepage"
msgstr "Faceți clic aici pentru a vă deschide pagina de pornire IoT"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""
"Faceți clic pe Avansat / Afișați detalii / Detalii / Mai multe informații"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Faceți clic pe Continuați cu ... / Adăugați o excepție / Vizitați acest site"
" web / Accesați pagina web"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "Faceți clic pe"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
#, python-format
msgid "Close"
msgstr "Închide"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Close this window and try again"
msgstr "Închideți această fereastră și încercați din nou"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "Companie"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect an IoT Box"
msgstr "Conectați o casetă IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "Conexiune"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "Conectarea la caseta IoT a eșuat"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_device_controllers.js:0
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Connection to device failed"
msgstr "Conexiunea la dispozitiv a eșuat"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Connection to printer failed"
msgstr "Conectarea la imprimantă eșuat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
msgid "Created on"
msgstr "Creat în"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "Dispozitiv"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "Număr de dispozitive"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "Tip Dispozitiv"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "Tip dizpozitiv este #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "Dispozitive"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "Afișare"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "Afișare URL"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "Adresa de domeniu"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Download Logs"
msgstr "Jurnale descărcări"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Firefox only : Click on Confirm Security Exception"
msgstr "Doar Firefox: faceți clic pe Confirmare excepție de securitate"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "Modulul de date fiscale"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "Grupează după"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "Hdmi"
msgstr "Hdmi"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "Dispozitiv IOT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "Identificator"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "Identificator (adresa Mac)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "Dacă dispozitivul este conectat la caseta IoT"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Dacă vă aflați pe un server securizat (HTTPS), verificați dacă ați acceptat "
"certificatul:"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "Versiune imagine"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "IoT Box"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "Pagina principală IoT Box"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "Cutii IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "Dispozitiv IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "Este Scanner"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "Tastatură"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "Aspect tastatură"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_box____last_update
#: model:ir.model.fields,field_description:iot.field_iot_device____last_update
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "Ultima valoare trimisă"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "Aspect"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "Măsurare manuală"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Citiți manual măsurarea de pe dispozitiv"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "Comutați manual tipul dispozitivului între tastatură și scaner"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "Producător"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "Nume"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "Rețea"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found !"
msgstr "Nu s-a găsit nicio casetă IoT!"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo nu poate ajunge la caseta IoT."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "Imperechere"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "Cod Împerechere"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "Terminal de plată"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser) :"
msgstr ""
"Vă rugăm să acceptați certificatul IoT Box (procedura depinde de browserul "
"dvs.):"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_mixins.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Vă rugăm să verificați dacă IoT Box este încă conectat."

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/iot_widgets.js:0
#, python-format
msgid "Please check if the device is still connected."
msgstr "Vă rugăm să verificați dacă dispozitivul este încă conectat."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "Imprimantă"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Printer "
msgstr "Imprimantă"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "Rapoarte de imprimantă"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "Contract de garanție a editorului pentru caseta IoT"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "Acțiune raport"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "Raport xml"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
msgid "Reports"
msgstr "Rapoarte"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "Cântar"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "Serial"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "Stare"

#. module: iot
#. openerp-web
#: code:addons/iot/static/src/js/action_manager.js:0
#, python-format
msgid "Successfully sent to printer!"
msgstr "Trimis la imprimantă cu succes!"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"Codul de împerechere pe care l-ați furnizat nu a fost găsit în sistemul "
"nostru."
"                                                                               "
"Vă rugăm să verificați dacă l-ați introdus corect."
"                                                                    "

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "Nu există niciun dispozitiv conectat la această casetă IoT"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "Nu există niciun dispozitiv conectat la casetele dvs. IoT"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "Jeton"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "Tip"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "Tipul de conexiune"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "Tipul de dizpozitiv"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"Adresa URL a paginii care va fi afișată de dispozitiv, lăsați-o goală pentru"
" a utiliza afișarea POS a clientului."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "Variantă"

#. module: iot
#: code:addons/iot/wizard/add_iot_box.py:0
#, python-format
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr ""
"Am întâmpinat probleme la asocierea casetei dvs. IoT. Vă rugăm să încercați "
"din nou mai târziu."

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_id
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""
"Când setați un dispozitiv aici, raportul va fi tipărit prin intermediul "
"acestui dispozitiv în caseta IoT"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "Conectare"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "pentru a adăuga o casetă IoT."
